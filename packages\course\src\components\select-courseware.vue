<template>
  <div>
    <!-- trigger -->
    <slot v-if="showTrigger">
      <el-button @click="open">选择课件</el-button>
    </slot>
    <el-drawer title="选择课件" :visible.sync="openDrawer" size="1200px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-row :gutter="16" class="m-query f-mt10">
          <el-form :inline="true" label-width="auto">
            <el-col :span="8">
              <el-form-item label="课件供应商">
                <biz-courseware-supplier v-model="queryCoursewareListParamVo.providers"></biz-courseware-supplier>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="课件名称">
                <el-input v-model="queryCoursewareListParamVo.name" clearable placeholder="请输入课件名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="课件分类">
                <biz-courseware-category v-model="queryCoursewareListParamVo.categoryIdList"></biz-courseware-category>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="课件类型">
                <biz-courseware-type v-model="queryCoursewareListParamVo.type.current"></biz-courseware-type>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="外部链接课件">
                <el-select v-model="queryCoursewareListParamVo.isOuter" clearable placeholder="请选择是否为外部链接">
                  <el-option
                    v-for="item in outsideChainList"
                    :key="item.code"
                    :value="item.code"
                    :label="item.desc"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary" @click="doQuery">查询</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--提示栏-->
        <div class="f-mb10">
          <el-alert type="warning" show-icon :closable="false" class="m-alert">
            如涉及到与课件供应商结算的情况，建议同一课程添加相同课件供应商的课件资源。
          </el-alert>
        </div>
        <!--表格-->
        <el-table
          ref="table"
          stripe
          :data="coursewareList"
          max-height="500px"
          class="m-table"
          v-loading="query.loading"
        >
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="课件名称" min-width="300" prop="name"></el-table-column>
          <el-table-column label="课件分类" min-width="180" prop="categoryName"></el-table-column>
          <el-table-column label="课件教师" min-width="140">
            <template slot-scope="scope">
              <span v-if="scope.row.teachers.length">
                {{ scope.row.teachers[0].name || '无' }}
              </span>
              <span v-else>无</span>
            </template>
          </el-table-column>
          <el-table-column label="转换状态" min-width="120">
            <template slot-scope="{ row }">
              <el-badge is-dot :type="statusMapType[row.status.current]" class="badge-status">
                {{ row.status }}
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column label="课件类型" min-width="120">
            <template slot-scope="{ row }">
              {{ row.type }}
            </template>
          </el-table-column>
          <el-table-column label="时长" min-width="100">
            <template slot-scope="{ row }">
              {{ row.timeLengthFormat }}
            </template>
          </el-table-column>
          <el-table-column label="外部链接" align="center" min-width="100">
            <template slot-scope="{ row }">{{ row.isOuter ? '是' : '否' }}</template>
          </el-table-column>
          <el-table-column label="课件供应商" align="cente r" min-width="120">
            <template slot-scope="{ row }">
              <div class="f-to" :title="row.providerName">{{ row.providerName }}</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot="header">
              <el-checkbox @change="selectedAllChange"></el-checkbox>
              操作
            </template>
            <template slot-scope="scope">
              <el-checkbox
                v-model="scope.row.selected"
                :label="scope.row.selected ? '取消选择' : '选择'"
                @change="changeSelect(scope.$index)"
              ></el-checkbox>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
        <div class="m-btn-bar f-tc f-mt20">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="saveSelect">保存</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import CoursewareDetail from '@api/service/management/resource/courseware/query/vo/CoursewareDetail'
  import BizCoursewareType from '@hbfe/jxjy-admin-components/src/biz/biz-courseware-type.vue'
  import BizCoursewareSupplier from '@hbfe/jxjy-admin-components/src/biz/biz-courseware-supplier.vue'
  import QueryCoursewareListParamVo from '@api/service/management/resource/courseware/query/vo/QueryCoursewareListParamVo'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import CoursewareListDetail from '@api/service/management/resource/courseware/query/vo/CoursewareListDetail'
  import CoursewareTransformStatus from '@api/service/management/resource/courseware/enum/CoursewareTransformStatus'
  import { CoursewareStatusEnum } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
  import { ElTable } from 'element-ui/types/table'
  @Component({
    components: { BizCoursewareSupplier, BizCoursewareType }
  })
  export default class extends Vue {
    @Ref('table') table: ElTable
    @Prop({
      type: Boolean,
      default: true
    })
    showTrigger: boolean
    @Prop({
      type: Boolean,
      default: false
    })
    multiple: boolean
    @Prop({
      type: Array,
      default() {
        return new Array<string>()
      }
    })
    preSelected: Array<string>
    cencelArray: Array<CoursewareListDetail> = new Array<CoursewareListDetail>()
    openDrawer = false
    page: UiPage
    query: Query = new Query()
    queryCoursewareListParamVo: QueryCoursewareListParamVo = new QueryCoursewareListParamVo()
    coursewareList: Array<CoursewareListDetail> = new Array<CoursewareListDetail>()
    selectCoursewareList: Array<CoursewareListDetail> = new Array<CoursewareListDetail>()
    // 获取外部链接课件
    outsideChainList = [
      {
        code: 1,
        desc: '是'
      },
      {
        code: 2,
        desc: '否'
      }
    ]
    statusMapType = {
      [CoursewareTransformStatus.enum.UNAVAILABLE]: 'danger',
      [CoursewareTransformStatus.enum.AVAILABLE]: 'success',
      [CoursewareTransformStatus.enum.TRANSCODING]: 'primary'
    }
    CoursewareStatusEnum = CoursewareStatusEnum

    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    async open() {
      this.openDrawer = true
      this.queryCoursewareListParamVo = new QueryCoursewareListParamVo()
      await this.doSearch()
    }

    selectedCoursewareList() {
      const list = this.selectCoursewareList.map((item) => item.id).concat(this.preSelected)
      this.coursewareList.map((courseWare: CoursewareListDetail) => {
        courseWare.selected = list.includes(courseWare.id)
        // courseWare.disabled =u  this.preSelected.includes(courseWare.id)
        return courseWare
      })
    }

    changeSelect(index: number) {
      const item = Object.assign({}, this.coursewareList[index])
      if (item.selected) {
        this.selectCoursewareList.push(item)
      } else {
        this.cencelArray.push(item)
        const index = this.selectCoursewareList.findIndex((it) => it.id === item.id)
        if (index !== -1) {
          this.selectCoursewareList.splice(index, 1)
        }
      }
      this.$set(this.coursewareList, index, item)
      this.coursewareList[index] = item
    }

    close() {
      this.openDrawer = false
    }

    selected: CoursewareDetail = new CoursewareDetail()

    saveSelect() {
      this.$emit('confirm', this.selectCoursewareList, this.cencelArray)
      this.selectCoursewareList = new Array<CoursewareListDetail>()
      this.cencelArray = new Array<CoursewareListDetail>()
    }

    cancel() {
      this.close()
    }

    async doQuery() {
      this.page.currentChange(1)
      await this.doSearch()
    }

    async doSearch() {
      this.query.loading = true
      try {
        this.queryCoursewareListParamVo.transformStatus.current = CoursewareStatusEnum.AVAILABLE
        this.queryCoursewareListParamVo.enable = 1
        this.coursewareList = await ResourceModule.coursewareFactory.queryCourseware.pageCourseware(
          this.page,
          this.queryCoursewareListParamVo
        )
      } catch (e) {
        console.log(e)
      } finally {
        this.selectedCoursewareList()
        ;(this.$refs['table'] as ElTable)?.doLayout()
        this.query.loading = false
      }
    }

    getSelected() {
      return this.coursewareList.filter((courseDetail: CoursewareListDetail) => courseDetail.selected)
    }

    selectedAllChange(flag: boolean) {
      this.coursewareList.forEach((courseware: CoursewareListDetail) => {
        courseware.selected = flag
      })
    }
  }
</script>

<style lang="scss" scoped>
  .el-main > div:not([class]) {
    height: 0;
  }
</style>
