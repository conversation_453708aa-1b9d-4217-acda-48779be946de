import MsTradeQuery, {
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import ExchangeOrderRecordDetailVo from '@api/service/management/train-class/query/vo/ExchangeOrderRecordDetailVo'
import QueryExchangeOrderRecordListVo from '@api/service/management/train-class/query/vo/QueryExchangeOrderRecordListVo'
import QueryReplaceableTrainClassListVo from '@api/service/management/train-class/query/vo/QueryReplaceableTrainClassListVo'
import QueryWaitExchangeTrainClassListVo from '@api/service/management/train-class/query/vo/QueryWaitExchangeTrainClassListVo'
import ReplaceableTrainClassDetailVo from '@api/service/management/train-class/query/vo/ReplaceableTrainClassDetailVo'
import WaitExchangeTrainClassDataVo from '@api/service/diff/management/xmlg/train-class/model/WaitExchangeTrainClassDataVo'
import UserModule from '@api/service/management/user/UserModule'
import QueryManager from '@api/service/management/user/query/manager/QueryManager'
import AdminUserInfoVo from '@api/service/management/user/query/manager/vo/AdminUserInfoVo'
import { Page } from '@hbfe/common'
import WaitExchangeTrainClassDetailVo from '@api/service/diff/management/xmlg/train-class/model/WaitExchangeTrainClassDetailVo'
import QueryPlatform from '@api/service/diff/common/xmlg/dictionary/QueryPlatform'

/**
 * @description 换班培训班商品查询类
 */
class QueryExchangeTrainClass {
  /**
   * 查询待更换班级列表（包含退款中）
   * @param {Page} page - 分页参数
   * @param {QueryWaitExchangeTrainClassListVo} queryParams - 查询参数
   */
  async queryAllWaitExchangeTrainClassList(
    page: Page,
    queryParams: QueryWaitExchangeTrainClassListVo
  ): Promise<WaitExchangeTrainClassDataVo> {
    const userId = queryParams.buyerId
    const response = await MsTradeQuery.listBuyerAllCommodityInSerivicer(queryParams.to())
    await QueryPlatform.queryList()
    let result = new Array<WaitExchangeTrainClassDetailVo>()
    if (response.status.isSuccess() && this.isWeightyArr(response.data)) {
      result = WaitExchangeTrainClassDetailVo.batchFrom(response.data)
      const schemeIds = result.map(scheme => {
        return scheme.schemeId
      })
      const schemeStatusMap = await WaitExchangeTrainClassDetailVo.batchGetUserStatusInScheme(userId, schemeIds)
      result = result.map(scheme => {
        if (schemeStatusMap.has(scheme.schemeId)) {
          scheme.assessmentStatus = schemeStatusMap.get(scheme.schemeId).assessmentStatus
          scheme.status = schemeStatusMap.get(scheme.schemeId).status
        }
        return scheme
      })
    } else {
      result = new Array<WaitExchangeTrainClassDetailVo>()
    }
    page.totalSize = result.length
    page.totalPageSize = Math.ceil(page.totalSize / page.pageSize)
    const pageResult = result.slice((page.pageNo - 1) * page.pageSize, page.pageNo * page.pageSize)
    const res = new WaitExchangeTrainClassDataVo()
    res.pageList = pageResult
    const commoditySkuIdList = result?.map(item => {
      return item.commoditySkuId
    })
    res.commoditySkuIdList = [...new Set(commoditySkuIdList)]
    console.log('waitExchangeTrainClassData', result)
    return res
  }

  /**
   * 数组是否有质量（是否不为空）
   */
  private isWeightyArr<T>(arr: T[]): boolean {
    return Array.isArray(arr) && arr.length ? true : false
  }
}

export default QueryExchangeTrainClass
