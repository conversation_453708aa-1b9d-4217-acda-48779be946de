import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-choose-course-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-choose-course-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Range {
  key?: string
  value?: string
}

/**
 * 选课信息
<AUTHOR>
@since 2022/1/17
 */
export class ChooseCourseInfoRequest {
  /**
   * 所属课程学习大纲编号
   */
  outlineId?: string
  /**
   * 课程编号
   */
  courseId?: string
}

/**
 * 选课请求参数对象
<AUTHOR>
@since 2022/1/17
 */
export class ChooseCourseRequest {
  /**
   * 选课凭证
   */
  chooseToken?: string
  /**
   * 选择课程列表
   */
  courseList?: Array<ChooseCourseInfoRequest>
}

/**
 * 删除课程大纲以及课程包使用情况请求
<AUTHOR>
@since 2023/10/23
 */
export class DeleteCourseTrainingOutlineAndUsageRequest {
  /**
   * 事务id
   */
  transactionId?: string
  /**
   * 课程大纲ID[必填]
   */
  courseTrainingOutlineId?: string
  /**
   * 删除类型[必填]
<p>
1 - 删除课程包使用情况，大纲下课程
2 - 删除课程大纲
<p>
注意，需要先删除课程包使用情况和大纲下课程，等待大纲下课程删除完成后，再调用删除课程大纲
如果先删除课程大纲会导致大纲下课程无法删除
<p>
   */
  deleteType: number
}

export class StudentChooseCourseRequest {
  /**
   * 学员课程id
   */
  id?: string
  /**
   * 平台编号
   */
  platformId?: string
  /**
   * 平台版本编号
   */
  platformVersionId?: string
  /**
   * 项目编号
   */
  projectId?: string
  /**
   * 子项目编号
   */
  subProjectId?: string
  /**
   * 服务商编号
   */
  servicerId?: string
  /**
   * 服务商所属单位编号
   */
  unitId?: string
  /**
   * 选课时的学时，一般是从大纲内获取，若最后一门则有可能学时被重置。
   */
  period: number
  /**
   * 场景id
   */
  sceneId?: string
  /**
   * 数据范围
   */
  ranges?: Array<Range>
  /**
   * 课程类型
@see CourseTypes
   */
  courseType: number
  /**
   * 学员课程创建时间
   */
  createTime?: string
  /**
   * 学习大纲编号
   */
  outlineId?: string
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 用户id
   */
  userId?: string
  /**
   * 学号
   */
  studentNo?: string
  /**
   * 参训资格编号
   */
  qualificationId?: string
  /**
   * 场景类型
@see CourseLearningSceneTypes
   */
  sceneType: number
}

/**
 * 智能选课规则
<AUTHOR>
@since 2024/8/14
 */
export class SmartCourseSelectionConfig {
  /**
   * 智能选课类型
0-依据选课规则选课
1-指定要求学时
   */
  smartCourseSelectionType: number
  /**
   * 要求学时
smartCourseSelectionType为1时必填
   */
  smartCourseSelectionPeriod: number
  /**
   * 智能选课规则
1 - 选课出现异常时都不选
2 - 选课出现异常时只有异常课程不选(如果是重复选课，重复的两门课程都不选)
   */
  smartCourseSelectionRule: number
}

/**
 * 选课数据范围
 */
export class ChooseCourseRange {
  /**
   * 数据范围key
   */
  key: string
  /**
   * 数据范围value
   */
  value: string
}

export class StudentChooseCourseParam {
  /**
   * 学习大纲编号
   */
  outlineId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 学员课程ID
若不提供 则默认使用UUIDUtils.generate()生成
   */
  studentCourseId: string
  /**
   * 课程类型 - 智能选课使用
@see CourseTypes
   */
  courseType: number
  /**
   * 学时 - 智能选课使用
   */
  period: number
  /**
   * 大纲下课程id - 智能选课使用
   */
  courseOfOutlineId: string
}

/**
 * 推荐课程结果
<AUTHOR>
@since 2024/8/15
 */
export class FullRuleSmartRecommendCoursesResult {
  /**
   * code
   */
  code: string
  /**
   * 消息
   */
  message: string
  /**
   * 选课结果
   */
  recommendInfo: FullRuleSmartRecommendInfo
}

/**
 * 选课规则智能选课信息
@see ChoseCourseValidateResult
 */
export class FullRuleSmartRecommendInfo {
  /**
   * 平台编号
   */
  platformId: string
  /**
   * 平台版本编号
   */
  platformVersionId: string
  /**
   * 项目编号
   */
  projectId: string
  /**
   * 子项目编号
   */
  subProjectId: string
  /**
   * 服务商编号
   */
  servicerId: string
  /**
   * 服务商所属单位编号
   */
  unitId: string
  /**
   * 需要选课的列表
   */
  currentChooseCourseList: Array<StudentChooseCourseParam>
  /**
   * 选课规则id
   */
  ruleId: string
  /**
   * 场景id
   */
  sceneId: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 选课数据范围
   */
  ranges: Array<ChooseCourseRange>
  /**
   * 选课时间
   */
  chooseCourseDate: string
  /**
   * 参训资格编号
   */
  qualificationId: string
  /**
   * 场景类型
   */
  sceneType: number
  jobCategoryName: string
  /**
   * 选课方式
1-直接选课 2-重算选课
   */
  chooseMethod: number
  /**
   * 智能选课配置
   */
  smartCourseSelectionConfig: SmartCourseSelectionConfig
}

/**
 * 选课返回值
<AUTHOR>
@since 2022/1/17
 */
export class ChooseCourseResponse {
  /**
   * 选课结果代码：
500-选课失败
41001-选课失败,需要选择的课程集合为空
41002-不满足必修要求学时
41003-超过选修课允许最大学时
41004-解析选课token异常
41005-选课失败，选课范围不满足选课规则要求
51001-课程大纲内不存在该课程
51002-重复选课
   */
  code: string
  /**
   * 选课结果信息
   */
  message: string
  /**
   * 选课结果集合
   */
  chooseErrorResult: Array<CourseChoseResultResponse>
}

export class CourseChoseResultResponse {
  /**
   * 所属课程学习大纲编号
   */
  outlineId: string
  /**
   * 课程编号
   */
  courseId: string
  /**
   * 选课结果code
41002-不满足必修要求学时
41003-超过选修课允许最大学时
41004-解析选课token异常
51001-课程大纲内不存在该课程
51002-重复选课
   */
  choseResultCode: string
  /**
   * 信息
   */
  message: string
}

/**
 * <AUTHOR>
@since 2023/10/23
 */
export class DeleteOutlineResponse {
  /**
   * code
   */
  code: string
  /**
   * 选课结果信息
   */
  message: string
}

/**
 * 准备选课返回值
<AUTHOR>
@since 2022/1/17
 */
export class PrepareChooseCourseResponse {
  /**
   * 选课规则名称
   */
  name: string
  /**
   * 选课规则及当前用户已选课程值
根据选课规则类型名称决定
name &#x3D; CompulsoryAndElectivePeriodRule
{
&quot;compulsoryPeriod&quot;: {
&quot;config&quot;: 0.00,
&quot;current&quot;: 0.00
},
&quot;electiveMaxPeriod&quot;: {
&quot;config&quot;: 0.00,
&quot;current&quot;: 0.00
},
&quot;allowLastChooseOver&quot;: false
}
   */
  properties: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 请求选课
   * @param request 选课参数
   * @return 选课结果
   * @see ValidateCodeConst
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async chooseCourse(
    request: ChooseCourseRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.chooseCourse,
    operation?: string
  ): Promise<Response<ChooseCourseResponse>> {
    return commonRequestApi<ChooseCourseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 补偿接口
   * 用于直接创建学员课程
   * @param mutate 查询 graphql 语法文档
   * @param chooseCourseList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createStudentCourse(
    chooseCourseList: Array<StudentChooseCourseRequest>,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createStudentCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { chooseCourseList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 补偿接口
   * 用于删除改造前无法删除掉的课程大纲，以及课程包使用情况
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async deleteCourseTrainingOutlineAndUsage(
    request: DeleteCourseTrainingOutlineAndUsageRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteCourseTrainingOutlineAndUsage,
    operation?: string
  ): Promise<Response<DeleteOutlineResponse>> {
    return commonRequestApi<DeleteOutlineResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 准备选课
   * @param chooseToken 选课凭证
   * @return 选课规则及当前选课信息
   * @param mutate 查询 graphql 语法文档
   * @param chooseToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async prepareChooseCourse(
    chooseToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.prepareChooseCourse,
    operation?: string
  ): Promise<Response<PrepareChooseCourseResponse>> {
    return commonRequestApi<PrepareChooseCourseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { chooseToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 智能选课
   * @param chooseToken:
   * @return {@link SmartChooseCourseResponse}
   * <AUTHOR> By Cb
   * @since 2024/5/13 11:58
   * @param mutate 查询 graphql 语法文档
   * @param chooseToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async smartChooseCourse(
    chooseToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.smartChooseCourse,
    operation?: string
  ): Promise<Response<FullRuleSmartRecommendCoursesResult>> {
    return commonRequestApi<FullRuleSmartRecommendCoursesResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: { chooseToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
