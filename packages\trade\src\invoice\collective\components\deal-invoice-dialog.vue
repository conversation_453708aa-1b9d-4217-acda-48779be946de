<template>
  <el-drawer title="处理发票" :visible.sync="dealVisible" size="600px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="dealForm" :model="dealInvoiceForm" :rules="rules" label-width="auto" class="m-form f-mt20">
            <el-form-item label="发票号码：" prop="invoiceNum">
              <el-input v-model="dealInvoiceForm.invoiceNum" placeholder="请输入发票号码" />
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="dealVisible = false">取消</el-button>
              <el-button type="primary" :loading="loading" @click="dealInvoice">确定</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import MutationOffLineInvoice from '@api/service/management/trade/batch/invoice/mutation/MutationOffLineInvoice'
  import TradeModule from '@api/service/management/trade/TradeModule'

  @Component
  export default class extends Vue {
    @Ref('dealForm') dealForm: any
    @Prop({
      type: Boolean,
      default: false
    })
    dealDialog: boolean

    @Prop({
      type: String,
      default: ''
    })
    invoiceId: string
    loading = false
    mutationOffLineInvoice: MutationOffLineInvoice =
      TradeModule.batchTradeBatchFactor.invoiceFactor.mutationOffLineInvoice

    dealVisible = false
    dealInvoiceForm = {
      invoiceNum: ''
    }

    rules = {
      invoiceNum: [
        {
          required: true,
          message: '请输入发票票号',
          trigger: 'blur'
        }
      ]
    }

    @Watch('dealDialog')
    changeDialogCtrl() {
      this.dealVisible = this.dealDialog
      if (this.dealVisible) {
        this.dealInvoiceForm.invoiceNum = ''
      }
    }

    @Watch('dealVisible')
    changeDialogVisible() {
      this.$emit('update:dealDialog', this.dealVisible)
    }

    async doDealInvoice() {
      const res = await this.mutationOffLineInvoice.dealWithInvoice(this.invoiceId, this.dealInvoiceForm.invoiceNum)
      if (res.isSuccess()) {
        this.$message.success('处理发票成功')
        this.dealVisible = false
        this.$emit('dealSuccess')
      } else {
        this.$message.error('处理发票失败')
      }
    }
    //处理发票
    async dealInvoice() {
      this.loading = true
      await this.dealForm.validate(async (valid: boolean) => {
        if (valid) {
          await this.doDealInvoice()
        }
      })
      this.loading = false
    }

    created() {
      this.dealVisible = this.dealDialog
    }
  }
</script>
