import { MyOrderQueryDTO } from '@api/gateway/PlatformTrade'
import MomentUtil from '@api/service/common/helpers/util/moment'
export default class QueryOrderListParams {
  /**
   * 订单号或商品名称,如果是纯数字的则以订单号来匹配否则通过商品名称like
   */
  orderNoOrSchemeName?: string
  /**
   * 订单创建时间
   */
  createTime?: string[] = []

  /**
   * @description: 转化成网关需要的模型
   * @param {*}
   * @return {*}
   */

  toMyOrderQueryDTO() {
    const myOrderQueyDto = new MyOrderQueryDTO()
    myOrderQueyDto.orderNoOrSchemeName = this.orderNoOrSchemeName
    myOrderQueyDto.createTime =
      this.createTime && (this.createTime[0] || this.createTime[1])
        ? {
            startTime: MomentUtil.formatYMDHMS(this.createTime[0]),
            endTime: MomentUtil.formatYMDHMS(this.createTime[1])
          }
        : {}
    return myOrderQueyDto
  }
}
