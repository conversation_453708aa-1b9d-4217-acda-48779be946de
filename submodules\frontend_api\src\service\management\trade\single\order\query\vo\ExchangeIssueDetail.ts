import { ExchangeIssueOperateTypeEnum } from '@api/service/common/enums/trade/ExchangeIssueOperateType'

/**
 * 期别商品信息
 */
export class IssueCommodityDetail {
  /**
   * 期别名称
   */
  issueName = ''
  /**
   * 方案名称
   */
  schemeName = ''
}

/**
 * @description 换班订单期别信息
 */
class ExchangeIssueDetail {
  /**
   * 初始期别商品
   */
  originalIssueCommodity = new IssueCommodityDetail()
  /**
   * 新期别商品
   */
  exchangeIssueCommodity = new IssueCommodityDetail()
  /**
   * 操作时间
   */
  operateTime = ''
  /**
   * 操作类型
   */
  operateType: ExchangeIssueOperateTypeEnum = null
  /**
   * 操作人id
   */
  operatorId = ''
  /**
   * 操作人名称
   */
  operatorName = ''
}

export default ExchangeIssueDetail
