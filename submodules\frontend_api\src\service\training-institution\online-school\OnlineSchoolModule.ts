import MsBasicdata, {
  ContractProviderAdminQueryRequest,
  ContractProviderInfoAdminOwnerRequest,
  RoleRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import MsServicercontract from '@api/ms-gateway/ms-servicercontract-v1'
import Distribution from '@api/platform-gateway/platform-jxjypxtypt-distribution-service-v1'
import { CreateStatusEnum } from '@api/service/training-institution/online-school/enum/OnlineSchoolEnum'
import AdministratorModel from '@api/service/training-institution/online-school/models/AdministratorModel'
import OnlineSchoolModel from '@api/service/training-institution/online-school/models/OnlineSchoolModel'
import { Page } from '@hbfe/common'
import MsSeries, { OnlineSchoolConfigResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import { Response } from '@hbfe/common'
import { DistributionServiceTypeEnum } from '@api/service/common/capability-service-config/enum/DistributionServiceTypeEnum'

export default class OnlineSchoolModule {
  /**
   * 构造函数
   * @param onlineSchoolId 网校id
   */
  constructor(onlineSchoolId?: string) {
    this.onlineSchool.schoolId = onlineSchoolId
  }

  /**
   * 创建模型
   */
  onlineSchool: OnlineSchoolModel = new OnlineSchoolModel()

  /**
   * 查询详情
   */
  async queryDetail() {
    const res = await MsBasicdata.getOnlineSchoolInfoResponseInSubProject(this.onlineSchool.schoolId)

    if (res.data) {
      this.onlineSchool = OnlineSchoolModel.from(res.data)
    }

    return this.onlineSchool
  }

  /**
   * 查询网校是否开启分销服务
   */
  async queryDistributionService(servicerId?: string) {
    const result = new Response<{
      distributionService: boolean
      distributionServiceType: DistributionServiceTypeEnum
    }>()
    const { status, data } = await Distribution.getDistributionService({
      servicerId: servicerId || this.onlineSchool.schoolId
    })
    result.status = status
    if (status.isSuccess() && data) {
      result.data = {
        distributionService: data.distributionService,
        distributionServiceType: data.distributionServiceType
      }
    }
    return result
  }
  /**
   * 查询详情
   */
  async queryAdminAccountList(page: Page) {
    const request = new ContractProviderAdminQueryRequest()
    request.owner = new ContractProviderInfoAdminOwnerRequest()
    request.owner.servicerIdList = new Array<string>()
    request.owner.servicerIdList.push(this.onlineSchool.schoolId)
    request.role = new RoleRequest()
    request.role.roleCategoryList = [510]
    const res = await MsBasicdata.pageOnlineSchoolAdminInfoInSubProject({ page, request })

    if (res.status.isSuccess()) {
      this.onlineSchool.administratorList = res?.data?.currentPageData?.map(item => AdministratorModel.from(item)) || []
    }
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    return this.onlineSchool.administratorList
  }

  /**
   * 创建网校
   */
  async createOnlineSchool() {
    const request = this.onlineSchool.toCreateRequest()
    request.status = CreateStatusEnum.CREATE
    const res = await MsServicercontract.createOnlineSchoolContract(request)
    return res
  }

  /**
   * 创建网校（草稿）
   */
  async createOnlineSchoolDraft() {
    const request = this.onlineSchool.toCreateRequest()
    request.status = CreateStatusEnum.DRAFT
    const res = await MsServicercontract.createOnlineSchoolContract(request)
    return res
  }

  /**
   * 更新基础信息
   */
  async updateSchoolBase() {
    const request = this.onlineSchool.toUpdateBaseRequest()

    const res = await MsServicercontract.updateOSBasicInfo(request)
    return res
  }

  /**
   * 更新网校配置
   */
  async updateSchoolConfig() {
    const request = this.onlineSchool.toUpdateConfigRequest()

    const res = await MsServicercontract.updateOnlineSchool(request)
    return res
  }

  /**
   * 更新网校模板
   * @param client 客户端
   */
  async updateSchoolTemplate() {
    const request = this.onlineSchool.toUpdateTemplateRequest()

    const res = await MsServicercontract.updateOSTemplate(request)

    return res
  }
  /**
   * 获取网校配置口（seo、验证、完善信息）
   */
  async getOnlineSchoolConfig() {
    const res = await MsSeries.getOnlineSchoolConfig()
    if (res.status.isSuccess() && res.data) {
      return res
    } else {
      return new Response<OnlineSchoolConfigResponse>()
    }
  }
}
