import EducationalManageParam from '@api/service/management/implement/models/EducationalManageParam'
import { Page, ResponseStatus } from '@hbfe/common'
import EducationalInfo from '@api/service/management/implement/models/EducationalInfo'
import PlatformAhjspxpt, {
  IssueLearningResultImportRequest
} from '@api/platform-gateway/platform-jxjy-learning-result-v1'
import PlatformDataExportFrontGateway, {
  UserInfoRequest
} from '@api/platform-gateway/diff-ms-data-export-front-gateway-DataExportBackstage'
import DiffSchemeLearningQueryFront, {
  AppointSchemeIssueRegistrationResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

export default class EducationalManage {
  /**
   * 方案id
   */
  private schemeId: string = undefined

  /**
   * 期别id
   */
  private periodId: string = undefined

  /**
   * 教务筛选参数
   */
  params: EducationalManageParam = new EducationalManageParam()

  /**
   * 列表
   */
  list: Array<EducationalInfo> = new Array<EducationalInfo>()

  /**
   * @param schemeId 方案id
   * @param periodId 期别id
   */
  constructor(schemeId: string, periodId: string) {
    this.schemeId = schemeId
    this.periodId = periodId
  }

  /**
   * 查询教务信息
   * @param page 分页
   */
  async queryList(page: Page) {
    const res = await DiffSchemeLearningQueryFront.pageAppointStudentSchemeIssueRegistrationInServicer({
      page,
      issueId: this.periodId,
      request: this.params.toRequest()
    })
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize

    const dtoList = res?.data?.currentPageData
    if (dtoList.length) {
      this.list = dtoList.map((item: AppointSchemeIssueRegistrationResponse) => {
        return EducationalInfo.from(item)
      })
    } else {
      this.list = new Array<EducationalInfo>()
    }
  }

  /**
   * 获取成绩导入模版
   */
  async getImportCompletionTemplate() {
    // getImportGradeResultTaskTemplatePath
    const res = await PlatformAhjspxpt.getImportGradeResultTaskTemplatePath()

    return res?.data
  }

  /**
   * 导入结业成果
   * @param filePath 文件路径
   * @param fileName 文件名称
   */
  async importCompletionResult(filePath: string, fileName: string) {
    const request = new IssueLearningResultImportRequest()
    request.issueId = this.periodId
    request.importFilePath = filePath
    request.importFileName = fileName
    const res = await PlatformAhjspxpt.importIssueLearningResult(request)

    if (res?.status && res.status.isSuccess()) {
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(200, '系统异常'))
    }
  }

  /**
   * 导出列表数据
   */
  async exportDataList() {
    const request = new UserInfoRequest()
    request.phoneNumber = this.params.phone
    request.name = this.params.name
    request.idCard = this.params.idCard
    const res = await PlatformDataExportFrontGateway.exportSchemeLearningEnrollmentInServicer({
      issueId: this.periodId,
      request: request
    })

    if (res?.data) {
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(200, '系统异常'))
    }
  }
}
