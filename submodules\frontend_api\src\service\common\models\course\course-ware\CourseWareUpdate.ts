class CourseWareUpdate {
  /**
   * 课件id
   */
  id = ''
  /**
   * 课件名称
   */
  name = ''
  /**
   * 课件时长，该属性只限文档型课件设置
   */
  timeLength = -1
  /**
   * 所属课件目录编号
   */
  courseChapterId = ''
  /**
   * 课件简介
   */
  abouts = ''
  /**
   * 课件分类ID
   */
  cwyId = ''
  /**
   * 是否可用
   */
  usable = true
  /**
   * 课件解析资源存放的路径
   */
  coursewareResourcePath = ''
  /**
   * 若是大文件上传，需要异步合并时，必须填文件的MD5
   */
  resourceMD5 = ''
  /**
   * 挂在课件是否支持试听 0不可以试听，1可以试听，
   */
  customeStatus: number
}

export default CourseWareUpdate
