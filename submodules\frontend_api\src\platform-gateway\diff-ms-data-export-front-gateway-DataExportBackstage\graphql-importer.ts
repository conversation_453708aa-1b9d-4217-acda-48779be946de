import BatchStudentLearningExperienceExportPdf from './queries/BatchStudentLearningExperienceExportPdf.graphql'
import exportBlendedSchemeLearningReportFormsDetailExcelInServicer from './queries/exportBlendedSchemeLearningReportFormsDetailExcelInServicer.graphql'
import exportBlendedSchemeLearningReportFormsExcelInServicer from './queries/exportBlendedSchemeLearningReportFormsExcelInServicer.graphql'
import exportBlendedStudentSchemeLearningExcelInDistributor from './queries/exportBlendedStudentSchemeLearningExcelInDistributor.graphql'
import exportBlendedStudentSchemeLearningExcelInServicer from './queries/exportBlendedStudentSchemeLearningExcelInServicer.graphql'
import exportBlendedStudentSchemeLearningExcelInServicerManageRegion from './queries/exportBlendedStudentSchemeLearningExcelInServicerManageRegion.graphql'
import exportBlendedStudentSchemeLearningExcelInTrainingChannel from './queries/exportBlendedStudentSchemeLearningExcelInTrainingChannel.graphql'
import exportFaceToFaceSchemeLearningReportFormsDetailExcelInServicer from './queries/exportFaceToFaceSchemeLearningReportFormsDetailExcelInServicer.graphql'
import exportFaceToFaceSchemeLearningReportFormsExcelInServicer from './queries/exportFaceToFaceSchemeLearningReportFormsExcelInServicer.graphql'
import exportFaceToFaceStudentSchemeLearningExcelInDistributor from './queries/exportFaceToFaceStudentSchemeLearningExcelInDistributor.graphql'
import exportFaceToFaceStudentSchemeLearningExcelInServicer from './queries/exportFaceToFaceStudentSchemeLearningExcelInServicer.graphql'
import exportFaceToFaceStudentSchemeLearningExcelInServicerManageRegion from './queries/exportFaceToFaceStudentSchemeLearningExcelInServicerManageRegion.graphql'
import exportFaceToFaceStudentSchemeLearningExcelInTrainingChannel from './queries/exportFaceToFaceStudentSchemeLearningExcelInTrainingChannel.graphql'
import exportQualificationStudentSchemeLearningDetailInServicer from './queries/exportQualificationStudentSchemeLearningDetailInServicer.graphql'
import exportQuestionExcelInServicer from './queries/exportQuestionExcelInServicer.graphql'
import exportRegionLearningReportFormsDetailExcelInServicer from './queries/exportRegionLearningReportFormsDetailExcelInServicer.graphql'
import exportRegionLearningReportFormsDetailExcelInServicerManageRegion from './queries/exportRegionLearningReportFormsDetailExcelInServicerManageRegion.graphql'
import exportRegionLearningReportFormsExcelInServicer from './queries/exportRegionLearningReportFormsExcelInServicer.graphql'
import exportRegionLearningReportFormsExcelInServicerManageRegion from './queries/exportRegionLearningReportFormsExcelInServicerManageRegion.graphql'
import exportSchemeLearningEnrollmentInServicer from './queries/exportSchemeLearningEnrollmentInServicer.graphql'
import exportSchemeLearningReportFormsDetailExcelInServicer from './queries/exportSchemeLearningReportFormsDetailExcelInServicer.graphql'
import exportSchemeLearningReportFormsExcelInServicer from './queries/exportSchemeLearningReportFormsExcelInServicer.graphql'
import exportStudentLearningExperienceInServicer from './queries/exportStudentLearningExperienceInServicer.graphql'
import exportStudentSchemeLearningExcelInDistributor from './queries/exportStudentSchemeLearningExcelInDistributor.graphql'
import exportStudentSchemeLearningExcelInServicer from './queries/exportStudentSchemeLearningExcelInServicer.graphql'
import exportStudentSchemeLearningExcelInServicerManageRegion from './queries/exportStudentSchemeLearningExcelInServicerManageRegion.graphql'
import exportStudentSchemeLearningExcelInTrainingChannel from './queries/exportStudentSchemeLearningExcelInTrainingChannel.graphql'

export {
  BatchStudentLearningExperienceExportPdf,
  exportBlendedSchemeLearningReportFormsDetailExcelInServicer,
  exportBlendedSchemeLearningReportFormsExcelInServicer,
  exportBlendedStudentSchemeLearningExcelInDistributor,
  exportBlendedStudentSchemeLearningExcelInServicer,
  exportBlendedStudentSchemeLearningExcelInServicerManageRegion,
  exportBlendedStudentSchemeLearningExcelInTrainingChannel,
  exportFaceToFaceSchemeLearningReportFormsDetailExcelInServicer,
  exportFaceToFaceSchemeLearningReportFormsExcelInServicer,
  exportFaceToFaceStudentSchemeLearningExcelInDistributor,
  exportFaceToFaceStudentSchemeLearningExcelInServicer,
  exportFaceToFaceStudentSchemeLearningExcelInServicerManageRegion,
  exportFaceToFaceStudentSchemeLearningExcelInTrainingChannel,
  exportQualificationStudentSchemeLearningDetailInServicer,
  exportQuestionExcelInServicer,
  exportRegionLearningReportFormsDetailExcelInServicer,
  exportRegionLearningReportFormsDetailExcelInServicerManageRegion,
  exportRegionLearningReportFormsExcelInServicer,
  exportRegionLearningReportFormsExcelInServicerManageRegion,
  exportSchemeLearningEnrollmentInServicer,
  exportSchemeLearningReportFormsDetailExcelInServicer,
  exportSchemeLearningReportFormsExcelInServicer,
  exportStudentLearningExperienceInServicer,
  exportStudentSchemeLearningExcelInDistributor,
  exportStudentSchemeLearningExcelInServicer,
  exportStudentSchemeLearningExcelInServicerManageRegion,
  exportStudentSchemeLearningExcelInTrainingChannel
}
