<route-meta>
  {
  "isMenu": true,
  "title": "集体报名退款订单",
  "sort": 2,
  "icon": "icon_menhuxinxiguanli"
  }
</route-meta>
<template>
  <el-main>
    <div
      class="f-p15"
      v-if="$hasPermission('query,queryfx,queryzt')"
      desc="query:查询退款列表,queryfx:查询退款列表（分销）,queryzt:查询退款列表（专题）"
      actions="query:doQueryPage,@BizDistributorSelect,@BizPortalSelect#queryfx:doQueryPagefx,@BizPortalDistributorSelect#queryzt:doQueryPagezt"
    >
      <el-card shadow="never" class="m-card f-mb15" id="search-item">
        <hb-search-wrapper expand :model="returnOrderRequestVo" @reset="resetParams">
          <el-form-item label="报名批次号">
            <el-input v-model="returnOrderRequestVo.batchOrderNo" clearable placeholder="请输入集体报名批次号" />
          </el-form-item>
          <el-form-item label="退款单号">
            <el-input v-model="returnOrderRequestVo.refundNo" clearable placeholder="请输入集体报名退款单号" />
          </el-form-item>
          <el-form-item label="交易流水号">
            <el-input v-model="returnOrderRequestVo.flowNo" clearable placeholder="请输入交易流水号" />
          </el-form-item>
          <el-form-item label="退款状态">
            <el-select v-model="returnOrderRequestVo.refundStatus" clearable filterable placeholder="请选择退款状态">
              <el-option label="全部" value=""></el-option>
              <el-option label="退款审批中" value="1"></el-option>
              <el-option label="退款处理中" value="2"></el-option>
              <el-option label="退款成功" value="3"></el-option>
              <el-option label="退款失败" value="4"></el-option>
              <el-option label="拒绝退款" value="5"></el-option>
              <el-option label="已取消" value="6"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="帐号">
            <el-input v-model="returnOrderRequestVo.buyerAccount" clearable placeholder="请输入帐号" />
          </el-form-item>

          <el-form-item label="姓名">
            <el-input v-model="returnOrderRequestVo.buyerName" clearable placeholder="请输入购买人姓名" />
          </el-form-item>
          <el-form-item label="退款申请时间">
            <double-date-picker
              :begin-create-time.sync="returnOrderRequestVo.refoundStartDate"
              :end-create-time.sync="returnOrderRequestVo.refoundEndDate"
              begin-time-placeholder="退款申请时间"
              end-time-placeholder="退款申请时间"
            ></double-date-picker>
          </el-form-item>
          <el-form-item label="审批时间">
            <double-date-picker
              :begin-create-time.sync="returnOrderRequestVo.approvalStartDate"
              :end-create-time.sync="returnOrderRequestVo.approvalEndDate"
              begin-time-placeholder="审批时间"
              end-time-placeholder="审批时间"
            ></double-date-picker>
          </el-form-item>
          <el-form-item label="退款成功时间">
            <double-date-picker
              :begin-create-time.sync="returnOrderRequestVo.refoundSuccessStartDate"
              :end-create-time.sync="returnOrderRequestVo.refoundSuccessEndDate"
              begin-time-placeholder="退款成功时间"
              end-time-placeholder="退款成功时间"
            ></double-date-picker>
          </el-form-item>

          <el-form-item label="销售渠道" v-if="!isFxlogin && !isZtlogin">
            <el-select v-model="returnOrderRequestVo.saleSource" clearable filterable placeholder="请选择销售渠道">
              <el-option
                v-for="item in saleChannelList"
                :key="item.code"
                :value="item.code"
                :label="item.desc"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="专题名称" v-if="topPicNameFilterShow && !isFxlogin">
            <el-input v-model="returnOrderRequestVo.specialSubjectName" clearable placeholder="请输入专题进行查询" />
          </el-form-item>
          <el-form-item label="缴费方式">
            <payment-mode v-model="returnOrderRequestVo.paymentMethod"></payment-mode>
          </el-form-item>
          <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
            <biz-portal-select
              v-model="returnOrderRequestVo.promotionPortalId"
              :name="promotionPortalName"
            ></biz-portal-select>
          </el-form-item>
          <el-form-item v-if="isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
            <biz-portal-distributor-select
              v-model="returnOrderRequestVo.promotionPortalId"
              :name="promotionPortalName"
            ></biz-portal-distributor-select>
          </el-form-item>
          <!--  v-if="isFXshow" -->
          <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="分销商">
            <biz-distributor-select
              v-model="returnOrderRequestVo.distributorId"
              :name="distributorName"
            ></biz-distributor-select>
          </el-form-item>
          <!--  v-if="isFXshow" -->
          <template slot="actions">
            <el-button type="primary" @click="search">查询</el-button>
          </template>
        </hb-search-wrapper>
        <div class="more-actions">
          <template v-if="$hasPermission('batchAgreeRefund')" desc="批量同意退款" actions="handleBatchAgree">
            <el-button type="primary" v-if="!isZtlogin" @click="showBatchDialog(true)">批量同意退款</el-button>
          </template>
          <template v-if="$hasPermission('batchConfirmRefund')" desc="批量确认退款" actions="handleBatchConfirm">
            <el-button type="primary" v-if="!isZtlogin" @click="showBatchDialog(false)">批量确认退款</el-button>
          </template>
          <template>
            <el-button
              @click="exportRefund"
              v-if="$hasPermission('export,exportfx,exportzt')"
              desc="export:导出,exportfx:导出（分销）,exportzt:导出（专题）"
              actions="export:exportRefundty#exportfx:exportRefundfx#exportzt:exportRefundzt"
            >
              导出列表数据
            </el-button>
          </template>
          <template>
            <el-button
              @click="exportRefundDetail"
              v-if="$hasPermission('exportDetail')"
              desc="导出明细"
              actions="exportRefundDetail"
            >
              导出退款明细
            </el-button>
          </template>
        </div>
        <!--操作栏-->
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert">
            <div class="f-c6">
              当前共有
              <span class="f-fb f-co">{{
                isZtlogin ? queryZtRefund.totalRefoundCount : queryRefundOrder.totalRefoundCount
              }}</span>
              笔退款订单，退款金额
              <span class="f-fb f-co"
                >¥ {{ isZtlogin ? queryZtRefund.totalRefoundAmount : queryRefundOrder.totalRefoundAmount }}</span
              >。
            </div>
          </el-alert>
        </div>
        <!--表格-->
        <el-table
          stripe
          :data="returnOrderResponseVo"
          @selection-change="tableSelect"
          max-height="500px"
          class="m-table f-mt10"
          ref="refundTable"
        >
          <el-table-column type="selection" v-if="!isZtlogin" width="55" align="center" fixed="left"></el-table-column>
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="集体报名退款单号" min-width="300">
            <template slot-scope="scope">
              {{ scope.row.refoundNo }}
              <hb-copy :content="scope.row.refoundNo"></hb-copy>
              <p class="f-c9">所属批次号：{{ scope.row.batchOrderNo }}</p>
              <el-tag type="success" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="交易流水号" prop="flowNo" min-width="300"> </el-table-column>
          <el-table-column label="缴费方式" min-width="120" align="center">
            <template slot-scope="scope">{{ PaymentMethod.map.get(scope.row.paymentMethod) }}</template>
          </el-table-column>
          <el-table-column label="退款人次" prop="refoundCount" min-width="120" align="center"> </el-table-column>
          <el-table-column label="实付金额/退款金额" width="140" align="right">
            <template slot-scope="scope">
              <p>{{ scope.row.payAmount || 0 }}/{{ scope.row.refoundAmount || 0 }}</p>
            </template>
          </el-table-column>

          <el-table-column label="购买人信息" min-width="240">
            <template slot-scope="scope">
              <p>姓名：{{ scope.row.buyerName }}</p>
              <p>账号：{{ scope.row.buyerAccount }}</p>
            </template>
          </el-table-column>
          <el-table-column label="申请时间 / 审批时间" min-width="220">
            <template slot-scope="scope">
              <p>申请：{{ scope.row.refoundDate || '-' }}</p>
              <p>审批：{{ scope.row.approvalDate || '-' }}</p>
            </template>
          </el-table-column>
          <el-table-column label="退款成功时间" min-width="180">
            <template slot-scope="scope">{{ scope.row.refoundSuccessDate || '-' }}</template>
          </el-table-column>
          <el-table-column label="退款状态" min-width="150">
            <template slot-scope="scope">
              <div v-if="scope.row.refundStatus === 1">
                <el-badge is-dot type="primary" class="badge-status">退款审批中</el-badge>
              </div>
              <div
                v-else-if="scope.row.refundStatus === 2 || scope.row.refundStatus === 7 || scope.row.refundStatus === 8"
              >
                <el-badge is-dot type="primary" class="badge-status">退款处理中</el-badge>
              </div>
              <div v-else-if="scope.row.refundStatus === 3">
                <el-badge is-dot type="success" class="badge-status">退款成功</el-badge>
              </div>
              <div v-else-if="scope.row.refundStatus === 4">
                <el-badge is-dot type="danger" class="badge-status">退款失败</el-badge>
              </div>
              <div v-else-if="scope.row.refundStatus === 5">
                <el-badge is-dot type="danger" class="badge-status">拒绝退款</el-badge>
              </div>
              <div v-else-if="scope.row.refundStatus === 6">
                <el-badge is-dot type="danger" class="badge-status">已取消</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="goDetail(scope.row.refoundNo)">详情</el-button>
              <template
                v-if="$hasPermission('refundApproval')"
                desc="退款审批"
                actions="cancelRefund,refuseRefund,agreeRefund"
              >
                <span v-if="scope.row.refundStatus === 1 && !isZtlogin">
                  <el-button type="text" size="mini" @click="openCancelRefund(scope.row)">取消退款</el-button>
                  <el-button type="text" size="mini" @click="openRefuseRefund(scope.row)">拒绝退款</el-button>
                  <el-button
                    type="text"
                    size="mini"
                    v-if="isWft(scope.row)"
                    @click="wftDialog(scope.row)"
                    :loading="query.loading"
                    >同意退款</el-button
                  >
                  <el-button type="text" size="mini" v-else @click="agreeRefund(scope.row)" :loading="query.loading"
                    >同意退款</el-button
                  >
                </span>
                <span v-if="scope.row.refundStatus === 4 && !isZtlogin">
                  <el-button type="text" size="mini" @click="retryRefund(scope.row)">继续退款</el-button>
                </span>
                <span v-if="scope.row.refundStatus === 7 && !isZtlogin">
                  <el-button type="text" size="mini" @click="retryRecycleRefund(scope.row)">重新回收资源</el-button>
                </span>
                <span v-if="scope.row.refundStatus === 8 && !isZtlogin">
                  <el-button type="text" size="mini" @click="confirmRefund(scope.row)">确认退款</el-button>
                </span>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
        <el-drawer title="取消退款申请" :visible.sync="cancelRefundDialog" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              确认取消该订单的退款申请？取消后需要重新发起退款！
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="取消原因：" required>
                    <el-input type="textarea" :rows="6" v-model="cancelReason" placeholder="请输入取消原因" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button @click="cancelRefundDialog = false">取消</el-button>
                    <el-button type="primary" @click="cancelRefund">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <el-drawer title="拒绝退款申请" :visible.sync="refuseRefundDialog" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="拒绝退款原因：" required>
                    <el-input type="textarea" :rows="6" v-model="cancelReason" placeholder="请输入拒绝退款原因" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button @click="refuseRefundDialog = false">取消</el-button>
                    <el-button type="primary" @click="refuseRefund">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">{{ exportText }}</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
          </div>
        </el-dialog>
        <el-dialog title="提示" :visible.sync="showBatchAgree" width="450px" class="m-dialog">
          <div class="f-c6">
            当前选择 <span class="f-fb f-co">{{ selectTotal.agreeTotal }}</span> 笔退款订单， 退款金额
            <span class="f-fb f-co">¥ {{ selectTotal.agreeSum }}</span
            >，是否确认批量同意退款？
          </div>
          <div slot="footer">
            <el-button @click="showBatchAgree = false">取消</el-button>
            <el-button :loading="btnLoading" type="primary" @click="handleBatchAgree">确认同意退款</el-button>
          </div>
        </el-dialog>
        <el-dialog title="提示" :visible.sync="showBatchConfirm" width="450px" class="m-dialog">
          <div class="f-c6">
            当前选择 <span class="f-fb f-co">{{ selectTotal.confirmTotal }}</span> 笔退款订单， 退款金额
            <span class="f-fb f-co">¥ {{ selectTotal.confirmSum }}</span
            >，是否确认批量同意退款？
          </div>
          <div slot="footer">
            <el-button @click="showBatchConfirm = false">取消</el-button>
            <el-button :loading="btnLoading" type="primary" @click="handleBatchConfirm">确认退款</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script lang="ts">
  import {
    BatchReturnOrderSortField,
    BatchReturnOrderSortRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { SortPolicy } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { BatchRefundTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
  import PaymentMethod from '@api/service/management/trade/batch/order/enum/PaymentMethod'
  import MutationBatchOrderRefund from '@api/service/management/trade/batch/order/mutation/MutationBatchOrderRefund'
  import QueryBatchRefundList from '@api/service/management/trade/batch/order/query/QueryBatchRefund'
  import BatchRefoundListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchRefoundListDetailVo'
  import QueryBatchRefundListParamVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchRefundListParamVo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { Query, UiPage } from '@hbfe/common'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import PaymentMode from '@hbfe/jxjy-admin-trade/src/order/collective/components/payment-mode.vue'
  import { ElTable } from 'element-ui/types/table'
  import { bind, debounce } from 'lodash-decorators'
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'

  import QueryBatchRefundInTrainingChannel from '@api/service/management/trade/batch/order/query/QueryBatchRefundInTrainingChannel'
  @Component({
    components: {
      DoubleDatePicker,
      BizPortalSelect,
      BizDistributorSelect,
      BizPortalDistributorSelect,
      PaymentMode
    }
  })
  export default class extends Vue {
    @Ref('refundTable')
    refundTable: ElTable
    SaleChannelEnum = SaleChannelEnum

    form = {
      date1: ''
    }
    // 是否来源专题
    isFromSpecialSubject: boolean = null
    input = ''
    select = ''
    // 缴费方式枚举
    PaymentMethod = PaymentMethod
    // 页面分页控件
    page: UiPage
    // 分页查询
    query: Query = new Query()
    //查询接口请求
    queryRefundOrder: QueryBatchRefundList =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchRefound
    //查询接口结果
    returnOrderResponseVo: Array<BatchRefoundListDetailVo> = new Array<BatchRefoundListDetailVo>()
    //接口查询参数
    returnOrderRequestVo: QueryBatchRefundListParamVo = new QueryBatchRefundListParamVo()
    //导出接口查询入参
    exportQueryParam: QueryBatchRefundListParamVo = new QueryBatchRefundListParamVo()
    //业务接口请求
    mutationBatchOrderRefund: MutationBatchOrderRefund =
      TradeModule.batchTradeBatchFactor.orderFactor.mutationOrderFactor.mutationBatchOrderRefund
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    promotionPortalName = ''
    distributorName = ''
    //导出成功弹窗
    exportSuccessVisible = false
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    //取消退款弹窗标识
    cancelRefundDialog = false
    //拒绝退款弹窗标识
    refuseRefundDialog = false
    //批量同意退款弹窗标识
    showBatchAgree = false
    //批量确认退款弹窗标识
    showBatchConfirm = false
    //确认按钮loading
    btnLoading = false
    //退款原因
    cancelReason = ''
    //退款当前项数据
    currentItem: BatchRefoundListDetailVo = new BatchRefoundListDetailVo()
    //流水号
    flowNo = ''
    //选择列表
    selectdList: Array<BatchRefoundListDetailVo> = new Array<BatchRefoundListDetailVo>()
    //选择列表订单号
    selectdNoList: string[] = []
    // 导出类型
    exportType = 'exportBatchReturnOrder'
    // 导出提示文字
    exportText = ''
    //专题查询
    queryZtRefund = new QueryBatchRefundInTrainingChannel()
    BatchRefundTradeStatusEnum = {
      [BatchRefundTradeStatusEnum.REFUNDING]: '退款审批中',
      [BatchRefundTradeStatusEnum.REFUNDDISPOSE]: '退款处理中',
      [BatchRefundTradeStatusEnum.REFUNDSUCCESS]: '退款成功',
      [BatchRefundTradeStatusEnum.REFUNDFAIL]: '退款失败',
      [BatchRefundTradeStatusEnum.REFUSEDREFUND]: '拒绝退款',
      [BatchRefundTradeStatusEnum.CANCELREFUND]: '取消退款'
    }
    //排序入参
    sortRequest: Array<BatchReturnOrderSortRequest> = new Array<BatchReturnOrderSortRequest>()

    @Watch('$route', {
      immediate: true,
      deep: true
    })
    async routeChange(val: any) {
      console.log('$route', val)
      this.batchOrderNo = val.query?.batchOrderNo ?? ''
      this.firstLoad = true
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else if (this.isZtlogin) {
        await this.doQueryPagezt()
      } else {
        await this.doQueryPage()
      }
      this.firstLoad = false
    }

    /**
     * 批次订单号
     */
    batchOrderNo = ''

    /**
     * 第一次加载
     */
    firstLoad = false

    @Watch('batchOrderNo', {
      immediate: true,
      deep: true
    })
    batchOrderNoChange(val: any) {
      console.log('batchOrderNo', val)
    }

    // 获取销售渠道列表
    saleChannelList = [
      {
        code: SaleChannelEnum.self,
        desc: '网校'
      },
      {
        code: SaleChannelEnum.distribution,
        desc: '分销'
      },
      {
        code: SaleChannelEnum.topic,
        desc: '专题'
      }
    ]

    // 专题名称筛选显示
    get topPicNameFilterShow() {
      return (
        this.returnOrderRequestVo.saleSource === SaleChannelEnum.topic ||
        (!this.returnOrderRequestVo.saleSource && this.returnOrderRequestVo.saleSource !== SaleChannelEnum.self)
      )
    }
    //计算选择列表金额
    get selectTotal() {
      console.log(this.selectdList, 'selectdList')
      let agreeTotal = 0
      let agreeSum = 0
      let confirmTotal = 0
      let confirmSum = 0
      this.selectdList.forEach((item: BatchRefoundListDetailVo) => {
        if (item.refundStatus === 1) {
          agreeTotal++
          agreeSum = CalculatorObj.add(agreeSum, item.refoundAmount)
        }
        if (item.refundStatus === 2 || item.refundStatus === 7 || item.refundStatus === 8) {
          confirmTotal++
          confirmSum = CalculatorObj.add(confirmSum, item.refoundAmount)
        }
      })
      return { agreeTotal, agreeSum, confirmTotal, confirmSum }
    }

    tableSelect(selection: BatchRefoundListDetailVo[]) {
      this.selectdList = selection
    }

    constructor() {
      super()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.page = new UiPage(this.doQueryPagefx, this.doQueryPagefx)
      } else if (this.isZtlogin) {
        this.page = new UiPage(this.doQueryPagezt, this.doQueryPagezt)
      } else {
        this.page = new UiPage(this.doQueryPage, this.doQueryPage)
      }
    }
    async doQueryPage() {
      this.query.loading = true
      this.sortRequest = []
      try {
        if (this.firstLoad) {
          this.returnOrderRequestVo.batchOrderNo = this.batchOrderNo
        }
        const item = new BatchReturnOrderSortRequest()
        item.field = BatchReturnOrderSortField.CREATED_TIME
        item.policy = SortPolicy.DESC
        this.sortRequest.push(item)
        this.returnOrderResponseVo = await this.queryRefundOrder.queryBatchRefoundList(
          this.page,
          this.returnOrderRequestVo,
          this.sortRequest
        )
        console.log('this.returnOrderResponseVo', this.returnOrderResponseVo)

        this.refundTable.doLayout()
      } catch (e) {
        console.log(e, '加载个人报名退款订单失败')
      } finally {
        this.queryRefundOrder.totalRefoundCount
        this.query.loading = false
        this.refundTable.doLayout()
      }
    }

    async search() {
      this.page.pageNo = 1
      //专题
      this.returnOrderRequestVo.isFromSpecialSubject = this.isFromSpecialSubject
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else if (this.isZtlogin) {
        await this.doQueryPagezt()
      } else {
        await this.doQueryPage()
      }
    }
    // 查询专题列表
    async doQueryPagezt() {
      this.query.loading = true
      this.sortRequest = []
      try {
        if (this.firstLoad) {
          this.returnOrderRequestVo.batchOrderNo = this.batchOrderNo
        }
        const item = new BatchReturnOrderSortRequest()
        item.field = BatchReturnOrderSortField.CREATED_TIME
        item.policy = SortPolicy.DESC
        this.sortRequest.push(item)
        this.returnOrderResponseVo = await this.queryZtRefund.queryBatchRefoundList(
          this.page,
          this.returnOrderRequestVo,
          this.sortRequest
        )
        this.refundTable.doLayout()
      } catch (e) {
        console.log(e, '加载个人报名退款订单失败')
      } finally {
        this.queryRefundOrder.totalRefoundCount
        this.query.loading = false
        this.refundTable.doLayout()
      }
    }
    // 查询分销列表
    async doQueryPagefx() {
      this.query.loading = true
      this.sortRequest = []
      try {
        if (this.firstLoad) {
          this.returnOrderRequestVo.batchOrderNo = this.batchOrderNo
        }
        const item = new BatchReturnOrderSortRequest()
        item.field = BatchReturnOrderSortField.CREATED_TIME
        item.policy = SortPolicy.DESC
        this.sortRequest.push(item)
        this.returnOrderResponseVo = await this.queryRefundOrder.queryFxBatchRefoundList(
          this.page,
          this.returnOrderRequestVo,
          this.sortRequest
        )
        this.refundTable.doLayout()
      } catch (e) {
        console.log(e, '加载个人报名退款订单失败')
      } finally {
        this.queryRefundOrder.totalRefoundCount
        this.query.loading = false
        this.refundTable.doLayout()
      }
    }

    async resetParams() {
      this.page.pageNo = 1
      this.returnOrderRequestVo = new QueryBatchRefundListParamVo()
      this.isFromSpecialSubject = null
      this.promotionPortalName = ''
      this.distributorName = ''
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else if (this.isZtlogin) {
        await this.doQueryPagezt()
      } else {
        await this.doQueryPage()
      }
    }
    goDetail(id: string) {
      this.$router.push('/training/trade/refund/collective/detail/' + id)
    }
    /*
     * 取消退款弹窗
     * */
    openCancelRefund(item: BatchRefoundListDetailVo) {
      this.currentItem = item
      this.cancelReason = ''
      this.cancelRefundDialog = true
    }
    /*
     * 拒绝退款弹窗
     * */
    openRefuseRefund(item: BatchRefoundListDetailVo) {
      this.currentItem = item
      this.cancelReason = ''
      this.refuseRefundDialog = true
    }
    /*
     * 取消退款
     * */
    @bind
    @debounce(200)
    async cancelRefund() {
      if (!this.cancelReason) {
        this.$message.error('请填写取消退款原因')
        return
      }
      this.query.loading = true
      //this.cancelReason  待放入

      const status = await this.mutationBatchOrderRefund.cancelRefund(this.currentItem.refoundNo, this.cancelReason)
      if (status?.code == '200') {
        this.query.loading = false
        this.cancelRefundDialog = false
        this.$message.success('取消退款成功')
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.doQueryPagefx()
        } else {
          await this.doQueryPage()
        }
      } else {
        this.query.loading = false
        this.$message.error('该批次退款申请正在处理中，暂无法执行其他操作。')
        return
      }
    }
    /*
     * 拒绝退款
     * */
    @bind
    @debounce(200)
    async refuseRefund() {
      if (!this.cancelReason) {
        this.$message.error('请填写拒绝退款原因')
        return
      }
      this.query.loading = true
      //this.cancelReason  待放入
      const res = await this.mutationBatchOrderRefund.refuseRefund(this.currentItem.refoundNo, this.cancelReason)
      if (res?.code == '200') {
        this.query.loading = false
        this.refuseRefundDialog = false
        this.$message.success('拒绝退款成功')
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.doQueryPagefx()
        } else {
          await this.doQueryPage()
        }
      } else {
        this.query.loading = false
        this.$message.error('该批次退款申请正在处理中，暂无法执行其他操作。')
        return
      }
    }
    /**
     * 判断是否是威富通以及线下
     */
    isWft(item: BatchRefoundListDetailVo) {
      if (item.flowNo.includes('WFT') && item.refundOrderType == 1) {
        return true
      } else {
        return false
      }
    }

    /**
     * 威富通退款点击事件
     * @param item
     */
    wftDialog(item: BatchRefoundListDetailVo) {
      this.$alert('兴业聚合支付（威富通）线上退款到账需要1~3个工作日，确认退款吗?', '提示', {
        confirmButtonText: '确认',
        type: 'warning'
      }).then(() => {
        this.agreeRefund(item)
      })
    }
    /*
     * 同意退款
     * */
    async agreeRefund(item: BatchRefoundListDetailVo) {
      this.query.loading = true
      const status = await this.mutationBatchOrderRefund.agreeRefund(item.refoundNo)
      if (status?.code === '200') {
        this.$message.success('同意退款成功')
        this.query.loading = false
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.doQueryPagefx()
        } else {
          await this.doQueryPage()
        }
      } else {
        this.query.loading = false
        this.$message.error('该批次退款申请正在处理中，暂无法执行其他操作。')
        return
      }
    }
    async retryRecycleRefund(item: BatchRefoundListDetailVo) {
      this.query.loading = true
      const status = await this.mutationBatchOrderRefund.againRecyclingbatchReturnOrderNo(item.refoundNo)
      if (status.code == 200) {
        this.$message.success('重新回收资源成功')
        this.query.loading = false
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.doQueryPagefx()
        } else {
          await this.doQueryPage()
        }
      } else {
        this.query.loading = false
        this.$message.error('该批次退款申请正在处理中，暂无法执行其他操作。')
        return
      }
    }
    async retryRefund(item: BatchRefoundListDetailVo) {
      this.query.loading = true
      const status = await this.mutationBatchOrderRefund.retryRefund(item.refoundNo)
      if (status.code == 200) {
        this.$message.success('继续退款成功')
        this.query.loading = false
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.doQueryPagefx()
        } else {
          await this.doQueryPage()
        }
      } else {
        this.query.loading = false
        this.$message.error('该批次退款申请正在处理中，暂无法执行其他操作。')
        return
      }
    }
    async confirmRefund(item: BatchRefoundListDetailVo) {
      this.query.loading = true
      const status = await this.mutationBatchOrderRefund.confirmRefund(item.refoundNo)
      if (status.code == '200') {
        this.$message.success('确认退款成功')
        this.query.loading = false
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.doQueryPagefx()
        } else {
          await this.doQueryPage()
        }
      } else {
        this.query.loading = false
        this.$message.error('该批次退款申请正在处理中，暂无法执行其他操作。')
        return
      }
    }
    //打开批量退款弹窗
    showBatchDialog(isAgree: boolean) {
      if (this.selectdList.length === 0) {
        this.$message.error({ message: '请先选择需批量' + (isAgree ? '同意' : '确认') + '退款的订单', offset: 120 })
        return
      }
      if (
        this.selectdList.some((item) => (isAgree ? item.refundStatus !== 1 : ![2, 7, 8].includes(item.refundStatus)))
      ) {
        this.$message.error({ message: '退款' + (isAgree ? '审批中' : '处理中') + '才能同意退款', offset: 120 })
        return
      }
      this[isAgree ? 'showBatchAgree' : 'showBatchConfirm'] = true
    }
    //处理批量同意退款
    async handleBatchAgree() {
      this.btnLoading = true
      const selectdNoList = this.selectdList.map((item) => item.refoundNo)
      console.log(selectdNoList, 'selectdNoList')
      const res = await this.mutationBatchOrderRefund.batchAgreeReturnApply(selectdNoList)
      console.log(res, '批量同意退款结果')
      if (res.batchReturnOrderAgreeApplyResponseList?.every((item: any) => item.code === '200')) {
        this.$message.success({ message: '同意退款成功', offset: 120 })
      } else {
        const length =
          res.batchReturnOrderAgreeApplyResponseList?.reduce(
            (i: number, item: any) => (item.code !== '200' ? i + 1 : i),
            0
          ) || selectdNoList.length
        this.$message.error({ message: length + '个订单同意退款失败', offset: 120 })
      }
      this.showBatchAgree = false
      this.btnLoading = false
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else {
        await this.doQueryPage()
      }
    }
    //处理批量确认退款
    async handleBatchConfirm() {
      this.btnLoading = true
      const selectdNoList = this.selectdList.map((item) => item.refoundNo)
      console.log(selectdNoList, 'selectdNoList')
      const res = await this.mutationBatchOrderRefund.batchConfirmRefund(selectdNoList)
      console.log(res, '批量确认退款结果')
      if (res.batchReturnOrderConfirmRefundResponseList?.every((item: any) => item.code === '200')) {
        this.$message.success({ message: '确认退款成功', offset: 120 })
      } else {
        const length =
          res.batchReturnOrderConfirmRefundResponseList?.reduce(
            (i: number, item: any) => (item.code !== '200' ? i + 1 : i),
            0
          ) || selectdNoList.length
        this.$message.error({ message: length + '个订单确认退款失败', offset: 120 })
      }
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else {
        await this.doQueryPage()
      }
      this.btnLoading = false
      this.showBatchConfirm = false
    }
    async exportRefund() {
      try {
        this.exportQueryParam = Object.assign(new BatchReturnOrderSortRequest(), this.returnOrderRequestVo)
        let res
        if (this.isHadFxAbility && this.isFxlogin) {
          res = await this.exportRefundfx()
        } else if (this.isZtlogin) {
          res = await this.exportRefundzt()
        } else {
          res = await this.exportRefundty()
        }
        if (res) {
          this.exportType = 'exportBatchReturnOrder'
          this.exportText = '下载入口：导出任务管理-集体报名退款批次'
          this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      }
    }

    async exportRefundty() {
      return await this.queryRefundOrder.exportBatchReturnOrderExcelInServicer(this.exportQueryParam)
    }

    async exportRefundfx() {
      return await this.queryRefundOrder.exportFxBatchReturnOrderExcelInServicer(this.exportQueryParam)
    }
    async exportRefundzt() {
      return await this.queryZtRefund.exportBatchReturnOrderExcelInServicer(this.exportQueryParam)
    }
    async exportRefundDetail() {
      try {
        this.exportQueryParam = Object.assign(new BatchReturnOrderSortRequest(), this.returnOrderRequestVo)
        let res
        if (this.isFxlogin && this.isHadFxAbility) {
          res = await this.queryRefundOrder.exportFxBatchReturnOrderDetailExcelInServicer(this.exportQueryParam)
        } else if (this.isZtlogin) {
          res = await this.queryZtRefund.exportBatchReturnOrderDetailExcelInServicer(this.exportQueryParam)
        } else {
          res = await this.queryRefundOrder.exportBatchReturnOrderDetailExcelInServicer(this.exportQueryParam)
        }
        if (res) {
          this.exportType = 'exportBatchReturnOrderDetail'
          this.exportText = '下载入口：导出任务管理-集体报名退款明细'
          this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      }
    }
    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: this.exportType }
      })
    }
    async activated() {
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else if (this.isZtlogin) {
        await this.doQueryPagezt()
      } else {
        await this.doQueryPage()
      }
    }
  }
</script>
<style scoped>
  .more-actions {
    display: flex;
    justify-content: flex-end;
    position: relative;
    top: -10px;
  }
</style>
