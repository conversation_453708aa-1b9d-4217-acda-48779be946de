schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""创建专题管理员
		@param request 创建专题管理员请求
		@return {code,message}
		40001, "指定角色不存在"
		40002, "当前角色不为专题管理员"
		2001：缺失数据
		3001：账号已被使用
		3002：手机号已被使用
		3100：网校未开启专题配置
		3200：专题不属于指定网校
		30000, "请输入11位真实有效手机号"
		30010, "账号和手机号一致"
	"""
	createTrainingChannelAdministrator(request:CreateTrainingChannelAdminRequest):GenernalResponse
	"""修改专题管理员
		@param request 修改专题管理员请求
		@return {code,message}
	"""
	updateTrainingChannelAdministrator(request:UpdateTrainingChannelAdminRequset):GenernalResponse
}
"""创建专题管理员请求"""
input CreateTrainingChannelAdminRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingChannelAdmin.CreateTrainingChannelAdminRequest") {
	"""登录账户【必填】"""
	account:String!
	"""姓名【必填】"""
	name:String!
	"""手机"""
	phone:String
	"""启用状态  正常: 1 禁用: 2【必填】"""
	status:Int!
	"""添加的角色id集合【必填】"""
	addRoleIds:[String]!
	"""管理的专题id集合"""
	trainingChannelIds:[String]!
}
"""@Description 修改专题管理员请求
	<AUTHOR>
	@Date 2024/6/5 16:42
"""
input UpdateTrainingChannelAdminRequset @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.trainingChannelAdmin.UpdateTrainingChannelAdminRequset") {
	"""被修改的管理员账户ID【必填】"""
	accountId:String!
	"""姓名【为null，表示不更新】"""
	name:String
	"""手机【为null，表示不更新】"""
	phone:String
	"""启用状态  正常: 1 禁用: 2【必填】"""
	status:Int!
	"""添加的角色id集合"""
	addRoleIds:[String]
	"""移除的角色id集合"""
	removeRoleIds:[String]
	"""添加的专题ID集合"""
	addTrainingChannelIds:[String]
	"""移除的专题Id集合"""
	removeTrainingChannelIds:[String]
}
"""<AUTHOR> [2023/7/11 20:54]"""
type GenernalResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.GenernalResponse") {
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}

scalar List
