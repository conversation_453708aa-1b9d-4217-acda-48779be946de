import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 证书类型
 */
enum CertificateModelEnum {
  JXJY_RS = 1,
  JXJY_JS = 2
}

export { CertificateModelEnum }
class CertificateModelTypes extends AbstractEnum<CertificateModelEnum> {
  static enum = CertificateModelEnum

  constructor(status?: CertificateModelEnum) {
    super()
    this.current = status
    this.map.set(CertificateModelEnum.JXJY_RS, '继续教育人设')
    this.map.set(CertificateModelEnum.JXJY_JS, '继续教育建设')
  }
}

export default new CertificateModelTypes()
