import MyTrainClassDetailClassVo from '@api/service/management/train-class/query/vo/MyTrainClassDetailClassVo'
import userModule from '@api/service/management/user/UserModule'
import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
import { StudentTrainingResultSimulateResponse } from '@api/platform-gateway/student-course-learning-query-back-gateway'
import DateScope from '@api/service/common/models/DateScope'
import { StudentLearningStaticsVo as StudentLearningStatics } from '@api/service/management/statisticalReport/query/vo/StudentLearningStaticsVo'

export class StudentLearningStaticsVo extends StudentLearningStatics {
  //登录账号
  loginCount: string = undefined

  //学员信息
  studentDetail = new UserDetailVo()

  /**
   * 是否有班级模拟数据
   */
  haveTrainSimulate: boolean = undefined

  /**
   * 是否有课程模拟数据集
   */
  haveCourseSimulate: boolean = undefined
  /**
   * 是否只有考试模拟数据
   */
  onlyHaveExamSimulate: boolean = undefined

  /**
   * 班级模拟数据合格时间
   */
  trainSimulatePassTime: string = undefined

  /**
   * 课程模拟数据最后一门合格时间
   */
  courseSimulateLastEndTime: string = undefined

  /**
   * 考试模拟成绩
   */
  simulateExamScore = ''

  /**
   * 考试模拟时间
   */
  simulateExamTime: DateScope = new DateScope()
  /**
   * 同步状态
   * 0 未同步
   * 1 已同步
   * 2 同步失败
   */
  syncStatus: number = undefined

  /**
   * 同步信息
   */
  syncMessage: string = undefined

  async addUserDetail(userId: string) {
    try {
      console.log('userId参数=', userId)
      this.studentDetail = await this.getUserDetail(userId)
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/vo/StudentLearningStaticsVo.ts所处方法，addUserDetail',
        e
      )
    }
  }

  /**
   * 填充模拟数据
   */
  setSimulateData(simulateData: StudentTrainingResultSimulateResponse) {
    // 查找班级模拟数据
    this.haveTrainSimulate = true
    this.trainSimulatePassTime = simulateData.studentSchemeLearning?.qualifiedTime
    this.simulateExamScore = simulateData.studentSchemeLearning?.score
    this.simulateExamTime.begin = simulateData.recordCreatedTime
    this.simulateExamTime.end = simulateData.recordCreatedTime
    this.onlyHaveExamSimulate = simulateData.type == 2
    // 查找课程模拟数据以及课程最后一门合格时间
    if (simulateData.trainingContentList?.length) {
      this.haveCourseSimulate = true
      let lastCourseEndTime = ''
      simulateData.trainingContentList.map((course) => {
        if (!lastCourseEndTime && course.endTime) {
          lastCourseEndTime = course.endTime
        } else if (lastCourseEndTime) {
          const currentEndTime = (course.endTime && new Date(course.endTime)) || undefined
          if (currentEndTime && currentEndTime.getTime() > new Date(lastCourseEndTime).getTime()) {
            lastCourseEndTime = course.endTime
          }
        }
      })

      this.courseSimulateLastEndTime = lastCourseEndTime
    }
  }

  async getUserDetail(userId: string) {
    try {
      console.log('userId参数=', userId)
      const queryUser = userModule.queryUserFactory.queryStudentDetail(userId)
      const data = await queryUser.queryDetail()

      if (data.status.isSuccess()) {
        return data.data
      }

      console.log('调用了getUserDetail方法，返回值=', new UserDetailVo())
      return new UserDetailVo()
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/vo/StudentLearningStaticsVo.ts所处方法，getUserDetail',
        e
      )
    }
  }
}
