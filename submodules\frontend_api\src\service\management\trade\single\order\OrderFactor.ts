import QueryOrder from '@api/service/management/trade/single/order/query/QueryOrder'
import QueryRefundList from '@api/service/management/trade/single/order/query/QueryRefundList'
import QueryRefundDetail from '@api/service/management/trade/single/order/query/QueryRefundDetail'
import QueryExchangeCommodity from '@api/service/management/trade/single/order/query/QueryExchangeCommodity'
import QueryOrderDetail from '@api/service/management/trade/single/order/query/QueryOrderDetail'
import QueryExchangeDetail from '@api/service/management/trade/single/order/query/QueryExchangeDetail'
import QueryExchangeDetailAccordingOrder from '@api/service/management/trade/single/order/query/QueryExchangeDetailAccordingOrder'
import QueryRefundReasonList from '@api/service/management/trade/single/order/query/QueryRefundReasonList'
import { QueryOrderPayStatue } from '@api/service/management/trade/single/order/query/QueryOrderPayStatue'

class OrderFactor {
  /**
   * 订单查询
   */
  get queryOrder() {
    return new QueryOrder()
  }
  /**
   * 订单详情查询
   */
  getQueryOrderDetail() {
    return new QueryOrderDetail()
  }
  /**
   * 查询订单支付状态
   */
  getQueryOrderPayStatue() {
    return new QueryOrderPayStatue()
  }
  /**
   * 换货单详情查询
   */
  getQueryExchangeDetail() {
    return new QueryExchangeDetail()
  }
  /**
   * 换货单详情根据子订单号查询
   */
  getQueryExchangeDetailByOrder() {
    return new QueryExchangeDetailAccordingOrder()
  }
  /**
   * 退款单列表查询
   */
  getQueryRefundOrder() {
    return new QueryRefundList()
  }
  /**
   * 退款原因列表查询
   */
  getQueryRefundReason() {
    return QueryRefundReasonList
  }
  /**
   * 换货单列表查询
   */
  getQueryExchangeOrder() {
    return new QueryExchangeCommodity()
  }
  /**
   * 退款单详情查询
   */
  getQueryRefundOrderDetail() {
    return new QueryRefundDetail()
  }
}
export default OrderFactor
