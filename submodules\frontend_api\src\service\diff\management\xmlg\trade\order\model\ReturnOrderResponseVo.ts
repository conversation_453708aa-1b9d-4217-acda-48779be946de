import BasicDataQueryBackstage, {
  AdminInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import {
  CommodityAuthInfoResponse,
  CommoditySkuPropertyResponse1,
  IssueInfo,
  PaymentInfoResponse,
  RefundCommodityResponse,
  RefundInfoResponse,
  ResourceResponse,
  ReturnApprovalInfoResponse,
  ReturnCloseReasonResponse,
  ReturnOrderApplyInfoResponse,
  ReturnOrderResponse,
  ReturnOrderStatusChangeTimeResponse,
  UserResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { RefundOrderStatusEnum } from '@api/service/common/trade/RefundOrderStatusEnum'
import TradeModule from '@api/service/management/trade/TradeModule'
import { ReturnOrderRecordVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRecordVo'
import userModule from '@api/service/management/user/UserModule'
import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
import { OnlyReturnList } from '@api/service/common/return-order/enums/OrderRefundType'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
import { ChangeOrderType } from '@api/service/common/trade/ChangeOrderType'
/**
 * 培训方案信息
 <AUTHOR>
 @date 2022/03/02
 */
export class SchemeResourceResponse implements ResourceResponse {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 方案名
   */
  schemeName: string
  /**
   * 学时
   */
  period: number
  /**
   * 培训方案类型
   <p>
   chooseCourseLearning：选课学习
   autonomousCourseLearning：自主学习
   @see com.fjhb.domain.learningscheme.api.scheme.consts.SchemeType
   */
  schemeType: string
  /**
   * 资源类型
   <br> Scheme:方案 ,Issue:期数
   @see com.fjhb.domain.trade.api.commodity.consts.CommoditySaleResourceTypes
   */
  resourceType: string
}
/**
 * 订单商品信息返回值
 <AUTHOR>
 @date 2022/1/26
 */
export class CommoditySkuResponse {
  /**
   * 商品id
   */
  commoditySkuId: string
  /**
   * 商品Sku名称
   */
  saleTitle: string
  /**
   * 商品封面图路径
   */
  commodityPicturePath: string
  /**
   * 商品售价
   */
  price: number
  /**
   * 商品原始单价
   */
  originalPrice: number
  /**
   * 是否使用优惠价
   */
  enableSpecialPrice: boolean
  /**
   * 是否展示原价
   */
  showPrice: boolean
  /**
   * 商品sku 配置
   */
  skuProperty: CommoditySkuPropertyResponse1 = new CommoditySkuPropertyResponse1()
  /**
   * 商品关联资源
   */
  resource: ResourceResponse = new SchemeResourceResponse()
  /**
   * 期别信息
   */
  issueInfo: IssueInfo
}

/**
 * 退货商品信息
 <AUTHOR>
 @date 2022/03/28
 */
export class ReturnCommodityResponse {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品信息
   */
  commoditySku: CommoditySkuResponse = new CommoditySkuResponse()
}
/**
 * 退货单关联订单信息
 <AUTHOR>
 @date 2022/3/23
 */
export class OrderInfoResponse1 {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 订单类型（1：常规订单 2：批次关联订单）
   @see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType: number
  /**
   * 关联批次单号
   */
  bathOrderNo: string
  /**
   * 购买渠道（1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道）
   @see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelType: number
  /**
   * 终端（Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统）
   @see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTerminalCodes
   */
  terminalCode: string
  /**
   * 订单支付信息
   */
  orderPaymentInfo: PaymentInfoResponse = new PaymentInfoResponse()
  /**
   * 买家信息
   */
  buyer: UserResponse
}
/**
 * 退货单基本信息
 <AUTHOR>
 @date 2022/3/18
 */
export class ReturnOrderBasicDataResponse {
  /**
   * 退货单类型（1：仅退货 2：仅退款 3：退货并退款）
   @see ReturnOrderTypes
   */
  returnOrderType: number
  /**
   * 退款总额
   */
  refundAmount: number
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
   @see ReturnOrderStatus
   */
  returnOrderStatus: number
  /**
   * 退货单状态变更时间
   */
  returnOrderStatusChangeTime: ReturnOrderStatusChangeTimeResponse = new ReturnOrderStatusChangeTimeResponse()
  /**
   * 退货单申请信息
   */
  applyInfo: ReturnOrderApplyInfoResponse = new ReturnOrderApplyInfoResponse()
  /**
   * 退货单退货失败信息
   */
  returnFailReason: string
  /**
   * 退货单关闭信息
   */
  returnCloseReason: ReturnCloseReasonResponse = new ReturnCloseReasonResponse()
}
/**
 * 退货子订单信息
 <AUTHOR>
 @date 2022/3/18
 */
export class SubOrderInfoResponse1 {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 子订单有换货
   */
  exchanged: boolean
  /**
   * 主订单信息
   */
  orderInfo: OrderInfoResponse1 = new OrderInfoResponse1()
  /**
   * 子订单是否使用优惠
   */
  useDiscount: boolean
}
export default class ReturnOrderResponseVo {
  /**
   * 退货单号
   */
  returnOrderNo: string

  /**
   * 退货单基本信息
   */
  basicData: ReturnOrderBasicDataResponse = new ReturnOrderBasicDataResponse()
  /**
   * 退货单是否需要审批
   */
  needApprove: boolean
  /**
   * 退货单审批信息
   */
  approvalInfo: ReturnApprovalInfoResponse = new ReturnApprovalInfoResponse()
  /**
   * 退款确认人
   */
  confirmUser: UserResponse
  /**
   * 退货单关联退款单信息
   */
  refundInfo: RefundInfoResponse = new RefundInfoResponse()
  /**
   * 退货商品信息
   */
  returnCommodity: ReturnCommodityResponse = new ReturnCommodityResponse()
  /**
   * 退款商品信息
   */
  refundCommodity: RefundCommodityResponse = new RefundCommodityResponse()
  /**
   * 退货子订单信息
   */
  subOrderInfo: SubOrderInfoResponse1 = new SubOrderInfoResponse1()
  //UI列表展示的状态
  UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumApproving
  /**
   * 第三方平台
   */
  thirdPartyPlatform = ''

  //退款操作记录
  records: ReturnOrderRecordVo[] = []
  /**
   * 退货商品分销信息（仅分销订单的退货单有值）
   */
  commodityAuthInfo = new CommodityAuthInfoResponse()
  //购买人信息
  buyer = new UserDetailVo()
  /**
   * 列表可确认退款
   * 不修改原先的枚举了
   */
  canConfirmReturn = false
  /**
   * 华医部分退款 1-专业课   2-公需课  3-都退
   */
  courseType = ''
  /**
   * 换货状态
   */
  changeOrderStatus: ChangeOrderType[] = []
  /**
   * 培训形式
   */
  trainingForm = ''
  /**
   * 培训形式ID
   */
  trainingFormId: TrainingModeEnum = undefined
  /**
   * 培训期别名称
   */
  trainingPeriodName = ''
  /**
   * 期别id
   */
  trainingPeriodId = ''
  /**
   * 期数
   */
  trainingPeriodNo = ''
  /**
   * 后端元商品信息
   */
  get commodityDto() {
    return this.returnCommodity || new ReturnCommodityResponse()
  }

  //添加购买人信息
  async addBuyer() {
    this.buyer = await this.getUserDetail(this.subOrderInfo.orderInfo.buyer.userId)
  }
  private async getUserDetail(userId: string) {
    const queryUser = userModule.queryUserFactory.queryStudentDetail(userId)
    const data = await queryUser.queryDetail()
    if (data.status.isSuccess()) {
      return data.data
    }
    return new UserDetailVo()
  }
  /*
   * 获取退款业务对象
   *
   * */
  getMutationReturn() {
    const mutationReturn = TradeModule.singleTradeBatchFactor.mutationFactory.getMutationReturnOrder()
    mutationReturn.returnOrderNo = this.returnOrderNo
    return mutationReturn
  }
  async fillRecords() {
    if (this.UIReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumApproving) {
      await this.addProveRecord()
    } else if (
      this.UIReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumHandling ||
      this.UIReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumRefuse ||
      this.UIReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumFail
    ) {
      await this.addProveRecord()
      await this.addHandleRecord(
        this.UIReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumFail
          ? RefundOrderStatusEnum.RefundOrderStatusEnumHandling
          : this.UIReturnOrderStatue
      )
    } else if (this.UIReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumCancel) {
      await this.addProveRecord()
      await this.addCloseRecord()
    } else {
      await this.addProveRecord()
      await this.addHandleRecord(RefundOrderStatusEnum.RefundOrderStatusEnumHandling)
      await this.addSuccessRecord()
    }
    const userIds = this.records.map((r) => r.userId).filter((id) => id !== null && id !== undefined)

    const res = await BasicDataQueryBackstage.pageAdminInfoInSubProject({
      page: {
        pageNo: 1,
        pageSize: userIds.length
      },
      request: {
        user: {
          userIdList: userIds
        }
      }
    })
    const userMap = new Map<string, AdminInfoResponse>()
    res.data.currentPageData.forEach((item) => userMap.set(item.userInfo.userId, item))
    this.records.forEach((r) => {
      const temp = userMap.get(r.userId)
      if (temp) {
        r.name = temp.userInfo.userName
        temp.roleList.forEach((role) => {
          if (role.roleCategory === 17 || role.roleCategory === 18) {
            r.roleCa = role.roleCategory
          }
        })
      }
    })
  }
  private async addProveRecord() {
    //   因为用户模块还没有。所以这里暂时用用户id代替名字
    if (this.basicData.applyInfo && this.basicData.applyInfo.applyUser) {
      const record = new ReturnOrderRecordVo()
      const user = await this.getUserDetail(this.basicData.applyInfo.applyUser.userId)
      record.name = user.userName
      record.userId = this.basicData.applyInfo.applyUser.userId
      record.time = this.basicData.returnOrderStatusChangeTime.applied
      record.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumApproving
      record.tipMsg = this.basicData.applyInfo.description
      record.money = this.basicData.refundAmount
      this.records.push(record)
    }
  }
  private async addHandleRecord(UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumRefuse) {
    //   因为用户模块还没有。所以这里暂时用用户id代替名字
    const record = new ReturnOrderRecordVo()
    const user = await this.getUserDetail(this.approvalInfo.approveUser.userId)
    record.name = user.userName
    record.userId = this.approvalInfo.approveUser.userId
    record.time = this.approvalInfo.approveTime
    record.UIReturnOrderStatue = UIReturnOrderStatue
    record.tipMsg = this.approvalInfo.approveComment
    this.records.push(record)
  }
  private async addCloseRecord() {
    //   因为用户模块还没有。所以这里暂时用用户id代替名字
    const record = new ReturnOrderRecordVo()
    const user = await this.getUserDetail(this.basicData.returnCloseReason.cancelUser.userId)
    record.name = user.userName
    record.userId = this.basicData.returnCloseReason.cancelUser.userId
    record.time = this.basicData.returnOrderStatusChangeTime.closed
    record.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumCancel
    record.tipMsg = this.basicData.returnCloseReason.cancelReason
    this.records.push(record)
  }
  private async addSuccessRecord() {
    const record = new ReturnOrderRecordVo()
    if (this.confirmUser && this.confirmUser.userId) {
      const user = await this.getUserDetail(this.confirmUser.userId)
      record.name = user.userName
      record.userId = this.confirmUser.userId
    }
    const map = {
      '1': this.basicData.returnOrderStatusChangeTime.returnCompleted,
      '2': this.basicData.returnOrderStatusChangeTime.refunded,
      '3': this.basicData.returnOrderStatusChangeTime.returnedAndRefunded
    }

    record.time = map[this.basicData.returnOrderType]
    record.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumSuccess
    this.records.push(record)
  }
  //  将底层返回的退款状态转化为前端展示的状态
  changeStatus() {
    if (this.basicData && this.basicData.returnOrderStatus !== undefined) {
      // const tmpItem = this
      const approveStates = [0, 1],
        handlingStates = [2, 3, 4, 5, 6],
        successStatues = [8, 9, 10, 99]
      if (approveStates.indexOf(this.basicData.returnOrderStatus) != -1) {
        this.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumApproving
      } else if (handlingStates.indexOf(this.basicData.returnOrderStatus) != -1) {
        this.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumHandling
        if (this.basicData.returnOrderStatus === 5) {
          this.canConfirmReturn = true
        }
      } else if (successStatues.indexOf(this.basicData.returnOrderStatus) != -1) {
        this.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumSuccess
      } else if (this.basicData.returnOrderStatus == 11 && this.basicData.returnCloseReason.closeType == 3) {
        this.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumRefuse
      } else if (
        this.basicData.returnOrderStatus == 11 &&
        [1, 2, 4].indexOf(this.basicData.returnCloseReason.closeType) != -1
      ) {
        this.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumCancel
      } else if (this.basicData.returnOrderStatus == 7) {
        this.UIReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumFail
      }
    }
  }

  getPeriodMessage(response: ReturnOrderResponse) {
    this.trainingForm = response?.returnCommodity?.commoditySku?.skuProperty?.trainingWay?.skuPropertyValueName
    const trainingWayId = response?.returnCommodity?.commoditySku?.skuProperty?.trainingWay?.skuPropertyValueId
    this.trainingFormId = trainingWayId as TrainingModeEnum
    this.trainingPeriodName = response?.returnCommodity?.commoditySku?.issueInfo?.issueName
    this.trainingPeriodId = response?.returnCommodity?.commoditySku?.issueInfo?.issueId
    this.trainingPeriodNo = response?.returnCommodity?.commoditySku?.issueInfo?.issueNum
  }
}
