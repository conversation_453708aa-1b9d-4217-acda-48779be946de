<template>
  <div>
    <el-drawer title="查看考勤详情" :visible.sync="showDrawer" direction="rtl" size="1000px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-alert
          type="warning"
          :closable="false"
          class="m-alert"
          v-loading="signConfigLoading"
          v-if="studentPeriodLog.signIn.isOpen || studentPeriodLog.signOut.isOpen"
        >
          <p><b>考勤规则</b></p>
          <p v-if="studentPeriodLog.signIn.isOpen">
            <span v-text="autoIndex(studentPeriodLog.signIn.isOpen)"></span>、开启签到：<span
              v-text="frequencyToText(studentPeriodLog.signIn.checkInFrequency)"
            ></span
            >签到一次，第一节课开始授课前<span class="f-ci" v-text="studentPeriodLog.signIn.preCheckInTime"></span
            >分钟和开始授课后<span class="f-ci" v-text="studentPeriodLog.signIn.afterCheckInTime"></span
            >分钟之间，需签到1次。
          </p>
          <p v-if="studentPeriodLog.signOut.isOpen">
            <span v-text="autoIndex(studentPeriodLog.signIn.isOpen, studentPeriodLog.signOut.isOpen)"></span
            >、开启签退：<span v-text="frequencyToText(studentPeriodLog.signOut.checkInFrequency)"></span
            >签退一次，第一节课开始授课前<span class="f-ci" v-text="studentPeriodLog.signOut.preCheckInTime"></span
            >分钟和开始授课后<span class="f-ci" v-text="studentPeriodLog.signOut.afterCheckInTime"></span
            >分钟之间，需签退1次。
          </p>
        </el-alert>
        <!--表格-->
        <el-table
          stripe
          :data="queryAttendanceVo.reportList"
          max-height="500px"
          class="m-table f-mt10"
          v-loading="loading"
          ref="tableRef"
        >
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="课程名称" min-width="240">
            <template slot-scope="{ row }">
              <div>{{ row.courseName }}</div>
            </template>
          </el-table-column>
          <el-table-column label="授课时间" min-width="200" align="center">
            <template slot-scope="{ row }">
              <p>
                <el-tag type="info" size="mini">开始</el-tag><span v-text="getObjValue(row.teachTime, 'begin')"></span>
              </p>
              <p>
                <el-tag type="info" size="mini">结束</el-tag><span v-text="getObjValue(row.teachTime, 'end')"></span>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="要求签到/签退情况" min-width="180" align="center">
            <template slot-scope="{ row }">
              <div>{{ row.isSignIn ? '' : '不' }}要求签到/{{ row.isSignOut ? '' : '不' }}要求签退</div>
            </template>
          </el-table-column>
          <el-table-column label="签到时间" min-width="120" align="center">
            <template slot-scope="{ row }">
              <div v-if="row.isSignIn">
                <span v-if="row.isSignIning"></span>
                <span v-else>
                  <span v-if="row.isNotLackSignIn">
                    {{ row.signInTime }}
                  </span>
                  <span v-else class="f-ci">未签到</span>
                </span>
              </div>
              <div v-else>
                <span>-</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="签退时间" min-width="120" align="center">
            <template slot-scope="{ row }">
              <div v-if="row.isSignOut">
                <span v-if="row.isSignOuting"> </span>
                <span v-else>
                  <span v-if="row.isNotLackSignOut">
                    {{ row.signOutTime }}
                  </span>
                  <span v-else class="f-ci">未签退</span>
                </span>
              </div>
              <div v-else>
                <span>-</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex; justify-content: flex-end">
          <hb-pagination class="f-mt15 f-tr" :page="page" v-bind="page"> </hb-pagination>
        </div>
      </div>
      <div class="drawer-ft m-btn-bar">
        <el-button type="primary" @click="showDrawer = false">返回</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Vue, Component, Prop } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import QueryAttendanceReportList from '@api/service/management/statisticalReport/query/QueryAttendanceReportList'
  import { CheckInFrequencyEnum } from '@api/service/common/implement/enums/CheckInFrequencyEnum'
  import StudentPeriodLog from '@api/service/management/train-class/offlinePart/model/StudentPeriodLog'
  @Component
  export default class extends Vue {
    /**
     * @description 展示弹窗
     * */
    showDrawer = false

    /**
     * @description 数据加载
     * */
    loading = false

    /**
     * @description 考勤数据加载
     * */
    signConfigLoading = false

    /**
     * @description 考勤数据
     * */
    queryAttendanceVo = new QueryAttendanceReportList()

    /**
     * @description 考勤规则
     * */
    studentPeriodLog = new StudentPeriodLog()

    page: UiPage
    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
    }

    /**
     * @description 对象取数
     * */
    get getObjValue() {
      return (obj: object, key: string) => {
        const hasValue = typeof obj === 'object' && obj !== null && Object.keys(obj).length > 0
        if (hasValue) return obj[key] ?? '-'
        return '-'
      }
    }

    /**
     * @description 根据 true 的数量自动返回序号
     * */
    get autoIndex() {
      return (...bools: boolean[]) => {
        let serialNumber = 0
        bools.map((item) => {
          if (item) ++serialNumber
        })
        return serialNumber
      }
    }

    /**
     * @description 签到频率转文字
     * */
    get frequencyToText() {
      return (val: CheckInFrequencyEnum) => {
        if (val == CheckInFrequencyEnum.halfDay) return '每半天'
        if (val == CheckInFrequencyEnum.perLesson) return '每节课'
      }
    }

    /**
     * @description 打开弹窗
     * */
    async open(studentPeriodLog: StudentPeriodLog, schemeId: string) {
      this.showDrawer = true
      this.page.pageNo = 1
      this.studentPeriodLog = studentPeriodLog
      this.queryAttendanceVo = new QueryAttendanceReportList(schemeId)
      this.doQuery()
      this.doQuerySignConfig()
    }

    /**
     * @description 查询考勤数据
     * */
    async doQuery() {
      try {
        this.loading = true
        this.queryAttendanceVo.qualificationId = this.studentPeriodLog.periodQualificationId
        await this.queryAttendanceVo.queryReportList(this.page)
      } catch (e) {
        console.log(e, '=====err')
        this.$message.error('考勤详情数据加载失败！')
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['tableRef'] as any)?.doLayout()
        this.loading = false
      }
    }

    /**
     * @description 查询考勤规则配置
     * */
    doQuerySignConfig() {
      try {
        this.signConfigLoading = true
        this.studentPeriodLog.queryAttendanceConfig()
      } catch (e) {
        console.log(e, '=====err')
        this.$message.error('考勤详情数据加载失败！')
      } finally {
        this.signConfigLoading = false
      }
    }
  }
</script>
