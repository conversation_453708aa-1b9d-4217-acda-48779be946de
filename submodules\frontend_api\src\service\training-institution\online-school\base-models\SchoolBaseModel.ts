import BusinessAttributesModel from '@api/service/training-institution/online-school/base-models/BusinessAttributesModel'
import { OnlineSchoolEnum } from '@api/service/training-institution/online-school/enum/OnlineSchoolEnum'
import { Industry } from '@api/ms-gateway/ms-servicercontract-v1'
import IndustriesPropertiesVo from '@api/service/training-institution/online-school/base-models/IndustriesPropertiesVo'

/**
 * 网校基本信息
 */
export default class SchoolBaseModel {
  /**
   * 网校名称
   */
  schoolName: string = undefined

  /**
   * 服务地区code
   */
  serviceRegionCodes: Array<string> = new Array<string>()

  /**
   * 是否有人社行业
   */
  haveRSIndustry: boolean = undefined

  /**
   * 是否有建设行业
   */
  haveJSIndustry: boolean = undefined

  /**
   * 是否有卫生行业
   */
  haveWSIndustry: boolean = undefined

  /**
   * 是否有工勤行业
   */
  haveGQIndustry: boolean = undefined

  /**
   * 是否有教师行业
   */
  haveLSIndustry: boolean = undefined

  /**
   * 是否有药师行业
   */
  haveYSIndustry: boolean = undefined

  /**
   * 人社行业属性
   */
  RSIndustry: Industry = new Industry()

  /**
   * 建设行业属性
   */
  JSIndustry: Industry = new Industry()

  /**
   * 卫生行业属性
   */
  WSIndustry: Industry = new Industry()

  /**
   * 工勤行业属性
   */
  GQIndustry: Industry = new Industry()
  /**
   * 教师行业属性
   */
  LSIndustry: Industry = new Industry()

  /**
   * 药师行业属性
   */
  YSIndustry: Industry = new Industry()
  /**
   * 人员行业属性
   */
  personIndustriesProperties: IndustriesPropertiesVo = new IndustriesPropertiesVo()

  /**
   * 业务年度
   */
  industryYears: Array<string> = new Array<string>()

  /**
   * 业务地区
   */
  industryRegionCodes: Array<string> = new Array<string>()

  /**
   * 业主名称
   */
  ownerUnitName: string = undefined

  /**
   * 业主简称
   */
  ownerUnitSingleName: string = undefined

  /**
   * 业主负责人
   */
  ownerCharge: string = undefined

  /**
   * 手机号
   */
  phone: string = undefined

  /**
   * 网校性质
   */
  schoolModel: OnlineSchoolEnum = undefined

  /**
   * 合约签订情况（是否签约）
   */
  contractSigning: boolean = undefined

  /**
   * 签约时间
   */
  signingTime: string = undefined

  /**
   * 归属市场经办
   */
  belongMarket: string = undefined

  /**
   * 网校背景描述
   */
  description: string = undefined
}
