import ApplyBatchBillAction from './mutation/apply-batch-bill-action/ApplyBatchBillAction'
import CancelBatchOrder from './mutation/cancel-batch-order/CancelBatchOrder'
import CreateBatchOrder from './mutation/create-batch-order/CreateBatchOrder'
import PayBatchOrder from './mutation/pay-batch-order/PayBatchOrder'
class MutationTradeBatchFactory {
  /**
   * @description: 获取申请发票
   * @param {*}
   * @return {*}
   */

  get applyBatchBillAction() {
    return new ApplyBatchBillAction()
  }

  /**
   * @description: 获取取消订单
   * @param {*}
   * @return {*}
   */

  get cancelBatchOrder() {
    return (batchOrderNo: string) => {
      return new CancelBatchOrder(batchOrderNo)
    }
  }

  /**
   * @description: 获取创建订单
   * @param {*}
   * @return {*}
   */

  get createBatchOrder() {
    return new CreateBatchOrder()
  }

  /**
   * @description: 获取支付订单
   * @param {*}
   * @return {*}
   */

  get PayBatchOrder() {
    return new PayBatchOrder()
  }
}

export default new MutationTradeBatchFactory()
