<template>
  <el-drawer :visible.sync="show" size="80%" title="预览课件" destroy-on-close>
    <div style="width: 100%; height: 80%">
      <iframe v-if="currentId" width="100%" :src="url" frameborder="0" height="100%" scrolling="no"></iframe>
    </div>
    <el-tabs
      v-if="viewList.length"
      type="card"
      class="f-mt20 courseware-list"
      style="padding: 0 20px"
      @tab-click="change"
      v-model="currentId"
    >
      <el-tab-pane
        :key="item.name"
        v-for="item in viewList"
        :label="item.name"
        :name="item.id + '/' + item.type"
        @click="change(item)"
      ></el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>

<style lang="scss">
  .courseware-list {
    .el-tabs__item {
      font-size: 12px;
      border-bottom-color: #e3e3e3;
      height: 30px;
      line-height: 30px;
    }

    .el-tabs__header {
      border-bottom: 0;

      .el-tabs__nav {
        border-bottom: 1px solid #e4e7ed;
      }
    }
  }
</style>

<script lang="ts">
  import { Prop, Vue, Watch } from 'vue-property-decorator'
  import Component from 'vue-class-component'
  import CoursewareType from '@api/service/management/resource/courseware/enum/CoursewareType'

  @Component
  export default class extends Vue {
    showPreview = false
    @Prop({
      type: Boolean,
      default: false
    })
    visible: boolean
    show = false
    @Prop({
      type: Array,
      default() {
        return new Array<{ name: string; id: string; type: string }>()
      }
    })
    viewList: Array<{ name: string; id: string; type: string }>
    @Prop({
      type: String,
      default: ''
    })
    coursewareId: string
    @Prop({
      type: CoursewareType,
      default: ''
    })
    courseWareType: CoursewareType

    @Watch('visible', { immediate: true })
    visibleChange() {
      this.show = this.visible
    }

    @Watch('show')
    showChange() {
      this.$emit('update:visible', this.show)
    }

    currentId = ''
    courseType = ''

    @Watch('viewList', { deep: true, immediate: true })
    viewListChange() {
      if (this.coursewareId) {
        this.currentId = this.coursewareId
      } else {
        this.currentId = this.viewList[0].id
      }
    }
    @Watch('courseWareType', { deep: true, immediate: true })
    crouseWareTypeChange() {
      this.courseType = this.courseWareType.current
    }

    get url() {
      return this.courseType
        ? `/play/previewCourseware/${this.currentId}/${this.courseType}?token=${this.localStorage}`
        : `/play/previewCourseware/${this.currentId}}`
    }

    get localStorage() {
      return `Mship ${localStorage.getItem('admin.Access-Token')}`
    }

    change(item: { name: string; id: string; type: string }) {
      this.currentId = item.name.split('/')[0]
      this.courseType = item.name.split('/')[1]
    }
  }
</script>
