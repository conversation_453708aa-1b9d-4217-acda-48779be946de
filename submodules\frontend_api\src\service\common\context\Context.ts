/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2023-06-14 14:24:45
 * @LastEditors: <PERSON>
 * @LastEditTime: 2023-07-19 08:43:49
 */
import { ServicerTokenResponse } from '@api/gateway/ms-servicer-v1'
import MsBasicDataQueryBackstage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import MsServicerV1 from '@api/ms-gateway/ms-servicer-v1'
import TrainingChannel from '@api/platform-gateway/platform-training-channel-fore-gateway'
import { ExtraConfig } from '@api/request'
import BusinessEnvironment from '@api/service/common/context/BusinessEnvironment'
import Environment from '@api/service/common/context/Environment'
import ServicerInfos from '@api/service/common/context/ServicerInfo'
import axios from 'axios'
import FxConfig from '@api/service/common/context/models/FxConfig'
import { FxPageTypeEnum } from '@api/service/common/context/enums/FxPageTypeEnum'
import { ContextTypeEnum } from '@api/service/common/context/enums/ContextTypeEnum'
import { DomainTypeEnum } from '@api/service/common/context/enums/DomainTypeEnum'
import fxnlQuery from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import { ContextTypeEnums } from '@api/service/common/enums/ContextTypeEnums'
import MsPlatformDistributorAdmin, {
  WhetherBelongDistributorRequest
} from '@api/platform-gateway/platform-jxjy-distributor-admin-v1'
import CryptUtil from '@api/service/common/crypt/CryptUtil'
import OrderParams from '@api/service/common/context/models/OrderParams'
import PlatformJxjyDistributorAdminV1 from '@api/platform-gateway/platform-jxjy-distributor-admin-v1'
import ShortCode from '@api/service/common/common/short-code'

/**
 * 上下文
 */
class Context {
  environment: Environment = new Environment()
  businessEnvironment: BusinessEnvironment = new BusinessEnvironment()

  /**
   * 服务商信息
   */
  servicerInfo: ServicerInfos = new ServicerInfos()

  /**
   * 分销商信息
   */
  fxPortalInfo: FxConfig = new FxConfig()

  /**
   * 域类型
   */
  currentDomain: DomainTypeEnum = undefined

  /**
   * 获取当前服务商信息
   * @private
   */
  async queryCurrentServicerInfo() {
    const result = await MsBasicDataQueryBackstage.getCurrentServicerInfo()
    if (result.status?.isSuccess()) {
      this.servicerInfo = result.data.servicerInfo ?? new ServicerInfos()
    }
  }

  /**
   * 本地调试拼接网校域名
   * @param domain
   * @private
   */
  private modifyDomain(domain: string) {
    const regex = /^(.*\.(dev\.|test2\.|test1\.))/
    const match = domain.match(regex)
    if (match) {
      const prefix = match[1]
      return prefix + '59iedu.com'
    } else {
      return domain
    }
  }

  /**
   * 构建分销门户上下文
   */
  async buildFxPortalContext() {
    const path = location.pathname
    const spiltPaths = path.split('/')
    const searchParams = new URLSearchParams(location.search)
    const params = {}
    if ((searchParams as any).size) {
      for (const [key, value] of searchParams) {
        params[key] = decodeURIComponent(value)
      }
    }

    let fxPortalId = ''
    if (params['isPromotionLink']) {
      this.fxPortalInfo.type = FxPageTypeEnum.commodity
      if (spiltPaths.length) {
        const param = spiltPaths[spiltPaths.length - 1].split('?')
        this.fxPortalInfo.commodityId = param[0]
      }
      if (params['commodityId']) {
        this.fxPortalInfo.commodityId = params['commodityId']
      }
    }
    if (path.includes('/distribution/')) {
      this.fxPortalInfo.type = FxPageTypeEnum.portal
      if (spiltPaths.length) {
        fxPortalId = spiltPaths[spiltPaths.length - 1]
      }
    }
    if (params['fxPortalId']) {
      fxPortalId = params['fxPortalId']
    }
    if (path.includes('center/create-order/wait-pay') && params['fxPortalInfo']) {
      const option = (await CryptUtil.decrypt(params['fxPortalInfo'])) as OrderParams
      fxPortalId = option.fxPortalId
      if (option.commodityId) {
        this.fxPortalInfo.type = FxPageTypeEnum.commodity
        this.fxPortalInfo.commodityId = option.commodityId
      }
    }
    const commodityId = sessionStorage.getItem('commodityId')
    let fxPageType
    if (params['fxPageType']) {
      fxPageType = params['fxPageType']
    } else {
      fxPageType = sessionStorage.getItem('fxPageType')
    }
    if (!this.fxPortalInfo.type) {
      if (commodityId) {
        this.fxPortalInfo.commodityId = commodityId
      }
      if (fxPageType) {
        this.fxPortalInfo.type = Number(fxPageType) as FxPageTypeEnum
      } else {
        this.fxPortalInfo.type = FxPageTypeEnum.portal
        if (spiltPaths.length) {
          fxPortalId = params['fxPortalId']
        }
      }
    }
    if (location.pathname.includes('/d/') || location.pathname.includes('/h5d/')) {
      const shortCode = location.pathname.split('/d/')[1] || location.pathname.split('/h5d/')[1]
      const res = await ShortCode.getUrlByShortCode(shortCode)
      if (res.data?.includes('fxPortalId=')) {
        fxPortalId = res.data?.split('fxPortalId=')[1]?.split('&source=scan')[0]
      } else {
        fxPortalId = res.data?.split('/distribution/')[1]
      }
    }
    sessionStorage.removeItem('commodityId')
    sessionStorage.removeItem('fxPageType')

    console.log('this.fxPortalInfo', this.fxPortalInfo)
    if (fxPortalId) {
      // 如果分销推广门户id
      const whetherReq = new WhetherBelongDistributorRequest()
      whetherReq.distributorId = this.fxPortalInfo?.serviceToken?.tokenMeta?.servicerId
      whetherReq.promotionId = fxPortalId
      const res = await MsPlatformDistributorAdmin.whetherBelongDistributor(whetherReq)

      if (res.data.code == '200') {
        this.fxPortalInfo.portalId = fxPortalId
      }
    } else {
      await this.getOnlyFxPortalId(1)
    }
  }

  /**
   * 构建H5分销上下文
   */
  async buildFxPortalContextByH5() {
    const path = location.hash
    const spiltPaths = path.split('?')
    const urlParams = new URLSearchParams('?' + spiltPaths[1])

    if (spiltPaths.length > 1) {
      const fxPortalId = urlParams.get('fxPortalId')
      const commodityId = urlParams.get('commodityId')
      const fxPortalInfo = urlParams.get('fxPortalInfo')
      // 适配支付页面
      if (path.includes('/pages/create-order/third-party-payment') && fxPortalInfo) {
        // const aesJson = decodeURIComponent(fxPortalInfo)
        const option = (await CryptUtil.decrypt(fxPortalInfo)) as OrderParams
        if (option.fxPortalId) {
          // 如果有分销推广门户id则要判断该门户id是否属于该分销商的门户
          const whetherReq = new WhetherBelongDistributorRequest()
          whetherReq.distributorId = this.fxPortalInfo?.serviceToken?.tokenMeta?.servicerId
          whetherReq.promotionId = option.fxPortalId
          const res = await MsPlatformDistributorAdmin.whetherBelongDistributor(whetherReq)
          if (res.data.code == '200') {
            this.fxPortalInfo.portalId = option.fxPortalId
          }
        }

        if (option.commodityId) {
          this.fxPortalInfo.type = FxPageTypeEnum.commodity
          this.fxPortalInfo.commodityId = option.commodityId
        }
      } else if (fxPortalId) {
        // 如果有分销推广门户id则要判断该门户id是否属于该分销商的门户
        const whetherReq = new WhetherBelongDistributorRequest()
        whetherReq.distributorId = this.fxPortalInfo?.serviceToken?.tokenMeta?.servicerId
        whetherReq.promotionId = urlParams.get('fxPortalId')
        const res = await MsPlatformDistributorAdmin.whetherBelongDistributor(whetherReq)

        if (res.data.code == '200') {
          this.fxPortalInfo.portalId = fxPortalId
        }
      } else {
        await this.getOnlyFxPortalId(2)
      }

      if (commodityId) {
        this.fxPortalInfo.commodityId = commodityId as string
      }
    } else {
      await this.getOnlyFxPortalId(2)
    }

    if (urlParams.get('isPromotionLink')) {
      this.fxPortalInfo.type = FxPageTypeEnum.commodity
    }
    if (path.includes('pages/distribution/home/<USER>')) {
      this.fxPortalInfo.type = FxPageTypeEnum.portal
    }

    const commodityId = sessionStorage.getItem('commodityId')
    let fxPageType
    if (urlParams.get(fxPageType)) {
      fxPageType = urlParams.get('fxPageType')
    } else {
      fxPageType = sessionStorage.getItem('fxPageType')
    }
    if (!this.fxPortalInfo.type) {
      if (commodityId) {
        this.fxPortalInfo.commodityId = commodityId
      }
      if (fxPageType) {
        this.fxPortalInfo.type = Number(fxPageType) as FxPageTypeEnum
      } else {
        this.fxPortalInfo.type = FxPageTypeEnum.portal
        if (spiltPaths.length) {
          this.fxPortalInfo.portalId = urlParams.get('fxPortalId')
        }
      }
    }
    sessionStorage.removeItem('commodityId')
    sessionStorage.removeItem('fxPageType')
  }

  /**
   * 构建cutomser上下文环境
   */
  async buildContextByCustomer() {
    // 本地调试对域名进行修改
    let domain = this.environment.domain
    if (process.env.NODE_ENV !== 'production') {
      domain = this.modifyDomain(this.environment.domain)
    }
    const result = await MsServicerV1.applyForServiceByDomainName(domain)
    if (result.status.code !== 200) {
      const servicerTokenResponse = new ServicerTokenResponse()
      this.businessEnvironment.serviceToken = servicerTokenResponse
    } else {
      axios.interceptors.request.use((config: ExtraConfig) => {
        if (config.serviceCapability === ContextTypeEnum.fx) {
          // 切换上下文为分销上下文
          if (this.fxPortalInfo.serviceToken?.token) {
            config.headers[BusinessEnvironment.ContentTypeKey] = this.fxPortalInfo.serviceToken?.token
          }
          // 存在分销的登录令牌（表示有登录），把登录令牌切换到分销
          const fxAccessToken = localStorage.getItem('customer.Fx-Access-Token')
          if (fxAccessToken) {
            config.headers['Authorization'] = `Mship ${fxAccessToken}`
          } else {
            delete config.headers['Authorization']
          }
          return config
        } else {
          if (this.businessEnvironment?.serviceToken.token) {
            config.headers[BusinessEnvironment.ContentTypeKey] = this.businessEnvironment?.serviceToken.token
          }
          const accessToken = localStorage.getItem('customer.Access-Token')
          if (accessToken) {
            config.headers.Authorization = `Mship ${accessToken}`
          }
          return config
        }
      })
      // 判断服务商是否是分销商
      if (result.data.tokenMeta.servicerType === 7) {
        this.currentDomain = DomainTypeEnum.distribution
        // 分销商服务商信息保存在分销商内
        this.fxPortalInfo.serviceToken = result.data
        // 换取8.0服务商信息
        await this.getGeneralServiceTokenByFxContext()
      } else {
        this.currentDomain = DomainTypeEnum.general
        this.businessEnvironment.serviceToken = result.data
        this.servicerInfo.id = result.data?.tokenMeta?.servicerId || ''
      }
    }
  }

  /**
   * 构建admin上下文
   */
  async buildContextByAdmin() {
    // 本地调试对域名进行修改
    let domain = this.environment.domain
    if (process.env.NODE_ENV !== 'production') {
      domain = this.modifyDomain(this.environment.domain)
    }
    const result = await MsServicerV1.applyForServiceByDomainName(domain)
    // 分销域名禁止访问管理端
    if (result.data?.tokenMeta?.servicerType == 7 && location.href.includes('/admin/')) {
      location.replace(location.origin)
    }
    if (result.status.code !== 200) {
      const servicerTokenResponse = new ServicerTokenResponse()
      this.businessEnvironment.serviceToken = servicerTokenResponse
    } else {
      this.businessEnvironment.serviceToken = result.data
    }
    this.servicerInfo.id = result.data?.tokenMeta?.servicerId || ''

    axios.interceptors.request.use((config: ExtraConfig) => {
      // 判断如果是微服务，则请求微服务特有的路径
      if (this.businessEnvironment?.serviceToken.token == undefined) {
        return config
      } else {
        config.headers[BusinessEnvironment.ContentTypeKey] = this.businessEnvironment?.serviceToken.token
        return config
      }
    })
  }

  /**
   * 构建h5上下文
   */
  async buildContextByH5() {
    // 本地调试对域名进行修改
    let domain = this.environment.domain
    if (process.env.NODE_ENV !== 'production') {
      domain = this.modifyDomain(this.environment.domain)
    }
    const result = await MsServicerV1.applyForServiceByDomainName(domain)
    if (result.status.code !== 200) {
      const servicerTokenResponse = new ServicerTokenResponse()
      this.businessEnvironment.serviceToken = servicerTokenResponse
      return null
    } else {
      // 判断服务商是否是分销商
      if (result.data.tokenMeta.servicerType === 7) {
        this.currentDomain = DomainTypeEnum.distribution
        // 分销商服务商信息保存在分销商内
        this.fxPortalInfo.serviceToken = result.data
        this.servicerInfo.id = result.data?.tokenMeta?.servicerId || ''
        return DomainTypeEnum.distribution
      } else {
        this.currentDomain = DomainTypeEnum.general
        this.businessEnvironment.serviceToken = result.data
        this.servicerInfo.id = result.data?.tokenMeta?.servicerId || ''
        return DomainTypeEnum.general
      }
    }
  }

  /**
   * 获取唯一分销门户id
   * @param type 1-web 2-移动端
   * @private
   */
  private async getOnlyFxPortalId(type: number) {
    const singlePortal = await fxnlQuery.ifOnlyPorttalInDustributor(type, ContextTypeEnums.fx)
    if (singlePortal?.data) {
      // 解决单例缓存来不及刷新
      localStorage.setItem('singlePortalId', singlePortal.data)
      this.fxPortalInfo.portalId = singlePortal.data
    } else {
      const res = await fxnlQuery.getCurrentPortalIdInDustributor(type, ContextTypeEnums.fx)
      if (res?.data) {
        // 解决单例缓存来不及刷新
        localStorage.setItem('singlePortalId', res.data)
        this.fxPortalInfo.portalId = res.data
      }
    }
  }
  /**
   * 获取唯一分销门户id(仅在web端侧边栏使用)
   * @param type 1-web 2-移动端
   */
  async getFxPortalId(type: number) {
    const singlePortal = await fxnlQuery.ifOnlyPorttalInDustributor(type, ContextTypeEnums.fx)
    if (singlePortal?.data) {
      return singlePortal.data
    } else {
      const res = await fxnlQuery.getCurrentPortalIdInDustributor(type, ContextTypeEnums.fx)
      if (res?.data) {
        return res.data
      }
    }
  }

  /**
   * 用分销上下文换取8.0上下文
   */
  async getGeneralServiceTokenByFxContext() {
    const serviceTokenRes = await MsServicerV1.getOnlineSchoolServicerProviderByHeader(ContextTypeEnums.fx)
    this.businessEnvironment.serviceToken = serviceTokenRes.data
    this.servicerInfo.id = serviceTokenRes.data.tokenMeta.servicerId
  }

  /**
   * 获取当前专题信息
   */
  async getCurrentTrainingChannelInfo() {
    const response = await TrainingChannel.getCurrentTrainingChannelInfo()
    this.businessEnvironment.specialTopicsInfo = response.data
    // * 判断专题顶级网校域名与当前域名是否一致
    const domain = this.businessEnvironment?.specialTopicsInfo?.netSchoolDomainName[0]
    if (domain) {
      if (domain?.netSchoolDomainName.split('/')[0] !== location.hostname) {
        this.environment.domain = domain.netSchoolDomainName.split('/')[0]
        await this.buildContextByCustomer()
        this.businessEnvironment.superiorServiceInfo = this.businessEnvironment.serviceToken
      }
    } else {
      this.businessEnvironment.superiorServiceInfo = this.businessEnvironment.serviceToken
    }
  }

  /**
   * 获取当前专题信息
   */
  async getCurrentTrainingChannelInfoByH5() {
    // 本地调试对域名进行修改
    let curDomain = this.environment.domain
    if (process.env.NODE_ENV !== 'production') {
      curDomain = this.modifyDomain(this.environment.domain)
    }
    const { status, data } = await PlatformJxjyDistributorAdminV1.findInfoByDomainName(curDomain)
    if (status && status.isSuccess() && data) {
      // 判断当前域名是否是专题域名，是则请求
      if (data.domainType === 3) {
        const response = await TrainingChannel.getCurrentTrainingChannelInfo()
        this.businessEnvironment.specialTopicsInfo = response.data

        // * 判断专题顶级网校域名与当前域名是否一致
        const domain = this.businessEnvironment?.specialTopicsInfo?.netSchoolDomainName[0]
        if (domain) {
          if (domain?.netSchoolDomainName.split('/')[0] !== location.hostname) {
            this.environment.domain = domain.netSchoolDomainName.split('/')[0]
            const res = await this.buildContextByH5()
            this.businessEnvironment.superiorServiceInfo = this.businessEnvironment.serviceToken
            return res
          }
        } else {
          this.businessEnvironment.superiorServiceInfo = this.businessEnvironment.serviceToken
          return null
        }
      }
    }
  }
}

export default new Context()
