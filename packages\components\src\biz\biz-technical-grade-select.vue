<route-meta>
  {
  "title": "技术等级选择器"
  }
  </route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    filterable
    clearable
  >
    <el-option
      v-for="item in technologyLevelOptions"
      :label="item.name"
      :value="item.propertyId"
      :key="item.propertyId"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import QueryTechnologyLevel from '@api/service/common/basic-data-dictionary/QueryBasicdataDictionaryFactory'
  import QueryJobLevel from '@api/service/common/basic-data-dictionary/query/QueryJobLevel'
  import { TechnologyLevelVo } from '@api/service/common/basic-data-dictionary/query/QueryTechnologyLevel'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
  @Component
  export default class extends Vue {
    selected = ''
    technologyLevelOptions: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Prop({
      type: String,
      default: '请选择技术等级'
    })
    placeholder: string

    //行业属性分类id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    // 行业id
    @Prop({
      type: String,
      default: ''
    })
    industryId: string

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }
    async created() {
      // const queryTechnologyLevel = new QueryTechnologyLevel()
      const data = await QueryJobLevel.QueryJobLevelByIndustry(this.industryId, this.industryPropertyId)
      this.technologyLevelOptions = data
    }
  }
</script>
