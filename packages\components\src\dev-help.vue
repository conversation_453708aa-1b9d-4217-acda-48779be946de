<template>
  <el-row style="padding: 20px">
    <el-timeline>
      <el-timeline-item timestamp="2020/2/24" placement="top">
        <el-card shadow="never">
          <h3 style="color: #409eff;">graphql-playground</h3>
          <br />
          <p>
            <el-link href="/graphql-playground?ms=PlatformBasicData" target="_blank">graphql-playground</el-link>
          </p>
          <br />
          <el-alert :closable="false" type="warning" show-icon>
            <div>
              http://【host】/graphql-playground?ms=
              <el-tag>PlatformBasicData</el-tag>
            </div>
          </el-alert>
        </el-card>
      </el-timeline-item>
      <el-timeline-item timestamp="2020/2/23" placement="top">
        <el-card shadow="never">
          <h3 style="color: #409eff;">graphql-voyager</h3>
          <br />
          <p>
            <el-link href="/graphql-voyager?ms=PlatformBasicData" target="_blank">graphql-voyager</el-link>
          </p>
          <br />
          <el-alert :closable="false" type="warning" show-icon>
            <div>
              http://【host】/graphql-voyager?ms=
              <el-tag>PlatformBasicData</el-tag>
            </div>
          </el-alert>
        </el-card>
      </el-timeline-item>
    </el-timeline>

    <TinymceEditor v-model="test"></TinymceEditor>
    <div class="mt20" id="echarts_main" style="width: 500px; height: 500px;"></div>

    <el-card shadow="never">
      <div slot="header">yyc 山东矿机</div>
      <vue-draggable>
        <div v-for="element in [1, 2, 3, 4]" class="item" :key="element.id">{{ element }}</div>
      </vue-draggable>
    </el-card>
  </el-row>
</template>

<style scoped>
  .item {
    padding: 10px;
    background: gray;
    margin: 10px;
  }
</style>

<script>
  import TinymceEditor from '@hbfe/jxjy-admin-components/src/tinymce-editor'
  import VueDraggable from 'vuedraggable'
  // 引入 ECharts 主模块
  const echarts = require('echarts/lib/echarts')
  // 引入柱状图
  require('echarts/lib/chart/bar')
  // 引入提示框和标题组件
  require('echarts/lib/component/tooltip')
  require('echarts/lib/component/title')

  export default {
    components: {
      TinymceEditor,
      VueDraggable
    },
    data() {
      return {
        test: undefined
      }
    },
    mounted() {
      // 基于准备好的dom，初始化echarts实例
      const myChart = echarts.init(document.getElementById('echarts_main'))
      console.log(123132)
      myChart.setOption({
        title: {
          text: 'ECharts 入门示例'
        },
        tooltip: {},
        xAxis: {
          data: ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子']
        },
        yAxis: {},
        series: [
          {
            name: '销量',
            type: 'bar',
            data: [5, 20, 36, 10, 10, 20]
          }
        ]
      })
    }
  }
</script>
