import { Response } from '@hbfe/common'
import { CalReturnOrderUtil } from '@api/service/management/trade/single/order/utils/CalReturnOrderUtil'
import { OrderRefundTypeEnum } from '@api/service/common/return-order/enums/OrderRefundType'
import PlatformJxjypxtyptFjzjTrade, {
  ApplyOrderReturnRequest,
  SubOrderAfterInfo
} from '@api/diff-gateway/platform-jxjypxtypt-fjzj-trade'
export default class MutationCreateReturnOrder {
  orderNo = '' //订单号
  subOrderNo = '' //子订单号
  /**
   * 退货/款物品 是否选中（UI交互使用）
   */
  refundedGoods: Array<string> = []
  /**
   * 退货原因id
   */
  reasonId?: string
  /**
   * 退货原因描述
   */
  description?: string

  /**
   * 退款类型
   */
  refundType: OrderRefundTypeEnum = undefined

  /**
   * 退款金额
   */
  refundAmount: number = undefined

  /**
   * 退款金额来源
   */
  amountSource: number = undefined

  /**
   * 数量（暂时没用上，目前先固定为 1，后续多商品再调整）
   */
  quantity = 1
  /*
   *  发起退货
   * */
  async agreeReturnApply() {
    const request = new ApplyOrderReturnRequest()
    request.orderNo = this.orderNo
    request.reasonId = this.reasonId //退货原因id
    request.description = this.description //退货原因描述
    request.returnOrderType = this.refundType //退货单类型
    const returnInfo = new SubOrderAfterInfo()
    returnInfo.subOrderNo = this.subOrderNo //子订单号
    returnInfo.amount = this.refundAmount //退货金额
    returnInfo.amountSource = this.amountSource //退货金额来源
    returnInfo.quantity = this.quantity //退货数量
    const properties = new Map<string, string>() //扩展信息（华医网差异化course Type)1=专业2=公需
    if (this.refundedGoods.length == 1) {
      properties.set('courseType', this.refundedGoods[0])
    }
    if (this.refundedGoods.length == 2) {
      properties.set('courseType', '3')
    }
    const obj: any = {}
    for (const [key, value] of properties) {
      obj[key] = Number(value)
    }
    returnInfo.properties = obj
    request.returnInfo = returnInfo
    console.log(request, 'request')
    const res = await PlatformJxjypxtyptFjzjTrade.applyOrderReturn(request)
    return this.filterRes(res).status
  }

  filterRes(res: Response<any>) {
    return CalReturnOrderUtil.filterRes(res)
  }
}
