/**
 * 延时器
 */
import UserInfoModule from '@api/service/customer/user/UserModule'
import BaseReportModel from '@api/service/common/webfunny/models/BaseReportModel'
import WebfunnyReport from '@api/service/common/webfunny/WefunnyReport'

class DailyTiming {
  /**
   * 获取用户信息
   */
  userInfo = UserInfoModule.queryUserFactory.getQueryUserInfo()

  /**
   * webfunny埋点模型
   */
  baseReportModel = new BaseReportModel()
  /**
   * 初始化map
   */
  getStoredMap() {
    const storedMap = localStorage.getItem('userDateMap')
    return storedMap ? new Map(JSON.parse(storedMap)) : new Map()
  }
  /**
   * 存储map到localStorage
   */
  saveMapToLocalStorage(map: any) {
    localStorage.setItem('userDateMap', JSON.stringify(Array.from(map.entries())))
  }
  /**
   * 上报智能学习webfunny
   */
  recordDate() {
    const userDateMap = this.getStoredMap()
    const userId = this.userInfo.userInfo.userInfo.userId
    if (localStorage.getItem('userDateMap')) {
      if (userDateMap.has(userId)) {
        const storedDate = userDateMap.get(userId)
        if (storedDate == new Date().toDateString()) {
          return
        } else {
          localStorage.removeItem('userDateMap')
          userDateMap.clear()
          WebfunnyReport.upCommonEvent('通用-智能学习', this.baseReportModel)
        }
      } else {
        WebfunnyReport.upCommonEvent('通用-智能学习', this.baseReportModel)
      }
    } else {
      WebfunnyReport.upCommonEvent('通用-智能学习', this.baseReportModel)
    }
    userDateMap.set(userId, new Date().toDateString())
    this.saveMapToLocalStorage(userDateMap)
  }
}

export default new DailyTiming()
