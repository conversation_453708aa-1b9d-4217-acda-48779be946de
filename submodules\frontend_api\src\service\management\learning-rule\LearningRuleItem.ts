import BasicInfo, { TrainingTimeRange } from '@api/service/management/learning-rule/model/BasicInfo'
import RuleInfo, { TimeRange } from '@api/service/management/learning-rule/model/RuleInfo'
import PlatformSupplementStudyRulesSetting, {
  CourseStudyRuleSettingRequest,
  GenernalResponse,
  PageSupplementStudyRuleSettingResponse,
  SaveSupplementStudyRuleSettingRequest,
  SpecialRuleSettingRequest,
  SuitPropertyRequest,
  SuitTrainingTimeRequest,
  SupplementStudyRuleSettingResponse,
  UpdateSupplementStudyRuleSettingRequest
} from '@api/platform-gateway/platform-supplement-study-rules-setting-v1'
import { Response, ResponseStatus } from '@hbfe/common'
import { ApplyRangeEnum } from '@api/service/management/learning-rule/enum/ApplyRangeEnum'
import StudyRulesSetting from '@api/platform-gateway/platform-supplement-study-rules-setting-v1'
import { cloneDeep } from 'lodash'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import { TimeModeEnum } from '../learning-rule/enum/TimeModeEnum'

export default class LearnRuleItem {
  id = ''
  /**
   *基础设置
   */
  basicInfo: BasicInfo = new BasicInfo()
  /**
   * 规则设置
   */
  ruleInfo: RuleInfo = new RuleInfo()
  /**
   * 状态启停用
   */
  enable = false
  /**
   * 更新时间
   */
  updateTime = ''
  /**
   * 操作人id
   */
  createUserId = ''
  /**
   * 操作人
   */
  operator = ''
  /**
   * 比对自身
   */
  private compareData: LearnRuleItem = null
  /**
   * 重复数组
   */
  duplicateList: Array<string> = []

  /**
   * 获取详情
   * @param id
   */
  async getDetail(id: string) {
    const res = await StudyRulesSetting.getSupplementStudyRuleSettingInServicer(id)
    if (res.status.isSuccess()) {
      await QueryIndustry.getIndustryDICT()
      Object.assign(this, LearnRuleItem.fromDetail(res.data))
      this.basicInfo.industryName =
        QueryIndustry.industryDICTList.find(i => i.id === this.basicInfo.industryId)?.name || ''
    }
    return res.status
  }

  static from(dto: PageSupplementStudyRuleSettingResponse) {
    const vo = new LearnRuleItem()
    vo.id = dto.ruleId
    vo.basicInfo.industryId = dto.suitIndustryId
    vo.basicInfo.applyRange = dto.suitType
    vo.updateTime = dto.updateTime
    vo.createUserId = dto.createUserId
    vo.enable = dto.enabled
    vo.basicInfo.yearTrainRangeList = dto.years.map(item => {
      const itemVo = new TrainingTimeRange()
      itemVo.year = item.toString()
      return itemVo
    })
    if (vo.basicInfo.applyRange === ApplyRangeEnum.region) {
      vo.basicInfo.regionIds = dto.suitProperties.map(item => item.value)
    } else if (vo.basicInfo.applyRange === ApplyRangeEnum.scheme) {
      vo.basicInfo.schemeIds = dto.suitProperties.map(item => item.value)
    }
    // vo.basicInfo.timeMode = dto.courseStudyRuleSetting.studyLimitTypes == 1 ? 1 : 0
    return vo
  }

  static fromDetail(dto: SupplementStudyRuleSettingResponse) {
    const vo = new LearnRuleItem()
    vo.id = dto.ruleId
    vo.basicInfo.industryId = dto.suitIndustryId
    vo.basicInfo.applyRange = dto.suitType
    if (dto.suitType === ApplyRangeEnum.region) {
      vo.basicInfo.regionIds = dto.suitProperties.map(item => item.value)
    } else if (dto.suitType === ApplyRangeEnum.scheme) {
      vo.basicInfo.schemeIds = dto.suitProperties.map(item => item.value)
    }
    vo.basicInfo.yearTrainRangeList = dto.suitTrainingTimes.map(item => {
      const itemVo = new TrainingTimeRange()
      itemVo.year = item.year.toString()
      itemVo.startTime = item.trainingTime.startTime
      itemVo.endTime = item.trainingTime.endTime
      return itemVo
    })
    vo.basicInfo.timeMode =
      dto.courseStudyRuleSetting.studyLimitTypes === 1 ? TimeModeEnum.learning : TimeModeEnum.physical
    vo.ruleInfo.staggerStartTrainingTime = dto.staggerStartTrainingTime
    vo.ruleInfo.learningTimeRange = new TimeRange(
      dto.courseStudyRuleSetting.allowedDailyStudyTimes?.[0].startTime ?? '00:00:00',
      dto.courseStudyRuleSetting.allowedDailyStudyTimes?.[0].endTime ?? '23:59:59'
    )
    vo.ruleInfo.notLearningTimes = dto.courseStudyRuleSetting.disallowedDailyStudyTimes?.length
      ? dto.courseStudyRuleSetting.disallowedDailyStudyTimes.map(item => new TimeRange(item.startTime, item.endTime))
      : [
          new TimeRange('00:00:00', '08:00:00'),
          new TimeRange('12:00:00', '14:00:00'),
          new TimeRange('22:00:00', '23:59:59')
        ]
    vo.ruleInfo.firstLearningStartTime = dto.courseStudyRuleSetting.dailyOneTimeStudyHoursRange.startTime
    vo.ruleInfo.firstLearningEndTime = dto.courseStudyRuleSetting.dailyOneTimeStudyHoursRange.endTime
    vo.ruleInfo.maxLearningTime = dto.courseStudyRuleSetting.dailyStudyTime
    vo.ruleInfo.learningTime = dto.courseStudyRuleSetting.dailyOneTimeStudyHours
    vo.ruleInfo.minAdvanceDay = dto.specialtyRuleSetting.advanceOpenTimeDay?.startTime ?? 5
    vo.ruleInfo.maxAdvanceDay = dto.specialtyRuleSetting.advanceOpenTimeDay?.endTime ?? 10
    vo.compareData = new LearnRuleItem()
    vo.compareData.ruleInfo = cloneDeep(vo.ruleInfo)
    vo.compareData.basicInfo = cloneDeep(vo.basicInfo)
    return vo
  }

  /**
   * 启停用规则
   */
  async changeStatus() {
    let res = new Response<GenernalResponse>()
    if (this.enable) {
      // 停用
      res = await PlatformSupplementStudyRulesSetting.disAbleSupplementStudyRuleSetting(this.id)
    } else {
      // 启用
      res = await PlatformSupplementStudyRulesSetting.enableSupplementStudyRuleSetting(this.id)
    }
    if (res.status.isSuccess()) {
      return new ResponseStatus(res.status.code, res.status.message)
    } else {
      return res.status
    }
  }

  /**
   * 保存学习规则
   */
  async save() {
    const request = LearnRuleItem.to(this)
    this.duplicateList = []
    const res = await PlatformSupplementStudyRulesSetting.saveSupplementStudyRuleSetting(request)
    if (res.status.isSuccess() && res.data) {
      this.id = res.data.ruleId
      this.duplicateList = res.data.codeList || []
      return new ResponseStatus(Number(res.data.code), res.data.message)
    }
    return res.status
  }
  /**
   * 更新学习规则
   */
  async update() {
    const request = LearnRuleItem.toUpdate(this)
    this.duplicateList = []
    const res = await PlatformSupplementStudyRulesSetting.updateSupplementStudyRuleSetting(request)
    if (res.status.isSuccess() && res.data) {
      this.duplicateList = res.data.codeList || []
      return new ResponseStatus(Number(res.data.code), res.data.message)
    }
    return res.status
  }
  static to(vo: LearnRuleItem) {
    const dto = new SaveSupplementStudyRuleSettingRequest()
    dto.suitIndustryId = vo.basicInfo.industryId
    dto.suitType = vo.basicInfo.applyRange
    if (dto.suitType === ApplyRangeEnum.region) {
      dto.sultProperties = new Array<SuitPropertyRequest>()
      const regionList = vo.basicInfo.getSelectedRegion()
      regionList.forEach(region => {
        if (region.leaf) {
          dto.sultProperties.push({
            type: ApplyRangeEnum.region,
            value: region.code
          })
        } else {
          region.children.forEach(child => {
            dto.sultProperties.push({
              type: ApplyRangeEnum.region,
              value: child.code
            })
          })
        }
      })
    } else if (dto.suitType === ApplyRangeEnum.scheme) {
      dto.sultProperties = new Array<SuitPropertyRequest>()
      vo.basicInfo.schemeIds.forEach(scheme => {
        dto.sultProperties.push({
          type: ApplyRangeEnum.scheme,
          value: scheme
        })
      })
    }
    dto.suitTrainingTimes = vo.basicInfo.yearTrainRangeList.map(item => {
      const suitTrainingTime = new SuitTrainingTimeRequest()
      suitTrainingTime.year = Number(item.year)
      suitTrainingTime.timeRequest = {
        startTime: item.startTime,
        endTime: item.endTime
      }
      return suitTrainingTime
    })
    dto.staggerStartTrainingTime = vo.ruleInfo.staggerStartTrainingTime
    const courseStudyRuleSetting = new CourseStudyRuleSettingRequest()
    courseStudyRuleSetting.allowedDailyStudyTimes = [vo.ruleInfo.learningTimeRange]
    courseStudyRuleSetting.disallowedDailyStudyTimes = vo.ruleInfo.notLearningTimes
    courseStudyRuleSetting.startDay = vo.ruleInfo.firstLearningStartTime
    courseStudyRuleSetting.endDay = vo.ruleInfo.firstLearningEndTime

    courseStudyRuleSetting.dailyStudyTime = vo.ruleInfo.maxLearningTime
    courseStudyRuleSetting.dailyOneTimeStudyHours = vo.ruleInfo.learningTime
    courseStudyRuleSetting.studyLimitTypes = vo.basicInfo.timeMode === TimeModeEnum.physical ? 2 : 1
    dto.courseStudyRuleSetting = courseStudyRuleSetting
    dto.specialtyRuleSetting = new SpecialRuleSettingRequest()
    dto.specialtyRuleSetting.advanceOpenTimeDay = {
      startTime: vo.ruleInfo.minAdvanceDay,
      endTime: vo.ruleInfo.maxAdvanceDay
    }
    return dto
  }

  static toUpdate(vo: LearnRuleItem) {
    const dto = new UpdateSupplementStudyRuleSettingRequest()
    dto.ruleId = vo.id
    // dto.courseStudyRuleSetting.studyLimitTypes = vo.basicInfo.timeMode === TimeModeEnum.physical ? 2 : 1
    if (vo.basicInfo.applyRange === ApplyRangeEnum.region && vo.basicInfo.regionTree?.length) {
      const sultProperties = new Array<SuitPropertyRequest>()
      const regionIdList = new Array<string>()
      const regionList = vo.basicInfo.getSelectedRegion()
      regionList.forEach(region => {
        if (region.leaf) {
          region.isServiceArea && regionIdList.push(region.code)
          region.isServiceArea &&
            sultProperties.push({
              type: ApplyRangeEnum.region,
              value: region.code
            })
        } else {
          region.children.forEach(child => {
            child.isServiceArea && regionIdList.push(child.code)
            child.isServiceArea &&
              sultProperties.push({
                type: ApplyRangeEnum.region,
                value: child.code
              })
          })
        }
      })
      if (!LearnRuleItem.isSameStringArray(vo.basicInfo.regionIds, regionIdList)) {
        dto.sultProperties = sultProperties
      }
    } else if (
      vo.basicInfo.applyRange === ApplyRangeEnum.scheme &&
      !LearnRuleItem.isSameStringArray(vo.basicInfo.schemeIds, vo.compareData.basicInfo.schemeIds)
    ) {
      dto.sultProperties = new Array<SuitPropertyRequest>()
      vo.basicInfo.schemeIds.forEach(scheme => {
        dto.sultProperties.push({
          type: ApplyRangeEnum.scheme,
          value: scheme
        })
      })
    }
    dto.staggerStartTrainingTime =
      vo.ruleInfo.staggerStartTrainingTime != vo.compareData?.ruleInfo.staggerStartTrainingTime
        ? vo.ruleInfo.staggerStartTrainingTime
        : undefined
    let differentProps: string[] = []
    // 对比培训区间
    differentProps = LearnRuleItem.findDifferentProperties(
      vo.basicInfo.yearTrainRangeList,
      vo.compareData.basicInfo.yearTrainRangeList
    )
    if (
      differentProps.length ||
      vo.basicInfo.yearTrainRangeList.length != vo.compareData.basicInfo.yearTrainRangeList.length
    ) {
      dto.suitTrainingTimes = vo.basicInfo.yearTrainRangeList.map(item => {
        const suitTrainingTime = new SuitTrainingTimeRequest()
        suitTrainingTime.year = Number(item.year)
        suitTrainingTime.timeRequest = {
          startTime: item.startTime,
          endTime: item.endTime
        }
        return suitTrainingTime
      })
    }
    // 对比培训规则
    differentProps = LearnRuleItem.findDifferentProperties(vo.ruleInfo, vo.compareData.ruleInfo, [
      'minAdvanceDay',
      'maxAdvanceDay',
      'staggerStartTrainingTime'
    ])
    if (differentProps.length) {
      const courseStudyRuleSetting = new CourseStudyRuleSettingRequest()
      courseStudyRuleSetting.allowedDailyStudyTimes = [vo.ruleInfo.learningTimeRange]
      courseStudyRuleSetting.disallowedDailyStudyTimes = vo.ruleInfo.notLearningTimes
      courseStudyRuleSetting.startDay = vo.ruleInfo.firstLearningStartTime
      courseStudyRuleSetting.endDay = vo.ruleInfo.firstLearningEndTime
      courseStudyRuleSetting.dailyStudyTime = vo.ruleInfo.maxLearningTime
      courseStudyRuleSetting.dailyOneTimeStudyHours = vo.ruleInfo.learningTime
      courseStudyRuleSetting.studyLimitTypes = vo.basicInfo.timeMode === TimeModeEnum.physical ? 2 : 1
      dto.courseStudyRuleSetting = courseStudyRuleSetting
    }
    // 对比特殊规则
    if (
      vo.ruleInfo.maxAdvanceDay != vo.compareData.ruleInfo.maxAdvanceDay ||
      vo.ruleInfo.minAdvanceDay != vo.compareData.ruleInfo.minAdvanceDay
    ) {
      dto.specialtyRuleSetting = new SpecialRuleSettingRequest()
      dto.specialtyRuleSetting.advanceOpenTimeDay = {
        startTime: vo.ruleInfo.minAdvanceDay,
        endTime: vo.ruleInfo.maxAdvanceDay
      }
    }
    return dto
  }
  static findDifferentProperties<T>(curr: T, prev: T, filterKey: string[] = []): string[] {
    const differentProps: string[] = []

    const recursiveCompare = (curr: T, prev: T, path = '') => {
      Object.keys(curr).forEach(key => {
        if (!Object.prototype.hasOwnProperty.call(prev, key)) {
          differentProps.push(path ? `${path}.${key}` : key)
        } else {
          const value1 = curr[key]
          const value2 = prev[key]

          if (typeof value1 !== 'object' || value1 === null || value2 === null) {
            if (value1 !== value2 && !filterKey.includes(key)) {
              differentProps.push(path ? `${path}.${key}` : key)
            }
          } else {
            recursiveCompare(value1, value2, path ? `${path}.${key}` : key)
          }
        }
      })
    }
    recursiveCompare(curr, prev)
    return differentProps
  }

  static async getHasPlatformSupplementStudyRuleSettingIndustrys() {
    const res = await StudyRulesSetting.getHasPlatformSupplementStudyRuleSettingIndustrysInServicer()
    return res.data
  }

  /**
   * 判断两个string数组内值完全相同
   */
  static isSameStringArray(arr1: string[], arr2: string[]) {
    if (arr1.length !== arr2.length) {
      return false
    }
    const set1 = new Set(arr1)
    const set2 = new Set(arr2)
    return set1.size === set2.size && [...set1].every(item => set2.has(item))
  }
}
