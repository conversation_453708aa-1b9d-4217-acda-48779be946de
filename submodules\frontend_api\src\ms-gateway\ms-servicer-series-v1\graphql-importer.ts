import getDockingTycAndQcc from './queries/getDockingTycAndQcc.graphql'
import getExcellentCourses from './queries/getExcellentCourses.graphql'
import getForceModifyInitialPassword from './queries/getForceModifyInitialPassword.graphql'
import getIndustries from './queries/getIndustries.graphql'
import getOfflineCollectiveRegisterConfig from './queries/getOfflineCollectiveRegisterConfig.graphql'
import getOfflineCollectiveRegisterConfigWithAuth from './queries/getOfflineCollectiveRegisterConfigWithAuth.graphql'
import getOnlineCollectiveRegisterConfig from './queries/getOnlineCollectiveRegisterConfig.graphql'
import getOnlineSchoolConfig from './queries/getOnlineSchoolConfig.graphql'
import getOnlineSchoolProtolConfig from './queries/getOnlineSchoolProtolConfig.graphql'
import getPhysicalRegions from './queries/getPhysicalRegions.graphql'
import getRegions from './queries/getRegions.graphql'
import getStudentRegisterFormConstraint from './queries/getStudentRegisterFormConstraint.graphql'
import getStudentRegisterFormIdCardTypeList from './queries/getStudentRegisterFormIdCardTypeList.graphql'
import getStudentResisterFormConstraintForConfig from './queries/getStudentResisterFormConstraintForConfig.graphql'
import getWechatLoginConfig from './queries/getWechatLoginConfig.graphql'
import getYears from './queries/getYears.graphql'
import isOfflineCollectiveRegisterConfigFilled from './queries/isOfflineCollectiveRegisterConfigFilled.graphql'
import save from './mutates/save.graphql'
import saveDockingTycAndQccConfig from './mutates/saveDockingTycAndQccConfig.graphql'
import saveExcellentCoursesConfig from './mutates/saveExcellentCoursesConfig.graphql'
import saveForceModifyInitialPassword from './mutates/saveForceModifyInitialPassword.graphql'
import saveOfflineCollectiveRegisterConfig from './mutates/saveOfflineCollectiveRegisterConfig.graphql'
import saveOnlineCollectiveRegisterConfig from './mutates/saveOnlineCollectiveRegisterConfig.graphql'
import saveOnlineSchoolProtocolConfig from './mutates/saveOnlineSchoolProtocolConfig.graphql'
import saveOnlineSchoolSEOConfig from './mutates/saveOnlineSchoolSEOConfig.graphql'
import saveStudentRegisterFormConstraintConfig from './mutates/saveStudentRegisterFormConstraintConfig.graphql'
import saveStudentRegisterFormIdCardTypeList from './mutates/saveStudentRegisterFormIdCardTypeList.graphql'
import saveValidateMethodConfig from './mutates/saveValidateMethodConfig.graphql'
import saveWechatLoginConfig from './mutates/saveWechatLoginConfig.graphql'
import updateTrainingCategoryName from './mutates/updateTrainingCategoryName.graphql'

export {
  getDockingTycAndQcc,
  getExcellentCourses,
  getForceModifyInitialPassword,
  getIndustries,
  getOfflineCollectiveRegisterConfig,
  getOfflineCollectiveRegisterConfigWithAuth,
  getOnlineCollectiveRegisterConfig,
  getOnlineSchoolConfig,
  getOnlineSchoolProtolConfig,
  getPhysicalRegions,
  getRegions,
  getStudentRegisterFormConstraint,
  getStudentRegisterFormIdCardTypeList,
  getStudentResisterFormConstraintForConfig,
  getWechatLoginConfig,
  getYears,
  isOfflineCollectiveRegisterConfigFilled,
  save,
  saveDockingTycAndQccConfig,
  saveExcellentCoursesConfig,
  saveForceModifyInitialPassword,
  saveOfflineCollectiveRegisterConfig,
  saveOnlineCollectiveRegisterConfig,
  saveOnlineSchoolProtocolConfig,
  saveOnlineSchoolSEOConfig,
  saveStudentRegisterFormConstraintConfig,
  saveStudentRegisterFormIdCardTypeList,
  saveValidateMethodConfig,
  saveWechatLoginConfig,
  updateTrainingCategoryName
}
