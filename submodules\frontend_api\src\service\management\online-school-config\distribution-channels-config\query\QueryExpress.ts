import MsTradeQueryFrontGatewayTradeQueryBackstage, {
  OfflineInvoiceDeliveryChannelResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import ExpressDetailVo from '@api/service/management/online-school-config/distribution-channels-config/query/vo/ExpressDetailVo'

class QueryExpress {
  detail: ExpressDetailVo = new ExpressDetailVo()

  hasExpressRemark() {
    return this.detail.id
  }

  async queryExpressDetail() {
    const result = await MsTradeQueryFrontGatewayTradeQueryBackstage.getDeliveryChannelListInServicer()
    this.detail = ExpressDetailVo.from(
      result.data.find((channel: OfflineInvoiceDeliveryChannelResponse) => {
        return channel.shippingMethod === 0
      })
    )
  }
}

export default QueryExpress
