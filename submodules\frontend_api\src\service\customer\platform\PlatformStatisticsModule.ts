import { ResponseStatus } from '../../../Response'
import PlatformUserCourseGateway from '@api/gateway/PlatformUserCourse'
import PlatformExamGateway, { UserFavoriteQuestionStatisticParamDTO } from '@api/gateway/PlatformExam'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { Role, RoleType, Secure } from '@api/Secure'
import { Module as Mod } from 'vuex'
import PlatformCourseLearning from '@api/service/common/models/platform/PlatformCourseLearning'

/**
 * 本地状态数据
 */
interface PlatformStatisticsState {
  /**
   * 平台课程学习统计
   */
  platformCourseLearningStatistics: Array<PlatformCourseLearning>
  /**
   * 收藏试题总数
   */
  totalFavoriteQuestionCount: number
}

/**
 * 我的学习方案模块
 */
@Module({ namespaced: true, dynamic: true, name: 'CustomerPlatformStatisticsModule', store })
class PlatformStatisticsModule extends VuexModule implements PlatformStatisticsState {
  /**
   * 平台课程学习统计
   */
  public platformCourseLearningStatistics: Array<PlatformCourseLearning> = new Array<PlatformCourseLearning>()

  /**
   * 收藏试题总数
   */
  public totalFavoriteQuestionCount = 0

  /**
   * 是否需要重新加载收藏题数
   */
  private needReloadFavoriteQuestionCount = true

  constructor(module: Mod<ThisType<any>, any>) {
    super(module)
    store.subscribe(mutation => {
      if (mutation.type?.indexOf('ALERT_EXAM_RELOAD') !== -1) {
        this.processSubmitPaperMessage(mutation.payload)
      }
    })
  }

  /**
   * 初始化平台学习统计
   * @param params 参数：courseId -  课程编号
   */
  @Action
  @Role([RoleType.user])
  public async init(params: { courseId: string }): Promise<ResponseStatus> {
    const currentCourseStatistics = this.platformCourseLearningStatistics.find(x => x.courseId === params.courseId)
    if (!currentCourseStatistics) {
      const { data, status } = await PlatformUserCourseGateway.getCourseLearningCount(params.courseId)
      if (status.isSuccess()) {
        const platformCourseLearning: PlatformCourseLearning = new PlatformCourseLearning()
        platformCourseLearning.courseId = params.courseId
        platformCourseLearning.learningCount = data
        this.SET_PLATFORM_LEARNING_STATISTICS(platformCourseLearning)
      } else {
        return Promise.resolve(status)
      }
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  @Action
  @Role([RoleType.user])
  async loadFavoriteQuestionCount(): Promise<ResponseStatus> {
    if (!this.needReloadFavoriteQuestionCount) {
      return new ResponseStatus(200, '无需再次加载收藏试题数')
    }

    const param: UserFavoriteQuestionStatisticParamDTO = new UserFavoriteQuestionStatisticParamDTO()
    const response = await PlatformExamGateway.statisticUserFavoriteQuestionCount(param)

    if (response.status.isSuccess()) {
      this.setTotalFavoriteQuestionCount(response.data)
    }

    return response.status
  }

  /**
   * 处理交卷消息
   * @param payload
   */
  async processSubmitPaperMessage(payload: { schemeId: string; issueId: string; answersId: string }) {
    console.log('收藏题匹配到，needReload置为true' + JSON.stringify(payload))
    this.setTotalFavoriteQuestionNeedReload()
  }

  /**
   * 指示获取指定课程下的学习人次统计信息
   * @return Function 参数：courseId - 课程编号
   */
  get getStatisticsByCourseId() {
    return (courseId: string): PlatformCourseLearning | undefined => {
      return this.platformCourseLearningStatistics.find(x => x.courseId === courseId)
    }
  }

  /**
   * 设置平台学习统计信息
   * @param params 学习统计信息
   */
  @Mutation
  private SET_PLATFORM_LEARNING_STATISTICS(params: PlatformCourseLearning) {
    const currentCourseStatistics = this.platformCourseLearningStatistics.find(x => x.courseId === params.courseId)
    if (currentCourseStatistics) {
      currentCourseStatistics.courseId = params.courseId
      currentCourseStatistics.learningCount = params.learningCount
    } else {
      this.platformCourseLearningStatistics.push(params)
    }
  }

  /**
   * 设置收藏题总数
   * @param totalFavoriteQuestionCount 收藏题总数
   */
  @Mutation
  private setTotalFavoriteQuestionCount(totalFavoriteQuestionCount: number) {
    this.totalFavoriteQuestionCount = totalFavoriteQuestionCount
    this.needReloadFavoriteQuestionCount = false
  }

  /**
   * 设置收藏题总数需要再次加载
   */
  @Mutation
  private setTotalFavoriteQuestionNeedReload() {
    this.needReloadFavoriteQuestionCount = true
  }
}

export default getModule(PlatformStatisticsModule)
