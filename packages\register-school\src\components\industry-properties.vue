<template>
  <el-radio-group v-model="propertyId" class="m-attribute-select">
    <el-radio
      v-for="indutryItem in list"
      :key="indutryItem.title"
      :label="getPropertyChooseValue(indutryItem.title)"
      @change="changeStyle"
      :disabled="disabled"
    >
      <span class="tit" v-if="!specailNameList.includes(indutryItem.title)"
        >{{ indutryItem.title }}通用的行业{{ isPersonnel ? '人员' : '培训' }}属性</span
      >
      <span class="tit" v-else>{{ indutryItem.title }}人社行业{{ isPersonnel ? '人员' : '' }}培训属性</span>
      <el-popover placement="right" width="400" trigger="click">
        <div class="f-fb f-f16 f-mb10" v-if="!specailNameList.includes(indutryItem.title)">
          {{ indutryItem.title }}通用的行业{{ isPersonnel ? '人员' : '培训' }}属性
        </div>
        <div class="f-fb f-f16 f-mb10" v-else>
          {{ indutryItem.title }}人社行业{{ isPersonnel ? '人员' : '' }}培训属性
        </div>
        <el-tabs v-model="activeName1" type="card" class="m-tab-card">
          <template v-if="['人社', '建设'].includes(title)">
            <el-tab-pane label="科目类型" name="first" v-if="!isPersonnel">
              <div
                class="f-pt10 f-f15 f-pl10 m-attribute-tree"
                :class="{ 'loading-height': indutryItem.isLoading }"
                v-loading="indutryItem.isLoading"
              >
                <div class="f-mb10" v-for="item in indutryItem.subjectType" :key="item.propertyId">
                  <el-badge is-dot type="info" class="badge-status f-mr30">{{ item.name }}</el-badge>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="专业类别" :name="!isPersonnel ? 'second' : 'first'">
              <div class="f-pt10 u-mh100" v-loading="indutryItem.isLoading">
                <el-tree
                  :data="indutryItem.majorType"
                  :props="defaultProps"
                  @node-click="handleNodeClick"
                  class="m-attribute-tree"
                ></el-tree>
              </div>
            </el-tab-pane>
          </template>
          <template v-if="title == '职业卫生'">
            <el-tab-pane label="培训类别" name="first" v-if="!isPersonnel">
              <div class="f-pt10 u-mh100" v-loading="indutryItem.isLoading">
                <el-tree
                  :data="indutryItem.majorType"
                  :props="defaultProps"
                  @node-click="handleNodeClick"
                  class="m-attribute-tree"
                ></el-tree>
              </div>
            </el-tab-pane>
            <el-tab-pane label="培训对象-岗位类别" :name="!isPersonnel ? 'second' : 'first'">
              <div class="f-pt10 u-mh100" v-loading="indutryItem.isLoading">
                <el-tree
                  :data="indutryItem.objectType"
                  :props="defaultProps"
                  @node-click="handleNodeClick"
                  class="m-attribute-tree"
                ></el-tree>
              </div>
            </el-tab-pane>
            <!-- <el-tab-pane label="岗位类别" name="fifth">
              <div class="f-pt10 f-f15 f-pl10 m-attribute-tree">
                <div class="f-mb10" v-for="item in IndustryData.postType" :key="item.propertyId">
                  <el-badge is-dot type="info" class="badge-status f-mr30">{{ item.name }}</el-badge>
                </div>
              </div>
            </el-tab-pane> -->
          </template>
          <template v-if="title == '工勤'">
            <el-tab-pane label="技术等级" name="first">
              <div class="f-pt10 f-f15 f-pl10 m-attribute-tree" v-loading="indutryItem.isLoading">
                <div class="f-mb10" v-for="item in indutryItem.technicalType" :key="item.propertyId">
                  <el-badge is-dot type="info" class="badge-status f-mr30">{{ item.name }}</el-badge>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="工种" name="seventh">
              <div class="f-pt10 f-f15 f-pl10 m-attribute-tree" v-loading="indutryItem.isLoading">
                <div class="f-mb10" v-for="item in indutryItem.jobType" :key="item.propertyId">
                  <el-badge is-dot type="info" class="badge-status f-mr30">{{ item.name }}</el-badge>
                </div>
              </div>
            </el-tab-pane>
          </template>
          <template v-if="title == '教师'">
            <!-- <el-tab-pane label="学段" name="first">
              <div class="f-pt10 f-f15 f-pl10 m-attribute-tree">
                <div class="f-mb10" v-for="item in IndustryData.section" :key="item.propertyId">
                  <el-badge is-dot type="info" class="badge-status f-mr30">{{ item.name }}</el-badge>
                </div>
              </div>
            </el-tab-pane> -->

            <el-tab-pane label="学段" name="first">
              <div class="f-pt10 u-mh100" v-loading="indutryItem.isLoading">
                <el-tree
                  :data="indutryItem.section"
                  :props="defaultProps"
                  @node-click="handleNodeClick"
                  class="m-attribute-tree"
                ></el-tree>
              </div>
            </el-tab-pane>
            <el-tab-pane label="学科" name="eighth">
              <div class="f-pt10 f-f15 f-pl10 m-attribute-tree" v-loading="indutryItem.isLoading">
                <div class="f-mb10" v-for="item in indutryItem.subjects" :key="item.propertyId">
                  <el-badge is-dot type="info" class="badge-status f-mr30">{{ item.name }}</el-badge>
                </div>
              </div>
            </el-tab-pane>
          </template>
          <template v-if="title == '药师'">
            <el-tab-pane label="科目类型" name="first" v-if="!isPersonnel">
              <div
                class="f-pt10 f-f15 f-pl10 m-attribute-tree"
                :class="{ 'loading-height': indutryItem.isLoading }"
                v-loading="indutryItem.isLoading"
              >
                <div class="f-mb10" v-for="item in indutryItem.subjectType" :key="item.propertyId">
                  <el-badge is-dot type="info" class="badge-status f-mr30">{{ item.name }}</el-badge>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="执业类别" :name="!isPersonnel ? 'ninth' : 'first'">
              <div class="f-pt10 u-mh100" v-loading="indutryItem.isLoading">
                <el-tree
                  :data="indutryItem.certType"
                  :props="defaultProps"
                  @node-click="handleNodeClick"
                  class="m-attribute-tree"
                ></el-tree>
              </div>
            </el-tab-pane>
          </template>
        </el-tabs>
        <el-button type="text" class="btn" slot="reference" @click="getIndustryInfo(indutryItem)"
          >查看详情<i class="f-ml5 el-icon-arrow-right"></i
        ></el-button>
      </el-popover>
    </el-radio>
  </el-radio-group>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import { IndustryPropertyIdEnum } from '@api/service/training-institution/online-school/enum/IndustryPropertyIdEnum'
  import { PersonIndustryIdEnum } from '@api/service/training-institution/online-school/enum/PersonIndustryIdEnum'
  class IndustryData {
    subjectType: Array<any>
    majorType: Array<any>
    objectType: Array<any>
    postType: Array<any>
    technicalType: Array<any>
    jobType: Array<any>
    section: Array<any> = []
    subjects: Array<any> = []
    title: string
    industryPropertyId: string
    isQuery = false
    isLoading = false
  }
  class IndustryPropertyList {
    RSindustryData: Array<IndustryData>
    JSindustryData: Array<IndustryData>
    WSindustryData: Array<IndustryData>
    GQindustryData: Array<IndustryData>
    LSindustryData: Array<IndustryData>
    YSindustryData: Array<IndustryData>
  }
  @Component
  export default class extends Vue {
    /**
     * 是否禁止修改
     */
    @Prop({
      required: false,
      default: () => false
    })
    disabled: boolean
    // 属性信息集合
    @Prop({
      required: true,
      default: () => new IndustryPropertyList()
    })
    industryPropertyObject: IndustryPropertyList
    // 人社类别
    @Prop({
      required: true,
      default: () => ''
    })
    title: string
    // 是否人员
    @Prop({
      type: Boolean,
      default: false
    })
    isPersonnel: boolean
    @Watch('title', { immediate: true, deep: true })
    getType(val: any) {
      if (val) {
        switch (val) {
          case '人社':
            this.list = this.industryPropertyObject.RSindustryData
            console.group(
              '%c%s',
              'padding:3px 60px;color:#fff;background-image: linear-gradient(to right, #ffa17f, #00223e)',
              'schemeRefundList调试'
            )
            console.log(this.industryPropertyObject.RSindustryData, 'this.industryPropertyObject.RSindustryData')
            console.groupEnd()
            break
          case '建设':
            this.list = this.industryPropertyObject.JSindustryData
            break
          case '职业卫生':
            this.list = this.industryPropertyObject.WSindustryData
            break
          case '工勤':
            this.list = this.industryPropertyObject.GQindustryData
            break
          case '教师':
            this.list = this.industryPropertyObject.LSindustryData
            break
          case '药师':
            this.list = this.industryPropertyObject.YSindustryData
            break
          default:
            break
        }
      }
    }
    // 属性id判断是否选中
    @Prop({
      required: false,
      default: () => ''
    })
    PropertiesId: string
    @Watch('PropertiesId', { immediate: true, deep: true })
    getValue(val: any) {
      if (val) {
        // 有属性id后改用属性id比较好
        this.propertyId = val
      }
    }
    @Prop({
      required: false,
      default: () => 0
    })
    isCancel: number
    @Watch('isCancel', { immediate: true, deep: true })
    getCancel(val: any) {
      if (!this.isCancel && !this.PropertiesId) {
        this.propertyId = 0
      }
    }
    propertyId = 0
    activeName1 = 'first'
    defaultProps = {
      children: 'children',
      label: 'name'
    }
    list: Array<IndustryData> = []
    /**
     * 特殊培训属性名称集合
     * RS HN AH
     */
    specailNameList: Array<string> = ['福建省', '河南省', '安徽省']
    handleNodeClick(data: any) {
      console.log(data)
    }
    /**
     * 用于外部触发回传
     */
    selectProperty() {
      if (this.propertyId) {
        this.$emit('getProperty', this.propertyId, this.title)
      }
    }
    // 选中先获取样式 内部触发回传
    changeStyle() {
      if (this.propertyId) {
        this.$emit('getProperty', this.propertyId, this.title)
      }
    }
    /**
     * 培训属性值判断
     */
    getPropertyChooseValue(val: string) {
      if (val) {
        switch (val) {
          case '人社':
            return this.isPersonnel ? PersonIndustryIdEnum.RS : IndustryPropertyIdEnum.RS
          case '福建省':
            return this.isPersonnel ? PersonIndustryIdEnum.RS : IndustryPropertyIdEnum.RS
          case '河南省':
            return this.isPersonnel ? PersonIndustryIdEnum.HN : IndustryPropertyIdEnum.HN
          case '安徽省':
            return this.isPersonnel ? PersonIndustryIdEnum.AH : IndustryPropertyIdEnum.AH
          case '甘肃省':
            return this.isPersonnel ? PersonIndustryIdEnum.GS : IndustryPropertyIdEnum.GS
          case '江苏省':
            return this.isPersonnel ? PersonIndustryIdEnum.JSS : IndustryPropertyIdEnum.JSS
          case '四川省人社':
            return this.isPersonnel ? PersonIndustryIdEnum.SCRS : IndustryPropertyIdEnum.SCRS
          case '江西省':
            return this.isPersonnel ? PersonIndustryIdEnum.JX : IndustryPropertyIdEnum.JX
          case '建设':
            return this.isPersonnel ? PersonIndustryIdEnum.JS : IndustryPropertyIdEnum.JS
          case '四川省':
            return this.isPersonnel ? PersonIndustryIdEnum.SC : IndustryPropertyIdEnum.SC
          case '职业卫生':
            return this.isPersonnel ? PersonIndustryIdEnum.WS : IndustryPropertyIdEnum.WS
          case '工勤':
            return this.isPersonnel ? PersonIndustryIdEnum.GQ : IndustryPropertyIdEnum.GQ
          case '教师':
            return this.isPersonnel ? PersonIndustryIdEnum.LS : IndustryPropertyIdEnum.LS
          case '药师':
            return this.isPersonnel ? PersonIndustryIdEnum.YS : IndustryPropertyIdEnum.YS
          default:
            break
        }
      }
    }
    getIndustryInfo(item: IndustryData) {
      if (item.isQuery) return
      this.$emit('getIndustryInfo', item.industryPropertyId)
    }
  }
</script>

<style lang="scss" scoped>
  .loading-height {
    min-height: 50px;
  }
  //抽屉
  .m-drawer {
    .el-drawer__body {
      overflow-y: scroll;
      display: flex;
      flex-direction: column;
    }

    .el-drawer__header {
      font-size: 18px;
      color: #333;
      margin-bottom: 20px;
    }

    .drawer-bd {
      padding: 0 20px 20px;
      flex: 1;
    }

    .drawer-ft {
      position: sticky;
      bottom: 0;
      z-index: 9;
      padding: 15px 0;
      background-color: rgba(#f8f8f8, 0.9);
      border-top: 1px solid #eee;
      text-align: center;
    }

    .m-btn-bar {
      &.is-sticky {
        padding-top: 10px;
      }
    }
    //行业属性弹窗-单选控件列表
    .m-attribute-select {
      width: 100%;

      .el-radio {
        border: 1px solid #e4e7ed;
        border-radius: 3px;
        padding: 15px;
        margin-top: 20px;
        margin-right: 0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;

        .el-radio__label {
          flex: 1;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }

        .tit {
          width: 400px;
          display: inline-block;
          font-size: 15px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &.is-checked {
          border: 1px solid #1f86f0;
        }
      }
    }
  }
</style>
