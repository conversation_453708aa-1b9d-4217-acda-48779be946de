schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取用户课程分页
		@param page
		@param query
		@return
	"""
	findUserCoursePage(page:Page,query:UserCourseQueryRequest,sort:UserCourseSortRequest):UserCourseItemResponsePage @page(for:"UserCourseItemResponse")
	"""获取课件媒体提交的进度的key
		@param token
		@param courseWareId
		@param mediaId
		@return
	"""
	getAssessKey(token:String,courseWareId:String,mediaId:String):String
	"""获取最后学习的媒体信息
		@param token 学习方案学习参数凭证
		@return 最后学习媒体信息
	"""
	getLastLearningMedia(token:String):LastLearningMediaResponse
	"""根据toekn获取学习信息
		@param token
		@return
	"""
	getLearningByToken(token:String):LearningResponse
	"""依据Token查询课程进度信息
		@param token 学习方案学习参数凭证
		@return 课程进度信息
	"""
	getLearningScheduleByToken(token:String):CourseScheduleResponse
	"""获取需要作答的弹窗题，默认规则是所有
		@return
		@param token
		@param courseWareId
	"""
	getNeedAnswerPopQuestions(token:String,courseWareId:String):[SimplePopQuestionResponse]
	"""获取弹窗题
		@param popQuestionId
		@return
	"""
	getPopQuestion(popQuestionId:String):PopQuestionResponse
	"""获取用户课程
		@param id
		@return
	"""
	getUserCourse(id:String):UserCourseResponse
}
type Mutation {
	"""申请课件播放资源
		@param token 申请播放token
		@param coursewareId 课件ID
	"""
	applyCoursewarePlayingResource(token:String,coursewareId:String):CoursewareResourcePlayResponse @NotAuthenticationRequired
	"""申请预览试听的播放token
		@param courseId 要试听的课程ID
	"""
	applyListenCourseToken(courseId:String):String @NotAuthenticationRequired
	"""申请播放
		@param token 申请播放token
	"""
	applyPlaying(token:String):PlayingResponse @NotAuthenticationRequired
	"""申请预览课程的播放token
		@param courseId 要预览的课程ID
	"""
	applyPreviewCourseToken(courseId:String):String @NotAuthenticationRequired
	"""申请重新加载华为云视频防盗链资源
		@param videoId 视频ID
	"""
	applyReloadHwyVideoAntiLeech(videoId:String):HWYVideoPlayResponse @NotAuthenticationRequired
	"""批量选课
		@param token
		@param chooseInfoList
	"""
	batchChooseCourse(token:String,chooseInfoList:[BatchChooseCourseRequest]):Void
	"""批量删除用户选课
		@param batchRemove
	"""
	batchRemoveUserCourse(batchRemove:UserBatchRemoveCourseRequest):Void
	"""删除用户选课
		@param id
	"""
	removeUserCourse(id:String):Void
	"""提交答案
		@param token
		@param submit
		@return
	"""
	submitAnswer(token:String,submit:SubmitPopQuestionRequest):Boolean!
	"""更新课件进度
		@param token 播放token
		@param coursewareId 课件ID
		@param schedule 要更新的进度
	"""
	updateCoursewareSchedule(token:String,coursewareId:String,schedule:Double!):Void
	"""用户批量选课
		@deprecated 选课数据6个字段从上下文中获取，和方案的6个字段id不一定相同 推荐换成 {@link #batchChooseCourse}
		@param batchChoose
	"""
	userBatchChooseCourse(batchChoose:UserBatchChooseCourseRequest):Void
	"""用户选课
		@param create
		@return
	"""
	userChooseCourse(create:UserCourseCreateRequest):UserCourseResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""批量选课信息"""
input BatchChooseCourseRequest @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.request.BatchChooseCourseRequest") {
	"""课程包ID"""
	poolId:String
	"""课程ID列表"""
	courseIds:[String]
}
"""<AUTHOR> create 2019/11/26 14:24"""
input CourseLearningDescribeRequest @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.request.CourseLearningDescribeRequest") {
	"""方案id"""
	schemeId:String
	"""学习方式id"""
	learningId:String
	"""选课规则id"""
	ruleId:String
	"""课程包id"""
	poolId:String
	sourceType:String
}
"""
	<AUTHOR> create 2019/11/5 16:49
"""
input MarkerRequest @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.request.MarkerRequest") {
	"""特征标记键"""
	key:String
	"""特征标记值"""
	value:String
}
"""<AUTHOR> create 2019/11/26 14:25"""
input NoRuleValidateDescribeRequest @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.request.NoRuleValidateDescribeRequest") {
	"""课程包id"""
	poolId:String
	"""特征标记"""
	markers:[MarkerRequest]
	sourceType:String
}
"""提交弹窗题
	<AUTHOR> create 2020/4/15 16:02
"""
input SubmitPopQuestionRequest @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.request.SubmitPopQuestionRequest") {
	"""课程ID"""
	courseId:String
	"""课件ID"""
	courseWareId:String
	"""媒体ID"""
	mediaId:String
	"""弹窗题Id"""
	questionId:String
	"""用户答题信息,单选题、多选题将用户选择的选项ID填入； 判断题 将"""
	answer:[String]
}
"""用户批量选课dto
	<AUTHOR> create 2019/11/26 14:33
"""
input UserBatchChooseCourseRequest @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.request.UserBatchChooseCourseRequest") {
	"""用户id"""
	userId:String
	"""来源类型
		@see UserCourseSourceType
	"""
	sourceType:String
	"""课程id集合"""
	courseIds:[String]
	"""选课学习来源描述"""
	courseLearning:CourseLearningDescribeRequest
	"""无规则校验来源描述"""
	noRuleValidate:NoRuleValidateDescribeRequest
	"""是否测试数据"""
	test:Boolean!
}
"""用户批量退课dto
	<AUTHOR> create 2019/11/26 14:33
"""
input UserBatchRemoveCourseRequest @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.request.UserBatchRemoveCourseRequest") {
	"""用户id"""
	userId:String
	"""来源类型
		@see UserCourseSourceType
	"""
	sourceType:String
	"""课程id集合"""
	courseIds:[String]
	"""选课学习来源描述"""
	courseLearning:CourseLearningDescribeRequest
	"""无规则校验来源描述"""
	noRuleValidate:NoRuleValidateDescribeRequest
}
"""<AUTHOR> create 2019/11/26 14:09"""
input UserCourseCreateRequest @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.request.UserCourseCreateRequest") {
	"""用户id"""
	userId:String
	"""来源类型
		@see UserCourseSourceType
	"""
	sourceType:String
	"""选课学习来源描述"""
	courseLearning:CourseLearningDescribeRequest
	"""无规则校验来源描述"""
	noRuleValidate:NoRuleValidateDescribeRequest
	"""课程id"""
	courseId:String
	"""是否测试数据"""
	test:Boolean!
}
"""用户课程查询
	<AUTHOR> create 2019/11/26 14:13
"""
input UserCourseQueryRequest @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.request.UserCourseQueryRequest") {
	"""用户id"""
	userId:String
	"""课程名称"""
	courseName:String
	"""课程名称是否精确查询"""
	excat:Boolean!
	"""学习状态，-1/0/1/2，全部/未学习/学习中/学习完成"""
	studyState:Int!
	"""起始最后一次学习时间"""
	startLastStudyTime:DateTime
	"""截止最后一次学习时间"""
	endLastStudyTime:DateTime
	"""课程包编号集合"""
	poolIdList:[String]
	"""课程编号集合"""
	courseIdList:[String]
	"""选课特征标记列表"""
	markers:[MarkerRequest]
	"""课程分类编号列表"""
	courseCatalogIdList:[String]
}
"""<AUTHOR> create 2019/11/28 13:57"""
input UserCourseSortRequest @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.request.UserCourseSortRequest") {
	"""排序字段
		0-最后学习时间
		1-学习进度
	"""
	orderByField:Int!
	"""是否降序"""
	descending:Boolean!
}
enum ScaleType @type(value:"com.fjhb.platform.component.exam.commons.api.consts.question.ScaleType") {
	CUSTOM
	SATISFACTION
	RECOGNITION
	IMPORTANCE
	WILLING
	CONFORMITY
}
type TagDTO @type(value:"com.fjhb.platform.component.exam.commons.api.dto.question.TagDTO") {
	id:String
	code:String
	tag:String
}
type BlankFillingDTO @type(value:"com.fjhb.platform.component.exam.commons.api.dto.question.content.BlankFillingDTO") {
	answerCount:Int!
	answersGroup:[[String]]
	answersItemScore:[Double]
	answerType:Int!
	sequence:Boolean!
	standard:String
}
type ChoiceItemDTO @type(value:"com.fjhb.platform.component.exam.commons.api.dto.question.content.ChoiceItemDTO") {
	id:String
	content:String
}
type ComprehensiveChildQuestionDTO @type(value:"com.fjhb.platform.component.exam.commons.api.dto.question.content.ComprehensiveChildQuestionDTO") {
	questionId:String
	title:String
	questionType:Int!
	judgement:JudgementDTO
	singleChoice:SingleChoiceDTO
	multipleChoice:MultipleChoiceDTO
	blankFilling:BlankFillingDTO
	essay:EssayDTO
	scale:ScaleDTO
	mode:Int!
	difficultyValue:Double!
	description:String
}
type ComprehensiveDTO @type(value:"com.fjhb.platform.component.exam.commons.api.dto.question.content.ComprehensiveDTO") {
	children:[ComprehensiveChildQuestionDTO]
}
type EssayDTO @type(value:"com.fjhb.platform.component.exam.commons.api.dto.question.content.EssayDTO") {
	referenceAnswer:String
	standard:String
	limitAnswerLength:Boolean!
	permitAnswerLengthMin:Int!
	permitAnswerLengthMax:Int!
}
type JudgementDTO @type(value:"com.fjhb.platform.component.exam.commons.api.dto.question.content.JudgementDTO") {
	correctAnswer:Boolean!
	correctText:String
	incorrectText:String
}
type MultipleChoiceDTO @type(value:"com.fjhb.platform.component.exam.commons.api.dto.question.content.MultipleChoiceDTO") {
	choiceItems:[ChoiceItemDTO]
	correctAnswers:[String]
}
type ScaleDTO @type(value:"com.fjhb.platform.component.exam.commons.api.dto.question.content.ScaleDTO") {
	scaleType:ScaleType
	startDegree:String
	endDegree:String
	series:Int!
	initialValue:Int!
}
type SingleChoiceDTO @type(value:"com.fjhb.platform.component.exam.commons.api.dto.question.content.SingleChoiceDTO") {
	choiceItems:[ChoiceItemDTO]
	correctAnswer:String
}
type SourceDescribeDTO @type(value:"com.fjhb.platform.core.courselearning.v1.api.dto.usercourse.source.SourceDescribeDTO") {
	sourceType:String
}
"""章节播放结构信息"""
type ChaptersPlayResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.ChaptersPlayResponse") {
	"""章节编号"""
	id:String
	"""章节名称"""
	name:String
	"""上级章节编号"""
	parentId:String
	"""播放时间点"""
	timePoint:Int!
	"""排序"""
	sort:Int!
}
"""课程分类dto
	<AUTHOR> create 2019/11/28 14:23
"""
type CourseCategoryResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CourseCategoryResponse") {
	"""课程分类ID"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属服务单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""课程分类名称"""
	name:String
	"""上级分类ID"""
	parentId:String
	"""排序"""
	sort:Int!
	"""备注"""
	remarks:String
	"""业务实体ID"""
	objectId:String
}
"""课程来源描述详情
	<AUTHOR> create 2019/12/3 14:59
"""
type CourseDescDetailResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CourseDescDetailResponse") {
	"""课程信息"""
	course:CourseResponse
	"""最后学习媒体"""
	lastLearningMedia:LastLearningMediaResponse
	"""课程id"""
	courseId:String
	"""学习的特征标记"""
	markers:[MarkerResponse]
}
"""
	<AUTHOR> create 2019/12/3 15:06
"""
type CourseInPoolResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CourseInPoolResponse") {
	"""课程池与课程关系编号"""
	id:String
	"""所属课程池编号"""
	poolId:String
	"""课程编号"""
	courseId:String
	"""课程信息"""
	course:CourseResponse
	"""课程序号"""
	sequence:Int!
	"""课程标量值|在课程池规则中，课程在课程池中权重值"""
	quantitative:Double!
	"""课程学时|课程在课程池中的学时"""
	period:Double!
	"""课程过期时间|课程在课程池中到期时间，null表示该课程在课程池中无期限限制"""
	courseExpireTime:DateTime
	"""特征标记列表"""
	markers:[MarkerResponse]
	"""创建时间"""
	createTime:DateTime
}
"""课程目录播放结构信息"""
type CourseOutlinePlayResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CourseOutlinePlayResponse") {
	"""课程目录ID"""
	id:String
	"""课程目录名称"""
	name:String
	"""父级ID，0表示顶级"""
	parentId:String
	"""排序"""
	sort:Int!
}
"""<AUTHOR> create 2019/11/26 16:56"""
type CourseOutlineResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CourseOutlineResponse") {
	"""课程目录ID"""
	id:String
	"""课程ID"""
	cseId:String
	"""挂载的课件ID"""
	cweId:String
	"""课件信息"""
	courseWare:CoursewareResponse
	"""目录名称"""
	name:String
	"""上级目录ID"""
	parentId:String
	"""排序"""
	sort:Int!
	"""自定义状态 0不可以试听，1可以试听"""
	customeStatus:Int!
	"""自定义拓展数据"""
	expendData:String
	"""下级目录"""
	subCourseOutlines:[CourseOutlineResponse]
}
"""课程播放结构信息"""
type CoursePlayResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CoursePlayResponse") {
	"""课程ID"""
	id:String
	"""课程名称"""
	name:String
	"""课程已学习的进度"""
	schedule:Double!
	"""最后播放时间"""
	lastPlayTime:DateTime
	"""课程计划总课件数"""
	plannedCoursewareCount:Int!
	"""当前课件数"""
	currentCoursewareCount:Int!
	"""课程简介"""
	abouts:String
	"""教师ID集合"""
	teacherIds:[String]
	"""课程目录信息"""
	courseOutlines:[CourseOutlinePlayResponse]
	"""课件列表"""
	coursewareList:[CoursewarePlayResponse]
}
"""<AUTHOR> create 2019/11/26 16:53"""
type CourseResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CourseResponse") {
	"""课程ID"""
	id:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""权重,表示学时,学分等"""
	period:Double!
	"""课程简介"""
	abouts:String
	"""课程目录"""
	courseOutline:[CourseOutlineResponse]
	"""计划授课讲数"""
	plannedLecturesNum:Int!
	"""章节"""
	chapters:[TagResponse]
}
"""课程进度信息
	<AUTHOR>
	@date 2020/3/12
	@since 1.0.0
"""
type CourseScheduleResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CourseScheduleResponse") {
	"""课程编号"""
	courseId:String
	"""用户编号"""
	userId:String
	"""课程学习进度"""
	schedule:Double!
	"""学习状态：0/1/2，未学习/学习中/学习完成"""
	studyState:Int!
	"""最后学习时间"""
	lastStudyTime:DateTime
	"""课件学习进度列表"""
	coursewareScheduleList:[CoursewareScheduleResponse]
}
"""
	<AUTHOR> create 2019/12/3 15:03
"""
type CourseWareDescDetailResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CourseWareDescDetailResponse") {
	"""课件信息"""
	courseWare:CoursewareResponse
	"""课件id"""
	courseWareId:String
}
"""课件播放结构信息"""
type CoursewarePlayResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CoursewarePlayResponse") {
	"""课件ID"""
	id:String
	"""课件名称"""
	name:String
	"""课件时长，单位秒"""
	timeLength:Int!
	"""课件类型：1表示文档，2表示单视频，3表示多媒体"""
	type:Int!
	"""自定义状态 0不可以试听，1可以试听"""
	customeStatus:Int!
	"""排序"""
	sort:Int!
	"""课件已学习的进度"""
	schedule:Double!
	"""是否有课程目录"""
	hedCourseOutline:Boolean!
	"""课程目录ID"""
	courseOutlineId:String
	"""是否最后一次播放的课件"""
	lastedPlay:Boolean!
	"""最后播放时间"""
	lastPlayTime:DateTime
}
"""课件播放资源结构信息"""
type CoursewareResourcePlayResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CoursewareResourcePlayResponse") {
	"""课件ID"""
	id:String
	"""课件类型"""
	type:Int!
	"""试听时长"""
	listenTime:Int!
	"""视频信息"""
	video:VideoPlayResponse
	"""文档信息"""
	document:DocumentPlayResponse
}
"""课件信息
	<AUTHOR> create 2019/11/12 10:42
"""
type CoursewareResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CoursewareResponse") {
	"""课件id"""
	id:String
	"""课件名称"""
	name:String
	"""媒体进度"""
	schedule:Double!
	"""媒体列表"""
	mediaList:[MediaResponse]
}
"""课件学习进度信息
	<AUTHOR>
	@date 2020/3/12
	@since 1.0.0
"""
type CoursewareScheduleResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.CoursewareScheduleResponse") {
	"""课件编号"""
	coursewareId:String
	"""课件学习进度"""
	schedule:Double!
	"""学习状态：0/1/2，未学习/学习中/学习完成"""
	studyState:Int!
	"""最后学习时间"""
	lastStudyTime:DateTime
	"""媒体学习进度信息"""
	mediaScheduleList:[MediaScheduleResponse]
}
"""文档播放结构信息"""
type DocumentPlayResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.DocumentPlayResponse") {
	"""文档ID"""
	id:String
	"""文档类型"""
	type:Int!
	"""文档播放路径"""
	path:String
	"""文档时长"""
	timeLength:Int!
	"""已播放时长"""
	playedTimeLength:Int!
}
"""800里视频dto
	<AUTHOR> create 2019/11/13 16:52
"""
type EHLIVideoResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.EHLIVideoResponse") {
	"""资源地址.mp3.mp4"""
	resource:String
	"""资源域名"""
	streamHost:String
}
"""华为云资源dto
	<AUTHOR> create 2019/11/13 16:54
"""
type ExternalLinksResourceResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.ExternalLinksResourceResponse") {
	"""地址"""
	url:String
	"""清晰度"""
	quality:Int!
}
"""华为云视频播放结构信息"""
type ExternalLinksVideoPlayResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.ExternalLinksVideoPlayResponse") {
	"""视频资源信息"""
	videoResources:[ExternalLinksResourceResponse]
}
"""华为云音频资源播放结构信息"""
type HWYAudioResourcePlayResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.HWYAudioResourcePlayResponse") {
	"""音频播放地址"""
	path:String
}
"""华为云资源dto
	<AUTHOR> create 2019/11/13 16:54
"""
type HWYResourceResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.HWYResourceResponse") {
	"""地址"""
	url:String
	"""清晰度"""
	quality:Int!
}
"""华为云视频播放结构信息"""
type HWYVideoPlayResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.HWYVideoPlayResponse") {
	"""视频资源信息"""
	videoResources:[HWYVideoResourcePlayResponse]
	"""音频资源信息"""
	audioResource:HWYAudioResourcePlayResponse
}
type HWYVideoResourcePlayResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.HWYVideoResourcePlayResponse") {
	path:String
	clarity:Int!
}
"""华为云视频dto
	<AUTHOR> create 2019/11/13 16:53
"""
type HWYVideoResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.HWYVideoResponse") {
	"""华为云媒体资源"""
	resources:[HWYResourceResponse]
	"""资源域名"""
	streamHost:String
}
"""<AUTHOR> create 2019/12/13 15:42"""
type LastLearningMediaResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.LastLearningMediaResponse") {
	"""多媒体学习记录编号"""
	multimediaRecordId:String
	"""用户编号"""
	userId:String
	"""业务实体编号"""
	objectId:String
	"""课程编号"""
	courseId:String
	"""课件编号"""
	coursewareId:String
	"""学习进度，百分比小数点后两位"""
	schedule:Double!
	"""上次播放刻度|最后一次播放的刻度,int"""
	lastPlayScale:Int!
	"""学习状态，0/1/2，未学习/学习中/学习完成"""
	studyState:Int!
	"""学习完成时间，如果学习状态不是学习完成，该值为默认日期时间1970-01-01"""
	completedTime:DateTime
	"""最后一次学习时间，日期格式yyyy-MM-dd HH:mm:ss"""
	lastStudyTime:DateTime
	"""多媒体ID"""
	multiMediaId:String
}
"""学习模型
	<AUTHOR> create 2019/12/3 14:56
"""
type LearningResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.LearningResponse") {
	"""token
		<p>
		以token作为id是因为目前学习服务依靠课程id+marker来标识唯一的学习记录，相关接口也是以课程id+marker来查询。
		学习领域依靠 培训方案或其他调用方生成的token来获取学习信息（其中来源类型和来源的具体值都是描述在token内）。
		</p>
	"""
	id:String
	"""用户id"""
	userId:String
	"""来源类型"""
	sourceType:Int!
	"""课程来源描述"""
	courseDescribe:CourseDescDetailResponse
	"""课程池内课程描述"""
	poolCourseDescribe:PoolCourseDescDetailResponse
	"""用户课程描述"""
	userCourseDescribe:UserCourseDescDetailResponse
	"""课件描述"""
	courseWareDescribe:CourseWareDescDetailResponse
	"""进度"""
	schedule:Double!
}
"""讲义播放结构信息"""
type LecturePlayResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.LecturePlayResponse") {
	"""讲义ID"""
	id:String
	"""讲义类型，1代表html|2代表image|3表示vga"""
	type:Int!
	"""播放时间点，单位秒"""
	timePoint:Int!
	"""讲义路径"""
	path:String
}
"""讲义信息
	<AUTHOR> create 2019/11/13 17:07
"""
type LectureResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.LectureResponse") {
	"""讲义ID"""
	id:String
	"""所属课件ID"""
	cweId:String
	"""讲义类型，1代表html|2代表image|3表示vga"""
	type:Int!
	"""播放时间点，单位秒"""
	timePoint:Int!
	"""讲义路径"""
	path:String
	"""拓展信息"""
	expand:String
}
"""
	<AUTHOR> create 2019/11/5 16:49
"""
type MarkerResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.MarkerResponse") {
	"""特征标记键"""
	key:String
	"""特征标记值"""
	value:String
}
"""媒体信息
	<AUTHOR> create 2019/11/12 10:43
"""
type MediaResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.MediaResponse") {
	"""媒体id"""
	id:String
	"""媒体名称"""
	name:String
	"""时长"""
	time:Int!
	"""媒体类型 1.pdf 2.单视频 3.三分 4.web"""
	type:Int!
	"""播放资源"""
	playResources:PlayResourcesResponse
	"""媒体时长"""
	timelength:Int!
	"""自定义状态 0不可以试听，1可以试听"""
	customeStatus:Int!
	"""播放模式 1.支持试听 2.不支持试听 3.购买后播放"""
	mode:Int!
	"""试听时长"""
	listenTime:Int!
	"""媒体进度"""
	schedule:Double!
	"""已经播放时长"""
	alreadyPlayTime:Int!
	"""允许播放次数,播放学习时用到,未有限制传null"""
	allowPlayTimes:Int
	"""讲义信息"""
	lectureList:[LectureResponse]
	"""视频章节信息"""
	catalogList:[VideoChapterResponse]
}
"""媒体学习进度信息
	<AUTHOR>
	@date 2020/3/12
	@since 1.0.0
"""
type MediaScheduleResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.MediaScheduleResponse") {
	"""媒体编号"""
	mediaId:String
	"""媒体学习进度"""
	schedule:Double!
	"""媒体学习状态：0/1/2，未学习/学习中/学习完成"""
	studyState:Int!
	"""最后学习时间"""
	lastStudyTime:DateTime
}
"""播放资源dto
	<AUTHOR> create 2019/11/13 16:50
"""
type PlayResourcesResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.PlayResourcesResponse") {
	"""文档地址"""
	docPath:String
	"""800里视频资源"""
	player_800li:EHLIVideoResponse
	"""华为云视频资源"""
	player_hwCloud:HWYVideoResponse
	"""默认资源类型（当存在800里及华为云两种视频资源时默认播放的视频资源）"""
	defaultResourceType:String
}
"""播放信息"""
type PlayingResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.PlayingResponse") {
	"""播放模式，1表示用户学习，2表示预览 3表示试听"""
	mode:Int!
	"""播放资源类型，1表示课程，2表示课件"""
	resourceType:Int!
	"""用户ID"""
	userId:String
	"""课程资源信息"""
	course:CoursePlayResponse
}
"""课程池内课程来源详情
	<AUTHOR> create 2019/12/3 15:04
"""
type PoolCourseDescDetailResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.PoolCourseDescDetailResponse") {
	"""课程池内课程"""
	courseInPool:CourseInPoolResponse
	"""最后学习媒体"""
	lastLearningMedia:LastLearningMediaResponse
	"""课程包id"""
	poolId:String
	"""课程id"""
	courseId:String
	"""学习特征标记"""
	markers:[MarkerResponse]
}
"""弹窗题
	<AUTHOR> create 2020/4/16 8:06
"""
type PopQuestionResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.PopQuestionResponse") {
	"""弹窗题id、主键id"""
	popQuestionId:String
	"""弹窗时间：秒"""
	timePoint:Int!
	"""课件id"""
	courseWareId:String
	id:String
	applyTypes:[Int]
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	organizationId:String
	libraryId:String
	title:String
	judgement:JudgementDTO
	singleChoice:SingleChoiceDTO
	multipleChoice:MultipleChoiceDTO
	blankFilling:BlankFillingDTO
	essay:EssayDTO
	scale:ScaleDTO
	comprehensive:ComprehensiveDTO
	questionType:Int!
	mode:Int!
	difficulty:Double!
	description:String
	createUserId:String
	createTime:DateTime
	lastChangeTime:DateTime
	enabled:Boolean!
	rootId:String
	token:String
	relateCourseId:String
	relateCourseIds:[String]
	tags:[TagDTO]
}
"""简单的弹窗题dto
	<AUTHOR> create 2020/4/15 16:21
"""
type SimplePopQuestionResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.SimplePopQuestionResponse") {
	"""弹窗题id、主键id"""
	popQuestionId:String
	"""试题id"""
	id:String
	"""弹窗时间：秒"""
	timePoint:Int!
	"""课件id"""
	courseWareId:String
}
"""
	<AUTHOR> create 2020/1/13 14:12
"""
type TagResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.TagResponse") {
	"""标签id"""
	id:String
	"""标签code"""
	code:String
	"""标签"""
	tag:String
}
"""<AUTHOR> create 2019/12/3 15:08"""
type UserCourseDescDetailResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.UserCourseDescDetailResponse") {
	"""用户课程"""
	userCourse:UserCourseResponse
	"""最后学习媒体"""
	lastLearningMedia:LastLearningMediaResponse
	"""用户课程id"""
	id:String
	"""方案id"""
	schemeId:String
	"""学习方式id"""
	learningId:String
	"""选课规则id"""
	ruleId:String
	"""课程包id"""
	poolId:String
}
"""用户课程分页项
	<AUTHOR> create 2019/11/26 14:11
"""
type UserCourseItemResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.UserCourseItemResponse") {
	"""选课记录id"""
	id:String
	"""所属平台编号"""
	platformId:String
	"""所属平台版本编号"""
	platformVersionId:String
	"""所属项目编号"""
	projectId:String
	"""所属子项目编号"""
	subProjectId:String
	"""所属组织机构编号"""
	organizationId:String
	"""所属单位编号"""
	unitId:String
	"""用户id"""
	userId:String
	"""来源类型
		@see UserCourseSourceType
	"""
	sourceType:String
	"""来源描述"""
	sourceDescribe:SourceDescribeDTO
	"""用户课程学时"""
	period:Double
	"""课程状态|0/1/2，正常/冻结/过期，课程在用户个人选课池中的状态"""
	state:Int!
	"""(常规)选课时间"""
	chooseTime:DateTime
	"""过期时间|表示当前选择的课程有效期过期时间，默认2099/12/31，表示该选择的课程无期限的限制"""
	expireTime:DateTime
	"""课程信息"""
	course:CourseResponse
	"""课程分类"""
	courseCategory:CourseCategoryResponse
	"""学习记录信息"""
	courseSchedule:CourseScheduleResponse
}
"""用户课程dto
	<AUTHOR> create 2019/11/26 14:59
"""
type UserCourseResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.UserCourseResponse") {
	id:String
	userId:String
	"""来源类型
		@see UserCourseSourceType
	"""
	sourceType:String
	"""来源描述"""
	sourceDescribe:SourceDescribeDTO
	"""用户课程学时"""
	period:Double
	"""课程id"""
	courseId:String
	"""课程信息"""
	course:CourseResponse
	"""进度"""
	schedule:Double!
	"""是否测试数据"""
	test:Boolean!
}
"""视频章节信息
	<AUTHOR> create 2019/11/13 17:07
"""
type VideoChapterResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.VideoChapterResponse") {
	"""章节编号"""
	id:String
	"""所属课件编号"""
	cweId:String
	"""所属课件视频编号"""
	vdoId:String
	"""章节名称"""
	name:String
	"""上级章节编号"""
	parentId:String
	"""播放时间点，-1代表不参与播放，或许它就是用于展示章节结构的展示"""
	timePoint:Int!
	"""排序"""
	sort:Int!
}
"""视频播放结构信息"""
type VideoPlayResponse @type(value:"com.fjhb.platform.core.courselearning.v1.kernel.gateway.graphql.response.VideoPlayResponse") {
	"""视频ID"""
	id:String
	"""视频时长"""
	timeLength:Int!
	"""已播放时长"""
	playedTimeLength:Int!
	"""华为云视频信息"""
	hwyVideo:HWYVideoPlayResponse
	"""视频章节信息"""
	chapters:[ChaptersPlayResponse]
	"""视频讲义信息"""
	lectures:[LecturePlayResponse]
	"""外部链接视频信息"""
	externalLinksVideo:ExternalLinksVideoPlayResponse
}

scalar List
type UserCourseItemResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserCourseItemResponse]}
