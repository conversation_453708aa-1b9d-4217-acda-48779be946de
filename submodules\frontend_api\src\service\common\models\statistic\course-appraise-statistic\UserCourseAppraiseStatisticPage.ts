import { Appraise, EveryStartHit } from '@api/service/common/models/statistic/course-appraise-statistic/Appraise'
import { CourseAppraisalRecordResponse } from '@api/gateway/CommentEvaluation-default'
import { UserSimpleInfoDTO } from '@api/gateway/PlatformUser'

/**
 * 每行数据
 * <AUTHOR>
 * @Date 2021/5/7/0007 9:46
 */
export class UserCourseAppraiseStatistic {
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名
   */
  userName: string
  /**
   * 综合评价
   */
  averageComprehensive: Appraise
  /**
   * 评价内容
   */
  content: string
  /**
   * 评价时间
   */
  appraiseTime: string
  /**
   * 是否屏蔽
   */
  shield: boolean

  /**
   * 是否门户展示
   */
  isShowPortal() {
    return !this.shield
  }
}

/**
 * 分页
 * <AUTHOR>
 * @Date 2021/5/7/0007 9:40
 */
export class UserCourseAppraiseStatisticPage {
  list: Array<UserCourseAppraiseStatistic>
  totalSize: number

  static fromRemote(remotes: Array<CourseAppraisalRecordResponse>, userInfos: Array<UserSimpleInfoDTO>) {
    const list = new Array<UserCourseAppraiseStatistic>()
    remotes.forEach(remote => {
      const item = new UserCourseAppraiseStatistic()
      item.userId = remote.userId
      const user = userInfos.find(user => user.userId === remote.userId)
      item.userName = user?.nickName
      item.averageComprehensive = new Appraise()
      item.averageComprehensive.average = remote.comprehensiveStars
      item.content = remote.contents
      item.appraiseTime = remote.createTime
      item.shield = remote.shield
      list.push(item)
    })
    return list
  }
}
