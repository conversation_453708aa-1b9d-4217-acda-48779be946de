import { InvoiceTypeEnum, OrderOfInvoiceEnum } from '@hbfe/jxjy-customer-manage/src/views/enum/InvoiceEnum'

/*
 * @Description: 发票类
 * @Version: feature/*******.0
 * @Autor: Zhu Song
 * @Date: 2022-02-16 09:43:49
 * @LastEditors: Lin yt
 * @LastEditTime: 2022-05-07 09:12:18
 */

/**
 * InvoiceData 类用于封装修发票相关的数据。
 * 包含发票抬头信息、单位名称、统一社会信用代码等属性，以及配送和取货相关信息。
 */
export default class InvoiceData {
  /**
   * 发票抬头信息---姓名
   *
   * 表示发票抬头的个人或公司名称。
   *
   * 类型: string
   */
  name: string

  /**
   * 发票抬头类型
   *
   * 指定发票抬头是个人还是企业，默认为个人。
   *
   * 类型: OrderOfInvoiceEnum.PERSONAL
   */
  titleType = OrderOfInvoiceEnum.PERSONAL

  /**
   * 发票类型
   *
   * 指定发票的具体类型，默认为电子普通增值税发票。
   *
   * 类型: InvoiceTypeEnum.INVOICEELECTRONICORDINARYVAT
   */
  type = InvoiceTypeEnum.INVOICEELECTRONICORDINARYVAT

  /**
   * 发票抬头信息 -- 单位名称
   *
   * 如果发票抬头为企业，则需要填写单位名称。
   *
   * 类型: string
   */
  unitName: string

  /**
   * 统一社会信用代码
   *
   * 企业的统一社会信用代码，用于识别企业身份。
   *
   * 类型: string
   */
  code: string

  /**
   * 注册地址
   *
   * 企业的注册地址信息。
   *
   * 类型: string
   */
  regurl: string

  /**
   * 注册电话
   *
   * 企业的注册联系电话。
   *
   * 类型: string
   */
  rePhone: string

  /**
   * 开户银行
   *
   * 企业开户的银行名称。
   *
   * 类型: string
   */
  bank: string

  /**
   * 银行账户
   *
   * 企业在开户银行中的账户号码。
   *
   * 类型: string
   */
  bankAccount: string

  /**
   * 手机号码
   *
   * 联系人的手机号码。
   *
   * 类型: string
   */
  phone: string

  /**
   * 电子邮箱
   *
   * 联系人的电子邮件地址。
   *
   * 类型: string
   */
  email: string

  /**
   * 营业执照URL
   *
   * 企业的营业执照图片链接列表。
   *
   * 类型: Array<any>
   */
  charterUrl: Array<any>

  /**
   * 开户许可证URL
   *
   * 企业的开户许可证图片链接列表。
   *
   * 类型: Array<any>
   */
  accountLicenceUrl: Array<any>

  /**
   * 发票票面备注
   *
   * 发票票面上的备注信息。
   *
   * 类型: string
   */
  remark: string

  /**
   * 地区备注
   *
   * 配送地址的地区备注信息。
   *
   * 类型: string
   */
  deliveryAddressRemark: string

  /**
   * 配送配置标识
   *
   * 用于指定配送方式或逻辑。
   *
   * 类型: number
   */
  distributionConfig: number

  /**
   * 收件人姓名
   *
   * 表示包裹接收人的名称。
   *
   * 类型: string
   */
  consignee: string

  /**
   * 配送地址电话
   *
   * 收件人的联系电话。
   *
   * 类型: string
   */
  deliveryAddressPhone: string

  /**
   * 地区信息
   *
   * 表示配送地址所属的区域。
   *
   * 类型: string
   */
  region: string

  /**
   * 详细地址
   *
   * 具体到门牌号的收货地址。
   *
   * 类型: string
   */
  address: string

  /**
   * 自提地点
   *
   * 用户选择的取货位置。
   *
   * 类型: string
   */
  pickupLocation: string

  /**
   * 取货时间
   *
   * 用户预约或指定的取货时间。
   *
   * 类型: string
   */
  pickupTime: string

  /**
   * 取货点备注（可选）
   *
   * 用于补充说明取货点的相关信息。
   *
   * 类型: string | undefined
   */
  takePointRemark?: string
}
