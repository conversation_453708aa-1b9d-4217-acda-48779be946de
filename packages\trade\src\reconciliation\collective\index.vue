<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2024-09-11 19:07:04
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2024-12-26 16:02:12
-->
<route-meta>
{
"isMenu": true,
"title": "集体报名对账",
"sort": 2,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="@OrderReconciliation,@RefundReconciliation">
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky" @tab-click="handleClick">
      <template
        v-if="$hasPermission('orderReconciliation,orderReconciliationfx,orderReconciliationzt')"
        desc="orderReconciliation:退款订单对账,orderReconciliationfx:分销退款订单对账,orderReconciliationzt:专题退款订单对账"
        actions="orderReconciliation:@OrderReconciliation#orderReconciliationfx:@OrderReconciliation#orderReconciliationzt:@OrderReconciliation"
      >
        <el-tab-pane label="报名订单对账" name="order-reconciliation">
          <order-reconciliation ref="orderRef"></order-reconciliation>
        </el-tab-pane>
      </template>
      <template
        v-if="$hasPermission('refundReconciliation,refundReconciliationfx,refundReconciliationzt')"
        desc="refundReconciliation:退款订单对账,refundReconciliationfx:分销退款订单对账,refundReconciliationzt:专题退款订单对账"
        actions="refundReconciliation:@RefundReconciliation#refundReconciliationfx:@RefundReconciliation#refundReconciliationzt:@RefundReconciliation"
      >
        <el-tab-pane label="退款订单对账" name="refund-reconciliation">
          <refund-reconciliation ref="refundRef"></refund-reconciliation>
        </el-tab-pane>
      </template>
    </el-tabs>
  </el-main>
</template>
<script lang="ts">
  import OrderReconciliation from '@hbfe/jxjy-admin-trade/src/reconciliation/collective/components/order-reconciliation.vue'
  import RefundReconciliation from '@hbfe/jxjy-admin-trade/src/reconciliation/collective/components/refund-reconciliation.vue'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  @Component({
    components: { OrderReconciliation, RefundReconciliation }
  })
  export default class extends Vue {
    @Ref('orderRef') orderRef: any
    @Ref('refundRef') refundRef: any
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable

    activeName = 'order-reconciliation'
    async handleClick(val: any) {
      if (val.name === 'order-reconciliation') {
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.orderRef.doSearchfx()
        } else if (this.isZtlogin) {
          await this.orderRef.doSearchzt()
        } else {
          await this.orderRef.doSearch()
        }
      } else {
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.refundRef.doSearchfx()
        } else if (this.isZtlogin) {
          await this.refundRef.doSearchZt()
        } else {
          await this.refundRef.doSearch()
        }
      }
    }
  }
</script>
