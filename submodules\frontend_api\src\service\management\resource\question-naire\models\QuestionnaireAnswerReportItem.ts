import { QuestionAnswerResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'
export default class QuestionnaireAnswerReportItem {
  /**
   * 姓名
   */
  name = ''
  /**
   * 身份证
   */
  idCard = ''
  /**
   * 手机号
   */
  phone = ''
  /**
   * 提交时间
   */
  submitTime = ''
  /**
   * 答卷id
   */
  answerPaperId = ''
  /**
   * 期别id
   */
  issueId = ''
  static from(dto: QuestionAnswerResponse) {
    const vo = new QuestionnaireAnswerReportItem()
    vo.answerPaperId = dto.answerPaperId
    vo.idCard = dto.idCard
    vo.phone = dto.phoneNum
    vo.submitTime = dto.submitTime
    vo.name = dto.name
    vo.issueId = dto.issueId
    return vo
  }
}
