<template>
  <el-drawer title="修改发票信息" :visible.sync="show" size="900px" custom-class="m-drawer">
    <div class="drawer-bd">
      <div class="m-tit">
        <span class="tit-txt">原发票信息</span>
      </div>
      <el-form ref="elForm" :model="InvoiceObject" :rules="rules" label-width="170px" class="m-text-form f-mt10">
        <el-col :span="12">
          <el-form-item label="发票类型：">增值税专用发票</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发票抬头：">【单位】福建华博教育科技股份有限公司</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="统一社会信用代码：">35212544125658956L</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户银行：">福建建设银行</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户帐号：">215421512542152</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册电话：">8312008</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册地址：">工业路611号</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册地址：">工业路611号</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="统一社会信用代码证：">
            <el-image
              src="/assets/images/web-default-banner.jpg"
              :preview-src-list="['/assets/images/web-default-banner.jpg']"
              class="course-pic is-small"
            >
            </el-image>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户许可证：">
            <el-image
              src="/assets/images/web-default-banner.jpg"
              :preview-src-list="['/assets/images/web-default-banner.jpg']"
              class="course-pic is-small"
            >
            </el-image>
          </el-form-item>
        </el-col>
      </el-form>
      <div class="m-sub-tit">
        <span class="tit-txt">配送信息</span>
      </div>
      <el-form label-width="150px" class="m-text-form f-mt10">
        <el-col :span="12">
          <el-form-item label="配送方式：">自取</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收货人：">林林一</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号：">13003831002</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件号：">350121199112025689</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="自取地址：">工业路611号</el-form-item>
        </el-col>
      </el-form>
      <el-divider class="m-divider"></el-divider>
      <div class="m-tit">
        <span class="tit-txt">修改的发票信息</span>
      </div>
      <el-form ref="elForm" :model="editInvoiceObject" :rules="editRules" label-width="170px" class="m-form f-mt10">
        <el-form-item label="发票类型：" class="is-text">增值税专用发票</el-form-item>
        <el-form-item label="发票抬头：">
          <el-radio-group v-model="editInvoiceObject.distributionMode">
            <el-radio label="1">个人</el-radio>
            <el-radio label="2">单位</el-radio>
          </el-radio-group>
          <div class="f-mt10">
            <!--选择单位时改为 请输入单位名称-->
            <el-input clearable placeholder="请输入个人名字" class="form-l" />
          </div>
        </el-form-item>
        <el-form-item label="统一社会信用代码：" required>
          <el-input clearable placeholder="请输入18位统一社会信用代码" class="form-l" />
        </el-form-item>
        <el-form-item label=" " class="is-text">
          <span class="f-co">注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span>
        </el-form-item>
        <el-form-item label="开户银行：" prop="bank">
          <el-input clearable placeholder="请输入开户银行" class="form-l" v-model="editInvoiceObject.bank" />
        </el-form-item>
        <el-form-item label="开户帐号：" prop="account">
          <el-input clearable placeholder="请输入开户帐号" class="form-l" v-model="editInvoiceObject.account" />
        </el-form-item>
        <el-form-item label="注册电话：" prop="phone">
          <el-input clearable placeholder="请输入单位注册电话" class="form-m" v-model="editInvoiceObject.phone" />
        </el-form-item>
        <el-form-item label="注册地址：" required>
          <el-input clearable placeholder="请输入单位注册地址" />
        </el-form-item>
        <el-form-item label="统一社会信用代码证：">
          <el-upload
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            class="m-pic-upload proportion-pic is-small"
          >
            <div slot="default" class="upload-placeholder">
              <i class="el-icon-plus"></i>
              <p class="txt">上传图片</p>
            </div>
            <div slot="file" slot-scope="{ file }" class="img-file">
              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
              <div class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                  <i class="el-icon-delete"></i>
                </span>
              </div>
            </div>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
            <img :src="dialogImageUrl" alt="" />
          </el-dialog>
        </el-form-item>
        <el-form-item label="开户许可证：">
          <el-upload
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            class="m-pic-upload proportion-pic is-small"
          >
            <div slot="default" class="upload-placeholder">
              <i class="el-icon-plus"></i>
              <p class="txt">上传图片</p>
            </div>
            <div slot="file" slot-scope="{ file }" class="img-file">
              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
              <div class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                  <i class="el-icon-delete"></i>
                </span>
              </div>
            </div>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
            <img :src="dialogImageUrl" alt="" />
          </el-dialog>
        </el-form-item>
      </el-form>
      <div class="m-sub-tit">
        <span class="tit-txt">配送信息</span>
      </div>
      <el-form ref="elForm" :model="InvoiceObject" :rules="rules" label-width="150px" class="m-form f-mt10">
        <el-form-item label="配送方式：">
          <el-radio-group v-model="InvoiceObject.resource">
            <el-radio label="1">快递</el-radio>
            <el-radio label="2">自取</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="收货人：" prop="addressName">
          <el-input clearable placeholder="请输入收货人" v-model="InvoiceObject.addressName" class="form-s" />
        </el-form-item>
        <el-form-item label="手机号：" prop="phone">
          <el-input clearable placeholder="请输入手机号" v-model="InvoiceObject.phone" class="form-m" />
        </el-form-item>
        <el-form-item label="所在地区：" prop="area" v-if="InvoiceObject.resource != 2">
          <el-cascader clearable placeholder="请选择所在地区" v-model="InvoiceObject.area" class="form-m" />
        </el-form-item>
        <el-form-item label="详细地址：" prop="address" v-if="InvoiceObject.resource != 2">
          <el-input clearable placeholder="请输入详细地址" v-model="InvoiceObject.address" />
        </el-form-item>
        <el-form-item label="邮编：" prop="email" v-if="InvoiceObject.resource != 2">
          <el-input clearable placeholder="请输入邮编" class="form-s" />
        </el-form-item>
        <!-- <el-form-item label="姓名：" required>
          <el-input clearable placeholder="请输入姓名" class="form-s" />
        </el-form-item>
        <el-form-item label="手机号：" required>
          <el-input clearable placeholder="请输入手机号" class="form-m" />
        </el-form-item> -->
        <el-form-item label="证件号：" prop="idCard" v-if="InvoiceObject.resource == 2">
          <el-input clearable placeholder="请输入证件号" class="form-m" />
        </el-form-item>
        <el-form-item label="自取地点：" class="is-text"> 福建省福州市鼓楼区工业路611号 </el-form-item>
      </el-form>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="close()">取消</el-button>
      <el-button type="primary" @click="save()">保存发票信息</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
  import { ElForm } from 'element-ui/types/form'

  import ResourceModule from '@api/service/management/resource/ResourceModule'

  @Component
  export default class extends Vue {
    @Ref('elForm') elForm: ElForm

    /**
     * 外部传过来打开关闭
     */
    @Prop({
      required: true
    })
    visible: boolean

    InvoiceObject = {
      type: '增值税电子普通发票111',
      name: '【个人】林林一',
      resource: '1',
      resourceName: '',
      code: '',
      phone: '',
      email: '',
      addressName: '',
      address: '',
      area: '',
      idCard: '',
      distributionMode: '1'
    }
    editInvoiceObject = {
      name: '【个人】林林一',
      code: '',
      bank: '',
      account: '',
      phone: ''
    }
    dialogVisible = false
    dialogImageUrl = ''

    rules = {
      resourceName: [
        {
          required: true,
          message: '请输入抬头',
          trigger: 'blur'
        }
      ],
      code: [
        {
          required: true,
          message: '请输入统一社会信用代码'
        },
        {
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            // this.heckCode(rule, value, callback)
            callback()
          },
          trigger: 'blur'
        }
      ],
      phone: [
        {
          required: true,
          min: 11,
          message: '请输入11位手机号码',
          trigger: 'blur'
        },
        {
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            if (/^1[3456789]\d{9}$/.test(value)) {
              callback()
            } else {
              callback(new Error('手机格式错误'))
            }
          }
        }
      ],
      email: [
        {
          required: true,
          message: '请输入电子邮箱'
        },
        {
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            if (/^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
              callback()
            } else {
              callback(new Error('电子邮箱错误'))
            }
          }
        }
      ],
      addressName: [
        {
          required: true,
          message: '请输入收货人',
          trigger: 'blur'
        }
      ],
      area: [
        {
          required: true,
          message: '请选择所在地区',
          trigger: 'change'
        }
      ],
      address: [
        {
          required: true,
          message: '请输入详细地址',
          trigger: 'blur'
        }
      ],
      idCard: [
        {
          required: true,
          message: '请输入证件号',
          trigger: 'blur'
        }
      ]
    }
    editRules = {
      name: [
        {
          required: true,
          message: '请输入抬头',
          trigger: 'blur'
        }
      ],
      code: [
        {
          required: true,
          message: '请输入统一社会信用代码'
        },
        {
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            // this.heckCode(rule, value, callback)
            callback()
          },
          trigger: 'blur'
        }
      ],
      phone: [
        {
          required: true,
          min: 11,
          message: '请输入11位手机号码',
          trigger: 'blur'
        },
        {
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            if (/^1[3456789]\d{9}$/.test(value)) {
              callback()
            } else {
              callback(new Error('手机格式错误'))
            }
          }
        }
      ],
      bank: [
        {
          required: true,
          message: '请输入开户银行',
          trigger: 'blur'
        }
      ],
      account: [
        {
          required: true,
          message: '请输入开户帐号',
          trigger: 'blur'
        }
      ],
      address: [
        {
          required: true,
          message: '请输入单位注册地址',
          trigger: 'blur'
        }
      ]
    }
    //统一社会信用代码校验
    heckCode(rule: any, value: number, callback: (error?: Error) => void) {
      {
        if (!Number.isInteger(value)) {
          callback(new Error('请输入数字值'))
        } else {
          callback()
        }
      }
    }
    get show() {
      //该抽屉的显隐
      return this.visible
    }
    set show(val: boolean) {
      this.$emit('update:visible', val)
    }
    close() {
      this.show = false
    }
    save() {
      this.elForm.validate((val) => {
        // 11
      })
      this.show = false
    }
    handleRemove(file: any, fileList: any) {
      console.log(file, fileList)
    }
    handlePictureCardPreview(file: any) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
  }
</script>
