import {
  UpdateOnlineSchoolSubAdminByTokenRequest,
  UpdateOnlineSchoolSubAdminRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { UpdateDistributorAdminRequest } from '@api/platform-gateway/platform-jxjy-distributor-admin-v1'
import Context from '@api/service/common/context/Context'

class UpdateOnlineSchoolSubAdminRequestVo extends UpdateOnlineSchoolSubAdminRequest {
  /**
   * 角色id集合
   */
  originalRoleIds: string[] = []
  /**
   * 角色id集合
   */
  roleIds: string[] = []

  static to(params: UpdateOnlineSchoolSubAdminRequestVo) {
    const vo = new UpdateOnlineSchoolSubAdminRequest()
    vo.accountId = params?.accountId
    vo.identity = null
    vo.name = params?.name
    vo.gender = params?.gender
    vo.phone = params?.phone
    vo.email = params?.email
    vo.status = params?.status
    vo.addRoleIds = []
    vo.removeRoleIds = []
    if (params?.roleIds && params?.roleIds?.length) {
      params.roleIds.map(res => {
        if (!params.originalRoleIds.includes(res)) {
          vo.addRoleIds.push(res)
        }
      })
      vo.addRoleIds = Array.from(new Set(vo.addRoleIds))
    }
    if (params?.originalRoleIds && params?.originalRoleIds?.length) {
      params.originalRoleIds.map(res => {
        if (!params.roleIds.includes(res)) {
          vo.removeRoleIds.push(res)
        }
      })
      vo.removeRoleIds = Array.from(new Set(vo.removeRoleIds))
    }
    return vo
  }
  static toV2(params: UpdateOnlineSchoolSubAdminRequestVo) {
    const vo = new UpdateOnlineSchoolSubAdminByTokenRequest()
    vo.accountId = params?.accountId
    vo.identity = null
    vo.name = params?.name
    vo.gender = params?.gender
    vo.phone = params?.phone
    vo.email = params?.email
    vo.status = params?.status
    vo.addRoleIds = []
    vo.removeRoleIds = []
    if (params?.roleIds && params?.roleIds?.length) {
      params.roleIds.map(res => {
        if (!params.originalRoleIds.includes(res)) {
          vo.addRoleIds.push(res)
        }
      })
      vo.addRoleIds = Array.from(new Set(vo.addRoleIds))
    }
    if (params?.originalRoleIds && params?.originalRoleIds?.length) {
      params.originalRoleIds.map(res => {
        if (!params.roleIds.includes(res)) {
          vo.removeRoleIds.push(res)
        }
      })
      vo.removeRoleIds = Array.from(new Set(vo.removeRoleIds))
    }
    return vo
  }

  static toUpdateDistributorAdminRequest(params: UpdateOnlineSchoolSubAdminRequestVo) {
    const vo = new UpdateDistributorAdminRequest()
    vo.onlineSchoolId = Context.businessEnvironment.serviceToken?.tokenMeta?.servicerId
    vo.accountId = params?.accountId
    vo.identity = null
    vo.name = params?.name
    vo.gender = params?.gender
    vo.phone = params?.phone
    vo.email = params?.email
    vo.status = params?.status
    vo.addRoleIds = []
    vo.removeRoleIds = []
    if (params?.roleIds && params?.roleIds?.length) {
      params.roleIds.map(res => {
        if (!params.originalRoleIds.includes(res)) {
          vo.addRoleIds.push(res)
        }
      })
      vo.addRoleIds = Array.from(new Set(vo.addRoleIds))
    }
    if (params?.originalRoleIds && params?.originalRoleIds?.length) {
      params.originalRoleIds.map(res => {
        if (!params.roleIds.includes(res)) {
          vo.removeRoleIds.push(res)
        }
      })
      vo.removeRoleIds = Array.from(new Set(vo.removeRoleIds))
    }
    return vo
  }
}

export default UpdateOnlineSchoolSubAdminRequestVo
