import {
  SchoolTrainingPropertyQueryRequest,
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import BasicDataGateway from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

import { listIndustryPropertyByOnlineSchoolV2 } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage/graphql-importer'
import Basicdata, {
  BusinessDataDictionaryResponse,
  RegionCodeRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { getRegionInSubProject } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage/graphql-importer'
import { SchemeSkuPropertyResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import QueryTechnologyLevel from '@api/service/common/basic-data-dictionary/query/QueryTechnologyLevel'
import Context from '@api/service/common/context/Context'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import SkuVo from '../query/vo/SkuVo'
import { CommoditySkuPropertyResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

/**
 * sku查询工具
 */
class SkuPropertyConvertUtilsV2 {
  /**
   * 地区map结构
   */
  resigonMap = new Map<string, SkuVo>()
  /**
   * 地区数组结构
   */
  resigonArr = new Array<string>()
  /**
   * 行业map结构
   */
  industryMap = new Map<string, SkuVo>()
  /**
   * 行业数组结构
   */
  industryArr = new Array<string>()
  /**
   * 技术等级map结构
   */
  technologyLevelMap = new Map<string, SkuVo>()
  /**
   * 技术等级数组结构
   */
  technologyLevelArr = new Array<string>()
  /**
   * 根据行业属性挂钩的Map结构
   * 科目类型 培训类别 培训专业 卫生行业-培训对象 卫生行业-岗位类别 工勤行业-技术等级 学段 学科

   */
  industryChildrenMap = new Map<string, Map<string, SkuVo>>()
  async queryAllSku(source: (SchemeSkuPropertyResponse | CommoditySkuPropertyResponse)[]) {
    // 查询地区
    const regions = this.filterArr(
      source.map((item: SchemeSkuPropertyResponse | CommoditySkuPropertyResponse) => [
        item.province?.skuPropertyValueId,
        item.city?.skuPropertyValueId,
        item.county?.skuPropertyValueId
      ])
    ).filter(item => !this.resigonArr.includes(item) && item)
    if (regions.length > 0) {
      await this.queryRegion(regions)
    }
    // 查询行业
    const industryArr = source
      .map((item: SchemeSkuPropertyResponse | CommoditySkuPropertyResponse) => item.industry?.skuPropertyValueId)
      .filter(item => item)
    if (industryArr.length > 0) {
      const industryRes = await QueryIndustry.getIndustryDICT()
      industryRes.forEach(item => {
        const skuVo = new SkuVo()
        skuVo.skuPropertyValueId = item.id
        skuVo.skuPropertyName = item.name
        this.industryMap.set(item.id, skuVo)
        this.industryArr.push(item.id)
      })
    }
    // 查询技术等级
    const technicalGradeArr = source
      .map((item: SchemeSkuPropertyResponse | CommoditySkuPropertyResponse) => item.technicalGrade?.skuPropertyValueId)
      .filter(item => item)
    if (technicalGradeArr.length > 0) {
      const queryTechnologyLevel = await QueryTechnologyLevel.query()
      queryTechnologyLevel.forEach(item => {
        const skuVo = new SkuVo()
        skuVo.skuPropertyValueId = item.id
        skuVo.skuPropertyName = item.showName
        this.technologyLevelMap.set(item.id, skuVo)
        this.technologyLevelArr.push(item.id)
      })
    }
    // 查询行业挂钩的属性    以行业ID为key 存 map<string,SkuVo>值
    const industryChildren: {
      industryId: string
      propertyId: string
    }[] = []
    source.map((item: SchemeSkuPropertyResponse | CommoditySkuPropertyResponse) => {
      if (item.industry) {
        if (item.subjectType) {
          // 科目类型
          industryChildren.push({
            industryId: item.industry.skuPropertyValueId,
            propertyId: item.subjectType.skuPropertyValueId
          })
        }
        if (item.trainingCategory) {
          // 培训类别
          industryChildren.push({
            industryId: item.industry.skuPropertyValueId,
            propertyId: item.trainingCategory.skuPropertyValueId
          })
        }
        if (item.trainingProfessional) {
          // 培训专业
          industryChildren.push({
            industryId: item.industry.skuPropertyValueId,
            propertyId: item.trainingProfessional.skuPropertyValueId
          })
        }
        if (item.trainingObject) {
          //  卫生行业-培训对象
          industryChildren.push({
            industryId: item.industry.skuPropertyValueId,
            propertyId: item.trainingObject.skuPropertyValueId
          })
        }
        if (item.positionCategory) {
          //  卫生行业-岗位类别
          industryChildren.push({
            industryId: item.industry.skuPropertyValueId,
            propertyId: item.positionCategory.skuPropertyValueId
          })
        }
        if (item.jobLevel) {
          // 工勤行业-技术等级
          industryChildren.push({
            industryId: item.industry.skuPropertyValueId,
            propertyId: item.jobLevel.skuPropertyValueId
          })
        }
        if (item.learningPhase) {
          // 学段
          industryChildren.push({
            industryId: item.industry.skuPropertyValueId,
            propertyId: item.learningPhase.skuPropertyValueId
          })
        }
        if (item.discipline) {
          // 学科
          industryChildren.push({
            industryId: item.industry.skuPropertyValueId,
            propertyId: item.discipline.skuPropertyValueId
          })
        }
        if (item.certificatesType) {
          // 证书类型
          industryChildren.push({
            industryId: item.industry.skuPropertyValueId,
            propertyId: item.certificatesType.skuPropertyValueId
          })
        }
        if (item.practitionerCategory) {
          // 执业类别
          industryChildren.push({
            industryId: item.industry.skuPropertyValueId,
            propertyId: item.practitionerCategory.skuPropertyValueId
          })
        }
      }
    })
    const industryChildrenParam: { industryId: string; propertyId: string }[] = []
    industryChildren.forEach(item => {
      if (!item.industryId || !item.propertyId) return
      const subMap = this.industryChildrenMap.get(item.industryId)

      if (subMap) {
        if (!subMap.get(item.propertyId)) {
          // 存
          industryChildrenParam.push(item)
        }
      } else {
        // 存
        industryChildrenParam.push(item)
      }
    })
    if (industryChildrenParam.length > 0) {
      const paramMap = new Map<string, string[]>()
      industryChildren.forEach(item => {
        const temp = paramMap.get(item.industryId)
        if (temp) {
          paramMap.set(item.industryId, [...temp, item.propertyId])
        } else {
          paramMap.set(item.industryId, [item.propertyId])
        }
      })
      const param = new Array<SchoolTrainingPropertyQueryRequest>()
      paramMap.forEach((item, value) => {
        const schoolTrainingPropertyQueryRequest = new SchoolTrainingPropertyQueryRequest()
        schoolTrainingPropertyQueryRequest.propertyId = [...new Set(item)]
        schoolTrainingPropertyQueryRequest.industryId = value
        schoolTrainingPropertyQueryRequest.schoolId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
        param.push(schoolTrainingPropertyQueryRequest)
      })
      const rew = new RewriteGraph<Array<TrainingPropertyResponse>, SchoolTrainingPropertyQueryRequest>(
        BasicDataGateway._commonQuery,
        listIndustryPropertyByOnlineSchoolV2
      )
      await rew.request(param, 'industryId')
      ;[...rew.itemMap.keys()].forEach(item => {
        //
        const cache = this.industryChildrenMap.get(item)
        if (cache) {
          rew.itemMap.get(item)?.forEach(item => {
            const skuVo = new SkuVo()
            skuVo.skuPropertyValueId = item.propertyId
            skuVo.skuPropertyName = item.name
            skuVo.parentId = item.parentId
            cache.set(item.propertyId, skuVo)
          })
          this.industryChildrenMap.set(item, cache)
        } else {
          const newCache = new Map<string, SkuVo>()
          rew.itemMap.get(item)?.forEach(item => {
            const skuVo = new SkuVo()
            skuVo.skuPropertyValueId = item.propertyId
            skuVo.skuPropertyName = item.name
            skuVo.parentId = item.parentId
            newCache.set(item.propertyId, skuVo)
          })
          this.industryChildrenMap.set(item, newCache)
        }
      })
    }
    // 查询培训类别
  }
  private filterArr(ids: string[][] | string[]) {
    return [...new Set(ids.flat().filter(item => item !== ''))]
  }
  /**
   * 查询地区
   */
  private async queryRegion(ids: string[]) {
    const params = new Array<RegionCodeRequest>()
    ids.forEach(id => {
      const param = new RegionCodeRequest()
      param.code = id
      param.businessId = 'PLATFORM_BUSINESS_REGION'
      params.push(param)
    })
    const request = new RewriteGraph<BusinessDataDictionaryResponse, RegionCodeRequest>(
      Basicdata._commonQuery,
      getRegionInSubProject
    )
    await request.request(params, 'code')
    ids.forEach(item => {
      const skuVo = new SkuVo()
      const temp = request.itemMap.get(item)
      if (temp) {
        skuVo.skuPropertyValueId = item
        skuVo.skuPropertyName = temp.name
        skuVo.parentId = temp.parentId
        this.resigonMap.set(item, skuVo)
        this.resigonArr.push(item)
      }
    })
  }
}

export default new SkuPropertyConvertUtilsV2()
