<template>
  <div class="f-p15">
    <el-tabs v-model="activeName" @tab-click="handleClick" type="card" class="m-tab-card">
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="electronic-invoice-auto" :lazy="true">
        <electronic-invoice-auto :user-id="userId" ref="electronic-invoice-auto"></electronic-invoice-auto>
      </el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（线下开票）" name="electronic-invoice-offline" :lazy="true">
        <electronic-invoice-offline :user-id="userId" ref="electronic-invoice-offline"></electronic-invoice-offline>
      </el-tab-pane>
      <el-tab-pane label="增值税电子专用发票（线下开票）" name="electronic-special-offline" :lazy="true">
        <ElectronicSpecialInvoiceOffline :user-id="userId"></ElectronicSpecialInvoiceOffline>
      </el-tab-pane>
      <el-tab-pane label="增值税专用发票（纸质票）" name="third" :lazy="true">
        <special-invoice :user-id="userId"></special-invoice>
      </el-tab-pane>
      <el-tab-pane label="发票配送" name="fourth" :lazy="true">
        <invoice-distribution :user-id="userId" ref="invoice-distribution"></invoice-distribution>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import ElectronicInvoiceAuto from '@hbfe/jxjy-admin-customerService/src/collective/components/electronic-invoice-auto.vue'
  import ElectronicInvoiceOffline from '@hbfe/jxjy-admin-customerService/src/collective/components/electronic-invoice-offline.vue'
  import SpecialInvoice from '@hbfe/jxjy-admin-customerService/src/collective/components/special-invoice.vue'
  import ElectronicSpecialInvoiceOffline from '@hbfe/jxjy-admin-customerService/src/collective/components/electonic-special-invoice-offline.vue'
  import InvoiceDistribution from '@hbfe/jxjy-admin-customerService/src/collective/components/invoice-distribution.vue'
  @Component({
    components: {
      ElectronicInvoiceAuto,
      ElectronicInvoiceOffline,
      SpecialInvoice,
      InvoiceDistribution,
      ElectronicSpecialInvoiceOffline
    }
  })
  export default class extends Vue {
    @Ref('electronic-invoice-auto') electronicInvoiceAuto: ElectronicInvoiceAuto
    @Ref('electronic-invoice-offline') electronicInvoiceOffline: ElectronicInvoiceOffline
    @Ref('invoice-distribution') invoiceDistribution: InvoiceDistribution

    @Prop({
      type: String,
      default: ''
    })
    userId: string

    activeName = 'electronic-invoice-auto'
    async handleClick() {
      if (this.activeName === 'electronic-invoice-auto') {
        this.$nextTick(async () => {
          await this.electronicInvoiceAuto.doSearch()
        })
        //todo
      } else if (this.activeName === 'electronic-invoice-offline') {
        this.$nextTick(async () => {
          await this.electronicInvoiceOffline.doSearch()
        })
      }
    }
  }
</script>
