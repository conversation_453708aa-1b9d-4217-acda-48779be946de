import SubjectType from '@api/service/customer/personal-leaning/query/vo/enum/SubjectTypeEnum'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import { HistoryStudentTrainingInfoResponse } from '@api/platform-gateway/platform-student-scheme-learning-query-front-gateway'
import TrainingContentVo from '@api/service/customer/personal-leaning/query/vo/TrainingContentVo'
import TrainPlatformInfo from '@api/service/customer/personal-leaning/query/vo/TrainPlatformInfo'

class HistoryTrainingArchivesVo {
  /**
   * 历史培训证明id
   */
  historyCertificateId = ''
  /**
   * 用户名
   */
  userName = ''
  /**
   * 身份证号
   */
  idCard = ''
  /**
   * 培训年度
   */
  trainYear = ''
  /**
   * 科目类型: 0公需、1专业、2公需+专业
   */
  subjectType = new SubjectType()
  /**
   * 学时，类型为number
   */
  period = 0
  /**
   *培训班名称，类型为string
   */
  trainClassName = ''
  /**
   * 培训班图片
   */
  picture = ''
  /**
   * 单位名称
   */
  unitName = ''
  /**
   * 平台信息
   */
  trainPlatformInfo = new TrainPlatformInfo()
  /**
   * 培训内容集合（课程集合）
   */
  trainingContentList: TrainingContentVo[] = []
  /**
   * 是否合格
   */
  isQualified = false
  // 模型转换-列表
  static fromList(source: HistoryStudentTrainingInfoResponse[]) {
    if (!source || !source.length) return []
    return source.map(item => {
      return HistoryTrainingArchivesVo.fromDetail(item)
    })
  }
  // 模型转换-详情
  static fromDetail(source: HistoryStudentTrainingInfoResponse) {
    const detailVo = new HistoryTrainingArchivesVo()
    if (!source) return detailVo
    // 读取阿波罗配置，获取旧平台链接
    const historyTrainingArchivesTrainPlatformJson = ConfigCenterModule.getFrontendApplication(
      frontendApplication.historyTrainingArchivesTrainPlatformInfo
    )
    const historyTrainingArchivesTrainPlatformInfo = historyTrainingArchivesTrainPlatformJson
      ? JSON.parse(historyTrainingArchivesTrainPlatformJson)
      : {}
    detailVo.historyCertificateId = source.id
    detailVo.userName = source.userName
    detailVo.idCard = source.userIdCard
    detailVo.trainYear = source.trainYear
    detailVo.trainClassName = source.trainName
    detailVo.subjectType.current = source.subjectType
    detailVo.period = source.trainPeriod
    detailVo.unitName = source.unitName
    detailVo.trainPlatformInfo.trainPlatformKey = source.trainPlatformKey
    if (source.trainPlatformKey) {
      detailVo.trainPlatformInfo.trainPlatformName =
        historyTrainingArchivesTrainPlatformInfo[source.trainPlatformKey]?.name
      detailVo.trainPlatformInfo.trainPlatformValue =
        historyTrainingArchivesTrainPlatformInfo[source.trainPlatformKey]?.value
    }
    detailVo.trainingContentList = source.trainingContentList?.map(val => {
      const course = new TrainingContentVo()
      Object.assign(course, val)
      return course
    })
    detailVo.isQualified = !!source.qualifiedTime
    return detailVo
  }
}
export default HistoryTrainingArchivesVo
