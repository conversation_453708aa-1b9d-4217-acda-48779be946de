<route-params content="/:id"></route-params>
<route-meta>
{
"title": "学员详情"
}
</route-meta>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/training/user/student')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/user/student' }">学员管理</el-breadcrumb-item>
      <el-breadcrumb-item>学员详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <div class="f-plr45 f-pt20 f-pb10">
          <el-row :gutter="16">
            <el-form :inline="true" label-width="auto" class="m-text-form">
              <el-col :sm="12" :md="8">
                <el-form-item label="姓名：">{{ UserDetail.userName }}</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="性别：">{{ UserDetail.genderName }}</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="证件号：">{{ UserDetail.idCard }}</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="手机号：">{{ UserDetail.phone }}</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="地区：">{{ getregion(UserDetail.companyRegion) }}</el-form-item>
              </el-col>
              <!-- <el-col :sm="12" :md="8">
                <el-form-item label="技术等级：">{{ UserDetail.professionalLevelName }}</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="技术等级工种：">{{ UserDetail.jobCategoryName }}</el-form-item>
              </el-col> -->
              <el-col :sm="12" :md="8">
                <el-form-item label="注册时间：">{{ UserDetail.createTime }}</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="注册来源：">{{ UserDetail.sourceTypeName }}</el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">行业信息</span>
        </div>
        <div class="f-pb20 f-pt10">
          <template v-if="UserDetail.rsStudentIndustryInfo && UserDetail.rsStudentIndustryInfo.industryId">
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">人社行业</span>
            </div>
            <div class="f-plr40">
              <el-row :gutter="16">
                <el-form :inline="true" label-width="auto" class="m-text-form f-mt10">
                  <el-col :sm="12" :md="8" v-if="firstProfessionalCategoryName">
                    <el-form-item label="专业类别："
                      >{{ UserDetail.rsStudentIndustryInfo.firstProfessionalCategoryName }}
                      {{
                        UserDetail.rsStudentIndustryInfo.secondProfessionalCategoryName
                          ? '- ' + UserDetail.rsStudentIndustryInfo.secondProfessionalCategoryName
                          : '--'
                      }}</el-form-item
                    >
                  </el-col>
                  <el-col :sm="12" :md="8" v-if="professionalQualification">
                    <el-form-item label="职称等级：">{{ professionalQualificationName }}</el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" v-if="!firstProfessionalCategoryName">
                    <el-form-item label="专业类别：">---- </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="职称等级：" v-if="!professionalQualification">--</el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </template>
          <template
            v-if="UserDetail.jsStudentIndustryInfo && UserDetail.jsStudentIndustryInfo.userCertificateList.length > 0"
          >
            <div class="m-sub-tit is-border-bottom f-mt10">
              <span class="tit-txt">建设行业</span>
            </div>
            <div
              class="f-plr40"
              v-for="(item, index) in UserDetail.jsStudentIndustryInfo.userCertificateList"
              :key="index"
            >
              <p class="f-cb f-mt20 f-f15">{{ item.certificateCategoryName }}</p>
              <el-table stripe max-height="500px" class="m-table f-mt10" :data="getlist(item)">
                <el-table-column label="专业" min-width="200" fixed="left">
                  <template slot-scope="scope">{{ scope.row.registerProfessionalName || '-' }}</template>
                </el-table-column>
                <el-table-column label="证书编号" width="200">
                  <template slot-scope="scope">{{ scope.row.certificateCode }}</template>
                </el-table-column>
                <el-table-column label="证书发放日期-证书有效期" min-width="200">
                  <template slot-scope="scope">{{
                    scope.row.releaseStartTime.slice(0, 10) + ' - ' + scope.row.certificateEndTime.slice(0, 10)
                  }}</template>
                </el-table-column>
                <el-table-column label="附件" width="400">
                  <template slot-scope="scope">
                    <ul class="m-certificate-img f-clear">
                      <li v-for="(img, index) in scope.row.attachmentInfoList" :key="index">
                        <el-image :src="img.url" class="img" :preview-src-list="getsrcList(img.url)"> </el-image>
                      </li>
                    </ul>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
          <template v-if="UserDetail.gqStudentIndustryInfo && UserDetail.gqStudentIndustryInfo.industryId">
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">工勤行业</span>
            </div>
            <div class="f-plr40">
              <el-row :gutter="16">
                <el-form :inline="true" label-width="auto" class="m-text-form f-mt10">
                  <el-col :sm="12" :md="8" v-if="UserDetail.gqStudentIndustryInfo.professionalLevelName">
                    <el-form-item label="技术等级："
                      >{{ UserDetail.gqStudentIndustryInfo.professionalLevelName }}
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" v-if="UserDetail.gqStudentIndustryInfo.jobCategoryName">
                    <el-form-item label="工种：">{{ UserDetail.gqStudentIndustryInfo.jobCategoryName }}</el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </template>
          <template v-if="UserDetail.wsStudentIndustryInfo && UserDetail.wsStudentIndustryInfo.industryId">
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">卫生行业</span>
            </div>
            <div class="f-plr40">
              <el-row :gutter="16">
                <el-form :inline="true" label-width="auto" class="m-text-form f-mt10">
                  <el-col :sm="12" :md="8" v-if="UserDetail.wsStudentIndustryInfo.personnelCategoryName">
                    <el-form-item label="人员类别："
                      >{{ UserDetail.wsStudentIndustryInfo.personnelCategoryName }}
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" v-if="UserDetail.wsStudentIndustryInfo.positionCategoryName">
                    <el-form-item label="岗位类别：">{{
                      UserDetail.wsStudentIndustryInfo.positionCategoryName
                    }}</el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </template>
          <template v-if="UserDetail.lsStudentIndustryInfo && UserDetail.lsStudentIndustryInfo.industryId">
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">教师行业</span>
            </div>
            <div class="f-plr40">
              <el-row :gutter="16">
                <el-form :inline="true" label-width="auto" class="m-text-form f-mt10">
                  <el-col :sm="12" :md="8" v-if="UserDetail.lsStudentIndustryInfo.sectionAndSubjectsName[0].section">
                    <el-form-item label="学段"
                      >{{ UserDetail.lsStudentIndustryInfo.sectionAndSubjectsName[0].section }}
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" v-if="UserDetail.lsStudentIndustryInfo.sectionAndSubjectsName[0].subjects">
                    <el-form-item label="学科">{{
                      UserDetail.lsStudentIndustryInfo.sectionAndSubjectsName[0].subjects
                    }}</el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </template>
        </div>
        <!-- <div class="m-no-date f-ptb30">
          <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
          <div class="date-bd">
            <p class="f-f15 f-c9">暂无数据</p>
          </div>
        </div> -->
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">培训记录</span>
        </div>
        <div class="f-p20">
          <!--表格-->
          <el-table stripe max-height="500px" class="m-table" :data="schemeTableData">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="培训方案" min-width="300">
              <template slot-scope="scope">{{ scope.row.basicInfo.schemeName }}</template>
            </el-table-column>
            <el-table-column label="报名时间" min-width="180">
              <template slot-scope="scope">{{ scope.row.basicInfo.openTime }}</template>
            </el-table-column>
            <el-table-column label="是否合格" min-width="120">
              <template slot-scope="scope">
                <div v-if="!scope.row.assessResult.isQualified">
                  <el-badge is-dot type="danger" class="badge-status">未合格</el-badge>
                </div>
                <div v-else>
                  <el-badge is-dot type="success" class="badge-status">合格</el-badge>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="考核通过时间" min-width="180">
              <template slot-scope="scope">{{
                scope.row.assessResult.isQualified ? scope.row.assessResult.qualifiedTime : ' '
              }}</template>
            </el-table-column>
            <el-table-column label="报名方式" min-width="140">
              <template slot-scope="scope">{{ getopenType(scope.row.basicInfo.openType) }}</template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination
            :total-size="totalSize"
            :page="trainClassPage"
            class="f-mt15 f-tr"
            @size-change="pageSizeChange"
            @current-change="currentPageChange"
          >
          </hb-pagination>
        </div>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import UserModule from '@api/service/management/user/UserModule'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import QueryBasicdataDictionaryFactory from '@api/service/common/basic-data-dictionary/QueryBasicdataDictionaryFactory'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
  import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
  import StudentTrainClassDetailVo from '@api/service/management/train-class/query/vo/StudentTrainClassDetailVo'
  import QueryStudentTrainClassListVo from '@api/service/management/train-class/query/vo/QueryStudentTrainClassListVo'
  import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
  import QueryStudentDetail from '@api/service/diff/management/zztt/user/student/QueryStudentDetail'
  @Component
  export default class extends Vue {
    queryStudentDetail = UserModule.queryUserFactory.queryStudentDetail
    queryStudentTrainClassObj = TrainClassManagerModule.queryTrainClassFactory.queryStudentTrainClass
    trainClassPage: UiPage = new UiPage()
    querySchemeParams = new QueryStudentTrainClassListVo()
    totalSize = 0
    /**
     * 行业列表
     */
    industryList: IndustryVo[] = new Array<IndustryVo>()
    /**
     * 接口查询
     */
    queryM: QueryBasicdataDictionaryFactory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
    UserDetail = new UserDetailVo()
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    page: UiPage = new UiPage()
    id = ''
    schemeTableData = new Array<StudentTrainClassDetailVo>()
    get firstProfessionalCategoryName() {
      return this.UserDetail.rsStudentIndustryInfo?.firstProfessionalCategoryName
    }
    get professionalQualification() {
      return this.UserDetail.rsStudentIndustryInfo?.professionalQualification
    }
    get professionalQualificationName() {
      return this.UserDetail.rsStudentIndustryInfo?.professionalQualificationName
    }

    async created() {
      await this.queryIndustryList()
      this.id = this.$route.params.id
      console.log(this.$route.params.id)
      const res = await new QueryStudentDetail(this.id).queryDetail()
      this.UserDetail = res.data
      await this.doQuery()
    }
    // 查询已报方案列表
    async doQuery() {
      try {
        this.querySchemeParams.userId = this.UserDetail.userId
        this.schemeTableData = await this.queryStudentTrainClassObj.queryStudentTrainClassList(
          this.trainClassPage,
          this.querySchemeParams
        )
        this.totalSize = this.trainClassPage.totalSize
        console.log(this.schemeTableData, 11)
      } catch (e) {
        this.$message.error('获取已报方案列表失败！')
      } finally {
        console.log('结束了')
      }
    }
    constructor() {
      super()
      this.trainClassPage = new UiPage(this.doQuery, this.doQuery)
    }
    async currentPageChange() {
      await this.doQuery()
    }
    getopenType(openType: number) {
      switch (openType) {
        case 1:
          return '学员自主报名'
        case 2:
          return '集体报名'
        case 3:
          return '管理员导入'
        default:
          return '无'
      }
    }
    getscope(scope: any) {
      console.log(scope)
    }
    getregion(area: any) {
      let region = ''
      if (area) {
        if (area.provinceName && area.cityName && area.countyName) {
          region = area.provinceName + '/' + area.cityName + '/' + area.countyName
        } else if (area.provinceName && area.cityName) {
          region = area.provinceName + '/' + area.cityName
        } else if (area.provinceName) {
          region = area.provinceName
        }
      }

      return region
    }
    /**
     * 查询行业列表
     */
    async queryIndustryList() {
      const res = await this.queryM.queryIndustry.queryIndustry()
      this.industryList = res.isSuccess() ? this.queryM.queryIndustry.industryList : ([] as IndustryVo[])
      console.log('industryList', this.industryList)
    }
    getlist(item: any) {
      const list = [] as any
      list.push(item)
      return list
    }
    getsrcList(url: string) {
      const list = [] as any
      list.push(url)
      return list
    }
    pageSizeChange() {
      //todo
    }
  }
</script>

<style scoped>
  .none {
    text-align: center;
  }
</style>
