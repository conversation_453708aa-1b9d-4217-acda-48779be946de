import AbstractEnum from '@api/service/common/enums/AbstractEnum'
// 定义专题类型
export enum SubjectType {
  region = 1,
  industry = 2,
  unit = 3
}

export default class ApproveStatusType extends AbstractEnum<SubjectType> {
  static enum = SubjectType
  constructor(status?: SubjectType) {
    super()
    this.current = status
    this.map.set(SubjectType.region, '地区')
    this.map.set(SubjectType.industry, '行业')
    this.map.set(SubjectType.unit, '单位')
  }
}
