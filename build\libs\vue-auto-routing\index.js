function Exam() {
  return import(/* webpackChunkName: "@/views/exam" */ '@/views/exam.vue')
}

function ExamIndex() {
  return import(/* webpackChunkName: "@/views/exam-index" */ '@/views/exam/index.vue')
}

function ExamAdd() {
  return import(/* webpackChunkName: "@/views/exam-add" */ '@/views/exam/add.vue')
}

function ExamType() {
  return import(/* webpackChunkName: "@/views/exam-type" */ '@/views/exam/type.vue')
}

function ExamIdIndex() {
  return import(/* webpackChunkName: "@/views/exam-id-index" */ '@/views/exam/_id/index.vue')
}

function ExamIdModify() {
  return import(/* webpackChunkName: "@/views/exam-id-modify" */ '@/views/exam/_id/modify.vue')
}

function ImportOpen() {
  return import(/* webpackChunkName: "@/views/import-open" */ '@/views/import-open.vue')
}

function ImportOpenIndex() {
  return import(/* webpackChunkName: "@/views/import-open-index" */ '@/views/import-open/index.vue')
}

function ImportOpenResult() {
  return import(/* webpackChunkName: "@/views/import-open-result" */ '@/views/import-open/result.vue')
}

function ImportOpenTask() {
  return import(/* webpackChunkName: "@/views/import-open-task" */ '@/views/import-open/task.vue')
}

function LearningContent() {
  return import(/* webpackChunkName: "@/views/learning-content" */ '@/views/learning-content.vue')
}

function LearningContentCourse() {
  return import(/* webpackChunkName: "@/views/learning-content-course" */ '@/views/learning-content/course.vue')
}

function LearningContentCourseIndex() {
  return import(
    /* webpackChunkName: "@/views/learning-content-course-index" */ '@/views/learning-content/course/index.vue'
  )
}

function LearningContentCourseAdd() {
  return import(/* webpackChunkName: "@/views/learning-content-course-add" */ '@/views/learning-content/course/add.vue')
}

function LearningContentCourseIdIndex() {
  return import(
    /* webpackChunkName: "@/views/learning-content-course-id-index" */ '@/views/learning-content/course/_id/index.vue'
  )
}

function LearningContentCourseIdModify() {
  return import(
    /* webpackChunkName: "@/views/learning-content-course-id-modify" */ '@/views/learning-content/course/_id/modify.vue'
  )
}

function LearningContentMedia() {
  return import(/* webpackChunkName: "@/views/learning-content-media" */ '@/views/learning-content/media.vue')
}

function LearningContentMediaIndex() {
  return import(
    /* webpackChunkName: "@/views/learning-content-media-index" */ '@/views/learning-content/media/index.vue'
  )
}

function LearningContentMediaAdd() {
  return import(/* webpackChunkName: "@/views/learning-content-media-add" */ '@/views/learning-content/media/add.vue')
}

function LearningContentMediaType() {
  return import(/* webpackChunkName: "@/views/learning-content-media-type" */ '@/views/learning-content/media/type.vue')
}

function LearningContentMediaIdIndex() {
  return import(
    /* webpackChunkName: "@/views/learning-content-media-id-index" */ '@/views/learning-content/media/_id/index.vue'
  )
}

function LearningContentMediaIdModify() {
  return import(
    /* webpackChunkName: "@/views/learning-content-media-id-modify" */ '@/views/learning-content/media/_id/modify.vue'
  )
}

function LearningContentPackage() {
  return import(/* webpackChunkName: "@/views/learning-content-package" */ '@/views/learning-content/package.vue')
}

function LearningContentPackageIndex() {
  return import(
    /* webpackChunkName: "@/views/learning-content-package-index" */ '@/views/learning-content/package/index.vue'
  )
}

function LearningContentPackageAdd() {
  return import(
    /* webpackChunkName: "@/views/learning-content-package-add" */ '@/views/learning-content/package/add.vue'
  )
}

function LearningContentPackageIdIndex() {
  return import(
    /* webpackChunkName: "@/views/learning-content-package-id-index" */ '@/views/learning-content/package/_id/index.vue'
  )
}

function LearningContentPackageIdModify() {
  return import(
    /* webpackChunkName: "@/views/learning-content-package-id-modify" */ '@/views/learning-content/package/_id/modify.vue'
  )
}

function LearningScheme() {
  return import(/* webpackChunkName: "@/views/learning-scheme" */ '@/views/learning-scheme.vue')
}

function LearningSchemeIndex() {
  return import(/* webpackChunkName: "@/views/learning-scheme-index" */ '@/views/learning-scheme/index.vue')
}

function LearningSchemeAdd() {
  return import(/* webpackChunkName: "@/views/learning-scheme-add" */ '@/views/learning-scheme/add.vue')
}

function Organization() {
  return import(/* webpackChunkName: "@/views/organization" */ '@/views/organization.vue')
}

function OrganizationIndex() {
  return import(/* webpackChunkName: "@/views/organization-index" */ '@/views/organization/index.vue')
}

function OrganizationAdd() {
  return import(/* webpackChunkName: "@/views/organization-add" */ '@/views/organization/add.vue')
}

function OrganizationAdminIndex() {
  return import(/* webpackChunkName: "@/views/organization-admin-index" */ '@/views/organization/admin/index.vue')
}

function Outline() {
  return import(/* webpackChunkName: "@/views/outline" */ '@/views/outline.vue')
}

function OutlineIndex() {
  return import(/* webpackChunkName: "@/views/outline-index" */ '@/views/outline/index.vue')
}

function OutlineBatchAdd() {
  return import(/* webpackChunkName: "@/views/outline-batch-add" */ '@/views/outline/batch-add.vue')
}

function Question() {
  return import(/* webpackChunkName: "@/views/question" */ '@/views/question.vue')
}

function QuestionIndex() {
  return import(/* webpackChunkName: "@/views/question-index" */ '@/views/question/index.vue')
}

function QuestionAdd() {
  return import(/* webpackChunkName: "@/views/question-add" */ '@/views/question/add.vue')
}

function QuestionBatchAdd() {
  return import(/* webpackChunkName: "@/views/question-batch-add" */ '@/views/question/batch-add.vue')
}

function QuestionWrongIndex() {
  return import(/* webpackChunkName: "@/views/question-wrong-index" */ '@/views/question/wrong/index.vue')
}

function QuestionIdIndex() {
  return import(/* webpackChunkName: "@/views/question-id-index" */ '@/views/question/_id/index.vue')
}

function QuestionIdModify() {
  return import(/* webpackChunkName: "@/views/question-id-modify" */ '@/views/question/_id/modify.vue')
}

function Trade() {
  return import(/* webpackChunkName: "@/views/trade" */ '@/views/trade.vue')
}

function TradeOrderIndex() {
  return import(/* webpackChunkName: "@/views/trade-order-index" */ '@/views/trade/order/index.vue')
}

function TradeRefundIndex() {
  return import(/* webpackChunkName: "@/views/trade-refund-index" */ '@/views/trade/refund/index.vue')
}

function TradeVerifyIndex() {
  return import(/* webpackChunkName: "@/views/trade-verify-index" */ '@/views/trade/verify/index.vue')
}

function TradeOrderIdIndex() {
  return import(/* webpackChunkName: "@/views/trade-order-id-index" */ '@/views/trade/order/_id/index.vue')
}

function TradeRefundIdIndex() {
  return import(/* webpackChunkName: "@/views/trade-refund-id-index" */ '@/views/trade/refund/_id/index.vue')
}

function HomeIndex() {
  return import(/* webpackChunkName: "@/views/home-index" */ '@/views/home/<USER>')
}

function UserIndex() {
  return import(/* webpackChunkName: "@/views/user-index" */ '@/views/user/index.vue')
}

export default [
  {
    path: '/exam',
    component: Exam,
    meta: {
      title: '试卷管理'
    },
    children: [
      {
        name: 'exam-index',
        path: '',
        component: ExamIndex,
        meta: {
          title: '试卷管理'
        }
      },
      {
        name: 'exam-add',
        path: 'add',
        component: ExamAdd,
        meta: {
          title: '新建试卷'
        }
      },
      {
        name: 'exam-type',
        path: 'type',
        component: ExamType,
        meta: {
          title: '试题分类管理'
        }
      },
      {
        name: 'exam-id-index',
        path: ':id',
        component: ExamIdIndex,
        meta: {
          title: '查看试卷详情'
        }
      },
      {
        name: 'exam-id-modify',
        path: ':id/modify',
        component: ExamIdModify,
        meta: {
          title: '修改试卷'
        }
      }
    ]
  },
  {
    path: '/import-open',
    component: ImportOpen,
    meta: {
      title: '导入开通管理'
    },
    children: [
      {
        name: 'import-open-index',
        path: '',
        component: ImportOpenIndex,
        meta: {
          title: '导入开通'
        }
      },
      {
        name: 'import-open-result',
        path: 'result',
        component: ImportOpenResult,
        meta: {
          title: '导入开通结果跟踪'
        }
      },
      {
        name: 'import-open-task',
        path: 'task',
        component: ImportOpenTask,
        meta: {
          title: '导入任务查阅'
        }
      }
    ]
  },
  {
    name: 'learning-content',
    path: '/learning-content',
    component: LearningContent,
    meta: {
      title: '学习内容管理'
    },
    children: [
      {
        path: 'course',
        component: LearningContentCourse,
        meta: {
          title: '课程管理'
        },
        children: [
          {
            name: 'learning-content-course-index',
            path: '',
            component: LearningContentCourseIndex,
            meta: {
              title: '课程管理'
            }
          },
          {
            name: 'learning-content-course-add',
            path: 'add',
            component: LearningContentCourseAdd,
            meta: {
              title: '新建课程'
            }
          },
          {
            name: 'learning-content-course-id-index',
            path: ':id',
            component: LearningContentCourseIdIndex,
            meta: {
              title: '课程详情'
            }
          },
          {
            name: 'learning-content-course-id-modify',
            path: ':id/modify',
            component: LearningContentCourseIdModify,
            meta: {
              title: '修改课程'
            }
          }
        ]
      },
      {
        path: 'media',
        component: LearningContentMedia,
        meta: {
          title: '媒体管理'
        },
        children: [
          {
            name: 'learning-content-media-index',
            path: '',
            component: LearningContentMediaIndex,
            meta: {
              title: '媒体管理'
            }
          },
          {
            name: 'learning-content-media-add',
            path: 'add',
            component: LearningContentMediaAdd,
            meta: {
              title: '新建媒体'
            }
          },
          {
            name: 'learning-content-media-type',
            path: 'type',
            component: LearningContentMediaType,
            meta: {
              title: '分类管理'
            }
          },
          {
            name: 'learning-content-media-id-index',
            path: ':id',
            component: LearningContentMediaIdIndex,
            meta: {
              title: '查看媒体详情'
            }
          },
          {
            name: 'learning-content-media-id-modify',
            path: ':id/modify',
            component: LearningContentMediaIdModify,
            meta: {
              title: '修改媒体信息'
            }
          }
        ]
      },
      {
        path: 'package',
        component: LearningContentPackage,
        meta: {
          title: '课程包管理'
        },
        children: [
          {
            name: 'learning-content-package-index',
            path: '',
            component: LearningContentPackageIndex,
            meta: {
              title: '课程包管理'
            }
          },
          {
            name: 'learning-content-package-add',
            path: 'add',
            component: LearningContentPackageAdd,
            meta: {
              title: '新建课程包'
            }
          },
          {
            name: 'learning-content-package-id-index',
            path: ':id',
            component: LearningContentPackageIdIndex,
            meta: {
              title: '课程包详情'
            }
          },
          {
            name: 'learning-content-package-id-modify',
            path: ':id/modify',
            component: LearningContentPackageIdModify,
            meta: {
              title: '修改课程包'
            }
          }
        ]
      }
    ]
  },
  {
    path: '/learning-scheme',
    component: LearningScheme,
    meta: {
      title: '培训方案管理'
    },
    children: [
      {
        name: 'learning-scheme-index',
        path: '',
        component: LearningSchemeIndex,
        meta: {
          title: '培训方案管理'
        }
      },
      {
        name: 'learning-scheme-add',
        path: 'add',
        component: LearningSchemeAdd,
        meta: {
          title: '创建培训方案'
        }
      }
    ]
  },
  {
    path: '/organization',
    component: Organization,
    meta: {
      title: '培训结构管理'
    },
    children: [
      {
        name: 'organization-index',
        path: '',
        component: OrganizationIndex,
        meta: {
          title: '培训机构管理'
        }
      },
      {
        name: 'organization-add',
        path: 'add',
        component: OrganizationAdd,
        meta: {
          title: '新建机构'
        }
      },
      {
        name: 'organization-admin-index',
        path: 'admin',
        component: OrganizationAdminIndex,
        meta: {
          title: '机构管理员管理'
        }
      }
    ]
  },
  {
    path: '/outline',
    component: Outline,
    meta: {
      title: '考纲管理'
    },
    children: [
      {
        name: 'outline-index',
        path: '',
        component: OutlineIndex,
        meta: {
          title: '考纲管理'
        }
      },
      {
        name: 'outline-batch-add',
        path: 'batch-add',
        component: OutlineBatchAdd,
        meta: {
          title: '批量创建考纲'
        }
      }
    ]
  },
  {
    path: '/question',
    component: Question,
    meta: {
      title: '试题管理'
    },
    children: [
      {
        name: 'question-index',
        path: '',
        component: QuestionIndex,
        meta: {
          title: '试题管理'
        }
      },
      {
        name: 'question-add',
        path: 'add',
        component: QuestionAdd,
        meta: {
          title: '创建试题'
        }
      },
      {
        name: 'question-batch-add',
        path: 'batch-add',
        component: QuestionBatchAdd,
        meta: {
          title: '批量创建试题'
        }
      },
      {
        name: 'question-wrong-index',
        path: 'wrong',
        component: QuestionWrongIndex,
        meta: {
          title: '试题错误信息'
        }
      },
      {
        name: 'question-id-index',
        path: ':id',
        component: QuestionIdIndex,
        meta: {
          title: '试题详情'
        }
      },
      {
        name: 'question-id-modify',
        path: ':id/modify',
        component: QuestionIdModify,
        meta: {
          title: '修改试题'
        }
      }
    ]
  },
  {
    name: 'trade',
    path: '/trade',
    component: Trade,
    meta: {
      title: '交易系统'
    },
    children: [
      {
        name: 'trade-order-index',
        path: 'order',
        component: TradeOrderIndex,
        meta: {
          title: '订单管理'
        }
      },
      {
        name: 'trade-refund-index',
        path: 'refund',
        component: TradeRefundIndex,
        meta: {
          title: '退款管理'
        }
      },
      {
        name: 'trade-verify-index',
        path: 'verify',
        component: TradeVerifyIndex,
        meta: {
          title: '对账管理'
        }
      },
      {
        name: 'trade-order-id-index',
        path: 'order/:id',
        component: TradeOrderIdIndex,
        meta: {
          title: '订单详情'
        }
      },
      {
        name: 'trade-refund-id-index',
        path: 'refund/:id',
        component: TradeRefundIdIndex,
        meta: {
          title: '退款详情'
        }
      }
    ]
  },
  {
    name: 'home-index',
    path: '/home',
    component: HomeIndex,
    meta: {
      title: '首页'
    }
  },
  {
    name: 'user-index',
    path: '/user',
    component: UserIndex,
    meta: {
      title: '用户管理'
    }
  }
]
