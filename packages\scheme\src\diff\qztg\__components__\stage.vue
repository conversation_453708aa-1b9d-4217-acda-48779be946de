<template>
  <stage ref="stageRef" v-bind="$attrs" v-on="$listeners">
    <template #sales-configuration-slot>
      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
        <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
        <div slot="content">
          <p>合并报名的任意方案下架或不在报名时段内或关闭学员报名将导致学员无法合并报名，请谨慎操作。</p>
        </div>
      </el-tooltip>
    </template>
  </stage>
</template>

<script lang="ts">
  import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  import Stage from '@hbfe/jxjy-admin-scheme/src/components/stage.vue'

  @Component({
    components: {
      Stage
    }
  })
  export default class extends Vue {
    @Ref('stageRef') stageRef: Stage

    mounted() {
      this.stageRef.labelWidth = '180px'
    }
    validateForm() {
      return this.stageRef.validateForm()
    }
  }
</script>
