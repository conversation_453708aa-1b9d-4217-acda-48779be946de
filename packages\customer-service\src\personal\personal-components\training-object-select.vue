<route-meta>
{
"title": "培训对象选择器"
}
</route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    filterable
    clearable
  >
    <el-option
      v-for="item in trainingObjectOptions"
      :label="showLabel(item)"
      :value="item.propertyId"
      :key="item.propertyId"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
  import QueryPersonIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'

  @Component
  export default class extends Vue {
    selected = ''
    // 培训对象选项
    trainingObjectOptions: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()

    @Prop({
      type: String,
      default: '请选培训对象'
    })
    placeholder: string

    //行业属性分类id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    // 行业id
    @Prop({
      type: String,
      default: ''
    })
    industryId: string

    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      this.$emit('updateTrainingCategory', this.selected)
      return this.selected
    }

    @Watch('industryPropertyId', {
      immediate: true,
      deep: true
    })
    async industryPropertyIdChange() {
      await this.getTrainingCategoryOptions()
    }

    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: TrainingCategoryVo) => {
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    /**
     * 获取培训对象 - 职业卫生行业
     */
    async getTrainingCategoryOptions() {
      const data = await QueryPersonIndustry.getTrainingObject(this.industryId)
      this.trainingObjectOptions = data
    }
  }
</script>
