import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/PlatformReconciliationQuery'

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 对账管理查询参数dto
Author:FangKunSen
Time:2020-03-30,14:10
 */
export class BatchReconciliationQueryParamsDTO {
  /**
   * 收款账号id
   */
  paymentAccountId?: string
  /**
   * 交易流水号
   */
  tradeFlowNo?: string
  /**
   * 下单渠道
   */
  channel?: string
  /**
   * 交易成功时间 起
   */
  tradeSuccessTimeStart?: string
  /**
   * 交易成功时间 止
   */
  tradeSuccessTimeEnd?: string
  /**
   * 集体缴费批次号
   */
  batchNo?: string
}

/**
 * 对账管理查询参数dto
Author:FangKunSen
Time:2020-03-30,14:10
 */
export class ReconciliationQueryParamsDTO {
  /**
   * 收款账号id
   */
  paymentAccountId?: string
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 交易流水号
   */
  tradeFlowNo?: string
  /**
   * 下单渠道
   */
  channel?: string
  /**
   * 交易成功时间 起
   */
  tradeSuccessTimeStart?: string
  /**
   * 交易成功时间 止
   */
  tradeSuccessTimeEnd?: string
}

/**
 * 对账管理dto
Author:FangKunSen
Time:2020-03-30,14:14
 */
export class BatchReconciliationDTO {
  /**
   * 订单号
   */
  batchNo: string
  /**
   * 交易流水号
   */
  tradeFlowNo: string
  /**
   * 交易成功时间
   */
  tradeSuccessTime: string
  /**
   * 支付成功时间
   */
  paySuccessTime: string
  /**
   * 买家姓名
   */
  buyerName: string
  /**
   * 买家手机号
   */
  buyerPhone: string
  /**
   * 买家身份证号
   */
  buyerIdNumber: string
  /**
   * 实付金额
   */
  amount: string
  /**
   * 收款账号
   */
  paymentAccountName: string
}

/**
 * 对账管理dto
Author:FangKunSen
Time:2020-03-30,14:14
 */
export class ReconciliationDTO {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 交易流水号
   */
  tradeFlowNo: string
  /**
   * 交易成功时间
   */
  tradeSuccessTime: string
  /**
   * 支付成功时间
   */
  paySuccessTime: string
  /**
   * 所属培训机构
   */
  unitName: string
  /**
   * 买家姓名
   */
  buyerName: string
  /**
   * 买家手机号
   */
  buyerPhone: string
  /**
   * 买家身份证号
   */
  buyerIdNumber: string
  /**
   * 实付金额
   */
  amount: string
  /**
   * 收款账号
   */
  paymentAccountName: string
}

/**
 * 对账管理合计dto
Author:FangKunSen
Time:2020-03-30,14:23
 */
export class ReconciliationStatisticDTO {
  /**
   * 订单总数
   */
  totalOrderNumber: number
  /**
   * 交易总额
   */
  totalAmount: number
}

export class BatchReconciliationDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BatchReconciliationDTO>
}

export class ReconciliationDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ReconciliationDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param reconciliationQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReconciliation(
    reconciliationQueryParamsDTO: BatchReconciliationQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.exportBatchReconciliation,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: { reconciliationQueryParamsDTO },
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param reconciliationQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReconciliation(
    reconciliationQueryParamsDTO: ReconciliationQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.exportReconciliation,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: { reconciliationQueryParamsDTO },
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param reconciliationQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBatchReconciliationStatistic(
    reconciliationQueryParamsDTO: BatchReconciliationQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.getBatchReconciliationStatistic,
    operation?: string
  ): Promise<Response<ReconciliationStatisticDTO>> {
    return commonRequestApi<ReconciliationStatisticDTO>(SERVER_URL, {
      query: query,
      variables: { reconciliationQueryParamsDTO },
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param reconciliationQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getReconciliationStatistic(
    reconciliationQueryParamsDTO: ReconciliationQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.getReconciliationStatistic,
    operation?: string
  ): Promise<Response<ReconciliationStatisticDTO>> {
    return commonRequestApi<ReconciliationStatisticDTO>(SERVER_URL, {
      query: query,
      variables: { reconciliationQueryParamsDTO },
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageBatchReconciliation(
    params: { page?: Page; reconciliationQueryParamsDTO?: BatchReconciliationQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageBatchReconciliation,
    operation?: string
  ): Promise<Response<BatchReconciliationDTOPage>> {
    return commonRequestApi<BatchReconciliationDTOPage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageReconciliation(
    params: { page?: Page; reconciliationQueryParamsDTO?: ReconciliationQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageReconciliation,
    operation?: string
  ): Promise<Response<ReconciliationDTOPage>> {
    return commonRequestApi<ReconciliationDTOPage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }
}

export default new DataGateway()
