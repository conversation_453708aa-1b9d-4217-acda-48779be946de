import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/diff-exam-query-front-gateway-QuestionnaireQueryBackStage'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'diff-exam-query-front-gateway-QuestionnaireQueryBackStage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class QuestionnaireAnswerStaitsticsRequest {
  questionnaireId?: string
}

/**
 * @Description 管理端统计报表查询班级下课程教师集合条件
@Date 2025/4/25
 */
export class QuestionnaireAnswerTeachersRequest {
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 期别id
   */
  issueId?: string
}

export class OwnerInfo {
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  servicerType: number
  servicerId: string
}

export class AnswerStaitsticsResponse {
  questionId: string
  questionType: number
  totalCount: number
  isTeacherQuestion: boolean
  statisticsAnswerContents: Array<StatisticsAnswerContentResponse>
  tag: string
}

export class QuestionnaireAnswerStaitsticsResponse {
  surveyInformationResponse: SurveyInformationResponse
  totalAnswerNum: number
  answerStaitsticsList: Array<AnswerStaitsticsResponse>
}

export class StatisticsAnswerContentResponse {
  answerItem: string
  chooseCount: number
}

export class PreconditionResponse {
  name: string
  expression: string
  learningId: string
}

export class SurveyInformationResponse {
  questionnaireId: string
  templateId: string
  schemeId: string
  ownerId: string
  questionnaireName: string
  usedRange: number
  includedInAssessment: boolean
  forceToComplete: boolean
  type: number
  createTime: string
  questionnaireStartTime: string
  questionnaireEndTime: string
  status: number
  isReferenced: boolean
  description: string
  learningId: string
  precondition: PreconditionResponse
  openResults: boolean
}

/**
 * @Description
@Date 2025/4/25
 */
export class QuestionnaireAnswerAllTeacherResponse {
  /**
   * 线上教师集合
   */
  onlineTeachers: Array<QuestionnaireAnswerTeacherResponse>
  /**
   * 线上教师集合
   */
  offlineTeachers: Array<QuestionnaireAnswerTeacherResponse>
}

/**
 * @description: 教师信息
@author: sugs
@create: 2022-03-10 10:04
 */
export class QuestionnaireAnswerTeacherResponse {
  /**
   * 教师id
   */
  id: string
  /**
   * 数据归属信息
   */
  owner: OwnerInfo
  /**
   * 教师名称
   */
  name: string
  /**
   * 头像/照片
   */
  photo: string
  /**
   * 简介内容
   */
  aboutsContent: string
  /**
   * 性别 -1:未知 0:女 1:男
@see TeacherGenders
   */
  gender: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人id
   */
  createUserId: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询各班级下学习的课程教师集合包含期别中的教师集合和线上课程的教师集合
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCourseTeachersWithPeriodAndOnlineInServicer(
    request: QuestionnaireAnswerTeachersRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCourseTeachersWithPeriodAndOnlineInServicer,
    operation?: string
  ): Promise<Response<QuestionnaireAnswerAllTeacherResponse>> {
    return commonRequestApi<QuestionnaireAnswerAllTeacherResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询当前网校下调查问卷内试题作答统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getQuestionnaireAnswerStaitsticsInServicer(
    request: QuestionnaireAnswerStaitsticsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getQuestionnaireAnswerStaitsticsInServicer,
    operation?: string
  ): Promise<Response<QuestionnaireAnswerStaitsticsResponse>> {
    return commonRequestApi<QuestionnaireAnswerStaitsticsResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
