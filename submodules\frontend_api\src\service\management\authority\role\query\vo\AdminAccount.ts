import gql from 'graphql-tag'

class AdminAccount {
  private gql = gql`
    query {
      id
      name
    }
  `
  id: string
  name: string
  enable = false
  createTime: string
  roleId: string
  account: string

  get enableLabel() {
    try {
      console.log('调用了enableLabel方法，返回值=', this.enable ? '正常' : '停用')
      return this.enable ? '正常' : '停用'
    } catch (e) {
      console.log('报错了，所处位置/service/management/authority/role/query/vo/AdminAccount.ts所处方法，enableLabel', e)
    }
  }
}

export default AdminAccount
