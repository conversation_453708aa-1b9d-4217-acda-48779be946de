import MSAutoLearningTask, {
  StudentAutoLearningTaskResultQueryPageRequest,
  StudentAutoLearningTaskResult
} from '@api/ms-gateway/ms-autolearning-student-auto-learning-task-result-v1'
import { Page } from '@hbfe/common'
import IntelligenceAbnormalParams from '@api/service/management/statisticalReport/query/vo/IntelligenceAbnormalParams'
import IntelligenceAbnormalItem from '@api/service/management/statisticalReport/query/vo/IntelligenceAbnormalItem'

export default class IntelligenceAbnormalList {
  /**
   * 查询参数
   */
  queryParams: IntelligenceAbnormalParams = new IntelligenceAbnormalParams()
  /**
   * 查询列表
   */
  list: Array<IntelligenceAbnormalItem> = []
  /**
   * 智能学习异常任务查询
   */
  async queryList(page: Page) {
    const params = IntelligenceAbnormalParams.to(this.queryParams)
    params.pageNo = page.pageNo
    params.pageSize = page.pageSize

    this.list = []
    const res = await MSAutoLearningTask.queryByPage(params)
    if (res.status.isSuccess()) {
      page.totalSize = res.data.totalSize
      page.totalPageSize = res.data.totalPageSize
      if (res.data.currentPageData.length) {
        this.list = res.data.currentPageData.map((item: StudentAutoLearningTaskResult) => {
          return IntelligenceAbnormalItem.from(item)
        })
        const ids = res.data.currentPageData.map((item) => item.mainTaskId)
        await MSAutoLearningTask.findLastFailSubTaskByMainTaskIdList(ids).then((res) => {
          if (res.status.isSuccess()) {
            this.list.map((item) => {
              item.operationTime = res.data.find((subItem) => subItem.mainTaskId === item.mainTaskId)?.completedTime
              return item
            })
          }
        })
      }
    } else {
      page.totalSize = 0
      page.totalPageSize = 0
    }

    return res.status
  }
}
