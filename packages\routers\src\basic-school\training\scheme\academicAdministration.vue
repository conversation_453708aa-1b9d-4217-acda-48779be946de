<route-params content="/:schemeId/:periodId"></route-params>
<route-meta>
{
"isMenu": true,
"title": "教务管理",
"sort": 2,
"icon": "icon_guanli",
"hideMenu": true
}
</route-meta>
<script lang="ts">
  import AcademicAdministration from '@hbfe/jxjy-admin-scheme/src/implementingManagement/academicAdministration.vue'
  import { WXGLY } from '@/models/RoleTypes'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  @RoleTypeDecorator({
    getPeriodConfigById: [WXGLY],
    educational: [WXGLY],
    Questionnaire: [WXGLY],
    educationalQuery: [WXGLY],
    openChangeLivingInfo: [WXGLY],
    exportDataList: [WXGLY],
    importResult: [WXGLY],
    downloadTemplate: [WXGLY],
    MinUploadFile: [WXGLY],
    confirmImport: [WXGLY],
    save: [WXGLY]
  })
  export default class extends AcademicAdministration {
    //
  }
</script>
