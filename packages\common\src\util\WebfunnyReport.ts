import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import Context from '@api/service/common/context/Context'
import UserModule from '@api/service/customer/user/UserModule'
import BaseEventTracking, { Env } from '@hbfe/base-event-tracking/src/index'
import Environment from '@api/service/common/utils/Env'
interface EventTrack {
  projectId: string
  pointId: string
  projectKey: string
  timeoutSecond: string
}
export interface UpErrorTrack {
  response: string
  bizCode: string
  httpCode: string
  ciphertext: string
}

export class WebfunnyConfig {
  config = {
    dev: {
      host: 'https://webfunny.test1.59iedu.com:8443',
      projectId: 'event1088',
      projectName: '8.0通用平台',
      pointIdList: [
        { id: 257, name: '通用-完善' },
        { id: 236, name: '通用--课程播放' },
        { id: 237, name: '华医网跳转页面h5-异常上报' },
        { id: 238, name: '华医网跳转页面-customer-异常上报' },
        { id: 239, name: '异常请求' },
        { id: 240, name: 'admin-网络慢请求' },
        { id: 241, name: 'customer-网络慢请求' },
        { id: 242, name: 'h5-网络慢请求' },
        { id: 243, name: '浏览量_copy' },
        { id: 235, name: '浏览量' }
      ]
    },
    release: {
      host: 'https://webfunny.59iedu.com',
      projectId: 'event1013',
      projectName: '8.0继续教育',
      pointIdList: [
        { id: 95, name: '通用-完善' },
        { id: 79, name: 'h5-网络慢请求' },
        { id: 80, name: '浏览量_copy' },
        { id: 73, name: '通用--课程播放' },
        { id: 74, name: '华医网跳转页面h5-异常上报' },
        { id: 75, name: '华医网跳转页面-customer-异常上报' },
        { id: 76, name: '异常请求' },
        { id: 77, name: 'admin-网络慢请求' },
        { id: 78, name: 'customer-网络慢请求' },
        { id: 72, name: '华医网跳转页面-h5-异常上报' },
        { id: 71, name: '华医网跳转页面customer-上报异常' },
        { id: 65, name: 'admin-慢请求' },
        { id: 64, name: 'customer-慢请求' },
        { id: 63, name: 'h5-慢请求' },
        { id: 62, name: '浏览量' }
      ]
    }
  }
}
//播放异常
export class ScreeningErrorPoint {
  userId?: string // 用户ID
  pointId?: number // pointId | 类型：文本 | 长度：50
  courserId?: string //课程id | 类型：文本 | 长度200
  courseName?: string //课程名称| 类型：文本 | 长度：500
  courseWareId?: string //课件id| 类型：文本 | 长度：200
  courseWareName?: string //课件名称| 类型：文本 | 长度：500
  schemeId?: string //方案id| 类型：文本 | 长度：200
  schemeName?: string //方案名称| 类型：文本 | 长度：50
  currentScale?: number //当前刻度| 类型：文本 | 长度：500
  bizCode?: string // bizCode | 类型：文本 | 长度：5 | 描述：业务状态码
  requestBody?: string // requestBody | 类型：文本 | 长度：500 | 描述：网络请求参数体
  response?: string // response  | 类型：文本 | 长度：500 | 描述：网络请求返回结果
  domainName?: string // domainName | 类型：文本 | 长度：500 | 描述：网校域名
  servicerId?: string // servicerId | 类型：文本 | 长度：200 | 描述：子项目id
  abnormalMessage?: string // abnormalMessage | 类型：文本 | 长度：1000 | 描述：异常信息
  accountId?: string // accountId | 类型：文本 | 长度：200 | 描述：账号id
}

export class PerfectInfoPoint {
  abnormalMessage: string
  domainName: string
  requestBody: string
  response: string
  userId: string
}

class WebfunnyReport extends BaseEventTracking {
  webfunnyConfig = new WebfunnyConfig()
  /***
   * 是否初始化完成
   */
  private initOver = false
  /**
   * 是否启用 字符串的布尔值
   */
  private webfunnySwitch = 'false'
  /**
   * webfunny体系下的项目ID
   */
  private projectId = ''
  /**
   * 业务系统的项目Key(当前项目名称)projectKey 业务系统的项目Key(当前项目名称)
   */
  private projectKey = 'jxjy_customer'
  /**
   * 定位ID
   */
  private pointId = 215
  /**
   * 上报慢请求时间
   */
  private timeoutSecond = 2000
  constructor() {
    super()
    // 初始化配置
    // this.initWithConfig(
    //   '{"env":"dev","status":true,"projectId":"event1054","bizKey":"btglxt","nomarlEvent":{"slowRQ":{"id":"68","limit":1000},"networkError":{"id":"69","exHttp":[200,401]}}}'
    // )
  }

  /**
   * 最初初始化
   */
  beforeInit() {
    this.webfunnySwitch = ConfigCenterModule.getFrontendApplication(frontendApplication.webfunnySwitch)
    if (this.webfunnySwitch === 'false') {
      console.log('不启用')
      return
    }

    if (Environment.isProxyInnerNetworkEnv) {
      this.projectId = this.webfunnyConfig.config.dev.projectId
      this.init(this.projectId, this.projectKey, 'tourist', Env.dev)
      const userModule = UserModule.queryUserFactory.getQueryUserInfo()
      this.setUserId(userModule.userInfo.userInfo.userId)
    } else {
      this.projectId = this.webfunnyConfig.config.release.projectId
      this.init(this.projectId, this.projectKey, 'tourist', Env.release)
      const userModule = UserModule.queryUserFactory.getQueryUserInfo()
      this.setUserId(userModule.userInfo.userInfo.userId)
    }
    this.initOver = true
  }
  /**
   * 登录后 重新初始化 只有在上报时去重新初始化
   */
  private login(userId: string) {
    if (Environment.isProxyInnerNetworkEnv) {
      this.init(this.projectId, this.projectKey, userId, Env.dev)
    } else {
      this.init(this.projectId, this.projectKey, userId, Env.release)
    }
  }
  private findIdByName(name: string, list: { id: number; name: string }[]): number | undefined {
    for (const item of list) {
      if (item.name === name) {
        return item.id
      }
    }
    return undefined // 如果没有找到匹配项，则返回 undefined
  }

  private removeSpecialCharacters(str: string) {
    return str.replace(/\s+/g, '_').replace(/[^\w\u4e00-\u9fa5\-_]/g, '')
  }
  /**
   * 上报课程异常
   */
  upCoursePlayback(screeningError: any) {
    if (this.webfunnySwitch === 'false') {
      console.log('不启用')
      return
    }
    const userModule = UserModule.queryUserFactory.getQueryUserInfo()
    if (userModule.userInfo.userInfo.userId) {
      this.login(userModule.userInfo.userInfo.userId)
    }
    if (Environment.isProxyInnerNetworkEnv) {
      this.pointId = this.findIdByName('通用--课程播放', this.webfunnyConfig.config.dev.pointIdList)
    } else {
      this.pointId = this.findIdByName('通用--课程播放', this.webfunnyConfig.config.release.pointIdList)
    }
    const screeningErrorPoint = { ...screeningError }
    screeningErrorPoint.pointId = this.pointId
    screeningErrorPoint.userId = userModule.userInfo.userInfo.userId
    screeningErrorPoint.servicerId = Context.businessEnvironment?.serviceToken?.tokenMeta?.servicerId
    screeningErrorPoint.domainName = location.hostname
    screeningErrorPoint.accountId = userModule.userInfo.accountInfo.accountId
    Object.keys(screeningErrorPoint).forEach((key: string) => {
      if (typeof screeningErrorPoint[key] == 'string') {
        screeningErrorPoint[key] = this.removeSpecialCharacters(screeningErrorPoint[key])
        // jsonObj[key] = btoa(jsonObj[key])
        // jsonObj[key] = encodeURIComponent(jsonObj[key])
      }
    })
    this.upMyEvents(screeningErrorPoint)
  }

  /**
   * 上报完善信息
   */
  upPerfectInfo(screeningError: any) {
    try {
      if (this.webfunnySwitch === 'false') {
        console.log('不启用')
        return
      }
      const userModule = UserModule.queryUserFactory.getQueryUserInfo()
      if (userModule.userInfo.userInfo.userId) {
        this.login(userModule.userInfo.userInfo.userId)
      }
      if (Environment.isProxyInnerNetworkEnv) {
        this.pointId = this.findIdByName('通用-完善', this.webfunnyConfig.config.dev.pointIdList)
      } else {
        this.pointId = this.findIdByName('通用-完善', this.webfunnyConfig.config.release.pointIdList)
      }

      const screeningErrorPoint = { ...screeningError }
      screeningErrorPoint.pointId = this.pointId
      screeningErrorPoint.userId = userModule.userInfo.userInfo.userId
      screeningErrorPoint.servicerId = Context.businessEnvironment?.serviceToken?.tokenMeta?.servicerId
      screeningErrorPoint.domainName = location.hostname
      screeningErrorPoint.accountId = userModule.userInfo.accountInfo.accountId
      Object.keys(screeningErrorPoint).forEach((key: string) => {
        if (typeof screeningErrorPoint[key] == 'string') {
          screeningErrorPoint[key] = this.removeSpecialCharacters(screeningErrorPoint[key])
          // jsonObj[key] = btoa(jsonObj[key])
          // jsonObj[key] = encodeURIComponent(jsonObj[key])
        }
      })
      this.upMyEvents(screeningErrorPoint)
    } catch (e) {
      console.group(
        '%c%s',
        'padding:3px 60px;color:#fff;background-image: linear-gradient(to right, #ffa17f, #00223e)',
        'schemeRefundList调试'
      )
      console.log(e, '报错')
      console.groupEnd()
    }
  }
  /**
   * 上报
   */
  slowRQEvent(duration: number, url: string) {
    if (this.webfunnySwitch === 'false') {
      console.log('不启用')
      return
    }
    const userModule = UserModule.queryUserFactory.getQueryUserInfo()
    if (userModule.userInfo.userInfo.userId) {
      this.login(userModule.userInfo.userInfo.userId)
    }
    if (Environment.isProxyInnerNetworkEnv) {
      this.pointId = this.findIdByName('customer-网络慢请求', this.webfunnyConfig.config.dev.pointIdList)
    } else {
      this.pointId = this.findIdByName('customer-网络慢请求', this.webfunnyConfig.config.release.pointIdList)
    }

    if (this.initOver) {
      if (duration > this.timeoutSecond) {
        this.upSlowRQEvent(duration, url, this.pointId)
        console.log(`Request ${url} duration: ${duration} ms`)
      }
    }
  }

  upErrorRQEvent(upData: UpErrorTrack) {
    if (Environment.isProxyInnerNetworkEnv) {
      this.pointId = this.findIdByName('华医网跳转页面-customer-异常上报', this.webfunnyConfig.config.dev.pointIdList)
    } else {
      this.pointId = this.findIdByName(
        '华医网跳转页面-customer-异常上报',
        this.webfunnyConfig.config.release.pointIdList
      )
    }
    upData.response = upData.response.substr(0, 499)
    let isUploadLog = false
    if (this.webfunnySwitch === 'false') {
      console.log('不启用')
      return
    }
    const userModule = UserModule.queryUserFactory.getQueryUserInfo()
    if (userModule.userInfo.userInfo.userId) {
      this.login(userModule.userInfo.userInfo.userId)
    } else {
      this.login(`tourist`)
    }
    if (this.getPointNetworkError().exHttp.indexOf(Number(upData.httpCode)) < 0) {
      // 排除不提交的网络状态
      isUploadLog = true
    }
    if (upData.httpCode == '200' && upData.bizCode != '200') {
      isUploadLog = true
    }

    if (!this.pointId) {
      this.pointId = this.getPointNetworkError().id
    }
    if (this.pointId <= 0) return

    const comments = { ...upData, pointId: this.pointId, projectKey: this.projectKey }
    if (isUploadLog) {
      if (this.initOver) {
        this.upMyEvents(comments)
      }
    }
  }
}
export default new WebfunnyReport()
