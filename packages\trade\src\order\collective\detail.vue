<route-params content="/:id"></route-params>
<route-meta>
{
"isMenu": false,
"onlyShowOnTab": true,
"title": "集体报名订单详情"
}
</route-meta>
<template>
  <el-main v-loading="uiConfig.loading.pageLoading">
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/training/trade/order/collective')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/trade/order/collective' }">集体报名订单</el-breadcrumb-item>
      <el-breadcrumb-item>详情</el-breadcrumb-item>
    </el-breadcrumb>
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom" v-if="isWaitPlaceOrder">
      <p>温馨提示：</p>
      <p>1. 需为学员缴费，请先创建报名批次，再选择对应批次缴费的人员及培训班；</p>
      <p>2. 平台提供批量导班，需先下载导入模版再提交，导入任务成功的记录会直接显示在报名批次中；</p>
      <p>3. 报名批次提交后，请在 24小时 内进行缴费，超过时间未缴则系统将默认关闭批次，需重新创建批次进行缴费。</p>
    </el-alert>
    <!--待下单-->
    <div class="f-p15" v-if="isWaitPlaceOrder">
      <el-card shadow="never" class="m-card is-header m-order-info f-mb15">
        <div class="f-flex-sub f-plr20 f-pt10">
          <div class="m-tit">
            <span class="tit-txt">批次信息</span>
          </div>
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="24">
              <el-form-item label="报名批次号：">{{ batchOrderDetail.batchInfo.batchOrderNo }}</el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="批次状态：">
                <el-tag size="small">
                  {{ batchOrderStatusName(batchOrderStatus) }}
                </el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="缴费人次：">{{ batchOrderDetail.batchInfo.payPersonTime }}</el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="实付金额（元）："> {{ batchOrderDetail.batchInfo.payAmount }}</el-form-item>
            </el-col>
          </el-form>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="f-flex-sub f-plr20 f-pt10">
          <div class="m-tit is-small">
            <span class="tit-txt">当前批次状态</span>
          </div>
          <!--条件查询-->
          <hb-search-wrapper @reset="resetSignUpQueryParams" class="m-query f-pt10">
            <el-form-item label="证件号：">
              <el-input v-model="userAccount" clearable placeholder="请输入身份证号" @clear="userAccount = null" />
            </el-form-item>
            <el-form-item label="培训方案：">
              <biz-learning-scheme-select v-model="schemeList"></biz-learning-scheme-select>
            </el-form-item>
            <template slot="actions">
              <el-button type="primary" @click="searchSignUpBase">查询</el-button>
              <el-button @click="viewImportTask">查看导入任务</el-button>
            </template>
          </hb-search-wrapper>
          <!--表格-->
          <el-table stripe :data="signUpList" max-height="500px" class="m-table f-mt5" v-loading="signUpQuery.loading">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="学员信息" min-width="250" fixed="left">
              <template slot-scope="scope">
                <p>姓名：{{ scope.row.studentInfo.buyerName }}</p>
                <p>手机号：{{ scope.row.studentInfo.buyerPhone }}</p>
                <p>身份证号：{{ scope.row.studentInfo.buyerAccount }}</p>
              </template>
            </el-table-column>
            <el-table-column label="培训方案名称" min-width="300">
              <template slot-scope="scope">{{ scope.row.commodityInfo.schemeName }}</template>
            </el-table-column>
            <el-table-column label="属性" min-width="240">
              <template slot-scope="scope">
                <p v-if="getSkuPropertyName(scope.row, 'industry')">
                  行业：{{ getSkuPropertyName(scope.row, 'industry') }}
                </p>
                <p v-if="getSkuPropertyName(scope.row, 'region')">
                  地区：{{ getSkuPropertyName(scope.row, 'region') }}
                </p>
                <p v-if="getSkuPropertyName(scope.row, 'subjectType')">
                  科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
                </p>
                <p v-if="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
                  培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
                </p>
                <p v-if="getSkuPropertyName(scope.row, 'trainingMajor')">
                  培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
                </p>
                <p v-if="getSkuPropertyName(scope.row, 'trainingObject')">
                  培训对象：{{ getSkuPropertyName(scope.row, 'trainingObject') }}
                </p>
                <p v-if="getSkuPropertyName(scope.row, 'positionCategory')">
                  岗位类别：{{ getSkuPropertyName(scope.row, 'positionCategory') }}
                </p>
                <p v-if="getSkuPropertyName(scope.row, 'jobLevel')">
                  技术等级：{{ getSkuPropertyName(scope.row, 'jobLevel') }}
                </p>
                <p v-if="getSkuPropertyName(scope.row, 'grade')">学段：{{ getSkuPropertyName(scope.row, 'grade') }}</p>
                <p v-if="getSkuPropertyName(scope.row, 'subject')">
                  学科：{{ getSkuPropertyName(scope.row, 'subject') }}
                </p>
                <p v-if="getSkuPropertyName(scope.row, 'year')">
                  培训年度：{{ getSkuPropertyName(scope.row, 'year') }}
                </p>
              </template>
            </el-table-column>
            <el-table-column label="学时" min-width="140" align="center">
              <template slot-scope="scope">{{ scope.row.period }}</template>
            </el-table-column>
            <el-table-column label="价格(元)" width="200" align="right">
              <template slot-scope="scope">{{ scope.row.price }}</template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="signUpPage" v-bind="signUpPage"> </hb-pagination>
        </div>
      </el-card>
    </div>
    <!--其他状态-->
    <div class="f-p15" v-else>
      <!--待支付/下单中-->
      <el-card shadow="never" class="m-card is-header m-order-state" v-if="isPlacingOrderOrWaitPay">
        <div class="info">
          <p>报名批次号：{{ batchOrderDetail.batchOrderNo }}</p>
          <p class="state f-co">{{ batchOrderStatusName(batchOrderStatus) }}</p>
        </div>
        <el-steps :active="1" align-center class="process">
          <el-step title="提交批次" :description="batchOrderDetail.applyTime" icon="hb-iconfont icon-s-myorder">
          </el-step>
          <el-step title="已付款" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step title="已发货" icon="hb-iconfont icon-s-learningcenter"></el-step>
          <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
      </el-card>
      <!--支付中-->
      <el-card shadow="never" class="m-card is-header m-order-state" v-if="isPaying">
        <div class="info">
          <p>报名批次号：{{ batchOrderDetail.batchOrderNo }}</p>
          <p class="state f-cb">{{ batchOrderStatusName(batchOrderStatus) }}</p>
        </div>
        <el-steps :active="1" align-center class="process">
          <el-step title="提交批次" :description="batchOrderDetail.applyTime" icon="hb-iconfont icon-s-myorder">
          </el-step>
          <el-step title="已付款" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step title="已发货" icon="hb-iconfont icon-s-learningcenter"></el-step>
          <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
      </el-card>
      <!--开通中-->
      <el-card shadow="never" class="m-card is-header m-order-state" v-if="isOpening">
        <div class="info">
          <p>报名批次号：{{ batchOrderDetail.batchOrderNo }}</p>
          <p class="state f-cb">{{ batchOrderStatusName(batchOrderStatus) }}</p>
        </div>
        <el-steps :active="batchOrderDetail.deliveryTime ? 3 : 2" align-center class="process">
          <el-step title="提交批次" :description="batchOrderDetail.applyTime" icon="hb-iconfont icon-s-myorder">
          </el-step>
          <el-step title="已付款" :description="batchOrderDetail.payTime" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step title="已发货" :description="batchOrderDetail.deliveryTime" icon="hb-iconfont icon-s-learningcenter">
          </el-step>
          <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
      </el-card>
      <!--交易成功-->
      <el-card shadow="never" class="m-card is-header m-order-state" v-if="isPaySuccess">
        <div class="info">
          <p>报名批次号：{{ batchOrderDetail.batchOrderNo }}</p>
          <p class="state f-cg">{{ batchOrderStatusName(batchOrderStatus) }}</p>
        </div>
        <el-steps :active="4" align-center class="process">
          <el-step title="提交批次" :description="batchOrderDetail.applyTime" icon="hb-iconfont icon-s-myorder">
          </el-step>
          <el-step title="已付款" :description="batchOrderDetail.payTime" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step title="已发货" :description="batchOrderDetail.deliveryTime" icon="hb-iconfont icon-s-learningcenter">
          </el-step>
          <el-step title="交易成功" :description="batchOrderDetail.tradeSuccessTime" icon="hb-iconfont icon-success">
          </el-step>
        </el-steps>
      </el-card>
      <!--交易关闭中-->
      <el-card shadow="never" class="m-card is-header m-order-state" v-if="isClosingPay">
        <div class="info">
          <p>报名批次号：{{ batchOrderDetail.batchOrderNo }}</p>
          <p class="state f-c9">{{ batchOrderStatusName(batchOrderStatus) }}</p>
        </div>
        <el-steps :active="1" align-center class="process">
          <el-step title="提交批次" :description="batchOrderDetail.applyTime" icon="hb-iconfont icon-s-myorder">
          </el-step>
          <el-step title="交易关闭" icon="hb-iconfont icon-s-close"></el-step>
        </el-steps>
      </el-card>
      <!--交易关闭-->
      <el-card shadow="never" class="m-card is-header m-order-state" v-if="isClosePay">
        <div class="info">
          <p>报名批次号：{{ batchOrderDetail.batchOrderNo }}</p>
          <p class="state f-c9">{{ batchOrderStatusName(batchOrderStatus) }}</p>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step title="提交批次" :description="batchOrderDetail.applyTime" icon="hb-iconfont icon-s-myorder">
          </el-step>
          <el-step title="交易关闭" :description="batchOrderDetail.tradeCloseTime" icon="hb-iconfont icon-s-close">
          </el-step>
        </el-steps>
      </el-card>

      <el-tabs v-model="activeName" type="card" class="m-tab-card">
        <el-tab-pane label="基础信息" name="basic-data">
          <!--订单信息-->
          <el-card shadow="never" class="m-card is-header m-order-info">
            <div class="f-flex-sub f-plr20 f-pt10">
              <div class="m-tit">
                <span class="tit-txt">发票信息</span>
              </div>
              <!--要求发票-->
              <div v-if="batchOrderDetail.requireInvoice">
                <el-form
                  label-width="140px"
                  class="m-text-form f-pt10 f-pb20"
                  v-for="(item, index) in batchOrderDetail.invoiceInfoList"
                  :key="index"
                >
                  <el-col :span="24">
                    <el-form-item label="发票类型：">
                      {{ invoiceType(item) }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="发票状态：">
                      <el-tag :type="invoiceStatusStyle(item)" size="small" class="f-mr5">
                        {{ invoiceStatus(item) }}
                      </el-tag>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="申请开票时间：">
                      {{ item.applyInvoiceDate }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="发票抬头：" v-if="invoiceTitleType(item) && item.invoiceTitle">
                      {{ invoiceTitleType(item) }} - {{ item.invoiceTitle }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="统一社会信用代码：">{{ item.taxpayerNo }}</el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="isShowInvoiceAdditionalInfo(item)">
                    <el-form-item label="开户银行：">{{ item.bankName }}</el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="isShowInvoiceAdditionalInfo(item)">
                    <el-form-item label="开户帐号：">{{ item.accountNo }}</el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="isShowInvoiceAdditionalInfo(item)">
                    <el-form-item label="注册电话：">{{ item.registerPhone }}</el-form-item>
                  </el-col>
                  <el-col :span="24" v-if="isShowInvoiceAdditionalInfo(item)">
                    <el-form-item label="注册地址：">{{ item.registerAddress }}</el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="发票备注：">{{ item.remark }}</el-form-item>
                  </el-col>
                </el-form>
              </div>
              <!--不要求发票-->
              <el-form label-width="140px" class="m-text-form f-pt10 f-pb20" v-else>
                <el-col :span="12">
                  <el-form-item label="是否需要发票：">否</el-form-item>
                </el-col>
              </el-form>
              <div class="m-tit" v-if="batchOrderDetail.deliveryInfoVisible && (isSelfFetched || isCourier)">
                <span class="tit-txt">配送信息</span>
              </div>
              <!--快递-->
              <el-form
                label-width="140px"
                class="m-text-form f-pt10 f-pb20"
                v-if="batchOrderDetail.deliveryInfoVisible && isCourier"
              >
                <el-col :span="12">
                  <el-form-item label="配送方式：">{{ deliveryWay(batchOrderDetail) }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="快递公司：">{{ batchOrderDetail.deliveryInfo.deliveryCompany }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="收货地址：">{{ batchOrderDetail.deliveryInfo.distributeAddress }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="收件人：">{{ batchOrderDetail.deliveryInfo.distributeConsignee }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="手机号：">{{ batchOrderDetail.deliveryInfo.distributePhone }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="运单号：">
                    {{ batchOrderDetail.deliveryInfo.deliveryNo }}
                    <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                      <i
                        class="el-icon-document-copy f-link-gray f-ml5 f-c9"
                        @click="copyDeliveryNo(batchOrderDetail.deliveryInfo.deliveryNo, $event)"
                      ></i>
                      <div slot="content">复制运单号并查询</div>
                    </el-tooltip>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发货时间：">{{ batchOrderDetail.deliveryInfo.deliveryTime }}</el-form-item>
                </el-col>
              </el-form>
              <!--自取-->
              <el-form
                label-width="140px"
                class="m-text-form f-pt10 f-pb20"
                v-if="batchOrderDetail.deliveryInfoVisible && isSelfFetched"
              >
                <el-col :span="12">
                  <el-form-item label="配送方式：">{{ deliveryWay(batchOrderDetail) }}</el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="领取地点：">{{ batchOrderDetail.deliveryInfo.selfFetchedPoint }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="领取时间：">{{ batchOrderDetail.deliveryInfo.selfFetchTime }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备注：">{{ batchOrderDetail.deliveryInfo.remark }}</el-form-item>
                </el-col>
              </el-form>
            </div>
            <div class="right f-plr20 f-ptb10">
              <div class="m-tit">
                <span class="tit-txt">购买人信息</span>
              </div>
              <el-form label-width="120px" class="m-text-form is-column f-pt10 f-pb20">
                <el-form-item label="购买人：">{{ batchOrderDetail.buyerInfo.buyerName }}</el-form-item>
                <el-form-item label="帐号：">{{ batchOrderDetail.buyerInfo.buyerAccount }}</el-form-item>
              </el-form>
              <div class="m-tit">
                <span class="tit-txt">支付信息</span>
              </div>
              <el-form label-width="120px" class="m-text-form is-column f-pt10 f-pb20">
                <el-form-item label="支付方式：">{{ paymentType(batchOrderDetail) || '-' }}</el-form-item>
                <!--线上支付-->
                <el-form-item label="交易号：" v-if="!isOffLinePay(batchOrderDetail)">
                  {{ batchOrderDetail.paymentInfo.tradeNo || '-' }}
                </el-form-item>
                <el-form-item label="付款时间：" v-if="!isOffLinePay(batchOrderDetail)">
                  {{ batchOrderDetail.paymentInfo.applyPayDate || '-' }}
                </el-form-item>
                <el-form-item label="付款成功时间：" v-if="!isOffLinePay(batchOrderDetail)">
                  {{ batchOrderDetail.paymentInfo.paySuccessDate || '-' }}
                </el-form-item>
                <!--线下支付-->
                <el-form-item label="汇款凭证：" v-if="isOffLinePay(batchOrderDetail)">
                  <div
                    class="course-pic is-small img-hover f-mb10"
                    v-for="(item, index) in batchOrderDetail.paymentInfo.remittanceVoucherList"
                    :key="index"
                  >
                    <el-image
                      class="course-pic is-small"
                      :src="mfsImage(item.thumbnailImageUrl)"
                      :preview-src-list="[mfsImage(item.originImageUrl)]"
                    />
                    <p class="hover-txt">点击图片查看大图</p>
                  </div>
                </el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="购买清单" name="purchase-list">
          <purchase-list ref="purchaseListRef" :batchOrderNo="batchOrderDetail.batchOrderNo"></purchase-list>
        </el-tab-pane>
      </el-tabs>
      <!--其他状态-->
    </div>
    <view-import-task ref="viewImportTaskRef" :visible.sync="uiConfig.dialog.viewImportTaskVisible"></view-import-task>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import BatchOrderDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailVo'
  import QueryBatchOrderDetail from '@api/service/management/trade/batch/order/query/QueryBatchOrderDetail'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import BatchOrderTradeStatus, {
    BatchOrderTradeStatusEnum
  } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import { Query, UiPage } from '@hbfe/common'
  import QueryBatchOrderSignUpListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderSignUpListVo'
  import BatchOrderSignUpListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderSignUpListDetailVo'
  import QueryBatchOrderMainOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderMainOrderListVo'
  import BatchOrderMainOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderListDetailVo'
  import ViewImportTask from '@hbfe/jxjy-admin-trade/src/order/collective/components/detail/view-import-task.vue'
  import BatchOrderInvoiceType, {
    BatchOrderInvoiceTypeEnum
  } from '@api/service/management/trade/batch/order/enum/BatchOrderInvoiceType'
  import InvoiceTitleType, {
    InvoiceTitleTypeEnum
  } from '@api/service/common/enums/trade-configuration/InvoiceTitleType'
  import BatchOrderInvoiceStatus, {
    BatchOrderInvoiceStatusEnum
  } from '@api/service/management/trade/batch/order/enum/BatchOrderInvoiceStatus'
  import BatchOrderDeliveryWay, {
    BatchOrderDeliveryWayEnum
  } from '@api/service/management/trade/batch/order/enum/BatchOrderDeliveryWay'
  import Clipboard from 'clipboard'
  import { clipboardError, clipboardSuccess } from '@hbfe/jxjy-admin-common/src/clipboard'
  import PurchaseList from '@hbfe/jxjy-admin-trade/src/order/collective/components/detail/purchase-list.vue'
  import BatchOrderDetailInvoiceInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailInvoiceInfoVo'
  import LearningSchemeSelect from '@hbfe/jxjy-admin-trade/src/order/personal/components/learning-scheme-select.vue'
  import { HasSelectCommodityMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectCommodityMode'
  import DataResolve from '@api/service/common/utils/DataResolve'
  import BatchOrderDetailModule from '@/store/modules-ui/order/BatchOrderDetailModule'
  import { BatchOrderPayModeEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderPayMode'
  import OrderTerminalTypes, { OrderTerminalTypeEnum } from '@api/service/common/enums/order/OrderTerminalTypes'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'

  @Component({
    components: { LearningSchemeSelect, PurchaseList, ViewImportTask }
  })
  export default class extends Vue {
    /**
     * 查看导入任务
     */
    @Ref('viewImportTaskRef') viewImportTaskRef: ViewImportTask

    /**
     * 购物清单
     */
    @Ref('purchaseListRef') purchaseListRef: PurchaseList

    /**
     * 上传列表
     */
    // 查询
    signUpQuery: Query = new Query()
    // 分页
    signUpPage: UiPage
    // 查询参数
    signUpQueryParams: QueryBatchOrderSignUpListVo = new QueryBatchOrderSignUpListVo()
    // 上传学员列表
    signUpList: BatchOrderSignUpListDetailVo[] = []

    /**
     * 购买清单
     */
    // 查询
    mainOrderQuery: Query = new Query()
    // 分页
    mainOrderPage: UiPage
    // 查询参数
    mainOrderQueryParams: QueryBatchOrderMainOrderListVo = new QueryBatchOrderMainOrderListVo()
    // 主单列表
    mainOrderList: BatchOrderMainOrderListDetailVo[] = []

    /**
     * 批次单id
     */
    batchOrderNo = ''

    /**
     * 远端查询入口
     */
    queryRemote: QueryBatchOrderDetail =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchOrderDetail

    /**
     * 批次单详情
     */
    batchOrderDetail: BatchOrderDetailVo = new BatchOrderDetailVo()

    /**
     * 批次单状态列表
     */
    batchOrderStatusList: EnumOption<BatchOrderTradeStatusEnum>[] = BatchOrderTradeStatus.list()

    /**
     * 发票类型列表
     */
    invoiceTypeList: EnumOption<BatchOrderInvoiceTypeEnum>[] = BatchOrderInvoiceType.list()

    /**
     * 发票抬头类型列表
     */
    invoiceTitleTypeList: EnumOption<InvoiceTitleTypeEnum>[] = InvoiceTitleType.list()

    /**
     * 发票状态列表
     */
    invoiceStatusList: EnumOption<BatchOrderInvoiceStatusEnum>[] = BatchOrderInvoiceStatus.list()

    /**
     * 发票状态Map
     */
    invoiceStatusMap: Map<BatchOrderInvoiceStatusEnum, string> = new Map<BatchOrderInvoiceStatusEnum, string>()
      .set(BatchOrderInvoiceStatusEnum.Wait_For_Invoice, 'warning')
      .set(BatchOrderInvoiceStatusEnum.Complete, 'success')
      .set(BatchOrderInvoiceStatusEnum.Frozen, 'error')
      .set(BatchOrderInvoiceStatusEnum.Invalid, 'info')

    /**
     * 配送方式
     */
    deliveryWayList: EnumOption<BatchOrderDeliveryWayEnum>[] = BatchOrderDeliveryWay.list()

    // ui控制组
    uiConfig = {
      loading: {
        pageLoading: false
      },
      dialog: {
        // 查看导入任务
        viewImportTaskVisible: false
      }
    }

    /**
     * 活页
     */
    activeName = 'basic-data'

    /**
     * 选中的培训方案
     */
    schemeList: HasSelectCommodityMode[] = []

    /**
     * 证件号
     */
    userAccount: string = null

    // 支付终端类型列表
    paymentTerminalList: EnumOption<OrderTerminalTypeEnum>[] = OrderTerminalTypes.list()

    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    /**
     * 是否存在已发货时间
     */
    isExistDeliveredTime() {
      return (detail: BatchOrderDetailVo) => {
        return detail.deliveryTime ? 3 : 2
      }
    }

    /**
     * 是否是线下支付
     */
    get isOffLinePay() {
      return (detail: BatchOrderDetailVo) => {
        // debugger
        const payMode = detail.paymentInfo?.payMode ?? null
        console.log('payMode', payMode)
        return payMode === BatchOrderPayModeEnum.Offline_Pay ? true : false
      }
    }

    /**
     * 支付信息-支付方式
     */
    get paymentType() {
      return (detail: BatchOrderDetailVo) => {
        const result = [] as string[]
        if (this.isOffLinePay(detail)) {
          // 线下
          return '集体报名-线下支付-线下转账汇款'
        } else {
          // 线上
          const paymentInfo = detail.paymentInfo
          const terminalName = this.paymentTerminalList.find((el) => el.code === paymentInfo.terminalCode)?.desc ?? ''
          const payChannelName = paymentInfo.payChannelName

          if (terminalName && payChannelName) {
            result.push('集体报名')
            result.push(terminalName)
            // 适配威富通
            if (payChannelName == '威富通支付') {
              result.push('兴业银行(威富通)')
            } else {
              result.push(payChannelName)
            }
          }
          return result.join('-')
        }
      }
    }

    constructor() {
      super()
      this.signUpPage = new UiPage(this.pageSignUpList, this.pageSignUpList)
    }

    /**
     * 批次单状态
     */
    get batchOrderStatus() {
      return this.batchOrderDetail.batchInfo.orderStatus
    }

    /**
     * 批次单状态名称
     */
    get batchOrderStatusName() {
      return (status: BatchOrderTradeStatusEnum) => {
        return this.batchOrderStatusList.find((el) => el.code === status)?.desc || null
      }
    }

    /**
     * 是否是【待下单】状态
     */
    get isWaitPlaceOrder() {
      return this.batchOrderStatus === BatchOrderTradeStatusEnum.Wait_Place_Order ? true : false
    }

    /**
     * 是否是【下单中、待支付】状态
     */
    get isPlacingOrderOrWaitPay() {
      return this.batchOrderStatus === BatchOrderTradeStatusEnum.Placing_Order ||
        this.batchOrderStatus === BatchOrderTradeStatusEnum.Wait_Pay
        ? true
        : false
    }

    /**
     * 是否是【支付中】状态
     */
    get isPaying() {
      return this.batchOrderStatus === BatchOrderTradeStatusEnum.Paying ? true : false
    }

    /**
     * 是否是【开通中】状态
     */
    get isOpening() {
      return this.batchOrderStatus === BatchOrderTradeStatusEnum.Opening ? true : false
    }

    /**
     * 是否是【交易成功】状态
     */
    get isPaySuccess() {
      return this.batchOrderStatus === BatchOrderTradeStatusEnum.Pay_Success ? true : false
    }

    /**
     * 是否是【交易关闭中】状态
     */
    get isClosingPay() {
      return this.batchOrderStatus === BatchOrderTradeStatusEnum.Closing_Pay ? true : false
    }

    /**
     * 是否是【交易关闭】状态
     */
    get isClosePay() {
      return this.batchOrderStatus === BatchOrderTradeStatusEnum.Close_Pay ? true : false
    }

    /**
     * 发票类型
     */
    get invoiceType() {
      return (item: BatchOrderDetailInvoiceInfoVo) => {
        return this.invoiceTypeList.find((el) => el.code === item.invoiceType)?.desc || null
      }
    }

    /**
     * 发票抬头类型
     */
    get invoiceTitleType() {
      return (item: BatchOrderDetailInvoiceInfoVo) => {
        return this.invoiceTitleTypeList.find((el) => el.code === item.invoiceTitleType)?.desc || null
      }
    }

    /**
     * 发票状态
     */
    get invoiceStatus() {
      return (item: BatchOrderDetailInvoiceInfoVo) => {
        return this.invoiceStatusList.find((el) => el.code === item.invoiceStatus)?.desc || null
      }
    }

    /**
     * 发票状态样式
     */
    get invoiceStatusStyle() {
      return (item: BatchOrderDetailInvoiceInfoVo) => {
        return this.invoiceStatusMap.get(item.invoiceStatus) || null
      }
    }

    /**
     * 是否展示发票额外信息
     */
    get isShowInvoiceAdditionalInfo() {
      return (item: BatchOrderDetailInvoiceInfoVo) => {
        return item.bankName || item.accountNo || item.registerAddress || item.registerPhone ? true : false
      }
    }

    /**
     * 配送方式
     */
    get deliveryWay() {
      return (item: BatchOrderDetailVo) => {
        return this.deliveryWayList.find((el) => el.code === item.deliveryInfo.deliveryWay)?.desc || null
      }
    }

    /**
     * 是否是邮寄
     */
    get isCourier() {
      return this.batchOrderDetail.deliveryInfo.deliveryWay === BatchOrderDeliveryWayEnum.Courier ? true : false
    }

    get isSelfFetched() {
      return this.batchOrderDetail.deliveryInfo.deliveryWay === BatchOrderDeliveryWayEnum.Self_Fetched ? true : false
    }

    /**
     *
     */
    get mfsImage() {
      return (url: string) => {
        return DataResolve.getMFSImage(url)
      }
    }

    async activated() {
      this.uiConfig.loading.pageLoading = true
      this.init()
      await this.getBatchOrderDetail()
      // 待下单状态查询上传报名列表
      if (this.isWaitPlaceOrder) {
        // await this.findCollectiveSignupMetaSchema()
        await this.searchSignUpBase()
      }
      this.purchaseListRef?.searchBase()
      this.uiConfig.loading.pageLoading = false
    }

    /**
     * 数据初始化
     */
    init() {
      this.batchOrderNo = this.$route.params?.id ?? ''
      if (this.$route.query.type) {
        this.activeName = this.$route.query.type as string
      }
    }

    /**
     * 获取批次单具体信息
     */
    async getBatchOrderDetail() {
      if (this.isFxlogin && this.isHadFxAbility) {
        this.batchOrderDetail = await this.queryRemote.queryFxBatchOrderDetail(this.batchOrderNo)
      } else {
        this.batchOrderDetail = await this.queryRemote.queryBatchOrderDetail(this.batchOrderNo)
      }
      BatchOrderDetailModule.clearBatchOrderDetail()
      BatchOrderDetailModule.setBatchOrderDetail(this.batchOrderDetail)
      console.log('batchOrderDetail', this.batchOrderDetail)
    }

    async findCollectiveSignupMetaSchema() {
      try {
        await this.queryRemote.findCollectiveSignupMetaSchema()
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      }
    }

    /**
     * 查询上传列表
     */
    async searchSignUpBase() {
      this.signUpPage.pageNo = 1
      await this.pageSignUpList()
    }

    /**
     * 查询上传列表
     */
    async pageSignUpList() {
      this.signUpQuery.loading = true
      try {
        this.signUpQueryParams = this.getSignUpQueryParams()
        console.log('signUpQueryParams', this.signUpQueryParams)
        this.signUpList = [] as BatchOrderSignUpListDetailVo[]
        this.signUpList = await this.queryRemote.queryBatchOrderSignUpList(this.signUpPage, this.signUpQueryParams)
      } catch (e) {
        this.$message.error(e)
      } finally {
        this.signUpQuery.loading = false
      }
    }

    /**
     * 获取查询参数
     */
    getSignUpQueryParams() {
      const signUpQueryParams = new QueryBatchOrderSignUpListVo()
      signUpQueryParams.batchOrderNo = this.batchOrderNo ?? undefined
      signUpQueryParams.userAccount = this.userAccount ?? undefined
      if (DataResolve.isWeightyArr(this.schemeList)) {
        signUpQueryParams.schemeName = this.schemeList[0].commoditySkuName ?? undefined
      } else {
        signUpQueryParams.schemeName = undefined
      }
      return signUpQueryParams
    }

    /**
     * 重置条件
     */
    async resetSignUpQueryParams() {
      this.schemeList = new Array<HasSelectCommodityMode>()
      this.userAccount = null
      await this.searchSignUpBase()
    }

    /**
     * 查看导入任务
     */
    async viewImportTask() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        this.viewImportTaskRef.batchOrderNo = this.batchOrderDetail.batchOrderNo
        await this.viewImportTaskRef.searchBase()
        this.uiConfig.dialog.viewImportTaskVisible = true
      } catch (e) {
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: BatchOrderSignUpListDetailVo, type: string): string {
      if (row.skuValueNameProperty[type]?.skuPropertyName) {
        const value = row.skuValueNameProperty[type].skuPropertyName
        const valuesArr = value.split('/'),
          lastIndex = valuesArr.length - 1
        return type === 'trainingMajor' && !row.isSocietyIndustry ? valuesArr[lastIndex] : value
      }
      return ''
    }

    /**
     * 复制运单号并查询
     */
    copyDeliveryNo(text: string, event: MouseEvent) {
      // TODO
      const clipboard = new Clipboard(event.target as Element, {
        text: () => text
      })
      clipboard.on('success', () => {
        clipboardSuccess()
        clipboard.destroy()
      })
      clipboard.on('error', () => {
        clipboardError()
        clipboard.destroy()
      })
      ;(clipboard as any).onClick(event)
    }
  }
</script>

<style scoped></style>
