import { Vue } from 'vue-property-decorator'
import { cloneDeep } from 'lodash'
// 判断是否为空
export const isEmpty = function(val: any) {
  // null or undefined
  if (val == null) return true

  if (typeof val === 'boolean') return false

  if (typeof val === 'number') return false

  if (val instanceof Error) return val.message === ''

  switch (Object.prototype.toString.call(val)) {
    // String or Array
    case '[object String]':
    case '[object Array]':
      return !val.length

    // Map or Set or File
    case '[object File]':
    case '[object Map]':
    case '[object Set]': {
      return !val.size
    }
    // Plain Object
    case '[object Object]': {
      return !Object.keys(val).length
    }
  }

  return false
}
export function isArray(obj: any) {
  return Object.prototype.toString.call(obj) === '[object Array]'
}
export function isObject(obj: any) {
  return Object.prototype.toString.call(obj) === '[object Object]'
}

// 如果为空返回undefined
export function returnValue(val: any) {
  if (isEmpty(val)) {
    return undefined
  } else {
    return val
  }
}

/**
 * @param // 有个弊端就是 keyList 不传的情况下 main = {a:1}, sub = {b:2}
 * 经过方法赋值之后 会变成 main = {a:1,b:2}
 * @main => 需要赋值的对象 实参
 * @sub => 目标源
 * @keyList 如果出现特殊情况 比如 main = { a:1, b:2} sub = {c:3}的时候，我们想要把sub.c 赋值给 sub.a
 * 就需要 keyList 这个参数了 传参为：keyList：{c:'a'}
 * 然后我们会将 main[keyList.c] 即(main.a) 进行赋值 使他等于 sub.c
 */
export function mapKeyValue<T>(main: T, sub?: { [key: string]: any }, keyList?: { [key: string]: string }): T {
  let copy
  if (sub) {
    copy = cloneDeep(sub)
  } else {
    copy = cloneDeep(main)
  }
  const arr = Object.keys(copy)
  for (const i of arr) {
    // 根据情况进行赋值操作
    if (keyList) {
      // 当keyList下有这个键值对的时候进行赋值操作
      // keyList[i] && (main[keyList[i]] = returnValue(copy[i]))
      keyList[i] && Vue.set(main as any, keyList[i], returnValue(copy[i]))
    } else {
      Object.prototype.hasOwnProperty.call(main, i) && Vue.set(main as any, i, returnValue(copy[i]))
    }
  }
  return main
}

/**
 * 获取工种级联选择器的值  单选   数组转字符串
 */
export function getCascaderValueByRadio(val: string[]) {
  if (!val) {
    return ''
  }

  return val[val.length - 1]
}

/**
 * 获取工种级联选择器的值  多选   转数组
 */
export function getCascaderValueByMultiple(val: string[][]) {
  if (!val) {
    return []
  }
  return val.map(el => {
    return el[el?.length - 1]
  })
}

/**
 * 获取工种级联选择器的值  多选   转path路径
 */
export function getCascaderValueByMultipleByPath(val: string[][]) {
  if (!val) {
    return []
  }
  return val.map(el => {
    return `/${el[el.length - 2]}/${el[el.length - 1]}`
  })
}
