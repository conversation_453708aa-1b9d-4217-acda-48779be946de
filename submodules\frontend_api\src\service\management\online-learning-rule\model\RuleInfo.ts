import SchemeSpecialRuleItem from './SchemeSpecialRuleItem'

export default class RuleInfo {
  /**
   * 不包含培训方案，方案id
   */
  schemeIds: Array<string> = []
  /**
   * 删除培训方案，方案id
   * 用于比对删除
   */
  initSchemeIds: Array<string> = []
  /**
   * 指定方案设置特殊规则
   */
  schemeSpecialRuleList = new Array<SchemeSpecialRuleItem>()
  /**
   * 指定方案设置特殊规则
   * 用于比对
   */
  compareSpecialRuleList = new Array<SchemeSpecialRuleItem>()
  /**
   * 删除指定方案设置特殊规则，规则id
   * 用于比对删除
   */
  removeRuleIds: Array<string> = []
}
