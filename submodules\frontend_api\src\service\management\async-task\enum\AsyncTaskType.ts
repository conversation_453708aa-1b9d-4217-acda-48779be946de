import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum AsyncTaskTypeEnum {
  IMPORT_STUDENT_CLASS = 1,
  IMPORT_STUDENT = 2,
  COURSEBAG_IMPORT = 3,
  QUESTION_IMPORT = 4,
  SINGLE_ELEC_INVOICE = 5,
  SINGLE_SPECIAL_INVOICE = 6,
  SINGLE_DELIVERY_INVOICE = 7,
  COLLECTIVE_ELEC_INVOICE = 8,
  COLLECTIVE_SPECIAL_INVOICE = 9,
  COLLECTIVE_DELIVERY_INVOICE = 10
}

/**
 * @description 密码类型
 */
class AsyncTaskType extends AbstractEnum<AsyncTaskTypeEnum> {
  static enum = AsyncTaskTypeEnum
  constructor(status?: AsyncTaskTypeEnum) {
    super()
    this.current = status
    this.map.set(AsyncTaskTypeEnum.IMPORT_STUDENT_CLASS, '导入学员并开班')
    this.map.set(AsyncTaskTypeEnum.IMPORT_STUDENT, '导入学员')
    this.map.set(AsyncTaskTypeEnum.COURSEBAG_IMPORT, '课程包导入任务')
    this.map.set(AsyncTaskTypeEnum.QUESTION_IMPORT, '试题导入任务')
    this.map.set(AsyncTaskTypeEnum.SINGLE_ELEC_INVOICE, '个人报名增值税电子普通发票（线下开票）')
    this.map.set(AsyncTaskTypeEnum.SINGLE_SPECIAL_INVOICE, '个人报名增值税专用发票')
    this.map.set(AsyncTaskTypeEnum.SINGLE_DELIVERY_INVOICE, '个人报名发票配送')
    this.map.set(AsyncTaskTypeEnum.COLLECTIVE_ELEC_INVOICE, '集体报名增值税电子普通发票（线下开票）')
    this.map.set(AsyncTaskTypeEnum.COLLECTIVE_SPECIAL_INVOICE, '集体报名增值税专用发票')
    this.map.set(AsyncTaskTypeEnum.COLLECTIVE_DELIVERY_INVOICE, '集体报名发票配送')
  }
}

export default new AsyncTaskType()
