import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * @description 方案sku培训属性请求
 */
class SkuPropertyRequest {
  /**
   * 年度
   */
  year = ''
  /**
   * 地区
   */
  region: string[] = []
  /**
   * 行业
   */
  industry = ''
  /**
   * 科目类型
   */
  subjectType = ''
  /**
   * 技术等级
   */
  technicalGrade = ''
  /**
   * 培训类别
   */
  trainingCategory = ''
  /**
   * 培训专业
   */
  trainingMajor = ''
  /**
   * 培训对象
   */
  trainingObject = ''
  /**
   * 岗位类别
   */
  positionCategory = ''
  /**
   * 技术等级
   */
  jobLevel = ''
  /**
   * 学段
   */
  learningPhase = ''
  /**
   * 学科
   */
  discipline = ''
  /**
   * 证书类型
   */
  certificatesType = ''
  /**
   * 执业类别
   */
  practitionerCategory = ''
  /**
   * 主/增项
   */
  mainOrAdditionalItem = ''
  /**
   * 培训形式
   */
  trainingMode: TrainingModeEnum = null
}

export default SkuPropertyRequest
