import QueryBatchOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderListVo'
import { Response } from '@hbfe/common'
import MsDataExportBackstage from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import fxnlQuery from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import MutationBatchOrderExportBase from '@api/service/management/trade/batch/order/mutation/MutationBatchOrderExportBase'
/**
 * @description
 */
class MutationBatchOrderExport extends MutationBatchOrderExportBase {
  exportParams: QueryBatchOrderListVo = new QueryBatchOrderListVo()

  /**
   * 导出批次单列表
   */
  async doBatchOrderExport(): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await this.exportParams.to()
    const response = await MsDataExportBackstage.exportBatchOrderInServicer({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
  /**
   * 导出批次单列表（分销）
   */
  async doFxBatchOrderExport(): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await this.exportParams.to()
    const response = await fxnlQuery.exportBatchOrderInDistributor({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }

  /**
   * 导出批次单明细
   */
  async exportBatchOrderDetailInServicer(): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await this.exportParams.to()
    const response = await MsDataExportBackstage.exportBatchOrderDetailInServicer({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
  /**
   * 导出批次单明细（分销）
   */
  async exportFxBatchOrderDetailInServicer(): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await this.exportParams.to()
    const response = await fxnlQuery.exportBatchOrderDetailInDistributor({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
}

export default MutationBatchOrderExport
