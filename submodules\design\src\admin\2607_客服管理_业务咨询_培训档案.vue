<template>
  <el-main>
    <el-card shadow="never" class="m-card is-bg is-overflow-hidden">
      <div class="f-plr20 f-pt20">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="姓名">
                <el-input v-model="input" clearable placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="证件号">
                <el-input v-model="input" clearable placeholder="请输入证件号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="手机号">
                <el-input v-model="input" clearable placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="订单号">
                <el-input v-model="input" clearable placeholder="请输入订单号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="4" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--人员信息-->
        <el-collapse v-model="activeNames5" @change="handleChange" class="m-collapse no-border">
          <el-collapse-item name="1">
            <div slot="title" class="m-tit">
              <span class="tit-txt">人员信息</span>
            </div>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="240" highlight-current-row class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="姓名" min-width="160" fixed="left">
                <template>张依依</template>
              </el-table-column>
              <el-table-column label="证件号" min-width="200">
                <template>354875965412365896</template>
              </el-table-column>
              <el-table-column label="手机号" min-width="200">
                <template>13003831002</template>
              </el-table-column>
              <el-table-column label="单位地区" min-width="200">
                <template>福建省-福州市-鼓楼区</template>
              </el-table-column>
              <el-table-column label="注册时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-border-top is-sticky">
      <el-tab-pane label="学员信息" name="first">
        <div class="f-p15">详见 2601_客服管理_业务咨询_学员信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="学习内容" name="second">
        <div class="f-p15">详见 2602_客服管理_业务咨询_学习内容.vue</div>
      </el-tab-pane>
      <el-tab-pane label="换班信息" name="third">
        <div class="f-p15">详见 2603_客服管理_业务咨询_换班信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="订单信息" name="fourth">
        <div class="f-p15">详见 2604_客服管理_业务咨询_订单信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="发票信息" name="five">
        <div class="f-p15">详见 2605_客服管理_业务咨询_发票信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="退款信息" name="six">
        <div class="f-p15">详见 2606_客服管理_业务咨询_退款信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="学习档案" name="seven">
        <div class="f-p15">
          <el-card shadow="never" class="m-card f-mb15">
            <el-row :gutter="16" class="m-query">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="年度">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训方案">
                    <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="考核结果">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="培训方案信息" min-width="300" fixed="left">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    【培训班-选课规则】培训方案名称
                    <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                    <p>
                      <el-tag type="info" class="f-mr10">冻结</el-tag>
                      <el-tag type="warning" class="f-mr10">个人报名</el-tag>
                    </p>
                  </div>
                  <div v-else>
                    【培训班-选课规则】培训方案名称
                    <p>
                      <el-tag type="success" class="f-mr10">有效</el-tag>
                      <el-tag class="f-mr10">集体报名</el-tag>
                    </p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="培训属性" min-width="240">
                <template>
                  <p>2020年 / 人社行业 / 公需科目</p>
                  <p>福建省-福州市</p>
                  <p>工程系列/中学教师</p>
                </template>
              </el-table-column>
              <el-table-column label="学时" min-width="100" align="center">
                <template>15</template>
              </el-table-column>
              <el-table-column label="考核要求" min-width="300">
                <template>
                  <div class="f-flex">
                    <span>1.</span>
                    <div class="f-flex-sub">
                      <p>整体考核学时：60</p>
                      <p>① 每门课程进度=100%且测验达60分</p>
                      <p>② 课程评价：需评价</p>
                    </div>
                  </div>
                  <div class="f-flex">
                    <span>2.</span>
                    <div class="f-flex-sub">
                      <p>考试成绩>=60分</p>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="考核结果" min-width="240">
                <template>
                  <div class="f-flex">
                    <span>1.</span>
                    <div class="f-flex-sub">
                      <p>已获得 0 学时</p>
                      <p>① 班级学习进度=30%</p>
                    </div>
                  </div>
                  <div class="f-flex">
                    <span>2.</span>
                    <div class="f-flex-sub">
                      <p>考试成绩=60分</p>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="是否合格" min-width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="danger" class="badge-status">未合格</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">合格</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="合格时间" min-width="180">
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-info f-co f-mr5"></i>
                  <div slot="content">同步数据/模拟数据: 2021.07.23 11:11:11</div>
                </el-tooltip>
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">查阅学时证明</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames5: ['1'],
        activeName: 'seven',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
