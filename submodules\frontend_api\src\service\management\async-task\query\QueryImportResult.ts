import { Page } from '@hbfe/common'
import PageImportResultParam from './vo/PageImportResultParam'
import PageImportResultResponse from './vo/PageImportResultResponse'
import importopen, { MetaProperty } from '@api/ms-gateway/ms-importopen-v1'
import MsTradeQuery, {
  OrderBasicDataRequest,
  OrderResponse,
  OrderSortField,
  OrderSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
export default class QueryImportResult {
  /**
   * 查看导入开通结果列表
   */
  async queryPageImportResult(page: Page, request: PageImportResultParam): Promise<Array<PageImportResultResponse>> {
    //
    const { data } = await importopen.findImportDataWithServicerByPage({ page, request: request.to() })
    page.totalSize = data.totalSize
    page.totalPageSize = data.totalPageSize

    const result = new Array<PageImportResultResponse>()
    data.currentPageData.map((item) => {
      result.push(PageImportResultResponse.from(item))
    })
    return result
  }

  /**
   * 专题管理员-查看导入开通结果列表
   */
  async queryPageImportResultByThemeManager(
    page: Page,
    request: PageImportResultParam
  ): Promise<Array<PageImportResultResponse>> {
    //
    const { data } = await importopen.findImportDataWithSelfByPage({ page, request: request.to() })
    page.totalSize = data.totalSize
    page.totalPageSize = data.totalPageSize

    const result = new Array<PageImportResultResponse>()
    data.currentPageData.map((item) => {
      result.push(PageImportResultResponse.from(item))
    })
    return result
  }
}
