import {
  ReturnOrderResponse,
  SchemeResourceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'

/**
 * @description 批次单退款列表详情
 */
class RefundInfoListDetailVo {
  /**
   * 学员id
   */
  studentId = ''

  /**
   * 学员姓名
   */
  studentName = ''

  /**
   * 学员证件号
   */
  studentAccount = ''

  /**
   * 学员手机号
   */
  studentPhone = ''

  /**
   * 培训方案id
   */
  schemeId = ''

  /**
   * 培训班商品id
   */
  commoditySkuId = ''

  /**
   * 培训班名称
   */
  schemeName = ''
  /**
   * 技术等级
   */
  technicalGrade = ''
  /**
   * 年度
   */
  year = ''
  /**
   * 地区 - 省
   */
  province = ''
  /**
   * 地区 - 市
   */
  city = ''
  /**
   * 地区 - 区县
   */
  county = ''

  /**
   * 学时
   */
  period = 0

  /**
   * 价格
   */
  price = 0

  /**
   * 退款成功时间
   */
  refundSuccessTime = ''

  static from(response: ReturnOrderResponse): RefundInfoListDetailVo {
    const detail = new RefundInfoListDetailVo()
    detail.studentId = response.subOrderInfo?.orderInfo?.buyer?.userId ?? ''
    detail.commoditySkuId = response.returnCommodity?.commoditySku?.commoditySkuId ?? ''
    detail.schemeName = response.returnCommodity?.commoditySku?.saleTitle ?? ''
    detail.schemeId = (response.returnCommodity?.commoditySku?.resource as SchemeResourceResponse)?.schemeId ?? ''
    detail.period = (response.returnCommodity?.commoditySku?.resource as SchemeResourceResponse)?.period ?? 0
    detail.price = response.returnCommodity?.commoditySku?.price ?? 0
    detail.refundSuccessTime = response.basicData?.returnOrderStatusChangeTime?.returned
    // detail.technicalGrade = response.returnCommodity?.commoditySku?.skuProperty.technicalGrade.skuPropertyValueName
    detail.year = response.returnCommodity?.commoditySku?.skuProperty.year.skuPropertyValueName
    detail.province = response.returnCommodity?.commoditySku?.skuProperty.province.skuPropertyValueName
    detail.city = response.returnCommodity?.commoditySku?.skuProperty.city.skuPropertyValueName
    detail.county = response.returnCommodity?.commoditySku?.skuProperty.county.skuPropertyValueName
    return detail
  }
}

export default RefundInfoListDetailVo
