import {
  PaymentTypeStatisticDto,
  PurchaseChannelStatisticDto,
  RegionOpenReportFormResponse,
  SubOrderStatisticDto
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { cloneDeep } from 'lodash'
export class RegionOpenReportFormResponseVo extends RegionOpenReportFormResponse {
  list = new Array<PurchaseChannelStatisticDto>()
  parentId: string
  async fillData() {
    if (!this.summaryInfo) {
      this.summaryInfo = new SubOrderStatisticDto()
    }
    if (!this.purchaseChannelStatisticInfoList) {
      this.purchaseChannelStatisticInfoList = []
    }
    const channels = [1, 2, 3]
    for (const channel of channels) {
      this.fillPurchaseChannelStatisticInfoList(channel)
    }
    this.purchaseChannelStatisticInfoList = cloneDeep(this.list)
  }
  /**
   * 购买渠道 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
   @see PurchaseChannelTypes
   */
  private fillPurchaseChannelStatisticInfoList(purchaseChannel: number) {
    let findChannel = this.purchaseChannelStatisticInfoList.find(item => item.purchaseChannel == purchaseChannel)
    if (!findChannel) {
      findChannel = new PurchaseChannelStatisticDto()
      findChannel.purchaseChannel = purchaseChannel
      findChannel.paymentTypeStatisticInfoList = []
    }
    if (findChannel.purchaseChannel == 1) {
      if (!findChannel.paymentTypeStatisticInfoList.find(item => item.paymentType == 1)) {
        findChannel.paymentTypeStatisticInfoList.push(this.addPayment(1))
      }
    } else if (findChannel.purchaseChannel == 2) {
      if (!findChannel.paymentTypeStatisticInfoList.find(item => item.paymentType == 1)) {
        findChannel.paymentTypeStatisticInfoList.push(this.addPayment(1))
      }
      if (!findChannel.paymentTypeStatisticInfoList.find(item => item.paymentType == 2)) {
        findChannel.paymentTypeStatisticInfoList.push(this.addPayment(2))
      }
    } else if (findChannel.purchaseChannel == 3) {
      if (!findChannel.paymentTypeStatisticInfoList.find(item => item.paymentType == 1)) {
        // console.log(findChannel.paymentTypeStatisticInfoList, 'findChannel.paymentTypeStatisticInfoList')
        findChannel.paymentTypeStatisticInfoList.push(this.addPayment(1))
      }
    }
    this.list.push(findChannel)
  }
  private addPayment(paymentType: number) {
    const paymentModel = new PaymentTypeStatisticDto()
    paymentModel.paymentType = paymentType
    paymentModel.statisticInfo = new SubOrderStatisticDto()
    return paymentModel
  }
}
