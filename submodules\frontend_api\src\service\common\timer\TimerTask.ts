const { EventEmitter } = require('events')

export enum Events {
  start = 'start',
  timeupdate = 'timeupdate',
  stop = 'stop'
}

/**
 * 时间定时器
 */
export default class TimerTask extends EventEmitter {
  static Events = Events
  id: any
  // 毫秒
  duration = 1000
  // 秒
  time: number

  constructor(duration = 1000) {
    super()
    this.duration = duration
  }

  start() {
    if (!this.duration) {
      return
    }
    this.id = setInterval(() => {
      this.time += Math.ceil(this.duration / 1000)
      this.emit(TimerTask.Events.timeupdate, this.time)
    }, this.duration)
    this.emit(TimerTask.Events.start)
  }

  stop() {
    if (!this.duration) {
      return
    }
    clearInterval(this.id)
    this.id = null
    this.emit(TimerTask.Events.stop)
  }
}
