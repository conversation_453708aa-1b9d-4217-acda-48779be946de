import Region from '@api/service/management/common/Region'
import { cloneDeep } from 'lodash'
import AssembleTree from '../../utils/AssembleTree'
import RegionTreeVo from './vo/RegionTreeVo'

/**
 * 服务地区列表(分销商)
 */
class QueryBusinessRegionInDistribution {
  /**
   * 物理地区缓存
   */
  private countrywideRegionMap = new Map<string, RegionTreeVo>()
  /**
   * 服务地区id缓存
   */
  private serviceRegionMap = new Map<string, Array<string>>()

  /**
   * 查询网校服务地区列表(分销商) ------------  新口
   * @param type 0 行业地区 1服务地区
   * @param isRegistered 是否注册使用
   */
  async getServiceOrIndustry(type: number, isRegistered?: boolean) {
    const res = await Region.getServiceOrIndustryRegionInDistribution(type)
    const regionArr = res.data.map(item => RegionTreeVo.fromBusiness(item))
    const assembleTree = new AssembleTree<RegionTreeVo>(regionArr, 'id', 'parentId', 'children')
    const tree = assembleTree.assembleTree()
    if (isRegistered) {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].id === '100000') {
          // 删除全国的数据
          tree.splice(i, 1)
          i--
        }
      }
    }
    return tree
  }

  /**
   * 获取服务地区id
   */
  async getServiceRegionIds() {
    if (!this.serviceRegionMap.has('serviceRegion')) {
      const res = await Region.getServiceOrIndustryRegionInDistribution(1)
      const ids = new Array<string>()
      if (res.status.isSuccess()) {
        res.data.forEach(el => {
          ids.push(el.code)
        })
      }
      this.serviceRegionMap.set('serviceRegion', ids)
    }
    return this.serviceRegionMap.get('serviceRegion')
  }

  /**
   * UI 展示地区树使用
   * @param tree 完整的地区
   * @param regionCodes 需要展示的地区code
   * @param isCustomer 是否在学员域展示
   */
  filterRegionTree(tree: RegionTreeVo[], regionCodes: string[], isCustomer?: boolean): RegionTreeVo[] {
    const copyTree = cloneDeep(tree)
    const filteredTree: RegionTreeVo[] = []
    for (const node of copyTree) {
      const isMatched = regionCodes.includes(node.id)
      if (!isCustomer) {
        node.disabled = !(regionCodes.includes(node.id) || regionCodes.includes(node.parentId))
      }

      if (isMatched) {
        // 当前节点匹配指定地区代码，保留该节点及其子节点
        filteredTree.push(node)
      } else if (node.children) {
        // 当前节点不匹配指定地区代码，递归处理子节点
        const filteredChildren = this.filterRegionTree(node.children, regionCodes, isCustomer)

        if (filteredChildren.length > 0) {
          // 子节点有匹配的节点，保留当前节点及其子节点
          const newNode = { ...node }
          newNode.children = filteredChildren
          filteredTree.push(newNode)
        }
      }
    }

    return filteredTree
  }

  /**
   * 通过code获取详情
   * @param code 字典code
   */
  getCountrywideRegionCodeDetail(code: string): RegionTreeVo {
    return this.countrywideRegionMap.get(code)
  }

  /**
   * 批量通过code获取详情
   * @param codeList 字典codeList
   */
  getCountrywideRegionList(codeList: Array<string>): Array<RegionTreeVo> {
    if (!codeList.length) return
    const list = codeList.map(item => {
      return this.getCountrywideRegionCodeDetail(item)
    })
    return list
  }
}
export default new QueryBusinessRegionInDistribution()
