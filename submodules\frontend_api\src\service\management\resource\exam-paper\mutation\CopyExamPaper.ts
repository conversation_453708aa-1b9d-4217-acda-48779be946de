import { ResponseStatus } from '@hbfe/common'
import ExamPaperGateway from '@api/ms-gateway/ms-examextraction-v1'
import ExamPaper from './vo/common/ExamPaper'

class CopyExamPaper extends ExamPaper {
  /**
   * @description: 复制试卷
   */
  async doCopyExamPaper(): Promise<ResponseStatus> {
    const params = this.examPaperParams.toCopyDto()
    const { status } = await ExamPaperGateway.createPaperPublishConfigure(params)
    return status
  }
}

export default CopyExamPaper
