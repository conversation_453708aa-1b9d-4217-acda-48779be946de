import PopQuestionTypeEnum from '@api/service/management/resource/pop-question/enum/PopQuestionTypeEnum'

class CreatePopQuestionDTO {
  coursewareId: string
  coursewareName: string
  mediaName: string
  mediaId: string

  get mediaDuration(): number {
    return this._mediaDuration
  }

  set mediaDuration(value: number) {
    this._mediaDuration = value
  }

  private _mediaDuration: number

  pointHour: number
  pointMinutes: number
  pointSecond: number
  type: PopQuestionTypeEnum
  title: string
}

export default CreatePopQuestionDTO
