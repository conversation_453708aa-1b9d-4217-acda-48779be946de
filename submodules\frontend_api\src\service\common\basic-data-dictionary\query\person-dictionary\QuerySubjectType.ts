import BasicDataGateway, {
  SchoolTrainingPropertyQueryRequest,
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { listIndustryPropertyByOnlineSchoolV2 } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage/graphql-importer'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import SubjectTypeVo from '@api/service/common/basic-data-dictionary/query/vo/SubjectTypeVo'
import Context from '@api/service/common/context/Context'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import Vue from 'vue'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'

/**
 * @description 科目类型请求
 */
export class SubjectTypeRequest {
  /**
   * 行业id
   */
  industryId = ''
  /**
   * 科目类型id集合
   */
  subjectTypeIdList: string[] = []

  constructor(industryId?: string, subjectTypeIdList?: string[]) {
    this.industryId = industryId
    this.subjectTypeIdList = subjectTypeIdList
  }
}

class QuerySubjectType {
  /**
   * 科目类型列表
   */
  subjectTypeList: Array<SubjectTypeVo>

  /**
   * 科目类型列表缓存 key:industryPropertyId_subject.propertyId
   */
  subjectTypeCache: { [key: string]: SubjectTypeVo } = {}

  /**
   * 查询科目类型列表
   */
  async querySubjectTypeList(industryPropertyId: string, industryId: string) {
    // todo 是否需要缓存
    const res = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
      industryPropertyId: industryPropertyId,
      categoryCode: IndustryPropertyCodeEnum.PERSON_SUBJECT_TYPE,
      industryId
    })
    if (res.status.isSuccess()) {
      const currentIndustry = await BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustry.getIndustryByIdList(
        [industryPropertyId]
      )
      this.setSubjectTypeCache(currentIndustry[0].id, res.data)
      this.subjectTypeList = res.data
    }
    return res.status
  }

  /**
   * 获取科目类型详情列表
   */
  async getSubjectTypeByIdList(param: { industryId: string; subjectTypeIdList: Array<string> }) {
    const subjectTypeList = new Array<SubjectTypeVo>()
    const idList = new Array<string>()

    param.subjectTypeIdList?.forEach(id => {
      const subjectType = this.subjectTypeCache[`${param.industryId}_${id}`]
      if (subjectType) {
        subjectTypeList.push(subjectType)
      } else {
        // 不在缓存中时会发起请求获取
        idList.push(id)
      }
    })
    if (idList.length) {
      const list = await this.querySubjectTypeByIdList({
        industryId: param.industryId,
        subjectTypeIdList: idList
      })
      subjectTypeList.push(...list)
    }
    return subjectTypeList
  }

  /**
   * 查询科目类型详情通过idList
   */
  async querySubjectTypeByIdList(param: { industryId: string; subjectTypeIdList: Array<string> }) {
    const schoolTrainingPropertyQueryRequest = new SchoolTrainingPropertyQueryRequest()
    schoolTrainingPropertyQueryRequest.propertyId = param.subjectTypeIdList
    schoolTrainingPropertyQueryRequest.industryId = param.industryId
    schoolTrainingPropertyQueryRequest.schoolId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
    const res = await BasicDataGateway.listIndustryPropertyByOnlineSchoolV2(schoolTrainingPropertyQueryRequest)
    if (res.status.isSuccess()) {
      const list = res.data?.map(SubjectTypeVo.from)
      this.setSubjectTypeCache(param.industryId, list)
      return list
    }
    return new Array<SubjectTypeVo>()
  }

  /**
   * 设置科目类别的缓存
   */
  private setSubjectTypeCache(industryId: string, subjectList: Array<SubjectTypeVo>) {
    subjectList?.forEach(subject => {
      if (!this.subjectTypeCache[`${industryId}_${subject.propertyId}`]) {
        Vue.set(this.subjectTypeCache, `${industryId}_${subject.propertyId}`, subject)
      }
    })
  }

  /**
   * 批量查询科目类型详情
   * @param request
   */
  async batchGetSubjectTypeByIdList(request: SubjectTypeRequest[]): Promise<SubjectTypeVo[]> {
    const result = [] as SubjectTypeVo[]
    const params = request.map(item => {
      const opt = new SchoolTrainingPropertyQueryRequest()
      opt.propertyId = item.subjectTypeIdList
      opt.industryId = item.industryId
      opt.schoolId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
      return opt
    })
    const reWriteGQL = new RewriteGraph<TrainingPropertyResponse[], SchoolTrainingPropertyQueryRequest>(
      BasicDataGateway._commonQuery,
      listIndustryPropertyByOnlineSchoolV2
    )
    await reWriteGQL.request(params)
    for (const [key, value] of reWriteGQL.itemMap.entries()) {
      if (value && value.length) {
        value.forEach(el => {
          result.push(SubjectTypeVo.from(el))
        })
      }
    }
    return result
  }
}

export default new QuerySubjectType()
