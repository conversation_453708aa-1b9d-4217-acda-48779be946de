import QueryBatchRefundBase from '@api/service/management/trade/batch/order/query/vo/QueryBatchRefundBase'
import { Page, Response, ResponseStatus } from '@hbfe/common'
import QueryBatchRefundListParamVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchRefundListParamVo'
import TradeQuery, {
  BatchReturnOrderSortRequest,
  ReturnSortRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import BatchRefoundListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchRefoundListDetailVo'
import UserModule from '@api/service/management/user/UserModule'
import CollectiveManagerInfoVo from '@api/service/management/user/query/manager/vo/CollectiveManagerInfoVo'
import CollectiveManagerQueryIdVo from '@api/service/management/user/query/manager/vo/CollectiveManagerQueryIdVo'
import { FALSE } from 'sass'
import DataExportBackstage from '@api/platform-gateway/jxjy-data-export-gateway-backstage'

export default class QueryBatchRefundInTrainingChannel extends QueryBatchRefundBase {
  /**
   * 退款单数
   */
  totalRefoundCount = 0
  /**
   * 退款金额
   */
  totalRefoundAmount = 0

  /**
   * 【集体报名退款】查询列表
   * @param {Page} page - 分页参数
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<BatchRefoundListDetailVo[]>} - 【集体报名订单】列表
   */
  async queryBatchRefoundList(
    page: Page,
    queryParams: QueryBatchRefundListParamVo,
    sortRequest?: Array<BatchReturnOrderSortRequest>
  ): Promise<BatchRefoundListDetailVo[]> {
    const batchReturnOrderRequest = QueryBatchRefundListParamVo.to(queryParams)
    if (queryParams.createdUserId.length) {
      batchReturnOrderRequest.batchOrderInfo.creatorIdList = queryParams.createdUserId
    } else if (queryParams.buyerName || queryParams.buyerAccount) {
      //
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const collectiveManagerQueryIdVo = new CollectiveManagerQueryIdVo()
      collectiveManagerQueryIdVo.userName = queryParams.buyerName
      collectiveManagerQueryIdVo.identity = queryParams.buyerAccount
      const idList = await queryStudentIdList.queryCollectiveManagerIdList(collectiveManagerQueryIdVo)
      if (!idList?.length) {
        page.totalPageSize = 0
        page.totalSize = 0
        return []
      }
      batchReturnOrderRequest.batchOrderInfo.creatorIdList = idList
    }
    const request = {
      page,
      request: batchReturnOrderRequest,
      sortRequest
    }
    const response = await TradeQuery.pageBatchReturnOrderInTrainingChannel(request)
    // 获取批次退货单总数量、退款总金额  不使用同步执行，避免影响获取列表
    const statisticStatus = this.queryBatchRefoundListStatistic(queryParams)
    page.totalSize = response.data.totalSize
    page.totalPageSize = response.data.totalPageSize
    const list = new Array<BatchRefoundListDetailVo>()
    const userIdList = []
    for (let i = 0; i < response.data.currentPageData.length; i++) {
      const element = response.data.currentPageData[i]
      userIdList.push(element.batchOrderInfo.creator.userId)
      list.push(BatchRefoundListDetailVo.from(element))
    }
    const userInfoMap = await this.getUserInfo(userIdList)
    for (let i = 0; i < list.length; i++) {
      const element = list[i]
      if (userInfoMap.has(element.buyerId)) {
        const userInfo = userInfoMap.get(element.buyerId)
        element.buyerName = userInfo.userName
        element.buyerAccount = userInfo.idCard || userInfo.phone
      }
    }
    return list
  }

  /**
   * 【集体报名退款订单】查询列表统计数据 statisticBatchReturnOrderInServicer
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<ResponseStatus>}
   */
  async queryBatchRefoundListStatistic(queryParams: QueryBatchRefundListParamVo): Promise<ResponseStatus> {
    const { data, status } = await TradeQuery.statisticBatchReturnOrderInTrainingChannel(
      QueryBatchRefundListParamVo.to(queryParams)
    )
    this.totalRefoundCount = data?.totalBatchReturnOrderCount
    this.totalRefoundAmount = data?.totalBatchReturnOrderRefundAmount
    return status
  }

  /**
   * 【集体报名退款】导出批次退货单明细
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<BatchRefoundListDetailVo[]>} - 【集体报名订单】列表
   */
  async exportBatchReturnOrderDetailExcelInServicer(
    queryParams: QueryBatchRefundListParamVo,
    sortRequest?: Array<ReturnSortRequest>
  ): Promise<Response<boolean>> {
    const batchReturnOrderRequest = QueryBatchRefundListParamVo.to(queryParams)
    if (queryParams.buyerName || queryParams.buyerAccount) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const collectiveManagerQueryIdVo = new CollectiveManagerQueryIdVo()
      collectiveManagerQueryIdVo.userName = queryParams.buyerName
      collectiveManagerQueryIdVo.idCard = queryParams.buyerAccount
      const idList = await queryStudentIdList.queryCollectiveManagerIdList(collectiveManagerQueryIdVo)
      if (idList.length === 0) {
        batchReturnOrderRequest.batchOrderInfo.creatorIdList = ['-1']
      } else {
        batchReturnOrderRequest.batchOrderInfo.creatorIdList = idList
      }
    }
    const request = {
      request: batchReturnOrderRequest,
      sortRequest
    }
    const response = await DataExportBackstage.exportBatchReturnOrderDetailExcelInTrainingChannel(request)
    return response
  }

  /**
   * 【集体报名退款】导出批次退货单
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<BatchRefoundListDetailVo[]>} - 【集体报名订单】列表
   */
  async exportBatchReturnOrderExcelInServicer(
    queryParams: QueryBatchRefundListParamVo,
    sortRequest?: Array<ReturnSortRequest>
  ): Promise<Response<boolean>> {
    const batchReturnOrderRequest = QueryBatchRefundListParamVo.to(queryParams)
    if (queryParams.buyerName || queryParams.buyerAccount) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const collectiveManagerQueryIdVo = new CollectiveManagerQueryIdVo()
      collectiveManagerQueryIdVo.userName = queryParams.buyerName
      collectiveManagerQueryIdVo.idCard = queryParams.buyerAccount
      const idList = await queryStudentIdList.queryCollectiveManagerIdList(collectiveManagerQueryIdVo)
      if (idList.length == 0) {
        batchReturnOrderRequest.batchOrderInfo.creatorIdList = ['-1']
      } else {
        batchReturnOrderRequest.batchOrderInfo.creatorIdList = idList
      }
    }
    const request = {
      request: batchReturnOrderRequest,
      sortRequest
    }
    const response = await DataExportBackstage.exportBatchReturnOrderExcelInTrainingChannel(request)
    return response
  }
}
