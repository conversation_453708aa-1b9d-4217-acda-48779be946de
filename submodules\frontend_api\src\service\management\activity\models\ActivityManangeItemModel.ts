import {
  LearningExperienceEnum,
  AnswerMethodEnum,
  ApproveMethodEnum,
  ApproveResultEnum
} from '@api/service/management/activity/enum/ActivityEnum'
// 列表项
import Mockjs from 'mockjs'
import {
  StudentLearningExperienceResponse,
  StudentLearningExperienceStatus
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import ActivityManangeDetailModel from '@api/service/management/activity/models/ActivityManangeDetailModel'
export default class ActivityManangeItemModel {
  /**
   * 学习心得id
   */
  id = ''
  /**
   * 培训方案id
   */
  schemeId = ''
  /**
   * 培训方案名称
   */
  schemeName = ''
  /**
   * 主题
   */
  theme = ''
  /**
   * 学习心得类型
   */
  learningExperienceType: LearningExperienceEnum
  /**
   * 参加起始时间
   */
  joinStartTime = ''
  /**
   * 参加结束时间
   */
  joinEndTime = ''
  /**
   * 姓名
   */
  name = ''
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 作答方式
   */
  answerMethod: AnswerMethodEnum = null
  /**
   * 审核方式
   */
  approveMethod: ApproveMethodEnum = null
  /**
   * 审核结果分数
   */
  approveResultScore = 0
  /**
   * 审核结果状态
   */
  approveResult: ApproveResultEnum = null
  /**
   * 详情需要的数据
   */
  detail: ActivityManangeDetailModel = new ActivityManangeDetailModel()
  /**
   * 参训资格id，类型为string
   */
  qualificationId = ''
  /**
   * 模型转化
   * @param dto 后端模型
   */
  static from(response: StudentLearningExperienceResponse): any {
    const vo = new ActivityManangeItemModel()
    vo.schemeId = response.studentLearning?.schemeId
    vo.id = response.studentLearningExperienceId
    vo.theme = response.learningExperienceTopic?.experienceTopicName
    if (response.experienceType === 'COURSE') {
      vo.learningExperienceType = LearningExperienceEnum.COURSE
    } else if (response.experienceType === 'SCHEME') {
      vo.learningExperienceType = LearningExperienceEnum.CLASS
    }
    vo.joinStartTime = response.learningExperienceTopic?.startTime
    vo.joinEndTime = response.learningExperienceTopic?.endTime
    if (response.learningExperienceTopic?.participateType === 'SUBMIT_FILE') {
      vo.answerMethod = AnswerMethodEnum.UPLOAD
    } else if (response.learningExperienceTopic?.participateType === 'EDIT_ONLINE') {
      vo.answerMethod = AnswerMethodEnum.EDIT
    }
    if (response.learningExperienceTopic?.auditType === 'AUTO_AUDIT') {
      vo.approveMethod = ApproveMethodEnum.AUTO
    } else if (response.learningExperienceTopic?.auditType === 'MANUAL_AUDIT') {
      vo.approveMethod = ApproveMethodEnum.ARTIFICIAL
    }
    if (response.status === StudentLearningExperienceStatus.PASS) {
      vo.approveResult = ApproveResultEnum.SUCCESS
    } else if (response.status === StudentLearningExperienceStatus.RETURNED) {
      vo.approveResult = ApproveResultEnum.FAIL
    }
    vo.approveResultScore = response.score
    vo.detail = ActivityManangeDetailModel.from(response)
    vo.qualificationId = response.studentLearning.qualificationId
    return vo
  }
}
