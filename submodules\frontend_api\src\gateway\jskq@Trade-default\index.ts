import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/jskq@Trade-default'

// 枚举

// 类

/**
 * 申请退款信息
 */
export class ApplyRefundRequest {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 子订单号
   */
  subOrderNo?: string
  /**
   * 退款原因ID
   */
  reasonId?: string
  /**
   * 退款原因
   */
  reason?: string
}

/**
 * 索要发票信息
 */
export class InvoiceRequest {
  /**
   * [必填]发票抬头
   */
  title?: string
  /**
   * [必填]发票抬头类型
@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTitleTypeConst
   */
  titleType: number
  /**
   * 发票类型
@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTypeConst
   */
  type: number
  /**
   * 纳税人识别号，发票抬头类型为企业时必填
   */
  taxpayerNo?: string
  /**
   * 地址
   */
  address?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 开户行
   */
  bankName?: string
  /**
   * 账号
   */
  account?: string
  /**
   * 发票邮箱
   */
  email?: string
  /**
   * 发票备注
   */
  remark?: string
  /**
   * 发票的object信息集合
   */
  objectList?: Array<InvoiceObj>
  /**
   * 是否电子发票
   */
  electron: boolean
  /**
   * 是否非税发票
   */
  noTaxBill: boolean
}

export class InvoiceObj {
  objectType?: string
  objectId?: string
}

/**
 * 订单创建信息
 */
export class OrderCreateRequest {
  /**
   * 销售渠道ID
   */
  marketingChannelId?: string
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceRequest
  /**
   * 子订单集合
   */
  subOrderList?: Array<SubOrderItemCreateRequest>
}

/**
 * 订单发票更新信息
 */
export class OrderInvoiceUpdateRequest {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceRequest
}

export class OrderPayInfoRequest {
  orderNo?: string
  receiveAccountId?: string
  pageUrl?: string
  extParams?: Map<string, string>
}

export class SubOrderItemCreateRequest {
  /**
   * 商品ID
   */
  commodityId?: string
  /**
   * 是否需要发票
   */
  needBill: boolean
  /**
   * 购买数量
   */
  purchaseQuantity: number
}

/**
 * 索要发票信息
 */
export class InvoiceRequest1 {
  /**
   * [必填]发票抬头
   */
  title: string
  /**
   * [必填]发票抬头类型
@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTitleTypeConst
   */
  titleType: number
  /**
   * 发票类型
@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTypeConst
   */
  type: number
  /**
   * 纳税人识别号，发票抬头类型为企业时必填
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账号
   */
  account: string
  /**
   * 发票邮箱
   */
  email: string
  /**
   * 发票备注
   */
  remark: string
  /**
   * 发票的object信息集合
   */
  objectList: Array<InvoiceObj1>
  /**
   * 是否电子发票
   */
  electron: boolean
  /**
   * 是否非税发票
   */
  noTaxBill: boolean
}

export class InvoiceObj1 {
  objectType: string
  objectId: string
}

/**
 * 订单
 */
export class OrderResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 买家ID
   */
  buyerId: string
  /**
   * 买家名称
   */
  buyerName: string
  /**
   * 卖家ID
   */
  sellerId: string
  /**
   * 卖家名称
   */
  sellerName: string
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 发票索要信息
   */
  invoiceInfo: InvoiceRequest1
  /**
   * 子订单列表
   */
  subOrderList: Array<SubOrderItemResponse>
  /**
   * 订单状态
@see com.fjhb.platform.core.v1.order.api.constants.OrderStatusConst
   */
  orderStatus: number
  /**
   * 订单总金额
   */
  totalAmount: number
  /**
   * 创建方式
@see com.fjhb.platform.core.v1.order.api.constants.OrderCreateTypeConst
   */
  createType: number
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 退款单拓展信息
 */
export class RefundOrderExtResponse {
  /**
   * 业务自定义的objectType
   */
  objectType: string
  /**
   * 业务自定义的objectId
   */
  objectId: string
  /**
   * 备注
   */
  remark: string
}

/**
 * 退款单
 */
export class RefundOrderResponse {
  /**
   * 退款单号
   */
  refundServiceNo: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 主订单号
   */
  refundMasterOrderNo: string
  /**
   * 子订单号
   */
  refundSubOrderNo: string
  /**
   * 是否为虚拟物品
   */
  refundProductVirtual: string
  /**
   * 退款金额
   */
  refundAmount: number
  /**
   * 退款状态
@see com.fjhb.platform.core.v1.order.api.constants.RefundStatusConst
   */
  refundStatus: number
  /**
   * 退款申请人ID
   */
  applyUserId: string
  /**
   * 退款申请时间
   */
  applyTime: string
  /**
   * 退款原因ID
   */
  reasonId: string
  /**
   * 退款原因描述
   */
  reason: string
  /**
   * 审核时间
   */
  auditTime: string
  /**
   * 确认退款时间(即点击放款的时间)
   */
  affirmRefundTime: string
  /**
   * 退款成功或退款失败的时间(即退款单处于最终退款结果的时间)
   */
  finishTime: string
  /**
   * 创建类型
@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderCreateTypeConst
   */
  createType: number
  /**
   * 退款单类型
@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderTypeConst
   */
  type: number
  /**
   * 退款方式
@see com.fjhb.platform.core.v1.order.api.constants.RefundModeConst
   */
  mode: number
  /**
   * 取消退款原因描述
   */
  cancelReason: string
  /**
   * 取消退款申请时间
   */
  cancelApplyDate: string
  /**
   * 拒绝申请原因描述
   */
  refuseApplyDesc: string
  /**
   * 拒绝退款原因描述
   */
  refuseRefundDesc: string
  /**
   * 备注
   */
  remark: string
  /**
   * 操作员ID
   */
  operatorId: string
  /**
   * 拓展信息集合
   */
  extList: Array<RefundOrderExtResponse>
}

/**
 * 子订单的外链信息
 */
export class SubOrderItemLinkResponse {
  /**
   * 子订单外链资源的objectType
   */
  objectType: string
  /**
   * 子订单外链资源的objectId
   */
  objectId: string
  /**
   * 备注
   */
  remark: string
}

/**
 * 子订单
 */
export class SubOrderItemResponse {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 商品溯源码
   */
  commodityTraceCode: string
  /**
   * 商品ID
   */
  commodityId: string
  /**
   * 商品名称
   */
  commodityName: string
  /**
   * 商品图片地址
   */
  photoPath: string
  /**
   * 商品规格
   */
  specification: string
  /**
   * 是否为虚拟物品
   */
  virtualGoods: boolean
  /**
   * 商品标价
   */
  labelPrice: number
  /**
   * 成交单价
   */
  dealPrice: number
  /**
   * 购买数量
   */
  purchaseQuantity: number
  /**
   * 实付总价
   */
  totalAmount: number
  /**
   * 子订单状态
@see com.fjhb.platform.core.v1.order.api.constants.SubOrderStatusConst
   */
  orderStatus: number
  /**
   * 是否需要发票
   */
  needBill: boolean
  /**
   * 子订单的外链资源
   */
  subOrderLinks: Array<SubOrderItemLinkResponse>
}

/**
 * 建设考前相关创建订单类
<AUTHOR> create 2020/3/13 15:22
 */
export class JSKQOrderCreateInfo {
  /**
   * 培训机构id
   */
  unitId?: string
  marketingChannelId?: string
  invoiceInfo?: InvoiceRequestDTO
  subOrderList?: Array<SubOrderItemCreateInfo>
}

export class InvoiceRequestDTO {
  title?: string
  titleType: number
  type: number
  taxpayerNo?: string
  address?: string
  phone?: string
  bankName?: string
  account?: string
  email?: string
  remark?: string
  objectList?: Array<InvoiceObjDTO>
  electron: boolean
  noTaxBill: boolean
}

export class InvoiceObjDTO {
  objectType?: string
  objectId?: string
}

export class SubOrderItemCreateInfo {
  commodityId?: string
  needBill: boolean
  purchaseQuantity: number
}

export class InvoiceRequestDTO1 {
  title: string
  titleType: number
  type: number
  taxpayerNo: string
  address: string
  phone: string
  bankName: string
  account: string
  email: string
  remark: string
  objectList: Array<InvoiceObjDTO1>
  electron: boolean
  noTaxBill: boolean
}

export class InvoiceObjDTO1 {
  objectType: string
  objectId: string
}

export class OrderDTO {
  orderNo: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  organizationId: string
  buyerId: string
  buyerName: string
  sellerId: string
  sellerName: string
  needInvoice: boolean
  receiveAccountId: string
  invoiceInfo: InvoiceRequestDTO1
  subOrderList: Array<SubOrderItemDTO>
  orderStatus: number
  totalAmount: number
  createType: number
  createTime: string
}

export class SubOrderItemDTO {
  subOrderNo: string
  commodityTraceCode: string
  commodityId: string
  commodityName: string
  photoPath: string
  specification: string
  virtualGoods: boolean
  labelPrice: number
  dealPrice: number
  purchaseQuantity: number
  totalAmount: number
  orderStatus: number
  needBill: boolean
  subOrderLinks: Array<SubOrderItemLinkDTO>
}

export class SubOrderItemLinkDTO {
  objectType: string
  objectId: string
  remark: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 同意退款(此操作是在同意了退款申请后同意进行退款)
   * @param refundOrderNo 退款单号
   * @param mutate 查询 graphql 语法文档
   * @param refundOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeRefund(
    refundOrderNo: string,
    mutate: DocumentNode = GraphqlImporter.agreeRefund,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { refundOrderNo },
      operation: operation
    })
  }

  /**   * 同意退款申请
   * @param refundOrderNo 退款单号
   * @param remark        备注
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeRefundApply(
    params: { refundOrderNo?: string; remark?: string },
    mutate: DocumentNode = GraphqlImporter.agreeRefundApply,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 申请退款
   * @param applyInfo 退款申请信息
   * @return 退款单
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyRefund(
    applyInfo: ApplyRefundRequest,
    mutate: DocumentNode = GraphqlImporter.applyRefund,
    operation?: string
  ): Promise<Response<RefundOrderResponse>> {
    return commonRequestApi<RefundOrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { applyInfo },
      operation: operation
    })
  }

  /**   * 取消订单
   * @param orderNo  订单号
   * @param reasonId 取消原因ID
   * @param remark   取消备注
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelOrder(
    params: { orderNo?: string; reasonId?: string; remark?: string },
    mutate: DocumentNode = GraphqlImporter.cancelOrder,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 取消退款申请
   * @param refundOrderNo 退款单号
   * @param reason        取消退款申请原因，可为空
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelRefundApply(
    params: { refundOrderNo?: string; reason?: string },
    mutate: DocumentNode = GraphqlImporter.cancelRefundApply,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 创建订单
   * @param createInfo 订单创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOrder(
    createInfo: OrderCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createOrder,
    operation?: string
  ): Promise<Response<OrderResponse>> {
    return commonRequestApi<OrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { createInfo },
      operation: operation
    })
  }

  /**   * 强制关闭订单
   * @param orderNo  订单号
   * @param reasonId 关闭原因ID
   * @param remark   关闭备注
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async forceCloseOrder(
    params: { orderNo?: string; reasonId?: string; remark?: string },
    mutate: DocumentNode = GraphqlImporter.forceCloseOrder,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 隐藏用户订单
   * @param orderNo 订单号
   * @param mutate 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async hideOrder(
    orderNo: string,
    mutate: DocumentNode = GraphqlImporter.hideOrder,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { orderNo },
      operation: operation
    })
  }

  /**   * 支付订单
   * @param orderPayInfo 订单支付信息
   * @return 返回支付跳转的第三方支付页面地址或数据
   * @param mutate 查询 graphql 语法文档
   * @param orderPayInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async payOrder(
    orderPayInfo: OrderPayInfoRequest,
    mutate: DocumentNode = GraphqlImporter.payOrder,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { orderPayInfo },
      operation: operation
    })
  }

  /**   * 拒绝退款(此操作是在同意了退款申请后在放款给买家时拒绝的)
   * @param refundOrderNo 退款单号
   * @param reason        拒绝退款原因
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rejectRefund(
    params: { refundOrderNo?: string; reason?: string },
    mutate: DocumentNode = GraphqlImporter.rejectRefund,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 拒绝退款申请
   * @param refundOrderNo 退款单号
   * @param reason        拒绝退款申请原因
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rejectRefundApply(
    params: { refundOrderNo?: string; reason?: string },
    mutate: DocumentNode = GraphqlImporter.rejectRefundApply,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 更新订单发票信息
   * @param updateInfo 更新信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateOrderInvoice(
    updateInfo: OrderInvoiceUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateOrderInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { updateInfo },
      operation: operation
    })
  }

  /**   * 创建订单
   * @param createInfo 订单创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async jskqCreateOrder(
    createInfo: JSKQOrderCreateInfo,
    mutate: DocumentNode = GraphqlImporter.jskqCreateOrder,
    operation?: string
  ): Promise<Response<OrderDTO>> {
    return commonRequestApi<OrderDTO>(SERVER_URL, {
      query: mutate,
      variables: { createInfo },
      operation: operation
    })
  }
}

export default new DataGateway()
