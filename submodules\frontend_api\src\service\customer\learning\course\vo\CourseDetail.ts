import Chapter from '@api/service/customer/learning/course/vo/Chapter'
import {
  CourseOutlinePlayResourceResponse,
  CoursePlayResourceResponse
} from '@api/ms-gateway/ms-course-play-resource-v1'
import Courseware from '@api/service/customer/learning/course/vo/Courseware'
import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  TeacherResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'

class CourseDetail {
  id = ''
  // 目录
  chapters: Array<Chapter> = new Array<Chapter>()
  // 课程名称
  name = ''
  // 课程图片
  picture = ''
  // 已学数量
  learnedCount = ''
  // 课程简介
  introduction = ''
  // 评价分数
  evaluateScore: number = null

  teachers: Array<SimpleUserInfo> = new Array<SimpleUserInfo>()

  static from(response: CoursePlayResourceResponse, withoutVerify = false) {
    const detail = new CourseDetail()
    detail.id = response.id
    detail.name = response.name
    detail.picture = response.iconPath
    detail.chapters = response.courseOutlinePlayResourceList
      .filter((chapter: CourseOutlinePlayResourceResponse) => chapter.parentId === '-1')
      .map(Chapter.from)
    detail.introduction = ''

    const findParent = (parentId: string): CourseOutlinePlayResourceResponse => {
      const findOut = response.courseOutlinePlayResourceList.find(chapter => {
        return chapter.id === parentId
      })
      if (findOut && findOut.parentId !== '-1') {
        return findParent(findOut.parentId)
      }
      return findOut
    }

    response.courseChapterPlayResourceList.find(chapter => {
      const { id } = findParent(chapter.courseOutlineId)
      if (id) {
        const findOutChapter = detail.chapters.find((chap: Chapter) => {
          return chap.id === id
        })
        const resultCourseware = Courseware.from(
          response.coursewarePlayResourceList.find(courseware => courseware.id === chapter.coursewareId),
          chapter.sort
        )
        resultCourseware.isAllowAudition = withoutVerify ? true : chapter.allowAudition
        resultCourseware.belongChapterId = chapter.id
        findOutChapter.coursewares.push(resultCourseware)
      }
    })

    if (detail?.chapters?.length) {
      detail.chapters = detail.chapters.sort((a, b) => {
        return a.sort - b.sort
      })

      detail.chapters.map((item, index) => {
        if (item?.coursewares?.length) {
          detail.chapters[index].coursewares = item.coursewares.sort((a, b) => {
            return a.sort - b.sort
          })
        }
      })
    }

    return detail
  }

  /**
   * 加载课程简介
   */
  async queryCourseIntroduction() {
    this.introduction = '加载中...'
  }

  async queryTeacher() {
    const courseDetail = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.getCourseInServicer(this.id)
    if (courseDetail.data) {
      courseDetail.data.teacherIds.map((id: string) => {
        const teacher = new SimpleUserInfo()
        teacher.id = id
        this.teachers.push(teacher)
      })
      this.introduction = courseDetail.data.aboutsContent
    }

    const teacherIdList = this.teachers.map(teacher => teacher.id)
    if (teacherIdList.length) {
      const teacherList = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.listTeacherInServicer(
        teacherIdList
      )
      const teacherMap = new Map<string, TeacherResponse>()
      teacherList.data?.forEach((remote: TeacherResponse) => {
        teacherMap.set(remote.id, remote)
      })

      this.teachers.forEach((teacher: SimpleUserInfo) => {
        const findLocalTeacher = teacherMap.get(teacher.id)
        if (findLocalTeacher) {
          teacher.from(findLocalTeacher)
        }
      })
    }
  }
}

export default CourseDetail
