import {
  ChooseAnswerOption,
  MultipleQuestionResponse,
  OpinionQuestionResponse,
  RadioQuestionResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { QuestionTypeEnum } from '@api/service/common/enums/question/QuestionType'

/*
 * 试题公共模型类
 */
class QuestionDetail {
  /**
   * 试题ID
   */
  questionId = ''

  /**
   * 题库ID
   */
  libraryId = ''
  /**
   * 题库名称
   */
  libraryName = ''

  /**
   * 试题题目
   */
  topic = ' '
  /**
   * 试题解析
   */
  dissects = ''

  /**
   * 创建时间
   */
  createTime = ''

  /**
   * 试题类型
    @see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType = QuestionTypeEnum.radio
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string> = []
  /*
    关联课程名称集合
  */
  relateCourseNames: Array<string> = []

  /*
    是否启用
  */
  isEnable: boolean = null

  // * 答案ID
  correctAnswerId: any

  correctAnswerIds: string[] = []

  /**
   * 可选答案列表
   */
  answerOptions: Array<ChooseAnswerOption> = new Array<ChooseAnswerOption>()

  // 模型转换
  from(data: RadioQuestionResponse | MultipleQuestionResponse | OpinionQuestionResponse, list?: Array<string>) {
    //  子类重写方法
  }
}

export default QuestionDetail
