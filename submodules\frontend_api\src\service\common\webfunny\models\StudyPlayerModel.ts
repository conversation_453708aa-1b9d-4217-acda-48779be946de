import BaseReportModel from '@api/service/common/webfunny/models/BaseReportModel'

/**
 * 初始化错误埋点对象
 */
export class PlayerInitReportModel extends BaseReportModel {
  /*
   * 课程id
   */
  courserId: string

  /*
   * 课程名称
   */
  courseName?: string

  /*
   * 方案id
   */
  schemeId: string

  /*
   * 课件id
   */
  courseWareId?: string

  /*
   * 课件名称
   */
  courseWareName?: string

  /*
   * 参训资格id
   */
  qualificationId: string

  /*
   * 课程播放Token
   */
  applyStudentLearningToken: string

  /*
   * 课程计时Token
   */
  courseLearningTimingToken?: string

  /*
   * 课件学习Token
   */
  coursewareLearningToken?: string

  /*
   * 课件计时Token
   */
  coursewareLearningTimingToken?: string

  /*
   * 最后播放刻度
   */
  lastmediaPlayScale?: number

  /**
   * 媒体资源地址
   */
  mediaUrl?: string
}

/**
 * 播放过程进度跳跃埋点对象
 */
export class PlayerSkipReportModel extends BaseReportModel {
  /*
   * 课程id
   */
  courserId: string

  /*
   * 课程名称
   */
  courseName?: string

  /*
   * 方案id
   */
  schemeId: string

  /*
   * 课件id
   */
  courseWareId?: string

  /*
   * 课件名称
   */
  courseWareName?: string

  /**
   * 当前播放刻度
   */
  currentScale: number

  /**
   * 媒体资源
   */
  mediaUrl: string
}

/**
 * 提交进度异常
 */
export class CommitErrorReportModel extends BaseReportModel {
  /*
   * 课程id
   */
  courserId: string

  /*
   * 课程名称
   */
  courseName?: string

  /*
   * 方案id
   */
  schemeId: string

  /*
   * 课件id
   */
  courseWareId?: string

  /*
   * 课件名称
   */
  courseWareName?: string

  /**
   * 媒体资源
   */
  mediaUrl: string

  /**
   * 当前提交刻度
   */
  currentScale: number

  /**
   * 后端判定进度
   */
  determineTime?: number

  /**
   * 后端判定课程进度
   */
  determineCourseSchedule?: number

  /**
   * 后端判定课件进度
   */
  determineCoursewareSchedule?: number
}
