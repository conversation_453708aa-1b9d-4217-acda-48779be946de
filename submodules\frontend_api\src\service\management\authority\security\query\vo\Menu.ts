class Router {
  path: string
  params: any
}

export class Meta {
  openWhenInit = false
  icon = ''
  closeAble = true
  sort = 0
  id: string
  index: number
  isMenu: boolean
  keepAlive: boolean
  // isMenu被迫为true 隐藏菜单入口 默认false不隐藏 与isMenu搭配使用 安全对象构建使用
  hideMenu: boolean
}

class Menu {
  activeRouter: string
  title: string
  path: string
  icon?: string
  rootRoute?: string
  ext: string
  id: string
  name: string
  code: string
  router: Router = new Router()
  closeAble = true
  openWhenInit: boolean
  menuIcon: string
  parentId?: string
  children: Array<Menu> = new Array<Menu>()
  meta: Meta = new Meta()
}

export default Menu
