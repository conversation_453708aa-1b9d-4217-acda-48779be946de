/**
 * 章节
 * @description 章节中章节信息
 */
import Courseware from '@api/service/management/resource/course/mutation/dto/Courseware'
import Mockjs from 'mockjs'

class Chapter {
  /**
   * 章节编号，对应标签编号
   */
  private id: string

  constructor() {
    this.id = Mockjs.Random.guid()
  }

  /**
   * 章节名称，对应标签名称
   */
  name = ''

  /**
   * 父节点
   */
  parentId: string

  coursewares: Array<Courseware> = new Array<Courseware>()

  editNameMode = false

  edit() {
    this.editNameMode = true
  }

  addCourseware() {
    const courseware = new Courseware()
    this.coursewares.push(courseware)
  }

  removeCourseware(index: number) {
    this.coursewares.splice(index, 1)
  }
}

export default Chapter
