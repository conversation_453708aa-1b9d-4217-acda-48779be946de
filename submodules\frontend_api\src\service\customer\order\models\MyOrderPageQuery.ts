import PageRequest from '@api/service/common/models/common/PageRequest'
import { MyOrderQueryDTO, TimeRegionRequest } from '@api/gateway/PlatformTrade'
import { Constants } from '@api/service/common/models/common/Constants'
import moment from 'moment'
import { CommonTimeRegionRequest } from '@api/service/common/models/common/TimeRegionRequest'

/**
 * 我的订单列表查询条件
 */
export class MyOrderPageQuery extends PageRequest {
  /**
   * 订单号或商品名称,如果是纯数字的则以订单号来匹配否则通过商品名称like
   */
  orderNoOrCommodityName?: string
  /**
   * 订单状态
   */
  orderStatus?: number
  /**
   * 订单创建时间
   */
  createTime?: CommonTimeRegionRequest

  constructor() {
    super()
    this.pageNo = 1
    this.pageSize = 10
  }

  equals(query: any): boolean {
    if (!super.equals(query)) {
      return false
    }
    if (this.orderNoOrCommodityName !== query.orderNoOrCommodityName) {
      return false
    }
    if (this.orderStatus !== query.orderStatus) {
      return false
    }
    if (this.createTime?.startTime !== query.createTime?.startTime) {
      return false
    }
    if (this.createTime?.endTime !== query.createTime?.endTime) {
      return false
    }
    return true
  }

  toMyOrderQueryDTO(): MyOrderQueryDTO {
    const dto = new MyOrderQueryDTO()
    if (this.orderStatus) {
      dto.states = [this.orderStatus]
    }
    dto.createTime = new TimeRegionRequest()
    if (this.createTime) {
      if (this.createTime.startTime) {
        dto.createTime.startTime = moment(this.createTime?.startTime).format(Constants.DATE_PATTERN)
      }
      if (this.createTime.endTime) {
        dto.createTime.endTime = moment(this.createTime?.endTime).format(Constants.DATE_PATTERN)
      }
    }
    dto.orderNoOrSchemeName = this.orderNoOrCommodityName
    return dto
  }
}
