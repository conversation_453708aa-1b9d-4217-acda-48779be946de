/**
 * 时间格式化
 */
class TimeFormat {
  _time = 0

  constructor(time = 0) {
    this._time = time
    this.second = time % 60
    this.hour = Math.floor(time / 3600)
    this.minutes = Math.floor((time / 60) % 60)
  }

  hour: number
  minutes: number
  second: number

  /**
   * 前面不足追加 0，长度两位
   * @param val
   * @private
   */
  private pad(val: number) {
    const value = `${val}`
    return `${value.padStart(2, '0')}`
  }

  toString() {
    return `${this.pad(this.hour)}:${this.pad(this.minutes)}:${this.pad(this.second)}`
  }

  toJSON() {
    return this.toString()
  }
}

export default TimeFormat
