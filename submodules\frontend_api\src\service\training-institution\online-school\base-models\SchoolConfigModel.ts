import {
  CNZZModeEnum,
  DomainNameTypesEnum,
  perfectInfoEnum,
  ServicePeriodEnum,
  SmsProviderEnum
} from '@api/service/training-institution/online-school/enum/OnlineSchoolEnum'

/**
 * 网校配置信息
 */
export default class SchoolConfigModel {
  /**
   * 域名类型
   */
  domainNameType: DomainNameTypesEnum = undefined

  /**
   * web域名
   */
  webDomain: string = undefined

  /**
   * 是否提供PC终端
   */
  provideWebService: boolean = undefined

  /**
   * 是否提供H5终端
   */
  provideH5Service: boolean = undefined

  /**
   * H5域名
   */
  H5Domain: string = undefined

  /**
   * 是否提供短信服务
   */
  provideSms: boolean = undefined

  /**
   * 短信运营商
   */
  smsService: SmsProviderEnum = undefined

  /**
   * 短信账号
   */
  smsAccount: string = undefined

  /**
   * 短信账号密码
   */
  smsPassword: string = undefined

  /**
   * 服务期限
   */
  servicePeriodModel: ServicePeriodEnum = undefined

  /**
   * 到期时间
   */
  serviceOverTime: string = undefined

  /**
   * web模板id
   */
  webPortalTemplateId: string = undefined

  /**
   * H5模板id
   */
  H5PortalTemplateId: string = undefined

  /**
   * 友盟统计
   */
  cnzzMode: CNZZModeEnum = undefined

  /**
   * 业主自有统计值
   */
  cnzzValue: string = undefined

  /**
   * 默认CNZZ值 （请勿改动）
   */
  defaultCnzzValue = '**********'
  /**
   * 完善信息页设置
   */
  perfectInfoModel: perfectInfoEnum = undefined
}
