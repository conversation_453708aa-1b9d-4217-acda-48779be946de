import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import axios from 'axios'

/**
 * @description 文件预览
 */
class FilePreview {
  // 1. 图片、pdf、excel资源查看
  // 2. 时效1分钟 如何刷新

  /**
   * 资源访问token
   */
  resourceAccessToken = ''
  /**
   * 最新获取token时间戳
   */
  lastTimeStamp = 0

  /**
   * 获取查看受保护资源授权token
   */
  async applyResourceAccessToken() {
    const isValid = Date.now() - this.lastTimeStamp - 60000 < 0
    if (!isValid) {
      const baseUrl = `${ConfigCenterModule.getFrontendApplication(
        frontendApplication.apiendpoint
      )}/web/ms-file-v1/token/getTempAccessToken`
      try {
        const config = {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json;charset=UTF-8',
            'App-Authentication': `Basic ${process.env.VUE_APP_KEY}`
          }
        }
        if (localStorage.getItem('customer.Access-Token')) {
          config.headers['Authorization'] = `Mship ${localStorage.getItem('customer.Access-Token')}`
        }
        const res = await axios.get(baseUrl, config)
        if (res.data?.code === 200) {
          this.resourceAccessToken = res.data.data
          this.lastTimeStamp = Date.now()
        }
      } catch (error) {
        console.log(error)
      }
    }
  }
  /**
   * 文件url拼接token
   * @param src 文件url
   */
  getFileUrlWithToken(src: string) {
    if (!src) return
    const protectedPrefixReg = /\/ms-file\/protected\//
    if (protectedPrefixReg.test(src)) {
      if (src?.includes('token')) {
        return `${src.split('?')[0]}?token=${this.resourceAccessToken}`
      }
      return `${src}?token=${this.resourceAccessToken}`
    } else {
      return src
    }
  }
}

export default new FilePreview()
