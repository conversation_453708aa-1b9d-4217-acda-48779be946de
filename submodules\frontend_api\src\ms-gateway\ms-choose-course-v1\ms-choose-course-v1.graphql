"""独立部署的微服务,K8S服务名:ms-choose-course-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""请求选课
		@param request 选课参数
		@return 选课结果
		@see ValidateCodeConst
	"""
	chooseCourse(request:ChooseCourseRequest!):ChooseCourseResponse
	"""补偿接口
		用于直接创建学员课程
	"""
	createStudentCourse(chooseCourseList:[StudentChooseCourseRequest]):Void @optionalLogin
	"""补偿接口
		用于删除改造前无法删除掉的课程大纲，以及课程包使用情况
	"""
	deleteCourseTrainingOutlineAndUsage(request:DeleteCourseTrainingOutlineAndUsageRequest):DeleteOutlineResponse @optionalLogin
	"""准备选课
		@param chooseToken 选课凭证
		@return 选课规则及当前选课信息
	"""
	prepareChooseCourse(chooseToken:String!):PrepareChooseCourseResponse
	"""智能选课
		@param chooseToken:
		@return {@link SmartChooseCourseResponse}
		<AUTHOR> By Cb
		@since 2024/5/13 11:58
	"""
	smartChooseCourse(chooseToken:String!):FullRuleSmartRecommendCoursesResult
}
input Range @type(value:"com.fjhb.domain.course.api.courselearning.common.Range") {
	key:String
	value:String
}
"""选课信息
	<AUTHOR>
	@since 2022/1/17
"""
input ChooseCourseInfoRequest @type(value:"com.fjhb.ms.choose.course.v1.kernel.gateway.graphql.request.ChooseCourseInfoRequest") {
	"""所属课程学习大纲编号"""
	outlineId:String
	"""课程编号"""
	courseId:String
}
"""选课请求参数对象
	<AUTHOR>
	@since 2022/1/17
"""
input ChooseCourseRequest @type(value:"com.fjhb.ms.choose.course.v1.kernel.gateway.graphql.request.ChooseCourseRequest") {
	"""选课凭证"""
	chooseToken:String
	"""选择课程列表"""
	courseList:[ChooseCourseInfoRequest]
}
"""删除课程大纲以及课程包使用情况请求
	<AUTHOR>
	@since 2023/10/23
"""
input DeleteCourseTrainingOutlineAndUsageRequest @type(value:"com.fjhb.ms.choose.course.v1.kernel.gateway.graphql.request.DeleteCourseTrainingOutlineAndUsageRequest") {
	"""事务id"""
	transactionId:String
	"""课程大纲ID[必填]"""
	courseTrainingOutlineId:String
	"""删除类型[必填]
		<p>
		1 - 删除课程包使用情况，大纲下课程
		2 - 删除课程大纲
		<p>
		注意，需要先删除课程包使用情况和大纲下课程，等待大纲下课程删除完成后，再调用删除课程大纲
		如果先删除课程大纲会导致大纲下课程无法删除
		<p>
	"""
	deleteType:Int!
}
input StudentChooseCourseRequest @type(value:"com.fjhb.ms.choose.course.v1.kernel.gateway.graphql.request.StudentChooseCourseRequest") {
	"""学员课程id"""
	id:String
	"""平台编号"""
	platformId:String
	"""平台版本编号"""
	platformVersionId:String
	"""项目编号"""
	projectId:String
	"""子项目编号"""
	subProjectId:String
	"""服务商编号"""
	servicerId:String
	"""服务商所属单位编号"""
	unitId:String
	"""选课时的学时，一般是从大纲内获取，若最后一门则有可能学时被重置。"""
	period:Double!
	"""场景id"""
	sceneId:String
	"""数据范围"""
	ranges:[Range]
	"""课程类型
		@see CourseTypes
	"""
	courseType:Int!
	"""学员课程创建时间"""
	createTime:DateTime
	"""学习大纲编号"""
	outlineId:String
	"""课程id"""
	courseId:String
	"""用户id"""
	userId:String
	"""学号"""
	studentNo:String
	"""参训资格编号"""
	qualificationId:String
	"""场景类型
		@see CourseLearningSceneTypes
	"""
	sceneType:Int!
}
"""智能选课规则
	<AUTHOR>
	@since 2024/8/14
"""
type SmartCourseSelectionConfig @type(value:"com.fjhb.ms.choose.course.v1.api.config.SmartCourseSelectionConfig") {
	"""智能选课类型
		0-依据选课规则选课
		1-指定要求学时
	"""
	smartCourseSelectionType:Int!
	"""要求学时
		smartCourseSelectionType为1时必填
	"""
	smartCourseSelectionPeriod:Double
	"""智能选课规则
		1 - 选课出现异常时都不选
		2 - 选课出现异常时只有异常课程不选(如果是重复选课，重复的两门课程都不选)
	"""
	smartCourseSelectionRule:Int!
}
"""选课数据范围"""
type ChooseCourseRange @type(value:"com.fjhb.ms.choose.course.v1.api.param.ChooseCourseRange") {
	"""数据范围key"""
	key:String
	"""数据范围value"""
	value:String
}
type StudentChooseCourseParam @type(value:"com.fjhb.ms.choose.course.v1.api.param.StudentChooseCourseParam") {
	"""学习大纲编号"""
	outlineId:String
	"""课程id"""
	courseId:String
	"""学员课程ID
		若不提供 则默认使用UUIDUtils.generate()生成
	"""
	studentCourseId:String
	"""课程类型 - 智能选课使用
		@see CourseTypes
	"""
	courseType:Int
	"""学时 - 智能选课使用"""
	period:Double
	"""大纲下课程id - 智能选课使用"""
	courseOfOutlineId:String
}
"""推荐课程结果
	<AUTHOR>
	@since 2024/8/15
"""
type FullRuleSmartRecommendCoursesResult @type(value:"com.fjhb.ms.choose.course.v1.api.result.FullRuleSmartRecommendCoursesResult") {
	"""code"""
	code:String
	"""消息"""
	message:String
	"""选课结果"""
	recommendInfo:FullRuleSmartRecommendInfo
}
"""选课规则智能选课信息
	@see ChoseCourseValidateResult
"""
type FullRuleSmartRecommendInfo @type(value:"com.fjhb.ms.choose.course.v1.api.result.FullRuleSmartRecommendInfo") {
	"""平台编号"""
	platformId:String
	"""平台版本编号"""
	platformVersionId:String
	"""项目编号"""
	projectId:String
	"""子项目编号"""
	subProjectId:String
	"""服务商编号"""
	servicerId:String
	"""服务商所属单位编号"""
	unitId:String
	"""需要选课的列表"""
	currentChooseCourseList:[StudentChooseCourseParam]
	"""选课规则id"""
	ruleId:String
	"""场景id"""
	sceneId:String
	"""用户id"""
	userId:String
	"""学号"""
	studentNo:String
	"""选课数据范围"""
	ranges:[ChooseCourseRange]
	"""选课时间"""
	chooseCourseDate:DateTime
	"""参训资格编号"""
	qualificationId:String
	"""场景类型"""
	sceneType:Int!
	jobCategoryName:String
	"""选课方式
		1-直接选课 2-重算选课
	"""
	chooseMethod:Int!
	"""智能选课配置"""
	smartCourseSelectionConfig:SmartCourseSelectionConfig
}
"""选课返回值
	<AUTHOR>
	@since 2022/1/17
"""
type ChooseCourseResponse @type(value:"com.fjhb.ms.choose.course.v1.kernel.gateway.graphql.response.ChooseCourseResponse") {
	"""选课结果代码：
		500-选课失败
		41001-选课失败,需要选择的课程集合为空
		41002-不满足必修要求学时
		41003-超过选修课允许最大学时
		41004-解析选课token异常
		41005-选课失败，选课范围不满足选课规则要求
		51001-课程大纲内不存在该课程
		51002-重复选课
	"""
	code:String
	"""选课结果信息"""
	message:String
	"""选课结果集合"""
	chooseErrorResult:[CourseChoseResultResponse]
}
type CourseChoseResultResponse @type(value:"com.fjhb.ms.choose.course.v1.kernel.gateway.graphql.response.CourseChoseResultResponse") {
	"""所属课程学习大纲编号"""
	outlineId:String
	"""课程编号"""
	courseId:String
	"""选课结果code
		41002-不满足必修要求学时
		41003-超过选修课允许最大学时
		41004-解析选课token异常
		51001-课程大纲内不存在该课程
		51002-重复选课
	"""
	choseResultCode:String
	"""信息"""
	message:String
}
"""<AUTHOR>
	@since 2023/10/23
"""
type DeleteOutlineResponse @type(value:"com.fjhb.ms.choose.course.v1.kernel.gateway.graphql.response.DeleteOutlineResponse") {
	"""code"""
	code:String
	"""选课结果信息"""
	message:String
}
"""准备选课返回值
	<AUTHOR>
	@since 2022/1/17
"""
type PrepareChooseCourseResponse @type(value:"com.fjhb.ms.choose.course.v1.kernel.gateway.graphql.response.PrepareChooseCourseResponse") {
	"""选课规则名称"""
	name:String
	"""选课规则及当前用户已选课程值
		根据选课规则类型名称决定
		name = CompulsoryAndElectivePeriodRule
		{
		"compulsoryPeriod": {
		"config": 0.00,
		"current": 0.00
		},
		"electiveMaxPeriod": {
		"config": 0.00,
		"current": 0.00
		},
		"allowLastChooseOver": false
		}
	"""
	properties:String
}

scalar List
