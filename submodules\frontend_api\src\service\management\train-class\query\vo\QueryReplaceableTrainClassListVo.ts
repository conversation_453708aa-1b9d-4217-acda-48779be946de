import {
  CommoditySkuRequest,
  DateScopeRequest,
  OnShelveRequest,
  SkuPropertyRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import {
  RegionSkuPropertyRequest,
  RegionSkuPropertySearchRequest,
  SchemeRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * @description 查询可替换班级列表参数
 */
class QueryReplaceableTrainClassListVo {
  /**
   * 培训班名称
   */
  schemeName = ''

  /**
   * 年度
   */
  year: string[] = []

  /**
   * 地区
   */
  region: string[] = []

  /**
   * 行业
   */
  industry: string[] = []

  /**
   * 科目类型
   */
  subjectType: string[] = []

  /**
   * 培训类别
   */
  trainingCategory: string[] = []

  /**
   * 培训专业
   */
  trainingProfessional: string[] = []
  /**
   * 技术等级
   */
  technicalGrade: string[] = []

  /**
   * 培训对象（卫生）
   */
  trainingObject: string[] = []

  /**
   * 岗位类别（卫生）
   */
  positionCategory: string[] = []

  /**
   * 技术等级（工勤）
   */
  jobLevel: string[] = []
  /**
   * 学段（教师）
   */
  learningPhase?: Array<string>
  /**
   * 学科（教师）
   */
  discipline?: Array<string>
  /**
   * 证书类型（药师）
   */
  certificatesType?: Array<string>
  /**
   * 执业类别（药师）
   */
  practitionerCategory?: Array<string>
  /**
   * 价格
   */
  price: number = null

  /**
   * 剔除培训班商品id集合
   */
  excludeCommoditySkuIdList: string[] = []

  /**
   * 培训方案类型
   * 1: 选课规则
   * 2: 自主选课
   */
  schemeType: TrainClassSchemeEnum = null
  /**
   * 专题id
   */
  trainingChannelIds?: Array<string> = []
  /**
   * 培训形式
   */
  trainingMode: TrainingModeEnum = null

  /**
   * 分销商id
   */
  distributorId = ''

  formatDate(date: any, cut: any) {
    const dateTemp = new Date(date)
    const YY = dateTemp.getFullYear() + cut
    const MM = (dateTemp.getMonth() + 1 < 10 ? '0' + (dateTemp.getMonth() + 1) : dateTemp.getMonth() + 1) + cut
    const DD = dateTemp.getDate() < 10 ? '0' + dateTemp.getDate() : dateTemp.getDate()
    const hh = (dateTemp.getHours() < 10 ? '0' + dateTemp.getHours() : dateTemp.getHours()) + ':'
    const mm = (dateTemp.getMinutes() < 10 ? '0' + dateTemp.getMinutes() : dateTemp.getMinutes()) + ':'
    const ss = dateTemp.getSeconds() < 10 ? '0' + dateTemp.getSeconds() : dateTemp.getSeconds()
    return YY + MM + DD + ' ' + hh + mm + ss
  }
  to(): CommoditySkuRequest {
    const to: CommoditySkuRequest = new CommoditySkuRequest()
    to.notShowCommoditySkuIdList = QueryReplaceableTrainClassListVo.setArrValue(this.excludeCommoditySkuIdList)
    console.log(this.excludeCommoditySkuIdList, 'excludeCommoditySkuIdList')
    // 如果传的是0，说明是零元单
    to.price = QueryReplaceableTrainClassListVo.validateIsEmpty(this.price) ? this.price : undefined
    to.schemeRequest = new SchemeRequest()
    to.schemeRequest.schemeName = this.schemeName || undefined
    to.schemeRequest.trainingEndDate = new DateScopeRequest()
    to.schemeRequest.trainingEndDate.begin = this.formatDate(new Date(), '-')
    if (this.schemeType) {
      to.schemeRequest.schemeType = SchemeTypeEnum[this.schemeType]
    } else {
      to.schemeRequest.schemeType = undefined
    }

    to.skuPropertyRequest = new SkuPropertyRequest()
    to.skuPropertyRequest.year = QueryReplaceableTrainClassListVo.setArrValue(this.year)
    to.skuPropertyRequest.industry = QueryReplaceableTrainClassListVo.setArrValue(this.industry)
    to.skuPropertyRequest.subjectType = QueryReplaceableTrainClassListVo.setArrValue(this.subjectType)
    to.skuPropertyRequest.trainingCategory = QueryReplaceableTrainClassListVo.setArrValue(this.trainingCategory)
    to.skuPropertyRequest.trainingProfessional = QueryReplaceableTrainClassListVo.setArrValue(this.trainingProfessional)
    to.skuPropertyRequest.jobLevel = QueryReplaceableTrainClassListVo.setArrValue(this.jobLevel)
    to.skuPropertyRequest.trainingObject = QueryReplaceableTrainClassListVo.setArrValue(this.trainingObject)
    to.skuPropertyRequest.positionCategory = QueryReplaceableTrainClassListVo.setArrValue(this.positionCategory)
    to.skuPropertyRequest.learningPhase = QueryReplaceableTrainClassListVo.setArrValue(this.learningPhase)
    to.skuPropertyRequest.certificatesType = QueryReplaceableTrainClassListVo.setArrValue(this.certificatesType)
    to.skuPropertyRequest.practitionerCategory = QueryReplaceableTrainClassListVo.setArrValue(this.practitionerCategory)
    to.skuPropertyRequest.discipline = QueryReplaceableTrainClassListVo.setArrValue(this.discipline)
    to.skuPropertyRequest.trainingForm = this.trainingMode ? ([this.trainingMode] as unknown as string[]) : undefined
    if (this.region && this.region.length) {
      to.skuPropertyRequest.regionSkuPropertySearch = QueryReplaceableTrainClassListVo.setRegion(this.region)
    }
    to.skuPropertyRequest.technicalGrade = QueryReplaceableTrainClassListVo.setArrValue(this.technicalGrade)
    // 默认查询上架的班级
    to.onShelveRequest = new OnShelveRequest()
    to.onShelveRequest.onShelveStatus = 1
    to.trainingChannelIds = this.trainingChannelIds
    if (this.distributorId) {
      to.distributorId = this.distributorId
    }
    return to
  }

  /**
   * 数组是否有质量
   */
  static isWeightyArr<T>(arr: T[]) {
    return Array.isArray(arr) && arr.length ? true : false
  }

  /**
   * 设置数组值
   */
  static setArrValue<T>(arr: T[]): T[] | undefined {
    return QueryReplaceableTrainClassListVo.isWeightyArr(arr) ? arr : []
  }

  /**
   * 设置地区
   */
  static setRegion(arr: string[]): RegionSkuPropertySearchRequest {
    let result = new RegionSkuPropertySearchRequest()
    result.region = new Array<RegionSkuPropertyRequest>()
    if (QueryReplaceableTrainClassListVo.isWeightyArr(arr)) {
      const option = new RegionSkuPropertyRequest()
      option.province = arr.length >= 1 ? arr[0] : undefined
      option.city = arr.length >= 2 ? arr[1] : undefined
      option.county = arr.length >= 3 ? arr[2] : undefined
      result.region.push(option)
      result.regionSearchType = 1
    } else {
      result = new RegionSkuPropertySearchRequest()
    }
    return result
  }

  /**
   * 校验是否为空【null、undefined】
   */
  static validateIsEmpty(val: string | number) {
    return (val as string) !== '' && val !== undefined && val !== null
  }
}

export default QueryReplaceableTrainClassListVo
