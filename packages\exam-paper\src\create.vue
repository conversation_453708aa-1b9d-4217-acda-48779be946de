<template>
  <el-main>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/exam-paper')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/exam-paper' }">试卷管理</el-breadcrumb-item>
      <el-breadcrumb-item>创建试卷</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <!--第一步-->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <el-row type="flex" justify="center">
          <el-col :sm="20" :lg="12">
            <el-steps :active="currentStep" align-center class="m-steps f-pt40 f-pb10">
              <el-step title="填写基础信息"></el-step>
              <el-step title="配置试题"></el-step>
              <el-step title="创建成功"></el-step>
            </el-steps>
          </el-col>
        </el-row>
        <el-divider v-show="currentStep !== 2" class="m-divider"></el-divider>
        <step-1
          v-show="currentStep === 1"
          :create-exampaper-info.sync="createExamPaper"
          @getCurrentStep="getCurrentStep"
          @commitDraft="commitDraft"
        ></step-1>
        <step-2
          v-show="currentStep === 2"
          :create-exampaper-info.sync="createExamPaper"
          @BackFirstStep="backFirstStep"
          @thirdCommit="thirdCommit"
          @commitDraft="commitDraft"
        ></step-2>
        <step-3 v-if="currentStep === 3"></step-3>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import Step1 from '@hbfe/jxjy-admin-examPaper/src/add/step-1.vue'
  import Step2 from '@hbfe/jxjy-admin-examPaper/src/add/step-2.vue'
  import Step3 from '@hbfe/jxjy-admin-examPaper/src/add/step-3.vue'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { AutomaticExamPaperVo } from '@api/service/management/resource/exam-paper/MutationExamPaperFactory'

  @Component({
    components: { Step1, Step2, Step3 }
  })
  export default class extends Vue {
    // 获取创建试卷的实例对象
    createExamPaperObject = ResourceModule.mutationExamPaperFactory.getCreateExamPaper(AutomaticExamPaperVo)
    // 创建试卷的入参
    createExamPaper = this.createExamPaperObject.createExamPaperParams

    currentStep = 1

    getCurrentStep(value: any) {
      this.currentStep = value
    }

    backFirstStep(value: any) {
      this.currentStep = value
    }

    async thirdCommit(value: any) {
      if (value) {
        this.createExamPaper.isDraft = 2
        const res = await this.createExamPaperObject.doCreateExamPaper()
        if (res.isSuccess()) {
          this.currentStep = 3
        } else {
          this.$message.error(res.errors[0].message)
        }
      }
    }

    async commitDraft(value: any) {
      if (value) {
        this.createExamPaper.isDraft = 1

        const res = await this.createExamPaperObject.doCreateExamPaper()
        if (res.isSuccess()) {
          this.$message.success('保存草稿成功')
          await this.$router.push('/resource/exam-paper')
        }
      }
    }
  }
</script>
