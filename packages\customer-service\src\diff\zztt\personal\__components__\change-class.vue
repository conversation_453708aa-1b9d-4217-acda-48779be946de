<template>
  <div class="f-p15">
    <el-tabs v-model="activeName" type="card" class="m-tab-card" @tab-click="handleClick" v-loading="tabLoading">
      <el-tab-pane label="网授班" name="onlineClass">
        <change-online-class :userId="userId" ref="onlineClassRef"></change-online-class>
      </el-tab-pane>
      <el-tab-pane label="面授班" name="faceClass" v-if="isOnlineClassSupport">
        <change-face-class :userId="userId" ref="facaClassRef"></change-face-class>
      </el-tab-pane>
      <el-tab-pane label="面网授班" name="mixClassRef" v-if="isOnlineClassSupport">
        <change-mix-class :userId="userId" ref="mixClassRef"></change-mix-class>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import ChangeOnlineClass from '@hbfe/jxjy-admin-customerService/src/diff/zztt/personal/__components__/change-online-class.vue'
  import ChangeMixClass from '@hbfe/jxjy-admin-customerService/src/personal/components/components/change-class-components/change-mix-class.vue'
  import ChangeFaceClass from '@hbfe/jxjy-admin-customerService/src/personal/components/components/change-class-components/change-face-class.vue'
  import QueryShowOffline from '@api/service/common/config/QueryShowOffline'
  @Component({
    components: { ChangeFaceClass, ChangeMixClass, ChangeOnlineClass }
  })
  export default class extends Vue {
    @Ref('onlineClassRef') onlineClassRef: ChangeOnlineClass
    @Ref('mixClassRef') mixClassRef: ChangeMixClass
    @Ref('facaClassRef') facaClassRef: ChangeFaceClass
    // 学员id 由主文件ref传入
    userId = ''
    activeName = 'onlineClass'
    tabLoading = false
    async userIdChange(val: string) {
      this.userId = val
    }
    /**
     * 是否展示面网授
     */
    get isOnlineClassSupport() {
      const show = QueryShowOffline.getShowOfflineApolloConfig()
      return !show
    }
    handleClick() {
      if (this.activeName === 'onlineClass') {
        this.onlineClassRef.userId = this.userId
      }
      if (this.activeName === 'faceClass') {
        this.facaClassRef.userId = this.userId
      }
      if (this.activeName === 'mixClassRef') {
        this.mixClassRef.userId = this.userId
      }
    }
  }
</script>
