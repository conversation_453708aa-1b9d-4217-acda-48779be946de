<route-meta>
{
"isMenu": true,
"title": "问卷管理",
"sort": 6,
"icon": "icon-tiku"
}
</route-meta>
<script lang="ts">
  import Questionnaire from '@hbfe/jxjy-admin-questionnaire/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    questionnaireQuery: [WXGLY],
    questionnaireDelete: [WXGLY],
    questionnaireCreate: [WXGLY],
    questionnaireCopy: [WXGLY],
    questionnaireEdit: [WXGLY],
    questionnairePreview: [WXGLY]
  })
  export default class extends Questionnaire {}
</script>
