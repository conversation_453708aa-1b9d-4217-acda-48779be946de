import { ResponseStatus } from '@hbfe/common'

/**
 * 创建商品基础类
 */
class CreateCommodityBase {
  // region properties
  /**
   *商品id，类型为string
   */
  commoditySkuId = ''

  /**
   * 分类id
   */
  categoryId = ''

  /**
   *可见的购买渠道，1：用户自主购买，2：集体缴费，3：管理员导入，4：所有 类型为number[]
   */
  visibleChannelList: number[] = []
  /**
   *商品抬头，类型为string
   */
  saleTitle = ''
  /**
   *价格，类型为number
   */
  price = 0
  /**
   * 税务编码
   */
  taxCode = ''
  /**
   *是否关闭学员报名，类型为boolean
   */
  closeCustomerPurchase = false
  /**
   *上架计划时间，类型为string
   */
  onShelvesPlanTime = ''
  /**
   *下架计划时间，类型为string
   */
  offShelvesPlanTime = ''
  /**
   *是否立即上架，类型为boolean
   */
  onShelves = true
  /**
   * 是否重算中
   */
  recalculating = false
  /**
   * 是否智能学习中
   */
  // endregion
  // region methods

  /**
   * 创建商品
   */
  async createCommodity(): Promise<ResponseStatus> {
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  // endregion
}
export default CreateCommodityBase
