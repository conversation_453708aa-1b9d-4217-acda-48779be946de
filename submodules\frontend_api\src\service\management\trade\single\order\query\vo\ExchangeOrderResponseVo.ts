import {
  ExchangeOrderApplyInfoResponse,
  ExchangeOrderApprovalInfoResponse,
  ExchangeOrderCloseReasonResponse,
  ExchangeOrderResponse,
  ExchangeOrderStatusChangeTimeResponse,
  OriginalCommodityResponse,
  SubOrderInfoResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { ExchangeOrderStatue } from '@api/service/common/trade/ExchangeOrderStatue'
import AdminUserInfoVo from '@api/service/management/user/query/manager/vo/AdminUserInfoVo'
/**
 * 换货单基本信息
 <AUTHOR>
 @date 2022/03/23
 */
export class ExchangeOrderBasicDataResponse {
  /**
   * 换货单状态
   <p>
   0: 申请换货
   1: 取消中
   2: 退货中
   3: 退货失败
   4: 申请发货
   5: 发货中
   6: 换货完成
   7: 已关闭
   @see ExchangeOrderStatus
   */
  status: number
  /**
   * 换货单状态变更时间
   */
  statusChangeTime = new ExchangeOrderStatusChangeTimeResponse()
  /**
   * 换货单申请信息
   */
  applyInfo: ExchangeOrderApplyInfoResponse
  /**
   * 换货单失败信息
   */
  exchangeFailReason: string
  /**
   * 换货单关闭信息
   */
  exchangeOrderCloseReason: ExchangeOrderCloseReasonResponse
}
export default class ExchangeOrderResponseVo {
  /**
   * 换货单号
   */
  exchangeOrderNo = ''
  /**
   * 换货单基本信息
   */
  basicData = new ExchangeOrderBasicDataResponse()
  /**
   * 换货单审批信息
   */
  approvalInfo = new ExchangeOrderApprovalInfoResponse()
  /**
   * 原始商品信息
   */
  originalCommodity = new OriginalCommodityResponse()
  /**
   * 换货商品信息
   */
  exchangeCommodity = new OriginalCommodityResponse()
  /**
   * 换货单关联子订单信息
   */
  subOrderInfo = new SubOrderInfoResponse()
  exchangeStatue = ExchangeOrderStatue.ExchangeOrderStatueExchanging
  // * 换班人名称
  applyUserName = ''

  changeStatue() {
    const exchangingStatues = [0, 1, 2, 3, 4, 5, 6]
    if (exchangingStatues.indexOf(this.basicData.status) != -1) {
      this.exchangeStatue = ExchangeOrderStatue.ExchangeOrderStatueExchanging
    } else if (this.basicData.status == 7) {
      this.exchangeStatue = ExchangeOrderStatue.ExchangeOrderStatueExchangeSuccess
    } else {
      this.exchangeStatue = ExchangeOrderStatue.ExchangeOrderStatueExchangeCancel
    }
  }

  static addUserInfo(dto: ExchangeOrderResponseVo, userMap: Map<string, AdminUserInfoVo>) {
    dto.applyUserName = userMap.get(dto.basicData.applyInfo.applyUser.userId)?.userName || ''
    return dto
  }
}
