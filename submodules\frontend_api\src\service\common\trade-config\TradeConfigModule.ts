import store from '@/store'
import QueryTradeConfigFactory from '@api/service/common/trade-config/QueryTradeConfigFactory'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
@Module({
  name: 'TradeConfigModule',
  dynamic: true,
  namespaced: true,
  store
})
class TradeConfigModule extends VuexModule {
  /**
   * 查询交易基础配置工厂
   */
  queryTradeConfigFactory: QueryTradeConfigFactory = new QueryTradeConfigFactory()
}
export default getModule(TradeConfigModule)
