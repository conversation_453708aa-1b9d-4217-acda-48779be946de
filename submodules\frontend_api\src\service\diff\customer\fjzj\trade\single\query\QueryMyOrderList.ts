import {
  CommoditySkuRequest1,
  default as MsTradeQueryFrontGatewayCourseLearningForestage,
  OrderRequest,
  OrderResponse,
  OrderSortField,
  OrderSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { Page, Response, ResponseStatus } from '@hbfe/common'
import GetOrderInMyselfgraphql from '@api/service/customer/trade/single/query/graphql/getOrderInMyself.graphql'
import CommodityRefundStatus, {
  CommodityRefundStatusEnum,
  CommodityRefundStatusWithGoods
} from '@api/service/common/return-order/enums/CommodityRefundStatus'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplicationDiff } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import QueryMyOrderList from '@api/service/customer/trade/single/query/QueryMyOrderList'
import { CourseType } from '@api/service/diff/customer/fjzj/train-class/enums/CourseType'
import HywSchemeRefundStatus, {
  HywSchemeRefundEnum
} from '@api/service/diff/customer/fjzj/train-class/enums/HywSchemeRefundStatus'
import fjzjTradeGateway from '@api/diff-gateway/platform-jxjypxtypt-fjzj-trade'
import TradeQueryForestage, {
  ReturnOrderRequest,
  SubOrderInfoRequest
} from '@api/diff-gateway/fjzj-trade-query-front-gateway-TradeQueryForestage'
import { OrderInfoRequest } from '@api/diff-gateway/zztt-trade-query-front-gateway-TradeQueryForestage'
export default class QueryMyOrderListDiff extends QueryMyOrderList {
  //
  totalSize = 0
  async pageReturnOrderInMyself(order: string) {
    const returnOrderRequest = new ReturnOrderRequest()
    returnOrderRequest.subOrderInfo = new SubOrderInfoRequest()
    returnOrderRequest.subOrderInfo.orderInfo = new OrderInfoRequest()
    returnOrderRequest.subOrderInfo.orderInfo.orderNoList = [order]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    return await TradeQueryForestage.pageReturnOrderInMyself({ page, request: returnOrderRequest })
  }

  /**
   * 个人-根据商品查询订单号 - 订单状态
   * @return 0 是未退款 1是订单数据未出来（提示内容：订单信息未完成，请稍候在进入） 2是在退款中 3 是已被换班 或者 在换班中
   */
  async queryOrderStatus(commoditySkuId: string) {
    const HywSchemeIdList = JSON.parse(
      ConfigCenterModule.getFrontendApplicationDiff(frontendApplicationDiff.hywSchemeId)
    ).map((item: { fjzjCommoditySkuId: string }) => item.fjzjCommoditySkuId)
    if (HywSchemeIdList.includes(commoditySkuId)) {
      const request = new OrderRequest()
      request.currentCommodity = new CommoditySkuRequest1()
      request.currentCommodity.commoditySkuIdList = [commoditySkuId]
      const sort = new OrderSortRequest()
      sort.field = OrderSortField.ORDER_NORMAL_TIME
      sort.policy = SortPolicy.DESC
      const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pageOrderInMyself({
        page: {
          pageNo: 1,
          pageSize: 10
        },
        request: request,
        sortRequest: [sort]
      })
      const refundStatus = await this.checkPublicRefundStatus(res?.data?.currentPageData[0].orderNo, 1)
      let orderStatus
      switch (refundStatus.data.current) {
        case HywSchemeRefundEnum.scheme_refunding:
          orderStatus = 2
          break
        case HywSchemeRefundEnum.public_refunded:
          orderStatus = 2
          break
        default:
          orderStatus = 0
          break
      }
      return orderStatus
    } else {
      return await super.queryOrderStatus(commoditySkuId)
    }
  }

  /**
   * 个人-根据商品查询订单号 - 订单状态
   * @return 0 是未退款 1是订单数据未出来（提示内容：订单信息未完成，请稍候在进入） 2是在退款中 3 是已被换班 或者 在换班中
   */
  async queryOrderStatusByOrderNo(orderNo: string, commoditySkuId?: string) {
    const HywSchemeIdList = JSON.parse(
      ConfigCenterModule.getFrontendApplicationDiff(frontendApplicationDiff.hywSchemeId)
    ).map((item: { fjzjCommoditySkuId: string }) => item.fjzjCommoditySkuId)
    if (HywSchemeIdList.includes(commoditySkuId)) {
      return await this.queryOrderStatus(commoditySkuId)
    }
    // const res = await MsTradeQueryFrontGatewayCourseLearningForestage.getOrderInMyself(orderNo)
    return await super.queryOrderStatusByOrderNo(orderNo, commoditySkuId)
  }
  /**
   *前往课程方案退款状态
   * @param orderNo 订单号
   * @param type 退课类型
   */
  async checkPublicRefundStatus(orderNo: string, type: CourseType): Promise<Response<HywSchemeRefundStatus>> {
    const refundStatus = new HywSchemeRefundStatus()
    const response = new Response<HywSchemeRefundStatus>()
    const res = await fjzjTradeGateway.queryHymSchemeRefundInfo(orderNo)
    if (res.status.isSuccess() && res.data) {
      const publicCourseRefundStatus = res.data.publicCourse
      if (publicCourseRefundStatus.refund && publicCourseRefundStatus.completed && type === CourseType.public_course) {
        //公需课退款完成
        refundStatus.current = HywSchemeRefundEnum.public_refunded
      }
      if (publicCourseRefundStatus.refund && !publicCourseRefundStatus.completed) {
        //有课程处于退款状态中
        refundStatus.current = HywSchemeRefundEnum.scheme_refunding
      }
      response.status = new ResponseStatus(Number(res.data.code), res.data.message)
      response.data = refundStatus
    }
    return response
  }
}
