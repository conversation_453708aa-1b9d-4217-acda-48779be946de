<template>
  <div class="f-p30">
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <div class="m-as-form">
          <div class="item">
            <div class="tit">媒体源：</div>
            <div class="con">
              <el-radio-group :value="mediaType" @change="mediaTypeChange()" :disabled="!isCreate">
                <el-radio :label="1">华为云</el-radio>
                <el-radio :label="2">外链地址</el-radio>
              </el-radio-group>
              <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                <div slot="content">
                  <p>1.如是媒体上传形式选择华为云，如是外部链接选择外链地址，请根据实际资源情况进行选择。</p>
                  <p>2.媒体支持文件类型与格式：</p>
                  <ul style="padding: 0 25px; margin: 0;">
                    <li>文档格式：doc、xls、xlsx、pdf</li>
                    <li>视频格式：mp4、avi、wmv</li>
                  </ul>
                  <p>
                    3.外部链接形式：提供播放视频.m3u8地址，建议不要带授权或防盗链，采用可以直接播放方式。视频播放地址格式如：https://xxx.com/xxxxx.m3u8。
                  </p>
                </div>
              </el-tooltip>
            </div>
          </div>
        </div>
        <!--华为云-->
        <el-form
          :model="formData"
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          class="m-form"
          v-show="mediaType == MediaSourceTypeEnum.huawei"
        >
          <el-form-item label="媒体文件：">
            <upload-file
              @setFileItem="setFileItem"
              @fileFuncall="fileFuncall"
              :file-type.sync="fileType"
              :isCreate="isCreate"
              :isBeginUsed="allDetail.isBeingUsed"
              :uploadUtil="uploadUtil"
              ref="uploadFile"
            ></upload-file>
            <span v-if="fileList[0]">
              <div class="m-file-upload f-mt15" v-for="(item, index) in fileList[0].courseWares" :key="index">
                <!--文档-->
                <i class="icon el-icon-document" v-if="fileType === 1"></i>
                <!--视频-->
                <i class="icon el-icon-film" v-else-if="fileType === 2"></i>
                <!--压缩包-->
                <i class="icon el-icon-files" v-else></i>
                <span class="name">{{ item.name }}</span>
                <el-progress :text-inside="true" :stroke-width="18" :percentage="uploadUtil.progress"></el-progress>
                <span class="time">
                  <template v-if="item.timeLength != -1">{{ formatDuring(item.timeLength) }}</template>
                </span>
                <el-button type="text" @click="sureDeleteVisible = true">删除</el-button>
              </div>
            </span>
          </el-form-item>
          <el-form-item label="媒体时长：" required v-show="fileType === 1">
            <el-row>
              <el-col :span="3">
                <el-form-item required prop="hour">
                  <el-input class="input-num" v-model="formData.hour" :disabled="!isCreate && allDetail.isBeingUsed" />
                </el-form-item>
              </el-col>
              <el-col :span="1">
                <span class="f-mlr10">时</span>
              </el-col>
              <el-col :span="3">
                <el-form-item required prop="minute">
                  <el-input
                    class="input-num"
                    v-model="formData.minute"
                    :disabled="!isCreate && allDetail.isBeingUsed"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="1">
                <span class="f-mlr10">分</span>
              </el-col>
              <el-col :span="3">
                <el-form-item required prop="second">
                  <el-input
                    class="input-num"
                    v-model="formData.second"
                    :disabled="!isCreate && allDetail.isBeingUsed"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="1">
                <span class="f-mlr10">秒</span>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label=" " class="is-text" v-if="!isCreate">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              课件未被课程引用可直接替换媒体文件。如课件已被课程引用，如需替换媒体文件，要求替换后的媒体与替换前的媒体文件格式一致、视频的时长需大于等于替换前的媒体文件大小。
            </el-alert>
          </el-form-item>
        </el-form>
        <!--外链-->
        <el-form
          :model="outsideChainForm"
          :rules="outsideChainRules"
          ref="outsideChain"
          label-width="120px"
          class="m-form"
          v-if="mediaType == MediaSourceTypeEnum.outer"
        >
          <span>
            <el-form-item label="标清地址：" prop="standardAddress">
              <el-input
                clearable
                placeholder="请输入具体的媒体播放链接地址，视频播放地址格式如: https/lxxxcom/xxxxx.m3u8"
                v-model="outsideChainForm.standardAddress"
              />
            </el-form-item>
            <el-form-item label="高清地址：" prop="highAddress">
              <el-input
                clearable
                placeholder="请输入具体的媒体播放链接地址，视频播放地址格式如: https/lxxxcom/xxxxx.m3u8"
                v-model="outsideChainForm.highAddress"
              />
            </el-form-item>
            <el-form-item label="超清地址：" prop="superAddress">
              <el-input
                clearable
                placeholder="请输入具体的媒体播放链接地址，视频播放地址格式如: https/lxxxcom/xxxxx.m3u8"
                v-model="outsideChainForm.superAddress"
              />
            </el-form-item>
          </span>

          <el-form-item label=" " class="is-text">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              解析完成后显示媒体时长，提供播放视频.m3u8地址，建议不要带授权或防盗链，采用可以直接播放方式。视频播放地址格式如：https://xxx.com/xxxxx.m3u8，以上三种播放地址必须填写一项。
            </el-alert>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-dialog title="删除确认" :visible.sync="sureDeleteVisible" width="450px" class="m-dialog">
      <div>确认删除课件文件</div>
      <div slot="footer">
        <el-button @click="sureDeleteVisible = false">取 消</el-button>
        <el-button type="primary" @click="deleteFile">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import UploadFile from '@hbfe/jxjy-admin-courseware/src/components/upload-file.vue'
  import CourseWareCreate from '@api/service/common/models/course/course-ware/CourseWareCreate'
  import CourseChapterCreate from '@api/service/common/models/course/create/CourseChapterCreate'
  import previewCourseware from '@hbfe/jxjy-admin-components/src/previewer/Previewer'
  import { ElForm } from 'element-ui/types/form'
  import { MediaSourceTypeEnum } from '@api/service/management/resource/courseware/enum/MediaSourceType'
  import UpdateCoursewareVo from '@api/service/management/resource/courseware/mutation/vo/UpdateCoursewareVo'
  import CoursewareType, { CoursewareTypeEnum } from '@api/service/management/resource/courseware/enum/CoursewareType'
  import UploadUtil from '@api/service/common/obs/UploadUtil'

  class CourseChapterPercen extends CourseWareCreate {
    percen = 0
    uid = ''
    mediaType = MediaSourceTypeEnum.huawei
    standardAddress = ''
    highAddress = ''
    superAddress = ''
  }

  @Component({
    components: { UploadFile }
  })
  export default class extends Vue {
    @Ref('ruleForm') ruleForm: any
    @Ref('outsideChain') outsideChain: ElForm
    @Ref('uploadFile') uploadFile: UploadFile

    @Prop({
      type: Array,
      default: () => {
        return new Array<any>()
      }
    })
    value: Array<any>
    @Prop({
      type: String,
      default: ''
    })
    coursewareFormat: string

    // @Prop({
    //   type: Number,
    //   default: 1
    // })
    // fileType: number
    // 文档:1 视频:2 压缩包:3
    fileType = 2
    @Prop({
      type: String,
      default: ''
    })
    name: string

    //详情数据
    @Prop({
      type: UpdateCoursewareVo,
      default: () => new UpdateCoursewareVo()
    })
    allDetail: UpdateCoursewareVo

    // 上传实例化
    uploadUtil = new UploadUtil()

    timeLength = -1
    // 媒体类型 1：华为云 2：外链课件
    mediaType = MediaSourceTypeEnum.huawei
    // 标清
    standardAddress = ''
    // 高清
    highAddress = ''
    // 超清
    superAddress = ''
    index = 0
    isCreate = false
    // 媒体源赋值
    MediaSourceTypeEnum = MediaSourceTypeEnum

    sureDeleteVisible = false

    // 初始化数组
    oldFileList = {
      courseWares: [
        {
          standardAddress: '',
          highAddress: '',
          superAddress: ''
        }
      ]
    }
    formData = {
      hour: 0,
      minute: 0,
      second: 0
    }

    outsideChainForm = {
      standardAddress: '',
      highAddress: '',
      superAddress: ''
    }

    rules = {
      hour: [{ required: true, message: '请选择时间' }],
      second: [{ required: true, message: '请选择时间' }],
      minute: [{ required: true, message: '请选择时间' }]
    }
    outsideChainRules = {
      standardAddress: [{ validator: this.validatorComment, trigger: 'blur' }],
      highAddress: [{ validator: this.validatorComment, trigger: 'blur' }],
      superAddress: [{ validator: this.validatorComment, trigger: 'blur' }]
    }

    // 自定义校验
    validatorComment(rule: any, value: string, callback: any) {
      if (value) {
        const m3u8Regex = /\.m3u8$/
        const isMatch = m3u8Regex.test(value)
        if (isMatch) {
          callback()
        } else {
          callback(new Error('播放链接需提供.m3u8地址'))
        }
      } else {
        callback()
      }
    }

    // m3u8正则

    get fileList() {
      return this.value
    }

    set fileList(val: Array<any>) {
      this.$emit('input', val)
    }

    @Watch('name')
    changeFileName() {
      const curFileList = [
        {
          courseWares: [
            {
              abouts: '',
              coursewareResourcePath: '',
              cwyId: '',
              name: this.name + `${this.deatilTypeAll}`,
              percen: this.allDetailData('percen'),
              resourceMD5: '',
              timeLength: this.allDetailData('timeLength'),
              uid: 111111111,
              usable: true,
              mediaType: this.allDetailData('mediaType'),
              standardAddress: this.allDetailData('standardAddress'),
              highAddress: this.allDetailData('highAddress'),
              superAddress: this.allDetailData('superAddress')
            }
          ]
        }
      ]
      this.fileList = curFileList
    }

    allDetailData(target: string) {
      console.log(this.allDetail, 'this.allDetail.isOuter')
      let callBack: any = ''
      switch (target) {
        case 'standardAddress':
          callBack = this.allDetail?.standardAddress == undefined ? '' : this.allDetail?.standardAddress
          break
        case 'highAddress':
          callBack = this.allDetail?.highAddress == undefined ? '' : this.allDetail?.highAddress
          break
        case 'superAddress':
          callBack = this.allDetail?.superAddress == undefined ? '' : this.allDetail?.superAddress
          break
        case 'percen':
          callBack = this.isCreate ? 0 : 100
          break
        case 'mediaType':
          callBack = this.allDetail.isOuter ? MediaSourceTypeEnum.outer : MediaSourceTypeEnum.huawei
          break
        case 'timeLength':
          callBack = this.allDetail.resourceDataDto.timeLength ? this.allDetail.resourceDataDto.timeLength : -1
          break
      }
      if (this.allDetail?.isOuter) {
        this.outsideChainForm.standardAddress = this.allDetail?.standardAddress
        this.outsideChainForm.highAddress = this.allDetail?.highAddress
        this.outsideChainForm.superAddress = this.allDetail?.superAddress
      }
      this.mediaType = this.allDetail?.isOuter ? MediaSourceTypeEnum.outer : MediaSourceTypeEnum.huawei
      this.fileType = this.allDetail?.type.current == CoursewareTypeEnum.document ? 1 : 2
      if (this.allDetail.resourceDataDto.timeLength > 0) {
        this.formData.hour = Math.floor(this.allDetail.resourceDataDto.timeLength / 3600)
        this.formData.minute = Math.floor((this.allDetail.resourceDataDto.timeLength % 3600) / 60)
        this.formData.second = Math.floor((this.allDetail.resourceDataDto.timeLength % 3600) % 60)
      }
      return callBack
    }

    mounted() {
      if (this.$route.path.indexOf('create') != -1) {
        this.isCreate = true
      } else {
        this.uploadUtil.progress = 100
        this.isCreate = false
      }
      console.log('挂载')
    }

    // 外链传参
    outsideChainCall() {
      if (this.mediaType == MediaSourceTypeEnum.outer) {
        const obj = new CourseChapterPercen()
        // 外链参数添加
        obj.mediaType = Number(this.mediaType)
        obj.standardAddress = this.outsideChainForm.standardAddress
        obj.highAddress = this.outsideChainForm.highAddress
        obj.superAddress = this.outsideChainForm.superAddress
        this.oldFileList = Object.assign(this.oldFileList, this.fileList)
        this.oldFileList.courseWares[0] = obj
        return this.oldFileList
      }
    }

    // 设置上传的内容 对标上传文件
    setFileItem(file: any) {
      console.log(file, '回调')
      const obj = new CourseChapterPercen()
      obj.name = file.name
      obj.uid = file.uid
      obj.percen = 0
      obj.timeLength = this.timeLength

      if (this.fileList[this.index]?.courseWares) {
        //this.fileList[this.index].courseWares.push(obj)
        const oldFileList = Object.assign([], this.fileList)
        oldFileList[this.index].courseWares[0] = obj
        this.fileList = oldFileList
      } else {
        const arr = new CourseChapterCreate()
        arr.sort = this.fileList.length
        arr.courseWares = new Array<CourseChapterPercen>()
        this.fileList.push(arr)
        this.fileList[this.index].courseWares.push(obj)
      }
      // 也可设置为-1 到时候页面上用v-if进行判断
      this.timeLength = -1 //设置完毕时间后重置时间长度
    }

    fileFuncall(uid: string, callBack: any) {
      this.fileList.forEach(item => {
        item.courseWares.forEach((subItem: CourseChapterPercen, subIndex: number) => {
          if (subItem.uid === uid) {
            callBack(subItem, subIndex, item)
          }
        })
      })
    }
    // 格式化时间
    formatDuring(value: number) {
      let secondTime = parseInt(String(value)) // 秒
      let minuteTime = 0 // 分
      let hourTime = 0 // 小时
      if (secondTime > 60) {
        //如果秒数大于60，将秒数转换成整数
        //获取分钟，除以60取整数，得到整数分钟
        minuteTime = Math.floor(secondTime / 60)
        //获取秒数，秒数取佘，得到整数秒数
        secondTime = Math.floor(secondTime % 60)
        //如果分钟大于60，将分钟转换成小时
        if (minuteTime > 60) {
          //获取小时，获取分钟除以60，得到整数小时
          hourTime = Math.floor(minuteTime / 60)
          //获取小时后取佘的分，获取分钟除以60取佘的分
          minuteTime = Math.floor(minuteTime % 60)
        }
      }
      return (
        hourTime.toString().padStart(2, '0') +
        ':' +
        minuteTime.toString().padStart(2, '0') +
        ':' +
        secondTime.toString().padStart(2, '0')
      )
    }

    deleteFile() {
      // 重置当前文件项的 courseWares 属性
      if (this.fileList.length) {
        this.fileList = []
      }
      this.sureDeleteVisible = false
      this.uploadFile.loading = false
    }

    preview() {
      previewCourseware.previewCourseware(this.$route.params.id, this.allDetail.type)
    }

    validateMediae() {
      return new Promise(resolve => {
        let result = {
          status: true
        }
        if (this.fileType !== 1) {
          this.$emit('update:time', 0)
          result = {
            status: true
          }
        } else {
          this.ruleForm.validate((valid: any) => {
            const time: any =
              Number(this.formData.hour) * 60 * 60 + Number(this.formData.minute) * 60 + Number(this.formData.second)
            if (valid && time > 0) {
              this.$emit('update:time', time)
              result = {
                status: true
              }
            } else {
              this.$message({
                message: '文档需要设置时长',
                type: 'warning'
              })
              this.$emit('update:time', 0)
              result = {
                status: false
              }
            }
          })
        }

        resolve(result)
      })
    }

    // 外链校验
    validateOffMedia() {
      return new Promise(resolve => {
        let result = {
          status: true
        }
        this.outsideChain.validate((valid: any) => {
          if (!valid) {
            this.$confirm('播放链接需提供.m3u8地址。', '提示', {
              confirmButtonText: '我知道了',
              type: 'warning',
              showCancelButton: false
            }).then(async () => {
              console.log('点击了我知道了')
            })
            result = {
              status: true
            }
          } else {
            result = {
              status: false
            }
          }
        })
        resolve(result)
      })
    }

    //媒体源按钮切换
    mediaTypeChange() {
      if (!this.isFormEmpty()) {
        this.$confirm('当前上传形式已添加对应的媒体信息，切换后已上传内容将会清空，是否确认切换？', '提示', {
          confirmButtonText: '我知道了',
          type: 'warning'
        }).then(async () => {
          this.mediaType =
            this.mediaType === MediaSourceTypeEnum.huawei ? MediaSourceTypeEnum.outer : MediaSourceTypeEnum.huawei
          // 初始化或重置通用属性
          this.fileType = 2
          this.formData = { hour: 0, second: 0, minute: 0 }

          // 重置 outsideChainForm
          this.outsideChainForm = {
            standardAddress: '',
            highAddress: '',
            superAddress: ''
          }

          // 如果 outsideChain 表单存在，则重置其字段
          if (this.outsideChain && typeof this.outsideChain.resetFields === 'function') {
            this.outsideChain.resetFields()
          }

          // 重置当前文件项的 courseWares 属性
          if (this.fileList.length) {
            this.fileList = []
          }
        })
      } else {
        this.mediaType =
          this.mediaType === MediaSourceTypeEnum.huawei ? MediaSourceTypeEnum.outer : MediaSourceTypeEnum.huawei
      }
    }

    // 判断表单是否为空
    isFormEmpty() {
      const getOutsideChainFormValues = () =>
        [
          this.outsideChainForm.standardAddress,
          this.outsideChainForm.highAddress,
          this.outsideChainForm.superAddress
        ].some(Boolean)
      if (this.mediaType === 2) {
        return !getOutsideChainFormValues()
      } else {
        return !this.fileList.length || Object.keys(this.fileList[this.index]?.courseWares)?.length === 0
      }
    }

    get deatilTypeAll() {
      return this.allDetail.type.current == CoursewareTypeEnum.document ? '.pdf' : '.m3u8'
    }
  }
</script>
