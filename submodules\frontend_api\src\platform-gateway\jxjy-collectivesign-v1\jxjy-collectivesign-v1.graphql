schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""异步导出提交集体报名失败数据
		@param collectiveSignupNo 集体报名编号
		@return 导出数据ID
	"""
	asyncExportCollectiveSignupExcuteFailExcel(collectiveSignupNo:String!):String
	"""导出异常数据
		@param request 导出异常数据请求
		@return 导出异常数据响应
	"""
	exportFail(request:ExportTaskDataRequest):ExportTaskDataResponse
	"""查询导入/导出任务执行情况
		@param request 任务查询参数
		@param page    分页数据
		@return 任务执行情况分页
	"""
	queryForTask(request:QueryForTaskInfoRequest,page:Page):QueryTaskInfoResponsePage @page(for:"QueryTaskInfoResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""导出任务数据地址请求"""
input ExportTaskDataRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.ExportTaskDataRequest") {
	"""任务编号"""
	taskId:String!
}
"""查询任务信息"""
input QueryForTaskInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.QueryForTaskInfoRequest") {
	"""所属批次单编号"""
	batchNo:String!
	"""分类"""
	category:String!
}
"""导出任务数据地址响应
	<AUTHOR>
	@date 2022/11/7 14:12
"""
type ExportTaskDataResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.ExportTaskDataResponse") {
	"""文件名称"""
	fileName:String
	"""文件路径"""
	filePath:String
}
"""查询任务信息"""
type QueryTaskInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.QueryTaskInfoResponse") {
	"""任务编号"""
	mainTaskId:String
	"""起始执行时间"""
	executeStartTime:DateTime
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
}

scalar List
type QueryTaskInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [QueryTaskInfoResponse]}
