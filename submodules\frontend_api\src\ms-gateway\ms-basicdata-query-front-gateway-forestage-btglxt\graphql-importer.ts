import findRoleListByIdentity from './queries/findRoleListByIdentity.graphql'
import getEnterpriseUnitAdminInfoInMyself from './queries/getEnterpriseUnitAdminInfoInMyself.graphql'
import getEnterpriseUnitInfoInLibraryTeacher from './queries/getEnterpriseUnitInfoInLibraryTeacher.graphql'
import getEnterpriseUnitInfoInStudent from './queries/getEnterpriseUnitInfoInStudent.graphql'
import getStudentInfoInMyself from './queries/getStudentInfoInMyself.graphql'
import pageCertificateInfoInStudentMyself from './queries/pageCertificateInfoInStudentMyself.graphql'

export {
  findRoleListByIdentity,
  getEnterpriseUnitAdminInfoInMyself,
  getEnterpriseUnitInfoInLibraryTeacher,
  getEnterpriseUnitInfoInStudent,
  getStudentInfoInMyself,
  pageCertificateInfoInStudentMyself
}
