import { Response, UiPage } from '@hbfe/common'
import MsExamQueryBackStageGateway, {
  AutomaticPublishPattern,
  LibraryFixedQuestionScopeSetting,
  LibraryQuestionScopeSetting,
  LibraryRequest,
  LibraryResponse,
  PaperPublishConfigureResponse,
  QuestionScopeSetting
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import AutomaticExamPaperDetailVo from './vo/AutomaticExamPaperDetailVo'
import { QuestionScopeSettingTypes } from '../enum/ExamScopeSettingTypes'

/**
 * @description: 查询试卷详情
 */
class QueryExamPaperDetail {
  // 试卷id
  private paperId = ''
  // 题库id集合
  private questionLibraryIds = new Array<string>()
  private questionLibraryNames = new Array<string>()

  constructor(id: string) {
    this.paperId = id
  }

  // 智能出卷详情
  async queryAutomaticExamPaperDetail(): Promise<Response<AutomaticExamPaperDetailVo>> {
    const res = await MsExamQueryBackStageGateway.getPaperPublishConfigureInServicer(this.paperId)
    const response = new Response<AutomaticExamPaperDetailVo>()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.status = res.status
    const detail = new AutomaticExamPaperDetailVo()
    // 获取题库名称数组
    const list = await this.getQuestionBankLibraryName(res.data)
    response.data = detail.from(res?.data, list) as AutomaticExamPaperDetailVo
    return response
  }

  // 获取题库名称
  private async getQuestionBankLibraryName(item: PaperPublishConfigureResponse) {
    const pattern = item?.publishPattern as AutomaticPublishPattern
    const qRule = pattern?.questionExtractRule?.questionScopes as Array<QuestionScopeSetting>
    if (qRule?.length) {
      if (qRule[0].type === QuestionScopeSettingTypes.LibraryFixedQuestionExtractSetting) {
        // 按题库指定数量出题
        const arr = qRule[0] as LibraryFixedQuestionScopeSetting
        this.questionLibraryIds =
          arr.libraryMapQuestionNumSettings?.map(item => {
            return item.libraryId
          }) || []
      } else if (qRule[0].type === QuestionScopeSettingTypes.LibraryQuestionScopeSetting) {
        // 按题库数量出题
        const temp = qRule[0] as LibraryQuestionScopeSetting
        this.questionLibraryIds = temp.libraryIds || []
      } else {
        // 按学员课程id出题
        this.questionLibraryIds = []
      }
    }
    const result = await this.queryQuestionBankLibrary(this.questionLibraryIds)
    if (!result?.status?.isSuccess()) {
      console.error('获取题库名称失败！')
      this.questionLibraryNames = []
    } else {
      this.questionLibraryNames = result.data
    }
    return this.questionLibraryNames
  }

  /**
   * @description: 查询题库分页 【用于按题库出题的试卷】
   */
  private async queryQuestionBankLibrary(libraryIdList: Array<string>): Promise<Response<Array<string>>> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = libraryIdList?.length
    const req = new LibraryRequest()
    req.libraryIdList = [...new Set(libraryIdList)]

    const result = await MsExamQueryBackStageGateway.pageLibraryInServicer({
      page: page,
      request: req
    })
    const response = new Response<Array<string>>()
    if (!result?.status?.isSuccess()) {
      response.status = result.status
      return response
    }
    // 题库列表
    const resultList = new Array<string>()
    // 获取题库名称
    result.data?.currentPageData?.forEach((item: LibraryResponse) => {
      resultList.push(item.libraryName)
    })
    response.data = resultList
    response.status = result.status
    return response
  }

  // 查询试卷详情
  // async queryExamPaperDetail() {
  //   await this.requestByPatternTypes()
  // }

  /* 根据出卷模式，调用对应接口请求 */
  // requestByPatternTypes(type: PublishPatternTypes) {
  //   const queryMethods = {
  //     [PublishPatternTypes.AutomaticPublishPattern]: async () => {
  //       return await this.queryAutomaticExamPaperDetail()
  //     }
  //   }
  //   if (!queryMethods[type]) {
  //     console.error('查询的出卷模式详情不存在！')
  //     throw new Error('查询的出卷模式详情不存在！')
  //   }
  //   return queryMethods[type]()
  // }
}

export default QueryExamPaperDetail
