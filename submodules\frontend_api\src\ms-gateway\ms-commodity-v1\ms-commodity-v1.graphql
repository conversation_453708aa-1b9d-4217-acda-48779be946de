"""独立部署的微服务,K8S服务名:ms-commodity-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""校验商品是否可以购买
		@param commodityInfo 请求验证信息
		@return 验证结果信息
	"""
	validateCommodity(commodityInfo:ValidateCommodityRequest):ValidateCommodityResponse
}
type Mutation {
	"""下架商品
		@param request 商品下架信息
		@return true/false,下架成功/下架失败
	"""
	offShelve(request:CommoditySkuOffShelveRequest):Boolean!
	"""上架商品
		@param request 商品上架信息
		@return true/false,上架成功/上架失败
	"""
	onShelve(request:CommoditySkuOnShelveRequest):Boolean!
}
"""<AUTHOR>
input CommoditySkuOffShelveRequest @type(value:"com.fjhb.ms.commodity.v1.kernel.gateway.graphql.request.CommoditySkuOffShelveRequest") {
	"""商品SkuID"""
	id:String!
}
"""<AUTHOR>
input CommoditySkuOnShelveRequest @type(value:"com.fjhb.ms.commodity.v1.kernel.gateway.graphql.request.CommoditySkuOnShelveRequest") {
	"""商品SkuID"""
	id:String!
}
"""<AUTHOR>
input ValidateCommodityRequest @type(value:"com.fjhb.ms.commodity.v1.kernel.gateway.graphql.request.ValidateCommodityRequest") {
	"""商品Sku"""
	commoditySkuId:String!
	"""购买渠道类型
		1:用户自主购买
		2:集体缴费
		3:管理员导入
	"""
	channelType:Int!
	"""终端唯一编码
		Web:Web端
		IOS:IOS端
		Android:安卓端
		WechatMini:微信小程序
		WechatOfficial:微信公众号
	"""
	terminalCode:String!
}
type CommodityInfo @type(value:"com.fjhb.ms.commodity.v1.api.dto.ValidateCommodityResult$CommodityInfo") {
	"""资源类型，该类型由发布为商品的线上资源定义"""
	resourceType:String
	"""资源ID"""
	resourceId:String
	"""商品所属服务商编号"""
	servicerId:String
}
"""<AUTHOR>
type ValidateCommodityResponse @type(value:"com.fjhb.ms.commodity.v1.kernel.gateway.graphql.response.ValidateCommodityResponse") {
	"""验证结果
		200：验证通过
		30001：商品不存在
		30002：商品已下架
		30003：不支持当前渠道购买该商品
		30004：当前渠道已关闭
	"""
	code:String
	"""验证失败原因"""
	message:String
	"""资源信息"""
	data:CommodityInfo
}

scalar List
