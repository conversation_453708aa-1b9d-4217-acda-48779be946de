import CourseTeacher from '@api/service/common/models/course/CourseTeacher'
import CourseChapter from '@api/service/common/models/course/CourseChapter'
import Courseware from '@api/service/common/models/course/Courseware'
import CourseCategory from '@api/service/common/models/course/course-category/CourseCategory'
import { BTPXCourseItemResponse, TagDTO1 } from '@api/gateway/btpx@Course-default'

/**
 * 课程
 */
class Course {
  /**
   * 课程编号
   */
  id = ''
  /**
   * 单位id
   */
  unitId = ''
  /**
   * 单位名称
   */
  unitName = ''
  /**
   * 课程名称
   */
  name = ''
  /**
   * 封面图片路径
   */
  iconPath = ''
  /**
   * 学时
   */
  period = 0.0
  /**
   * 课程总章节数
   */
  totalLecture = 0
  /**
   * 已更新章节数
   */
  alreadyUpdateLecture = 0
  /**
   * 总时长
   */
  timeLength = 0
  /**
   * 课程简介
   */
  abouts = ''
  /**
   * 课程的课件状态，0表示解析中，1表示解析成功，2表示解析失败
   */
  status: number
  /**
   * 课程分类
   */
  categoryList: Array<CourseCategory> = new Array<CourseCategory>()
  /**
   * 课程章节
   */
  courseOutline: Array<CourseChapter> = new Array<CourseChapter>()

  /**
   * 课件
   */
  courseWares: Array<Courseware>
  /**
   * 课程教师信息
   */
  teacherList: Array<CourseTeacher> = new Array<CourseTeacher>()

  /**
   * 是否启用
   */
  enabled: boolean

  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建者编号
   */
  createUsrId: string
  /**
   * 共享来源id
   */
  shareSourceId: string
  /**
   * 工种
   */
  workTypes: Array<TagDTO1>
  /**
   * 标签（除工种外的标签）
   */
  otherTags: Array<TagDTO1>
  /**
   * 开放使用的平台
   */
  openUsedPlatforms: Array<string>
  /**
   * 开放使用的平台名字
   */
  openUsedPlatformNames: Array<string>
  /**
   * 资源供应商id
   */
  resourceVendorId: string
  /**
   * 是否试听
   */
  customStatus: number
  /**
   * 工种分类->工种路径，以/开始,/分隔，只需要填两级
   */
  workTypePaths?: Array<string>
  /**
   * 课件供应商id
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称
   */
  coursewareSupplierName: string

  /**
   * 复制远程对象为本地
   * @param item
   */
  fromPageRemote(item: BTPXCourseItemResponse) {
    this.id = item.id
    this.unitId = item.unitId
    this.unitName = item.unit?.name
    Object.assign(this.categoryList, item.categoryList)
    this.name = item.name
    this.iconPath = item.iconPath
    this.period = item.period
    this.abouts = item.abouts
    this.enabled = item.enabled
    this.status = item.status
    this.createTime = item.createTime
    this.timeLength = item.timeLength
    this.createUsrId = item.createUsrId
    this.shareSourceId = item.shareSourceId
    this.workTypes = item.workTypes
    this.otherTags = item.otherTags
    this.openUsedPlatforms = item.openUsedPlatforms
    Object.assign(this.teacherList, item.teachers)
    this.resourceVendorId = item.resourceVendorId
    this.workTypePaths = item.workTypePaths
    this.coursewareSupplierId = item.coursewareSupplierId
    return this
  }
}

export default Course
