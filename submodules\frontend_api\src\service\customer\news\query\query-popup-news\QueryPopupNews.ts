/*
 * @Description: 弹窗资讯
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-24 20:04:16
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 11:56:43
 */
import QueryNewsDetailVo from '@api/service/customer/news/query/query-popup-news/vo/NewsDetail'
import BasicDataQueryForestage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

class QueryPopupNews {
  async queryPopupNews(topNum = 1): Promise<QueryNewsDetailVo> {
    const { data } = await BasicDataQueryForestage.listPopUpsNews({ topNum })
    const response = await BasicDataQueryForestage.listRootNewsCategory(1)
    // * 目前只有帮助中心有二级分类
    const categorysResponse = await Promise.all(
      data.map(async res => {
        if (res.necId)
          return BasicDataQueryForestage.listChildNewsCategory({
            status: 1,
            necId: res.necId
          })
      })
    )
    const temp = response.data
    if (categorysResponse[0]) {
      categorysResponse.map(res => temp.push(...res.data.map(item => item)))
    }
    if (data.length !== 0) {
      const response = await BasicDataQueryForestage.getCommonNewsDetailWithPreviousAndNext({ newId: data[0].newId })
      const newsDetail = QueryNewsDetailVo.from(response.data)
      if (newsDetail?.necId) {
        newsDetail.necName = temp.find(res => res.newsCategoryId === newsDetail.necId)?.categoryName
      }
      return newsDetail
    }
    return new QueryNewsDetailVo()
  }

  /**
   * 查询专题弹窗公告
   * @param specialId 专题Id
   * @param topNum
   */
  async querySpecialPopupNews(specialId: string, topNum = 1): Promise<QueryNewsDetailVo> {
    const { data } = await BasicDataQueryForestage.listTrainingChannelPopUpsNews({
      topNum,
      specialSubjectId: specialId
    })
    const response = await BasicDataQueryForestage.listRootNewsCategory(1)
    // * 目前只有帮助中心有二级分类
    const categorysResponse = await Promise.all(
      data.map(async res => {
        if (res.necId)
          return BasicDataQueryForestage.listChildNewsCategory({
            status: 1,
            necId: res.necId
          })
      })
    )
    const temp = response.data
    if (categorysResponse[0]) {
      categorysResponse.map(res => temp.push(...res.data.map(item => item)))
    }
    if (data.length !== 0) {
      const response = await BasicDataQueryForestage.getTrainingChannelCommonNewsDetailWithPreviousAndNext({
        newId: data[0].newId,
        specialSubjectId: specialId
      })
      const newsDetail = QueryNewsDetailVo.from(response.data)
      if (newsDetail?.necId) {
        newsDetail.necName = temp.find(res => res.newsCategoryId === newsDetail.necId)?.categoryName
      }
      return newsDetail
    }
    return new QueryNewsDetailVo()
  }
}
export default QueryPopupNews
