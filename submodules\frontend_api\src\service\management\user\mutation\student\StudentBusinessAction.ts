import Basicdata, { ChangePhoneByAdminRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { Response, ResponseStatus } from '@hbfe/common'
/**
 * 操作学员业务类
 */
class StudentBusinessAction {
  /**
   *学员id
   */
  private studentId = ''

  constructor(id: string) {
    this.studentId = id
  }

  /*
    学员微信解绑
  */
  async doUnBindStudentWx(): Promise<ResponseStatus> {
    const { status } = await Basicdata.unbindWeChatOpenPlatform(this.studentId)
    return status
  }

  /**
   * 修改学员手机号
   * @param oldPhone 旧手机号
   * @param newPhone 新手机号
   */
  async doChangeStudentPhone(oldPhone: string, newPhone: string): Promise<Response<void>> {
    const rq = new ChangePhoneByAdminRequest()
    rq.userId = this.studentId
    rq.newPhone = newPhone
    rq.oldPhone = oldPhone
    const res = await Basicdata.ChangePhoneByAdmin(rq)

    return res
  }
}
export default StudentBusinessAction
