<!--岗位类别-->
<template>
  <el-select v-model="selectValue" :placeholder="placeholder" class="form-l" filterable clearable multiple>
    <el-option
      v-for="item in optionList"
      :label="showLabel(item)"
      :value="item.propertyId"
      :key="item.propertyId"
      :disabled="isDisabled && item.name != '全部'"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Mixins, Component, Prop, Watch } from 'vue-property-decorator'
  import QueryDictList from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryDictList'
  import SkuDictType from '@api/service/common/basic-data-dictionary/model/SkuDictType'
  import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
  import CommonSkuMixins from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSkuMixins'

  @Component
  export default class extends Mixins(CommonSkuMixins) {
    /**
     * 父级sku的值(培训对象id)
     */
    gradePropertyId: string[] = []

    /**
     * 是否需要获取展示名称
     */
    @Prop({
      type: Boolean,
      default: false
    })
    showName: boolean

    /**
     * 确认行业id
     */
    @Prop({
      type: String,
      default: ''
    })
    idType: IndustryIdEnum

    /**
     * sku 列表
     */
    skuList = new Array<SkuDictType>()
    /**
     * 选项列表
     */
    optionList = new Array<SkuDictType>()

    /**
     * 查询下级分类ids
     */
    propertyIdList: string[] = []

    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: SkuDictType) => {
        if (this.showName) {
          return item.name
        }
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    // 赋值方法
    setValue() {
      if (this.gradePropertyId[0] != '-1') {
        this.optionList =
          this.skuList?.filter((item) => {
            return this.gradePropertyId.includes(item.parentId)
          }) || []
        if (this.optionList.length) {
          const param = new SkuDictType()
          param.propertyId = '-1'
          param.name = '全部'
          param.sort = 0
          param.showName = ''
          this.optionList.unshift(param)
        }
        // 按需踢除
        if (this.optionList) {
          this.selectValue = this.selectValue.filter((item) => {
            return this.optionList.map((item) => item.propertyId).includes(item)
          })
          this.$emit('input', this.selectValue)
        }
      } else {
        // 如果没有父ID 显示所有
        this.optionList = this.skuList
        if (this.optionList[0]?.propertyId != '-1') {
          const param = new SkuDictType()
          param.propertyId = '-1'
          param.name = '全部'
          param.sort = 0
          param.showName = ''
          this.optionList.unshift(param)
        }
      }
    }

    /**
     * 查询sku列表
     */
    async querySkuList() {
      try {
        this.skuList = await QueryDictList.queryList(
          this.categoryCode,
          this.industryPropertyId,
          this.gradePropertyId,
          this.propertyIdList
        )
      } catch (e) {
        console.log(e)
      }
    }

    /**
     * 初始化
     */
    async initPositionCategoryData() {
      await this.querySkuList()
      this.setValue()
    }
  }
</script>
