import { OrderStatusEnum } from '@api/service/management/intelligence-learning/enum/OrderStatusEnum'
import TimeRange from '@api/service/management/intelligence-learning/model/TimeRange'
import { LearningStatusEnum } from '@api/service/management/intelligence-learning/enum/LearningStatusEnum'
import { TaskStatusEnum } from '@api/service/management/intelligence-learning/enum/TaskStatusEnum'
import { ImportOpenQueryParamRequest, ImportOpenQueryRequest } from '@api/ms-gateway/ms-importopen-v1'
import { MetaProperty } from '@api/ms-gateway/ms-collectivesign-v1'
import {
  StudentAutoLearningTaskResultQueryPageRequest,
  TaskResultByPageRequest
} from '@api/ms-gateway/ms-autolearning-student-auto-learning-task-result-v1'
import ImportOpenLearningParam from '@api/service/management/intelligence-learning/model/ImportOpenLearningParam'

export default class TrackingParam {
  /**
   * 培训方案id
   */
  trainClassId = ''

  /**
   * 方案名称
   */
  trainingSchemeName = ''

  /**
   * 姓名
   */
  name = ''

  /**
   * 是否立即处理
   */
  immediate = false

  /**
   * 证件号
   */
  idCard = ''

  /**
   * 手机号
   */
  phone = ''

  /**
   * 导入任务id
   */
  importTaskId = ''

  /**
   * 开通状态/订单状态
   */
  orderStatus: OrderStatusEnum = null

  /**
   * 智能选课/学习编排状态
   */
  learningState: LearningStatusEnum = null

  /**
   * 任务执行状态
   */
  taskStatus: TaskStatusEnum[] = null

  /**
   * 导入时间范围
   */
  importTimeRange = new TimeRange()

  /**
   * 编排结果入参转换
   */
  static to(vo: TrackingParam) {
    const importOpenQueryRequest = new ImportOpenQueryRequest()
    const metaProperty = new Array<MetaProperty>()
    if (vo.name) {
      metaProperty.push({ key: 'userInfo_name', value: vo.name })
    }
    if (vo.idCard) {
      metaProperty.push({ key: 'userInfo_idCard', value: vo.idCard })
    }
    if (vo.phone) {
      metaProperty.push({ key: 'userInfo_phone', value: vo.phone })
    }
    if (vo.trainingSchemeName) {
      metaProperty.push({ key: 'signUp_schemeName', value: vo.trainingSchemeName })
    }
    importOpenQueryRequest.metaPropertyList = metaProperty
    const query = new ImportOpenQueryParamRequest()
    query.mainTaskId = vo.importTaskId
    query.importEndTime = vo.importTimeRange.endTime
    query.importStartTime = vo.importTimeRange.startTime
    query.taskCategoryList = [ImportOpenLearningParam.category]
    if (vo.orderStatus == 3) {
      // 开通失败
      query.batchParallelQueryDtoList = [
        {
          parallelQueryDtoList: [
            {
              subTaskProcessResult: 2,
              existProperties: [
                { key: 'orderState', value: false },
                { key: 'learningState', value: false }
              ]
            },
            { orderState: 3 }
          ]
        }
      ]
    } else if (vo.orderStatus == 1) {
      // 开通中
      query.batchParallelQueryDtoList = [
        {
          parallelQueryDtoList: [{ orderState: 1 }]
        }
      ]
    } else if (vo.orderStatus == 2) {
      // 已开通
      query.batchParallelQueryDtoList = [
        {
          parallelQueryDtoList: [
            {
              subTaskProcessResult: 1,
              existProperties: [
                { key: 'orderState', value: false },
                { key: 'learningState', value: false }
              ]
            },
            { orderState: 2 }
          ]
        }
      ]
    }
    query.learningState = vo.learningState ?? undefined
    importOpenQueryRequest.queryParam = query
    return importOpenQueryRequest
  }

  /**
   * 执行情况入参转换
   */
  static toTaskStatus(vo: TrackingParam) {
    const dto = new TaskResultByPageRequest()
    dto.idCard = vo.idCard
    dto.name = vo.name
    dto.phone = vo.phone
    dto.mainTaskIdList = vo.importTaskId ? [vo.importTaskId] : undefined
    dto.learningSchemeName = vo.trainingSchemeName
    if (vo.taskStatus?.length) {
      if (vo.immediate) {
        dto.resultList = [...vo.taskStatus]
        dto.resultList.includes(TaskStatusEnum.fail) && dto.resultList.push(TaskStatusEnum.fail_arrange)
        dto.resultList = dto.resultList.filter((item) => item != 7)
      } else {
        dto.resultList = [...vo.taskStatus]
        dto.resultList.includes(TaskStatusEnum.fail) && dto.resultList.push(TaskStatusEnum.fail_arrange)
        dto.resultList.includes(TaskStatusEnum.stop) && dto.resultList.push(7)
      }
      if (vo.taskStatus.includes(TaskStatusEnum.un_start)) {
        dto.resultList.push(8)
      }
    }
    return dto
  }
}
