import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  CourseInfoRequest,
  CourseOfCourseTrainingOutlineRequest,
  StudentCourseLearningRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import { UiPage } from '@hbfe/common'
import StudentLearningCourseTypeEnum from '@api/service/customer/learning/scene/gates/enum/StudentLearningCourseTypeEnum'

class QueryStudentCourse {
  studentNo: string
  courseId: string
  schemeType: StudentLearningCourseTypeEnum

  constructor(studentNo: string, courseId: string, schemeType: StudentLearningCourseTypeEnum) {
    this.studentNo = studentNo
    this.schemeType = schemeType
    this.courseId = courseId
  }

  async queryStudentCourseId(): Promise<string> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    const request = new StudentCourseLearningRequest()
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    // request.courseOfCourseTrainingOutline.courseType = this.courseType
    request.course = new CourseInfoRequest()
    request.course.courseId = this.courseId
    request.studentNo = this.studentNo
    if (this.schemeType === StudentLearningCourseTypeEnum.AutonomousCourse) {
      const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseOfAutonomousCourseLearningSceneInMyself(
        { page, request }
      )
      return result.data.currentPageData[0]?.studentCourse?.studentCourseId
    }
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneInMyself(
      { page, request }
    )
    return result.data.currentPageData[0]?.studentCourse?.studentCourseId
  }
}

export default QueryStudentCourse
