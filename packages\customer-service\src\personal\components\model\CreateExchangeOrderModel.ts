import { MutationCreateExchangeOrder } from '@api/service/management/trade/single/order/mutation/MutationCreateExchangeOrder'

/**
 * 创建换货单模型
 */
export class CreateExchangeOrderModel extends MutationCreateExchangeOrder {
  /**
   * 培训方案名称
   */
  schemeName = ''

  /**
   * 价格
   */
  price = 0

  /**
   * 剔除培训班商品id集合
   */
  excludeCommoditySkuIdList: string[] = []
  /**
   * 剔除的期别id集合
   */
  excludedIssueIdList = new Array<string>()
  /**
   * 是否专题订单
   */
  isSpecialOrder?: boolean = false
  /**
   * 专题名称
   */
  specialOrderName?: string = ''
  /**
   * 专题id
   */
  saleChannelId = ''
  /**
   * 分销商id
   */
  distributorId = ''
  /**
   * 机构id
   */
  unitId = ''
  /**
   * 收款单位主体id
   */
  receiveUnitId: string = undefined
}

export default CreateExchangeOrderModel
