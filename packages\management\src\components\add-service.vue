<template>
  <el-card shadow="never" class="m-card">
    <div slot="header" class="f-flex f-align-center">
      <span class="tit-txt f-flex-sub">增值服务</span>
      <a class="f-link f-cb" @click="handleOpenDrawer">增值服务说明</a>
    </div>
    <div class="f-plr20 f-pt40">
      <el-row type="flex" justify="center" class="width-limit">
        <el-col :md="20" :lg="16" :xl="13">
          <el-form ref="addForm" :model="addData" label-width="auto" class="m-form">
            <el-form-item label="学习规则">
              <el-switch
                v-model="learningRule"
                active-text="开启"
                inactive-text="关闭"
                class="m-switch"
                @change="handleType('learningRule')"
                active-value="1"
                :inactive-value="null"
              />
            </el-form-item>
            <el-form-item label="智能学习">
              <el-switch
                v-model="intelligentlearning"
                active-text="开启"
                inactive-text="关闭"
                class="m-switch"
                @change="handleType('intelligentlearning')"
                active-value="2"
                :inactive-value="null"
              />
            </el-form-item>
            <el-form-item label="分销服务" class="fxService">
              <el-switch
                v-model="fxService"
                active-text="开启"
                inactive-text="关闭"
                class="m-switch"
                @change="handleType('fxService')"
                active-value="3"
                :inactive-value="null"
              />
              <div class="f-mt10" v-if="fxService">
                <el-radio v-model="distributionServiceType" :label="DistributionServiceTypeEnum.basic" class="f-mr30"
                  >基础版</el-radio
                ><el-radio v-model="distributionServiceType" :label="DistributionServiceTypeEnum.professional"
                  >专业版</el-radio
                >
              </div>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <div class="m-btn-bar f-tc is-sticky-1">
      <el-button @click="handleReJump">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </div>
    <el-dialog title="系统提醒" :visible.sync="tipVisible" width="400px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-f16 f-fb">网校信息修改成功！</p>
          <div class="f-f13 f-mt5">
            修改后的信息会同步更新至网校，请与网校确认是否完成对应的关联配置。修改后网校已生成的数据将不受影响。
          </div>
        </div>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="handleCancel">知道了</el-button>
      </div>
    </el-dialog>
    <el-dialog title="增值服务" :visible.sync="onlineSchoolDialog" width="460px" class="m-dialog">
      <div>请选择开通的分销服务类型：</div>
      <div class="f-mt15">
        <el-radio v-model="distributionServiceType" :label="DistributionServiceTypeEnum.basic" class="f-mr30"
          >基础版</el-radio
        ><el-radio v-model="distributionServiceType" :label="DistributionServiceTypeEnum.professional">专业版</el-radio>
      </div>
      <div slot="footer">
        <el-button @click="onlineSchoolDialog = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </el-dialog>
    <add-service-drawer ref="AddServiceDrawer"></add-service-drawer>
  </el-card>
</template>
<script lang="ts">
  import AddServiceModel from '@api/service/training-institution/online-school/base-models/AddServiceModel'
  import { Component, Ref, Vue, Prop, Watch } from 'vue-property-decorator'
  import OnlineSchoolModule from '@api/service/training-institution/online-school/OnlineSchoolModule'
  import AddServiceDrawer from '@hbfe/jxjy-admin-registerSchool/src/components/add-service-info.vue'
  import SchoolBaseModel from '@api/service/training-institution/online-school/base-models/SchoolBaseModel'
  import { AddServiceEnum } from '@api/service/training-institution/online-school/enum/AddServiceEnum'
  import { debounce, bind } from 'lodash-decorators'
  import { DistributionServiceTypeEnum } from '@api/service/common/capability-service-config/enum/DistributionServiceTypeEnum'
  @Component({
    components: { AddServiceDrawer }
  })
  export default class extends Vue {
    @Ref('addForm') addForm: any
    @Ref('AddServiceDrawer') AddServiceDrawer: AddServiceDrawer
    @Prop({
      required: true,
      default: () => {
        return new AddServiceModel()
      }
    })
    /**
     * 增值服务数据
     */
    addData: AddServiceModel
    /**
     * 基础信息数据（有校验）
     */
    @Prop({
      required: true,
      default: () => new SchoolBaseModel()
    })
    baseData: SchoolBaseModel
    // 合约id
    @Prop({
      required: true,
      default: () => ''
    })
    id: string
    @Watch('addData', { deep: true })
    handleAddData(val: AddServiceModel) {
      if (val.addServiceType.length && !this.isWatch) {
        // 只要第一次触发就行 后面不用触发
        this.learningRule = val.addServiceType.find((item) => item === AddServiceEnum.learningRule)
        this.intelligentlearning = val.addServiceType.find((item) => item === AddServiceEnum.intelligentlearning)
        this.fxService = val.addServiceType.find((item) => item === AddServiceEnum.fxService)

        this.isWatch = true
      }
      this.distributionServiceType = val?.distributionServiceType
      if (val?.distributionServiceType || val?.distributionServiceType === 0) {
        this.hasFxService = true
      } else {
        this.hasFxService = false
      }
    }
    /**
     * 默认展开的级联组件
     */
    activeNames: Array<string> = ['1']
    /**
     * 学习规则开关
     */
    learningRule: string = null
    /**
     * 智能学习开关
     */
    intelligentlearning: string = null
    /**
     * 分销服务开关
     */
    fxService: string = null
    /**
     * 已开启分销服务
     */
    hasFxService = false
    /**
     * 开通增值服务
     */
    onlineSchoolDialog = false
    /**
     * 分销服务类型
     */
    distributionServiceType: DistributionServiceTypeEnum = null
    DistributionServiceTypeEnum = DistributionServiceTypeEnum

    onlineSchoolObj: OnlineSchoolModule = new OnlineSchoolModule()
    isWatch = false
    tipVisible = false
    async created() {
      //
    }
    // 校验
    handleCheck() {
      return new Promise((resolve) => {
        let result = {
          status: true,
          msg: '',
          data: {}
        }
        this.addForm.validate((valid: any) => {
          if (valid) {
            result.data = this.addData
          } else {
            result = {
              status: false,
              msg: '增值服务未填写完整，请检查！',
              data: {}
            }
            return false
          }
        })
        resolve(result)
      })
    }
    /**
     * 切换增值服务开关
     */
    handleType(type: string) {
      console.log(this[type], type)

      // 保留预切换数据
      const newValue = this[type]
      this.addData.addServiceType = []
      if (this[type]) {
        // 二次确认前不切换开关
        this[type] = null
        this.addData.addServiceType.push(this[type])
      } else {
        // 二次确认前不切换开关
        // 后续如果添加增值服务类型 此处需要修改
        // this[type] = type == 'learningRule' ? AddServiceEnum.learningRule : AddServiceEnum.intelligentlearning
        if (type == 'learningRule') {
          this[type] = AddServiceEnum.learningRule
        } else if (type == 'intelligentlearning') {
          this[type] = AddServiceEnum.intelligentlearning
        } else {
          this[type] = AddServiceEnum.fxService
        }
      }
      if (type !== 'fxService') {
        this.$confirm('确认' + (this[type] ? '关闭' : '开启') + '该增值服务吗?', '增值服务', {
          confirmButtonText: '确认',
          cancelButtonText: '取消'
        })
          .then(() => {
            this[type] = newValue
          })
          .catch(() => {
            //
          })
      } else {
        if (this.fxService) {
          this.$confirm('确认关闭该增值服务吗?', '增值服务', {
            confirmButtonText: '确认',
            cancelButtonText: '取消'
          })
            .then(() => {
              this[type] = newValue
            })
            .catch(() => {
              //
            })
        } else {
          this.onlineSchoolDialog = true
        }
      }
    }
    handleConfirm() {
      if (this.distributionServiceType == null || isNaN(this.distributionServiceType)) {
        return this.$message.warning('请选择开通的分销服务类型')
      }
      this.fxService = '3'
      this.onlineSchoolDialog = false
    }
    /**
     * 取消
     */
    handleReJump() {
      this.$router.push('/school-management/management')
    }
    /**
     * 保存
     */
    @bind
    @debounce(200)
    async handleSubmit() {
      this.onlineSchoolObj.onlineSchool.addServiceConfig.addServiceType = []
      if (this.intelligentlearning) {
        this.onlineSchoolObj.onlineSchool.addServiceConfig.addServiceType.push(this.intelligentlearning)
      }
      if (this.learningRule) {
        this.onlineSchoolObj.onlineSchool.addServiceConfig.addServiceType.push(this.learningRule)
      }
      //分销服务
      if (this.fxService) {
        this.onlineSchoolObj.onlineSchool.addServiceConfig.addServiceType.push(this.fxService)
        this.onlineSchoolObj.onlineSchool.addServiceConfig.distributionServiceType = this.distributionServiceType
      }
      this.onlineSchoolObj.onlineSchool.id = this.id
      this.onlineSchoolObj.onlineSchool.schoolBase = this.baseData
      const result = await this.onlineSchoolObj.updateSchoolBase()
      if (result.status.code == 200) {
        this.tipVisible = true
      } else {
        this.$alert(result.status.errors[0].message, '提示', {
          type: 'warning'
        })
      }
    }
    /**
     * 保存成功提示
     */
    handleCancel() {
      this.tipVisible = false
      // this.$router.push('/school-management/management')
    }
    /**
     * 增值服务说明抽屉
     */
    handleOpenDrawer() {
      this.AddServiceDrawer.dialog = true
    }
  }
</script>

<style scoped lang="scss">
  .fxService ::v-deep .el-form-item__content {
    margin-left: 68px !important;
  }
</style>
