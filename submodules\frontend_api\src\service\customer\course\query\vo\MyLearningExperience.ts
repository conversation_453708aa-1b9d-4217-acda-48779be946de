import {
  LearningExperienceTypeEnum,
  LearningExperienceAnswerTypeEnum,
  LearningExperienceActStatusEnum,
  LearningExperienceJoinStatusEnum,
  LearningExperienceApproveStatusEnum,
  LearningExperienceApproveResultEnum,
  ApproveMethodEnum
} from '../enum/LearningExperienceStatusEnum'
import ActivityManangeModel from '@api/service/customer/course/query/vo/ActivityManangeModel'
import {
  StudentLearningExperienceResponse,
  StudentLearningExperienceStatus
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
class MyLearningExperience {
  // 学习心得id
  id = ''
  // 主题名称
  name = ''
  // 主题id
  topicId = ''
  // 类型
  type: LearningExperienceTypeEnum = null
  // 作答形式
  answerType: LearningExperienceAnswerTypeEnum = null
  // 是否必选
  isMustSelect = false
  // 学习心得起始时间
  startTime = ''
  // 学习心得结束时间
  endTime = ''
  // 活动状态
  actStatus: LearningExperienceActStatusEnum = null
  // 参加状态
  joinStatus: LearningExperienceJoinStatusEnum = null
  // 审核状态
  approveStatus: LearningExperienceApproveStatusEnum = null
  // 审核结果
  approveResult: LearningExperienceApproveResultEnum = null
  // 审核结果分数
  approveResultScore = 0
  // 提交时间
  submitTime = ''
  // 审核方式
  approveMethod: ApproveMethodEnum = null
  // 大纲id
  outLineId = ''
  // 剩余提交次数 -1无限次
  submitNum: number = null
  // 学习心得是否已被删除
  isDel = false
  // 详情
  detail: ActivityManangeModel = new ActivityManangeModel()
  static from(response: StudentLearningExperienceResponse): MyLearningExperience {
    const vo = new MyLearningExperience()
    vo.id = vo.detail.ActivityManange.studentLearningExperienceId = response.studentLearningExperienceId
    vo.name = response.learningExperienceTopic?.experienceTopicName
    if (response.experienceType === 'COURSE') {
      vo.type = LearningExperienceTypeEnum.course
      vo.detail.ActivityManange.learningExperienceType = 2
    } else if (response.experienceType === 'SCHEME') {
      vo.type = LearningExperienceTypeEnum.class
      vo.detail.ActivityManange.learningExperienceType = 1
    }
    if (response.learningExperienceTopic?.participateType === 'SUBMIT_FILE') {
      vo.answerType = LearningExperienceAnswerTypeEnum.upload
    } else if (response.learningExperienceTopic?.participateType === 'EDIT_ONLINE') {
      vo.answerType = LearningExperienceAnswerTypeEnum.edit
    }
    vo.isMustSelect = response.learningExperienceTopic?.isRequired

    vo.startTime = response.learningExperienceTopic?.startTime
    vo.endTime = response.learningExperienceTopic?.endTime
    if (new Date(vo.startTime.replace(/-/g, '/')) > new Date()) {
      vo.actStatus = LearningExperienceActStatusEnum.notStart
    } else if (
      new Date(vo.startTime.replace(/-/g, '/')) < new Date() &&
      new Date() < new Date(vo.endTime.replace(/-/g, '/'))
    ) {
      vo.actStatus = LearningExperienceActStatusEnum.inProgress
    } else if (new Date(vo.endTime.replace(/-/g, '/')) < new Date()) {
      vo.actStatus = LearningExperienceActStatusEnum.Ended
    }
    if (['SUBMITTED', 'PASS', 'RETURNED'].includes(response.status)) {
      vo.joinStatus = LearningExperienceJoinStatusEnum.join
    } else {
      vo.joinStatus = LearningExperienceJoinStatusEnum.not_join
    }
    if (response.status === 'SUBMITTED') {
      vo.approveStatus = LearningExperienceApproveStatusEnum.not_approved
    } else if (['PASS', 'RETURNED'].includes(response.status)) {
      vo.approveStatus = LearningExperienceApproveStatusEnum.approved
    }
    if (response.status === 'PASS') {
      vo.approveResult = LearningExperienceApproveResultEnum.success
    } else if (response.status === 'RETURNED') {
      vo.approveResult = LearningExperienceApproveResultEnum.fail
    }
    vo.approveResultScore = response.score
    vo.submitNum = 1
    if (response.learningExperienceTopic?.auditType === 'AUTO_AUDIT') {
      vo.approveMethod = ApproveMethodEnum.AUTO
    } else if (response.learningExperienceTopic?.auditType === 'MANUAL_AUDIT') {
      vo.approveMethod = ApproveMethodEnum.ARTIFICIAL
    }
    vo.detail.ActivityManange.themeId = vo.topicId = response.learningExperienceTopic?.topicId
    vo.detail.ActivityManange.schemeId = response.studentLearning?.schemeId || response.experienceTypeInfo[0]
    vo.detail.ActivityManange.courseDetail.id = response.experienceTypeInfo[0]
    vo.submitTime = (
      response.statusChangeTime.find(item => {
        return item.status === StudentLearningExperienceStatus.SUBMITTED
      }) || {}
    ).changeTime
    vo.isDel = response.learningExperienceTopic.isDelete
    return vo
  }
}

export default MyLearningExperience
