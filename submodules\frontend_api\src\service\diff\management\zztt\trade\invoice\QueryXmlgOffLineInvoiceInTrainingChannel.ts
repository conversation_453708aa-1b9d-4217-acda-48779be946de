import UserModule from '@api/service/management/user/UserModule'
import { Page } from '@hbfe/common'
import QueryOffLinePageInvoiceParam from '@api/service/management/trade/single/invoice/query/vo/QueryOffLinePageInvoiceParam'
import TradeQueryBackstage, {
  OfflineInvoiceSortField,
  OfflineInvoiceSortRequest,
  SortPolicy1
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import OffLinePageInvoiceVo from '@api/service/diff/management/zztt/trade/invoice/model/OffLinePageInvoiceResponseVo'
import QueryOffLineInvoiceInTrainingChannel from '@api/service/management/trade/single/invoice/query/QueryOffLineInvoiceInTrainingChannel'
import InvoiceListResponse from '@api/service/diff/management/fjzj/trade/invoice/model/InvoiceListResponse'

/**
 * 线下发票 - 专题管理员
 */
export default class QueryXmlgOffLineInvoiceInTrainingChannel extends QueryOffLineInvoiceInTrainingChannel {
  /**
   * 分页专票查询发票（电子）
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  async offLineXmlgPageElectVatspecialplaInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest> = [
      { field: OfflineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy1.DESC }
    ]
  ): Promise<Array<OffLinePageInvoiceVo>> {
    const data = await this.offLinePageElectVatspecialplaInvoiceInServicer(page, queryOffLinePageInvoiceParam, sort)
    return data.map((item) => {
      const offLinePageInvoiceVo = new OffLinePageInvoiceVo()
      Object.assign(offLinePageInvoiceVo, item)
      return offLinePageInvoiceVo
    })
  }

  /**
   * 分页电子查询发票
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  async offLineXmlgPageInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest> = [
      { field: OfflineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy1.DESC }
    ]
  ): Promise<Array<OffLinePageInvoiceVo>> {
    const data = await this.offLinePageInvoiceInServicer(page, queryOffLinePageInvoiceParam, sort)
    return data.map((item) => {
      const offLinePageInvoiceVo = new OffLinePageInvoiceVo()
      Object.assign(offLinePageInvoiceVo, item)
      return offLinePageInvoiceVo
    })
  }

  /**
   * 分页专票查询发票（纸质票）
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  async offLineXmlgPageVatspecialplaInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest> = [
      { field: OfflineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy1.DESC }
    ]
  ): Promise<Array<OffLinePageInvoiceVo>> {
    const data = await this.offLinePageVatspecialplaInvoiceInServicer(page, queryOffLinePageInvoiceParam, sort)
    return data.map((item) => {
      const offLinePageInvoiceVo = new OffLinePageInvoiceVo()
      Object.assign(offLinePageInvoiceVo, item)
      return offLinePageInvoiceVo
    })
  }
}
