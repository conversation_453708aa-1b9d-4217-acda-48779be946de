/*
 * @Description: 资讯列表中的对象模型   --- H5
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-25 10:33:10
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-01-25 10:36:27
 */
class H5TypeNews {
  // 资讯ID
  id: string
  // 资讯标题
  title: string
  // 资讯摘要
  abstract: string
  // 发布时间
  date: Date
  // 是否置顶
  isTop: boolean
  // 阅读人数
  number: number
  // DTO转VO
  static from() {
    // NOTE:DTO转VO
    return new H5TypeNews()
  }
}
export default H5TypeNews
