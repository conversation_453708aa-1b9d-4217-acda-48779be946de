<template>
  <el-drawer title="选择下载发票格式" :visible.sync="isShow" width="400px" class="m-dialog">
    <div class="m-btn-bar f-mlr50">
      <el-button type="primary" @click="downloadModule(paperPdfUrl)" v-if="paperPdfUrl">下载PDF格式</el-button>
      <el-button type="primary" @click="downloadModule(ofdUrl)" v-if="ofdUrl">下载OFD格式</el-button>
      <el-button type="primary" @click="downloadModule(xmlUrl)" v-if="xmlUrl">下载XML格式</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
  import Downloader, { HeaderObj } from '@api/service/common/utils/Downloader'
  import FileModule from '@api/service/common/file/FileModule'

  @Component
  export default class extends Vue {
    @Prop({ type: String, default: '' }) paperPdfUrl: string
    @Prop({ type: String, default: '' }) ofdUrl: string
    @Prop({ type: String, default: '' }) xmlUrl: string

    isShow = false

    isShowDialog() {
      this.isShow = !this.isShow
    }
    /**
     * 下载
     */
    download(url: string) {
      //  console.log(url, 'url')
      if (!url) {
        return this.$message.warning('暂无发票')
      }
      window.open('/mfs' + url, '_blank')
    }
    // 报名模板地址
    signUpTemplate = ''
    // 下载报名列表
    async downloadModule(url: string) {
      // console.log(url, 'urrrrrrrrrrr')
      if (url.indexOf('/protected') > -1) {
        const authorization = localStorage.getItem('admin.Access-Token')
        const header: HeaderObj = {
          ['Authorization']: authorization
        }
        await FileModule.applyResourceAccessToken()
        const type = url.split('.').pop().toLowerCase()

        const download = new Downloader(
          `/mfs${url}?token=${FileModule.resourceAccessToken}`,
          `发票下载.${type}`,
          header
        )
        download.download()
      } else {
        window.open('/mfs' + url, '_blank')
      }
    }
  }
</script>

<style scoped></style>
