import store from '@/store'
import KeyValue from '@api/service/common/models/KeyValue'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import { frontendApplication, ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import MsConfigV1 from '@api/ms-gateway/ms-config-v1'

@Module({
  name: 'CommonConfigCenter',
  store,
  namespaced: true,
  dynamic: true
})
class ConfigCenterModule extends VuexModule {
  private ingress: Map<string, string> = new Map<string, string>()
  private frontend: Map<string, string> = new Map<string, string>()
  private frontend_application: Map<string, string> = new Map<string, string>()
  private frontend_application_diff: Map<string, string> = new Map<string, string>()
  private frontend_common: Map<string, string> = new Map<string, string>()

  @Action
  /**
   * 查询项目配置
   */
  async queryApplicationConfig() {
    const result = await MsConfigV1.getCurrentFrontendConfig()
    const data = result.data as any
    this.SET_APPLICATION_CONFIG(data)
    return data
  }

  @Mutation
  SET_APPLICATION_CONFIG(applicationConfig: { ingress: Array<KeyValue>; frontend: Array<KeyValue> }) {
    Object.keys(applicationConfig).forEach((key: string) => {
      const config = applicationConfig[key]
      config.forEach((itemConfig: KeyValue) => {
        // 如果没有定义，创建一个 map 出来
        if (!this[key]) {
          this[key] = new Map<string, string>()
        }
        // 特殊处理上传组件的阿波罗
        if (itemConfig.key === ingress.apiendpoint || itemConfig.key === frontendApplication.apiendpoint) {
          sessionStorage.setItem(itemConfig.key, itemConfig.value)
        }
        this[key].set(itemConfig.key, itemConfig.value)
      })
    })
  }

  /**
   * 获取 ingress 里面的配置
   */
  get getIngress() {
    return (name: string) => {
      if (name === ingress.apiendpoint && !this.ingress.get(name) && sessionStorage.getItem(ingress.apiendpoint)) {
        return sessionStorage.getItem(ingress.apiendpoint)
      }
      return this.ingress.get(name)
    }
  }

  /**
   * 获取 frontend 里面的配置
   */
  get getFrontendConfig() {
    return (name: string) => {
      return this.frontend.get(name)
    }
  }

  /**
   * 获取 frontend_application 里面的配置
   */
  get getFrontendApplication() {
    return (name: string) => {
      if (
        name === frontendApplication.apiendpoint &&
        !this.frontend_application.get(name) &&
        sessionStorage.getItem(frontendApplication.apiendpoint)
      ) {
        return sessionStorage.getItem(frontendApplication.apiendpoint)
      }
      return this.frontend_application.get(name)
    }
  }

  /**
   * 获取 frontend_application 里面的配置
   */
  get getFrontendApplicationDiff() {
    return (name: string) => {
      return this.frontend_application_diff.get(name)
    }
  }

  /**
   * 获取 frontend_common 里面的配置
   */
  get getFrontendCommon() {
    return (name: string) => {
      return this.frontend_common.get(name)
    }
  }

  get getIngressByKeyMap() {
    return (waitFillNameMap: { [key: string]: string }) => {
      return Object.keys(waitFillNameMap).forEach((key: string) => {
        waitFillNameMap[key] = this.ingress.get(`ingress.${key}`)
      })
    }
  }
}

export default getModule(ConfigCenterModule)
