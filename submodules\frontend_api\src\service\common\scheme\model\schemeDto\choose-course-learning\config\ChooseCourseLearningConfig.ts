import ChooseCourseRule from '@api/service/common/scheme/model/schemeDto/choose-course-learning/config/choose-course-rule/ChooseCourseRule'
import ChooseCourseLearningCourseTrainingOutline from '@api/service/common/scheme/model/schemeDto/choose-course-learning/config/course-training-outlines/ChooseCourseLearningCourseTrainingOutline'
import ChooseCourseLearningCourseQuizConfig from '@api/service/common/scheme/model/schemeDto/choose-course-learning/config/course-quiz-config/ChooseCourseLearningCourseQuizConfig'
import ChooseCourseLearningCourseCompleteEvaluateConfig from '@api/service/common/scheme/model/schemeDto/choose-course-learning/config/course-complete-evaluate-config/ChooseCourseLearningCourseCompleteEvaluateConfig'
import ChooseCourseLearningCourseAppraisalConfig from '@api/service/common/scheme/model/schemeDto/choose-course-learning/config/course-appraisal-config/ChooseCourseLearningCourseAppraisalConfig'

/**
 * @description 选课规则学习方式-配置信息
 */
class ChooseCourseLearningConfig {
  /**
   * 学习方式id
   */
  id: string
  /**
   * 选课规则配置
   */
  chooseCourseRule: ChooseCourseRule
  /**
   * 选课学习场景课程学习大纲列表
   */
  courseTrainingOutlines: ChooseCourseLearningCourseTrainingOutline[]
  /**
   * 课后测验配置
   */
  courseQuizConfig: ChooseCourseLearningCourseQuizConfig
  /**
   * 课程完成评定配置
   */
  courseCompleteEvaluateConfig: ChooseCourseLearningCourseCompleteEvaluateConfig
  /**
   * 课程评价配置
   */
  courseAppraisalConfig: ChooseCourseLearningCourseAppraisalConfig
  /**
   * 操作类型
   */
  operation: number
}

export default ChooseCourseLearningConfig
