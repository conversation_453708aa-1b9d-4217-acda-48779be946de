<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <!--选课规则-->
    <div class="f-plr20 f-pt20">
      <show-choose-course-outline
        v-if="schemeType === 1"
        :outline-info="courseLearningInfo.classification"
        :type="1"
        :trainSchemeDetail.sync="schemeDetail"
      />
      <!--自主选课-->
      <show-auto-choose-outline
        v-if="schemeType === 2"
        :classification.sync="courseLearningInfo.classification"
        :trainSchemeDetail.sync="schemeDetail"
        @updateNode="handleUpdateNode"
      />
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="m-tit is-small bg-gray is-border-bottom">
          <span class="tit-txt">课程测验</span>
          <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
            <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
            <div slot="content">
              课程测验的组卷范围为已选择课程关联的试题。请先添加课程后，再添加测验。课程测验可选配。
            </div>
          </el-tooltip>
        </div>
        <div class="f-p20">
          <el-table stripe :data="courseQuizList" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="总分/及格分" min-width="140" align="center" fixed="left">
              <template slot-scope="scope">100 / {{ scope.row.quizPassScore }}</template>
            </el-table-column>
            <el-table-column label="组卷方式" min-width="120" align="center">
              <template>智能组卷</template>
            </el-table-column>
            <el-table-column :label="radioValue === 0 ? '测验题数' : '每学时题数'" min-width="120" align="center">
              <template slot-scope="scope">{{ scope.row.questionNum }}</template>
            </el-table-column>
            <el-table-column label="作答次数" min-width="120" align="center">
              <template>不限次</template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="m-tit is-small bg-gray is-border-bottom">
          <span class="tit-txt">学习要求</span>
        </div>
        <div class="f-p20">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" label-width="150px" class="m-text-form is-column f-mt10">
                <!--选课规则-->
                <el-form-item label="选修课选修要求：" v-if="schemeType === 1">
                  {{ courseLearningInfo.electiveRequirePeriod }} 学时
                  <div class="f-mt10 bg-gray f-plr20 f-ptb10" v-if="isTowElectiveRequire">
                    <el-checkbox v-model="TowElectiveListShow" disabled>二级分类存在选课要求</el-checkbox>
                    <div class="f-pb10" v-if="TowElectiveListShow">
                      <div
                        class="f-mt10"
                        v-for="item in courseLearningInfo.chooseCourseRule.secondElectiveMaxPeriod"
                        :key="item.id"
                      >
                        <i class="f-cb">{{ outTowElectiveName(item.id) }}</i
                        ><i class="f-mr10">选课要求</i> {{ item.electiveMaxPeriod }} 学时
                      </div>
                    </div>
                  </div>
                </el-form-item>
                <!--自主选课-->
                <el-form-item label="课程学习整体要求：" v-if="schemeType === 2">
                  需 ≥ {{ courseLearningInfo.requirePeriod }} 学时，
                  <template v-if="hasClassification">必学课程{{ compulsoryCoursePeriodTotal }}学时。</template>
                  <template v-if="!hasClassification"
                    >必学课程{{ courseLearningInfo.classification.compulsoryCoursePeriodTotal }}学时。</template
                  >
                  <p v-if="isTopLevelWeighty">
                    其中
                    <span v-for="(item, index) in topLevelPeriodInfoList" :key="index">
                      {{ item.name }}<i class="f-cr">{{ item.requirePeriod }}</i
                      >学时（必学课程<i class="f-cr">{{ item.compulsoryCoursePeriodTotal }}</i> 学时）<span
                        v-if="index < topLevelPeriodInfoList.length - 1"
                        >、</span
                      >
                    </span>
                  </p>
                </el-form-item>
                <el-form-item label="课程测验纳入考核：">{{
                  courseLearningInfo.courseQuizPagerStandard ? '是' : '否'
                }}</el-form-item>
                <el-form-item label="开放课程评价：">{{
                  courseLearningInfo.enableAppraisal ? '是' : '否'
                }}</el-form-item>
                <el-form-item label="评价条件：" v-if="courseLearningInfo.enableAppraisal"
                  >每门课程学习进度达 {{ courseLearningInfo.preconditionCourseSchedule }}%
                  可以进行课程评价</el-form-item
                >
                <el-form-item label="是否强制学员评价：" v-if="courseLearningInfo.enableAppraisal">
                  {{ courseLearningInfo.enableCompulsoryAppraisal ? '是' : '否' }}
                </el-form-item>
                <el-form-item label="考核要求：">
                  <!--选课规则-->
                  <p v-if="schemeType === 1">
                    1. 必修 <i class="f-cr">{{ courseLearningInfo.compulsoryRequirePeriod }}</i> 学时， 选修
                    <i class="f-cr">{{ courseLearningInfo.electiveRequirePeriod }}</i> 学时，学习进度 100%
                  </p>
                  <!--自主选课-->
                  <p v-if="schemeType === 2">
                    1. 课程学习整体要求≥ <i class="f-cr">{{ courseLearningInfo.requirePeriod }}</i> 学时，学习进度 100%
                  </p>
                  <p>
                    2. <span v-if="courseLearningInfo.courseQuizPagerStandard">课程测验纳入考核</span>
                    <span v-if="!courseLearningInfo.courseQuizPagerStandard">课程测验不纳入考核</span
                    >，每门课程学习进度达
                    <i class="f-cr">{{ courseLearningInfo.quizConfigModel.minCourseSchedule }}%</i>
                    可参加，测验及格分
                    <i class="f-cr">{{ courseLearningInfo.quizConfigModel.passScore }}</i>
                    分，次数不限次。
                  </p>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, Prop, PropSync, Watch } from 'vue-property-decorator'
  import CourseLearningLearningType from '@api/service/management/train-class/mutation/vo/CourseLearningLearningType'
  import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
  import ShowChooseCourseOutline from '@hbfe/jxjy-admin-scheme/src/components/functional-components/show-choose-course-outline.vue'
  import ShowAutoChooseOutline from '@hbfe/jxjy-admin-scheme/src/components/functional-components/show-auto-choose-outline.vue'
  import { CreateSchemeUtils, TopLevelOutlinePeriod } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'

  class CourseQuizDetail {
    // 测验题数
    questionNum: number
    // 测验及格分
    quizPassScore: number
  }

  @Component({
    components: { ShowAutoChooseOutline, ShowChooseCourseOutline }
  })
  export default class extends Vue {
    /**
     * 课程学习信息配置
     */
    @PropSync('courseLearning', { type: CourseLearningLearningType }) courseLearningInfo!: CourseLearningLearningType

    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: TrainClassDetailClassVo }) schemeDetail: TrainClassDetailClassVo
    /**
     * 监听数据判断显示二级分类
     */
    @Prop({
      type: Boolean,
      default: false
    })
    isTowElectiveListShow: boolean
    // 课程测验列表
    courseQuizList: Array<CourseQuizDetail> = new Array<CourseQuizDetail>()
    /**
     * 是否勾选二级选课要求
     */
    TowElectiveListShow = false
    /**
     *   测验试题数量配置 默认选中
     */
    radioValue = 0
    /**
     * 培训方案类型，1-选课规则，2-自主选课
     */
    get schemeType() {
      return this.schemeDetail.trainClassBaseInfo.schemeType
    }
    /**
     * 是否有二级分类
     */
    get isTowElectiveRequire() {
      const towElectiveList = this.courseLearningInfo.classification.childOutlines
      let isShow = false
      towElectiveList.map((item) => {
        if (item.childOutlines) {
          if (item.childOutlines.length > 0 && item.category == 2) {
            isShow = true
          }
        }
      })
      return isShow
    }
    /**
     * 一级分类学时信息列表
     */
    topLevelPeriodInfoList: TopLevelOutlinePeriod[] = [] as TopLevelOutlinePeriod[]

    @Watch('isTowElectiveListShow', {
      immediate: true,
      deep: true
    })
    isTowElectiveListShowChange(newVal: any, oldVal: any) {
      if (newVal) {
        this.TowElectiveListShow = true
      }
    }
    /**
     * 【自主选课】一级分类学时信息
     */
    get topLevelPeriodInfo() {
      return (item: Classification) => {
        const info = new TopLevelOutlinePeriod()
        info.requirePeriod = item.assessSetting.requirePeriod || 0
        info.name = item.name || ''
        info.compulsoryCoursePeriodTotal = CreateSchemeUtils.getOutlineCompulsoryCoursePeriod(item)
        return info
      }
    }

    /**
     * 【自主选课】是否展示一级分类相关信息（一级分类数量至少为2）
     */
    get isTopLevelWeighty() {
      return CreateSchemeUtils.isWeightyArray(this.topLevelPeriodInfoList) && this.topLevelPeriodInfoList.length > 1
        ? true
        : false
    }

    /**
     * 【自主选课】必学课程学时
     */
    get compulsoryCoursePeriodTotal() {
      const outlineInfo = this.courseLearningInfo.classification
      if (CreateSchemeUtils.isWeightyArray(outlineInfo.childOutlines)) {
        // 有分类
        const result =
          this.topLevelPeriodInfoList?.reduce((prev, cur) => {
            return CalculatorObj.add(cur.compulsoryCoursePeriodTotal, prev)
          }, 0) || 0
        return result
      } else {
        // 无分类
        return outlineInfo.compulsoryCoursePeriodTotal
      }
    }

    /**
     * 【自主选课】是否有分类
     */
    get hasClassification() {
      const classification = this.courseLearningInfo.classification.childOutlines
      return CreateSchemeUtils.isWeightyArray(classification)
    }

    /**
     * 【自主选课】要求学时合计
     */
    get requirePeriodTotal() {
      const outlineInfo = this.courseLearningInfo.classification
      if (CreateSchemeUtils.isWeightyArray(outlineInfo.childOutlines)) {
        // 有分类
        const result =
          this.topLevelPeriodInfoList?.reduce((prev, cur) => {
            return CalculatorObj.add(cur.requirePeriod, prev)
          }, 0) || 0
        return result
      } else {
        // 无分类
        return outlineInfo.assessSetting.requirePeriod || 0
      }
    }

    created() {
      if (this.courseLearningInfo.configCourseQuiz) {
        const option = new CourseQuizDetail()
        option.questionNum = this.courseLearningInfo.quizConfigModel.questionCount
        option.quizPassScore = this.courseLearningInfo.quizConfigModel.passScore
        this.radioValue = this.courseLearningInfo.quizConfigModel.questionCountConfigureType || 0
        this.courseQuizList.push(option)
      }
    }

    /**
     * 响应更新节点信息
     */
    handleUpdateNode() {
      this.topLevelPeriodInfoList = [] as TopLevelOutlinePeriod[]
      this.courseLearningInfo.classification?.childOutlines?.forEach((el: Classification) => {
        const option: TopLevelOutlinePeriod = this.topLevelPeriodInfo(el)
        this.topLevelPeriodInfoList.push(option)
      })
    }
    /**
     * 查询符合条件的二级选修课名称
     */
    outTowElectiveName(id: string) {
      let name = ''
      this.courseLearningInfo.classification.childOutlines.map((item) => {
        if (item.category == 2) {
          item.childOutlines.map((subItem) => {
            if (subItem.id == id) {
              name = subItem.name
            }
          })
        }
      })
      return name
    }
  }
</script>
