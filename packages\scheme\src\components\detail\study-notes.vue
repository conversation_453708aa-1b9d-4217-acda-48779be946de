<template>
  <div>
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div class="f-plr20">
        <!--无分类-->
        <div class="f-mtb20" v-if="learningFeelInfo.experienceName">
          学习心得对外展示名称：{{ learningFeelInfo.experienceName }}
        </div>
        <div class="f-mtb20">
          一共 <i class="f-cr">{{ learningFeelInfo.experienceList.length }}</i> 个，必选
          <i class="f-cr">{{ learningFeelInfo.requireCount }}</i> 个
        </div>
        <el-card shadow="never" class="m-card is-header f-mb15">
          <div class="f-p20">
            <el-table stripe :data="experienceList" max-height="500px" class="m-table f-mt15">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="主题" min-width="240" fixed="left" prop="theme">
                <template v-slot="{ row }">
                  <el-tag type="primary" effect="dark" size="mini" v-if="row.isRequired">必选</el-tag>
                  {{ row.theme }}
                </template>
              </el-table-column>
              <el-table-column label="参加时间" min-width="240" align="center" prop="joinTime">
                <template v-slot="{ row }">
                  {{ timeChange(row.joinTime) }}
                </template>
              </el-table-column>
              <el-table-column label="学习心得类型" min-width="100" align="center" prop="experienceType">
                <template v-slot="{ row }">
                  {{ row.experienceType }}
                </template>
              </el-table-column>
              <el-table-column label="作答形式" min-width="100" align="center" prop="answerType">
                <template v-slot="{ row }"> {{ row.answerType }}</template>
              </el-table-column>
              <el-table-column label="审核方式" min-width="100" align="center" prop="checkType">
                <template v-slot="{ row }">
                  {{ row.checkType }}
                </template>
              </el-table-column>
              <el-table-column label="总分" min-width="60" align="center" prop="score"></el-table-column>
              <el-table-column label="提交次数" width="160" align="center" prop="submitCount">
                <template v-slot="{ row }">
                  {{ submitNumber(row) }}
                </template>
              </el-table-column>
            </el-table>
            <hb-pagination :page="experiencePage" v-bind="experiencePage"></hb-pagination>
          </div>
        </el-card>

        <el-card shadow="never" class="m-card is-header f-mb15">
          <div class="m-tit is-small bg-gray is-border-bottom">
            <span class="tit-txt">学习心得要求</span>
          </div>
          <div class="f-p20">
            <el-row type="flex" justify="center" class="width-limit">
              <el-col :md="20" :lg="16" :xl="13">
                <el-form ref="form" label-width="150px" class="m-form f-mt10">
                  <el-form-item label="班级心得前置条件：" required v-if="learningFeelInfo.classExperienceList.length">
                    {{ learningFeelInfo.classCondition ? '可直接参加' : '完成班级所有课程学习方可参加' }}
                  </el-form-item>
                  <el-form-item label="课程心得前置条件：" required v-if="learningFeelInfo.courseExperienceList.length">
                    {{ learningFeelInfo.courseCondition ? '可直接参加' : '完成班级指定课程学习方可参加' }}
                  </el-form-item>
                  <el-form-item label="是否纳入考核：" required>
                    {{ learningFeelInfo.isExamine ? '是' : '否' }}
                  </el-form-item>
                  <el-form-item label="整体要求：" required v-if="learningFeelInfo.isExamine">
                    至少需要参加
                    {{ learningFeelInfo.joinCount }}
                    个学习心得
                  </el-form-item>
                  <el-form-item label="成绩要求：" required>
                    成绩 ≥
                    {{ learningFeelInfo.score }}
                    分（总分：100分）视为通过。
                  </el-form-item>
                  <el-form-item label="考核要求：">
                    <p>1. 各项学习心得要求以具体配置为准</p>
                    <p v-if="learningFeelInfo.isExamine">
                      2. 学习心得纳入考核，至少参加
                      <i class="f-cr">{{ learningFeelInfo.joinCount }}</i>
                      个心得，且每项心得均为通过。
                    </p>
                    <p v-else>2. 学习心得不纳入考核</p>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import LearningExperience from '@api/service/management/train-class/mutation/vo/LearningExperience'
  import { UiPage } from '@hbfe/common'
  import ExperienceItem from '@api/service/management/train-class/mutation/vo/ExperienceItem'

  @Component
  export default class extends Vue {
    @Prop({
      type: LearningExperience,
      default: () => {
        return new LearningExperience()
      }
    })
    learningFeelInfo!: LearningExperience

    /**
     * 学习心得列表
     */
    experienceList = this.learningFeelInfo.experienceList
    experiencePage: UiPage

    constructor() {
      super()
      this.experiencePage = new UiPage(this.pageData, this.pageData)
    }

    /**
     * 心得列表分页
     */
    async pageData() {
      this.experienceList = this.learningFeelInfo.pageData(this.experiencePage)
      console.log('我是心得列表1213', this.experienceList)
    }

    // 设置时间
    get timeChange() {
      return (item: string[]) => {
        if (!item.length) {
          return '-'
        } else if (item[0] == '1900-01-01 00:00:00' && item[1] == '2100-01-01 00:00:00') {
          return '长期有效'
        }
        return item[0] + '至' + item[1]
      }
    }

    /**
     * 判断提交次数
     */
    submitNumber(item: ExperienceItem) {
      if (item.checkType.current == 1) {
        return '--'
      } else {
        if (item.submitCountType) {
          return item.submitCount
        } else {
          return '不限次'
        }
      }
    }

    created() {
      this.experiencePage.currentChange(1)
    }
  }
</script>
