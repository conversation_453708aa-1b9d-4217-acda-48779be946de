import getAdminInfoInSubProject from './queries/getAdminInfoInSubProject.graphql'
import getBankInSubProject from './queries/getBankInSubProject.graphql'
import getBusinessDataDictionaryByIdInSubProject from './queries/getBusinessDataDictionaryByIdInSubProject.graphql'
import getBusinessDataDictionaryInSubProject from './queries/getBusinessDataDictionaryInSubProject.graphql'
import getCertificateCategoryInSubProject from './queries/getCertificateCategoryInSubProject.graphql'
import getCertificateInfoInSubject from './queries/getCertificateInfoInSubject.graphql'
import getCertificateLevelInSubProject from './queries/getCertificateLevelInSubProject.graphql'
import getCertificateMajorInSubProject from './queries/getCertificateMajorInSubProject.graphql'
import getCertificateTypeInSubProject from './queries/getCertificateTypeInSubProject.graphql'
import getEnterpriseUnitAdminInfoInMyself from './queries/getEnterpriseUnitAdminInfoInMyself.graphql'
import getEnterpriseUnitAdminInfoInSubProject from './queries/getEnterpriseUnitAdminInfoInSubProject.graphql'
import getEnterpriseUnitCountInGovernmentUnit from './queries/getEnterpriseUnitCountInGovernmentUnit.graphql'
import getEnterpriseUnitCountInSubProject from './queries/getEnterpriseUnitCountInSubProject.graphql'
import getEnterpriseUnitInfoInMyself from './queries/getEnterpriseUnitInfoInMyself.graphql'
import getEnterpriseUnitInfoInSubProject from './queries/getEnterpriseUnitInfoInSubProject.graphql'
import getGovernmentUnitAdminInfoInMyself from './queries/getGovernmentUnitAdminInfoInMyself.graphql'
import getGovernmentUnitAdminInfoInSubProject from './queries/getGovernmentUnitAdminInfoInSubProject.graphql'
import getGovernmentUnitInfoInMyself from './queries/getGovernmentUnitInfoInMyself.graphql'
import getGovernmentUnitInfoInSubProject from './queries/getGovernmentUnitInfoInSubProject.graphql'
import getIdentificationCertificateInfoInSubject from './queries/getIdentificationCertificateInfoInSubject.graphql'
import getJobCategoryInSubProject from './queries/getJobCategoryInSubProject.graphql'
import getRegionInSubProject from './queries/getRegionInSubProject.graphql'
import getStudentInfoInSubProject from './queries/getStudentInfoInSubProject.graphql'
import getUserInfoInMyself from './queries/getUserInfoInMyself.graphql'
import listBusinessDataDictionaryInSubProject from './queries/listBusinessDataDictionaryInSubProject.graphql'
import listChildRegionInSubProject from './queries/listChildRegionInSubProject.graphql'
import listRegionByCodeInSubProject from './queries/listRegionByCodeInSubProject.graphql'
import listRegionInSubProject from './queries/listRegionInSubProject.graphql'
import pageBankInSubProject from './queries/pageBankInSubProject.graphql'
import pageBusinessDataDictionaryByQueryInSubProject from './queries/pageBusinessDataDictionaryByQueryInSubProject.graphql'
import pageBusinessDataDictionaryInSubProject from './queries/pageBusinessDataDictionaryInSubProject.graphql'
import pageCertificateCategoryInSubProject from './queries/pageCertificateCategoryInSubProject.graphql'
import pageCertificateInfoInSubject from './queries/pageCertificateInfoInSubject.graphql'
import pageCertificateLevelInSubProject from './queries/pageCertificateLevelInSubProject.graphql'
import pageCertificateMajorInSubProject from './queries/pageCertificateMajorInSubProject.graphql'
import pageCertificateTypeInSubProject from './queries/pageCertificateTypeInSubProject.graphql'
import pageEnterpriseUnitAdminInfoInGovernmentUnit from './queries/pageEnterpriseUnitAdminInfoInGovernmentUnit.graphql'
import pageEnterpriseUnitAdminInfoInMyself from './queries/pageEnterpriseUnitAdminInfoInMyself.graphql'
import pageEnterpriseUnitAdminInfoInSubProject from './queries/pageEnterpriseUnitAdminInfoInSubProject.graphql'
import pageEnterpriseUnitInfoInGovernmentUnit from './queries/pageEnterpriseUnitInfoInGovernmentUnit.graphql'
import pageEnterpriseUnitInfoInServicer from './queries/pageEnterpriseUnitInfoInServicer.graphql'
import pageEnterpriseUnitInfoInSubProject from './queries/pageEnterpriseUnitInfoInSubProject.graphql'
import pageGovernmentUnitAdminInfoInGovernmentUnit from './queries/pageGovernmentUnitAdminInfoInGovernmentUnit.graphql'
import pageGovernmentUnitAdminInfoInMyself from './queries/pageGovernmentUnitAdminInfoInMyself.graphql'
import pageGovernmentUnitAdminInfoInSubProject from './queries/pageGovernmentUnitAdminInfoInSubProject.graphql'
import pageGovernmentUnitInfoInEnterpriseUnit from './queries/pageGovernmentUnitInfoInEnterpriseUnit.graphql'
import pageGovernmentUnitInfoInGovernmentUnit from './queries/pageGovernmentUnitInfoInGovernmentUnit.graphql'
import pageGovernmentUnitInfoInSubProject from './queries/pageGovernmentUnitInfoInSubProject.graphql'
import pageIdentificationCertificateInGovernmentUnit from './queries/pageIdentificationCertificateInGovernmentUnit.graphql'
import pageIdentificationCertificateInfoInSubject from './queries/pageIdentificationCertificateInfoInSubject.graphql'
import pageJobCategoryByQueryInSubProject from './queries/pageJobCategoryByQueryInSubProject.graphql'
import pageJobCategoryInSubProject from './queries/pageJobCategoryInSubProject.graphql'
import pageRatingAgenciesUnitInfoInGovernmentUnit from './queries/pageRatingAgenciesUnitInfoInGovernmentUnit.graphql'
import pageRegionInSubProject from './queries/pageRegionInSubProject.graphql'
import statisticEnterpriseUnitGroupByTimeInGovernmentUnit from './queries/statisticEnterpriseUnitGroupByTimeInGovernmentUnit.graphql'
import statisticEnterpriseUnitIndustryInGovernmentUnit from './queries/statisticEnterpriseUnitIndustryInGovernmentUnit.graphql'
import statisticEnterpriseUnitRegionInGovernmentUnit from './queries/statisticEnterpriseUnitRegionInGovernmentUnit.graphql'
import statisticEnterpriseUnitTypeInGovernmentUnit from './queries/statisticEnterpriseUnitTypeInGovernmentUnit.graphql'

export {
  getAdminInfoInSubProject,
  getBankInSubProject,
  getBusinessDataDictionaryByIdInSubProject,
  getBusinessDataDictionaryInSubProject,
  getCertificateCategoryInSubProject,
  getCertificateInfoInSubject,
  getCertificateLevelInSubProject,
  getCertificateMajorInSubProject,
  getCertificateTypeInSubProject,
  getEnterpriseUnitAdminInfoInMyself,
  getEnterpriseUnitAdminInfoInSubProject,
  getEnterpriseUnitCountInGovernmentUnit,
  getEnterpriseUnitCountInSubProject,
  getEnterpriseUnitInfoInMyself,
  getEnterpriseUnitInfoInSubProject,
  getGovernmentUnitAdminInfoInMyself,
  getGovernmentUnitAdminInfoInSubProject,
  getGovernmentUnitInfoInMyself,
  getGovernmentUnitInfoInSubProject,
  getIdentificationCertificateInfoInSubject,
  getJobCategoryInSubProject,
  getRegionInSubProject,
  getStudentInfoInSubProject,
  getUserInfoInMyself,
  listBusinessDataDictionaryInSubProject,
  listChildRegionInSubProject,
  listRegionByCodeInSubProject,
  listRegionInSubProject,
  pageBankInSubProject,
  pageBusinessDataDictionaryByQueryInSubProject,
  pageBusinessDataDictionaryInSubProject,
  pageCertificateCategoryInSubProject,
  pageCertificateInfoInSubject,
  pageCertificateLevelInSubProject,
  pageCertificateMajorInSubProject,
  pageCertificateTypeInSubProject,
  pageEnterpriseUnitAdminInfoInGovernmentUnit,
  pageEnterpriseUnitAdminInfoInMyself,
  pageEnterpriseUnitAdminInfoInSubProject,
  pageEnterpriseUnitInfoInGovernmentUnit,
  pageEnterpriseUnitInfoInServicer,
  pageEnterpriseUnitInfoInSubProject,
  pageGovernmentUnitAdminInfoInGovernmentUnit,
  pageGovernmentUnitAdminInfoInMyself,
  pageGovernmentUnitAdminInfoInSubProject,
  pageGovernmentUnitInfoInEnterpriseUnit,
  pageGovernmentUnitInfoInGovernmentUnit,
  pageGovernmentUnitInfoInSubProject,
  pageIdentificationCertificateInGovernmentUnit,
  pageIdentificationCertificateInfoInSubject,
  pageJobCategoryByQueryInSubProject,
  pageJobCategoryInSubProject,
  pageRatingAgenciesUnitInfoInGovernmentUnit,
  pageRegionInSubProject,
  statisticEnterpriseUnitGroupByTimeInGovernmentUnit,
  statisticEnterpriseUnitIndustryInGovernmentUnit,
  statisticEnterpriseUnitRegionInGovernmentUnit,
  statisticEnterpriseUnitTypeInGovernmentUnit
}
