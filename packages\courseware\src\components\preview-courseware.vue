<template>
  <el-drawer :visible.sync="show" size="80%" title="预览课件" destroy-on-close>
    <div style="width: 100%; height: 80%">
      <iframe
        v-if="currentId"
        width="100%"
        :src="`http://demo5.jxjy.dev.59iedu.com:8080/play/previewCourseware/${currentId}`"
        frameborder="0"
        height="100%"
        scrolling="no"
      ></iframe>
    </div>
    <el-tabs type="card" class="f-mt20" style="padding: 0 20px" @tab-click="change">
      <el-tab-pane
        :key="item.name"
        v-for="item in viewList"
        :label="item.name"
        :name="item.id"
        @click="change(item)"
      ></el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>

<script lang="ts">
  import { Prop, Vue, Watch } from 'vue-property-decorator'
  import Component from 'vue-class-component'

  @Component
  export default class extends Vue {
    showPreview = false
    @Prop({
      type: Boolean,
      default: false
    })
    visible: boolean
    show = false
    @Prop({
      type: String,
      default: ''
    })
    viewList: Array<{ name: string; id: string }>

    @Watch('visible', { immediate: true })
    visibleChange() {
      this.show = this.visible
    }

    @Watch('show')
    showChange() {
      this.$emit('update:visible', this.show)
    }

    currentId = ''

    @Watch('viewList', { deep: true, immediate: true })
    viewListChange() {
      this.currentId = this.viewList[0].id
      console.log(this.currentId)
    }

    change(item: { name: string }) {
      this.currentId = item.name
      // console.log(this.currentId)
    }
  }
</script>
