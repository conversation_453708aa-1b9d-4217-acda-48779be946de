import LearningTypeBase from '@api/service/common/scheme/model/LearningTypeBase'
import ChooseCourseRule from '@api/service/common/scheme/model/ChooseCourseRule'
import Classification from '@api/service/common/scheme/model/Classification'
import QuizConfig from '@api/service/common/scheme/model/QuizConfig'

/**
 * @description 课程学习方式
 */
class CourseLearningType extends LearningTypeBase {
  /**
   * 总要求学时，仅限自主选课模式使用
   */
  requirePeriod = 0
  /**
   * 必修课要求学时，仅限选课规则模式使用
   */
  compulsoryRequirePeriod = 0
  /**
   * 选修课要求学时，仅限选课规则模式使用
   */
  electiveRequirePeriod = 0
  /**
   * 选课规则，仅限选课规则模式使用
   */
  chooseCourseRule = new ChooseCourseRule()
  /**
   * 分类+课程包id
   * @description 其中第一层是顶级分类，如果第一级无children则代表无分类
   */
  classification = new Classification()
  /**
   *课程完成评定配置-进度要求
   */
  courseSchedule = 0
  /**
   *课程完成评定配置-是否要求课后测验合格
   */
  courseQuizPagerStandard = false
  /**
   *是否启用课程评价
   */
  enableAppraisal = false
  /**
   *是否强制课程评价
   */
  enableCompulsoryAppraisal = false
  /**
   *进行课程评价课程进度要求
   * @description 值-1 => 不限制
   */
  preconditionCourseSchedule = 0
  /**
   * 是否配置课程测验
   */
  configCourseQuiz = false
  /**
   * 课后测验配置
   */
  quizConfigModel = new QuizConfig()
}

export default CourseLearningType
