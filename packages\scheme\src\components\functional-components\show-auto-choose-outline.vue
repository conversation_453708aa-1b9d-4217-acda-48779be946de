<template>
  <div class="pure" v-if="uiConfig.showTree">
    <p class="f-pb15 f-fb">课程展示方式：{{ courseShowTypeName }}</p>
    <!--无分类-->
    <div class="pure" v-if="!hasClassification">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="f-p20">
          <div class="f-flex f-align-center">
            <div class="f-mr10">
              <el-button type="primary" plain class="f-mb10" @click="popViewCompulsoryCourse">查看必学课程</el-button>
            </div>
            <div class="f-fb f-flex-sub">
              一共 <i class="f-cr">{{ outlineInfo.courseTotal }}</i> 门，<i class="f-cr">
                {{ outlineInfo.coursePeriodTotal }}
              </i>
              学时，必学课程 <i class="f-cr">{{ outlineInfo.compulsoryCourseTotal }}</i> 门，<i class="f-cr">
                {{ outlineInfo.compulsoryCoursePeriodTotal }}</i
              >学时
            </div>
          </div>
          <el-table stripe :data="outlineInfo.courseList" max-height="500px" class="m-table f-mb15">
            <el-table-column type="index" label="No." width="60" align="center">
              <template slot-scope="scope">
                <span
                  :data-index="scope.$index + 1"
                  v-observe-visibility="(isVisible, entry) => visibleCourseList(isVisible, entry)"
                  >{{ scope.$index + 1 }}</span
                >
              </template>
            </el-table-column>
            <el-table-column label="课程名称" min-width="240">
              <template slot-scope="scope">
                <div v-if="isCompulsoryCourseWithoutClassification(outlineInfo, scope.row)">
                  <el-tag type="danger" size="mini" class="f-mr5">必学</el-tag>
                  {{ scope.row.name }}
                </div>
                <div v-else>{{ scope.row.name }}</div>
              </template>
            </el-table-column>
            <el-table-column label="所属课程包名称" min-width="200">
              <template slot-scope="scope">{{ scope.row.coursePackageName }}</template>
            </el-table-column>
            <el-table-column label="课程学时数" width="120" align="center">
              <template slot-scope="scope">{{ scope.row.period }}</template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
    <!--有分类-->
    <div class="pure" v-if="hasClassification">
      <div class="f-ptb20 fixed-btn">
        <el-button type="primary" plain @click="popViewCompulsoryCourse"> 查看必学课程 </el-button>
      </div>
      <el-card
        shadow="never"
        class="m-card is-header f-mb15"
        v-for="(item, index) in outlineInfo.childOutlines"
        :key="index"
      >
        <div class="m-tit is-small bg-gray is-border-bottom">
          <span class="tit-txt">{{ item.name }}</span>
          <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
            <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
            <div slot="content">
              按照分类展示课程，可指定必学课程和学习要求学时，支持设置多个子分类。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。
            </div>
          </el-tooltip>
          <span class="f-fb f-ml20 f-flex-sub f-tr">
            一共 <i class="f-cr">{{ item.courseTotal }}</i> 门，<i class="f-cr">{{ item.coursePeriodTotal }}</i>
            学时，必学课程<i class="f-cr">{{ item.compulsoryCourseTotal }}</i> 门，<i class="f-cr">{{
              item.compulsoryCoursePeriodTotal
            }}</i>
            学时
          </span>
        </div>
        <div class="f-flex f-align-center f-plr20 f-pt20 f-fb">
          <!--          <el-button type="primary" plain class="f-mr20">查看必学课程</el-button>-->
          <div class="f-flex-sub">
            {{ item.name }}要求完成 <i class="f-cr">{{ item.assessSetting.requirePeriod }}</i> 学时
          </div>
        </div>
        <el-divider class="m-divider no-mb"></el-divider>
        <el-row class="is-height">
          <el-col :sm="8" :xl="6" class="is-border-right">
            <div class="f-p20">
              <el-tree
                :data="[item]"
                node-key="id"
                :expand-on-click-node="false"
                class="m-course-tree"
                default-expand-all
                :props="{ label: 'name', children: 'childOutlines' }"
                @node-click="
                  (data, node, obj) => {
                    return handleNodeClick(data, true, index)
                  }
                "
              >
              </el-tree>
            </div>
          </el-col>
          <el-col :sm="16" :xl="18">
            <div class="f-p20" v-show="selectedOutlineInfoList[index].isCourseListVisible">
              <div class="f-flex f-align-center">
                <div class="m-tit is-mini">
                  <span class="tit-txt">{{ selectedOutlineInfoList[index].currentCourseInfo.name }}</span>
                </div>
                <div class="f-fb f-flex-sub">
                  （一共 <i class="f-cr">{{ selectedOutlineInfoList[index].currentCourseInfo.courseTotal }}</i> 门，<i
                    class="f-cr"
                    >{{ selectedOutlineInfoList[index].currentCourseInfo.coursePeriodTotal }}</i
                  >
                  学时，必学课程
                  <i class="f-cr">{{ selectedOutlineInfoList[index].currentCourseInfo.compulsoryCourseTotal }}</i>
                  门，<i class="f-cr">{{
                    selectedOutlineInfoList[index].currentCourseInfo.compulsoryCoursePeriodTotal
                  }}</i>
                  学时）
                </div>
              </div>
              <el-table
                :ref="`courseTableRef_${index}`"
                stripe
                :data="selectedOutlineInfoList[index].courseList"
                v-loading="selectedOutlineInfoList[index].courseQuery.loading"
                max-height="500px"
                class="m-table f-mt15"
              >
                <el-table-column type="index" label="No." width="60" align="center">
                  <template slot-scope="scope">
                    <span
                      :data-index="scope.$index + 1"
                      v-observe-visibility="(isVisible, entry) => visibleCourseListHasChildren(isVisible, entry, index)"
                      >{{ scope.$index + 1 }}</span
                    >
                  </template></el-table-column
                >
                <el-table-column label="课程名称" min-width="240">
                  <template slot-scope="scope">
                    <div v-if="isCompulsoryCourse(index, scope.row)">
                      <el-tag type="danger" size="mini" class="f-mr5">必学</el-tag>
                      {{ scope.row.name }}
                    </div>
                    <div v-else>{{ scope.row.name }}</div>
                  </template>
                </el-table-column>
                <el-table-column label="分类信息" min-width="200">
                  <template slot-scope="scope">{{ getCourseCategory(scope.row) }}</template>
                </el-table-column>
                <el-table-column label="所属课程包名称" min-width="200">
                  <template slot-scope="scope">{{ scope.row.sourceCoursePackageName }}</template>
                </el-table-column>
                <el-table-column label="课程学时数" width="120" align="center">
                  <template slot-scope="scope">{{ scope.row.period }}</template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
    <view-compulsory-course
      ref="viewCompulsoryCourseRef"
      v-model="uiConfig.dialog.viewCompulsoryCourseVisible"
      :options="outlineInfo.childOutlines"
      :has-children="hasClassification"
      :resource="outlineInfo"
    ></view-compulsory-course>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import CourseInSchemeResult from '@api/service/management/resource/course/query/vo/CourseInSchemeResult'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import QuerySchemePackageCourseListParams from '@api/service/customer/course/query/vo/QuerySchemePackageCourseListParams'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import SchemeDetailUtils from '@hbfe/jxjy-admin-scheme/src/utils/SchemeDetailUtils'
  import TrainingOutlineCourse from '@api/service/customer/course/query/vo/TrainingOutlineCourse'
  import ViewCompulsoryCourse from '@hbfe/jxjy-admin-scheme/src/components/functional-components/view-compulsory-course.vue'
  import { TreeNode } from 'element-ui/types/tree'
  import { Query, UiPage } from '@hbfe/common'
  import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'
  import CourseStatisticVo from '@api/service/management/resource/course/query/vo/CourseStatisticVo'
  import { CreateOutlineUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateOutlineUtils'
  /**
   * 选中的课程大纲节点信息
   */
  class EditOutlineInfo {
    // 是否展示课程统计信息
    showCourseStatistics: boolean
    // 是否展示分类下课程
    isCourseListVisible: boolean
    // 总计课程门数
    courseTotalCount: number
    // 总计学时数
    courseTotalPeriod: number
    // 必学课程门数
    compulsoryCourseTotalCount: number
    // 必学课程学时数
    compulsoryCourseTotalPeriod: number
    // 当前分类课程信息
    currentCourseInfo: Classification
    // 课程查询
    courseQuery: Query = new Query()
    // 是否加载右侧内容
    contentLoading: boolean
    // 课程分页
    coursePage: UiPage
    // 课程列表
    courseList: SchemeCourseDetailInCoursePackage[]

    constructor() {
      this.showCourseStatistics = false
      this.isCourseListVisible = false
      this.currentCourseInfo = new Classification()
      this.coursePage = new UiPage()
      this.courseQuery = new Query()
      this.courseList = [] as SchemeCourseDetailInCoursePackage[]
    }
  }

  /**
   * 课程列表详情
   */
  class SchemeCourseDetailInCoursePackage extends TrainingOutlineCourse {
    // 所属课程包名称
    coursePackageName: string
    // 分类信息
    courseCategoryInfo: Array<string>
  }

  @Component({
    components: {
      ViewCompulsoryCourse
    }
  })
  export default class extends Vue {
    /**
     * 大纲信息
     */
    @PropSync('classification', { type: Classification }) outlineInfo: Classification

    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: TrainClassDetailClassVo }) schemeDetail: TrainClassDetailClassVo

    /**
     * 查看必学课程
     */
    @Ref('viewCompulsoryCourseRef') viewCompulsoryCourseRef: ViewCompulsoryCourse

    // 选中的课程大纲信息列表
    selectedOutlineInfoList: Array<EditOutlineInfo> = new Array<EditOutlineInfo>()
    // 查询课程总控
    queryCourseM: QueryCourse = new QueryCourse()
    // 课程统计信息列表
    courseStatisticList: CourseStatisticVo[] = []
    // 必学课程学时统计信息
    courseCompulsoryStatisticList: Map<string, number> = new Map<string, number>()
    // 课程分页
    coursePage: UiPage = new UiPage()
    // 课程查询
    courseQuery: Query = new Query()
    /**
     *  ui相关的变量控制
     */
    uiConfig = {
      // 对话框是否展示
      dialog: {
        // 新建课程分类抽屉
        addOutlineVisible: false,
        // 选择课程包抽屉
        chooseCoursePackageVisible: false,
        // 必学课程管理
        compulsoryCourseManageVisible: false,
        // 查看必学课程
        viewCompulsoryCourseVisible: false
      },
      loading: {
        // 页面加载
        pageLoading: false
      },
      // 展示大纲树
      showTree: false
    }

    /**
     * 培训方案id
     */
    get schemeId() {
      return this.schemeDetail.trainClassBaseInfo.id
    }

    /**
     * 是否有分类
     */
    get hasClassification() {
      const classification = this.outlineInfo.childOutlines
      return CreateSchemeUtils.isWeightyArray(classification) ? true : false
    }

    /**
     * 获取课程展示方式名称
     */
    get courseShowTypeName() {
      return this.hasClassification ? '有分类' : '无分类'
    }

    /**
     * 必学课程总数
     */
    get compulsoryCourseTotal() {
      return (item: Classification) => {
        return CreateSchemeUtils.getOutlineCompulsoryCourseInfo(item).length
      }
    }

    /**
     * 必学课程学时总数
     */
    get compulsoryCoursePeriodTotal() {
      return (item: Classification) => {
        return CreateSchemeUtils.getOutlineCompulsoryCourseInfo(item).coursePeriodTotal
      }
    }

    /**
     * 是否是必学课程 - 无分类
     */
    get isCompulsoryCourseWithoutClassification() {
      return (node: Classification, item: SchemeCourseDetailInCoursePackage) => {
        const courseId = item.id
        return node.compulsoryCourseIdList.indexOf(courseId) > -1 ? true : false
      }
    }

    /**
     * 是否是必学课程 - 有分类
     */
    get isCompulsoryCourseWithClassification() {
      return (index: number, item: SchemeCourseDetailInCoursePackage) => {
        const { courseMap } = CreateSchemeUtils.getOutlineCompulsoryCourseInfo(this.outlineInfo.childOutlines[index])
        return courseMap.get(item.id) === item.courseCategoryInfo
      }
    }

    /**
     * 页面初始化
     */
    async created() {
      this.uiConfig.showTree = false
      this.courseStatisticList = await this.queryCourseM.queryCourseStatisticByScheme(
        this.schemeDetail.trainClassBaseInfo.id
      )
      await this.restoreOutline()
      this.initLocalData()
      this.uiConfig.showTree = true
      this.$emit('updateNode')
      console.log(
        this.outlineInfo,
        this.courseStatisticList,
        this.courseCompulsoryStatisticList,
        '(this.outlineInfo.childOutlines)'
      )
    }

    /**
     * 查询符合条件的节点 - 课程大纲树通用
     */
    outlineTreeFind(tree: Array<Classification>, func: any) {
      return CreateSchemeUtils.treeFind<Classification>(tree, func, 'childOutlines')
    }

    /**
     * 查找节点路径 - 课程大纲树通用
     */
    outlineTreeFindPath(func: any, key: string) {
      return CreateSchemeUtils.treeFindPath<Classification>(this.outlineInfo.childOutlines, func, key, 'childOutlines')
    }

    /**
     * 查找所有叶子节点 - 课程大纲树通用
     */
    outlineTreeFindAllLeaves(tree: Array<Classification>) {
      return CreateSchemeUtils.treeFindAllLeaves<Classification>(tree, 'childOutlines')
    }

    /**
     * 还原大纲信息
     */
    async restoreOutline() {
      if (this.hasClassification) {
        // 有分类
        CreateOutlineUtils.setOutlineNameRecursion(this.outlineInfo.childOutlines)
        const outlineTreeLeaves = this.outlineTreeFindAllLeaves(this.outlineInfo.childOutlines)
        const reqList =
          outlineTreeLeaves
            ?.map((ite) => {
              return { outlineId: ite.id, courseIdList: ite.compulsoryCourseIdList }
            })
            ?.filter((item) => item.courseIdList?.length > 0) || []
        this.courseCompulsoryStatisticList =
          await this.queryCourseM.allCountPeriodCountOfCourseTrainingOutlineInSchemeInServicer(
            this.schemeDetail.trainClassBaseInfo.id,
            reqList
          )
        await this.getCourseListWithClassification()
      } else {
        // 无分类
        if (this.outlineInfo.compulsoryCourseIdList?.length)
          this.courseCompulsoryStatisticList =
            await this.queryCourseM.allCountPeriodCountOfCourseTrainingOutlineInSchemeInServicer(
              this.schemeDetail.trainClassBaseInfo.id,
              [{ outlineId: this.outlineInfo.id, courseIdList: this.outlineInfo.compulsoryCourseIdList }]
            )
        await this.getCourseListWithoutClassification()
      }
    }

    /**
     * 获取有分类课程列表
     */
    async getCourseListWithClassification() {
      SchemeDetailUtils.recursionSetOutlineParentId(this.outlineInfo.childOutlines)
      const outlineTreeLeaves = this.outlineTreeFindAllLeaves(this.outlineInfo.childOutlines)
      outlineTreeLeaves.forEach((item) => {
        // 标识节点加载课程的模式
        const courseStatistic =
          this.courseStatisticList.find((ite) => ite.outlineId === item.id) || new CourseStatisticVo()
        item.courseTotal = courseStatistic.courseTotal
        item.coursePeriodTotal = courseStatistic.coursePeriodTotal
        item.compulsoryCoursePeriodTotal = this.courseCompulsoryStatisticList?.get(item.id) || 0
        item.compulsoryCourseTotal = item.compulsoryCourseIdList?.length || 0
        this.refreshOutlineTree(item.parentId)
      })
      // const hasCourseLeaves =
      //   outlineTreeLeaves?.filter((el: Classification) => el.coursePackageId) || ([] as Classification[])
      // await Promise.all(
      //   hasCourseLeaves?.map(async (item: Classification) => {
      //     await this.updateTreeNode(item.id)
      //   })
      // )
    }
    /**
     * 查询课程列表-无分类
     */
    async queryCourseList(): Promise<SchemeCourseDetailInCoursePackage[]> {
      this.courseQuery.loading = true
      const result = new Array<SchemeCourseDetailInCoursePackage>()
      const coursePackageId = this.outlineInfo.coursePackageId
      // 如果没有课程包id，直接返回空数组
      if (!coursePackageId) return result
      const respList = new Array<SchemeCourseDetailInCoursePackage>()
      const resList = await this.queryCourseM.queryCourseListInSchemeByOutline(
        this.coursePage,
        [this.outlineInfo.id],
        this.schemeDetail.trainClassBaseInfo.id
      )
      resList?.forEach((item) => {
        const coursePackageName = item.sourceCoursePackageName
        const opt = new SchemeCourseDetailInCoursePackage()
        Object.assign(opt, item)
        opt.coursePackageName = coursePackageName
        opt.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
          return node.id === item.outlineId
        }, 'name')
        respList.push(opt)
      })

      result.push(...respList)
      this.courseQuery.loading = false
      return result
    }
    /**
     * 查询课程列表-有分类
     */
    async queryCourseListHasChildren(target: EditOutlineInfo): Promise<SchemeCourseDetailInCoursePackage[]> {
      const result = [] as SchemeCourseDetailInCoursePackage[]
      const coursePackageId = target.currentCourseInfo.coursePackageId
      // 如果没有课程包id，直接返回空数组
      if (!coursePackageId) return result
      const resList = await this.queryCourseM.queryCourseListInSchemeByOutline(
        target.coursePage,
        [target.currentCourseInfo.id],
        this.schemeDetail.trainClassBaseInfo.id
      )
      resList?.forEach((item) => {
        const coursePackageName = item.sourceCoursePackageName
        const opt = new SchemeCourseDetailInCoursePackage()
        Object.assign(opt, item)
        opt.coursePackageName = coursePackageName
        opt.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
          return node.id === item.outlineId
        }, 'name')
        result.push(opt)
      })
      return result
    }
    /**
     * 滚动查询课程列表-有分类
     */
    async visibleCourseListHasChildren(isVisible: boolean, entry: any, index?: number) {
      const target = this.selectedOutlineInfoList[index]
      const scopeIndex = entry.target.dataset.index
      if (isVisible) {
        if (parseInt(scopeIndex) >= target.coursePage.totalSize) {
          // 最大值时不请求
          return
        }
        if (parseInt(scopeIndex) == target.courseList.length) {
          target.courseQuery.loading = true
          target.coursePage.pageNo++
          const list = await this.queryCourseListHasChildren(target)
          target.courseList = target.courseList.concat(list)
          target.courseQuery.loading = false
        }
      }
    }

    /**
     * 滚动查询课程列表无分类
     */
    async visibleCourseList(isVisible: boolean, entry: any, index?: number) {
      const target = this.outlineInfo
      const scopeIndex = entry.target.dataset.index
      if (isVisible) {
        if (parseInt(scopeIndex) >= this.coursePage.totalSize) {
          // 最大值时不请求
          return
        }
        if (parseInt(scopeIndex) == target.courseList.length) {
          this.coursePage.pageNo++
          const list = await this.queryCourseList()
          target.courseList.push(...list)
        }
      }
    }
    /**
     * 获取无分类课程列表
     */
    async getCourseListWithoutClassification() {
      const coursePackageId = this.outlineInfo.coursePackageId
      if (!coursePackageId) {
        this.$message.error('课程学习信息异常')
        return
      }
      const targetNode = this.outlineInfo
      targetNode.courseList = [] as SchemeCourseDetailInCoursePackage[]
      targetNode.courseList = await this.queryCourseList()
      const courseStatistic =
        this.courseStatisticList.find((ite) => ite.outlineId === targetNode.idCopy) || new CourseStatisticVo()
      targetNode.courseTotal = courseStatistic.courseTotal
      targetNode.coursePeriodTotal = courseStatistic.coursePeriodTotal
      targetNode.compulsoryCourseTotal = targetNode.compulsoryCourseIdList?.length || 0
      targetNode.compulsoryCoursePeriodTotal = this.courseCompulsoryStatisticList?.get(targetNode.id) || 0
    }

    /**
     * 初始化本地数据
     */
    initLocalData() {
      this.selectedOutlineInfoList = new Array<EditOutlineInfo>()
      this.outlineInfo.childOutlines?.forEach((el: Classification, index: number) => {
        const option = new EditOutlineInfo()
        option.isCourseListVisible = false
        option.currentCourseInfo = new Classification()
        option.showCourseStatistics = false
        this.selectedOutlineInfoList.push(option)
        this.handleNodeClick(el, true, index)
      })
      this.selectedOutlineInfoList?.forEach((el: EditOutlineInfo, index: number) => {
        el.currentCourseInfo = this.outlineInfo.childOutlines[index]
        el.isCourseListVisible = true
      })
    }

    /**
     * 查询课程大纲节点下课程
     */
    async getOutlineCourseList(outlineId: string): Promise<CourseInSchemeResult> {
      if (!outlineId) return
      const params: QuerySchemePackageCourseListParams = new QuerySchemePackageCourseListParams()
      params.schemeId = this.schemeDetail.trainClassBaseInfo.id
      params.outlineIdList = [outlineId]
      const queryCourseListRemote = ResourceModule.courseFactory.queryCourse
      return await queryCourseListRemote.queryCourseListInSchemeByByPackage(params)
    }

    /**
     * 更新节点信息 - 课程相关
     */
    async updateTreeNode(nodeId: string) {
      const targetNode = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
        return node.id === nodeId
      })
      targetNode.courseList = new Array<SchemeCourseDetailInCoursePackage>()
      const outlineId = targetNode.id
      const courseInfoInOutline: CourseInSchemeResult = await SchemeDetailUtils.getOutlineCourseList(
        outlineId,
        this.schemeId
      )
      courseInfoInOutline.trainingOutlineCourse?.map((el: TrainingOutlineCourse) => {
        const option = new SchemeCourseDetailInCoursePackage()
        Object.assign(option, el)
        option.coursePackageName = el.sourceCoursePackageName
        option.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
          return node.id === nodeId
        }, 'name')
        targetNode.courseList.push(option)
      })
      this.refreshOutlineTree(targetNode.parentId)
    }

    /**
     * 更新属性信息 - 末级课程列表变化触发
     * @param {string} parentId - 父节点id
     */
    refreshOutlineTree(parentId: string) {
      const parentOutlineNode = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
        return node.id === parentId
      })
      if (!parentOutlineNode) return
      parentOutlineNode.courseTotal =
        parentOutlineNode.childOutlines.reduce((prev, cur) => {
          return CalculatorObj.add(cur.courseTotal || 0, prev)
        }, 0) || 0
      parentOutlineNode.coursePeriodTotal =
        parentOutlineNode.childOutlines.reduce((prev, cur) => {
          return CalculatorObj.add(cur.coursePeriodTotal || 0, prev)
        }, 0) || 0
      // 必学课程学时
      parentOutlineNode.compulsoryCourseTotal =
        parentOutlineNode.childOutlines.reduce((prev, cur) => {
          return CalculatorObj.add(cur.compulsoryCourseTotal, prev)
        }, 0) || 0
      parentOutlineNode.compulsoryCoursePeriodTotal =
        parentOutlineNode.childOutlines.reduce((prev, cur) => {
          return CalculatorObj.add(cur.compulsoryCoursePeriodTotal, prev)
        }, 0) || 0
      this.refreshOutlineTree(parentOutlineNode.parentId)
    }

    /**
     * 查看必学课程
     */
    popViewCompulsoryCourse() {
      this.viewCompulsoryCourseRef.setData(true)
      this.uiConfig.dialog.viewCompulsoryCourseVisible = true
    }
    /**
     * 课程大纲节点点击响应事件
     */
    async handleNodeClick(data: Classification, cancelLoad = true, index?: number) {
      // 判断前后节点是否重复，重复则不变化
      if (!this.selectedOutlineInfoList[index]) return
      const sourceId = this.selectedOutlineInfoList[index].currentCourseInfo.id
      const targetId = data.id
      this.selectedOutlineInfoList[index].isCourseListVisible = true
      if (cancelLoad && sourceId === targetId) return
      const target = this.selectedOutlineInfoList[index]
      // 开始加载
      target.contentLoading = true
      target.currentCourseInfo = new Classification()
      target.currentCourseInfo = data
      target.coursePage = new UiPage()
      if (this.isLeafNode(index)) {
        // 叶子节点：重新请求列表
        target.courseList = await this.queryCourseListHasChildren(target)
      } else {
        // 非叶子节点：清空课程列表
        target.courseList = []
      }
      // 切换节点后，表格右侧滚动条自动滚到顶部
      this.$nextTick(() => {
        const ele = this.$refs[`courseTableRef_${index}`]
          ? (this.$refs[`courseTableRef_${index}`][0] as any)
          : undefined
        const bodyWrapper = ele?.bodyWrapper as any
        if (bodyWrapper) bodyWrapper.scrollTop = 0
      })
      target.contentLoading = false
    }

    /**
     * 获取课程分类信息
     */
    getCourseCategory(row: SchemeCourseDetailInCoursePackage) {
      return row.courseCategoryInfo?.join('>') || ''
    }

    /**
     * 选中的课程节点是否是叶子节点
     */
    get isLeafNode() {
      return (index: number) => {
        const length = this.selectedOutlineInfoList[index].currentCourseInfo.childOutlines?.length || 0
        return length === 0 ? true : false
      }
    }

    /**
     * 是否是必学课程
     */
    get isCompulsoryCourse() {
      return (index: number, item: SchemeCourseDetailInCoursePackage) => {
        const outline = this.outlineTreeFind(this.outlineInfo.childOutlines, (node: Classification) => {
          return node.id === this.selectedOutlineInfoList[index].currentCourseInfo.id
        })
        const getIndex = outline.compulsoryCourseIdList.findIndex((ite) => ite === item.id)
        return getIndex === -1 ? false : true
      }
    }
  }
</script>

<style scoped lang="scss">
  .pure {
    margin: 0;
    padding: 0;
  }
</style>
