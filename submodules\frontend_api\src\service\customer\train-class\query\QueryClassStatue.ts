import MsStudentLearningV1, { ApplyLearningTokenRequest } from '@api/ms-gateway/ms-studentlearning-v1'
import { SchemeInfoEnum } from '@api/service/common/webfunny/enums/SchemeEnum'
import SchemeReportModel from '@api/service/common/webfunny/models/SchemeReportModel'
import WebfunnyReport from '@api/service/common/webfunny/WefunnyReport'
import { ResponseStatus } from '@hbfe/common'

export class QueryClassStatue {
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 期别参训资格ID
   */
  periodQualificationId: string

  //校验班级状态
  /**
   * 正常&#x3D;200.
   培训未开始 &#x3D; 50002
   培训已结束 &#x3D; 50003
   处在换班或者退款中 &#x3D; 70001
   学员培训资格已失效 &#x3D; 70002
   学习方式被停用 &#x3D; 70003
   前置条件未达标 &#x3D; 60004
   重算中 &#x3D; 51001
   500 其他未定义异常
   */
  async judgeClass() {
    const newResStatus = new ResponseStatus(200, '')
    const stuRes = await MsStudentLearningV1.validStudentAllowLearning(this.qualificationId)
    if (!stuRes.status.isSuccess()) {
      if (stuRes.status.code == 30006) {
        const error = stuRes.status.errors[0]
        newResStatus.code = parseInt(this.shadowChange(error.code + ''))
        newResStatus.message = error.message
      } else {
        newResStatus.code = parseInt(this.shadowChange(stuRes.status.code + ''))
        newResStatus.message = stuRes.status.message
      }
      try {
        // webfunny 埋点
        const webfunnyError = new SchemeReportModel()
        webfunnyError.scene = SchemeInfoEnum.check_class_status
        webfunnyError.qualificationId = this.qualificationId
        webfunnyError.learningId = this.learningId
        webfunnyError.requestBody = JSON.stringify(this.qualificationId, ['qualificationId', 'learningId'], 4)
        webfunnyError.response = JSON.stringify(stuRes)
        webfunnyError.abnormalMessage = stuRes.status?.errors[0]?.message || '获取学员学习token失败'
        webfunnyError.bizCode = newResStatus.code.toString()
        WebfunnyReport.upCommonEvent('通用--培训方案', webfunnyError)
      } catch (error) {
        console.log(error)
      }
    }
    // else {
    //   if (stuRes.data.code != '200') {
    //     newResStatus.code = parseInt(this.shadowChange(stuRes.data.code))
    //     newResStatus.message = stuRes.data.message
    //   }
    // }
    return newResStatus
  }

  /**
   * 校验班级状态层
   * @description
   * 正常 = 200
   * 培训未开始 = 50002
   * 培训已结束 = 50003
   * 班级正在开通中 = 50006
   * 班级正在退班中 = 50007
   * 学员培训资格已冻结 = 60001
   * 学员培训资格已失效 = 60002
   * 重算未完成 = 60005
   * 存在未完成的强制调查问卷 = 60008
   * 期别已结束培训 = 60012
   * 期别未开始培训 = 60015
   */
  async judgeClassWithoutLearningTypeId(): Promise<ResponseStatus> {
    const { status } = await MsStudentLearningV1.validStudentAllowLearning(this.qualificationId)
    return status
  }

  /**
   * 正常&#x3D;200.
   培训未开始 &#x3D; 50002
   培训已结束 &#x3D; 50003
   处在换班或者退款中 &#x3D; 70001
   学员培训资格已失效 &#x3D; 70002
   学习方式被停用 &#x3D; 70003
   前置条件未达标 &#x3D; 60004
   非期数参训资格 = 60006
   重算中 &#x3D; 51001
   500 其他未定义异常
   */
  async judgePeriodClass() {
    const newResStatus = new ResponseStatus(200, '')
    const requestStudentLearningTokenParams = new ApplyLearningTokenRequest()
    requestStudentLearningTokenParams.learningId = this.learningId
    requestStudentLearningTokenParams.qualificationId = this.periodQualificationId
    const stuRes = await MsStudentLearningV1.applyStudentLearningTokenInIssue(requestStudentLearningTokenParams)
    if (!stuRes.status.isSuccess()) {
      if (stuRes.status.code == 30006) {
        const error = stuRes.status.errors[0]
        newResStatus.code = parseInt(this.shadowChange(error.code + ''))
        newResStatus.message = error.message
      } else {
        newResStatus.code = parseInt(this.shadowChange(stuRes.status.code + ''))
        newResStatus.message = stuRes.status.message
      }
      try {
        // webfunny 埋点
        const webfunnyError = new SchemeReportModel()
        webfunnyError.scene = SchemeInfoEnum.check_class_status
        webfunnyError.qualificationId = this.qualificationId
        webfunnyError.learningId = this.learningId
        webfunnyError.requestBody = JSON.stringify(
          requestStudentLearningTokenParams,
          ['qualificationId', 'learningId'],
          4
        )
        webfunnyError.response = JSON.stringify(stuRes)
        webfunnyError.abnormalMessage = stuRes.status?.errors[0]?.message || '获取学员学习token失败'
        webfunnyError.bizCode = newResStatus.code.toString()
        WebfunnyReport.upCommonEvent('通用--培训方案', webfunnyError)
      } catch (error) {
        console.log(error)
      }
    }
    return newResStatus
  }

  private shadowChange(code: string) {
    if (code == '60001') {
      return '70001'
    }
    if (code == '60002') {
      return '70002'
    }
    if (code == '60003') {
      return '70003'
    }
    return code
  }
}
