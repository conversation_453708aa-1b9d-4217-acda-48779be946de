import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-training-channel-fore-gateway'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-training-channel-fore-gateway'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class RegionModel {
  regionId: string
  regionPath: string
  provinceId: string
  provinceName: string
  cityId: string
  cityName: string
  countyId: string
  countyName: string
}

/**
 * 附件返回值
 */
export class AttachmentResponse {
  /**
   * 附件名称
   */
  name: string
  /**
   * 附件地址
   */
  url: string
}

export class IndustryModel {
  /**
   * 行业id
   */
  industryId: string
  /**
   * 行业名称
   */
  industryName: string
}

export class NetSchoolResponse {
  /**
   * 门户类型（1：web端 2：移动端）
   */
  portalType: number
  /**
   * 网校域名
   */
  netSchoolDomainName: string
}

export class TrainingChannelDetailResponse {
  /**
   * 专题id
   */
  id: string
  /**
   * 网校id
   */
  servicerId: string
  /**
   * 网校名称
   */
  netSchoolName: string
  /**
   * 网校域名
   */
  netSchoolDoMain: Array<NetSchoolResponse>
  /**
   * 专题入口名称
   */
  entryName: string
  /**
   * 专题名称
   */
  name: string
  /**
   * 域名类型（1：系统默认域名 2：自定义）
   */
  domainNameType: number
  /**
   * 专题域名名称
   */
  domainName: string
  /**
   * 专题类型：
1-地区  2-行业  3-班级
   */
  types: Array<number>
  /**
   * 是否显示在网校
   */
  showOnNetSchool: boolean
  /**
   * PC端专题模板编号
   */
  pcTemplateNo: string
  /**
   * H5端专题模板编号
   */
  h5TemplateNo: string
  /**
   * 启用状态（0：停用 1：启用）
   */
  enable: boolean
  /**
   * 状态（1：草稿 2：正常）
   */
  status: number
  /**
   * 排序号码
   */
  sort: number
  /**
   * 适用地区
   */
  regions: Array<RegionModel>
  /**
   * 适用行业
   */
  industrys: Array<string>
  /**
   * 专题门户
   */
  topic: TrainingChannelTopicResponse
  /**
   * 已配置方案数
   */
  configuredPlans: number
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 是否允许访问
   */
  allowAccess: boolean
}

export class TrainingChannelPageResponse {
  /**
   * 专题id
   */
  id: string
  /**
   * 专题名称
   */
  name: string
  /**
   * 入口名称
   */
  entryName: string
  /**
   * 专题类型：
1-地区  2-行业  3-班级
   */
  types: Array<number>
  /**
   * 是否展示在网校
   */
  showOnNetSchool: boolean
  /**
   * 是否允许访问
   */
  allowAccess: boolean
  /**
   * 网校id
   */
  netSchoolId: string
  /**
   * 网校名称
   */
  netSchoolName: string
  /**
   * 网校域名
   */
  netSchoolDomainName: Array<NetSchoolResponse>
  /**
   * 专题域名
   */
  domainName: string
  /**
   * PC端专题模板编号
   */
  pcTemplateNo: string
  /**
   * H5端专题模板编号
   */
  h5TemplateNo: string
  /**
   * 已配置方案数
   */
  configuredPlans: number
  /**
   * 启用状态（0：停用 1：启用）
   */
  enable: boolean
  /**
   * 排序
   */
  sort: number
  /**
   * 适用地区
   */
  regions: Array<RegionModel>
  /**
   * 适用行业
   */
  industrys: Array<IndustryModel>
  /**
   * 最后编辑时间
   */
  updatedTime: string
  /**
   * 单位名称
   */
  unitName: string
}

/**
 * <AUTHOR> linq
@date : 2025-05-15 14:08
@description : 专题简要信息列表
 */
export class TrainingChannelSimplePageResponse {
  /**
   * 专题id
   */
  id: string
  /**
   * 专题类型：
1-地区  2-行业  3-班级
   */
  types: Array<number>
  /**
   * 适用行业id
   */
  industryIdList: Array<string>
  /**
   * 单位名称
   */
  unitName: string
}

export class TrainingChannelTopicPhotoResponse {
  /**
   * 专题门户轮播图id
   */
  id: string
  /**
   * 专题门户轮播图类型：1 web端、2 H5端
   */
  type: number
  /**
   * 专题门户轮播图地址
   */
  pictureUrl: string
  /**
   * 链接地址
   */
  linkUrl: string
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
}

export class TrainingChannelTopicResponse {
  /**
   * 专题门户id
   */
  id: string
  /**
   * logo类型（1：文字、2：图片）
   */
  logoType: number
  /**
   * 专题门户logo名称（专题门户logo类型为文字时，有值）
   */
  logoName: string
  /**
   * 专题门户logo图片地址（专题门户logo类型为图片时，有值）
   */
  logoPictureUrl: string
  /**
   * 客服电话类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.CustomerServicePhoneTypes
   */
  customerServicePhoneType: number
  /**
   * 客服电话
   */
  customerServicePhone: string
  /**
   * 客服电话图片路径
   */
  customerServicePhonePictureUrl: string
  /**
   * 培训流程类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
@see com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.TrainingProcessTypes
   */
  trainingProcessType: number
  /**
   * 培训流程图片
@see com.fjhb.platform.jxjy.v1.api.trainingchannel.event.trainingchannelonlinecollective.Attachment
   */
  trainingProcessAttachments: Array<AttachmentResponse>
  /**
   * 企业微信客服类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
   */
  enterpriseWechatCustomerType: number
  /**
   * 企业微信客服图片
   */
  enterpriseWechatCustomerAttachments: Array<AttachmentResponse>
  /**
   * 咨询时间
   */
  seekTime: string
  /**
   * 专题门户底部落款类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.PortalBottomShowTypes
   */
  bottomShowType: number
  /**
   * 专题门户底部落款内容（专题门户底部落款类型为自定义时 有值）
   */
  bottomShowContent: string
  /**
   * 轮播图集合
   */
  photos: Array<TrainingChannelTopicPhotoResponse>
}

export class TrainingChannelPageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingChannelPageResponse>
}

export class TrainingChannelSimplePageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingChannelSimplePageResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取当前专题信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCurrentTrainingChannelInfo(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCurrentTrainingChannelInfo,
    operation?: string
  ): Promise<Response<TrainingChannelPageResponse>> {
    return commonRequestApi<TrainingChannelPageResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 子项目-分页获取网校下的专题信息
   * @param id                网校id
   * @param trainingChanelIds 专题id列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPageTrainingChannelInfo(
    params: { page?: Page; id?: string; trainingChanelIds?: Array<string>; isShowAll?: boolean },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPageTrainingChannelInfo,
    operation?: string
  ): Promise<Response<TrainingChannelPageResponsePage>> {
    return commonRequestApi<TrainingChannelPageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 子项目级别-分页获取网校下的专题简要信息
   * @param id                网校id
   * @param trainingChanelIds 专题id列表
   * @param isShowAll 是否查询所有专题（默认为false，查询启用且在网校下显示的专题）
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPageTrainingChannelSimpleInfoInSubject(
    params: { page?: Page; id?: string; trainingChanelIds?: Array<string>; isShowAll?: boolean },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPageTrainingChannelSimpleInfoInSubject,
    operation?: string
  ): Promise<Response<TrainingChannelSimplePageResponsePage>> {
    return commonRequestApi<TrainingChannelSimplePageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 子项目-根据专题id查询专题详情
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTrainingChannelDetailById(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTrainingChannelDetailById,
    operation?: string
  ): Promise<Response<TrainingChannelDetailResponse>> {
    return commonRequestApi<TrainingChannelDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
