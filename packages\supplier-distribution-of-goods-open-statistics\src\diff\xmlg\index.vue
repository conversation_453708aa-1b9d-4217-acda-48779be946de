//供应商分销商品开通统计

<template>
  <xmlg-supplier-distribution-of-goods-open-statistics ref="supplierDistributionOfGoodsOpenStatisticsRef">
    <template #remove-training-plan="{ distributionGoodsOpeningStatistics }">
      <el-form-item label="剔除商品">
        <biz-remove-training-plan
          v-model="distributionGoodsOpeningStatistics.param.excludeCommodityIdList"
          placeholder="请选择不纳入统计的商品"
        ></biz-remove-training-plan>
      </el-form-item>
    </template>
  </xmlg-supplier-distribution-of-goods-open-statistics>
</template>

<script lang="ts">
  import DistributionGoodsOpeningStatistics from '@api/service/diff/management/xmlg/statistical-report/DistributionGoodsOpeningStatistics/DistributionGoodsOpeningStatistics'
  import DistributionGoodsOpeningStatisticsParams from '@api/service/diff/management/xmlg/statistical-report/DistributionGoodsOpeningStatistics/model/DistributionGoodsOpeningStatisticsParams'
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import SupplierDistributionOfGoodsOpenStatistics from '@hbfe/jxjy-admin-supplierDistributionOfGoodsOpenStatistics/src/index.vue'

  @Component
  export class XmlgSupplierDistributionOfGoodsOpenStatistics extends SupplierDistributionOfGoodsOpenStatistics {
    // 分销商品开通统计 模型
    distributionGoodsOpeningStatistics = new DistributionGoodsOpeningStatistics()
  }
  @Component({
    components: {
      XmlgSupplierDistributionOfGoodsOpenStatistics
    }
  })
  export default class extends Vue {
    @Ref('supplierDistributionOfGoodsOpenStatisticsRef')
    supplierDistributionOfGoodsOpenStatisticsRef: XmlgSupplierDistributionOfGoodsOpenStatistics
  }
</script>
