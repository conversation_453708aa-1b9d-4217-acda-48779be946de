import GaugeType, { GaugeTypeEnum } from '@api/service/common/question-naire/enums/GaugeType'
import BaseQuestion from '@api/service/common/question-naire/BaseQuestion'
/**
 * 量表题
 */
export default class GaugeQuestion extends BaseQuestion {
  /**
   * UI组件绑值
   */
  selectedContent = 0
  /**
   * 是否选中 UI使用
   */
  isSelect = false
  /**
   * 级数
   */
  levelNum = 5
  /**
   * 量表类型
   */
  type: GaugeTypeEnum = null
  /**
   * 自定义程度大端值
   */
  maxDeepTip = ''
  /**
   * 自定义程度小端值
   */
  minDeepTip = ''
  /**
   * 起始数值
   */
  startLevel = 1
}
