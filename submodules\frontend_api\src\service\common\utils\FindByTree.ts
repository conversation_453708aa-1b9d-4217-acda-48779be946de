export default class FindByTree<T> {
  tree: Array<T>
  path: Array<T>

  constructor(tree: Array<T>) {
    this.tree = tree
    this.path = new Array<T>()
  }

  /**
   *
   * @param targetValue 目标值
   * @param children 孩子节点字段
   * @param targetKey 目标的字段
   * @param fuzzySearch 是否模糊查询
   * @returns 路径
   */
  findPath({ targetValue = '', children = 'children', targetKey = 'id', fuzzySearch = false }) {
    if (!targetValue) {
      return []
    }
    const result: T[] = []
    const fn = (
      targetValue = '',
      children = 'children',
      targetKey = 'id',
      tree: Array<T>,
      path: Array<T>,
      fuzzySearch: boolean
    ): Array<T> => {
      for (let i = 0; i < tree.length; i++) {
        const tempPath = [...path]
        tempPath.push(tree[i])
        if (fuzzySearch) {
          if (tree[i][targetKey].includes(targetValue)) {
            result.push(tempPath.pop())
            // return tempPath
          }
        } else {
          if (tree[i][targetKey] == targetValue) {
            result.push(tempPath.pop())
            // return tempPath
          }
        }
        if (tree[i][children] && tree[i][children].length) {
          const result = fn(targetValue, children, targetKey, tree[i][children], tempPath, fuzzySearch)
          if (result) {
            return result
          }
        }
      }
    }
    fn(targetValue, children, targetKey, this.tree, this.path, fuzzySearch)
    return result || []
  }
}
