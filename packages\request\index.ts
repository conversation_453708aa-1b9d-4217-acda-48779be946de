import axios, { AxiosResponse, AxiosError, AxiosRequestConfig } from 'axios'

export class Response<T> implements AxiosResponse {
  config: AxiosRequestConfig
  data: T
  headers: any
  status: number
  statusText: string
  // todo
}

export class ResponseError<T> implements AxiosError {
  config: AxiosRequestConfig
  isAxiosError: boolean
  message: string
  name: string
  response: Response<T>

  toJSON(): object {
    return {}
  }

  // todo
}

export default axios
