<template>
  <el-drawer title="修改发票信息" :visible.sync="dialogVisible" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <div class="m-tit">
        <span class="tit-txt">原发票信息</span>
      </div>
      <el-form ref="form" label-width="150px" class="m-text-form f-mt10">
        <el-col :span="12">
          <el-form-item label="发票类型：" class="is-text"> 增值税电子专用发票（线下开票） </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发票抬头：" class="is-text"> 【单位】{{ invoiceInfo.title }} </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="统一社会信用代码：" class="is-text">{{ invoiceInfo.taxpayerNo }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户银行：" class="is-text">{{ invoiceInfo.bankName }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户账号：" class="is-text">{{ invoiceInfo.account }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册电话：" class="is-text">{{ invoiceInfo.rePhone }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册地址：" class="is-text">{{ invoiceInfo.address }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="营业执照：">
            <el-image
              :src="invoiceInfo.businessLicenseUrl"
              :preview-src-list="[invoiceInfo.businessLicenseUrl]"
              class="course-pic is-small"
            >
            </el-image>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户许可证：">
            <el-image
              :src="invoiceInfo.permitUrl"
              :preview-src-list="[invoiceInfo.permitUrl]"
              class="course-pic is-small"
            >
            </el-image>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号码：" class="is-text">{{ invoiceInfo.contactPhone }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电子邮箱：" class="is-text">{{ invoiceInfo.contactEmail }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发票备注：" class="is-text" v-if="invoiceInfo.remark">
            {{ invoiceInfo.remark }}
          </el-form-item>
        </el-col>
      </el-form>
      <el-divider class="m-divider"></el-divider>
      <div class="m-tit">
        <span class="tit-txt">修改的发票信息</span>
      </div>
      <el-form
        ref="modifyInvoiceForm"
        :model="updateInfo"
        :rules="specialInvoiceRules"
        label-width="150px"
        class="m-form f-mt10"
      >
        <el-form-item label="发票类型：" class="is-text">增值税电子专用发票（线下开票）</el-form-item>
        <el-form-item label="发票抬头：" prop="title">
          <el-radio-group v-model="updateInfo.titleType">
            <el-radio :label="2">
              <span class="f-mr10">单位</span>
            </el-radio>
          </el-radio-group>
          <el-input v-model="updateInfo.title" clearable placeholder="请输入抬头" />
        </el-form-item>
        <el-form-item label="统一社会信用代码：" prop="taxpayerNo">
          <el-input
            v-model="updateInfo.taxpayerNo"
            @input="upperCase"
            clearable
            placeholder="请输入18位统一社会信用代码"
            class="form-l"
          />
        </el-form-item>
        <el-form-item label=" " class="is-text">
          <span class="f-co">注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span>
        </el-form-item>
        <el-form-item label="开户银行：" prop="bankName">
          <el-input v-model="updateInfo.bankName" clearable placeholder="请输入开户银行" class="form-l" />
        </el-form-item>
        <el-form-item label="开户帐号：" prop="account">
          <el-input v-model="updateInfo.account" clearable placeholder="请输入开户帐号" class="form-l" />
        </el-form-item>
        <el-form-item label="注册电话：" prop="rePhone">
          <el-input
            v-model="updateInfo.rePhone"
            clearable
            maxlength="20"
            show-word-limit
            placeholder="请输入单位注册电话"
            class="form-m"
          />
        </el-form-item>
        <el-form-item label="注册地址：" prop="address">
          <el-input
            v-model="updateInfo.address"
            clearable
            maxlength="100"
            show-word-limit
            placeholder="请输入单位注册地址"
          />
        </el-form-item>
        <el-form-item label="营业执照：" prop="businessLicenseUrl" ref="businessLicensePicture">
          <upload-images v-model="businessLicenseUrlList" :limit="1" :is-protected="true"></upload-images>
        </el-form-item>
        <el-form-item label="开户许可证：" prop="permitUrl" ref="permitPicture">
          <upload-images v-model="permitUrlList" :limit="1" :is-protected="true"></upload-images>
        </el-form-item>

        <el-form-item label="手机号码：" prop="contactPhone">
          <el-input v-model="updateInfo.contactPhone" clearable placeholder="请输入手机号码" class="form-l" />
        </el-form-item>
        <el-form-item label="电子邮箱：" prop="contactEmail">
          <el-input v-model="updateInfo.contactEmail" clearable placeholder="请输入电子邮箱" class="form-l" />
        </el-form-item>
        <el-form-item label="发票备注：">
          <el-input
            v-model="updateInfo.remark"
            maxlength="100"
            type="textarea"
            :rows="3"
            placeholder="请填写发票备注信息"
            class="f-mt5 f-mb15"
          ></el-input>
        </el-form-item>
      </el-form>

      <el-form
        ref="deliveryForm"
        :model="updateInfo"
        :rules="specialInvoiceRules"
        label-width="150px"
        class="m-form f-mt10"
      >
      </el-form>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="commitApply">保存发票信息</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { TitleTypeEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import UploadImages from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/upload-images.vue'
  import OffLinePageInvoiceResponseVo from '@api/service/management/trade/single/invoice/mutation/dto/OffLinePageInvoiceResponseVo'
  import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import NationalRegionCascader from '@hbfe/jxjy-admin-components/src/national-region/national-region-cascader.vue'
  import TakePlaceDetailVo from '@api/service/management/online-school-config/distribution-channels-config/query/vo/TakePlaceDetailVo'
  import QueryDeliverWayTypeList from '@api/service/common/trade-config/query/QueryDeliverWayTypeList'
  import FileModule from '@api/service/common/file/FileModule'
  export class UploadImageFile {
    name: string
    url: string
  }

  @Component({
    components: { NationalRegionCascader, UploadImages }
  })
  export default class extends Vue {
    @Ref('modifyInvoiceForm') modifyInvoiceForm: any
    @Ref('deliveryForm') deliveryForm: any
    @Prop({
      type: Boolean,
      default: false
    })
    dialogCtrl: boolean

    @Prop({
      type: String,
      default: '1'
    })
    invoiceType: string

    @Prop({
      type: String,
      default: ''
    })
    invoiceId: string

    businessLicenseUrlList: Array<UploadImageFile> = new Array<UploadImageFile>()
    permitUrlList: Array<UploadImageFile> = new Array<UploadImageFile>()

    invoiceMapType = {
      ['1']: '增值税电子普通发票（自动开票）',
      ['2']: '增值税电子发票（线下开票）'
    }

    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }

    form = {
      name: '',
      resource: ''
    }

    regionName = ''

    QueryDeliverWayTypeList = new QueryDeliverWayTypeList()

    // 原发票信息
    invoiceInfo: OffLinePageInvoiceVo = new OffLinePageInvoiceVo()
    // 修改的发票信息
    updateInfo: OffLinePageInvoiceResponseVo = new OffLinePageInvoiceResponseVo()

    pickUpList: Array<TakePlaceDetailVo> = new Array<TakePlaceDetailVo>()

    // curPickUpIndex = 0

    //专票开票校验规则
    specialInvoiceRules = {
      title: [
        {
          required: true,
          message: '请填写抬头',
          trigger: ['change', 'blur']
        }
      ],
      taxpayerNo: [
        {
          required: true,
          message: '请输入统一社会信用代码',
          trigger: ['change', 'blur']
        }
      ],
      bankName: [
        {
          required: true,
          message: '请输入开户银行',
          trigger: ['change', 'blur']
        }
      ],
      contactEmail: [
        {
          required: true,
          message: '请输入电子邮箱',
          trigger: ['change', 'blur']
        }
      ],
      contactPhone: [
        {
          required: true,
          message: '请输入手机号',
          trigger: ['change', 'blur']
        }
      ],
      account: [
        {
          required: true,
          message: '请输入开户账号',
          trigger: ['change', 'blur']
        }
      ],
      rePhone: [
        {
          required: true,
          message: '请输入注册电话',
          trigger: ['change', 'blur']
        }
      ],
      address: [
        {
          required: true,
          message: '请输入注册地址',
          trigger: ['change', 'blur']
        }
      ],
      businessLicenseUrl: [
        {
          required: true,
          message: '请上传营业执照',
          trigger: ['change', 'blur']
        }
      ],
      permitUrl: [
        {
          required: true,
          message: '请上传开户许可证',
          trigger: ['change', 'blur']
        }
      ]
    }

    dialogVisible = false

    @Watch('dialogCtrl')
    changeDialogCtrl() {
      if (this.dialogCtrl) {
        this.getInvoiceInfo()
      }
      this.dialogVisible = this.dialogCtrl
    }

    @Watch('dialogVisible')
    changeDialogVisible() {
      this.$emit('update:dialogCtrl', this.dialogVisible)
    }

    @Watch('businessLicenseUrlList', {
      deep: true
    })
    changeBusinessLicenseUrl(val: any) {
      const el: any = this.$refs['businessLicensePicture']
      if (el) {
        if (val.length) {
          this.updateInfo.businessLicenseUrl = val[0].url
          el.clearValidate()
        } else {
          this.updateInfo.businessLicenseUrl = null
          el.validate()
        }
      }
    }

    @Watch('permitUrlList', {
      deep: true
    })
    changePermitUrl(val: any) {
      const el: any = this.$refs['permitPicture']
      if (el) {
        if (val.length) {
          this.updateInfo.permitUrl = val[0].url
          el.clearValidate()
        } else {
          this.updateInfo.permitUrl = null
          el.validate()
        }
      }
    }

    upperCase() {
      const arr = this.updateInfo.taxpayerNo.split('')
      let newStr = ''
      arr.forEach((value) => {
        if (value >= 'a' && value <= 'z') {
          newStr += value.toUpperCase()
        } else {
          newStr += value
        }
      })
      this.updateInfo.taxpayerNo = newStr
    }

    doSave() {
      this.modifyInvoiceForm.validate((valid: boolean) => {
        this.deliveryForm.validate((deliveryValid: boolean) => {
          if (valid && deliveryValid) {
            this.doModify()
          }
        })
      })
    }

    async doModify() {
      const res =
        await TradeModule.singleTradeBatchFactor.invoiceFactor.mutationOffLineInvoice.updateOfflineElectInvoice(
          this.updateInfo
        )
      if (res.status.isSuccess()) {
        this.$message.success('修改发票成功')
        this.dialogVisible = false
      } else {
        this.$message.error(res.status?.errors[0]?.message || '修改发票失败')
      }
    }

    //四个全填和全不填的以及格式校验
    validUnitInfo() {
      const titleReg = /^[0-9a-zA-Z\u4e00-\u9fa5（）()《》—-]+$/
      const taxpayerNoReg = /^[A-Za-z0-9]{18}$/
      const telephoneReg = /^1[3456789][0-9]{9}$/
      const emailReg = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/

      if (this.updateInfo.address.indexOf('·') != -1) {
        this.$message.warning('注册地址暂不支持特殊字符“ · ”')
        return false
      }
      if (!titleReg.test(this.updateInfo.title)) {
        this.$message.warning('单位名称特殊符号仅支持《》、—、-、（）')
        return false
      }
      if (!taxpayerNoReg.test(this.updateInfo.taxpayerNo)) {
        this.$message.warning('请输入正确的18位统一社会信用代码')
        return false
      }
      //手机号校验
      if (!telephoneReg.test(this.updateInfo.contactPhone)) {
        this.$message.warning('请输入正确的手机号码')
        return false
      }
      //电子邮箱格式
      if (!emailReg.test(this.updateInfo.contactEmail)) {
        this.$message.warning('请输入正确的电子邮箱格式')
        return false
      }
      return true
    }

    //修改发票信息
    commitApply() {
      this.modifyInvoiceForm.validate((valid: boolean) => {
        this.deliveryForm.validate((deliveryValid: boolean) => {
          if (valid && deliveryValid && this.validUnitInfo()) {
            this.doModify()
          }
        })
      })
    }

    // 注释=====================

    async getPickUpInfo() {
      try {
        // this.curPickUpIndex = 0
        await this.QueryDeliverWayTypeList.query()
        this.pickUpList = this.QueryDeliverWayTypeList.pickUpList
        this.pickUpList.forEach((item: TakePlaceDetailVo, index: number) => {
          if (
            item.openTakeTime == this.updateInfo.takePointPickupTime &&
            item.address == this.updateInfo.takePointPickupLocation &&
            item.remark == this.updateInfo.takePointRemark
          ) {
            // this.curPickUpIndex = index
          }
        })
      } catch (e) {
        console.log(e)
      }
    }

    async getInvoiceInfo() {
      this.updateInfo = new OffLinePageInvoiceResponseVo()
      await FileModule.applyResourceAccessToken()
      try {
        this.invoiceInfo =
          (await TradeModule.singleTradeBatchFactor.invoiceFactor.queryOffLineInvoice.offLineGetInvoiceInServicer(
            this.invoiceId
          )) as OffLinePageInvoiceVo
        if (this.invoiceInfo.businessLicenseUrl && this.invoiceInfo.businessLicenseUrl.substring(0, 4) !== '/mfs') {
          this.invoiceInfo.businessLicenseUrl = this.$util.imgUrlWithToken('/mfs' + this.invoiceInfo.businessLicenseUrl)
        } else {
          this.invoiceInfo.businessLicenseUrl = this.$util.imgUrlWithToken(this.invoiceInfo.businessLicenseUrl)
        }
        if (this.invoiceInfo.permitUrl && this.invoiceInfo.permitUrl.substring(0, 4) !== '/mfs') {
          this.invoiceInfo.permitUrl = this.$util.imgUrlWithToken('/mfs' + this.invoiceInfo.permitUrl)
        } else {
          this.invoiceInfo.permitUrl = this.$util.imgUrlWithToken(this.invoiceInfo.permitUrl)
        }

        this.updateInfo = Object.assign(new OffLinePageInvoiceResponseVo(), this.invoiceInfo)
        this.updateInfo.titleType = TitleTypeEnum.UNIT

        this.businessLicenseUrlList = new Array<UploadImageFile>()
        this.permitUrlList = new Array<UploadImageFile>()
        this.businessLicenseUrlList.push(new UploadImageFile())
        this.permitUrlList.push(new UploadImageFile())
        this.businessLicenseUrlList[0].url = this.updateInfo.businessLicenseUrl
        this.businessLicenseUrlList[0].name = this.updateInfo.businessLicenseUrl
        this.permitUrlList[0].url = this.updateInfo.permitUrl
        this.permitUrlList[0].name = this.updateInfo.permitUrl
        await this.getPickUpInfo()
      } catch (e) {
        console.log(e)
      } finally {
        //都要执行的操作
      }
    }

    created() {
      this.dialogVisible = this.dialogCtrl
    }
  }
</script>
