<!--执行情况-->
<template>
  <div class="f-p15" v-if="$hasPermission('execution')" desc="智能学习执行情况" actions="queryServerList">
    <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb15">
      <div class="f-flex f-align-center" v-loading="loading">
        <div class="f-flex-sub">
          检测到当前系统存在【<span class="f-cr">{{ executionErrorCount || 0 }}</span
          >】条人性化学习任务执行异常，是否立即处理？
        </div>
        <template>
          <el-button type="primary" size="small" @click="handleNow">立即处理</el-button>
        </template>
      </div>
    </el-alert>
    <el-card shadow="never" class="m-card f-mb15">
      <!--条件查询-->
      <hb-search-wrapper :model="trackingParam" @reset="reset">
        <el-form-item label="培训方案" v-if="!isZtglyLogin">
          <biz-learning-scheme-select v-model="hasSelectSchemeMode" :isOnlyOnline="true"></biz-learning-scheme-select>
        </el-form-item>
        <el-form-item label="培训方案" v-if="isZtglyLogin">
          <biz-learning-scheme-zt-select
            v-model="hasSelectSchemeMode"
            :isOnlyOnline="true"
          ></biz-learning-scheme-zt-select>
        </el-form-item>

        <el-form-item label="姓名">
          <el-input v-model="trackingParam.name" clearable placeholder="请输入学员姓名" />
        </el-form-item>

        <el-form-item label="证件号">
          <el-input v-model="trackingParam.idCard" clearable placeholder="请输入学员证件号" />
        </el-form-item>

        <el-form-item label="手机号">
          <el-input v-model="trackingParam.phone" clearable placeholder="请输入手机号" />
        </el-form-item>

        <el-form-item label="导入任务">
          <template v-if="$hasPermission('importTask')" desc="导入任务" actions="@ImportTask">
            <import-task v-model="trackingParam.importTaskId" inputId="executionId" />
          </template>
        </el-form-item>

        <el-form-item label="任务执行状态">
          <el-select v-model="trackingParam.taskStatus" clearable multiple placeholder="请选择">
            <el-option v-for="status in taskStatusList" :key="status.code" :label="status.desc" :value="status.code" />
          </el-select>
        </el-form-item>

        <template slot="actions">
          <el-button type="primary" @click="queryList">查询</el-button>
          <template v-if="$hasPermission('terminateTaskList')" desc="批量终止任务" actions="terminateTaskList">
            <el-button type="primary" @click="terminateTaskList" :loading="stopLoading">批量终止任务</el-button>
          </template>
        </template>
      </hb-search-wrapper>
      <!--表格-->
      <el-table
        stripe
        ref="tableRef"
        :data="executionList"
        max-height="500px"
        class="m-table"
        :row-key="getRowKeys"
        @selection-change="handleSelectionChange"
        v-loading="loading"
      >
        <el-table-column
          type="selection"
          width="60"
          align="center"
          :selectable="selectable"
          fixed="left"
        ></el-table-column>
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="学员帐号信息" min-width="240" fixed="left">
          <template slot-scope="scope">
            <p>姓名：{{ scope.row.name || '-' }}</p>
            <p>证件号：{{ scope.row.idCard || '-' }}</p>
          </template>
        </el-table-column>
        <el-table-column label="培训方案名称" min-width="300">
          <template slot-scope="scope">{{ scope.row.trainingSchemeName || '-' }}</template>
        </el-table-column>
        <el-table-column label="订单号" min-width="220">
          <template slot-scope="scope">
            {{ scope.row.orderNo || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="任务执行状态" min-width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.taskStatus.equal(TaskStatusEnum.un_start)">
              <el-badge is-dot type="warning" class="badge-status">未开始</el-badge>
            </div>
            <div v-else-if="scope.row.taskStatus.equal(TaskStatusEnum.execution)">
              <el-badge is-dot type="primary" class="badge-status">执行中</el-badge>
            </div>
            <div v-else-if="scope.row.taskStatus.equal(TaskStatusEnum.fail)">
              <el-badge is-dot type="danger" class="badge-status">执行失败</el-badge>
            </div>
            <div v-else-if="scope.row.taskStatus.equal(TaskStatusEnum.stop)">
              <el-badge is-dot type="info" class="badge-status">终止</el-badge>
            </div>
            <div v-else-if="scope.row.taskStatus.equal(TaskStatusEnum.complete)">
              <el-badge is-dot type="success" class="badge-status">已完成</el-badge>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" min-width="180">
          <template slot-scope="scope">{{ scope.row.startTime }}</template>
        </el-table-column>
        <el-table-column label="状态更新时间" min-width="180">
          <template slot-scope="scope">{{ scope.row.updateTime }}</template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template slot-scope="scope">
            <template v-if="$hasPermission('logArrangement')" desc="查看编排日志" actions="@ArrangeLogDrawer">
              <el-button type="text" @click="viewArrangeLog(scope.row)">查看编排日志</el-button>
            </template>
            <template v-if="$hasPermission('reset')" desc="重启任务" actions="@RestartTaskDrawer">
              <el-button
                type="text"
                v-if="[TaskStatusEnum.stop, TaskStatusEnum.fail].includes(scope.row.taskStatus.current)"
                @click="restartTask(scope.row)"
                >重启任务</el-button
              >
            </template>
            <template v-if="$hasPermission('logExecution')" desc="查看执行日志" actions="@ExecutionLogDrawer">
              <el-button type="text" @click="viewExecutionLog(scope.row)">查看执行日志</el-button>
            </template>
            <template v-if="$hasPermission('terminateTask')" desc="终止任务" actions="terminateTask">
              <el-button
                type="text"
                v-if="
                  scope.row.taskStatus.equal(TaskStatusEnum.un_start) ||
                  scope.row.taskStatus.equal(TaskStatusEnum.execution)
                "
                :loading="stopLoading"
                @click="terminateTask(scope.row)"
                >终止任务</el-button
              >
            </template>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page" :pageSizes="[5, 10, 15, 20, 25, 50, 100]"></hb-pagination>
      <arrange-log-drawer ref="arrangeRef" />
      <execution-log-drawer ref="executionRef" />
      <restart-task-drawer ref="restartRef" @success="searchQuery" />
    </el-card>
    <span
      v-if="$hasPermission('ZNXXFB')"
      desc="智能学习任务反馈"
      actions="@hbfe/jxjy-admin-components/src/intelligent-learning-task-feedback/index.vue"
      query
    ></span>
  </div>
</template>
<script lang="ts">
  import { Vue, Component, Ref } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import TrackingParam from '@api/service/management/intelligence-learning/model/TrackingParam'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import ExecutionListInServicer from '@api/service/management/intelligence-learning/ExecutionListInServicer'
  import ExecutionListInTrainingChannel from '@api/service/management/intelligence-learning/ExecutionListInTrainingChannel'
  import TaskStatusType, { TaskStatusEnum } from '@api/service/management/intelligence-learning/enum/TaskStatusEnum'
  import BizLearningSchemeZtSelect from '@hbfe/jxjy-admin-components/src/biz/biz-learning-scheme-zt-select.vue'
  import ExecutionItem from '@api/service/management/intelligence-learning/model/ExecutionItem'
  import ArrangeLogDrawer from '@hbfe/jxjy-admin-intelligentLearning/src/learning-task-tracking/components/arrange-log-drawer.vue'
  import ExecutionLogDrawer from '@hbfe/jxjy-admin-intelligentLearning/src/learning-task-tracking/components/execution-log-drawer.vue'
  import RestartTaskDrawer from '@hbfe/jxjy-admin-intelligentLearning/src/learning-task-tracking/components/restart-task-drawer.vue'
  import ImportTask from '@hbfe/jxjy-admin-intelligentLearning/src/learning-task-tracking/components/import-task.vue'
  import { bind, debounce } from 'lodash-decorators'
  import { ElTable } from 'element-ui/types/table'

  @Component({
    components: { BizLearningSchemeZtSelect, ArrangeLogDrawer, ExecutionLogDrawer, RestartTaskDrawer, ImportTask }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.searchQuery, this.searchQuery)
    }

    @Ref('tableRef')
    tableRef: ElTable

    @Ref('arrangeRef') arrangeRef: ArrangeLogDrawer

    @Ref('executionRef') executionRef: ExecutionLogDrawer

    @Ref('restartRef') restartRef: RestartTaskDrawer

    stopLoading = false

    /**
     * 分页
     */
    page: UiPage

    /**
     * 加载状态
     */
    loading = false

    /**
     * 查询入参
     */
    trackingParam = new TrackingParam()

    /**
     * 执行任务状态枚举
     */
    TaskStatusEnum = TaskStatusEnum

    /**
     * 执行一场数
     */
    executionErrorCount = 0

    /**
     * 方案模型
     */
    hasSelectSchemeMode = new Array<HasSelectSchemeMode>()

    /**
     * 列表数据
     */
    executionList = new Array<ExecutionItem>()

    /**
     * 网校操作项
     */
    serverMutation = new ExecutionListInServicer()

    /**
     * 选中的值
     */
    selected = new Array<ExecutionItem>()

    /**
     * 判断当前用户是否拥有专题管理员角色类型
     */
    get isZtglyLogin() {
      return QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    }

    /**
     * 获取执行任务状态列表
     */
    get taskStatusList() {
      const taskStatusList = new TaskStatusType()
      return taskStatusList.list()
    }

    /**
     * 是否可选
     */
    selectable(row: ExecutionItem) {
      if (row.taskStatus.equal(TaskStatusEnum.un_start) || row.taskStatus.equal(TaskStatusEnum.execution)) {
        return true
      } else {
        return false
      }
    }

    /**
     * 唯一key
     * @param row
     */
    getRowKeys(row: ExecutionItem) {
      return row.currentTaskId
    }

    /**
     * 清空选中项
     */
    clearSelection() {
      this.tableRef.clearSelection()
      this.selected = new Array<ExecutionItem>()
    }

    /**
     * 批量终止任务
     */
    async terminateTaskList() {
      if (!this.selected.length) {
        return this.$confirm('请选择终止任务对象。', {
          showCancelButton: false
        })
      }
      this.$confirm(
        '您当前正在批量终止勾选的智能学习任务，操作成功后将终止其待执行/执行中的学习任务，后续如需智能学习需通过重启/重新导入重新编排学习任务，确认要操作？'
      )
        .then(async () => {
          try {
            this.stopLoading = true
            const ids = this.selected.map((item: ExecutionItem) => {
              return item.currentTaskId
            })
            const res = await this.serverMutation.terminateTaskListInServicer(ids)
            if (res.status.isSuccess()) {
              const find = res.data.responses?.find((item) => item.code == '501')
              if (find) {
                return this.$confirm('存在部分终止任务处理中，请耐性等待。', {
                  showCancelButton: false
                }).then(() => {
                  // todo
                })
              } else if (res.data.responses?.find((item) => item.code == '502')) {
                return this.$confirm('任务已终止，无需重复操作。', {
                  showCancelButton: false
                }).then(() => {
                  this.clearSelection()
                })
              } else {
                await this.queryList()
                this.clearSelection()
              }
            } else {
              this.$message.error('请求失败')
            }
          } catch (e) {
            console.log(e)
          } finally {
            this.stopLoading = false
          }
        })
        .catch(() => {
          //   todo
        })
    }

    /**
     * 选中操作
     */
    handleSelectionChange(value: ExecutionItem[]) {
      this.selected = value
    }

    /**
     * 查询按钮
     */
    @bind
    @debounce(200)
    async queryList() {
      this.trackingParam.immediate = false
      await this.page.currentChange(1)
    }

    /**
     * 重置
     */
    @bind
    @debounce(200)
    async reset() {
      this.trackingParam = new TrackingParam()
      this.hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
      await this.page.currentChange(1)
    }

    /**
     * 查询
     */
    async searchQuery() {
      if (this.isZtglyLogin) {
        await this.queryChannelList()
      } else {
        await this.queryServerList()
      }
    }

    /**
     * 网校查询编排任务
     */
    async queryServerList() {
      try {
        this.loading = true
        // const mutation = new ExecutionListInServicer()
        if (this.hasSelectSchemeMode.length) {
          this.trackingParam.trainingSchemeName = this.hasSelectSchemeMode[0].scheme
        } else {
          this.trackingParam.trainingSchemeName = ''
        }
        this.serverMutation.queryParam = this.trackingParam
        const res = await this.serverMutation.queryList(this.page)

        if (res.isSuccess()) {
          this.executionList = this.serverMutation.list
        } else {
          this.$message.error('请求失败')
        }
        const countRes = await ExecutionListInServicer.queryCountByStatusAndTime({
          status: [TaskStatusEnum.fail, TaskStatusEnum.stop]
        })
        if (countRes.status.isSuccess()) {
          this.executionErrorCount = countRes.data
        }
        this.clearSelection()
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 查询专题编排任务
     */
    async queryChannelList() {
      try {
        this.loading = true
        const mutation = new ExecutionListInTrainingChannel()
        if (this.hasSelectSchemeMode.length) {
          this.trackingParam.trainingSchemeName = this.hasSelectSchemeMode[0].scheme
        } else {
          this.trackingParam.trainingSchemeName = ''
        }
        mutation.queryParam = this.trackingParam
        const res = await mutation.queryList(this.page)
        if (res.isSuccess()) {
          this.executionList = mutation.list
        } else {
          this.$message.error('请求失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 查看编排日志
     */
    @bind
    @debounce(200)
    async viewArrangeLog(row: ExecutionItem) {
      await this.arrangeRef.open(row)
    }

    /**
     * 重启任务
     */
    @bind
    @debounce(200)
    async restartTask(row: ExecutionItem) {
      await this.restartRef.open(row)
    }

    /**
     * 查看执行日志
     */
    @bind
    @debounce(200)
    async viewExecutionLog(row: ExecutionItem) {
      await this.executionRef.open(row)
    }

    @bind
    @debounce(200)
    async terminateTask(row: ExecutionItem) {
      this.$confirm(
        '您当前正在终止学员的智能学习任务，操作成功后将终止其待执行/执行中的学习任务，后续如需智能学习需通过重启/重新导入重新编排学习任务，确认要操作？'
      )
        .then(async () => {
          try {
            this.stopLoading = true
            const res = await this.serverMutation.terminateTaskListInServicer([row.currentTaskId])
            if (res.status.isSuccess()) {
              const find = res.data.responses?.find((item) => item.code == '501')
              if (find) {
                return this.$confirm('终止任务处理中，请耐心等待。', {
                  showCancelButton: false
                }).then(() => {
                  // todo
                })
              } else if (res.data.responses?.find((item) => item.code == '502')) {
                return this.$confirm('任务已终止，无需重复操作。', {
                  showCancelButton: false
                }).then(() => {
                  // todo
                })
              } else {
                await this.queryList()
              }
            } else {
              this.$message.error('请求失败')
            }
          } catch (e) {
            console.log(e)
          } finally {
            this.stopLoading = false
          }
        })
        .catch(() => {
          //   todo
        })
    }

    /**
     * 立即处理
     */
    @bind
    @debounce(200)
    async handleNow() {
      this.trackingParam = new TrackingParam()
      this.trackingParam.taskStatus = [TaskStatusEnum.fail, TaskStatusEnum.stop]
      this.trackingParam.immediate = true
      await this.page.currentChange(1)
      this.clearSelection()
    }
  }
</script>
