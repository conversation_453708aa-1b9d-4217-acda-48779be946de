import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum JoinTimeTypeEnum {
  class_time = 1,
  designate_time
}

class JoinTimeType extends AbstractEnum<JoinTimeTypeEnum> {
  constructor(status?: JoinTimeTypeEnum) {
    super()
    this.current = status
    this.map.set(JoinTimeTypeEnum.class_time, '同培训班的学习起止时间')
    this.map.set(JoinTimeTypeEnum.designate_time, '指定的参加时间')
  }
}

export default JoinTimeType
