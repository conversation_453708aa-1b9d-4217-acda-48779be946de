import MSCertificate, { QueryExportTaskRequest } from '@api/ms-gateway/ms-certificate-v1'
import UserInfoCollectiveModule from '@api/service/management/user/UserModule'
import QueryExportRecordsVo from '@api/service/centre/train-class/query/vo/QueryExportRecordsVo'
import PlatformForestage, { JobRequest } from '@api/platform-gateway/fxnl-data-export-gateway-forestage'
import PlatformCertificate from '@api/platform-gateway/platform-certificate-v1'
import { Page, Response } from '@hbfe/common'
export default class QueryExportRecordsList {
  /**
   * 任务名(写死)
   */
  category = 'filter_batch_print_certificate'
  /**
   * 执行状态
   * 0 - 已创建 1 - 已就绪 2 - 执行中 3 - 已完成
   */
  taskState?: number

  /**
   * 查询导出列表-培训证明
   * @param page
   * @returns {Promise<QueryExportRecordsVo[]>}
   */
  async queryExportList(page: Page) {
    let result = new Array<QueryExportRecordsVo>()
    const request = new QueryExportTaskRequest()
    request.category = this.category
    request.taskState = this.taskState
    request.userId = UserInfoCollectiveModule.queryUserFactory.queryManagerDetail.adminInfo?.userInfo?.userId
    const res = await MSCertificate.queryExportTaskResponsePage({ page, request })

    if (res.status.isSuccess()) {
      result = res.data.currentPageData?.map(QueryExportRecordsVo.from)
      page.totalSize = res.data?.totalSize
      page.totalPageSize = res.data?.totalPageSize
    }
    return result
  }
  /**
   * 查询导出列表-学习数据
   */
  async queryExportStudyData(page: Page) {
    let result = new Array<QueryExportRecordsVo>()
    const jobRequest = new JobRequest()
    jobRequest.group = 'exportStudentLearningStatisticalInCollective'
    const res = await PlatformForestage.pageExportTaskInfoInMyself({ page, jobRequest })

    if (res.status.isSuccess()) {
      result = res.data.currentPageData?.map(QueryExportRecordsVo.fromStudy)
      page.totalSize = res.data?.totalSize
      page.totalPageSize = res.data?.totalPageSize
    }
    return result
  }

  /**
 * 查询导出失败数据
 * * @param mainTaskId  主任务id
 */
  async exportCertificateFailedData(mainTaskId: string): Promise<Response<string>> {
    //
    const res = await PlatformCertificate.collectivelyAdministratorExportCertificateFailedData({ mainTaskId })
    const response = new Response<string>()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = res.data
    return response
  }

}
