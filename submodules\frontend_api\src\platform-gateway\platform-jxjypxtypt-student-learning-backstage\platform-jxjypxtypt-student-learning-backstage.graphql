schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
type Query {
	"""重推学员学习合格数据
		@return
	"""
	rePushStudentTrainingResultInServicer(studentNos:[String]):String
	"""重推学员学习合格数据-统一网关 v2
		@return
	"""
	rePushStudentTrainingResultToGatewayInServicerV2(studentNos:[String]):String
}

scalar List
