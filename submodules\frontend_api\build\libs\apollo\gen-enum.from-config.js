/**
 * 文件用途，从 apollo 微服务读取当前适配的 appKey 的常量字段，在使用的时候，使用的时候避免使用有问题。
 */
const got = require('got')
const path = require('path')
const fs = require('fs')
const prettier = require('prettier')
const env = require('dotenv').config({ path: path.resolve(process.cwd(), '.env') })
const queryConfigGraphql = `query {
  getCurrentFrontendConfig
}`

const saveTargetPath = path.resolve(process.cwd(), './src/service/common/config/enums')
const apolloKeys = []

got
  .post(`${env.parsed.VUE_APP_MS_SERVICE_HOST}/gql/ms-config-v1`, {
    body: JSON.stringify({ query: queryConfigGraphql }),
    headers: {
      'Content-Type': 'application/json',
      'App-Authentication': `Basic ${env.parsed.VUE_APP_KEY}`,
      'Graphql-SchemaName': 'ms-config-v1',
      'Service-Name': 'ms-config'
    }
  })
  .then(res => {
    const body = JSON.parse(res.body)
    const getCurrentFrontendConfig = body.data.getCurrentFrontendConfig
    const keys = Object.keys(getCurrentFrontendConfig)
    keys.forEach(key => {
      const item = getCurrentFrontendConfig[key]
      const itemEnum = []
      const keyName = key.replace(/_[a-z]/g, i => {
        return i.replace(/_/g, '').toUpperCase()
      })
      itemEnum.push(`export enum ${keyName} {`)
      item.forEach(value => {
        const myKey = value.key.replace(new RegExp(`^${key}.`), '')
        const resultKey = myKey
          .replace(/\.[a-z]/g, (a, b) => {
            return a.replace(/\./, '').toUpperCase()
          })
          .replace(/-[a-zA-Z]/g, i => {
            return i.toUpperCase().replace(/-/g, '')
          })
        itemEnum.push(`${resultKey} = '${value.key}',`)
      })
      itemEnum[itemEnum.length - 1] = itemEnum[itemEnum.length - 1].replace(/,$/, '')
      itemEnum.push(`}`)
      apolloKeys.push(itemEnum.join('\n'))
    })

    fs.writeFileSync(
      path.join(saveTargetPath, 'ApolloConfigKeysEnum.ts'),
      prettier.format(apolloKeys.join('\n'), {
        parser: 'typescript',
        semi: false,
        singleQuote: true
      }),
      { encoding: 'utf-8' }
    )
  })
