<route-meta>
{
"title": "课件类型下拉选择器"
}
</route-meta>
<template>
  <el-select v-model="selected" :clearable="clearable" @clear="selected = undefined" :placeholder="placeholder">
    <el-option v-for="item in options" :label="item.desc" :value="item.code" :key="item.code"> </el-option>
  </el-select>
</template>

<script lang="ts">
  import { Prop, Emit, Watch, Component, Vue } from 'vue-property-decorator'
  import CoursewareType from '@api/service/management/resource/courseware/enum/CoursewareType'

  @Component
  export default class extends Vue {
    @Prop({
      type: String,
      default: '请选择课件类型'
    })
    placeholder: string

    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      type: String,
      default: ''
    })
    value: string

    selected = ''
    options = new CoursewareType().list()

    @Watch('value')
    valueChange() {
      this.selected = this.value
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }
  }
</script>
