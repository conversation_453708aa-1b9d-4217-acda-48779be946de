<!--
 * @Author: z张仁榕
 * @Date: 2025-04-09 14:00:35
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-04-18 11:49:04
 * @Description: 
-->
<template>
  <el-select v-model="selected" :clearable="clearable" @clear="selected = undefined" :placeholder="placeholder">
    <el-option v-for="item in options" :label="item.desc" :value="item.code" :key="item.code"> </el-option>
  </el-select>
</template>

<script lang="ts">
  import PaymentMethod, { PaymentMethodEnum } from '@api/service/management/trade/batch/order/enum/PaymentMethod'
  import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      type: String,
      default: '请选择缴费方式'
    })
    placeholder: string

    @Prop({
      type: String,
      default: ''
    })
    value: string

    selected = ''
    options = PaymentMethod.list().filter((item) => {
      return item.code !== PaymentMethodEnum.No_Payment
    })

    @Watch('value')
    valueChange() {
      this.selected = this.value
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }
  }
</script>
