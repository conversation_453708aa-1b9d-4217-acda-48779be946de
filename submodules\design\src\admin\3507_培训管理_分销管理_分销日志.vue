<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">日志详情</el-button>
        <el-drawer title="日志详情" :visible.sync="dialog2" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <div class="f-mt20 f-mlr40">
              <el-timeline>
                <el-timeline-item>
                  <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20</p>
                  <p>【林萧】将【x】改为【y】；修改内容名称【x】改为【y】</p>
                </el-timeline-item>
                <el-timeline-item>
                  <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20</p>
                  <p>
                    【修改人名称】将上级分销商的分销地区【福州市、厦门市】改为【厦门市】，当前分销商的
                    代理地区【福州市鼓楼区】改为【无】
                  </p>
                </el-timeline-item>
                <el-timeline-item>
                  <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20</p>
                  <p>【林萧】将【x】改为【y】；修改内容名称【x】改为【y】</p>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>关闭</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: true,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
