import MutationCreateTakePlace from '@api/service/management/online-school-config/distribution-channels-config/mutation/MutationCreateTakePlace'
import MutationUpdateTakePlace from '@api/service/management/online-school-config/distribution-channels-config/mutation/MutationUpdateTakePlace'
import MutationUpdateExpressRemark from '@api/service/management/online-school-config/distribution-channels-config/mutation/MutationUpdateExpressRemark'
import MutationSetDistributionWay from '@api/service/management/online-school-config/distribution-channels-config/mutation/MutationSetDistributionWay'
import MutationTakePlace from '@api/service/management/online-school-config/distribution-channels-config/mutation/MutationTakePlace'
import MsTradeQueryFrontGatewayTradeQueryBackstage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import TakePlaceDetailVo from '@api/service/management/online-school-config/distribution-channels-config/query/vo/TakePlaceDetailVo'
import UpdateTakePlaceVo from '@api/service/management/online-school-config/distribution-channels-config/mutation/vo/UpdateTakePlaceVo'

/**
 * @description 配送渠道配置工厂
 */
class MutationDistributionChannelsConfigFactory {
  /**
   * @description 添加自取点
   */
  get createTakePlace() {
    return new MutationCreateTakePlace()
  }

  /**
   * @description 修改自取点
   */
  async updateTakePlace(id: string) {
    const result = await MsTradeQueryFrontGatewayTradeQueryBackstage.getDeliveryChannelInServicer(id)
    return new MutationUpdateTakePlace(UpdateTakePlaceVo.from(result.data))
  }

  /**
   * @description 操作自取点
   */
  async mutationTakePlace(id: string) {
    return new MutationTakePlace(id)
  }

  /**
   * @description 修改快递备注
   */
  updateExpressRemark(id: string, remark: string) {
    return new MutationUpdateExpressRemark(id, remark)
  }

  /**
   * @description 设置配送方式
   */
  get setDistributionWay() {
    return new MutationSetDistributionWay()
  }
}

export default new MutationDistributionChannelsConfigFactory()
