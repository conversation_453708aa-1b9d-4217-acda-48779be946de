import { Response } from '@hbfe/common'
import MsCertificateGateway from '@api/ms-gateway/ms-certificate-v1'
import ConfigureElectronicSealRequestVo from './vo/ConfigureElectronicSealRequestVo'
class MutationBatchPrintTraining {
  // 配置电子章
  electronicSealParams = new ConfigureElectronicSealRequestVo()

  /**
   * @description: 配置电子章
   * 【修改/新增都是此接口】
   */
  async doConfigureElectronicSeal(): Promise<Response<string>> {
    const res = await MsCertificateGateway.configureElectronicSeal(this.electronicSealParams)
    const response = new Response<string>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = res.data
    response.status = res.status
    return response
  }
}

export default MutationBatchPrintTraining
