import ReportConfigDto from '@api/service/management/implement/models/ReportConfigDto'
import MockUtil from '@api/service/common/utils/MockUtil'
import PeriodConfig from '@api/service/management/implement/models/PeriodConfig'
import MsSchemeLearningQuery, {
  IssueStudyConfigResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { Page } from '@hbfe/common'

/**
 * 报道配置
 */
export default class ReportConfig {
  /**
   * 方案id
   */
  private schemeId: string = undefined
  /**
   * 通用配置
   */
  allConfig: ReportConfigDto = new ReportConfigDto()

  /**
   * 针对期别配置
   */
  periodInfoList: Array<PeriodConfig> = new Array<PeriodConfig>()

  /**
   * 是否开启报到
   */
  isOpenReportConfig: boolean = undefined

  /**
   * @param schemeId 方案id
   */
  constructor(schemeId?: string) {
    if (schemeId) {
      this.schemeId = schemeId
    }
  }

  /**
   * 获取报道配置
   */
  async getReportConfig() {
    MockUtil(this.allConfig)
    this.isOpenReportConfig = true
  }

  /**
   * 分页方案下期别的报到配置
   * @param page
   */
  async pageSchemeReportIssueConfig(page: Page) {
    const res = await MsSchemeLearningQuery.pageIssueStudyConfigInServicer({ page, schemeId: this.schemeId })
    if (res?.data?.currentPageData?.length) {
      this.periodInfoList = res.data.currentPageData.map((item: IssueStudyConfigResponse) => {
        return PeriodConfig.from(item)
      })
    } else {
      this.periodInfoList = new Array<PeriodConfig>()
    }

    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize

    return
  }

  /**
   * 保存报道配置
   */
  async saveReportConfig() {
    // todo
  }
}
