/*
 * @Description: 查询资讯列表参数
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-10 08:41:53
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-27 10:26:03
 */
import {
  NewsSimpleQueryRequest,
  TrainingChannelNewsSimpleQueryRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { NewsStatusEnum } from '../enum/NewsStatusEnum'
export default class NewsListParamVo {
  // 标题
  title?: string
  // 资讯分类
  categoryType?: Array<string>
  // 是否弹窗
  isPopup?: boolean
  // 资讯状态
  status?: NewsStatusEnum
  // 地区路径
  areaCodeList?: Array<Array<string>>
  /*
   * 所属专题名称
   * */
  belongSpecialName = ''

  static to(newsListParamVo: NewsListParamVo) {
    const newsSimpleQueryRequest = new NewsSimpleQueryRequest()
    newsSimpleQueryRequest.title = newsListParamVo.title
    newsSimpleQueryRequest.necId = newsListParamVo.categoryType?.length
      ? newsListParamVo.categoryType[newsListParamVo.categoryType.length - 1]
      : undefined
    newsSimpleQueryRequest.popUpsStatus = newsListParamVo.isPopup == undefined ? -1 : newsListParamVo.isPopup ? 1 : 0
    newsSimpleQueryRequest.status =
      newsListParamVo.status === 0 || newsListParamVo.status ? newsListParamVo.status : undefined
    newsSimpleQueryRequest.areaCodePath =
      newsListParamVo?.areaCodeList && newsListParamVo?.areaCodeList.length
        ? '/' + newsListParamVo.areaCodeList.join('/')
        : undefined
    return newsSimpleQueryRequest
  }

  static toSpecial(newsListParamVo: NewsListParamVo) {
    const newsSimpleQueryRequest = new TrainingChannelNewsSimpleQueryRequest()
    newsSimpleQueryRequest.title = newsListParamVo.title
    newsSimpleQueryRequest.necId = newsListParamVo.categoryType?.length
      ? newsListParamVo.categoryType[newsListParamVo.categoryType.length - 1]
      : undefined
    newsSimpleQueryRequest.popUpsStatus = newsListParamVo.isPopup == undefined ? -1 : newsListParamVo.isPopup ? 1 : 0
    newsSimpleQueryRequest.status =
      newsListParamVo.status === 0 || newsListParamVo.status ? newsListParamVo.status : undefined
    newsSimpleQueryRequest.areaCodePath =
      newsListParamVo?.areaCodeList && newsListParamVo?.areaCodeList.length
        ? '/' + newsListParamVo.areaCodeList.join('/')
        : undefined
    return newsSimpleQueryRequest
  }
}
