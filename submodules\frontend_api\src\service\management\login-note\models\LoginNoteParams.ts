import PlatLoginLog, { LoginLogRequest, LoginLogResponse } from '@api/platform-gateway/platform-login-log-v1'
import { DateScopeRequest } from '@api/ms-gateway/ms-data-export-front-gateway-DataExportBackstage'
class LoginNoteParams {
  /**
   * 登录IP地址
   */
  ip = ''
  /**
   * 用户名
   */
  userName = ''
  /**
   * 日志时间
   */
  dateRange: string[] = this.getDefaultDateRange()

  /*
   * 日志类型
   */
  type = ''

  /**
   * 获取默认日期范围（当前日期到三个月前的日期）
   */
  private getDefaultDateRange(): string[] {
    const today = new Date()
    const threeMonthsAgo = new Date(today)

    // 安全计算三个月前的日期（处理跨年情况）
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3)

    // 处理日期调整后可能出现的无效日期（如3月31日减3个月可能变成12月31日，但2月没有31日）
    if (threeMonthsAgo.getDate() !== today.getDate()) {
      threeMonthsAgo.setDate(0) // 设置为上个月的最后一天
    }

    const formatDate = (date: Date): string => {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    }

    return [formatDate(threeMonthsAgo), formatDate(today)]
  }
  /**
   * 模型转换
   */
  to(): LoginLogRequest {
    const to = new LoginLogRequest()
    if (this.ip) {
      to.ipList = [this.ip]
    }
    to.userName = this.userName
    to.eventTime = new DateScopeRequest()
    if (this.dateRange && this.dateRange.length) {
      // 修改点：只在日期非空时添加时间后缀
      to.eventTime.begin = this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : ''
      to.eventTime.end = this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : ''
    }
    to.eventType = this.type === 'loginIn' ? 'SSOLoginEvent' : 'SSOLogoutEvent'
    return to
  }
}
export default LoginNoteParams
