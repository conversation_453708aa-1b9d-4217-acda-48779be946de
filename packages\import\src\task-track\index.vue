<template>
  <el-main
    v-if="$hasPermission('query,queryZt')"
    desc="query:查询,queryZt:查询-专题管理员"
    actions="query: queryPageImportResult,intelligenceConfig,@BizLearningSchemeSelect
    #queryZt: queryPageImportResultByThemeManager,intelligenceConfigk,@BizLearningSchemeZtSelect"
  >
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <hb-search-wrapper :model="pageImportResultParam" @reset="resetCondition">
          <!-- <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6"> -->
          <el-form-item label="培训方案" v-if="!isZtglyLogin">
            <biz-learning-scheme-select v-model="hasSelectSchemeMode"></biz-learning-scheme-select>
          </el-form-item>
          <el-form-item label="培训方案" v-if="isZtglyLogin">
            <biz-learning-scheme-zt-select v-model="hasSelectSchemeMode"></biz-learning-scheme-zt-select>
          </el-form-item>
          <!--          已选择具体的培训方案把方案的commodityId传进去，且培训形式为面授、面网授时，显示此项。-->
          <el-form-item
            label="期别名称"
            v-if="selectedTrainingMode == TrainingModeEnum.offline || selectedTrainingMode == TrainingModeEnum.mixed"
          >
            <biz-select-issue-valname
              :commodityId="selectedCommodityId"
              v-model="pageImportResultParam.issueName"
            ></biz-select-issue-valname>
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="pageImportResultParam.name" clearable placeholder="请输入学员姓名" />
          </el-form-item>

          <el-form-item label="证件号">
            <el-input v-model="pageImportResultParam.idCard" clearable placeholder="请输入学员证件号" />
          </el-form-item>

          <el-form-item label="手机号">
            <el-input v-model="pageImportResultParam.phone" clearable placeholder="请输入手机号" />
          </el-form-item>

          <el-form-item label="导入任务">
            <template v-if="$hasPermission('importTask')" desc="导入任务" actions="@importTask">
              <import-task
                @getImport="getImport"
                ref="getImportRef"
                :clearImportDialog="clearImportDialog"
              ></import-task>
            </template>
          </el-form-item>
          <!-- <el-form-item label="打印状态">
              <el-select v-model="certifyParam.status" clearable placeholder="请选择">
                <el-option :value="false" label="未打印"></el-option>
                <el-option :value="true" label="已打印"></el-option>
              </el-select>
            </el-form-item> -->
          <el-form-item label="开通状态">
            <el-select
              v-model="pageImportResultParam.orderState"
              clearable
              @clear="pageImportResultParam.orderState = undefined"
              filterable
              placeholder="请选择"
            >
              <el-option label="开通失败" :value="3">开通失败</el-option>
              <el-option label="已开通" :value="2">已开通</el-option>
              <el-option label="开通中" :value="1">开通中</el-option>
              <!-- <el-option label="未开通" :value="0">未开通</el-option> -->
            </el-select>
          </el-form-item>

          <el-form-item label="导入时间">
            <double-date-picker
              :begin-create-time.sync="pageImportResultParam.startTime"
              :end-create-time.sync="pageImportResultParam.endTime"
              begin-time-placeholder="请选择导入开始时间"
              end-time-placeholder="请选择导入结束时间"
            ></double-date-picker>
          </el-form-item>
          <template slot="actions">
            <!-- </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr"> -->
            <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
          </template>
        </hb-search-wrapper>

        <!-- </el-col>
          </el-form>
        </el-row> -->
        <!--表格-->
        <el-table stripe ref="table" :data="pageData" v-loading="query.loading" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="学员帐号信息" min-width="240" fixed="left">
            <template slot-scope="scope">
              <p>姓名：{{ scope.row.name || '-' }}</p>
              <p>证件号：{{ scope.row.idCard || '-' }}</p>
              <!-- <p>手机号：{{ scope.row.phone || '-' }}</p> -->
            </template>
          </el-table-column>
          <el-table-column label="培训方案名称" min-width="300">
            <template slot-scope="scope">
              <p>{{ scope.row.trainingSchemeName || '-' }}</p>
              <p v-if="scope.row.issueName">
                <el-tag type="info" size="mini">培训期别：{{ scope.row.issueName }}</el-tag>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="订单号" min-width="220">
            <template slot-scope="scope">{{ scope.row.orderNo || '-' }}</template>
          </el-table-column>

          <el-table-column label="班级开通状态" min-width="120">
            <template slot-scope="scope">
              <div v-if="scope.row.orderState == 0">
                <el-badge is-dot type="primary" class="badge-status">等待开通</el-badge>
              </div>
              <div v-else-if="scope.row.orderState == 1">
                <el-badge is-dot type="primary" class="badge-status">开通中</el-badge>
              </div>
              <div v-else-if="scope.row.orderState == 2">
                <el-badge is-dot type="success" class="badge-status">已开通</el-badge>
              </div>
              <!-- 开通失败hover弹窗 -->
              <div v-else-if="scope.row.orderState == 3">
                <el-popover
                  placement="top-start"
                  title="失败原因："
                  width="200"
                  trigger="hover"
                  :content="scope.row.errorMessage"
                >
                  <el-badge slot="reference" is-dot type="danger" class="badge-status">开通失败</el-badge>
                </el-popover>
              </div>
              <div v-else>
                <template>-</template>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="错误日志" min-width="160">
            <template slot-scope="scope">
              <div>{{ scope.row.errorMessage || '-' }}</div>
            </template>
          </el-table-column> -->
          <el-table-column label="创建时间" min-width="180">
            <template slot-scope="scope">{{ scope.row.startTime || '-' }}</template>
          </el-table-column>
          <el-table-column min-width="240" align="center">
            <template slot="header"
              >是否更新密码/基础信息
              <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip is-small">
                <span class="el-icon-warning f-co f-ml5 f-f16"></span>
                <div slot="content">
                  仅针对平台已存在的用户去标记，本次的导入是否<br />更新密码、是否更新基础信息。如果是新用户则对<br />应的说明项显示
                  “-”。
                </div>
              </el-tooltip>
            </template>
            <template slot-scope="scope">{{ scope.row.isRejuvenation || '-' }}</template>
          </el-table-column>

          <el-table-column label="完成时间" min-width="180">
            <template slot-scope="scope">{{ scope.row.endTime || '-' }}</template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import AsyncTaskModule from '@api/service/management/async-task/AsyncTaskModule'
  import QueryImportResult from '@api/service/management/async-task/query/QueryImportResult'
  import PageImportResultParam from '@api/service/management/async-task/query/vo/PageImportResultParam'
  import PageImportResultResponse from '@api/service/management/async-task/query/vo/PageImportResultResponse'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryImportTaskVo from '@api/service/management/resource/question/query/vo/QueryImportTaskVo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { Query, UiPage } from '@hbfe/common'
  import BizLearningSchemeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-learning-scheme-select.vue'
  import BizLearningSchemeZtSelect from '@hbfe/jxjy-admin-components/src/biz/biz-learning-scheme-zt-select.vue'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import importTask from '@hbfe/jxjy-admin-import/src/task-track/import-task.vue'
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import Context from '@api/service/common/context/Context'
  import { ElTable } from 'element-ui/types/table'

  @Component({
    components: {
      DoubleDatePicker,
      importTask,
      BizLearningSchemeSelect,
      BizLearningSchemeZtSelect
    }
  })
  export default class extends Vue {
    @Ref('table') tableRef: ElTable
    @Ref('getImportRef') getImportRef: any
    page: UiPage
    query: Query = new Query()
    pageData: Array<PageImportResultResponse> = new Array<PageImportResultResponse>()
    queryImportResult: QueryImportResult = AsyncTaskModule.queryAsyncTaskFactory.queryImportResult
    pageImportResultParam: PageImportResultParam = new PageImportResultParam()
    hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
    @Watch('hasSelectSchemeMode', { deep: true })
    hasSelectSchemeModeChange(val: HasSelectSchemeMode) {
      if (val) {
        console.log(val[0], '选中的方案')
        this.selectedTrainingMode = val[0].trainingMode?.skuPropertyValueId
        this.selectedCommodityId = val[0].commodityId
      }
    }
    clearImportDialog = false
    selectedCommodityId = ''
    selectedTrainingMode = ''
    TrainingModeEnum = TrainingModeEnum
    constructor() {
      super()

      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    /**
     * 判断当前用户是否拥有专题管理员角色类型
     */
    get isZtglyLogin() {
      return QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    }

    async doSearch() {
      this.query.loading = true
      this.clearImportDialog = false
      if (this.getImportRef?.importName == '') {
        this.pageImportResultParam.importTask = ''
      }
      if (this.hasSelectSchemeMode.length) {
        this.pageImportResultParam.trainingSchemeName = this.hasSelectSchemeMode[0].scheme
      } else {
        this.pageImportResultParam.trainingSchemeName = null
      }
      try {
        this.pageImportResultParam.taskCategoryList = ['ADMIN_NORMAL_IMPORT']
        if (this.isZtglyLogin) {
          this.pageData = await this.queryPageImportResultByThemeManager()
        } else {
          this.pageData = await this.queryPageImportResult()
        }
        this.$nextTick(() => {
          this.tableRef.doLayout()
        })
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        this.query.loading = false
        console.log(this.pageData, 'pageData')
      }
    }

    // 专题管理员查询跟踪结果
    async queryPageImportResultByThemeManager() {
      const res = await this.queryImportResult.queryPageImportResultByThemeManager(
        this.page,
        this.pageImportResultParam
      )
      return res
    }

    // 网校管理员查询跟踪结果
    async queryPageImportResult() {
      const res = await this.queryImportResult.queryPageImportResult(this.page, this.pageImportResultParam)
      return res
    }

    async resetCondition() {
      this.page.pageNo = 1
      this.hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
      this.selectedTrainingMode = ''
      this.pageImportResultParam = new PageImportResultParam()
      this.getImportRef.importName = ''
      this.clearImportDialog = true
      await this.doSearch()
    }

    getImport(val: QueryImportTaskVo) {
      this.pageImportResultParam.importTask = val[0].taskId
    }

    async created() {
      await this.doSearch()
    }
  }
</script>
