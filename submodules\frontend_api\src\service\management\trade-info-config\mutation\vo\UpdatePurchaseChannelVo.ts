import {
  InvoiceConfigRequest,
  PurchaseChannelConfigCreateRequest,
  PurchaseChannelConfigUpdateRequest,
  TerminalCreateRequest,
  TerminalResponse
} from '@api/ms-gateway/ms-trade-configuration-v1'
import InvoiceConfigVo from './InvoiceConfigVo'
import TerminalVo from './TerminalVo'
import PurchaseChannelType, {
  PurchaseChannelEnum
} from '../../../../common/enums/trade-configuration/PurchaseChannelType'
import TermianlType, { TerminalTypeEnum } from '../../../../common/enums/trade-configuration/TerminalType'
import ReceiveAccountVo from './ReceiveAccountVo'
import PurchaseChannelTypeVo from './PurchaseChannelTypeVo'

class UpdatePurchaseChannelVo {
  /**
   * 购买渠道id
   */
  purchaseChannelId = ''
  /**
   * 渠道类型
   */
  channelType = 0
  /**
   * 渠道名称
   */
  channelName?: string = ''
  /**
   * 终端列表
   */
  terminalList?: Array<TerminalVo> = new Array<TerminalVo>()
  /**
   * 发票配置
   */
  invoiceConfig?: InvoiceConfigVo = new InvoiceConfigVo()

  constructor(purchaseChannelType?: number) {
    if (purchaseChannelType === PurchaseChannelEnum.UNIT_PURCHASE) {
      // 当报名方式为单位缴费时 只初始化web的终端列表
      const terminal = new TerminalVo()
      terminal.receiveAccountList = new Array<ReceiveAccountVo>()
      terminal.terminalCode = TerminalTypeEnum.WEB
      terminal.terminalName = TermianlType.map.get(TerminalTypeEnum.WEB)
      this.terminalList.push(terminal)
    } else {
      const list = TermianlType.list()
      for (let i = 0; i < list.length; i++) {
        const terminal = new TerminalVo()
        terminal.receiveAccountList = new Array<ReceiveAccountVo>()
        terminal.terminalCode = list[i].code
        terminal.terminalName = list[i].desc
        this.terminalList.push(terminal)
      }
    }
  }

  static from(res: PurchaseChannelTypeVo) {
    const updatePurchaseChannel = new UpdatePurchaseChannelVo(res.type)
    updatePurchaseChannel.purchaseChannelId = res.id
    updatePurchaseChannel.channelName = res.channelName
    updatePurchaseChannel.channelType = res.type
    updatePurchaseChannel.terminalList = updatePurchaseChannel.formatTerminalList(res.terminalList)
    updatePurchaseChannel.invoiceConfig = new InvoiceConfigVo()
    updatePurchaseChannel.invoiceConfig = res.invoiceConfig
      ? InvoiceConfigVo.from(res.invoiceConfig)
      : new InvoiceConfigVo()
    console.log('terminalList', updatePurchaseChannel.terminalList)
    return updatePurchaseChannel
  }

  static toChannelCreateDto(res: UpdatePurchaseChannelVo) {
    const purchaseChannelConfigCreateRequest = new PurchaseChannelConfigCreateRequest()
    purchaseChannelConfigCreateRequest.channelName = PurchaseChannelType.map.get(res.channelType)
    purchaseChannelConfigCreateRequest.channelType = res.channelType
    purchaseChannelConfigCreateRequest.terminalList = new Array<TerminalCreateRequest>()
    purchaseChannelConfigCreateRequest.terminalList = res.terminalList
      ?.filter(terminal => {
        return terminal.hasReceiveAccountList()
      })
      ?.map(TerminalVo.to)
    purchaseChannelConfigCreateRequest.invoiceConfig = null
    return purchaseChannelConfigCreateRequest
  }

  static toInvoiceCreateDto(res: UpdatePurchaseChannelVo) {
    const purchaseChannelConfigCreateRequest = new PurchaseChannelConfigCreateRequest()
    purchaseChannelConfigCreateRequest.channelName = PurchaseChannelType.map.get(res.channelType)
    purchaseChannelConfigCreateRequest.channelType = res.channelType
    purchaseChannelConfigCreateRequest.terminalList = null
    purchaseChannelConfigCreateRequest.invoiceConfig = new InvoiceConfigRequest()
    purchaseChannelConfigCreateRequest.invoiceConfig = InvoiceConfigVo.to(res.invoiceConfig)
    return purchaseChannelConfigCreateRequest
  }

  static toChannelUpdateDto(res: UpdatePurchaseChannelVo) {
    const purchaseChannelConfigUpdateRequest = new PurchaseChannelConfigUpdateRequest()
    purchaseChannelConfigUpdateRequest.purchaseChannelId = res.purchaseChannelId
    purchaseChannelConfigUpdateRequest.terminalList = new Array<TerminalCreateRequest>()
    purchaseChannelConfigUpdateRequest.terminalList = res.terminalList?.map(TerminalVo.to)
    purchaseChannelConfigUpdateRequest.invoiceConfig = null
    return purchaseChannelConfigUpdateRequest
  }

  static toInvoiceUpdateDto(res: UpdatePurchaseChannelVo) {
    const purchaseChannelConfigUpdateRequest = new PurchaseChannelConfigUpdateRequest()
    purchaseChannelConfigUpdateRequest.purchaseChannelId = res.purchaseChannelId
    purchaseChannelConfigUpdateRequest.terminalList = null
    purchaseChannelConfigUpdateRequest.invoiceConfig = new InvoiceConfigRequest()
    purchaseChannelConfigUpdateRequest.invoiceConfig = InvoiceConfigVo.to(res.invoiceConfig)
    return purchaseChannelConfigUpdateRequest
  }

  formatTerminalList(remoteList: Array<TerminalResponse>): Array<TerminalVo> {
    if (remoteList.length) {
      return this.terminalList
    }
    this.terminalList?.map(local => {
      remoteList.forEach(remote => {
        if (local.terminalCode === remote.terminalCode) {
          return (local = TerminalVo.from(remote))
        }
      })
    })
    return this.terminalList
  }
}
export default UpdatePurchaseChannelVo
