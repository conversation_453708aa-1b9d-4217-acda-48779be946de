import CourseInSchemeResult from '@api/service/management/resource/course/query/vo/CourseInSchemeResult'
import QuerySchemePackageCourseListParams from '@api/service/customer/course/query/vo/QuerySchemePackageCourseListParams'
import ResourceModule from '@api/service/management/resource/ResourceModule'
import Classification from '@api/service/management/train-class/mutation/vo/Classification'
import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'

/**
 * @description 培训方案详情工具类
 */
class SchemeDetailUtils {
  /**
   * 查询课程大纲节点下课程
   * @param {string} outlineId - 课程大纲节点Id
   * @param {string} schemeId - 培训方案Id
   */
  static async getOutlineCourseList(outlineId: string, schemeId: string): Promise<CourseInSchemeResult> {
    if (!outlineId) return
    const params: QuerySchemePackageCourseListParams = new QuerySchemePackageCourseListParams()
    params.schemeId = schemeId
    params.outlineIdList = [outlineId]
    const queryCourseListRemote = new QueryCourse()
    return await queryCourseListRemote.queryCourseListInSchemeByByPackage(params)
  }

  /**
   * 递归生成课程大纲节点parentId
   */
  static recursionSetOutlineParentId(tree: Classification[], parentId?: string) {
    if (!CreateSchemeUtils.isWeightyArray(tree)) return
    tree.map((el: Classification) => {
      el.parentId = parentId ? parentId : undefined
      el.courseList = []
      if (CreateSchemeUtils.isWeightyArray(el.childOutlines)) {
        this.recursionSetOutlineParentId(el.childOutlines, el.id)
      }
    })
  }

  /**
   * 判断是否是叶子节点
   * @param ele 节点信息
   */
  static isLeaf(ele: Classification) {
    return !ele.childOutlines || !ele.childOutlines.length
  }
}

export default SchemeDetailUtils
