<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">课程管理</el-breadcrumb-item>
      <el-breadcrumb-item>课程详情</el-breadcrumb-item>
    </el-breadcrumb>
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="课程详情" name="first">
        <div class="f-p15">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">基础信息</span>
            </div>
            <div class="f-p30">
              <el-row type="flex" justify="center" class="width-limit">
                <el-col :md="20" :lg="16" :xl="13">
                  <el-form ref="form" :model="form" label-width="120px" class="m-text-form is-column">
                    <el-form-item label="课程封面：">
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="course-pic"
                      >
                      </el-image>
                    </el-form-item>
                    <el-form-item label="课程名称：">课程名称课程名称课程名称</el-form-item>
                    <el-form-item label="课程分类：">课程分类课程分类课程分类课程分类</el-form-item>
                    <el-form-item label="课程简介：">
                      课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介课程简介
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">课程内容</span>
            </div>
            <div class="f-p30">
              <el-row type="flex" justify="center">
                <el-col :lg="16" :xl="13">
                  <p class="f-fb f-clear">
                    <i class="f-dot f-mr5"></i>
                    <span class="f-f15">课程目录</span>
                    <el-button type="primary" size="mini" class="f-fr">预览</el-button>
                  </p>
                  <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10 f-mb20">
                    <el-table-column label="章节1" min-width="300">
                      <template>
                        <i class="el-icon-video-play f-f20 f-c9"></i>
                        <span class="f-ml5">这里是文件名称文件名称文件名称这里是文件名称文件名称文件名称</span>
                      </template>
                    </el-table-column>
                    <el-table-column min-width="100" align="center">
                      <template>00:02:54</template>
                    </el-table-column>
                    <el-table-column width="100" align="center">
                      <template><el-checkbox v-model="checked" disabled>试听</el-checkbox></template>
                    </el-table-column>
                  </el-table>
                  <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10 f-mb20">
                    <el-table-column label="章节1" min-width="300">
                      <template>
                        <i class="el-icon-video-play f-f20 f-c9"></i>
                        <span class="f-ml5">这里是文件名称文件名称文件名称这里是文件名称文件名称文件名称</span>
                      </template>
                    </el-table-column>
                    <el-table-column min-width="100" align="center">
                      <template>00:02:54</template>
                    </el-table-column>
                    <el-table-column width="100" align="center">
                      <template><el-checkbox v-model="checked" disabled>试听</el-checkbox></template>
                    </el-table-column>
                  </el-table>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="课程评价" name="second">
        <div class="f-p15">
          <el-card shadow="never" class="m-card f-mb15">
            <div class="f-flex f-align-center">
              <span class="f-mr5">综合评价：</span>
              <el-rate v-model="rate" disabled show-score text-color="#ff9900" score-template="4.5" />
            </div>
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt20">
              <el-table-column label="评价人" min-width="80" fixed="left">
                <template>张一二</template>
              </el-table-column>
              <el-table-column label="课程内容评价" min-width="180" align="center" fixed="left">
                <template>
                  <el-rate v-model="rate" disabled show-score text-color="#ff9900" score-template="4.5" />
                </template>
              </el-table-column>
              <el-table-column label="教师授课评价" min-width="180" align="center" fixed="left">
                <template>
                  <el-rate v-model="rate" disabled show-score text-color="#ff9900" score-template="4.5" />
                </template>
              </el-table-column>
              <el-table-column label="评价内容" show-overflow-tooltip min-width="500">
                <template>
                  评价内容评价内容评价内容评价内容评价内容评价内容评价内容评价内容评价内容评价内容评价
                </template>
              </el-table-column>
              <el-table-column label="评价时间" min-width="180" align="center">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        rate: '4.5',
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        checked: true,
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
