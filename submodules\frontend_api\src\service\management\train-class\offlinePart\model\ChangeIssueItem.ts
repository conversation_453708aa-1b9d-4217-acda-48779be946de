import { IssueCommoditySkuBackStageResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

export default class ChangeIssueItem {
  /**
   * 商品id
   */
  commoditySkuId = ''
  /**
   * 期别id
   */
  issueId = ''

  /**
   * 期别名称
   */
  issueName = ''

  /**
   * 期别编号
   */
  issueNo = ''

  /**
   * 报名开始时间
   */
  registerStartTime = ''

  /**
   * 报名结束时间
   */
  registerEndTime = ''

  /**
   * 可报名人数
   */
  canRegisterNum: number = undefined

  static from(dto: IssueCommoditySkuBackStageResponse) {
    const vo = new ChangeIssueItem()
    const { commoditySkuInfo, issueResourceInfo, remainingRegisterNumber } = dto
    if (commoditySkuInfo) {
      vo.commoditySkuId = commoditySkuInfo.commoditySkuId
    }
    vo.canRegisterNum = remainingRegisterNumber
    if (issueResourceInfo) {
      vo.issueId = issueResourceInfo.issueId
      vo.issueNo = issueResourceInfo.issueNum
      vo.issueName = issueResourceInfo.issueName
      vo.registerEndTime = issueResourceInfo.issueSignUpEndDate
      vo.registerStartTime = issueResourceInfo.issueSignUpBeginDate
    }

    return vo
  }
}
