import { PasswordModelEnum } from '@api/service/management/intelligence-learning/enum/PasswordModelEnum'
import { EffectiveRangeEnum } from '@api/service/management/intelligence-learning/enum/EffectiveRangeEnum'
import { ImportOpenImportInfoForVerifyRequest } from '@api/ms-gateway/ms-importopen-v1'

export default class ImportOpenLearningParam {
  /**
   * 任务类型
   * @private
   */
  static category = 'ADMIN_NORMAL_IMPORT_AND_LEARNING'
  /**
   * 文件路径
   */
  filePath = ''

  /**
   * 文件名称
   */
  fileName = ''

  /**
   * 终端类型
   Web端：Web
   */
  terminalCode = 'Web'

  /**
   * 密码类型
   */
  passwordModel: PasswordModelEnum = null

  /**
   * 自定义密码
   * @description 密码类型为自定义密码时，密码为自定义密码
   */
  password = ''

  /**
   * 密码生效范围
   */
  passwordEffectiveRange: EffectiveRangeEnum = null

  /**
   * 是否更新已注册的学员信息
   */
  updateBasicInfo: boolean = null

  /**
   * 是否按照指定专题导入
   */
  importBySpecial: boolean = null

  /**
   * 专题id
   */
  specialId: string = null

  static to(vo: ImportOpenLearningParam) {
    const dto = new ImportOpenImportInfoForVerifyRequest()
    dto.category = ImportOpenLearningParam.category
    dto.terminalCode = vo.terminalCode
    dto.fileName = vo.fileName
    dto.filePath = vo.filePath
    switch (vo.passwordModel) {
      case PasswordModelEnum.password_model_zero:
      case PasswordModelEnum.password_model_abc:
        dto.passwordModel = 1
        dto.password = vo.passwordModel
        break
      case PasswordModelEnum.password_model_custom:
        dto.passwordModel = 1
        dto.password = vo.password
        break
      case PasswordModelEnum.password_model_id_card:
        dto.passwordModel = 2
        dto.password = ''
    }
    dto.passwordEffectiveRange = vo.passwordEffectiveRange
    dto.updateBasicInfo = vo.updateBasicInfo
    if (vo.importBySpecial) {
      dto.saleChannel = 2
      dto.saleChannelId = vo.specialId
    } else {
      dto.saleChannel = 0
      dto.saleChannelId = null
    }
    return dto
  }
}
