import CreateOrderParamsCommon from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/CreateOrderParams'
import { CreateHYWOrderRequest } from '@api/diff-gateway/platform-jxjypxtypt-xmlg-school'

export default class CreateOrderParams extends CreateOrderParamsCommon {
  /**
   * 身份证
   */
  idCard?: string

  /**
   * 渠道商编号
   */
  channelVendorId?: string

  /**
   * 加密串
   */
  key?: string

  toGeneralCreateOrderRequestDiff() {
    const createOrderParams = new CreateHYWOrderRequest()
    const data = super.toGeneralCreateOrderRequest()
    Object.assign(createOrderParams, data)
    return createOrderParams
  }
}
