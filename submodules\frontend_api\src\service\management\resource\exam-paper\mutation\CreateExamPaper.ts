import { ResponseStatus } from '@hbfe/common'
import ExamPaperGateway from '@api/ms-gateway/ms-examextraction-v1'
import CreateExamPaperVo from '@api/service/management/resource/exam-paper/mutation/vo/create/CreateExamPaperVo'
import ExamPublishPattern from '@api/service/management/resource/exam-paper/mutation/vo/common/ExamPublishPattern'
import AutomaticExamPaperVo from '@api/service/management/resource/exam-paper/mutation/vo/common/AutomaticExamPaperVo'
class CreateExamPaper<T extends ExamPublishPattern = AutomaticExamPaperVo> {
  createExamPaperParams: CreateExamPaperVo<T> = undefined

  /**
   * @description: 初始化参数
   * @param {object} publishPatternConstructor: T的构造函数（把类传进来
   * @return {void}
   */
  constructor(publishPatternConstructor: { new (): T }) {
    // 创建试卷Vo传入 （出卷模式属性）
    this.createExamPaperParams = new CreateExamPaperVo(publishPatternConstructor)
  }

  /**
   * @description: 创建试卷
   * @param {*}
   * @return {*}
   */
  async doCreateExamPaper(): Promise<ResponseStatus> {
    const createExamPaperDto = this.createExamPaperParams.toDto()
    const { status } = await ExamPaperGateway.createPaperPublishConfigure(createExamPaperDto)
    return status
  }

  // 根据试卷类型创建对应模型
  private createExamPaperVoByType() {
    // todo
  }
}

export default CreateExamPaper
