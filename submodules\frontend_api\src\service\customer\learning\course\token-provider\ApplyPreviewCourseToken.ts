import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import { ResponseStatus } from '@hbfe/common'
import MsCoursePlayResourceV1 from '@api/ms-gateway/ms-course-play-resource-v1'

/**
 * 申请课程试听 token
 */
class ApplyPreviewCourseToken extends AbstractApplyToken {
  /**
   * 课程学习播放凭证/课程试听凭证/课程预览凭证
   */
  private readonly courseId?: string

  constructor(courseId: string) {
    super()
    this.courseId = courseId
  }

  async apply(): Promise<ResponseStatus> {
    const { status, data } = await MsCoursePlayResourceV1.applyCoursePreview(this.courseId)
    if (status.isSuccess()) {
      if (data.applyResult.code !== '200') {
        return new ResponseStatus(200, '')
      }
    }
    this.token = data.token
  }
}

export default ApplyPreviewCourseToken
