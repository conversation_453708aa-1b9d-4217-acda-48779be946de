import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-examquestion-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-examquestion-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum FillAnswerType {
  disarray = 'disarray',
  sequence = 'sequence',
  sequenceRelate = 'sequenceRelate'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 创建试题请求体
<AUTHOR>
@date 2025/4/30
 */
export class CreateNoCheckQuestionRequest {
  /**
   * 试题内容
   */
  createQuestionRequest?: CreateQuestionRequest
  /**
   * 标签
   */
  tagCode?: Array<string>
}

/**
 * <AUTHOR>
@date 2025/4/30
 */
export class UpdateNoCheckQuestionRequest {
  /**
   * 试题内容
   */
  updateQuestionRequest?: UpdateQuestionRequest
  /**
   * 标签
   */
  tagCode?: Array<string>
}

/**
 * 创建题库请求
<AUTHOR> create 2021/7/1 14:06
 */
export class CreateLibraryRequest {
  /**
   * 题库id
   */
  id?: string
  /**
   * 名称【必填】
   */
  name: string
  /**
   * 描述
   */
  description?: string
  /**
   * 父试题库id 如果没有父题库为-1【必填】
   */
  parentId: string
  /**
   * 是否可用
   */
  enabled: boolean
}

/**
 * @Author: chenzeyu
@CreateTime: 2024-07-29  15:28
@Description: 创建调查问卷题库请求
 */
export class CreateQuestionnaireLibraryRequest {
  /**
   * 题库id
   */
  id?: string
  /**
   * 名称【必填】
   */
  name: string
  /**
   * 描述
   */
  description?: string
  /**
   * 父试题库id 如果没有父题库为QUESTIONNAIRE_LIBRARY
   */
  parentId: string
  /**
   * 是否可用
   */
  enabled: boolean
}

/**
 * 创建题库请求
<AUTHOR> 2022年12月21日20:28:05
 */
export class SystemCreateLibraryRequest {
  /**
   * 题库id
   */
  id?: string
  /**
   * 名称【必填】
   */
  name: string
  /**
   * 描述
   */
  description?: string
  /**
   * 父试题库id 如果没有父题库为-1【必填】
   */
  parentId: string
  /**
   * 是否可用
   */
  enabled: boolean
}

/**
 * 修改题库命令
<AUTHOR> create 2021/7/1 14:06
 */
export class UpdateLibraryRequest {
  /**
   * 题库ID【必填】
   */
  id: string
  /**
   * 名称【必填】
   */
  name: string
  /**
   * 描述
   */
  description?: string
  /**
   * 父试题库id 如果没有父题库为-1【必填】，问卷题库如果没有父题库为QUESTIONNAIRE_LIBRARY
   */
  parentId: string
}

/**
 * 批量导入试题查询请求
<AUTHOR>
 */
export class BatchImportQuestionQueryRequest {
  /**
   * 任务名称
   */
  taskName?: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState?: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult?: number
  /**
   * 执行时间（起始）
   */
  executeStartTime?: string
  /**
   * 执行时间（终止）
   */
  executeEndTime?: string
}

/**
 * 选择题答案选项实体
<AUTHOR>
 */
export class ChooseAnswerOptionRequest {
  /**
   * 答案ID
   */
  id: string
  /**
   * 答案内容
   */
  content: string
  /**
   * 选项建议分数
   */
  suggestionScore?: number
  /**
   * 是否允许填空
   */
  enableFillContent?: boolean
  /**
   * 填空是否必填
   */
  mustFillContent?: boolean
}

/**
 * <AUTHOR> create 2021/6/29 14:03
 */
export class CreateQuestionRequest {
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 批量导出导入的试题请求
<AUTHOR>
 */
export class ExportQuestionDataRequest {
  /**
   * 主任务id
   */
  mainTaskId: string
}

/**
 * 导入试题请求
<AUTHOR>
 */
export class ImportQuestionsRequest {
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 文件名
   */
  fileName?: string
}

/**
 * 初始化试题请求
 */
export class InitQuestionRequest {
  fixdType: number
}

/**
 * <AUTHOR> create 2021/6/29 14:03
 */
export class UpdateQuestionRequest {
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * <AUTHOR> create 2021/6/28 14:13
 */
export class CreateAskQuestionRequest implements CreateQuestionRequest {
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * <AUTHOR> create 2021/6/28 14:13
 */
export class UpdateAskQuestionRequest implements UpdateQuestionRequest {
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * <AUTHOR> create 2021/6/28 14:43
 */
export class ChildQuestionCreateInfoRequest {
  /**
   * 子题序号
   */
  no: number
  /**
   * 试题内容
   */
  question?: CreateQuestionRequest
}

/**
 * <AUTHOR> create 2021/6/28 14:43
 */
export class ChildQuestionUpdateInfoRequest {
  /**
   * 子题序号
   */
  no: number
  /**
   * 试题内容
   */
  question?: UpdateQuestionRequest
  /**
   * 新增试题
   */
  newQuestion?: CreateQuestionRequest
}

/**
 * <AUTHOR> create 2021/6/28 14:43
 */
export class CreateFatherQuestionRequest implements CreateQuestionRequest {
  /**
   * 子题集合
   */
  childQuestions: Array<ChildQuestionCreateInfoRequest>
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * <AUTHOR> create 2021/6/28 14:43
 */
export class UpdateFatherQuestionRequest implements UpdateQuestionRequest {
  /**
   * 子题集合
   */
  childQuestions: Array<ChildQuestionUpdateInfoRequest>
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * 填空题创建命令
<AUTHOR> create 2021/6/28 14:09
 */
export class CreateFillQuestionRequest implements CreateQuestionRequest {
  /**
   * 填空数
   */
  fillCount: number
  /**
   * 正确答案
   */
  correctAnswer?: FillAnswerRequest
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 散乱无序填空题答案实体
<AUTHOR>
 */
export class DisarrayFillAnswerRequest implements FillAnswerRequest {
  /**
   * 正确答案集合
   */
  correctAnswers: Array<string>
  /**
   * 答案类型
   */
  type: FillAnswerType
}

/**
 * 填空题答案基类
<AUTHOR>
 */
export class FillAnswerRequest {
  /**
   * 答案类型
   */
  type: FillAnswerType
}

/**
 * <AUTHOR> create 2021/6/29 15:41
 */
export class FillCorrectAnswers {
  /**
   * 空格位置
   */
  blankNo: number
  /**
   * 答案备选项
   */
  answers?: Array<string>
}

/**
 * 按序填空题答案
<AUTHOR>
 */
export class SequenceFillAnswerRequest implements FillAnswerRequest {
  correctAnswers: Array<FillCorrectAnswers>
  /**
   * 答案类型
   */
  type: FillAnswerType
}

/**
 * 按序关联填空题答案实体
<AUTHOR>
 */
export class SequenceRateFillAnswerRequest implements FillAnswerRequest {
  /**
   * 正确答案集合
   */
  correctAnswers?: Array<SequenceFillAnswerRequest>
  /**
   * 答案类型
   */
  type: FillAnswerType
}

/**
 * 填空题创建命令
<AUTHOR> create 2021/6/28 14:09
 */
export class UpdateFillQuestionRequest implements UpdateQuestionRequest {
  /**
   * 填空数
   */
  fillCount: number
  /**
   * 正确答案
   */
  correctAnswer?: FillAnswerRequest
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * 多选题创建命令
<AUTHOR> create 2021/6/28 14:07
 */
export class CreateMultipleQuestionRequest implements CreateQuestionRequest {
  /**
   * 可选答案列表【必填】
   */
  answerOptions?: Array<ChooseAnswerOptionRequest>
  /**
   * 正确答案ID集合【必填】
   */
  correctAnswerIds: Array<string>
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 多选题创建命令
<AUTHOR> create 2021/6/28 14:07
 */
export class UpdateMultipleQuestionRequest implements UpdateQuestionRequest {
  /**
   * 可选答案列表【必填】
   */
  answerOptions?: Array<ChooseAnswerOptionRequest>
  /**
   * 正确答案ID集合【必填】
   */
  correctAnswerIds: Array<string>
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * 判断题创建命令
<AUTHOR> create 2021/6/28 14:05
 */
export class CreateOpinionQuestionRequest implements CreateQuestionRequest {
  /**
   * 正确答案【必填】
   */
  correctAnswer: boolean
  /**
   * 正确文本【必填】
   */
  correctAnswerText?: string
  /**
   * 不正确文本【必填】
   */
  incorrectAnswerText?: string
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 判断题创建命令
<AUTHOR> create 2021/6/28 14:05
 */
export class UpdateOpinionQuestionRequest implements UpdateQuestionRequest {
  /**
   * 正确答案【必填】
   */
  correctAnswer: boolean
  /**
   * 正确文本【必填】
   */
  correctAnswerText: string
  /**
   * 不正确文本【必填】
   */
  incorrectAnswerText: string
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * 单选题创建命令
<AUTHOR> create 2021/6/28 9:39
 */
export class CreateRadioQuestionRequest implements CreateQuestionRequest {
  /**
   * 可选答案列表【必填】
   */
  answerOptions?: Array<ChooseAnswerOptionRequest>
  /**
   * 正确答案ID【必填】
   */
  correctAnswerId?: string
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 单选题创建命令
<AUTHOR> create 2021/6/28 9:39
 */
export class UpdateRadioQuestionRequest implements UpdateQuestionRequest {
  /**
   * 可选答案列表
   */
  answerOptions?: Array<ChooseAnswerOptionRequest>
  /**
   * 正确答案ID
   */
  correctAnswerId?: string
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * @Author: chenzeyu
@CreateTime: 2024-07-29  16:12
@Description: 量表题创建请求
 */
export class CreateScaleQuestionRequest implements CreateQuestionRequest {
  /**
   * 量表类型
@see com.fjhb.domain.exam.api.question.consts.ScaleTypes
   */
  scaleType: number
  /**
   * 程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  startDegree?: string
  /**
   * 程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  endDegree?: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * @Author: chenzeyu
@CreateTime: 2024-07-30  15:40
@Description: 量表题修改请求
 */
export class UpdateScaleQuestionRequest implements UpdateQuestionRequest {
  /**
   * 量表类型
@see com.fjhb.domain.exam.api.question.consts.ScaleTypes
   */
  scaleType: number
  /**
   * 程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  startDegree?: string
  /**
   * 程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  endDegree?: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
  /**
   * 试题id
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID
   */
  libraryId: string
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
}

/**
 * 各状态及执行结果对应数量
<AUTHOR>
 */
export class EachStateCount {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

/**
 * 批量导入试题查询响应
<AUTHOR>
 */
export class FindBatchImportQuestionByPageResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 【必填】服务商id
   */
  servicerId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务分类
   */
  category: string
  /**
   * 所属批次单编号
   */
  batchNo: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 就绪时间
   */
  alreadyTime: string
  /**
   * 执行时间
   */
  executingTime: string
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount>
}

export class FindBatchImportQuestionByPageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<FindBatchImportQuestionByPageResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量导入试题分页查询
   * @param request 批量导入试题查询请求
   * @param page    分页信息
   * @return 批量导入试题分页查询响应
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findBatchImportQuestionByPage(
    params: { request?: BatchImportQuestionQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findBatchImportQuestionByPage,
    operation?: string
  ): Promise<Response<FindBatchImportQuestionByPageResponsePage>> {
    return commonRequestApi<FindBatchImportQuestionByPageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出导入试题数据
   * @param request 批量导出导入的试题请求
   * @return 文件路径
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchExportAllChooseQuestionData(
    request: ExportQuestionDataRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchExportAllChooseQuestionData,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出导入试题失败数据
   * @param request 批量导出导入的试题请求
   * @return 文件路径
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchExportFailChooseQuestionData(
    request: ExportQuestionDataRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchExportFailChooseQuestionData,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量导入试题
   * @param request 导入试题请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchImportChooseQuestions(
    request: ImportQuestionsRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchImportChooseQuestions,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建题库
   * @param request 创建题库请求
   * @return 题库id
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createLibrary(
    request: CreateLibraryRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createLibrary,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建试题
   * @param request 创建试题请求
   * @return 试题id
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createQuestion(
    request: CreateQuestionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createQuestion,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * @param request
   * @return java.lang.String
   * @title:创建调查问卷题库
   * @description:父试题库id如果没有，父题库id默认为QUESTIONNAIRE_LIBRARY
   * @author: chenzeyu
   * @date: 2024/7/29 15:39
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createQuestionnaireLibrary(
    request: CreateQuestionnaireLibraryRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createQuestionnaireLibrary,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建试题(有标签)
   * 且不进行重复性校验
   * @param request 创建试题请求
   * @return 试题id
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createTagQuestion(
    request: CreateNoCheckQuestionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createTagQuestion,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 停用试题
   * @param id 试题id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableQuestion(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disableQuestion,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用试题
   * @param id 试题id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableQuestion(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableQuestion,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async initQuestionData(
    request: InitQuestionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.initQuestionData,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 删除题库
   * @param id 题库id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeLibrary(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeLibrary,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除试题
   * @param id 试题id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeQuestion(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeQuestion,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除试题
   * @param id 试题id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeTagQuestion(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeTagQuestion,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * @创建题库
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async systemCreateLibrary(
    request: SystemCreateLibraryRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.systemCreateLibrary,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 修改题库
   * @param request 修改题库请求
   * @return 题库id
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateLibrary(
    request: UpdateLibraryRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateLibrary,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改试题
   * @param request 修改试题请求
   * @return 试题id
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateQuestion(
    request: UpdateQuestionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateQuestion,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改试题
   * @param request 修改试题请求
   * @return 试题id
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTagQuestion(
    request: UpdateNoCheckQuestionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTagQuestion,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
