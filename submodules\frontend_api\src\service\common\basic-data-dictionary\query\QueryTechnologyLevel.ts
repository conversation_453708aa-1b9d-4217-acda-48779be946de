/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-10-17 09:30:31
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-11-09 16:12:59
 * @Description:
 */
import Basicdata from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
export class TechnologyLevelVo {
  /**
   * id
   */
  id: string
  /**
   * 编码
   */
  code: number
  /**
   * 名称
   */
  showName: string
  /**
   * 是否可用（0：禁用 1：启用）
   */
  available: number
  /**
   * 排序
   */
  sort: number
}
/**
 * @description 查询技术等级
 */
class QueryTechnologyLevel {
  data: TechnologyLevelVo[] = []

  /**
   * 查询
   */
  async query() {
    if (!this.data.length) {
      const res = await Basicdata.listBusinessDataDictionaryInSubProject({
        businessDataDictionaryType: 'TECHNICAL_GRADE'
      })
      this.data = res.data.map(item => {
        const temp = new TechnologyLevelVo()
        temp.id = item.id
        temp.code = item.code
        temp.available = item.available
        temp.showName = item.name
        temp.sort = item.sort
        return temp
      })
    }

    return this.data
  }

  // 获取技术等级
  async queryTechnologyLevelById(id: string) {
    if (!this.data || !this.data.length) {
      await this.query()
    }
    let name = ''
    this.data.forEach(item => {
      if (id == item.id) {
        name = item.showName
      }
    })
    return name
  }
}

export default new QueryTechnologyLevel()
