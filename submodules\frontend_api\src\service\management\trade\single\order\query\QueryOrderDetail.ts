import tradeQueryGateway from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { Page, ResponseStatus } from '@hbfe/common'
import OrderDetailVo from '@api/service/management/trade/single/order/query/vo/UserOrderDetailVo'
import SchemeLearningQueryBackstage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

/**
 * 查询订单信息
 */

class QueryOrderDetail {
  // // 订单号
  orderNo = ''
  //
  // private _orderDetail = new OrderDetail()
  //
  orderDetail = new OrderDetailVo()

  async queryOrderDetailByOrderNo(): Promise<ResponseStatus> {
    const res = await tradeQueryGateway.getOrderInServicer(this.orderNo)
    if (res.status.isSuccess()) {
      const tmpItem = new OrderDetailVo()
      Object.assign(tmpItem, res.data)
      tmpItem.changeStatue()
      await tmpItem.addInvoice()
      await Promise.all([tmpItem.addRefundOrder(), tmpItem.getForceStatue(), tmpItem.addBuyer()])
      this.orderDetail = tmpItem
    }
    return res.status
  }
  /**
   * 判断是否已经同步
   * true为同步 false为同步失败或者未同步
   * @param studntId 用户ID
   * @param orderNo 订单号
   */
  async isSynchronize(studntId: string, orderNo: string) {
    const result = await SchemeLearningQueryBackstage.pageStudentSchemeLearningInServicer({
      page: {
        pageNo: 1,
        pageSize: 1
      },
      request: {
        student: {
          userIdList: [studntId]
        },
        learningRegister: {
          orderNoList: [orderNo]
        }
      }
    })
    return result.data.currentPageData[0]?.connectManageSystem.syncStatus === 1
  }

  /**
   * 查询分销商订单详情
   */
  async queryFxOrderDetailByOrderNo(): Promise<ResponseStatus> {
    const res = await tradeQueryGateway.getOrderInDistributor(this.orderNo)
    if (res.status.isSuccess()) {
      const tmpItem = new OrderDetailVo()
      Object.assign(tmpItem, res.data)
      tmpItem.changeStatue()
      await tmpItem.addInvoice()
      await tmpItem.addFxRefundOrder()
      await Promise.all([tmpItem.addFxRefundOrder(), tmpItem.getForceStatue(), tmpItem.addBuyer()])
      this.orderDetail = tmpItem
    }
    return res.status
  }
  /**
   * 判断是否已经同步（分销商）
   * true为同步 false为同步失败或者未同步
   * @param studntId 用户ID
   * @param orderNo 订单号
   */
  async isFxSynchronize(studntId: string, orderNo: string) {
    const result = await SchemeLearningQueryBackstage.pageStudentSchemeLearningInDistributor({
      page: {
        pageNo: 1,
        pageSize: 1
      },
      request: {
        student: {
          userIdList: [studntId]
        },
        learningRegister: {
          orderNoList: [orderNo]
        }
      }
    })
    return result.data.currentPageData[0]?.connectManageSystem.syncStatus === 1
  }
}

export default QueryOrderDetail
