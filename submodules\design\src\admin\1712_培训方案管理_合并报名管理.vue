<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">培训方案管理</el-breadcrumb-item>
      <el-breadcrumb-item>合并报名管理</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="150px" class="m-text-form is-column">
              <el-form-item label="方案类型：">培训班-选课规则</el-form-item>
              <el-form-item label="方案封面：">
                <el-image
                  src="/assets/images/web-default-banner.jpg"
                  :preview-src-list="['/assets/images/web-default-banner.jpg']"
                  class="course-pic"
                />
              </el-form-item>
              <el-form-item label="方案名称：">方案名称方案名称方案名称方案名称</el-form-item>
              <el-form-item label="年度：">2022</el-form-item>
              <el-form-item label="地区：">福建省</el-form-item>
              <el-form-item label="行业：">建设行业</el-form-item>
              <el-form-item label="科目类型：">专业科目</el-form-item>
              <el-form-item label="培训类别：">一级注册建造师</el-form-item>
              <el-form-item label="培训专业：">培训专业</el-form-item>
              <el-form-item label="成果是否同步：">同步</el-form-item>
              <el-form-item label="成果开始时间规则：">以首次开始学习时间为准</el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="m-tit bg-gray is-border-bottom f-align-center">
          <span class="tit-txt">主方案设置</span>
          <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
            <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
            <div slot="content">
              设置成功后，学员报名所选方案时自动与当前方案合并报名。合并报名的任意方案下架或关闭学员报名或不在报名时段内将导致学员无法合并报名。<br />
              当前方案是否单独开放学员报名仍然以本方案设置为准。<br />
              合并报名规则仅在主网站和专题学员门户生效，集体报名、导入开通、分销仍然分别报名。
            </div>
          </el-tooltip>
        </div>
        <div class="f-p20">
          <el-button type="primary" icon="el-icon-plus" class="f-mb15">新增方案</el-button>
          <!--通用空数据-->
          <div class="m-no-date">
            <img class="img" src="./assets/images/no-data-normal.png" alt="" />
            <div class="date-bd">
              <p class="f-f15 f-c9">暂无数据~</p>
            </div>
          </div>
          <el-table stripe :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="方案名称" min-width="240" fixed="left">
              <template>
                <div>课程名称课程名称课程名称课程名称</div>
                <div>
                  <el-tag type="danger" effect="dark" size="mini">已下架</el-tag>
                  <el-tag type="danger" effect="dark" size="mini">不开放学员报名</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="培训属性" min-width="220">
              <template>
                <p>某某行业</p>
                <p>属性1：属性值</p>
                <p>属性2：属性值</p>
                <p>属性3：属性值</p>
              </template>
            </el-table-column>
            <el-table-column label="报名学时" min-width="100">
              <template>50</template>
            </el-table-column>
            <el-table-column label="价格" min-width="100">
              <template>50.00</template>
            </el-table-column>
            <el-table-column label="学习起止时间" min-width="120">
              <template>长期有效</template>
            </el-table-column>
            <el-table-column label="报名起止时间" min-width="200">
              <template>
                <p>起始：2021-10-15 00:21:21</p>
                <p>结束：2021-10-15 00:21:21</p>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center" fixed="right">
              <template>
                <el-popconfirm
                  confirm-button-text="确认"
                  cancel-button-text="取消"
                  icon="el-icon-info"
                  icon-color="red"
                  title="移除主方案不影响已创建的订单。学员报名已移除的主方案时，当前方案将无法被合并报名。请确认是否移除？"
                >
                  <el-button type="text" size="mini" slot="reference">移除</el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header">
        <div class="m-tit bg-gray is-border-bottom f-align-center">
          <span class="tit-txt">当前方案下的合并报名方案</span>
          <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
            <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
            <div slot="content">
              学员报名本方案时，将自动合并报名以下培训方案。如需解除合并，请在需解除的培训方案中修改。<br />
              合并报名的任意方案下架或关闭学员报名或不在报名时段内将导致学员无法合并报名。<br />
              合并报名规则仅在主网站和专题学员门户生效，集体报名、导入开通、分销仍然分别报名。
            </div>
          </el-tooltip>
        </div>
        <div class="f-p20">
          <!--通用空数据-->
          <div class="m-no-date">
            <img class="img" src="./assets/images/no-data-normal.png" alt="" />
            <div class="date-bd">
              <p class="f-f15 f-c9">暂无数据~</p>
            </div>
          </div>
          <el-table stripe :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="方案名称" min-width="240" fixed="left">
              <template>
                <div>课程名称课程名称课程名称课程名称</div>
                <div>
                  <el-tag type="danger" effect="dark" size="mini">已下架</el-tag>
                  <el-tag type="danger" effect="dark" size="mini">不开放学员报名</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="培训属性" min-width="220">
              <template>
                <p>某某行业</p>
                <p>属性1：属性值</p>
                <p>属性2：属性值</p>
                <p>属性3：属性值</p>
              </template>
            </el-table-column>
            <el-table-column label="报名学时" min-width="100">
              <template>50</template>
            </el-table-column>
            <el-table-column label="价格" min-width="100">
              <template>50.00</template>
            </el-table-column>
            <el-table-column label="学习起止时间" min-width="120">
              <template>长期有效</template>
            </el-table-column>
            <el-table-column label="报名起止时间" min-width="200" align="center" fixed="right">
              <template>
                <p>起始：2021-10-15 00:21:21</p>
                <p>结束：2021-10-15 00:21:21</p>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
        </div>
      </el-card>
      <div class="m-btn-bar is-sticky-1 f-tc">
        <el-button>取消</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      const data = [
        {
          id: 1,
          label: '一级 1',
          children: [
            {
              id: 4,
              label: '二级 1-1'
            }
          ]
        }
      ]
      return {
        num: 1,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        input1: '1',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        tableData1: [
          {
            id: 1,
            field01: '这里是分类名称',
            hasChildren: true
          },
          {
            id: 2,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 3,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 4,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 5,
            field01: '分类名称1',
            hasChildren: true
          }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down'],
        data
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      },
      load(tree, treeNode, resolve) {
        setTimeout(() => {
          resolve([
            {
              id: 11,
              field01: '分类名称1-1'
            },
            {
              id: 12,
              field01: '分类名称1-2'
            }
          ])
        }, 1000)
      }
    }
  }
</script>
