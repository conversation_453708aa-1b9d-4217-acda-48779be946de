import axios from 'axios'
import { UpLoadBase64 } from './UploadModule'
import Md5 from 'crypto-js/md5'
import { checkDomain } from '../Common'
import Mockjs from 'mockjs'
import Env from '@api/service/common/utils/Env'
/*
 * @Author: ZhuSong
 * @Date: 2022-12-12 08:43:05
 * @LastEditors: ZhuSong
 * @LastEditTime: 2022-12-15 15:06:48
 * @Description:
 */
const MAX_FILE_SIZE = 20 * 1024 * 1024

export default class UploadUtil {
  /**
   * 是否为公共的
   */
  isPublic = false
  /**
   * 上传百分比
   */
  progress = 0
  /**
   * axiox 取消请求实例
   */
  private source = axios.CancelToken.source()
  /**
   * 图片OR文件
   */
  private isImageRequest = false
  /**
   * 传文件使用
   */
  private MD5 = ''

  /**
   * 上传图片
   */
  async doUploadBase64(file: File, isPublic: boolean, token: string) {
    this.isImageRequest = true
    this.isPublic = isPublic
    const base64Str = await this.getBase64(file)
    const imgString = base64Str.split(',')
    const param = new UpLoadBase64()
    param.fileName = file.name
    param.base64Data = imgString.pop()
    // const request = new UpLoad()
    // const obj = {
    //   bizType: BizTypeEnum.HY_PT,
    //   owner: '2ca4343083f848640183f84ae77f0010',
    //   sign: '测试Obs上传功能-图片'
    // }
    // await request.getToken(obj)
    // const wordArray = Crypto.enc.Utf8.parse(JSON.stringify(obj))
    // console.log(Crypto.enc.Base64.stringify(wordArray))
    param.uploadToken = token
    return this.uploadBase64(param)
  }

  /**
   * 上传文件
   * @param uploadFile 文件
   * @param isPublic 是否为公共
   * @param handleUploadSuccess 成功回调
   * @param handleUploadError 失败回调
   */
  uploadFile(uploadFile: File, isPublic: boolean, userId: string, token: string) {
    this.isImageRequest = false
    this.MD5 = this.getMd5(uploadFile.name, userId)
    if (uploadFile.size < MAX_FILE_SIZE) {
      return this.doUploadFile(uploadFile, isPublic, token)
    } else {
      return this.doUploadFileBlock(uploadFile, isPublic, token)
    }
  }

  /**
   * 取消请求
   */
  doCancel() {
    //
    this.source.cancel()
  }

  /**
   * 上传文件
   * @param uploadFile 文件
   * @param isPublic 是否为公共
   * @param handleUploadSuccess 成功回调
   * @param handleUploadError 失败回调
   */
  private doUploadFile(uploadFile: File, isPublic: boolean, token: string) {
    this.isPublic = isPublic
    const _processCallback = (progressEvent: any) => {
      // 完成百分比
      const complete = ((progressEvent.loaded / progressEvent.total) * 100) | 0
      this.progress = complete
    }
    const file = uploadFile
    const formData = new FormData()
    formData.append('file', file)
    formData.append('uploadToken', token)
    //   MD5
    formData.append('md5', this.MD5)
    formData.append('size', file.size as unknown as string)

    return this.uploadFileBlock(formData, _processCallback)
  }

  /**
   * 断点续传 --- 大文件使用
   * @param uploadFile 文件
   * @param isPublic 是否为公共
   * @param handleUploadSuccess 成功回调
   * @param handleUploadError 失败回调
   */
  private doUploadFileBlock(uploadFile: File, isPublic: boolean, token?: string) {
    const file = uploadFile
    const fileSize = file.size
    let start = 0
    let end = 0
    const fileResourceId = Mockjs.Random.guid().replaceAll('-', '')
    const chunks = Math.ceil(file.size / MAX_FILE_SIZE)
    this.isPublic = isPublic
    // 计算完成百分比
    const _handleUploadProgress = (progressEvent: any) => {
      console.log('进度事件', progressEvent)
      console.log(start, end)

      // 这里的上传百分比是切割块的百分比，所以要结合整个文件的大小算上传百分比。
      let percent = parseInt((((progressEvent.loaded + start) / fileSize) * 100) as unknown as string, 10)
      console.log(percent, 'percent')
      if (percent < this.progress) {
        // 计算的时候分段的进度少于之前的进度
        return
      }
      // 如果格式话之后超过了100%之后，直接变成100%
      if (percent > 100) {
        percent = 100
      }
      this.progress = percent != 100 ? percent : 99
    }

    const sliceUpload = async (start: any, end: any, index: number) => {
      const fileBlock = file.slice(start, end)
      const form = new FormData()
      //   数据内容
      form.append('file', fileBlock, file.name)
      //  总数据
      form.append('chunks', chunks as any)
      //   第几块数据
      form.append('chunk', index as any)
      //   上传数据大小
      form.append('chunkSize', (end - start) as any)
      //   总大小
      form.append('size', file.size as any)
      //   文件ID
      form.append('fileResourceId', fileResourceId)
      //   MD5
      form.append('md5', this.MD5)
      //   token
      form.append('uploadToken', token)
      const controller = new AbortController()
      controller.abort()
      return await this.uploadFileBlock(form, _handleUploadProgress)
    }
    const uploadFileByBlock = async () => {
      // start 不需要切片 从0开始 end 文件大小
      // start 需要切片 第一块切完了 start = end end是上限
      // index ++   当最后一片小于上限 就停止递归
      // const indexCount = Math.ceil(fileSize / MAX_FILE_SIZE) // 线上取证
      for (let i = 0; i < chunks; i++) {
        if (i == chunks - 1) {
          start = i * MAX_FILE_SIZE
          end = fileSize
          return await sliceUpload(start, end, i)
        }
        start = i * MAX_FILE_SIZE
        end = start + MAX_FILE_SIZE
        await sliceUpload(start, end, i)
      }

      // if (start + MAX_FILE_SIZE >= fileSize) {
      //   end = fileSize
      //   // 上传方法
      //   return
      // } else {
      //   end = start + MAX_FILE_SIZE
      // }
    }

    return uploadFileByBlock()
  }
  // 上传文件
  private uploadFileBlock(data: FormData, processCallback: (progressEvent: any) => void): any {
    const config = this.init()
    return axios
      .request({
        url: config.action,
        method: 'post',
        headers: config.headers,
        data,
        onUploadProgress: processCallback,
        cancelToken: this.source.token
      })
      .then((res) => {
        if (this.progress == 99) {
          this.progress = 100
        }
        return res
      })
  }

  // 上传图片
  private uploadBase64(data: UpLoadBase64) {
    const config = this.init()
    return axios.request({
      url: config.action,
      method: 'post',
      headers: config.headers,
      data,
      cancelToken: this.source.token
    })
  }

  // 初始化请求配置
  private init() {
    const env = Env.proxyEnvStr
    const port = Env.proxyPortStr
    const host = location.hostname
    let action
    const headers = new Object()
    if (checkDomain()) {
      if (Env.isProxyInnerNetworkEnv) {
        // 内网环境
        action = `${['https://api', env, '59iedu.com', port].join('')}${'/web/ms-obsfile-v1'}`
      } else {
        // 生产环境
        action = `https://api.59iedu.com${'/web/ms-obsfile-v1'}`
      }
    } else {
      // IP访问
      if (process.env.NODE_ENV != 'production') {
        // 开发模式
        action = `http://************:1457/web/ms-obsfile-v1`
      } else {
        // 部署模式
        action = `${location.protocol}//${location.host}/web/ms-obsfile-v1`
        // console.log(config.url)
      }
    }

    if (this.isPublic) {
      if (this.isImageRequest) {
        action += '/web/uploadPublicBase64'
      } else {
        action += '/web/uploadPublic'
      }
    } else {
      if (this.isImageRequest) {
        action += '/web/uploadProtectedBase64'
      } else {
        action += '/web/uploadMedia'
      }
    }
    // 设置额外请求头
    headers['Service-Name'] = 'ms-obsfile-v1'
    headers['Graphql-SchemaName'] = 'ms-obsfile-v1'
    return {
      action,
      headers
    }
  }

  /**
   * 图片转换base64
   * @param file 图片
   * @returns
   */
  private getBase64(file: File): Promise<string> {
    return new Promise(function (resolve, reject) {
      const reader = new FileReader()
      let imgResult = ''
      reader.readAsDataURL(file)
      reader.onload = function () {
        imgResult = reader.result as string
      }
      reader.onerror = function (error) {
        reject(error)
      }
      reader.onloadend = function () {
        resolve(imgResult)
      }
    })
  }
  /**
   * 生成MD5
   */
  private getMd5(fileName: string, userId: string) {
    return Md5(fileName + userId + new Date().getTime()).toString()
  }
}
