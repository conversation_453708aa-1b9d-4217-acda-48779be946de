<route-meta>
{
"isMenu": true,
"title": "培训方案管理",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main>
    <template v-if="$hasPermission('query')" desc="查询" actions="searchBase">
      <div class="f-p15" :style="fullScreen.isFull ? fullScreen.style : ''">
        <el-card shadow="never" class="m-card f-mb15" v-loading="uiConfig.loading.pageLoading">
          <!--条件查询-->
          <hb-search-wrapper expand @reset="resetQueryParam" class="m-query is-border-bottom">
            <el-form-item label="年度">
              <biz-year-select v-model="localSkuProperty.year" placeholder="请选择培训年度"></biz-year-select>
            </el-form-item>
            <el-form-item label="地区">
              <biz-national-region
                v-model="localSkuProperty.region"
                :check-strictly="true"
                placeholder="请选择地区"
              ></biz-national-region>
            </el-form-item>
            <el-form-item label="行业">
              <biz-industry-select
                v-model="localSkuProperty.industry"
                @clearIndustrySelect="handleClearIndustrySelect"
                @industryPropertyId="handleIndustryPropertyId"
                @industryInfos="handleIndustryInfos"
                ref="industrySelect"
              ></biz-industry-select>
            </el-form-item>
            <template v-if="localSkuProperty.industry && localSkuProperty.industry === envConfig.teacherIndustryId">
              <el-form-item label="学段">
                <biz-study-period
                  v-model="localSkuProperty.studyPeriodId"
                  :industry-id="localSkuProperty.industry"
                  :industry-property-id="industryPropertyId"
                  @updateStudyPeriod="updateStudyPeriod"
                  @clearSubject="clearSubject"
                ></biz-study-period>
              </el-form-item>
              <el-form-item label="学科">
                <biz-subject
                  v-model="localSkuProperty.subjectId"
                  :industry-property-id="industryPropertyId"
                  :studyPeriodId="localSkuProperty.studyPeriodId"
                  @updateSubject="updateSubject"
                ></biz-subject>
              </el-form-item>
            </template>
            <el-form-item
              label="技术等级"
              v-if="
                skuVisible.jobLevel &&
                localSkuProperty.industry &&
                localSkuProperty.industry === envConfig.workServiceId
              "
            >
              <biz-technical-grade-select
                v-model="localSkuProperty.jobLevel"
                :industry-id="localSkuProperty.industry"
                :industry-property-id="industryPropertyId"
              ></biz-technical-grade-select>
            </el-form-item>
            <el-form-item label="科目类型" v-if="skuVisible.subjectType && localSkuProperty.industry">
              <biz-accounttype-select
                v-model="localSkuProperty.subjectType"
                :industry-property-id="industryPropertyId"
                :industryId="localSkuProperty.industry"
              >
              </biz-accounttype-select>
            </el-form-item>
            <el-form-item
              label="培训专业"
              v-if="
                skuVisible.trainingCategory &&
                localSkuProperty.industry &&
                envConfig.societyIndustryId &&
                localSkuProperty.industry === envConfig.societyIndustryId
              "
            >
              <biz-major-cascader
                v-model="localSkuProperty.societyTrainingMajor"
                :industry-property-id="industryPropertyId"
                :industryId="localSkuProperty.industry"
              />
            </el-form-item>
            <el-form-item
              label="培训类别"
              v-if="
                skuVisible.trainingCategory &&
                localSkuProperty.industry &&
                (localSkuProperty.industry === envConfig.constructionIndustryId ||
                  localSkuProperty.industry === envConfig.occupationalHealthId)
              "
            >
              <biz-training-category-select
                v-model="localSkuProperty.trainingCategory"
                :industry-property-id="industryPropertyId"
                @updateTrainingCategory="handleUpdateTrainingCategory"
                :industryId="localSkuProperty.industry"
              />
            </el-form-item>
            <el-form-item
              label="培训专业"
              v-if="
                skuVisible.trainingCategory &&
                localSkuProperty.industry &&
                envConfig.constructionIndustryId &&
                localSkuProperty.industry === envConfig.constructionIndustryId
              "
            >
              <biz-major-select
                v-model="localSkuProperty.constructionTrainingMajor"
                :industry-property-id="industryPropertyId"
                :training-category-id="trainingCategoryId"
                :industryId="localSkuProperty.industry"
              />
            </el-form-item>
            <el-form-item
              label="培训对象"
              v-if="
                skuVisible.trainingCategory &&
                localSkuProperty.industry &&
                localSkuProperty.industry === envConfig.occupationalHealthId
              "
            >
              <biz-training-object-select
                v-model="localSkuProperty.trainingObject"
                placeholder="请选择培训对象"
                :industry-property-id="industryPropertyId"
                :industry-id="localSkuProperty.industry"
                @updateTrainingCategory="updateTrainingCategory"
              />
            </el-form-item>
            <el-form-item
              label="岗位类别"
              v-if="
                skuVisible.trainingCategory &&
                localSkuProperty.industry &&
                localSkuProperty.industry === envConfig.occupationalHealthId
              "
            >
              <biz-obj-category-select
                v-model="localSkuProperty.positionCategory"
                placeholder="请选择岗位类别"
                :industry-property-id="industryPropertyId"
                :industryId="localSkuProperty.industry"
                :training-object-id="localSkuProperty.trainingObject"
              />
            </el-form-item>
            <el-form-item
              label="执业类别"
              v-if="
                skuVisible.occupationalCategory &&
                localSkuProperty.industry &&
                localSkuProperty.industry === envConfig.yshyId
              "
            >
              <biz-practicing-category-cascader
                v-model="localSkuProperty.pharmacistIndustry"
                :industryId="localSkuProperty.industry"
              ></biz-practicing-category-cascader>
            </el-form-item>
            <el-form-item label="培训方案类型">
              <biz-scheme-type v-model="schemeTypeInfo"></biz-scheme-type>
            </el-form-item>
            <el-form-item label="培训方案名称">
              <el-input clearable placeholder="请输入培训方案名称" v-model="schemeName" />
            </el-form-item>
            <el-form-item label="销售状态">
              <el-select
                clearable
                @clear="onShelveStatus = undefined"
                placeholder="请选择销售状态"
                v-model="onShelveStatus"
              >
                <el-option
                  v-for="item in onShelveStatusOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <template slot="actions">
              <el-button type="primary" @click="searchBase">查询</el-button>
              <template v-if="$hasPermission('exportList')" desc="导出列表数据" actions="doExportList">
                <el-button type="primary" @click="openExportList" :loading="exportLoading">导出列表数据</el-button>
              </template>
              <template
                v-if="$hasPermission('batchUpdate')"
                desc="批量更新方案"
                actions="doBatchUpdate,@BatchUpdateScheme"
              >
                <el-button type="primary" @click="doBatchUpdate">批量更新方案</el-button>
              </template>
              <el-button type="primary" @click="tableScreen">表格全屏</el-button>
            </template>
          </hb-search-wrapper>

          <el-tabs
            v-model="activeListName"
            type="card"
            @tab-click="tabChange"
            class="m-tab-card"
            v-loading="trainSchemeQuery.loading"
            v-if="isOnlineClassSupport"
          >
            <el-tab-pane label="网授" name="onlineClassList"></el-tab-pane>
            <el-tab-pane label="面授" name="offlineClassList"></el-tab-pane>
            <el-tab-pane label="面网授" name="noOnlyOnlineClassList"></el-tab-pane>
          </el-tabs>
          <el-card shadow="never" class="m-card" style="min-height: 220px" v-loading="trainSchemeQuery.loading">
            <!--表格-->
            <el-table
              stripe
              :data="trainSchemeList"
              class="m-table"
              @sort-change="handleSortChange"
              :key="count"
              ref="elTableRef"
              :max-height="fullScreen.isFull ? '800px' : '500px'"
            >
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="培训方案名称" min-width="300" fixed="left">
                <template slot-scope="scope">
                  <p>
                    <biz-show-scheme-type
                      :trainingMode="scope.row.skuValueNameProperty.trainingMode.skuPropertyValueId"
                      :schemeType="scope.row.schemeType"
                      :isShowTrainingMode="false"
                    />
                    <!--                    <el-tag type="primary" effect="dark" size="mini">{{ getSchemeType(scope.row) }}</el-tag>-->
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="scope.row.commodityBasicData.saleTitle"
                      placement="top"
                    >
                      <span style="margin-right: 5px">{{ scope.row.commodityBasicData.saleTitle }}</span>
                    </el-tooltip>
                    <hb-copy :content="scope.row.commodityBasicData.saleTitle"></hb-copy>
                  </p>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    placement="bottom"
                    popper-class="m-tooltip"
                    v-if="getSchemeIsProcessing(scope.row)"
                  >
                    <el-tag type="warning">处理中<i class="el-icon-info icon"></i></el-tag>
                    <div slot="content">系统正在处理上一次修改方案的重算任务，暂时不支持再次修改</div>
                  </el-tooltip>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    placement="bottom"
                    popper-class="m-tooltip"
                    v-if="scope.row.hasError"
                  >
                    <el-tag type="danger">异常<i class="el-icon-info icon"></i></el-tag>
                    <div slot="content">异常原因：{{ scope.row.errorMsg }}</div>
                  </el-tooltip>
                  <el-tag type="warning" v-if="scope.row.intelligentLearning">处理中</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="期别数量" v-if="!isOnlineClass" min-width="110" align="center">
                <template slot-scope="scope">
                  {{ scope.row.issueInfoResponse.issueCount }}
                </template>
              </el-table-column>
              <el-table-column label="报名学时" min-width="100" align="center">
                <template slot-scope="scope">{{ scope.row.period }}</template>
              </el-table-column>
              <el-table-column label="价格" min-width="100" align="right">
                <template slot-scope="scope">{{ scope.row.commodityBasicData.price }}</template>
              </el-table-column>
              <el-table-column label="培训属性" min-width="240">
                <template slot-scope="scope">
                  <p v-if="getSkuPropertyName(scope.row, 'industry')">
                    行业：{{ getSkuPropertyName(scope.row, 'industry') }}
                  </p>
                  <p v-if="getSkuPropertyName(scope.row, 'region')">
                    地区：{{ getSkuPropertyName(scope.row, 'region') }}
                  </p>
                  <p v-if="getSkuPropertyName(scope.row, 'jobLevel')">
                    技术等级：{{ getSkuPropertyName(scope.row, 'jobLevel') }}
                  </p>
                  <p v-if="getSkuPropertyName(scope.row, 'subjectType')">
                    科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
                  </p>
                  <p v-if="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
                    培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
                  </p>
                  <p v-if="getSkuPropertyName(scope.row, 'trainingMajor')">
                    培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
                  </p>
                  <p v-if="getSkuPropertyName(scope.row, 'trainingObject')">
                    培训对象：{{ getSkuPropertyName(scope.row, 'trainingObject') }}
                  </p>
                  <p v-if="getSkuPropertyName(scope.row, 'positionCategory')">
                    岗位类别：{{ getSkuPropertyName(scope.row, 'positionCategory') }}
                  </p>
                  <p v-if="getSkuPropertyName(scope.row, 'year')">
                    培训年度：{{ getSkuPropertyName(scope.row, 'year') }}
                  </p>
                  <p v-if="getSkuPropertyName(scope.row, 'learningPhase')">
                    学段：{{ getSkuPropertyName(scope.row, 'learningPhase') }}
                  </p>
                  <p v-if="getSkuPropertyName(scope.row, 'discipline')">
                    学科：{{ getSkuPropertyName(scope.row, 'discipline') }}
                  </p>
                  <p v-if="getSkuPropertyName(scope.row, 'practitionerCategory')">
                    执业类别：{{ getSkuPropertyName(scope.row, 'certificatesType') }}-{{
                      getSkuPropertyName(scope.row, 'practitionerCategory')
                    }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column label="销售状态" min-width="140">
                <template slot-scope="scope">
                  <div v-if="scope.row.onShelve.shelveStatus === 0">
                    <el-badge is-dot type="info" class="badge-status">已下架</el-badge>
                  </div>
                  <div v-if="scope.row.onShelve.shelveStatus === 1">
                    <el-badge is-dot type="success" class="badge-status">已上架</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="学习起止时间"
                min-width="220"
                v-if="isOnlineClass || activeListName === 'noOnlyOnlineClassList'"
              >
                <template slot-scope="scope">
                  <p v-if="!scope.row.isLongTerm">起始：{{ scope.row.trainingBeginDate }}</p>
                  <p v-if="!scope.row.isLongTerm">结束：{{ scope.row.trainingEndDate }}</p>
                  <p v-if="scope.row.isLongTerm">长期有效</p>
                </template>
              </el-table-column>
              <el-table-column label="报名起止时间" min-width="220" v-if="isOnlineClass">
                <template slot-scope="scope">
                  <p v-if="scope.row.registerBeginDate || scope.row.registerEndDate">
                    起始：{{ scope.row.registerBeginDate ? scope.row.registerBeginDate : '--' }}
                  </p>
                  <p v-if="scope.row.registerBeginDate || scope.row.registerEndDate">
                    结束：{{ scope.row.registerEndDate ? scope.row.registerEndDate : '--' }}
                  </p>
                  <p v-if="!scope.row.registerBeginDate && !scope.row.registerEndDate">暂不开启</p>
                </template>
              </el-table-column>
              <el-table-column label="最新修改时间" min-width="180" sortable="custom" prop="commodityLastEditTime">
              </el-table-column>
              <el-table-column label="是否仅导入开通" min-width="140">
                <template slot-scope="scope">
                  {{ scope.row.commodityPurchaseChannelConfig.customerPurchase.couldBuy ? '否' : '是' }}
                </template>
              </el-table-column>
              <el-table-column
                v-if="schoolConfigFlag && isOnlineClass"
                key="needDataSync"
                label="成果是否同步"
                min-width="180"
                prop="needDataSync"
                align="center"
              >
                <template slot-scope="scope">
                  {{ isNeedDataSync(scope.row.needDataSync) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center" fixed="right">
                <template slot-scope="scope">
                  <template
                    v-if="$hasPermission('modify')"
                    desc="修改"
                    actions="@hbfe/jxjy-admin-scheme/src/modify.vue,editScheme"
                  >
                    <el-button type="text" size="mini" @click="editScheme(scope.row)" :disabled="!scope.row.isNewScheme"
                      >修改
                    </el-button>
                  </template>
                  <template
                    v-if="$hasPermission('detail')"
                    desc="详情"
                    actions="@hbfe/jxjy-admin-scheme/src/detail.vue"
                  >
                    <el-button type="text" size="mini" @click="viewSchemeDetail(scope.row)">详情</el-button>
                  </template>
                  <template
                    v-if="$hasPermission('create')"
                    desc="创建/复制"
                    actions="@hbfe/jxjy-admin-scheme/src/create.vue"
                  >
                    <el-popconfirm
                      confirm-button-text="确定复制"
                      title="确认复制该培训方案?线上课程分类下的课程将引用的课程包最新的课程。"
                      icon="el-icon-info"
                      icon-color="gray"
                      @confirm="copyScheme(scope.row)"
                    >
                      <el-button type="text" size="mini" slot="reference">复制</el-button>
                    </el-popconfirm>
                  </template>
                  <!-- <el-button type="text" size="mini" @click="viewLog(scope.row)">修改日志</el-button> -->
                  <slot name="operating-button-slot" :scope="scope">
                    <template v-if="$hasPermission('enrollClose')" desc="立即下架" actions="vaildOnShelvesFrom">
                      <el-popconfirm
                        v-show="scope.row.onShelve.shelveStatus === 1"
                        confirm-button-text="确定下架"
                        title="确定立即下架该培训方案？"
                        @confirm="vaildOnShelvesFrom(scope.row, false)"
                      >
                        <el-button type="text" size="mini" slot="reference">立即下架</el-button>
                      </el-popconfirm>
                    </template>
                    <template v-if="$hasPermission('enrollOpen')" desc="立即上架" actions="vaildOnShelvesFrom">
                      <el-popconfirm
                        v-show="scope.row.onShelve.shelveStatus === 0"
                        confirm-button-text="确定上架"
                        title="确定立即上架该培训方案？"
                        @confirm="vaildOnShelvesFrom(scope.row, true)"
                      >
                        <el-button type="text" size="mini" slot="reference">立即上架</el-button>
                      </el-popconfirm>
                    </template>
                    <template v-if="$hasPermission('delete')" desc="删除" actions="deleteScheme">
                      <el-popconfirm
                        confirm-button-text="确定删除"
                        title="删除后需要重新创建培训班，是否确认删除？"
                        @confirm="deleteScheme(scope.row)"
                      >
                        <el-button type="text" size="mini" slot="reference">删除</el-button>
                      </el-popconfirm>
                    </template>
                  </slot>
                  <template v-if="$hasPermission('signUpIssue')" desc="查看期别" actions="openSignUpIssueDrawer">
                    <el-button
                      v-if="!isOnlineClass"
                      type="text"
                      size="mini"
                      slot="reference"
                      @click="openSignUpIssueDrawer(scope.row.resource.schemeId)"
                      >查看期别</el-button
                    >
                  </template>
                  <template
                    v-if="$hasPermission('enterImpManagement')"
                    desc="实施管理"
                    actions="@hbfe/jxjy-admin-scheme/src/implementingManagement/index.vue"
                  >
                    <el-button
                      type="text"
                      size="mini"
                      slot="reference"
                      @click="enterImpManagement(scope.row.resource.schemeId)"
                      >实施管理</el-button
                    ></template
                  >
                  <!--</template>-->
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <hb-pagination :page="trainSchemePage" v-bind="trainSchemePage"></hb-pagination>
          </el-card>
          <!--日志详情-->
          <modify-log-drawer ref="modifyLogDrawerRef" />
        </el-card>
      </div>
    </template>

    <!--提示弹窗-->
    <async-task-dialog
      :visible.sync="uiConfig.exportDialog"
      :is-export="isExport"
      :task-name="taskName"
      :task-path="taskPath"
      :type="asyncTaskType"
    />
    <batch-update-scheme
      :visible.sync="batchUpdateDrawerVisible"
      @showImportBatchUpdateSchemeDialog="showImportBatchUpdateSchemeDialog"
    />
    <sign-up-issue-drawer
      ref="signUpRef"
      :termLoading="termLoading"
      :issueList="issueList"
      :sign-up-issue-drawer.sync="uiConfig.signUpIssueDrawer"
    ></sign-up-issue-drawer>
    <ExportDrawer ref="ExportDrawerRef" @confirmOption="confirmOption"></ExportDrawer>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import { Page, Query, UiPage } from '@hbfe/common'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import {
    CommoditySkuRequest,
    OnShelveRequest,
    RegionSkuPropertySearchRequest,
    SchemeRequest,
    SkuPropertyRequest,
    SortPolicy
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import screenfull, { Screenfull } from 'screenfull'
  import { cloneDeep, isBoolean } from 'lodash'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import BizNationalRegion from '@hbfe/jxjy-admin-components/src/biz/biz-national-region.vue'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import BizAccounttypeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-accounttype-select.vue'
  import BizTrainingCategorySelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-category-select.vue'
  import BizMajorSelect from '@hbfe/jxjy-admin-components/src/biz/biz-major-select.vue'
  import { RegionSkuPropertyRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import BizMajorCascader from '@hbfe/jxjy-admin-components/src/biz/biz-major-cascader.vue'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import { bind, debounce } from 'lodash-decorators'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import QueryOrderListVo from '@api/service/management/trade/single/order/query/vo/QueryOrderListVo'
  import { TableSort } from '@hbfe/jxjy-admin-scheme/src/models/TableSort'
  import { FakeOperateTypeEnum } from '@hbfe/jxjy-admin-scheme/src/models/FakeOperateTypeEnum'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-scheme/src/models/SchemeSkuProperty'
  import FakeSchemeOperateLog from '@hbfe/jxjy-admin-scheme/src/models/FakeSchemeOperateLog'
  import UITrainClassCommodityDetail from '@hbfe/jxjy-admin-scheme/src/models/UITrainClassCommodityDetail'
  import MutationTrainClassCommodityClass from '@api/service/management/train-class/mutation/MutationTrainClassCommodityClass'
  import QueryOrder from '@api/service/management/trade/single/order/query/QueryOrder'
  import ModifyLogDrawer from '@hbfe/jxjy-admin-scheme/src/components/modify-log-drawer.vue'
  import BizTechnicalGradeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-technical-grade-select.vue'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import getServicerIsDocking from '@api/service/management/online-school-config/portal/query/QueryPortal'
  import screenFull from 'screenfull'
  import FullScreen from '@hbfe/jxjy-admin-common/src/util/FullScreen'
  import moment from 'moment'
  import MutationTrainClassOnSheles from '@api/service/management/train-class/mutation/MutationTrainClassOnSheles'
  import SchemeType, { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import BatchUpdateScheme from '@hbfe/jxjy-admin-scheme/src/components/batch-update-scheme.vue'
  import AsyncTaskDialog from '@hbfe/jxjy-admin-scheme/src/components/async-task-dialog.vue'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import SignUpIssueDrawer from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/sign-up-issue-drawer.vue'
  import ExportDrawer from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/export-drawer.vue'
  import {
    CommoditySkuSortRequest as CommoditySkuSortRequestv2,
    CommoditySkuSortField
  } from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
  import QueryShowOffline from '@api/service/common/config/QueryShowOffline'

  @Component({
    components: {
      SignUpIssueDrawer,
      AsyncTaskDialog,
      BizTechnicalGradeSelect,
      BizMajorCascader,
      BizMajorSelect,
      BizTrainingCategorySelect,
      BizAccounttypeSelect,
      BizIndustrySelect,
      BizNationalRegion,
      BizYearSelect,
      ModifyLogDrawer,
      BatchUpdateScheme,
      ExportDrawer
    }
  })
  export default class extends Vue {
    @Ref('elTableRef') elTableRef: any
    @Ref('modifyLogDrawerRef') modifyLogDrawer: ModifyLogDrawer
    @Ref('industrySelect') industrySelect: BizIndustrySelect
    @Ref('ExportDrawerRef') ExportDrawerRef: ExportDrawer
    @Ref('BizAccounttypeSelectRef') BizAccounttypeSelectRef: BizAccounttypeSelect
    @Ref('signUpRef') signUpRef: SignUpIssueDrawer

    FakeOperateTypeEnum = FakeOperateTypeEnum
    showLog = false
    operateLogList: Array<FakeSchemeOperateLog> = new Array<FakeSchemeOperateLog>()
    // 重算学员，需抽离单独组件
    studentListDialog = false
    studentList = new Array<{ name: string; account: string }>()
    studentListPage: UiPage
    // 查询参数 - 培训方案
    trainSchemeQueryParam: CommoditySkuRequest
    // 分页参数 - 培训方案
    trainSchemePage: UiPage
    // 分页加载参数 - 培训方案
    trainSchemeQuery: Query = new Query()
    // 培训方案列表
    trainSchemeList: Array<UITrainClassCommodityDetail> = new Array<UITrainClassCommodityDetail>()
    // 培训方案业务状态层入口
    QueryTrainClassCommodityList = new QueryTrainClassCommodityList()
    // 排序策略
    sortPolicy: Array<CommoditySkuSortRequestv2> = new Array<CommoditySkuSortRequestv2>()

    // 修改培训方案(方案计划状态) - 模型zyj
    mutationTrainClassOnSheles = new MutationTrainClassOnSheles()

    /**
     * 选中
     */
    activeListName = 'onlineClassList'
    onShelvesDialogShow = false
    // 设置时间选择范围
    isDisabled = {
      disabledDate(time: Date) {
        if (!time) {
          return false
        }
        // 大于某个日期不能选择
        const myDate = new Date(moment().format('YYYY-MM-DD 00:00:00'))
        return time.getTime() < myDate.getTime()
      }
    }

    /**
     * 资源供应商
     */
    resourceSupplierId = ''

    QueryOrder = new QueryOrder()

    fullScreen = FullScreen
    schemeType = SchemeType
    // 是否隐藏专业
    isHidden = false
    // 培训方案名称
    schemeName = ''
    // 培训机构
    unit = ''
    // 培训机构id
    unitId = ''
    // 更新table
    count = 0
    /**
     * 是否在当前页面，否则停止轮询
     */
    isActivated = true
    /**
     * 定时器创建，修改查询定时器
     */
    timer: any
    /**
     * loading遮罩层
     */
    loading: any
    /**
     * ui控制组
     */
    uiConfig = {
      loading: {
        pageLoading: false
      },
      exportDialog: false,
      signUpIssueDrawer: false
    }

    /**
     * 导出列表数据按钮loading加载
     */
    exportLoading = false

    // 暂存行业id
    industryId = ''
    // 期别弹窗loading
    termLoading = false
    // 期别数组
    issueList = new Array<IssueConfigDetail>()
    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = {
      /**
       * 年度
       */
      year: '',
      /**
       * 地区
       */
      region: [] as string[],
      /**
       * 行业
       */
      industry: '',
      /**
       * 科目类型
       */
      subjectType: '',
      /**
       * 培训类别
       */
      trainingCategory: '',
      /**
       * 培训专业 - 建设行业
       */
      constructionTrainingMajor: '',
      /**
       * 培训专业 - 人社行业
       */
      societyTrainingMajor: [] as string[],
      /**
       * 技术等级 - 工勤行业
       **/
      jobLevel: '',
      /**
       * 培训对象
       */
      trainingObject: '',
      /**
       * 岗位类别
       */
      positionCategory: '',
      /**
       * 学段id
       */
      studyPeriodId: '',
      /**
       * 学科id
       */
      subjectId: '',
      /**
       * 药师id
       */
      pharmacistIndustry: [] as string[]
    } as SchemeSkuProperty

    exportType = 1

    // 期别列表
    trainClassCommodityVo = new TrainClassCommodityVo()

    /**
     * 培训方案类型
     */
    schemeTypeInfo: Array<string> = new Array<string>()

    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''

    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: true,
      // 培训类别
      trainingCategory: true,
      // 技术等级
      jobLevel: true,
      // 培训对象
      trainingObject: true,
      //   岗位类别
      positionCategory: true,
      //执业类别
      occupationalCategory: true
    }

    /**
     * 当前网校信息
     */
    envConfig = {
      // 工勤行业
      workServiceId: '',
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 职业卫生行业Id
      occupationalHealthId: '',
      //教师行业id
      teacherIndustryId: '',
      //药师行业id
      yshyId: ''
    }

    // 长期有效 - 开始时间
    defaultBeginDate = CreateSchemeUtils.defaultBeginDate
    // 长期有效 - 结束时间
    defaultEndDate = CreateSchemeUtils.defaultEndDate

    MutationTrainClassCommodityClass = new MutationTrainClassCommodityClass()
    /**
     * 上下架状态-（销售状态）
     */
    onShelveStatus: number = null
    // 网校对接与否
    schoolConfigFlag = true
    onShelveStatusOptions = [
      { id: 1, label: '已上架' },
      { id: 0, label: '已下架' }
    ]

    isFullscreen = false

    /**
     * 是否展示面网授
     */
    get isOnlineClassSupport() {
      const show = QueryShowOffline.getShowOfflineApolloConfig()
      return !show
    }
    /**
     * 是否网授班
     */
    get isOnlineClass() {
      return this.activeListName === 'onlineClassList'
    }

    @Watch('localSkuProperty', {
      immediate: true,
      deep: true
    })
    localSkuPropertyChange(val: any) {
      console.log('localSkuProperty', cloneDeep(val))
    }

    constructor() {
      super()
      this.initQueryParam()
      this.trainSchemePage = new UiPage(this.pageScheme, this.pageScheme)
    }

    async openSignUpIssueDrawer(schemeId: string) {
      this.signUpRef.showDrawer = true
      this.termLoading = true
      this.trainClassCommodityVo.schemeId = schemeId
      try {
        this.issueList = await this.trainClassCommodityVo.queryIssueList()
      } catch (error) {
        console.log(error)
      } finally {
        this.termLoading = false
      }
    }

    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.trainSchemeQueryParam = new CommoditySkuRequest()
      this.trainSchemeQueryParam.onShelveRequest = new OnShelveRequest()
      this.trainSchemeQueryParam.schemeRequest = new SchemeRequest()
      this.trainSchemeQueryParam.schemeRequest.schemeName = ''
      this.trainSchemeQueryParam.skuPropertyRequest = new SkuPropertyRequest()
      this.trainSchemeQueryParam.skuPropertyRequest.year = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.trainSchemeQueryParam.skuPropertyRequest.regionSkuPropertySearch.region =
        new Array<RegionSkuPropertyRequest>()
      this.trainSchemeQueryParam.skuPropertyRequest.industry = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.subjectType = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingProfessional = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingObject = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.positionCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.jobLevel = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.learningPhase = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.discipline = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.certificatesType = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.practitionerCategory = new Array<string>()
      this.trainSchemeQueryParam.isDisabledResourceShow = true
      this.sortPolicy = new Array<CommoditySkuSortRequestv2>()
      this.schemeTypeInfo = new Array<string>()
    }

    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.studyPeriodId = ''
      if (this.localSkuProperty.subjectType) {
        this.BizAccounttypeSelectRef.clear()
      }
      this.initQueryParam()
      this.localSkuProperty = new SchemeSkuProperty()
      this.onShelveStatus = null
      this.schemeName = ''
      this.sortPolicy = new Array<CommoditySkuSortRequestv2>()

      // 移除表格排序
      this.elTableRef.clearSort()
      await this.searchBase()
    }

    /**
     * 加载第一页
     */
    @bind
    @debounce(200)
    async searchBase() {
      this.trainSchemePage.pageNo = 1
      await this.pageScheme()
    }

    hiddenSelect(val: boolean) {
      this.isHidden = val
    }

    /**
     * 分页查询
     */
    async pageScheme() {
      this.trainSchemeQuery.loading = true
      // 清除滚动条位置并定位回顶部
      this.clearScrollPositionBackTop()
      try {
        this.getPageQueryParams()
        const trainSchemeList = await this.getTrainSchemeList()
        this.trainSchemeList = new Array<UITrainClassCommodityDetail>()
        trainSchemeList?.map((el: UITrainClassCommodityDetail) => {
          const item = new UITrainClassCommodityDetail()
          Object.assign(item, el)
          item.isLongTerm = false
          item.isSocietyIndustry = false
          if (item.trainingEndDate === this.defaultEndDate && item.trainingBeginDate === this.defaultBeginDate) {
            item.isLongTerm = true
          }
          if (item.skuValueNameProperty?.industry?.skuPropertyName === '人社行业') {
            item.isSocietyIndustry = true
          }
          this.trainSchemeList.push(item)
          this.count++
        })
        console.log('trainSchemeList', this.trainSchemeList)
      } catch (e) {
        console.log('获取培训方案分页列表失败！', e)
      } finally {
        this.trainSchemeQuery.loading = false
        this.$nextTick(() => {
          this.elTableRef && this.elTableRef.doLayout()
        })
      }
    }

    /**
     * 处理列表查询参数
     */
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.trainSchemeQueryParam.onShelveRequest.onShelveStatus =
        this.onShelveStatus || this.onShelveStatus === 0 ? this.onShelveStatus : undefined
      this.trainSchemeQueryParam.schemeRequest.schemeName = this.schemeName || undefined

      this.configureTrainSchemeQueryParam()
    }

    /**
     * 获取培训班列表
     */
    async getTrainSchemeList() {
      if (this.activeListName === 'onlineClassList') {
        this.trainSchemeQueryParam.skuPropertyRequest.trainingForm = [TrainingModeEnum.online]
      } else if (this.activeListName === 'offlineClassList') {
        this.trainSchemeQueryParam.skuPropertyRequest.trainingForm = [TrainingModeEnum.offline]
      } else {
        this.trainSchemeQueryParam.skuPropertyRequest.trainingForm = [TrainingModeEnum.mixed]
      }
      if (this.sortPolicy.length) {
        return (
          (await this.QueryTrainClassCommodityList.queryTrainClassCommodityList(
            this.trainSchemePage,
            this.trainSchemeQueryParam,
            this.sortPolicy
          )) || ([] as TrainClassCommodityVo[])
        )
      } else {
        return (
          (await this.QueryTrainClassCommodityList.queryTrainClassCommodityList(
            this.trainSchemePage,
            this.trainSchemeQueryParam
          )) || ([] as TrainClassCommodityVo[])
        )
      }
    }

    /**
     * 配置查询参数
     */
    configureTrainSchemeQueryParam() {
      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.trainSchemeQueryParam.schemeRequest.schemeType = undefined
      }
      const schemeType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      if (schemeType in SchemeTypeEnum) {
        this.trainSchemeQueryParam.schemeRequest.schemeType = schemeType
      } else {
        this.trainSchemeQueryParam.schemeRequest.schemeType = undefined
      }
    }

    /**
     * 获取本地sku选项
     */
    getLocalSkuProperty() {
      const skuProperties = cloneDeep(this.trainSchemeQueryParam.skuPropertyRequest)
      skuProperties.year = !this.localSkuProperty.year ? ([] as string[]) : [this.localSkuProperty.year]
      skuProperties.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
      const localRegion = cloneDeep(this.localSkuProperty.region)
      if (Array.isArray(localRegion) && localRegion.length) {
        const option = new RegionSkuPropertyRequest()
        option.province = localRegion.length >= 1 ? localRegion[0] : undefined
        option.city = localRegion.length >= 2 ? localRegion[1] : undefined
        option.county = localRegion.length >= 3 ? localRegion[2] : undefined
        skuProperties.regionSkuPropertySearch.region.push(option)
        skuProperties.regionSkuPropertySearch.regionSearchType = 1
      } else {
        skuProperties.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      }
      skuProperties.industry = !this.localSkuProperty.industry ? ([] as string[]) : [this.localSkuProperty.industry]
      skuProperties.jobLevel = !this.localSkuProperty.jobLevel ? ([] as string[]) : [this.localSkuProperty.jobLevel]
      skuProperties.subjectType = !this.localSkuProperty.subjectType
        ? ([] as string[])
        : [this.localSkuProperty.subjectType]
      skuProperties.trainingCategory = !this.localSkuProperty.trainingCategory
        ? ([] as string[])
        : [this.localSkuProperty.trainingCategory]
      skuProperties.trainingProfessional = this.getTrainingProfessional()
      skuProperties.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      skuProperties.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      skuProperties.learningPhase = !this.localSkuProperty.studyPeriodId
        ? ([] as string[])
        : [this.localSkuProperty.studyPeriodId]
      skuProperties.discipline = !this.localSkuProperty.subjectId ? ([] as string[]) : [this.localSkuProperty.subjectId]
      //执业类别id TODO

      skuProperties.certificatesType = !this.localSkuProperty.pharmacistIndustry
        ? ([] as string[])
        : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
        ? [this.localSkuProperty.pharmacistIndustry[0]]
        : []
      skuProperties.practitionerCategory = !this.localSkuProperty.pharmacistIndustry
        ? ([] as string[])
        : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
        ? [this.localSkuProperty.pharmacistIndustry[1]]
        : []

      this.trainSchemeQueryParam.skuPropertyRequest = cloneDeep(skuProperties)
      //   console.log('selectedSkuProperties', JSON.stringify(this.trainSchemeQueryParam.skuPropertyRequest))
    }

    created() {
      this.clearScrollPosition()
      this.init()
      this.change()
    }

    init() {
      if (screenFull.isEnabled) {
        screenFull.on('change', this.change)
      }
    }

    change() {
      FullScreen.isFull = (screenFull as Screenfull).isFullscreen
      console.log(FullScreen.isFull)
    }

    /**
     * @description 页面初始化
     * */
    async mounted() {
      this.queryConfig()
      await this.searchBase()
    }

    /**
     * @description 页面被激活
     */
    async activated() {
      this.useScrollPosition()
      this.isActivated = true
      if (this.$route.query.schemeName) {
        this.schemeName = this.$route.query.schemeName as string
        await this.searchBase()
      }
      this.$nextTick(() => {
        this.elTableRef && this.elTableRef.doLayout()
      })
    }

    /**
     * @description 基础查询
     */
    async queryConfig() {
      // 获取是否对接公共服务平台
      this.schoolConfigFlag = await getServicerIsDocking.getServicerIsDocking()
      if (this.$route.query.schemeName) {
        this.schemeName = this.$route.query.schemeName as string
      }
      // 查询sku
      await QueryIndustry.queryIndustry()
      if (QueryIndustry.industryList.length == 1) {
        this.localSkuProperty.industry = QueryIndustry.industryList[0].id
      }
    }

    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      // console.log('envConfig', this.envConfig)
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人设行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }

    /**
     * 修改培训方案
     */
    async editScheme(row: UITrainClassCommodityDetail) {
      this.setScrollPosition()
      if (row.recalculating)
        return this.$confirm(
          '系统正在处理上一次修改方案的重算任务，处理过程中仅支持修改培训方案基础信息，是否立即前往？',
          '系统提醒',
          {
            confirmButtonText: '确定',
            type: 'warning',
            cancelButtonText: '取消'
          }
        )
          .then(() => {
            this.$router.push('/training/scheme/modify/' + row.commoditySkuId)
          })
          .catch(() => {
            //
          })
      // 方案处于智能学习中
      if (row.intelligentLearning)
        return this.$confirm(
          '当前培训方案下，系统在为指定已报名学员进行智能学习，处理过程中仅支持修改培训方案基础信息，是否立即前往？',
          '系统提醒',
          {
            confirmButtonText: '确定',
            type: 'warning',
            cancelButtonText: '取消'
          }
        )
          .then(() => {
            this.$router.push('/training/scheme/modify/' + row.commoditySkuId)
          })
          .catch(() => {
            //
          })
      if (row.isResourceEnabled === false)
        return this.$confirm('方案正在处理中，处理成功后可再次修改。', '系统提醒', {
          confirmButtonText: '确定',
          type: 'warning',
          showCancelButton: false
        })
      const res = await this.QueryTrainClassCommodityList.getIsProcessedByTransaction([row.schemeId])
      if (res?.length && !res[0].isProcessed) {
        return this.$confirm('该方案暂不支持修改', '系统提醒', {
          confirmButtonText: '确定',
          type: 'warning',
          showCancelButton: false
        })
      }
      FullScreen.closeFull()
      await this.$router.push('/training/scheme/modify/' + row.commoditySkuId)
    }

    /**
     * 查看培训方案详情
     */
    viewSchemeDetail(row: UITrainClassCommodityDetail) {
      this.setScrollPosition()
      FullScreen.closeFull()
      this.$router.push('/training/scheme/detail/' + row.commoditySkuId)
    }

    /**
     * 复制培训方案
     */
    copyScheme(row: UITrainClassCommodityDetail) {
      this.setScrollPosition()
      FullScreen.closeFull()
      this.$router.push('/training/scheme/create/' + row.commoditySkuId)
    }

    /**
     * 进入实施管理
     */
    enterImpManagement(id: string) {
      this.$router.push(`/training/scheme/implementingManagement/${id}`)
    }
    /**
     * 进入教务管理
     */
    enterAcademicAdministration(schemeId: string, periodId: string) {
      this.$router.push('/training/scheme/academicAdministration/1/2')
    }

    /**
     * 查看修改日志
     */
    viewLog(row: UITrainClassCommodityDetail) {
      this.modifyLogDrawer.viewLog(row)
    }

    /**
     * 立即上架/下架
     * @param {UITrainClassCommodityDetail} row - 分页单行数据
     * @param {boolean} futureStatus - 未来（点击“确认”转换后）的状态
     */
    @bind
    @debounce(200)
    async handleSignUpStatusChange() {
      const status = await this.mutationTrainClassOnSheles.changeStatusAndTime()
      if (status.isSuccess()) {
        this.$message.success('操作成功')
        await this.searchBase()
      } else {
        this.$message.error(status?.errors?.[0]?.message || '操作失败')
      }
    }

    // 表格全屏
    @bind
    @debounce(200)
    async tableScreen() {
      //   判断浏览器是否支持放大
      if (screenfull.isEnabled) {
        // // 检测是否全屏
        // if (screenfull.isFullscreen) {
        //   this.isFullscreen = false
        //   // 退出全屏
        //   screenfull.exit()
        // } else {
        //   const el = document.querySelector('.f-p15')
        //   this.isFullscreen = true
        //   //   进入全屏
        //   screenfull.request(el)
        // }
        if (screenFull.isEnabled) {
          await screenFull.toggle(document.documentElement)
        } else {
          console.error('Fullscreen is not supported')
        }
      } else {
        this.$message.error('当前浏览器不支持全屏')
      }
    }

    async openExportList() {
      if (this.isOnlineClass) {
        this.exportType = 1
        await this.doExportList()
      } else {
        this.ExportDrawerRef.open()
      }
    }

    // 导出
    async confirmOption(val: number) {
      this.exportType = val
      this.taskName = this.exportType === 1 ? '培训方案' : '培训方案期别明细'
      await this.doExportList()
      this.ExportDrawerRef.showDrawer = false
    }

    /**
     * 导出培训方案
     */
    async doExportScheme() {
      const res = await this.QueryTrainClassCommodityList.exportCommoditySkuInServicer(
        this.trainSchemeQueryParam,
        this.sortPolicy
      )
      return res
    }

    /**
     * 导出期别数据
     */
    async doExportIssue() {
      const res = await this.QueryTrainClassCommodityList.exportIssueCommoditySkuInServicer(
        this.trainSchemeQueryParam,
        this.sortPolicy
      )
      return res
    }

    /**
     * 导出数据
     */
    async doBeforeExportList() {
      const sortRequestM = new CommoditySkuSortRequestv2()
      sortRequestM.sortField = CommoditySkuSortField.ON_SHELVE_TIME
      sortRequestM.policy = SortPolicy.DESC
      this.sortPolicy.push(sortRequestM)
      let res
      if (this.exportType === 1) {
        res = await this.doExportScheme()
      } else {
        res = await this.doExportIssue()
      }
      // const res = await this.QueryTrainClassCommodityList.exportCommoditySkuInServicer(
      //   this.trainSchemeQueryParam,
      //   this.sortPolicy
      // )
      return res
    }

    // 导出列表数据
    @bind
    @debounce(200)
    async doExportList() {
      //   console.log('导出列表数据')
      //   loading效果开启
      this.exportLoading = true
      this.getPageQueryParams()
      const res = await this.doBeforeExportList()
      //   loading效果关闭
      this.exportLoading = false
      if (!res?.status?.isSuccess()) {
        this.$message.error('导出列表请求失败！')
      } else {
        this.isExport = true
        this.uiConfig.exportDialog = true
        if (this.exportType === 1) {
          this.taskName = '培训方案'
          this.taskPath = '/training/task/exporttask'
          this.asyncTaskType = 'exportCommodity'
        } else {
          this.taskName = '培训方案期别明细'
          this.taskPath = '/training/task/exporttask'
          this.asyncTaskType = 'exportCommodityIssue'
        }
      }
    }

    /**
     * 删除培训方案
     */
    @bind
    @debounce(200)
    async deleteScheme(row: UITrainClassCommodityDetail) {
      if (row.isResourceEnabled === false)
        return this.$confirm('方案正在处理中，处理成功后可删除。', '系统提醒', {
          confirmButtonText: '确定',
          type: 'warning',
          showCancelButton: false
        })
      this.loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        // 校验是否允许删除：培训班不存在交易记录【不存在个人订单】
        let enableDelete = true
        const page = new Page()
        page.pageNo = 1
        page.pageSize = 10
        const orderQuery = new QueryOrderListVo()
        orderQuery.commoditySkuIdList = [row.commoditySkuId]
        const orderResponse = await this.QueryOrder.queryOrderList(page, orderQuery, false)
        if (orderResponse.length) {
          enableDelete = false
        }
        if (!enableDelete) {
          this.$alert('当前培训班已存在交易记录，不可删除', '系统提醒', {
            confirmButtonText: '知道了',
            type: 'warning',
            callback: (action) => {
              console.log(action)
            }
          })
        } else {
          this.MutationTrainClassCommodityClass.schemeId = row.schemeId
          const status = await this.MutationTrainClassCommodityClass.doDeleteTrainClass()
          if (status.isSuccess()) {
            const asyncGetStatus = () =>
              new Promise((resolve, reject) => {
                const timerFn = async () => {
                  //
                  if (!this.isActivated) return
                  const statusRes = await this.QueryTrainClassCommodityList.querySchemeStatus(row.schemeId)
                  if (
                    statusRes?.lastTransactionStep === 5 &&
                    (!statusRes?.errors || statusRes?.errors?.length === 0) &&
                    !statusRes?.hangUp &&
                    !statusRes?.recalculating
                  ) {
                    clearTimeout(this.timer)
                    resolve('操作成功')
                  } else if (statusRes?.hangUp) {
                    clearTimeout(this.timer)
                    const errMsg = '操作失败'
                    reject(errMsg)
                  } else if (statusRes?.errors?.length) {
                    const errMsg = statusRes.errors.find((err) => !!err)?.message || '操作失败'
                    clearTimeout(this.timer)
                    reject(errMsg)
                  } else {
                    this.timer = setTimeout(timerFn, 2000)
                  }
                }
                timerFn()
              })
            await asyncGetStatus().then(async () => {
              this.$message.success('操作成功')
              this.loading?.close()
              await this.searchBase()
            })
          } else {
            this.$message.error(status?.errors[0]?.message || '操作失败')
          }
        }
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        if (this.timer) clearTimeout(this.timer)
        this.loading?.close()
      }
    }

    /**
     * 获取培训方案类型
     */
    getSchemeType(row: UITrainClassCommodityDetail) {
      return this.schemeType.getSchemeType(row.schemeType, true)
      //   if (row.schemeType === 1) {
      //     return '培训班-选课规则'
      //   } else if (row.schemeType === 2) {
      //     return '培训班-自主选课'
      //   } else {
      //     return ''
      //   }
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: UITrainClassCommodityDetail, type: string): string {
      if (row.skuValueNameProperty[type]?.skuPropertyName) {
        const value = row.skuValueNameProperty[type].skuPropertyName
        const valuesArr = value.split('/'),
          lastIndex = valuesArr.length - 1
        return type === 'trainingMajor' && !row.isSocietyIndustry ? valuesArr[lastIndex] : value
      }
      return ''
    }

    /**
     * 处理排序 - 最后修改时间
     */
    async handleSortChange(column: any) {
      console.log('start sort', column.prop, column.order)
      if (column?.prop === 'commodityLastEditTime') {
        if (!column.order) {
          // 按发布时间降序排
          console.log('默认按发布时间降序排')
          this.sortPolicy = new Array<CommoditySkuSortRequestv2>()
        }
        if (column.order === TableSort.DESC) {
          // 按最后修改时间降序排
          console.log('按最后修改时间降序排')
          this.sortPolicy = new Array<CommoditySkuSortRequestv2>()
          const option = new CommoditySkuSortRequestv2()
          option.sortField = CommoditySkuSortField.LAST_EDIT_TIME
          option.policy = SortPolicy.DESC
          this.sortPolicy.push(option)
        }
        if (column.order === TableSort.ASC) {
          // 按最后修改时间升序排
          console.log('按最后修改时间升序排')
          this.sortPolicy = new Array<CommoditySkuSortRequestv2>()
          const option = new CommoditySkuSortRequestv2()
          option.sortField = CommoditySkuSortField.LAST_EDIT_TIME
          option.policy = SortPolicy.ASC
          this.sortPolicy.push(option)
        }
      }
      await this.searchBase()
    }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.jobLevel = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.studyPeriodId = ''
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.pharmacistIndustry = [] as string[]
    }

    updateTrainingCategory(val: string) {
      if (val) {
        this.localSkuProperty.positionCategory = ''
      }
    }

    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const jobLevel = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const trainingObject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const positionCategory = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.POSITION_CATEGORY)
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.jobLevel = jobLevel > -1
      this.skuVisible.trainingObject = trainingObject > -1
      this.skuVisible.positionCategory = positionCategory > -1
    }

    /**
     * 响应组件行业Id集合传参
     */
    async handleIndustryInfos(values: any) {
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.occupationalHealthId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
      this.envConfig.yshyId = values.medicineIndustryId || ''
      // 仅当单个行业，需要默认选中
      //   if (this.industrySelect?.industryOptions?.length === 1) {
      //     this.localSkuProperty.industry = this.industrySelect.industryOptions[0].id
      //   }
    }

    //选择学段
    updateStudyPeriod(val: string) {
      this.localSkuProperty.studyPeriodId = val
      this.localSkuProperty.subjectId = ''
      this.trainSchemeQueryParam.skuPropertyRequest.discipline = []
    }

    //选择学科
    updateSubject(val: string) {
      this.localSkuProperty.subjectId = val
    }

    /**
     * 是否处理中
     */
    getSchemeIsProcessing(item: UITrainClassCommodityDetail) {
      return (item?.lastTransactionStep < 5 || item?.recalculating) && !item?.hasError
    }

    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }

    beforeDestroy() {
      this.isActivated = false
      clearTimeout(this.timer)
      this.loading?.close()
    }

    deactivated() {
      this.isActivated = false
      clearTimeout(this.timer)
      this.loading?.close()
    }

    clearSubject() {
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.studyPeriodId = ''
      this.trainSchemeQueryParam.skuPropertyRequest.discipline = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.learningPhase = new Array<string>()
    }

    isNeedDataSync(needDataSync: boolean) {
      if (!needDataSync && isBoolean(needDataSync)) {
        return '不同步'
      } else {
        // 旧数据默认显示同步
        return '同步'
      }
    }

    /**
     * @description 保存滚动条位置
     * @mark localStorage
     * */
    setScrollPosition() {
      // 获取el-table的滚动容器元素
      const tableBodyWrapper = this.elTableRef.$el.querySelector('.el-table__body-wrapper')
      // 获取滚动条的垂直偏移量
      const scrollTop = tableBodyWrapper.scrollTop
      // 存储滚动条的垂直偏移量
      localStorage.setItem('scrollPosition', scrollTop)
    }

    /**
     * @description 使用滚动条位置
     * @mark localStorage
     * */
    useScrollPosition() {
      // 获取el-table的滚动容器元素
      const tableBodyWrapper = this.elTableRef.$el.querySelector('.el-table__body-wrapper')
      const position = Number(localStorage.getItem('scrollPosition'))
      this.delayChangeScrollTop(tableBodyWrapper, position)
    }

    /**
     * @description 清除滚动条位置
     * @mark localStorage
     * */
    clearScrollPosition() {
      localStorage.removeItem('scrollPosition')
    }

    /**
     * @description 清除滚动条并滚动回顶部
     * */
    clearScrollPositionBackTop() {
      this.clearScrollPosition()
      const tableBodyWrapper = this.elTableRef.$el.querySelector('.el-table__body-wrapper')
      this.delayChangeScrollTop(tableBodyWrapper, 1)
    }

    /**
     * @description 定位滚动条延迟滚动
     * @reason 用于防止滚动条定位后列表错位
     * */
    delayChangeScrollTop(tableBodyWrapper: Element, position: number) {
      // 判断边界
      tableBodyWrapper.scrollTop = position - 1 >= 0 ? position - 1 : position + 1
      setTimeout(() => {
        tableBodyWrapper.scrollTop = position
      }, 100)
    }

    /**
     * 校验方案上/下架 弹窗表单
     */
    async vaildOnShelvesFrom(row: UITrainClassCommodityDetail, futureStatus: boolean) {
      // this.MutationTrainClassCommodityClass.commoditySkuId = row.commoditySkuId
      this.mutationTrainClassOnSheles = new MutationTrainClassOnSheles(row.schemeId, futureStatus)
      await this.mutationTrainClassOnSheles.querySchemeConfig().then(async () => {
        // 配置开启方案时间或关闭方案时间，表明配置方案开启时间范围-改为非必填无需校验当前时间
        const openPlanTime = new Date(this.mutationTrainClassOnSheles.onShelvePlanTime).getTime()
        const closePlanTime = new Date(this.mutationTrainClassOnSheles.offShelvePlanTime).getTime()
        const myDate = Date.now()
        // （临时）当配置的计划时间小于当前时间，立即上下架状态时清空对应时间
        if (openPlanTime && openPlanTime < myDate) {
          this.mutationTrainClassOnSheles.onShelvePlanTime = ''
        }
        if (closePlanTime && closePlanTime < myDate) {
          this.mutationTrainClassOnSheles.offShelvePlanTime = ''
        }
        // 如果有下架时间，没有上架时间，则下架时间一起清空
        if (!openPlanTime && closePlanTime) {
          this.mutationTrainClassOnSheles.onShelvePlanTime = ''
          this.mutationTrainClassOnSheles.offShelvePlanTime = ''
        }
        this.handleSignUpStatusChange()
      })
    }

    /**
     * 是否为导出任务的标识。
     * 该变量用于指示当前任务是否与导出操作相关。
     */
    isExport = true

    /**
     * 当前任务的名称。
     * 该变量存储了任务的描述性名称，用于标识任务的用途或内容。
     */
    taskName = '培训方案'

    /**
     * 当前任务的路径。
     * 该变量定义了任务相关的接口路径或资源路径，用于请求或定位任务资源。
     */
    taskPath = '/training/task/exporttask'

    /**
     * 异步任务的类型。
     * 该变量用于指定异步任务的具体类型，通常用于区分不同的后台任务处理逻辑。
     */
    asyncTaskType = 'exportCommodity'

    /**
     * 批量更新方案抽屉显示
     */
    batchUpdateDrawerVisible = false

    /**
     * 批量更新方案
     */
    doBatchUpdate() {
      this.batchUpdateDrawerVisible = true
    }

    /**
     * 展示导入批量更新方案弹窗
     */
    showImportBatchUpdateSchemeDialog() {
      this.isExport = false
      this.uiConfig.exportDialog = true
      this.taskName = '批量更新方案'
      this.taskPath = '/training/task/importtask'
      this.asyncTaskType = 'batchUpdateScheme'
    }

    tabChange() {
      this.searchBase()
    }
  }
</script>

<style scoped lang="scss">
  .m-table {
    width: 100%;
    color: #444;
    height: auto;
  }
</style>
