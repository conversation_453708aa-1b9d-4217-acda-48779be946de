import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  CourseOfCourseTrainingOutlineRequest,
  StudentCourseLearningRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import { Page } from '@hbfe/common'
import { cloneDeep } from 'lodash'
/**
 * 课程大纲下必学课程学习信息
 */
export class OutlineCompulsoryCourseLearningInfo {
  // 课程大纲id
  outlineId = ''
  // 已学习课程列表
  learnedCourseList: CourseInfo[] = []
  // 所有课程列表
  allCourseList: CourseInfo[]
  // 已学课程总学时
  get learnedTotalPeriod() {
    return (
      this.learnedCourseList?.reduce((prev, cur) => {
        return CalculatorObj.add(prev, cur.coursePeriod || 0)
      }, 0) || 0
    )
  }
  // 所有课程总学时
  get allTotalPeriod() {
    return (
      this.allCourseList?.reduce((prev, cur) => {
        return CalculatorObj.add(prev, cur.coursePeriod || 0)
      }, 0) || 0
    )
  }
}

/**
 * 课程信息
 */
export class CourseInfo {
  // 课程id
  courseId = ''
  // 课程选课学时
  coursePeriod = 0

  constructor(courseId: string, coursePeriod: number) {
    this.courseId = courseId
    this.coursePeriod = coursePeriod
  }
}

/**
 * @description 批量课程查询工具类
 */
class BatchCourseQueryUtil {
  /**
   * 根据学员学号查询必学课程列表
   * @param studentNo 学员学号
   */
  async queryCompulsoryCourseListByStudentNo(studentNo: string) {
    const result: OutlineCompulsoryCourseLearningInfo[] = []
    const request = new StudentCourseLearningRequest()
    request.studentNo = studentNo
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    request.courseOfCourseTrainingOutline.courseType = 1
    const response = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseOfAutonomousCourseLearningSceneInMyself(
      {
        page: new Page(1, 1),
        request
      }
    )
    const totalSize = response.data?.totalSize || 0
    if (response.status?.isSuccess() && response.data?.currentPageData?.length && totalSize) {
      const reqList = Array(Math.ceil(totalSize / 200)).fill('')
      const respList = await Promise.allSettled(
        reqList.map(async (item, index) => {
          const params = {
            page: new Page(index + 1, 200),
            request: cloneDeep(request)
          }
          return MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseOfAutonomousCourseLearningSceneInMyself(
            params
          )
        })
      )
      respList?.forEach(tmpResp => {
        if (tmpResp.status === 'fulfilled') {
          const status = tmpResp.value.status
          const data = tmpResp.value.data?.currentPageData
          if (status?.isSuccess() && data && data?.length) {
            data.forEach(item => {
              const outline = result.find(el => el.outlineId === item.courseOfCourseTrainingOutline?.outlineId)
              if (outline) {
                // 找到就在对象上直接修改
                if (item.studentCourse?.courseLearningStatus === 2) {
                  // 合格
                  outline.learnedCourseList.push(
                    new CourseInfo(item.course?.courseId, item.courseOfCourseTrainingOutline?.period)
                  )
                }
                outline.allCourseList.push(
                  new CourseInfo(item.course?.courseId, item.courseOfCourseTrainingOutline?.period)
                )
              } else {
                // 找不到就新建一个并push
                const opt = new OutlineCompulsoryCourseLearningInfo()
                opt.outlineId = item.courseOfCourseTrainingOutline?.outlineId
                opt.allCourseList = []
                opt.learnedCourseList = []
                // 找到就在对象上直接修改
                if (item.studentCourse?.courseLearningStatus === 2) {
                  // 合格
                  opt.learnedCourseList.push(
                    new CourseInfo(item.course?.courseId, item.courseOfCourseTrainingOutline?.period)
                  )
                }
                opt.allCourseList.push(
                  new CourseInfo(item.course?.courseId, item.courseOfCourseTrainingOutline?.period)
                )
                result.push(opt)
              }
            })
          }
        }
      })
    }
    return result
  }
}

export default BatchCourseQueryUtil
