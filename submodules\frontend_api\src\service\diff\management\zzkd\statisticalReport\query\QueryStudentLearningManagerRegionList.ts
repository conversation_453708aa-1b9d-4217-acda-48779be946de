import exportMsgateway from '@api/platform-gateway/diff-ms-data-export-front-gateway-DataExportBackstage'
import {
  StudentSchemeLearningRequestVo,
  SyncResultEnmu
} from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
import {
  default as MsSchemeQueryFrontGatewayCourseLearningBackstage,
  SchemeConfigResponse,
  StudentSchemeLearningRequest,
  UserRequest,
  StudentSchemeLearningResponse,
  StudentSchemeLearningResponsePage
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { QueryStudentLearningManagerRegionList } from '@api/service/management/statisticalReport/query/QueryStudentLearningManagerRegionList'
import { Page, Response } from '@hbfe/common'
import { ConnectManageSystemRequest } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import SkuPropertyConvertUtilsV2 from '@api/service/management/train-class/Utils/SkuPropertyConvertUtilsV2'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import MsBasicDataQueryBackstageGateway, {
  StudentInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { getStudentInfoInSubProject } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage/graphql-importer'
import QueryDasicdata, {
  BusinessDataDictionaryCodeRequest,
  BusinessDataDictionaryResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
import { getBusinessDataDictionaryInSubProject } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage/graphql-importer'
import tradeQueryGateway, {
  OrderResponse,
  RegionModel
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { getOrderInServicer } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage/graphql-importer'
import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
import studentCourseLearningQuery, {
  SimulateStudentSchemeLearningRequest,
  StudentTrainingResultSimulateRequest,
  StudentTrainingResultSimulateResponse
} from '@api/platform-gateway/student-course-learning-query-back-gateway'
import ZzkdSchemeLearningMsGateway, {
  ZZKDStudentSchemeLearningResponse
} from '@api/diff-gateway/platform-jxjypxtypt-zzkd-school'
import { StudentLearningStaticsVoDiff } from '@api/service/diff/management/zzkd/statisticalReport/vo/StudentLearningStaticsVo'
import { StudentLearningStaticsVo } from '@api/service/management/statisticalReport/query/vo/StudentLearningStaticsVo'

export default class QueryStudentLearningManagerRegionListDiff extends QueryStudentLearningManagerRegionList {
  /**
   * 列表
   */
  studentSchemeLearningList = new Array<ZZKDStudentSchemeLearningResponse>()
  /**
   * 学员学习统计列表
   */
  async listRegionLearningReportFormsInServicerDiff(
    page: Page,
    filter: StudentSchemeLearningRequestVo
  ): Promise<Array<StudentLearningStaticsVoDiff>> {
    try {
      const data = await this.listRegionLearningReportFormsInServicer(page, filter)
      return this.enrichWithCertificateNumber(data)
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，listRegionLearningReportFormsInServicer',
        e
      )
    }
  }
  /**
   * 超管网授
   */
  async pageStudentSchemeLearningInServicerManageRegion(page: Page, filter: StudentSchemeLearningRequestVo) {
    const data = await ZzkdSchemeLearningMsGateway.pageStudentSchemeLearningInServicerManageRegion({
      page: page,
      request: filter
    })
    this.studentSchemeLearningList = data?.data?.currentPageData || new Array<ZZKDStudentSchemeLearningResponse>()
    const studentSchemeLearningResponsePage = new Response<StudentSchemeLearningResponsePage>()
    return Object.assign(studentSchemeLearningResponsePage, data)
  }
  /**
   * 导出
   */

  async exportStudentSchemeLearningExcelInServicerManageRegion(param: StudentSchemeLearningRequestVo) {
    return await ZzkdSchemeLearningMsGateway.exportStudentSchemeLearningExcelInServicerManageRegion(param)
  }
  /**
   * 获取证书编号
   */
  private enrichWithCertificateNumber(data: Array<StudentLearningStaticsVo>): Array<StudentLearningStaticsVoDiff> {
    return data.map((item) => {
      const tmp = Object.assign(new StudentLearningStaticsVoDiff(), item)
      const studentSchemeLearningInfo = this.studentSchemeLearningList.find((res) => res.studentNo === item.studentNo)
      if (studentSchemeLearningInfo) {
        tmp.certificateNumber = studentSchemeLearningInfo.certificateNumber
      }
      return tmp
    })
  }
}
