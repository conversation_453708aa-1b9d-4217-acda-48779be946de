import { Response } from '@hbfe/common'
class Param {
  key: string
  type: string
}
/**
 *  /**
 * 示例： graph传入
 * mutation issueElectronicInvoice($invoiceId: String) {
        issueElectronicInvoice(invoiceId: $invoiceId) {
                _ALL_
            }
        }
 * 重写之后
 mutation issueElectronicInvoice($invoiceId0: String,$invoiceId1: String) {
            invoiceFn0:issueElectronicInvoice(invoiceId: $invoiceId0) { _ALL_ }
            invoiceFn1:issueElectronicInvoice(invoiceId: $invoiceId1) { _ALL_ }
        }
 */

/**
 * 重写graph类
 * 仅支持graph入参为一个的情况下
 * @param T 返回值类型
 * @param Y 入参类型
 */
export class RewriteGraph<T, Y> {
  /**
   * graph语句
   */
  private graphStr = ''
  /**
   * 入参前字符串 示例： mutation issueElectronicInvoice(
   */
  private headerStr = ''
  /**
   * 拿到入参数据，后续进行循环   示例：{ key:$invoiceId0, type:String }
   */
  private param = new Param()
  /**
   * 函数体
   *  issueElectronicInvoice(invoiceId: $invoiceId) {
   *            _ALL_
   *    }
   */
  private fn = ''
  /**
   * 微服务里的 _commonQuery 函数
   */
  private commonQuery = new Function()
  /**
   * 请求后返回值
   * 做入参下标映射值
   */
  indexMap = new Map<number, T>()
  /**
   * 请求后返回值
   * 做入参值映射返回值
   */
  itemMap = new Map<any, T>()
  /**
   * 实例化
   * @param _commonQuery 微服务里的_commonQuery函数
   * @param graph graph值 从GraphqlImporter中拿
   */
  constructor(_commonQuery: Function, graph: unknown) {
    this.graphStr = graph as string
    this.commonQuery = _commonQuery
    /**
     * 1. 截取入参之前的字符串数据
     */
    const index = this.graphStr.indexOf('(')
    /** 示例截取后的字符串应该为  mutation issueElectronicInvoice(   */
    this.headerStr = this.graphStr.substring(0, index + 1)
    /**
     * 去除头部后的graph
     */
    const Graph = this.graphStr.substring(index + 1, this.graphStr.length)
    /**
     * 2. 截取入参的字符串
     */
    const paramIndex = Graph.indexOf(')')
    const paramStr = Graph.substring(0, paramIndex)
    const splitParamStr = paramStr.split(':')
    this.param.key = splitParamStr[0].replace(/\s+/g, '')
    this.param.type = splitParamStr[1].replace(/\s+/g, '')
    /**
     * 3. 截取函数体  tips：边角料在拼接的时候做
     */
    const startIndex = this.graphStr.indexOf('{')
    const endIndex = this.graphStr.indexOf('}')
    this.fn = this.graphStr.substring(startIndex + 4, endIndex + 1)
    if (!this.fn.includes('_ALL_')) {
      this.fn = this.fn.split('}')[0]
    }
  }
  /**
   * 只获取graph字符串
   * @param length 需要的长度
   */
  private getNewGraph(length: number) {
    if (length === 0) {
      console.error('想重组graph你倒是传长度给我啊')
      console.error(this.fn)
      return ''
    }
    let newGraph = ''
    newGraph = this.headerStr
    /**
     * 拼参数
     */
    for (let i = 0; i < length; i++) {
      if (length - 1 == i) {
        newGraph += this.param.key + i + ':' + this.param.type
      } else {
        newGraph += this.param.key + i + ':' + this.param.type + ','
      }
    }
    newGraph += '){'
    /**
     * 拼函数体
     */
    for (let i = 0; i < length; i++) {
      const splitFn = this.fn.split(this.param.key)
      newGraph += 'fn' + i + ':' + splitFn[0] + this.param.key + i + splitFn[1]
    }
    newGraph += '}'
    return newGraph
  }

  /**
   * 请求
   * @param param 入参
   * @param itemKey 需要用入参数组对象里的哪个字段做itemMap字段 只有param是数组对象时可使用
   */
  async request(param: Array<Y>, itemKey?: string) {
    if (param.length === 0) {
      return
    }
    const privateParam = {}
    for (let i = 0; i < param.length; i++) {
      const element = param[i]
      privateParam[this.param.key.split('$')[1] + i] = element
    }
    const result = await this.commonQuery(this.getNewGraph(param.length), privateParam)
    if (param.length === 1) {
      this.indexMap.set(0, result.data)
      if (itemKey) {
        this.itemMap.set(param[0][itemKey], result.data)
      } else {
        this.itemMap.set(param[0], result.data)
      }
      return result
    } else {
      for (const key in result.data) {
        if (Object.prototype.hasOwnProperty.call(result.data, key)) {
          const element = result.data[key]
          const index = parseInt(key.match(/\d+$/gi)[0])
          this.indexMap.set(index, element)
          if (itemKey) {
            this.itemMap.set(param[index][itemKey], element)
          } else {
            this.itemMap.set(param[index], element)
          }
        }
      }
      return result
    }
  }
  /**
   * 根据入参的值下标返回结果
   * @param index 入参的下标
   */
  getParamIndexData(index: number): T {
    return this.indexMap.get(index)
  }
  /**
   * 根据入参的值返回结果
   * @param index 入参的值
   */
  getParamData(item: any): T {
    return this.itemMap.get(item)
  }
}
