<route-meta>
{
"isMenu": true,
"title": "学员管理",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main>
    <template
      v-if="$hasPermission('participantsManagement')"
      desc="查询"
      actions="created,currentPageChange,queryPageList,searchBase"
    >
      <!-- hasPermission（key值） desc 描述 actions 页面进来触发的操作-->
      <div class="f-p15">
        <el-card shadow="never" class="m-card f-mb15">
          <!--条件查询-->
          <hb-search-wrapper @reset="restQuery" class="m-query is-border-bottom">
            <el-form-item label="姓名">
              <el-input v-model="studentQuery.userName" clearable placeholder="请输入姓名" />
            </el-form-item>
            <el-form-item label="登录账号" v-if="queryShowLoginAccount.isShowLoginAccount">
              <el-input v-model="studentQuery.loginAccount" clearable placeholder="请输入请输入省平台ID" />
            </el-form-item>
            <el-form-item label="证件号">
              <el-input v-model="studentQuery.idCard" clearable placeholder="请输入证件号" />
            </el-form-item>
            <el-form-item label="手机号">
              <el-input v-model="studentQuery.phone" clearable placeholder="请输入手机号" />
            </el-form-item>
            <el-form-item label="人员地区">
              <biz-region-cascader
                :check-strictly="true"
                placeholder="请选择地区"
                v-model="studentQuery.regionPathList"
              ></biz-region-cascader>
            </el-form-item>
            <el-form-item label="工作单位">
              <el-input v-model="studentQuery.companyName" clearable placeholder="请输入工作单位" />
            </el-form-item>
            <el-form-item label="注册时间">
              <el-date-picker
                v-model="date"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="起始时间"
                end-placeholder="结束时间"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="注册来源">
              <el-select v-model="studentQuery.sourceType" clearable filterable placeholder="请选择注册来源">
                <el-option
                  v-for="option in AccountSourceTypes"
                  :key="option.code"
                  :value="option.code"
                  :label="option.desc"
                ></el-option>
              </el-select>
            </el-form-item>
            <template slot="actions">
              <el-button type="primary" @click="searchBase" v-loading="loading">查询</el-button>
              <!--如果方法是跳转页面的 要标注页面的路径 不是标注页面跳转的方法-->
              <template
                v-if="$hasPermission('queryTask')"
                desc="导出任务查看"
                actions="exportStudent,@hbfe/jxjy-admin-task/src/exporttask/index.vue"
              >
                <el-button @click="exportStudent">导出学员</el-button>
              </template>
            </template>
          </hb-search-wrapper>
          <!--表格-->
          <el-table stripe :data="tableData" class="m-table" v-loading="loading">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="姓名" min-width="120" fixed="left">
              <template slot-scope="scope">{{ scope.row.userName }}</template>
            </el-table-column>
            <el-table-column
              label="登录账号"
              min-width="120"
              fixed="left"
              v-if="queryShowLoginAccount.isShowLoginAccount"
            >
              <template slot-scope="scope">{{ scope.row.loginAccount || '-' }}</template>
            </el-table-column>
            <el-table-column label="证件号" min-width="180">
              <template slot-scope="scope">{{ scope.row.idCard }}</template>
            </el-table-column>
            <el-table-column label="性别" min-width="80">
              <template slot-scope="scope">{{ getGender(scope.row.gender) }}</template>
            </el-table-column>
            <el-table-column label="手机号" min-width="140">
              <template slot-scope="scope">{{ scope.row.phone }}</template>
            </el-table-column>
            <el-table-column label="地区" min-width="240">
              <template slot-scope="scope">{{ getArea(scope.row.region) }}</template>
            </el-table-column>
            <el-table-column label="工作单位" min-width="280">
              <template slot-scope="scope">{{ scope.row.companyName }}</template>
            </el-table-column>
            <el-table-column label="注册来源" min-width="280">
              <template slot-scope="scope">{{ scope.row.sourceTypeName }}</template>
            </el-table-column>
            <el-table-column label="注册时间" min-width="180">
              <template slot-scope="scope">{{ scope.row.createTime }}</template>
            </el-table-column>
            <el-table-column label="操作" min-width="100" align="center" fixed="right">
              <template
                slot-scope="scope"
                v-if="$hasPermission('detali')"
                desc="详情"
                actions="@hbfe/jxjy-admin-user/src/student/detail.vue"
              >
                <el-button type="text" size="mini" @click="goDetail(scope.row.userId)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page"> </hb-pagination>
        </el-card>
      </div>
    </template>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import QueryStudentList from '@api/service/diff/management/qztg/user/student/QueryStudentList'
  import QueryStudentPage from '@api/service/management/user/query/student/QueryStudentPage'
  import UserModule from '@api/service/management/user/UserModule'
  import { UiPage } from '@hbfe/common'
  import StudentQueryVo from '@api/service/diff/management/qztg/user/student/model/StudentQueryVo'
  import bizRegionCascader from '@hbfe/jxjy-admin-components/src/biz/biz-region-cascader.vue'
  import AccountSourceTypes from '@api/service/diff/management/qztg/user/student/enums/AccountSourceTypes'
  import StudentUserInfoVo from '@api/service/diff/management/qztg/user/student/model/StudentUserInfoVo'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'
  @Component({
    components: { bizRegionCascader }
  })
  export default class extends Vue {
    page: UiPage = new UiPage()
    queryStudentList = new QueryStudentList()
    StudentQueryVo = new StudentQueryVo()
    studentQuery = {
      /**
       * 用户名称（模糊）,
       */
      userName: '',
      /**
       * 证件号
       */
      idCard: '',
      /**
       * 手机号
       */
      phone: '',
      /**
       * 登录账号
       */
      loginAccount: '',
      /*
      人员地区
    */
      regionPathList: new Array<string>(),

      /*
      工作单位
    */
      companyName: '',
      /*
        注册开始时间
    */
      beginTime: '',
      /*
      注册结束时间
     */
      endTime: '',
      /**
       * 注册来源
       */
      sourceType: ''
    }
    date = [] as any
    /**
     * 列表返回数据
     */
    tableData = [] as StudentUserInfoVo[]
    /**
     * 注册来源Map
     */
    AccountSourceTypes = AccountSourceTypes.list()
    /**
     * 表格loading
     */
    loading = false
    constructor() {
      super()
      this.page = new UiPage(this.queryPageList, this.queryPageList)
    }
    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount
    async exportStudent() {
      //   this.StudentQueryVo = Object.assign(new StudentQueryVo(), this.StudentQueryVo)
      await this.queryStudentList.export(this.StudentQueryVo)
      const confirmText = ['导出成功，是否前往下载数据？', '下载入口：导出任务查看-学员']
      const newDatas = []
      const h = this.$createElement
      for (const i in confirmText) {
        newDatas.push(h('p', null, confirmText[i]))
      }
      this.$confirm('提示', {
        title: '提示',
        message: h('div', null, newDatas),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      })
        .then(() => {
          this.$router.push({
            path: '/training/task/exporttask',
            query: { type: 'exportStudent' }
          })
        })
        .catch((e) => {
          console.log(e)
        })
    }
    async currentPageChange() {
      this.page.currentChange(this.page.pageNo)
      this.StudentQueryVo.userName = this.studentQuery.userName
      this.StudentQueryVo.idCard = this.studentQuery.idCard
      this.StudentQueryVo.phone = this.studentQuery.phone
      this.StudentQueryVo.regionPathList = this.studentQuery.regionPathList
      this.StudentQueryVo.companyName = this.studentQuery.companyName
      this.StudentQueryVo.beginTime = this?.date[0]
      this.StudentQueryVo.endTime = this?.date[1]
      this.StudentQueryVo.sourceType = this.studentQuery.sourceType
      const res = await this.queryStudentList.queryStudentListByCondition(this.page, this.StudentQueryVo)
      if (res.status.code == 200) {
        this.tableData = res.data
      }
    }

    async created() {
      await this.init()
    }
    getArea(area: any) {
      let region = ''
      if (area) {
        if (area.provinceName && area.cityName && area.countyName) {
          region = area.provinceName + '/' + area.cityName + '/' + area.countyName
        } else if (area.provinceName && area.cityName) {
          region = area.provinceName + '/' + area.cityName
        } else if (area.provinceName) {
          region = area.provinceName
        }
      }

      return region
    }
    async init() {
      this.loading = true
      try {
        const res = await this.queryStudentList.queryStudentListByCondition(this.page, this.StudentQueryVo)
        if (res.status.code == 200) {
          this.tableData = res.data
          console.log(this.tableData)
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }
    getGender(gender: number) {
      switch (gender) {
        case -1:
          return '未知'
        case 0:
          return '女'
        case 1:
          return '男'
        default:
          return ''
      }
    }
    /**
     * 加载第一页
     */
    async searchBase() {
      this.page.pageNo = 1
      await this.queryPageList()
    }
    // 点击查询订单列表
    async queryPageList() {
      this.loading = true
      this.StudentQueryVo.userName = this.studentQuery.userName
      this.StudentQueryVo.loginAccount = this.studentQuery.loginAccount
      this.StudentQueryVo.idCard = this.studentQuery.idCard
      this.StudentQueryVo.phone = this.studentQuery.phone
      if (this.studentQuery.regionPathList[0] == '/10000') {
        this.studentQuery.regionPathList[0] = ''
      }
      this.StudentQueryVo.regionPathList = this.studentQuery.regionPathList
      this.StudentQueryVo.companyName = this.studentQuery.companyName
      this.StudentQueryVo.beginTime = this.date[0]
      this.StudentQueryVo.endTime = this.date[1]
      this.StudentQueryVo.sourceType = this.studentQuery.sourceType
      try {
        const res = await this.queryStudentList.queryStudentListByCondition(this.page, this.StudentQueryVo)
        if (res.status.code == 200) {
          this.tableData = res.data
        }
      } catch (e) {
        console.log()
      } finally {
        this.loading = false
      }
    }

    async restQuery() {
      this.studentQuery.userName = ''
      this.studentQuery.idCard = ''
      this.studentQuery.phone = ''
      this.studentQuery.regionPathList = []
      this.studentQuery.companyName = ''
      this.studentQuery.beginTime = ''
      this.studentQuery.endTime = ''
      this.date = []
      this.studentQuery.sourceType = ''
      this.page.pageNo = 1
      await this.currentPageChange()
    }

    goDetail(id: string) {
      this.$router.push('/training/user/student/detail/' + id)
    }
  }
</script>
