import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-salesman-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-salesman-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 启用业务员请求
 */
export class ChangeSalesmanStatusRequest {
  /**
   * 业务员id
   */
  salesmanId?: string
}

/**
 * @author: xucenhao
@time: 2024-08-13
@description: 创建业务员请求类
 */
export class CreateSalesmanRequest {
  /**
   * 姓名
   */
  name?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 备注
   */
  remark?: string
}

/**
 * 修改业务员请求
 */
export class UpdateSalesmanRequest {
  /**
   * 业务员id
   */
  id?: string
  /**
   * 姓名
   */
  name?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 备注
   */
  remark?: string
}

/**
 * <AUTHOR> [2023/7/11 20:54]
 */
export class GeneralResponse {
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * @author: xucenhao
@time: 2024-09-12
@description:
 */
export class CreateSalesmanResponse {
  /**
   * 销售员ID
   */
  salesmanId: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建业务员
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createSalesman(
    request: CreateSalesmanRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createSalesman,
    operation?: string
  ): Promise<Response<CreateSalesmanResponse>> {
    return commonRequestApi<CreateSalesmanResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 停用业务员
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableSalesman(
    request: ChangeSalesmanStatusRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disableSalesman,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用业务员
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableSalesman(
    request: ChangeSalesmanStatusRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableSalesman,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改业务员
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateSalesman(
    request: UpdateSalesmanRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateSalesman,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
