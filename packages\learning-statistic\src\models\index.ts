import { RegionRequest } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import {
  StudentSchemeLearningRequestVo,
  SyncResultEnmu
} from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
export default // sku绑定模型
class SchemeSkuProperty {
  year: string[] = []
  region: string[] = []
  industry = ''
  subjectType = ''
  trainingCategory = ''
  societyTrainingMajor: string[] = []
  // 学段
  learningPhase = ''
  // 学科
  discipline = ''
  constructionTrainingMajor = ''
  trainingObject = ''
  positionCategory = ''
  trainingResultPeriodBegin: number = null
  trainingResultPeriodEnd: number = null
  requirePeriodBegin: number = null
  requirePeriodEnd: number = null
  acquiredPeriodBegin: number = null
  acquiredPeriodEnd: number = null
  technicalGrade = ''
  syncResult: any = null
  trainingResultTimeBegin = ''
  trainingResultTimeEnd = ''
  registerTimeEnd = ''
  registerTimeBegin = ''
  companyName = ''
  schemeName = ''
  regionList: Array<string>
  schemeTypeInfo: Array<string> = []
  trainingResultList: Array<number> = []
  name: ''
  idCard: ''
  phone: ''
  loginAccount: ''
  periodId = ''
  issueName = ''
}
