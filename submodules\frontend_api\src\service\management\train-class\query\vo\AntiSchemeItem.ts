import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'

class AntiSchemeItem {
  /**
   * 方案id
   */
  schemeId?: string = ''
  /**
   * 方案名称
   */
  schemeName?: string = ''
  /**
   * 方案名称
   */
  sku?: SkuPropertyResponseVo = new SkuPropertyResponseVo()

  /**
   * 方案是否有配置反作弊
   */
  hasAntiConfig: boolean
  /**
   * 学时
   */
  period?: number | string = 0
  constructor(
    schemeId: string,
    schemeName: string,
    sku: SkuPropertyResponseVo,
    period?: number,
    hasAntiConfig?: boolean
  ) {
    this.schemeId = schemeId
    this.schemeName = schemeName
    this.sku = sku
    this.period = period
    this.hasAntiConfig = hasAntiConfig
  }
}

export default AntiSchemeItem
