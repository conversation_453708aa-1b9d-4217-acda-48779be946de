import Configuration from '@api/service/common/models/exam/paper/section/configuration/Configuration'
import { ExamConfigType } from '@api/service/customer/my-question/question-practice/models/enums'

/**
 * 大题项配置
 */
export class RandomConfigurationItem {
  /**
   * 配置项名称,可能是大题的名称
   */
  name?: string
  /**
   * 试题数量
   */
  count: number
  /**
   * 试题类型，0/1/2/3/4/5/6/7 未知/判断/单选/多选/填空/简答/父子/混合
   智能卷不支持混合题型
   */
  type: number
  /**
   * 分数分配类型
   1：平均分配
   2：独立分配
   */
  scoreWay: number
  /**
   * 单题用时, 单位：分
   如果试卷的计时方式是[单题计时]，则控制的是大题下每道题可作答的时间长度
   @since 1.23.0
   */
  singleAnswerableTimeLength = 0
  /**
   * 配置项总分
   */
  totalScore: number
  /**
   * 是否比率
   如果随机出题方式按试卷抽取则本项成员无效
   */
  ratio = false
}

class Random extends Configuration {
  getConfigType(): ExamConfigType {
    return ExamConfigType.RANDOM
  }

  /**
   * 大题项配置
   */
  configurationItems?: Array<RandomConfigurationItem>

  randomType = 0

  recursive = false

  randomWay = 0

  ratio = false
}

export default Random
