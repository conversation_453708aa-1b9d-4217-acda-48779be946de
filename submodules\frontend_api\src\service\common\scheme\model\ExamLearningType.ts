import LearningTypeBase from '@api/service/common/scheme/model/LearningTypeBase'
import { ExamMethodEnum } from '@api/service/common/enums/train-class/ExamMethodEnum'

/**
 * @description 考试学习方式
 */
class ExamLearningType extends LearningTypeBase {
  /**
   * 前置条件id
   */
  preconditionId = ''
  /**
   * 前置条件名称
   */
  preconditionName = ''
  /**
   * 考试说明
   */
  description = ''
  /**
   * 试卷模板id
   */
  paperPublishConfigureId = ''
  /**
   * 试卷模板名称
   */
  paperPublishConfigureName = ''
  /**
   * 考试场次名称
   */
  name = ''
  /**
   * 允许作答次数
   * @description 值-1代表不限次
   */
  allowCount = 0
  /**
   * 允许考试开始时间
   * @description date 同培训班的学习起止时填班级的学习开始
   */
  allowStartTime = ''
  /**
   * 允许考试结束时间
   * @description date 同培训班的学习起止时填班级的学习结束
   */
  allowEndTime = ''
  /**
   * 考试时长
   * @description 单位：秒数
   */
  timeLength = 0
  /**
   * 班级考试考核成绩
   */
  qualifiedScore = 0
  /**
   * 班级考试考核成绩
   */
  examPassScore = 0
  /**
   * 合格后是否允许继续作答
   */
  allowAnswerIfQualified = false
  /**
   * 开放题析
   */
  openDissects = false
  /**
   * 试题提交答案后是否可以重答
   */
  questionAgainAnswer = false
  /**
   * 试题呈现方式
   * @description 1 整卷
   */
  questionDisplay = 0
  /**
   * 多选题漏选得分方式
   * @description 0 不得分 1 得全部分数 2 得一半分数 3 每个选项按平均得分
   */
  multipleMissScorePattern = 0
  /**
   * 成绩是否公布
   */
  gradesWhetherHide = true
  /**
   * 考试是否纳入考核
   */
  isExamAssessed = false
  /**
   * 前置条件
   * 0 无 1 完成课程学习考核
   */
  preCondition = 0
  /**
   * 考试方式
   */
  examPattern = ExamMethodEnum.on_call_exam
}

export default ExamLearningType
