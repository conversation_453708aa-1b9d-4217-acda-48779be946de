import MutationCreateTrainClassCommodity from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'
import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
import Mockjs from 'mockjs'
import AssessSettingInfo from '@api/service/common/scheme/model/AssessSettingInfo'
import {
  IssueAssessTypeEnum,
  QuestionnaireAssessTypeEnum,
  TeachPlanAssessTypeEnum
} from '@api/service/common/scheme/enum/AssessType'
import MultipleAssessLearningTypeInfo from '@api/service/common/scheme/model/MultipleAssessLearningTypeInfo'
import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
import IssueCourseDetail from '@api/service/common/scheme/model/IssueCourseDetail'
import { QuestionnaireStatusEnum } from '@api/service/common/scheme/enum/QuestionnaireStatus'
import { QuestionnaireAppliedRangeTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'
import TeachingPlanItemsGroup from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/teaching-plan-items-groups/TeachingPlanItemsGroup'
import TeachingPlanItemGroupInfo from '@api/service/common/scheme/model/TeachingPlanItemGroupInfo'

/**
 * @description 操作方案对象
 */
class MutationTrainClass {
  /**
   * 方案复制时还原面授部分信息
   * @description 用于创建方案时，重置面授部分信息，包括期别、问卷等
   * @param createSchemeObj 创建方案模型
   */
  resolveInfoWhenCopy(createSchemeObj: MutationCreateTrainClassCommodity): void {
    // 期别id映射Map，<源期别id, 重新生成的期别id>
    const issueIdMap = new Map<string, string>()
    // 期别
    const issue = createSchemeObj.learningTypeModel.issue
    issue.learningTypeId = ''
    issue.configId = ''
    issue.assessId = ''
    issue.assessName = ''
    issue.issueConfigList.forEach((issueConfig) => {
      const originalIssueId = issueConfig.id
      issueConfig.operationType = OperationTypeEnum.create
      // 重置期别id后记录到映射Map
      issueConfig.id = Mockjs.Random.guid()
      issueIdMap.set(originalIssueId, issueConfig.id)
      issueConfig.configId = ''
      issueConfig.assessSettings = [] as AssessSettingInfo<IssueAssessTypeEnum>[]
      issueConfig.trainingConfigConfigureId = ''
      issueConfig.issueNo = ''
      issueConfig.trainingPointConfigId = ''
      issueConfig.relateTeachPlanLearning = new MultipleAssessLearningTypeInfo<TeachPlanAssessTypeEnum>()
      issueConfig.perQuestionnaireBackupMap = new Map<string, QuestionnaireConfigDetail>()
      issueConfig.issueCourseList.forEach((issueCourse) => {
        issueCourse.operationType = OperationTypeEnum.create
        issueCourse.id = IssueCourseDetail.customPrefixId + Mockjs.Random.guid()
        issueCourse.belongGroupInfo = new TeachingPlanItemGroupInfo()
        issueCourse.configId = ''
        issueCourse.assessSettings = [] as AssessSettingInfo[]
        issueCourse.teacherId = ''
      })
      issueConfig.teachingPlanItemsGroupsBackup = [] as TeachingPlanItemsGroup[]
    })
    // 问卷
    const questionnaire = createSchemeObj.learningTypeModel.questionnaire
    questionnaire.learningTypeId = ''
    questionnaire.configId = ''
    questionnaire.assessId = ''
    questionnaire.assessName = ''
    questionnaire.questionnaireConfigList.forEach((questionnaireConfig) => {
      questionnaireConfig.operationType = OperationTypeEnum.create
      questionnaireConfig.id = Mockjs.Random.guid()
      questionnaireConfig.uniqueKey = Mockjs.Random.guid()
      questionnaireConfig.configId = ''
      // AHJSPXPT-3244：复制的问卷，问卷状态默认为启用
      questionnaireConfig.status = QuestionnaireStatusEnum.enabled
      // 清空问卷前置条件信息
      questionnaireConfig.preconditionId = ''
      questionnaireConfig.preconditionName = ''
      questionnaireConfig.assessSettings = [] as AssessSettingInfo<QuestionnaireAssessTypeEnum>[]
      if (questionnaireConfig.appliedRangeType === QuestionnaireAppliedRangeTypeEnum.assign_issue) {
        const curIssueId = questionnaireConfig.curIssueId
        questionnaireConfig.curIssueId = issueIdMap.get(curIssueId)
      }
    })
  }

  /**
   * 方案更新时还原面授部分信息
   * @description 用于更新方案时，还原面授部分信息，包括期别、问卷等
   * @param updateSchemeObj 更新方案模型
   */
  resolveInfoWhenUpdate(updateSchemeObj: MutationCreateTrainClassCommodity): void {
    const issue = updateSchemeObj.learningTypeModel.issue.issueConfigList
    const questionnaire = updateSchemeObj.learningTypeModel.questionnaire.questionnaireConfigList
    issue.forEach((issueConfig) => {
      issueConfig.operationType = OperationTypeEnum.update
    })
    questionnaire.forEach((questionnaireConfig) => {
      questionnaireConfig.operationType = OperationTypeEnum.update
    })
  }
}

export default new MutationTrainClass()
