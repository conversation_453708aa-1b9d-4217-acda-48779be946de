import StepVo from '@api/service/customer/online-school-config/online-school-partal-config/query/vo/StepVo'
import { OfflineCollectiveRegisterConfigResponse } from '@api/ms-gateway/ms-servicer-series-v1'

/**
 * @description 线下集体报名配置
 */
class OfflineCollectiveRegisterSettingVo {
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 线下集体报名名称
   */
  title: string
  /**
   * 报名模板路径
   */
  templatePath: string
  /**
   * 线下集体报名的步骤
   */
  steps: Array<StepVo>
  // todo 是否与首页相同 相同则需要单独抽出来
  /**
   * 底部落款
   */
  footContent: string

  /**
   * 报名入口图片
   */
  enterPhoto = ''

  static from(response: OfflineCollectiveRegisterConfigResponse) {
    const offlineCollectiveRegisterSettingVo = new OfflineCollectiveRegisterSettingVo()
    offlineCollectiveRegisterSettingVo.enable = response.enabled
    offlineCollectiveRegisterSettingVo.title = response.title
    offlineCollectiveRegisterSettingVo.steps = response.steps?.map(item => StepVo.from(item))
    offlineCollectiveRegisterSettingVo.templatePath = response.templatePath
    // todo 待定
    offlineCollectiveRegisterSettingVo.footContent = response.footContent
    offlineCollectiveRegisterSettingVo.enterPhoto =
      (response?.registerPortalPicture?.length && response.registerPortalPicture[0].url) || ''
    return offlineCollectiveRegisterSettingVo
  }
}

export default OfflineCollectiveRegisterSettingVo
