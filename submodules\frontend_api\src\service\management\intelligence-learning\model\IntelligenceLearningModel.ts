import { ResponseStatus } from '@hbfe/common'
import MsAutolearning, {
  TimeRange,
  IntegerRange,
  OnlineSchoolSmartLearningServiceConfigRequest,
  CourseLearningConfigure,
  CourseQuizConfigure,
  ExamConfigure
} from '@api/ms-gateway/ms-autolearning-v1'
import { OnlineSchoolSmartLearningServiceConfigResponse } from '@api/ms-gateway/ms-autolearning-online-school-smart-learning-service-v1'
import Context from '@api/service/common/context/Context'
import { TimeModeEnum } from '@api/service/management/intelligence-learning/enum/TimeModeEnum'

export default class IntelligenceLearningModel {
  // 是否开启智能学习配置
  enable = true
  /**
   * 每天学习时间段
   */
  LearningDate: Array<TimeRange> = [{ min: '00:00:00', max: '23:59:59' }]
  /**
   * 每天不学习时间段
   */
  noLearningDate: Array<TimeRange> = new Array<TimeRange>()
  /**
   * 首次开始学习起始天数
   */
  firstLearningStartDay: number = undefined
  /**
   * 首次开始学习结束天数
   */
  firstLearningEndDay: number = undefined
  /**
   * 每天最多学习学时/小时数
   */
  everyDayMaxLearningHour: number = undefined
  /**
   * 每次最多学习学时/小时数
   */
  everyTurnMaxLearningHour: number = undefined
  /**
   * 时长方式
   * 0：按课程物理时长 1：按课程学习学时
   */
  timeMode: TimeModeEnum = TimeModeEnum.learning
  /**
   * 休息时间区间（分钟）
   */
  restRange: IntegerRange = new IntegerRange()
  /**
   * 课程测验时间区间（分钟）  测验规则
   */
  courseTestRange: IntegerRange = new IntegerRange()
  /**
   * 考试时间区间（分钟）  考试规则
   */
  examRange: IntegerRange = new IntegerRange()
  /**
   * 考试时长占比（占配置时长的比例）  考试规则
   */
  examTimeRatio: number = undefined

  /**
   * 转换
   */
  from(dto?: OnlineSchoolSmartLearningServiceConfigResponse) {
    this.enable = dto.courseLearningConfigure?.enable
    this.LearningDate = dto.courseLearningConfigure?.everyDayLearningTimeList || [{ min: '00:00:00', max: '23:59:59' }]
    this.noLearningDate = dto.courseLearningConfigure?.everyDayNotLearningTimeList || new Array<TimeRange>()
    this.firstLearningStartDay = dto.courseLearningConfigure?.firstLearningDayRange.min
    this.firstLearningEndDay = dto.courseLearningConfigure?.firstLearningDayRange.max

    this.restRange = {
      max: Math.floor(dto.courseLearningConfigure?.restTimeRange?.max / 60),
      min: Math.floor(dto.courseLearningConfigure?.restTimeRange?.min / 60)
    }
    this.courseTestRange = {
      max: Math.floor(dto.courseQuizConfigure?.answerTimeRange?.max / 60),
      min: Math.floor(dto.courseQuizConfigure?.answerTimeRange?.min / 60)
    }
    this.examRange = {
      max: Math.floor(dto.examConfigure?.answerTimeRange?.max / 60),
      min: Math.floor(dto.examConfigure?.answerTimeRange?.min / 60)
    }
    this.examTimeRatio = dto.examConfigure?.durationRatio
    this.timeMode = dto.courseLearningConfigure.ruleType
    if (this.timeMode === TimeModeEnum.physical) {
      this.everyDayMaxLearningHour = Math.floor(dto.courseLearningConfigure?.everyDayLearningTimeRange?.max / 3600)
      this.everyTurnMaxLearningHour = Math.floor(dto.courseLearningConfigure?.everyLearningTimeRange?.max / 3600)
    } else if (this.timeMode === TimeModeEnum.learning) {
      this.everyDayMaxLearningHour = Math.floor(dto.courseLearningConfigure?.everyDayLearningPeriodRange?.max)
      this.everyTurnMaxLearningHour = Math.floor(dto.courseLearningConfigure?.everyLearningPeriodRange?.max)
    }
    return this
  }
  to() {
    const req = new OnlineSchoolSmartLearningServiceConfigRequest()
    req.servicerId = Context.servicerInfo.id
    req.courseLearningConfigure = new CourseLearningConfigure()
    req.courseLearningConfigure.enable = this.enable
    req.courseLearningConfigure.everyDayLearningTimeList = this.LearningDate || [{ min: '00:00:00', max: '23:59:59' }]
    req.courseLearningConfigure.everyDayNotLearningTimeList = this.noLearningDate
    req.courseLearningConfigure.firstLearningDayRange = {
      max: this.firstLearningEndDay,
      min: this.firstLearningStartDay
    }

    req.courseLearningConfigure.ruleType = this.timeMode
    if (this.timeMode === TimeModeEnum.physical) {
      req.courseLearningConfigure.everyDayLearningTimeRange = {
        max: this.everyDayMaxLearningHour * 3600,
        min: this.everyDayMaxLearningHour * 3600
      }
      req.courseLearningConfigure.everyLearningTimeRange = {
        max: this.everyTurnMaxLearningHour * 3600,
        min: this.everyTurnMaxLearningHour * 3600
      }
    } else if (this.timeMode === TimeModeEnum.learning) {
      req.courseLearningConfigure.everyDayLearningPeriodRange = {
        max: this.everyDayMaxLearningHour,
        min: this.everyDayMaxLearningHour
      }
      req.courseLearningConfigure.everyLearningPeriodRange = {
        max: this.everyTurnMaxLearningHour,
        min: this.everyTurnMaxLearningHour
      }
    }
    req.courseLearningConfigure.restTimeRange = { max: this.restRange.max * 60, min: this.restRange.min * 60 }
    req.courseQuizConfigure = new CourseQuizConfigure()
    req.courseQuizConfigure.answerTimeRange = { max: this.courseTestRange.max * 60, min: this.courseTestRange.min * 60 }
    req.examConfigure = new ExamConfigure()
    req.examConfigure.answerTimeRange = { max: this.examRange.max * 60, min: this.examRange.min * 60 }
    req.examConfigure.durationRatio = 0.34
    return req
  }
}
