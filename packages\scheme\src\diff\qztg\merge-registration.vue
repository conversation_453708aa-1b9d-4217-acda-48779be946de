<route-params content="/:id"></route-params>
<route-meta>
{
"title": "合并报名管理"
}
</route-meta>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click.stop="goBack">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/scheme/manage' }">培训方案管理</el-breadcrumb-item>
      <el-breadcrumb-item>合并报名管理</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <basic-info v-if="show" :trainSchemeDetail.sync="schemeDetail.trainClassDetail" />
      <main-scheme-setting
        v-if="show"
        ref="mainSchemeSettingRef"
        :trainSchemeDetail.sync="schemeDetail.trainClassDetail"
        :associatedCommodityArr.sync="schemeDetail.associatedCommodityList"
      />
      <merge-registration-manage
        v-if="show"
        :trainSchemeDetail.sync="schemeDetail.trainClassDetail"
        :mergeCommodityArr.sync="schemeDetail.mergeCommodityList"
      />
      <div class="m-btn-bar is-sticky-1 f-tc">
        <el-button @click="goBack">取消</el-button>
        <el-button type="primary" @click="publishScheme">发布</el-button>
      </div>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
  import QueryTrainClassDetailClass from '@api/service/diff/management/qztg/train-class/query/QueryTrainClassDetailClass'
  import BasicInfo from '@hbfe/jxjy-admin-scheme/src/components/detail/basic-info.vue'
  import SchemeDetailUIModule from '@/store/modules-ui/scheme/SchemeDetailUIModule'
  import MainSchemeSetting from '@hbfe/jxjy-admin-scheme/src/diff/qztg/__components__/main-scheme-setting.vue'
  import MergeRegistrationManage from '@hbfe/jxjy-admin-scheme/src/diff/qztg/__components__/merge-registration-manage.vue'
  import MutationCreateTrainClassCommodity from '@api/service/diff/management/qztg/train-class/mutation/MutationCreateTrainClassCommodity'

  import { bind, debounce } from 'lodash-decorators'
  @Component({
    components: { BasicInfo, MainSchemeSetting, MergeRegistrationManage }
  })
  export default class extends Vue {
    /**
     * 基础信息
     */
    @Ref('mainSchemeSettingRef') mainSchemeSettingRef: MainSchemeSetting

    // 当前培训方案id
    currentSchemeId = ''
    // 培训方案详情
    schemeDetail: QueryTrainClassDetailClass

    /**
     * 是否展示组件
     */
    show = false

    /**
     * loading遮罩层
     */
    loading: any

    mutationCreateTrainClassCommodity = new MutationCreateTrainClassCommodity()

    load() {
      // TODO
    }

    /**
     * 页面初始化
     */
    async created() {
      this.currentSchemeId = (this.$route.params?.id as string) || ''
      if (this.currentSchemeId) {
        await this.getSchemeDetail()
      }
    }

    /**
     * 获取培训方案详情
     */
    async getSchemeDetail() {
      this.schemeDetail = new QueryTrainClassDetailClass()
      this.schemeDetail.commodityId = this.currentSchemeId
      // this.mockData()
      await this.schemeDetail.queryTrainClassDetail()
      // 保存到数据持久层
      SchemeDetailUIModule.setSchemeDetail(this.schemeDetail.trainClassDetail)
      console.log('schemeDetail', this.schemeDetail)
      this.show = true
    }

    /**
     * 返回列表
     */
    goBack() {
      this.$router.push('/training/scheme/manage')
    }

    /**
     * 发布
     */
    @bind
    @debounce(200)
    async publishScheme() {
      const associatedCommodityList = this.mainSchemeSettingRef.associatedCommodityList || []
      console.log(associatedCommodityList, 'associatedCommodityList')
      try {
        this.loading = this.$loading({
          lock: true,
          text: '加载中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.8)'
        })
        this.mutationCreateTrainClassCommodity.commoditySkuId = this.schemeDetail.trainClassDetail.commoditySkuId
        this.mutationCreateTrainClassCommodity.associatedCommodityList = associatedCommodityList
        this.mutationCreateTrainClassCommodity.originalAssociatedCommodityList =
          this.schemeDetail.associatedCommodityList
        console.log(this.mutationCreateTrainClassCommodity)
        const res = await this.mutationCreateTrainClassCommodity.updateMergeRelation()
        console.log(res, 'res')
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.goBack()
        } else {
          const errMsg = (res?.message || '操作失败') as string
          this.$message.warning(errMsg)
        }
      } catch (e) {
        const errMsg = e?.message || '操作失败'
        console.log('加载失败: ', e)
        this.$message.warning(errMsg)
      } finally {
        this.loading.close()
      }
    }
  }
</script>
