<template>
  <el-drawer title="添加年度" :visible.sync="openDialog" size="700px" custom-class="m-drawer" :destroy-on-close="true">
    <div class="drawer-bd">
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="dialogRef" :rules="rules" :model="trainingTimeRange" label-width="auto" class="m-form f-mt30">
            <el-form-item label="年度：" prop="year">
              <biz-year-select v-model="trainingTimeRange.year" placeholder="请选择培训年度"></biz-year-select>
            </el-form-item>
            <el-form-item label="设置年度培训时间：" prop="time">
              <el-date-picker
                type="daterange"
                v-model="trainingTimeRange.time"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="cencel()">取消</el-button>
              <el-button type="primary" @click="sureAdd()">确认</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import BasicInfo, { TrainingTimeRange } from '@api/service/management/learning-rule/model/BasicInfo'
  import { ElForm } from 'element-ui/types/form'
  import RootModule from '@/store/RootModule'
  @Component({
    components: { BizYearSelect }
  })
  export default class extends Vue {
    @Ref('dialogRef') dialogRef: ElForm
    // 接收基础信息
    @Prop({
      type: BasicInfo,
      default: () => new BasicInfo()
    })
    basicInfo: BasicInfo
    // 打开抽屉
    openDialog = false

    // 年度模型
    trainingTimeRange = new TrainingTimeRange()
    // 规则
    rules = {
      year: [
        {
          required: true,
          validator: async (rules: any, value: any, callback: (message?: any) => {}) => {
            if (!value) {
              return callback(new Error(`请选择培训年度`))
            } else if (new Date(Number(RootModule.currentSystemTime.timestamp)).getFullYear() <= value) {
              return callback(new Error('当前年度不支持配置学习规则。'))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      time: [{ required: true, validator: this.validateTime, message: '请设置年度培训时间', trigger: 'blur' }]
    }

    // 初始化
    init() {
      this.trainingTimeRange = new TrainingTimeRange()
      this.openDialog = true
    }

    // 确认添加时间区间
    async sureAdd() {
      const res = await this.dialogRef.validate()
      if (!res) return
      this.basicInfo.addTimeRange(this.trainingTimeRange)
      this.openDialog = false
    }

    // 取消方法
    cencel() {
      this.dialogRef.clearValidate()
      this.openDialog = false
    }

    // 自定义时间校验
    validateTime(rule: any, value: any, callback: any) {
      if (!value[0] || !value[1]) {
        callback(new Error('请设置年度培训时间'))
      } else {
        callback()
      }
    }
  }
</script>
