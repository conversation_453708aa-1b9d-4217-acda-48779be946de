<template>
  <div>
    <el-main v-if="$hasPermission('query')" desc="查询" actions="doSearch,activated,mounted">
      <div class="f-p15">
        <el-row :gutter="15" class="is-height">
          <el-col :span="7">
            <el-card shadow="never" class="m-card is-header-sticky f-mb15">
              <div slot="header" class="is-sticky">
                <span class="tit-txt">导入任务类型</span>
              </div>
              <div class="f-plr20 f-pb20">
                <el-row :gutter="5" class="m-query no-gutter f-pt15">
                  <el-form :inline="true" label-width="auto">
                    <el-col :span="18">
                      <el-form-item>
                        <el-input v-model="taskType" placeholder="请输入任务类型关键字" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="4">
                      <!-- <el-form-item v-if="$hasPermission('query')" desc="查询" actions="getTaskType"> -->
                      <el-button type="primary" @click="getTaskType">查询</el-button>
                      <!-- </el-form-item> -->
                    </el-col>
                  </el-form>
                </el-row>
                <el-table
                  ref="importTable"
                  stripe
                  :data="taskTypeList"
                  max-height="500px"
                  highlight-current-row
                  class="m-table is-body m-plan-list is-arrow"
                  @current-change="handleCurrentChange"
                  v-loading="uiConfig.typeLoading"
                >
                  <el-table-column>
                    <template slot-scope="scope">
                      <div>{{ scope.row.name }}</div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-card>
          </el-col>
          <el-col :span="17">
            <el-card shadow="never" class="m-card is-header-sticky f-mb15">
              <div slot="header" class="">
                <span class="tit-txt">{{ currentTaskType }}</span>
              </div>
              <div class="f-plr20 f-pt20">
                <el-row :gutter="16" class="m-query is-border-bottom no-gutter">
                  <el-form :inline="true" label-width="auto">
                    <el-col :sm="12" :xl="6">
                      <el-form-item label="任务名称">
                        <el-input v-model="pageQueryParam.taskName" clearable placeholder="请输入任务名称" />
                      </el-form-item>
                    </el-col>
                    <el-col v-show="advanced" :sm="12" :xl="5">
                      <el-form-item label="执行状态">
                        <el-select v-model="state" clearable @clear="clearState" filterable placeholder="请选择">
                          <el-option
                            v-for="item in status"
                            :label="item.name"
                            :value="item.value"
                            :key="item.value"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col v-show="advanced" :sm="12" :xl="8">
                      <el-form-item label="执行时间">
                        <double-date-picker
                          :begin-create-time.sync="pageQueryParam.executeStartTime"
                          :end-create-time.sync="pageQueryParam.executeEndTime"
                        ></double-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :xl="5" class="f-fr">
                      <el-form-item class="f-tr">
                        <template v-if="$hasPermission('query')" desc="查询" actions="currentChange">
                          <el-button type="primary" @click="currentChange">查询</el-button>
                        </template>
                        <el-button @click="resetCondition">重置</el-button>
                        <el-button type="text" v-if="!defaultShow" @click="showAdvanced">
                          {{ this.advanced ? '收起' : '展开' }}
                          <i :class="advanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
                        </el-button>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
                <!--表格-->
                <el-table stripe ref="tableRef" :data="tableData" max-height="500" class="m-table">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="任务名称" min-width="240" fixed="left">
                    <template slot-scope="scope">{{ scope.row.name }}</template>
                  </el-table-column>
                  <el-table-column label="任务处理时间" min-width="170">
                    <template slot-scope="scope">
                      <template v-if="scope.row.taskState == 3 && scope.row.processResult == 1">
                        {{ scope.row.executingTime }}
                      </template>
                      <template v-else>-</template>
                    </template>
                  </el-table-column>
                  <el-table-column label="任务结束时间" min-width="170">
                    <template slot-scope="scope">
                      <template v-if="scope.row.taskState == 3 && scope.row.processResult == 1">
                        {{ scope.row.completedTime }}
                      </template>
                      <template v-else>-</template>
                    </template>
                  </el-table-column>
                  <el-table-column label="任务执行状态" min-width="120">
                    <template slot-scope="scope">
                      <div v-if="scope.row.taskState == 2 || scope.row.taskState == 1">
                        <el-tag type="info">执行中</el-tag>
                      </div>
                      <div v-if="scope.row.taskState == 3">
                        <el-tag type="success">已执行</el-tag>
                      </div>
                      <div
                        v-else-if="
                          scope.row.taskState !== 1 &&
                          scope.row.taskState !== 2 &&
                          scope.row.taskState !== 3 &&
                          scope.row.taskState !== 0
                        "
                      >
                        <el-tag type="info">执行失败</el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="任务处理结果" min-width="120">
                    <template slot-scope="scope">
                      <div v-if="scope.row.processResult === 1">
                        <el-badge is-dot type="success" class="badge-status">成功</el-badge>
                      </div>
                      <div v-else-if="scope.row.processResult == 2">
                        <el-badge is-dot type="danger" class="badge-status">失败</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="danger" class="badge-status">未知</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="处理总条数/成功条数/失败条数" width="240" align="center">
                    <template slot-scope="scope">
                      <span v-if="scope.row.eachStateCounts">
                        {{ getcount(scope.row.eachStateCounts) }}/{{ getsuccess(scope.row.eachStateCounts) }}/{{
                          getfali(scope.row.eachStateCounts)
                        }}
                      </span>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="140" align="center" fixed="right">
                    <template slot-scope="scope">
                      <template v-if="$hasPermission('download')" desc="下载全部数据" actions="location">
                        <el-button
                          type="text"
                          size="mini"
                          @click="location(scope.row)"
                          :disabled="
                            scope.row.taskState === 2 ||
                            (scope.row.taskState !== 1 &&
                              scope.row.taskState !== 2 &&
                              scope.row.taskState !== 3 &&
                              scope.row.taskState !== 0)
                          "
                          >下载全部数据
                        </el-button>
                      </template>
                      <template v-if="$hasPermission('download')" desc="查看失败数据" actions="locationfail">
                        <el-button
                          type="text"
                          size="mini"
                          @click="locationfail(scope.row)"
                          :disabled="
                            scope.row.taskState === 2 ||
                            (scope.row.taskState !== 1 &&
                              scope.row.taskState !== 2 &&
                              scope.row.taskState !== 3 &&
                              scope.row.taskState !== 0) ||
                            getfali(scope.row.eachStateCounts) === 0
                          "
                          >查看失败数据
                        </el-button>
                      </template>
                      <template v-if="$hasPermission('viewLog')" desc="查看日志" actions="viewLog">
                        <el-button
                          type="text"
                          @click="viewLog(scope.row)"
                          size="mini"
                          :disabled="scope.row.processResult === 0"
                          >查看日志
                        </el-button>
                      </template>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <hb-pagination
                  :total-size="totalSize"
                  :page="page"
                  class="f-mt15 f-tr"
                  @current-change="currentPageChange"
                  @size-change="currentChange"
                >
                </hb-pagination>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-main>
  </div>
</template>
<script lang="ts">
  import { BatchImportQuestionQueryRequest } from '@api/ms-gateway/ms-examquestion-v1'
  import { AsyncTaskJobStatusEnum } from '@api/service/common/enums/async-task/AsyncTaskJobStatusType'
  import AsyncTaskModule from '@api/service/management/async-task/AsyncTaskModule'
  import AsyncTaskItemVo from '@api/service/management/async-task/query/vo/AsyncTaskItemVo'
  import { UiPage } from '@hbfe/common'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'

  import { TaskExecuteParamRequest } from '@api/ms-gateway/ms-importopen-v1'
  import Context from '@api/service/common/context/Context'
  import Downloader, { HeaderObj } from '@api/service/common/utils/Downloader'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'

  enum AsyncTaskJobStatus {
    EXECUTING = 1,
    EXECUTED = 2,
    FAIL = 3
  }

  @Component({
    components: { DoubleDatePicker }
  })
  export default class extends Vue {
    @Ref('importTable') importTab: any
    /**
     * 任务类型列表数据
     */
    taskList = [
      {
        id: 16,
        type: 'batchUpdateScheme',
        name: '批量更新方案'
      },
      {
        id: 1,
        name: '导入学员并开班'
      },
      {
        id: 2,
        name: '导入学员开班并学习'
      },
      {
        id: 3,
        name: '导入学员'
      },
      {
        id: 4,
        name: '外链课件导入任务'
      },
      {
        id: 5,
        name: '课程包导入任务'
      },
      {
        id: 6,
        name: '试题导入任务'
      },
      {
        id: 7,
        name: '个人报名增值税电子普通发票（线下开票）'
      },

      {
        id: 8,
        name: '个人报名增值税专用发票（纸质票）'
      },
      {
        id: 9,
        name: '个人报名发票配送'
      },
      {
        id: 10,
        name: '个人报名增值税电子专用发票（线下开票）'
      },
      {
        id: 11,
        name: '集体报名增值税电子普通发票（线下开票）'
      },
      {
        id: 12,
        name: '集体报名增值税电子专用发票（线下开票）'
      },
      {
        id: 13,
        name: '集体报名增值税专用发票（纸质票）'
      },
      {
        id: 14,
        name: '集体报名发票配送'
      },
      {
        id: 15,
        name: '按人员名单打印证明'
      },
      {
        id: 17,
        type: 'issueResult',
        name: '导入结业成果'
      },
      {
        id: 18,
        type: 'UPDATE_SCHEME_SHOW_IMPORT',
        name: '批量更新方案展示'
      }
    ]
    totalSize = 0
    select = ''
    input = ''
    tableData = [] as any
    page: UiPage = new UiPage()
    form = {
      data1: ''
    }
    state = '' as any
    status = [
      {
        name: '已执行',
        value: AsyncTaskJobStatus.EXECUTED
      },
      {
        name: '执行中',
        value: AsyncTaskJobStatus.EXECUTING
      },
      {
        name: '执行失败',
        value: AsyncTaskJobStatus.FAIL
      }
    ]
    // 筛选默认展示
    defaultShow = document.documentElement.clientWidth > 1800 ? true : false
    // 筛选默认收起
    advanced = document.documentElement.clientWidth > 1800 ? true : false
    /**
     * 路径资源前缀
     */
    //resourcesPrefix: string
    pageData: Array<AsyncTaskItemVo> = new Array<AsyncTaskItemVo>()
    intelligenceLearningModule = new IntelligenceLearningModule()
    // 根据返回结果是否展示导入学员开班并学习导入任务类型
    intelligenceShow = true
    /**
     * 任务类型关键字
     */
    taskType = ''
    /**
     * 当前选中的任务类型
     */
    currentTaskType = '导入学员并开班'

    /**
     * 任务类型列表数据
     */
    taskTypeList = [
      {
        id: 16,
        type: 'batchUpdateScheme',
        name: '批量更新方案'
      },
      {
        id: 1,
        name: '导入学员并开班'
      },
      {
        id: 2,
        name: '导入学员开班并学习'
      },
      {
        id: 3,
        name: '导入学员'
      },
      {
        id: 4,
        name: '外链课件导入任务'
      },
      {
        id: 5,
        name: '课程包导入任务'
      },
      {
        id: 6,
        name: '试题导入任务'
      },
      {
        id: 7,
        name: '个人报名增值税电子普通发票（线下开票）'
      },

      {
        id: 8,
        name: '个人报名增值税专用发票（纸质票）'
      },
      {
        id: 9,
        name: '个人报名发票配送'
      },
      {
        id: 10,
        name: '个人报名增值税电子专用发票（线下开票）'
      },
      {
        id: 11,
        name: '集体报名增值税电子普通发票（线下开票）'
      },
      {
        id: 12,
        name: '集体报名增值税电子专用发票（线下开票）'
      },
      {
        id: 13,
        name: '集体报名增值税专用发票（纸质票）'
      },
      {
        id: 14,
        name: '集体报名发票配送'
      },
      {
        id: 15,
        name: '按人员名单打印证明'
      },
      {
        id: 17,
        type: 'issueResult',
        name: '导入结业成果'
      },
      {
        id: 18,
        type: 'UPDATE_SCHEME_SHOW_IMPORT',
        name: '批量更新方案展示'
      }
    ]

    queryImportAsyncTask = AsyncTaskModule.queryAsyncTaskFactory.queryImportAsyncTask

    pageQueryParam = new BatchImportQuestionQueryRequest()

    // 当前用户是否拥有专题管理员角色类型
    isZtglyLogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    importIndex = -1
    uiConfig = {
      loading: false,
      typeLoading: false
    }

    taskStatusMapName = {
      [AsyncTaskJobStatus.EXECUTING]: '正在执行',
      [AsyncTaskJobStatus.EXECUTED]: '已执行',
      [AsyncTaskJobStatus.FAIL]: '执行失败'
    }

    taskStatusMapType = {
      [AsyncTaskJobStatusEnum.EXECUTING]: 'primary',
      [AsyncTaskJobStatusEnum.EXECUTED]: 'success',
      [AsyncTaskJobStatusEnum.FAIL]: 'danger'
    }

    @Watch('tableData', {
      deep: true,
      immediate: true
    })
    doLayout(val: any) {
      ;(this.$refs['tableRef'] as any)?.doLayout()
    }

    currentChange() {
      const val = { name: '' }
      val.name = this.currentTaskType
      if (this.state == 1) {
        if (
          val.name === '导入学员并开班' ||
          val.name === '导入学员开班并学习' ||
          val.name === '导入学员' ||
          val.name === '个人报名发票配送'
        ) {
          this.pageQueryParam.taskState = 2
          this.pageQueryParam.processResult = 0
        } else if (
          val.name === '个人报名增值税电子普通发票（线下开票）' ||
          val.name === '个人报名增值税专用发票（纸质票）' ||
          val.name === '外链课件导入任务' ||
          val.name === '集体报名增值税电子专用发票（线下开票）'
        ) {
          this.pageQueryParam.taskState = 1
          this.pageQueryParam.processResult = 0
        } else {
          this.pageQueryParam.taskState = 2
          this.pageQueryParam.processResult = 3
        }
      }
      if (this.state == 2) {
        this.pageQueryParam.taskState = 3
        this.pageQueryParam.processResult = 1
      }
      if (this.state == 3) {
        this.pageQueryParam.taskState = 3
        this.pageQueryParam.processResult = 2
      }
      this.doTask(val)
    }

    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    async activated() {
      this.taskType = ''

      await this.getTaskType(Boolean(this.$route.query.type))
      if (this.$route.query.type) {
        const index = this.taskTypeList.findIndex((item: any) => {
          return item.name === this.$route.query.type || (item.type && item.type === this.$route.query.type)
        })
        if (index !== -1) {
          this.setCurrent(this.taskTypeList[index])
        }
      }
      // 当运营域未开通智能学习时，不显示导入学员开班并学习导入任务类型

      this.resetCondition()
    }

    async mounted() {
      window.onresize = () => {
        return this.getWindowWidth()
      }
      await this.intelligenceConfig()
      this.importIndex = this.taskTypeList.findIndex((item: any) => {
        return item.name === '导入学员开班并学习'
      })
      console.log(this.importIndex, 'importIndex')
      //this.resourcesPrefix = '' //获取下载资源路径前缀
      await this.getTaskType(Boolean(this.$route.query.type))
      if (this.$route.query.type) {
        console.log(this.taskTypeList, 'this.taskTypeList')

        const index = this.taskTypeList.findIndex((item: any) => {
          return item.name === this.$route.query.type
        })
        if (index !== -1) {
          this.setCurrent(this.taskTypeList[index])
        }
      }
      // this.setCurrent(this.taskTypeList[0])
    }

    getWindowWidth() {
      if (document.documentElement.clientWidth > 1800) {
        this.defaultShow = true
        this.advanced = true
      } else {
        this.defaultShow = false
      }
    }

    showAdvanced() {
      this.advanced = !this.advanced
    }

    //获取导入任务类型
    getTaskType(route?: boolean) {
      this.uiConfig.typeLoading = true

      //   专题管理员只显示导入学员开班、导入学员、个人报名发票相关的任务
      if (this.isZtglyLogin) {
        this.taskTypeList = this.fuzzyQuery(this.taskType).filter((item: any) => {
          return item.name == '导入学员并开班'
        })
      } else {
        this.taskTypeList = this.fuzzyQuery(this.taskType)
      }

      // 如果是不路由选中，默认选中第一个
      if (!route) this.setCurrent(this.taskTypeList[0])
      this.uiConfig.typeLoading = false
    }

    //获取总数
    getcount(item: any) {
      let count = 0
      if (item?.length == 1) {
        count = item[0].count
      } else if (item?.length == 2) {
        const fali = item.find((item: any) => {
          return item.result == 2
        })
        const success = item.find((item: any) => {
          return item.result == 1
        })
        const aa = success?.count ? success?.count : 0
        const ff = fali?.count ? fali?.count : 0
        count = aa + ff
      }
      return count
    }

    //获取成功条数
    getsuccess(item: any) {
      let successnum = 0
      const success = item?.find((item: any) => {
        return item.result == 1
      })
      if (!success) {
        successnum = 0
      } else {
        successnum = success.count
      }
      return successnum
    }

    //获取失败条数
    getfali(item: any) {
      let failnum = 0
      const fail = item?.find((item: any) => {
        return item.result == 2
      })
      if (!fail) {
        failnum = 0
      } else {
        failnum = fail.count
      }
      return failnum
    }

    //过滤
    fuzzyQuery(taskType: string) {
      const arr = []
      for (let i = 0; i < this.taskList.length; i++) {
        if (this.taskList[i].name.indexOf(taskType) >= 0) {
          arr.push(this.taskList[i])
        }
      }
      if (this.intelligenceShow === false && this.importIndex !== -1) {
        arr.splice(this.importIndex, 1)
      }
      return arr
    }

    async handleCurrentChange(val: any) {
      this.tableData = []
      this.currentTaskType = val.name
      this.pageQueryParam = new BatchImportQuestionQueryRequest()
      // this.pageQueryParam.taskName = val.name
      await this.resetCondition()
      await this.doSearch(val)
    }

    setCurrent(row: any) {
      console.group(
        '%c%s',
        'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#32FFFF, #fff)',
        'this.$refs调试输出'
      )

      console.count('this.$refs输出次数')
      console.groupEnd()
      console.group(
        '%c%s',
        'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#32FFFF, #fff)',
        'this.$refs调试输出'
      )
      console.count('this.$refs输出次数')
      console.groupEnd()
      ;(this.$refs.importTable as any).setCurrentRow(row)
    } //
    async currentPageChange() {
      await this.page.currentChange(this.page.pageNo)
      await this.page.pageSizeChange(this.page.pageSize)
      const val = { name: '' }
      val.name = this.currentTaskType
      if (this.state == 1) {
        if (
          val.name === '导入学员并开班' ||
          val.name === '导入学员开班并学习' ||
          val.name === '导入学员' ||
          val.name === '个人报名发票配送'
        ) {
          this.pageQueryParam.taskState = 2
          this.pageQueryParam.processResult = 0
        } else if (
          val.name === '个人报名增值税电子普通发票（线下开票）' ||
          val.name === '个人报名增值税专用发票（纸质票）' ||
          val.name === '外链课件导入任务' ||
          val.name === '集体报名增值税电子专用发票（线下开票）'
        ) {
          this.pageQueryParam.taskState = 1
          this.pageQueryParam.processResult = 0
        } else {
          this.pageQueryParam.taskState = 2
          this.pageQueryParam.processResult = 3
        }
      }
      if (this.state == 2) {
        this.pageQueryParam.taskState = 3
        this.pageQueryParam.processResult = 1
      }
      if (this.state == 3) {
        this.pageQueryParam.taskState = 3
        this.pageQueryParam.processResult = 2
      }
      this.doTask(val)
    }

    clearState() {
      this.state = ''
      this.pageQueryParam = new BatchImportQuestionQueryRequest()
    }

    async doSearch(val: any) {
      this.uiConfig.loading = true

      // this.doTask(val)
      this.uiConfig.loading = false
    }

    doTask(val: any) {
      switch (val.name) {
        case '导入学员并开班':
          this.queryQueryImportList()
          break
        case '导入学员开班并学习':
          this.queryQueryIntelligenceImportList()
          break
        case '导入学员':
          this.queryImportStudentByPage()
          break
        case '课程包导入任务':
          this.queryImportCoursebagByPage()
          break
        case '试题导入任务':
          this.testTask()
          break
        case '个人报名增值税电子普通发票（线下开票）':
          this.queryImportElectronicInvoice()
          break
        case '个人报名增值税专用发票（纸质票）':
          this.queryImportSpecialPaperOfflineInvoice()
          break
        case '个人报名发票配送':
          this.queryImportDeliverySpecialInvoice()
          break
        case '个人报名增值税电子专用发票（线下开票）':
          this.queryForImportOfflineSpecialElectronicInvoice()
          break
        case '集体报名增值税电子普通发票（线下开票）':
          this.queryImportBatchPayOfflineInvoice()
          break
        case '集体报名增值税电子专用发票（线下开票）':
          this.queryCommonQueryOfflineInvoiceImportResult()
          break
        case '集体报名增值税专用发票（纸质票）':
          this.queryImportBatchPaySpecialPaperOfflineInvoice()
          break
        case '集体报名发票配送':
          this.queryImportDeliveryBatchPaySpecialInvoice()
          break
        case '外链课件导入任务':
          this.queryOuterCoursewareImportResult()
          break
        case '按人员名单打印证明':
          this.queryProofOfPersonnelListPrinting()
          break
        case '批量更新方案':
          this.queryBatchUpdateSchemeListData()
          break
        case '导入结业成果':
          this.queryIssueResultListData()
          break
        case '批量更新方案展示':
          this.queryBatchUpdatePlanShowTaskList()
          break
        default:
          return this.doTaskDiff(val)
      }
    }

    doTaskDiff(val: any) {
      return ' '
    }

    //试题任务
    async testTask() {
      this.tableData = await this.queryImportAsyncTask.queryImportQuestionByPage(this.page, this.pageQueryParam)
      this.totalSize = this.page.totalSize
    }

    resetCondition() {
      this.page.pageNo = 1
      this.pageQueryParam = new BatchImportQuestionQueryRequest()
      this.state = ''
      this.currentChange()

      // this.doSearch()
    }

    /**
     * 下载导出数据
     */
    async location(item: any) {
      switch (this.currentTaskType) {
        case '导入学员并开班':
          this.queryExportStudentAllData(item)
          break
        case '导入学员开班并学习':
          this.queryExportIntelligenceData(item)
          break
        case '导入学员':
          this.doExportAllStudentResult(item)
          break
        // case '批量分配课程导入任务':
        //   this.queryMassdistributionAllData(item)
        //   break
        case '课程包导入任务':
          this.queryImportCoursebagAllData(item)
          break
        case '试题导入任务':
          this.queryBatchExportAllChooseQuestionData(item)
          break
        case '个人报名增值税电子普通发票（线下开票）':
          this.queryInvoiceImportAllData(item)
          break
        case '个人报名增值税专用发票（纸质票）':
          this.queryInvoiceImportAllData(item)
          break
        case '个人报名发票配送':
          this.queryInvoiceImportAllData(item)
          break
        case '个人报名增值税电子专用发票（线下开票）':
          this.queryInvoiceImportAllData(item)
          break

        case '集体报名增值税电子普通发票（线下开票）':
          this.queryInvoiceImportAllData(item)
          break
        case '集体报名增值税电子专用发票（线下开票）':
          this.queryInvoiceImportAllData(item)
          break
        case '集体报名增值税专用发票（纸质票）':
          this.queryInvoiceImportAllData(item)
          break
        case '集体报名发票配送':
          this.queryInvoiceImportAllData(item)
          break
        case '外链课件导入任务':
          this.queryOuterCoursewareAllData(item)
          break
        case '按人员名单打印证明':
          this.findImportPrintData(item)
          break
        case '批量更新方案':
          this.queryBatchUpdateSchemeAllData(item)
          break
        case '导入结业成果':
          this.queryIssueResultAllData(item)
          break
        case '批量更新方案展示':
          this.queryBatchUpdatePlanShowAllData(item)
          break
        default:
          return this.locationDiff(item)
      }
    }

    locationDiff(item: any) {
      return ' '
    }

    /**
     * 下载失败数据
     */
    async locationfail(item: any) {
      switch (this.currentTaskType) {
        case '导入学员并开班':
          this.queryExportStudentFailData(item)
          break
        case '导入学员开班并学习':
          this.queryExportStudentFailData(item)
          break
        case '导入学员':
          this.doExportErrorStudentResult(item)
          break
        case '课程包导入任务':
          this.queryImportCoursebagFailData(item)
          break
        case '试题导入任务':
          this.querybatchExportFailChooseQuestionData(item)
          break
        case '批量授权产品':
          this.queryFailBatchProduct(item)
          break
        case '个人报名增值税电子普通发票（线下开票）':
          this.queryInvoiceImportFailData(item)
          break
        case '个人报名增值税专用发票（纸质票）':
          this.queryInvoiceImportFailData(item)
          break
        case '个人报名发票配送':
          this.queryInvoiceImportFailData(item)
          break
        case '个人报名增值税电子专用发票（线下开票）':
          this.queryInvoiceImportFailData(item)
          break

        case '集体报名增值税电子普通发票（线下开票）':
          this.queryInvoiceImportFailData(item)
          break
        case '集体报名增值税电子专用发票（线下开票）':
          this.queryInvoiceImportFailData(item)
          break
        case '集体报名增值税专用发票（纸质票）':
          this.queryInvoiceImportFailData(item)
          break
        case '集体报名发票配送':
          this.queryInvoiceImportFailData(item)
          break
        case '外链课件导入任务':
          this.queryOuterCoursewareFailData(item)
          break
        case '按人员名单打印证明':
          this.findImportPrintFailData(item)
          break
        case '批量更新方案':
          this.queryBatchUpdateSchemeFailData(item)
          break
        case '导入结业成果':
          this.queryIssueResultFailData(item)
          break
        case '批量更新方案展示':
          this.queryBatchUpdatePlanShowFailData(item)
          break
        default:
          return this.locationfailDiff(item)
      }
    }

    locationfailDiff(item: any) {
      return ' '
    }

    /**
     * 下载试题导出数据
     */
    async queryBatchExportAllChooseQuestionData(item: any) {
      const res = await this.queryImportAsyncTask.queryBatchExportAllChooseQuestionData(item.id)
      const fileUrl = res
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 下载批量授权产品失败数据
     */
    async queryFailBatchProduct(item: any) {
      // TODO
    }

    /**
     * 下载试题失败数据
     */
    async querybatchExportFailChooseQuestionData(item: any) {
      const res = await this.queryImportAsyncTask.querybatchExportFailChooseQuestionData(item.id)
      if (!this.hasDownLoadLink(res)) return
      const fileUrl = res.substring(res.indexOf('/') + 4, res.length)
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 查询课程包导入任务
     */
    async queryImportCoursebagByPage() {
      this.tableData = await this.queryImportAsyncTask.queryImportCoursebagByPage(this.page, this.pageQueryParam)
      this.totalSize = this.page.totalSize
    }

    /**
     * 下载课程包导出数据
     */
    async queryImportCoursebagAllData(item: any) {
      const res = await this.queryImportAsyncTask.queryImportCoursebagAllData(item.id)
      const fileUrl = res
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 下载课程包失败数据
     */
    async queryImportCoursebagFailData(item: any) {
      const res = await this.queryImportAsyncTask.queryImportCoursebagFailData(item.id)
      if (!this.hasDownLoadLink(res)) return
      const fileUrl = '/mfs' + res
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
      //   window.open('/mfs' + res)
    }

    /**
     * 查询学员导入并开班
     */
    async queryQueryImportList() {
      const newPageQueryParam = new TaskExecuteParamRequest()
      newPageQueryParam.taskCategoryList = ['ADMIN_NORMAL_IMPORT']
      newPageQueryParam.taskName = this.pageQueryParam.taskName
      newPageQueryParam.taskState = this.pageQueryParam.taskState
      newPageQueryParam.executeEndTime = this.pageQueryParam.executeEndTime
      newPageQueryParam.executeStartTime = this.pageQueryParam.executeStartTime
      newPageQueryParam.processResult = this.pageQueryParam.processResult
      if (this.isZtglyLogin) {
        this.tableData = await this.queryImportAsyncTask.queryQueryImportListByThemeManeger(
          newPageQueryParam,
          this.page
        )
      } else {
        this.tableData = await this.queryImportAsyncTask.queryQueryImportList(newPageQueryParam, this.page)
      }

      this.totalSize = this.page.totalSize
    }

    /**
     * 下载查询学员导入并开班数据
     */
    async queryExportStudentAllData(item: any) {
      const res = await this.queryImportAsyncTask.queryExportStudentAllData(item.id)
      const fileUrl = '/mfs' + res
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
      // window.open(`/mfs` + res)
    }

    /**
     * 查询学员导入并开班
     */
    async queryQueryIntelligenceImportList() {
      const newPageQueryParam = new TaskExecuteParamRequest()
      newPageQueryParam.taskCategoryList = ['ADMIN_NORMAL_IMPORT_AND_LEARNING']
      newPageQueryParam.taskName = this.pageQueryParam.taskName
      newPageQueryParam.taskState = this.pageQueryParam.taskState
      newPageQueryParam.executeEndTime = this.pageQueryParam.executeEndTime
      newPageQueryParam.executeStartTime = this.pageQueryParam.executeStartTime
      newPageQueryParam.processResult = this.pageQueryParam.processResult
      this.tableData = await this.queryImportAsyncTask.queryQueryImportList(newPageQueryParam, this.page)
      this.totalSize = this.page.totalSize
    }

    /**
     * 下载查询学员导入开班并学习数据
     */
    async queryExportIntelligenceData(item: any) {
      const res = await this.queryImportAsyncTask.queryExportStudentAllData(item.id)
      const fileUrl = '/mfs' + res
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
      // window.open(`/mfs` + res)
    }

    /**
     * 下载查询学员导入并开班失败数据
     */
    async queryExportStudentFailData(item: any) {
      const res = await this.queryImportAsyncTask.queryExportStudentFailData(item.batchNo)
      if (!this.hasDownLoadLink(res)) return
      const fileUrl = '/mfs' + res
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 下载查询学员导入开班并学习失败数据
     */
    async queryExportStudentLearningFailData(item: any) {
      // const res = await this.queryImportAsyncTask.queryExportStudentLearningFailData(item.batchNo)
      // if (!this.hasDownLoadLink(res)) return
      // const fileUrl = '/mfs' + res
      // const fileName = res.split('/')[res.split('/').length - 1]
      // const authorization = localStorage.getItem('admin.Access-Token')
      // const header: HeaderObj = {
      //   ['Authorization']: authorization
      // }
      // const downloadFile = new Downloader(fileUrl, fileName, header)
      // downloadFile.download()
    }

    /**
     * 查询批量分配课程导入任务
     */
    // async queryMassdistribution() {
    //   const res = await this.queryImportAsyncTask.pageCourseImportTaskQuery(this.page, this.pageQueryParam)
    //   this.tableData = res.data.currentPageData
    //   this.totalSize = this.page.totalSize
    // }
    /**
     * 下载查询批量分配课程导入任务数据
     */
    // async queryMassdistributionAllData(item: any) {
    //   const { data } = await this.queryImportAsyncTask.exportCourseAllocateImportInfo({ taskId: item.id, all: true })
    //   const fileUrl = '/mfs' + data
    //   const fileName = data.split('/')[data.split('/').length - 1]
    //   const authorization = localStorage.getItem('admin.Access-Token')
    //   const header: HeaderObj = {
    //     ['Authorization']: authorization
    //   }
    //   const downloadFile = new Downloader(fileUrl, fileName, header)
    //   downloadFile.download()
    // }
    /**
     * 下载查询批量分配课程导入任务失败数据
     */
    // async queryMassdistributionFailData(item: any) {
    //   const { data } = await this.queryImportAsyncTask.exportCourseAllocateImportInfo({ taskId: item.id, all: true })
    //   const fileUrl = '/mfs' + data
    //   const fileName = data.split('/')[data.split('/').length - 1]
    //   const authorization = localStorage.getItem('admin.Access-Token')
    //   const header: HeaderObj = {
    //     ['Authorization']: authorization
    //   }
    //   const downloadFile = new Downloader(fileUrl, fileName, header)
    //   downloadFile.download()
    // }
    /**
     * 查询导入学员
     */
    async queryImportStudentByPage() {
      this.tableData = await this.queryImportAsyncTask.queryImportStudentByPage(this.page, this.pageQueryParam)
      this.totalSize = this.page.totalSize
    }

    /**
     * 下载查询学员导入数据
     */
    async doExportAllStudentResult(item: any) {
      const res = await this.queryImportAsyncTask.doExportAllStudentResult(item.id)
      //   const fileUrl = '/mfs' + res
      // 状态层处理
      const fileUrl = res
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 下载查询学员导入失败数据
     */
    async doExportErrorStudentResult(item: any) {
      const res = await this.queryImportAsyncTask.doExportErrorStudentResult(item.id)
      if (!this.hasDownLoadLink(res)) return
      const fileUrl = '/mfs' + res
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 查询按人员名单打印证明
     */
    async queryProofOfPersonnelListPrinting() {
      this.tableData = await this.queryImportAsyncTask.queryExportList(this.page, this.pageQueryParam)
      this.totalSize = this.page.totalSize
    }

    /**
     * 查询导入个人报名增值税电子普通发票（线下开票）
     */
    async queryImportElectronicInvoice() {
      this.tableData = await this.queryImportAsyncTask.queryImportElectronicInvoice(this.pageQueryParam, this.page)
      this.totalSize = this.page.totalSize
    }

    /**
     * 查询导入集体报名增值税电子普通发票（线下开票）
     */
    async queryImportBatchPayOfflineInvoice() {
      this.tableData = await this.queryImportAsyncTask.queryImportBatchPayOfflineInvoice(this.pageQueryParam, this.page)
      this.totalSize = this.page.totalSize
    }

    /**
     * 查询导入个人报名增值税专用
     */
    async queryImportSpecialPaperOfflineInvoice() {
      this.tableData = await this.queryImportAsyncTask.queryImportSpecialPaperOfflineInvoice(
        this.pageQueryParam,
        this.page
      )
      this.totalSize = this.page.totalSize
    }

    /**
     * 查询个人报名配送
     */
    async queryImportDeliverySpecialInvoice() {
      this.tableData = await this.queryImportAsyncTask.queryImportDeliverySpecialInvoice(this.pageQueryParam, this.page)
      this.totalSize = this.page.totalSize
    }

    async queryForImportOfflineSpecialElectronicInvoice() {
      this.tableData = await this.queryImportAsyncTask.queryForImportOfflineSpecialElectronicInvoice(
        this.pageQueryParam,
        this.page
      )
      this.totalSize = this.page.totalSize
    }

    /**
     * 查询导入集体报名增值税电子专用发票（线下开票）
     */
    async queryCommonQueryOfflineInvoiceImportResult() {
      this.tableData = await this.queryImportAsyncTask.queryCommonQueryOfflineInvoiceImportResult(
        this.pageQueryParam,
        this.page
      )
      this.totalSize = this.page.totalSize
    }

    /**
     * 查询集体增值税专用
     */
    async queryImportBatchPaySpecialPaperOfflineInvoice() {
      this.tableData = await this.queryImportAsyncTask.queryImportBatchPaySpecialPaperOfflineInvoice(
        this.pageQueryParam,
        this.page
      )
      this.totalSize = this.page.totalSize
    }

    /**
     * 查询集体发票配送
     */
    async queryImportDeliveryBatchPaySpecialInvoice() {
      this.tableData = await this.queryImportAsyncTask.queryImportDeliveryBatchPaySpecialInvoice(
        this.pageQueryParam,
        this.page
      )
      this.totalSize = this.page.totalSize
    }

    /**
     *  查询外链课件导入任务
     */
    async queryOuterCoursewareImportResult() {
      this.tableData = await this.queryImportAsyncTask.queryOuterCoursewareImportResult(this.pageQueryParam, this.page)
      this.totalSize = this.page.totalSize
    }

    /**
     * 查询批量更新课件导入任务
     */
    async queryBatchUpdateSchemeListData() {
      this.tableData = await this.queryImportAsyncTask.queryBatchUpdateSchemeImportResult(
        this.pageQueryParam,
        this.page
      )
      this.totalSize = this.page.totalSize
    }

    /**
     * 查询导入结业成果
     */
    async queryIssueResultListData() {
      this.tableData = await this.queryImportAsyncTask.queryGraduationResultImportResult(this.pageQueryParam, this.page)
      this.totalSize = this.page.totalSize
    }

    /**
     * 查询批量更新方案展示任务列表
     */
    async queryBatchUpdatePlanShowTaskList() {
      this.tableData = await this.queryImportAsyncTask.queryBatchUpdatePlanShowTaskList(this.page, this.pageQueryParam)
      this.totalSize = this.page.totalSize
    }

    /**
     * 下载发票相关全部数据
     */
    async queryInvoiceImportAllData(item: any) {
      console.log(item)
      const res = await this.queryImportAsyncTask.queryInvoiceImportAllData(item.id)
      const fileUrl = '/mfs' + res
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 下载外链课件相关全部数据
     */
    async queryOuterCoursewareAllData(item: any) {
      const res = await this.queryImportAsyncTask.queryOuterCoursewareAllData(item.id)
      const fileUrl = res
      const fileName = res.split('/')[res.split('/').length - 1].split('?')[0]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 下载按人员名单打印全部数据
     */
    async findImportPrintData(item: any) {
      const res = await this.queryImportAsyncTask.findImportPrintData(item.id)
      const fileUrl = res
      const fileName = res.split('/')[res.split('/').length - 1].split('?')[0]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 下载批量更新方案全部数据
     */
    async queryBatchUpdateSchemeAllData(item: any) {
      const res = await this.queryImportAsyncTask.queryBatchUpdateSchemeAllData(item.id)
      const fileUrl = '/mfs' + res
      const fileName = res.split('/')[res.split('/').length - 1].split('?')[0]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 下载导入结业成果全部数据
     */
    async queryIssueResultAllData(item: any) {
      const res = await this.queryImportAsyncTask.queryGraduationResultAllData(item.id)
      const fileUrl = '/mfs' + res.filePath
      const fileName = res.fileName ?? res.filePath.split('/')[res.filePath.split('/').length - 1].split('?')[0]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 查看按人员名单打印相关失败数据
     */
    async findImportPrintFailData(item: any) {
      const res = await this.queryImportAsyncTask.findImportPrintFailData(item.id)
      if (!this.hasDownLoadLink(res)) return
      const fileUrl = location.origin + res
      const fileName = res.split('/')[res.split('/').length - 1].split('?')[0]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 查看外链课件相关失败数据
     */
    async queryOuterCoursewareFailData(item: any) {
      const res = await this.queryImportAsyncTask.queryOuterCoursewareFailData(item.id)
      if (!this.hasDownLoadLink(res)) return
      const fileUrl = location.origin + res
      const fileName = res.split('/')[res.split('/').length - 1].split('?')[0]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 下载发票相关全部失败数据
     */
    async queryInvoiceImportFailData(item: any) {
      const res = await this.queryImportAsyncTask.queryInvoiceImportFailData(item.id)
      if (!this.hasDownLoadLink(res)) return
      const fali = res.substring(res.indexOf('/') + 4, res.length)
      const fileUrl = fali
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 下载批量更新方案全部失败数据
     */
    async queryBatchUpdateSchemeFailData(item: any) {
      const res = await this.queryImportAsyncTask.queryBatchUpdateSchemeFailData(item.id)
      if (!this.hasDownLoadLink(res)) return
      const fali = location.origin + '/mfs' + res
      const fileUrl = fali
      const fileName = res.split('/')[res.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 下载结业成果全部失败数据
     */
    async queryIssueResultFailData(item: any) {
      const res = await this.queryImportAsyncTask.queryGraduationResultFailData(item.id)
      if (!this.hasDownLoadLink(res.filePath)) return
      const fali = location.origin + '/mfs' + res.filePath
      const fileUrl = fali
      const fileName = res.fileName ?? res.filePath.split('/')[res.filePath.split('/').length - 1]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 查询批量更新方案展示 全部数据
     */
    async queryBatchUpdatePlanShowAllData(item: any) {
      const res = await this.queryImportAsyncTask.queryBatchUpdatePlanShowAllData(item.id)
      const fileUrl = '/mfs' + res.fileUrl
      const fileName = res.fileUrl.split('/')[res.fileUrl.split('/').length - 1].split('?')[0]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 查询批量更新方案展示 失败数据
     */
    async queryBatchUpdatePlanShowFailData(item: any) {
      const res = await this.queryImportAsyncTask.queryBatchUpdatePlanShowFailData(item.id)
      const fileUrl = '/mfs' + res.fileUrl
      const fileName = res.fileUrl.split('/')[res.fileUrl.split('/').length - 1].split('?')[0]
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const downloadFile = new Downloader(fileUrl, fileName, header)
      downloadFile.download()
    }

    /**
     * 查看日志
     */
    viewLog(item: any) {
      if (item.processResult == 1) {
        this.$message.success('执行成功')
      } else {
        this.$message.error(item.message)
      }
    }

    // 下载链接是否存在
    hasDownLoadLink(link: string) {
      if (link) return true
      if (!link) {
        this.$message.error('下载数据失败！')
        return false
      }
    }

    async intelligenceConfig() {
      try {
        const serviceId = Context?.businessEnvironment?.serviceToken?.tokenMeta?.servicerId
        const res = await this.intelligenceLearningModule.doQueryServiceConfigByServicerId(serviceId)
        this.intelligenceShow = res == 0 ? false : true
      } catch (error) {
        console.log(error)
      }
    }
  }
</script>
