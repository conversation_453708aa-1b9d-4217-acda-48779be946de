import UserModule from '@api/service/customer/user/UserModule'
import PlatformJxjypxtyptAhzjSchool, { CreateOrderRequest } from '@api/platform-gateway/platform-jxjypxtypt-ahzj-school'
import CreateOrderParams from '@api/service/diff/customer/anhui/create-order/CreateOrderParams'
import CreateOrderCommon from "@api/service/customer/trade/single/mutation/customer-order/vo/create-order/CreateOrder";
export default class CreateOrder extends CreateOrderCommon {
  //  创建订单入参
  createOrderParams: CreateOrderParams = new CreateOrderParams()
  // constructor() {}
  /**
   * @description: 创建订单 待差异化
   * @return {*}
   */
  /**
   * 下单渠道token (目前只有分销使用)
   */
  channelToken: string = undefined

  async doCreateOrder() {
    const res = await PlatformJxjypxtyptAhzjSchool.createOrder(this.createOrderParams.toGeneralCreateOrderRequest())
    if (res.status.isSuccess() && !res.data.success) {
      res.status.message = res.data.message
      res.status.code = 9032
    }
    return res
  }
}
