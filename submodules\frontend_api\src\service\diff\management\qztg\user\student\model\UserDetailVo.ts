import UserVo from '@api/service/management/user/query/student/vo/UserDetailVo'
import AccountSourceTypes from '@api/service/diff/management/qztg/user/student/enums/AccountSourceTypes'
import { StudentInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
/*
 * 用户详情
 */
class UserDetailVo extends UserVo {
  from(item: StudentInfoResponse, nameMap?: Map<string, string>) {
    super.from(item, nameMap)
    this.sourceTypeName = AccountSourceTypes.map.get(item.accountInfo?.sourceType)
  }
}

export default UserDetailVo
