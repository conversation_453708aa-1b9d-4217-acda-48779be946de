<template>
  <div>
    <el-result icon="success" title="新建专题完成并保存成功！">
      <template slot="extra">
        <div>是否关闭当前页面? <a class="f-cb f-ml5" @click="close">立即关闭</a></div>
        <div class="f-mt10">是否继续新建专题?<a class="f-cb f-ml5" @click="newCreate">继续新建专题</a></div>
        <div class="f-mt10" v-if="!isShow">
          是否前往配置专题集体报名?<a class="f-cb f-ml5" @click="gotoUnitApply">立即前往</a>
        </div>
        <div class="f-mt10">是否前往配置专题精品课程?<a class="f-cb f-ml5" @click="gotoCourse">立即前往</a></div>
      </template>
    </el-result>
  </div>
</template>

<script lang="ts">
  import { SubjectType } from '@api/service/management/thematic-management/enum/SubjectType'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  import { Component, Prop, Vue } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    //TODO
    @Prop({ type: Object, default: () => new ThematicManagementItem() }) thematicManagementItem: ThematicManagementItem
    newCreate() {
      this.$emit('newCreate')
    }
    close() {
      this.$router.push({
        path: '/training/special-topics/manage'
      })
    }

    // 判断专题是否有单位属性，有则不显示集体相关
    get isShow() {
      return this.thematicManagementItem.basicInfo.subjectType.includes(SubjectType.unit)
    }

    gotoUnitApply() {
      this.$router.push({
        path: `/training/special-topics/manage/edit/${this.thematicManagementItem.topicID}`,
        query: {
          activeName: 'onlineApply'
        }
      })
    }
    gotoCourse() {
      this.$router.push({
        path: `/training/special-topics/manage/edit/${this.thematicManagementItem.topicID}`,
        query: {
          activeName: 'premiumCourse'
        }
      })
    }
  }
</script>
