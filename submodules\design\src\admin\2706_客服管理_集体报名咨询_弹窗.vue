<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--查看下单结果-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">查看下单结果</el-button>
        <el-drawer
          title="查看下单结果"
          :visible.sync="dialog1"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-text-form is-column f-ml10 f-mt10">
              <el-form-item label="集体报名批次号：">batch210820181132748989080007</el-form-item>
              <el-form-item label="下单结果："><el-tag type="success" size="small">处理完成</el-tag></el-form-item>
              <el-form-item label="缴费人次：">2</el-form-item>
              <el-form-item label="下单时间：">2021.02.02 14:00:00</el-form-item>
              <el-form-item label="实付金额（元）：">200</el-form-item>
              <el-form-item label="付款成功时间：">2021.02.02 14:00:00</el-form-item>
              <el-form-item label="支付方式：">集体报名-线下支付</el-form-item>
              <el-form-item label="处理结果说明：">
                当前缴费人次
                <span class="f-co">2</span>
                次，处理成功
                <span class="f-co">2</span>
                ，处理失败
                <span class="f-co">0</span>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <p>发票信息 相关弹窗详见 2209_发票管理_弹窗.vue</p>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
