<!--编排任务-->
<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <!--条件查询-->
      <hb-search-wrapper :model="trackingParam" @reset="reset">
        <el-form-item label="培训方案" v-if="!isZtglyLogin">
          <biz-learning-scheme-select v-model="hasSelectSchemeMode" :isOnlyOnline="true"></biz-learning-scheme-select>
        </el-form-item>
        <el-form-item label="培训方案" v-if="isZtglyLogin">
          <biz-learning-scheme-zt-select
            v-model="hasSelectSchemeMode"
            :isOnlyOnline="true"
          ></biz-learning-scheme-zt-select>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="trackingParam.name" clearable placeholder="请输入学员姓名" />
        </el-form-item>
        <el-form-item label="证件号">
          <el-input v-model="trackingParam.idCard" clearable placeholder="请输入学员证件号" />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="trackingParam.phone" clearable placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="导入任务">
          <import-task v-model="trackingParam.importTaskId" inputId="arrangementId" />
        </el-form-item>
        <el-form-item label="开通状态">
          <el-select v-model="trackingParam.orderStatus" clearable filterable placeholder="请选择">
            <el-option v-for="status in orderStatusList" :key="status.code" :label="status.desc" :value="status.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="智能选课/学习编排状态">
          <el-select v-model="trackingParam.learningState" clearable filterable placeholder="请选择">
            <el-option
              v-for="status in learningStatusList"
              :key="status.code"
              :label="status.desc"
              :value="status.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="导入时间">
          <double-date-picker
            :begin-create-time.sync="trackingParam.importTimeRange.startTime"
            :end-create-time.sync="trackingParam.importTimeRange.endTime"
            begin-time-placeholder="请选择导入开始时间"
            end-time-placeholder="请选择导入结束时间"
          ></double-date-picker>
        </el-form-item>
        <template slot="actions">
          <el-button type="primary" @click="queryList">查询</el-button>
        </template>
      </hb-search-wrapper>
      <!--表格-->
      <el-table stripe :data="arrangementResultList" max-height="500px" class="m-table" v-loading="loading">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="学员帐号信息" min-width="240" fixed="left">
          <template slot-scope="scope">
            <p>姓名：{{ scope.row.name || '-' }}</p>
            <p>证件号：{{ scope.row.idCard || '-' }}</p>
          </template>
        </el-table-column>
        <el-table-column label="培训方案名称" min-width="300">
          <template slot-scope="scope">
            {{ scope.row.trainingSchemeName || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="订单号" min-width="220">
          <template slot-scope="scope">{{ scope.row.orderNo || '-' }}</template>
        </el-table-column>
        <el-table-column label="班级开通状态" min-width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.orderState.equal(OrderStatusEnum.un_open)">
              <el-badge is-dot type="primary" class="badge-status">等待开通</el-badge>
            </div>
            <div v-else-if="scope.row.orderState.equal(OrderStatusEnum.open_ing)">
              <el-badge is-dot type="primary" class="badge-status">开通中</el-badge>
            </div>
            <div v-else-if="scope.row.orderState.equal(OrderStatusEnum.open_success)">
              <el-badge is-dot type="success" class="badge-status">已开通</el-badge>
            </div>
            <div v-else-if="scope.row.orderState.equal(OrderStatusEnum.open_fail)">
              <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                <el-badge is-dot type="danger" class="badge-status">开通失败</el-badge>
                <div slot="content">失败原因：{{ scope.row.errorMessage }}</div>
              </el-tooltip>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column label="智能选课/学习编排状态" min-width="180">
          <template slot-scope="scope">
            <div v-if="scope.row.learningState.equal(LearningStatusEnum.waiting_process)">
              <el-badge is-dot type="primary" class="badge-status">等待处理</el-badge>
            </div>
            <div v-else-if="scope.row.learningState.equal(LearningStatusEnum.processing)">
              <el-badge is-dot type="primary" class="badge-status">进行中</el-badge>
            </div>
            <div v-else-if="scope.row.learningState.equal(LearningStatusEnum.process_success)">
              <el-badge is-dot type="success" class="badge-status">处理成功</el-badge>
            </div>
            <div v-else-if="scope.row.learningState.equal(LearningStatusEnum.process_fail)">
              <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                <el-badge is-dot type="danger" class="badge-status">处理失败</el-badge>
                <div slot="content">失败原因：{{ scope.row.errorMessage }}</div>
              </el-tooltip>
            </div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" min-width="180">
          <template slot-scope="scope">{{ scope.row.startTime }}</template>
        </el-table-column>
        <el-table-column min-width="240" align="center">
          <template slot="header"
            >是否更新密码/基础信息<el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="m-tooltip is-small"
            >
              <span class="el-icon-warning f-co f-ml5 f-f16"></span>
              <div slot="content">
                仅针对平台已存在的用户去标记，本次的导入是否<br />更新密码、是否更新基础信息。如果是新用户则对<br />应的说明项显示
                “-”。
              </div>
            </el-tooltip></template
          >
          <template slot-scope="scope">{{ scope.row.isRejuvenation }}</template>
        </el-table-column>
        <el-table-column label="完成时间" min-width="180">
          <template slot-scope="scope">{{ scope.row.endTime }}</template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </el-card>
  </div>
</template>
<script lang="ts">
  import { Vue, Component } from 'vue-property-decorator'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import ArrangementResultListInServicer from '@api/service/management/intelligence-learning/ArrangementResultListInServicer'
  import ArrangementResultListInTrainingChannel from '@api/service/management/intelligence-learning/ArrangementResultListInTrainingChannel'
  import { UiPage } from '@hbfe/common'
  import ArrangementResultItem from '@api/service/management/intelligence-learning/model/ArrangementResultItem'
  import TrackingParam from '@api/service/management/intelligence-learning/model/TrackingParam'
  import BizLearningSchemeZtSelect from '@hbfe/jxjy-admin-components/src/biz/biz-learning-scheme-zt-select.vue'
  import BizLearningSchemeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-learning-scheme-select.vue'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import OrderStatusType from '@api/service/management/intelligence-learning/enum/OrderStatusEnum'
  import LearningStatusType, {
    LearningStatusEnum
  } from '@api/service/management/intelligence-learning/enum/LearningStatusEnum'
  import { OrderStatusEnum } from '@api/service/management/intelligence-learning/enum/OrderStatusEnum'
  import ImportTask from '@hbfe/jxjy-admin-intelligentLearning/src/learning-task-tracking/components/import-task.vue'
  import { bind, debounce } from 'lodash-decorators'

  @Component({
    components: {
      BizLearningSchemeZtSelect,
      BizLearningSchemeSelect,
      DoubleDatePicker,
      ImportTask
    }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.searchQuery, this.searchQuery)
    }

    /**
     * 分页
     */
    page: UiPage

    /**
     * 加载状态
     */
    loading = false

    /**
     * 开通状态枚举
     */
    OrderStatusEnum = OrderStatusEnum

    /**
     * 编排处理枚举
     */
    LearningStatusEnum = LearningStatusEnum

    /**
     * 查询入参
     */
    trackingParam = new TrackingParam()

    /**
     * 方案模型
     */
    hasSelectSchemeMode = new Array<HasSelectSchemeMode>()

    /**
     * 列表数据
     */
    arrangementResultList = new Array<ArrangementResultItem>()

    /**
     * 获取开通状态
     */
    get orderStatusList() {
      const orderStatusType = new OrderStatusType()
      return orderStatusType.list()
    }

    /**
     * 获取编排状态
     */
    get learningStatusList() {
      const learningStatusType = new LearningStatusType()
      return learningStatusType.list()
    }

    /**
     * 判断当前用户是否拥有专题管理员角色类型
     */
    get isZtglyLogin() {
      return QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    }

    /**
     * 查询按钮
     */
    @bind
    @debounce(200)
    async queryList() {
      await this.page.currentChange(1)
    }

    /**
     * 重置
     */
    @bind
    @debounce(200)
    async reset() {
      this.trackingParam = new TrackingParam()
      this.hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
      await this.page.currentChange(1)
    }

    /**
     * 查询
     */
    async searchQuery() {
      if (this.isZtglyLogin) {
        await this.queryChannelList()
      } else {
        await this.queryServerList()
      }
    }

    /**
     * 网校查询编排任务
     */
    async queryServerList() {
      try {
        this.loading = true
        const mutation = new ArrangementResultListInServicer()
        if (this.hasSelectSchemeMode.length) {
          this.trackingParam.trainingSchemeName = this.hasSelectSchemeMode[0].scheme
        } else {
          this.trackingParam.trainingSchemeName = ''
        }
        mutation.queryParam = this.trackingParam
        const res = await mutation.queryList(this.page)
        if (res.isSuccess()) {
          this.arrangementResultList = mutation.list
          console.log(this.arrangementResultList, 'this.arrangementResultList')
        } else {
          this.$message.error(res.getMessage() || '请求失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 查询专题编排任务
     */
    async queryChannelList() {
      try {
        this.loading = true
        const mutation = new ArrangementResultListInTrainingChannel()
        if (this.hasSelectSchemeMode.length) {
          this.trackingParam.trainingSchemeName = this.hasSelectSchemeMode[0].scheme
        } else {
          this.trackingParam.trainingSchemeName = ''
        }
        mutation.queryParam = this.trackingParam
        const res = await mutation.queryList(this.page)
        if (res.isSuccess()) {
          this.arrangementResultList = mutation.list
        } else {
          this.$message.error(res.getMessage() || '请求失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }
  }
</script>
