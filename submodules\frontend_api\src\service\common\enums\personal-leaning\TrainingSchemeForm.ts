import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 适用培训方案形式
 */
export enum TrainingSchemeFormEnum {
  'TRAINING_CLASS' = 'trainingClass'
}

class TrainingSchemeForm extends AbstractEnum<TrainingSchemeFormEnum> {
  static enum = TrainingSchemeFormEnum

  constructor(status?: TrainingSchemeFormEnum) {
    super()
    this.current = status
    this.map.set(TrainingSchemeFormEnum.TRAINING_CLASS, '培训班')
  }
}

export default new TrainingSchemeForm()
