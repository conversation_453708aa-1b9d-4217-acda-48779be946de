/*
 * @Description: 个人对账查询
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:32:55
 * @LastEditors: <PERSON><PERSON>ong
 * @LastEditTime: 2022-11-08 10:31:47
 */
import { Page } from '@hbfe/common'
import UserModule from '@api/service/management/user/UserModule'
import StudentUserInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'
import MsTradeQueryFrontGatewayCourseLearningBacktage, {
  OrderRequest,
  OrderStatisticResponse,
  ReturnOrderRequest,
  ReturnOrderSortField,
  ReturnSortRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import MsTradeQuery, {
  OrderSortField,
  OrderSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import CheckAccountParam from '@api/service/diff/management/zztt/trade/order/model/CheckAccountParam'
import RefundCheckAccountParam from '@api/service/diff/management/zztt/trade/order/model/RefundCheckAccountParam'
import CheckAccountListResponse from '@api/service/diff/management/zztt/trade/order/model/CheckAccountListResponse'
import RefundCheckAccountListResponse from '@api/service/diff/management/zztt/trade/order/model/RefundCheckAccountListResponse'
import QueryPlatform from '@api/service/diff/common/zztt/dictionary/QueryPlatform'
import DataExportBackstage from '@api/diff-gateway/zztt-data-export-gateway-backstage'
import statisticOrderInServicer from '@api/service/management/trade/single/order/query/graphql/statisticOrderInServicer.graphql'
import TradeQueryFrontGatewayTradeQueryBackstage from '@api/diff-gateway/zztt-trade-query-front-gateway-TradeQueryBackstage'
import { ReturnOrderBasicDataRequest } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import { ReturnOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
export default class QueryCheckAccount {
  /**
   * 报名订单数
   */
  orderCount = 0
  /**
   * 交易总金额
   */
  amount = 0
  /**
   * 退款订单数
   */
  refundTradeCount = 0
  /**
   * 退款总金额
   */
  refundAmountCount = 0

  /**
   * 报名订单分页查询
   * @param page 页数
   * @param queryCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<CheckAccountListResponse>
   */
  async queryOfRegistrationOrder(
    page: Page,
    checkAccountParam: CheckAccountParam
  ): Promise<Array<CheckAccountListResponse>> {
    const request = CheckAccountParam.to(checkAccountParam)
    const sort = new OrderSortRequest()
    sort.field = OrderSortField.ORDER_NORMAL_TIME
    sort.policy = SortPolicy.DESC
    const { data } = await MsTradeQuery.pageOrderInServicer({
      page,
      request,
      sortRequest: [sort]
    })
    await this.statisticOrderInServicerRemoveTotalPeriod(request)
    await QueryPlatform.queryList()
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const orderArr = new Array<CheckAccountListResponse>()
    const userIdList = new Array<string>()
    data.currentPageData.map((item) => {
      const data = CheckAccountListResponse.from(item)
      userIdList.push(data.userId)
      orderArr.push(data)
    })
    const map = await this.getUserInfo(userIdList)

    orderArr.map((item) => {
      const userInfo = map.get(item.userId)
      if (userInfo) {
        item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
      }
    })
    return orderArr
  }
  private async statisticOrderInServicer(orderRequest: OrderRequest) {
    const { data } = await MsTradeQuery.statisticOrderInServicer(orderRequest)
    this.orderCount = data?.totalOrderCount || 0
    this.amount = data?.totalOrderAmount || 0
  }
  private async statisticOrderInServicerRemoveTotalPeriod(orderRequest: OrderRequest) {
    const response = await MsTradeQuery._commonQuery<OrderStatisticResponse>(statisticOrderInServicer, {
      request: orderRequest
    })
    this.orderCount = response.data?.totalOrderCount || 0
    this.amount = response.data?.totalOrderAmount || 0
  }
  /**
   * 报名订单分页查询
   * @param page 页数
   * @param queryCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<CheckAccountListResponse>
   */
  async queryOfFxRegistrationOrder(
    page: Page,
    checkAccountParam: CheckAccountParam
  ): Promise<Array<CheckAccountListResponse>> {
    const request = CheckAccountParam.to(checkAccountParam)
    const sort = new OrderSortRequest()
    sort.field = OrderSortField.ORDER_NORMAL_TIME
    sort.policy = SortPolicy.DESC
    const { data } = await MsTradeQuery.pageOrderInDistributor({
      page,
      request,
      sortRequest: [sort]
    })
    await this.statisticFxOrderInServicer(request)
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const orderArr = new Array<CheckAccountListResponse>()
    const userIdList = new Array<string>()
    data.currentPageData.map((item) => {
      const data = CheckAccountListResponse.from(item)
      userIdList.push(data.userId)
      orderArr.push(data)
    })
    const map = await this.getUserInfo(userIdList)

    orderArr.map((item) => {
      const userInfo = map.get(item.userId)
      if (userInfo) {
        item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
      }
    })
    return orderArr
  }
  private async statisticFxOrderInServicer(orderRequest: OrderRequest) {
    const { data } = await MsTradeQuery.statisticOrderInDistributor(orderRequest)
    this.orderCount = data?.totalOrderCount || 0
    this.amount = data?.totalOrderAmount || 0
  }
  /**
   * 退款订单分页查询
   * @param page 页数
   * @param queryRefundCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<RefundCheckAccountListResponse>
   */
  async queryOfRefundOrder(
    page: Page,
    queryRefundCheckAccountParam: RefundCheckAccountParam
  ): Promise<Array<RefundCheckAccountListResponse>> {
    const request = RefundCheckAccountParam.refurnTo(queryRefundCheckAccountParam)
    const sort = new ReturnSortRequest()
    sort.field = ReturnOrderSortField.APPLIED_TIME
    sort.policy = SortPolicy.DESC

    const { data } = await TradeQueryFrontGatewayTradeQueryBackstage.pageReturnOrderInServicer({
      page: page,
      request,
      sort: [sort]
    })
    await this.queryStatisticReturnOrder(request)
    await QueryPlatform.queryList()
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const refundArr = new Array<RefundCheckAccountListResponse>()
    const userIdList = new Array<string>()
    data.currentPageData.map((item) => {
      const data = RefundCheckAccountListResponse.from(Object.assign(item, new ReturnOrderResponse()))
      userIdList.push(data.userId)
      refundArr.push(data)
    })
    const map = await this.getUserInfo(userIdList)
    refundArr.map((item) => {
      const userInfo = map.get(item.userId)
      if (userInfo) {
        item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
      }
      item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
    })
    return refundArr
  }
  /**
   * 分销退款订单分页查询
   * @param page 页数
   * @param queryRefundCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<RefundCheckAccountListResponse>
   */
  async queryOfFxRefundOrder(
    page: Page,
    queryRefundCheckAccountParam: RefundCheckAccountParam
  ): Promise<Array<RefundCheckAccountListResponse>> {
    const request = RefundCheckAccountParam.refurnTo(queryRefundCheckAccountParam)
    const sort = new ReturnSortRequest()
    sort.field = ReturnOrderSortField.APPLIED_TIME
    sort.policy = SortPolicy.DESC

    const { data } = await TradeQueryFrontGatewayTradeQueryBackstage.pageReturnOrderInDistributor({
      page: page,
      request,
      sort: [sort]
    })
    await this.queryFxStatisticReturnOrder(request)
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const refundArr = new Array<RefundCheckAccountListResponse>()
    const userIdList = new Array<string>()
    data.currentPageData.map((item) => {
      const data = RefundCheckAccountListResponse.from(Object.assign(item, new ReturnOrderResponse()))
      userIdList.push(data.userId)
      refundArr.push(data)
    })
    const map = await this.getUserInfo(userIdList)
    refundArr.map((item) => {
      const userInfo = map.get(item.userId)
      if (userInfo) {
        item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
      }
      item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
    })
    return refundArr
  }

  private async queryFxStatisticReturnOrder(request: ReturnOrderRequest) {
    const totalRes = await MsTradeQueryFrontGatewayCourseLearningBacktage.statisticReturnOrderInDistributor(request)
    if (totalRes.status.isSuccess()) {
      if (totalRes.data) {
        this.refundAmountCount = totalRes.data.totalRefundAmount
        this.refundTradeCount = totalRes.data.totalReturnOrderCount
      } else {
        this.refundAmountCount = 0
        this.refundTradeCount = 0
      }
    }
  }
  /**
   * 个人报名对账导出
   */
  async listExport(checkAccountParam: CheckAccountParam, sortRequest?: Array<OrderSortRequest>): Promise<boolean> {
    const request = CheckAccountParam.to(checkAccountParam)
    const { data } = await DataExportBackstage.exportReconciliationExcelInServicer({
      request,
      sort: sortRequest
    })
    return data
  }
  /**
   * 个人报名对账导出（分销）
   */
  async listFxExport(checkAccountParam: CheckAccountParam, sortRequest?: Array<OrderSortRequest>): Promise<boolean> {
    const request = CheckAccountParam.to(checkAccountParam)
    const { data } = await DataExportBackstage.exportReconciliationExcelInDistributor({ request, sort: sortRequest })
    return data
  }
  /**
   * 个人退款对账导出
   */
  async listReturnExport(
    checkAccountParam: CheckAccountParam,
    sortRequest?: Array<ReturnSortRequest>
  ): Promise<boolean> {
    const request = CheckAccountParam.toReturn(checkAccountParam)
    const { data } = await DataExportBackstage.exportReturnReconciliationExcelInServicer({
      request,
      sort: sortRequest
    })
    return data
  }
  /**
   * 个人退款对账导出（分销）
   */
  async listFxReturnExport(
    checkAccountParam: CheckAccountParam,
    sortRequest?: Array<ReturnSortRequest>
  ): Promise<boolean> {
    const request = CheckAccountParam.toReturn(checkAccountParam)
    const { data } = await DataExportBackstage.exportReturnReconciliationExcelInDistributor({
      request,
      sort: sortRequest
    })
    return data
  }

  private async queryStatisticReturnOrder(request: ReturnOrderRequest) {
    const totalRes = await MsTradeQueryFrontGatewayCourseLearningBacktage.statisticReturnOrderInServicer(request)
    if (totalRes.status.isSuccess()) {
      if (totalRes.data) {
        this.refundAmountCount = totalRes.data.totalRefundAmount
        this.refundTradeCount = totalRes.data.totalReturnOrderCount
      } else {
        this.refundAmountCount = 0
        this.refundTradeCount = 0
      }
    }
  }

  /**
   * 获取用户信息
   * @param ids id数组
   */
  private async getUserInfo(ids: Array<string>) {
    // 根据用户ID获取用户信息
    const response = await UserModule.queryUserFactory.queryStudentList.queryStudentListInSubject(ids)
    const userIdMap: Map<string, StudentUserInfoVo> = new Map()
    response.data.forEach((item) => {
      userIdMap.set(item.userId, item)
    })
    return userIdMap
  }
}
