<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">学员管理</el-breadcrumb-item>
      <el-breadcrumb-item>学员详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <div class="f-plr45 f-pt20 f-pb10">
          <el-row :gutter="16">
            <el-form :inline="true" label-width="auto" class="m-text-form">
              <el-col :sm="12" :md="8">
                <el-form-item label="姓名：">林依依</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="性别：">未知</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="证件号：">356247854125896547</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="手机号：">1574596321</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="地区：">福建省-福州市-鼓楼区</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="注册时间：">2012-11-05 14:23:12</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="注册来源：">主网站</el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">行业信息</span>
        </div>
        <div class="f-pb20 f-pt10">
          <div class="m-sub-tit is-border-bottom">
            <span class="tit-txt">人社行业</span>
          </div>
          <div class="f-plr40">
            <el-row :gutter="16">
              <el-form :inline="true" label-width="auto" class="m-text-form f-mt10">
                <el-col :sm="12" :md="8">
                  <el-form-item label="专业类别：">系列-具体专业</el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="职称等级：">中级</el-form-item>
                </el-col>
              </el-form>
            </el-row>
          </div>
          <div class="m-sub-tit is-border-bottom f-mt10">
            <span class="tit-txt">建设行业</span>
          </div>
          <div class="f-plr40">
            <p class="f-cb f-mt20 f-f15">二级建造师</p>
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column label="专业" min-width="200" fixed="left">
                <template>具体专业具体专业具体专业</template>
              </el-table-column>
              <el-table-column label="证书编号" width="200">
                <template>201145874526</template>
              </el-table-column>
              <el-table-column label="证书发放日期-证书有效期" min-width="200">
                <template>2020-11-11<span class="f-plr10">-</span>2020-11-11</template>
              </el-table-column>
              <el-table-column label="附件" width="400">
                <template>
                  <ul class="m-certificate-img f-clear">
                    <li>
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="img"
                      >
                      </el-image>
                    </li>
                    <li>
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="img"
                      >
                      </el-image>
                    </li>
                    <li>
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="img"
                      >
                      </el-image>
                    </li>
                    <li>
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="img"
                      >
                      </el-image>
                    </li>
                    <li>
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="img"
                      >
                      </el-image>
                    </li>
                    <li>
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="img"
                      >
                      </el-image>
                    </li>
                  </ul>
                </template>
              </el-table-column>
            </el-table>
            <p class="f-cb f-mt20 f-f15">注册造价工程师</p>
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column label="专业" min-width="200" fixed="left">
                <template>具体专业具体专业具体专业</template>
              </el-table-column>
              <el-table-column label="证书编号" width="200">
                <template>201145874526</template>
              </el-table-column>
              <el-table-column label="证书发放日期-证书有效期" min-width="200">
                <template>2020-11-11<span class="f-plr10">-</span>2020-11-11</template>
              </el-table-column>
              <el-table-column label="附件" width="400">
                <template>
                  <ul class="m-certificate-img f-clear">
                    <li>
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="img"
                      >
                      </el-image>
                    </li>
                    <li>
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="img"
                      >
                      </el-image>
                    </li>
                    <li>
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="img"
                      >
                      </el-image>
                    </li>
                    <li>
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="img"
                      >
                      </el-image>
                    </li>
                    <li>
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="img"
                      >
                      </el-image>
                    </li>
                    <li>
                      <el-image
                        src="/assets/images/web-default-banner.jpg"
                        :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        class="img"
                      >
                      </el-image>
                    </li>
                  </ul>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="m-sub-tit is-border-bottom">
            <span class="tit-txt">职业卫生行业</span>
          </div>
          <div class="f-plr40">
            <el-row :gutter="16">
              <el-form :inline="true" label-width="auto" class="m-text-form f-mt10">
                <el-col :sm="12" :md="8">
                  <el-form-item label="人员类别：">读取人员类别</el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="岗位类别：">读取岗位类别</el-form-item>
                </el-col>
              </el-form>
            </el-row>
          </div>
          <div class="m-sub-tit is-border-bottom">
            <span class="tit-txt">工勤行业</span>
          </div>
          <div class="f-plr40">
            <el-row :gutter="16">
              <el-form :inline="true" label-width="auto" class="m-text-form f-mt10">
                <el-col :sm="12" :md="8">
                  <el-form-item label="技术等级：">读取人员技术等级</el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="工种：">读取人员工种</el-form-item>
                </el-col>
              </el-form>
            </el-row>
          </div>
          <div class="m-sub-tit is-border-bottom">
            <span class="tit-txt">教师行业</span>
          </div>
          <div class="f-plr40">
            <el-row :gutter="16">
              <el-form :inline="true" label-width="82px" class="m-text-form f-mt10">
                <el-col :sm="12" :md="8">
                  <el-form-item label="学段：">读取学段</el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="学科：">读取学科</el-form-item>
                </el-col>
              </el-form>
            </el-row>
          </div>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">专题信息</span>
        </div>
        <div class="f-pb20 f-pt10">
          <div class="m-sub-tit is-border-bottom">
            <span class="tit-txt">金昌市</span>
          </div>
          <div class="f-plr40">
            <el-row :gutter="16">
              <el-form :inline="true" label-width="auto" class="m-text-form is-edit f-mt20">
                <el-col :sm="12" :md="8">
                  <el-form-item label="工作单位性质：">xxx</el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="在编情况：">xxx</el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="是否在专技岗位工作：">xxx</el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="职称系列：">xxx</el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="职称专业：">xxx</el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="现有职称等级：">xxx</el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="现有职称资格名称：">xxx</el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="现有职称有效范围：">xxx</el-form-item>
                </el-col>
                <el-col :sm="12" :md="8">
                  <el-form-item label="最高学历：">xxx</el-form-item>
                </el-col>
              </el-form>
            </el-row>
          </div>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">培训记录</span>
        </div>
        <div class="f-p20">
          <!--表格-->
          <el-table stripe :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="培训方案" min-width="300">
              <template>培训方案培训方案培训方案</template>
            </el-table-column>
            <el-table-column label="报名时间" min-width="180">
              <template>2020-11-11 12:20:20</template>
            </el-table-column>
            <el-table-column label="是否合格" min-width="120">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  <el-badge is-dot type="danger" class="badge-status">未合格</el-badge>
                </div>
                <div v-else>
                  <el-badge is-dot type="success" class="badge-status">合格</el-badge>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="考核通过时间" min-width="180">
              <template>2020-11-11 12:20:20</template>
            </el-table-column>
            <el-table-column label="报名方式" min-width="140">
              <template>自主报名</template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
        </div>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
