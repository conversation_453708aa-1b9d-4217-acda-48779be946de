<route-meta>
{
"isMenu": true,
"title": "学员学习明细",
"sort": 5,
"icon": "icon-mingxi"
}
</route-meta>
<template>
  <JxgxLearningStatistic></JxgxLearningStatistic>
</template>

<script lang="ts">
  import LearningStatistic from '@hbfe/jxjy-admin-learningStatistic/src/index.vue'
  import { Component, Vue } from 'vue-property-decorator'
  import StaticticalReportManagerModule from '@api/service/diff/management/jxgx/statisticalReport/StaticticalReportManagerModule'
  @Component
  class JxgxLearningStatistic extends LearningStatistic {
    async exportExcel() {
      return await StaticticalReportManagerModule.queryStaticticalReportFactory
        .getQueryStudentLearningList()
        .exportExcel(this.filter)
    }
  }
  @Component({
    components: {
      JxgxLearningStatistic
    }
  })
  export default class extends Vue {}
</script>
