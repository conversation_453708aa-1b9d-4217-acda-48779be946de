<template>
  <div class="f-p15" v-if="$hasPermission('orderInfo')" desc="订单详情" actions="searchBase">
    <el-card shadow="never" class="m-card f-mb15">
      <hb-search-wrapper :model="pageQueryParam" @reset="resetCondition" class="m-query">
        <el-form-item label="订单号">
          <el-input v-model="orderNo" clearable placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="orderStatus" clearable filterable placeholder="请选择">
            <el-option v-for="(item, index) in orderTransactionList" :key="index" :value="item.code" :label="item.desc">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="orderCreateTime"
            type="datetimerange"
            :value-format="'yyyy-MM-dd HH:mm:ss'"
            range-separator="至"
            start-placeholder="请选择订单创建时间"
            end-placeholder="请选择订单创建时间"
          >
          </el-date-picker>
        </el-form-item>
        <template slot="actions">
          <el-button type="primary" @click="searchBase" :loading="query.loading">查询</el-button>
        </template>
      </hb-search-wrapper>
      <!--表格-->
      <el-table stripe :data="orderList" max-height="500px" class="m-table" v-loading="query.loading" ref="tableRef">
        <!-- <el-table-column type="index" label="No." width="60" align="center"></el-table-column> -->
        <el-table-column label="NO." width="70" align="center">
          <template slot-scope="scope">
            <span :data-index="scope.$index + 1" v-observe-visibility="visibilityConfig">{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单号" min-width="240">
          <template slot-scope="scope">
            <div>
              {{ scope.row.orderNo }}
              <p>
                <el-tag type="warning" class="f-mr10" v-if="getChannelTypeName(scope.row)">{{
                  getChannelTypeName(scope.row)
                }}</el-tag>
                <!-- 分销推广字段还未提供 -->
                <el-tag type="warning" class="f-mr10" v-if="scope.row.saleChannel == SaleChannelEnum.distribution"
                  >分销推广：{{ scope.row.commodityAuthInfo.distributorName }}</el-tag
                >
                <el-tag
                  type="success"
                  size="small"
                  class="f-mr10"
                  v-show="scope.row.saleChannel === SaleChannelEnum.topic"
                  >专题</el-tag
                >
                <!-- <el-tag type="danger" class="f-mr10" v-show="scope.row.isExchange">换班</el-tag> -->
                <el-tag size="small" v-show="scope.row.changeOrderStatus.includes(ChangeOrderType.CLASS_TYPE)"
                  >换班</el-tag
                >
                <el-tag
                  type="warning"
                  size="small"
                  v-show="scope.row.changeOrderStatus.includes(ChangeOrderType.PERIOD_TYPE)"
                  >换期</el-tag
                >
                <el-tag type="danger" v-if="scope.row.thirdPartyPlatform">{{ scope.row.thirdPartyPlatform }}</el-tag>
              </p>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="购买内容" min-width="240">
          <template slot-scope="scope">
            <p>{{ scope.row.commodityName.join(';') }}</p>
            <p v-if="isMixedClass(scope.row.trainingFormId)">培训期别：{{ scope.row.trainingPeriodName || '-' }}</p>
          </template>
        </el-table-column>
        <el-table-column label="购买人信息" min-width="240">
          <template slot-scope="scope">
            <p>姓名：{{ scope.row.buyerInfo.userName }}</p>
            <p>证件号：{{ scope.row.buyerInfo.userIdCard }}</p>
            <p>手机号：{{ scope.row.buyerInfo.userPhoneNumber }}</p>
          </template>
        </el-table-column>
        <el-table-column label="购买学时" min-width="120" align="center">
          <template slot-scope="scope">{{ scope.row.period }}</template>
        </el-table-column>
        <el-table-column label="缴费方式" min-width="240">
          <template slot-scope="scope"
            ><span v-if="scope.row.saleChannel === SaleChannelEnum.huayi && scope.row.channelType === 5">
              {{ scope.row.orderStatus == 5 ? '-' : '网上报名-华医网' }} </span
            ><span v-else>{{ scope.row.orderStatus == 5 ? '-' : getPaymentType(scope.row) }}</span></template
          >
        </el-table-column>
        <el-table-column label="创建时间" min-width="180">
          <template slot-scope="scope">
            <div>{{ scope.row.createTime }}</div>
          </template>
        </el-table-column>
        <el-table-column label="实付金额(元)" min-width="140" align="right">
          <template slot-scope="scope">
            <div>{{ scope.row.payAmount }}</div>
          </template>
        </el-table-column>
        <el-table-column label="交易状态" min-width="120">
          <template slot-scope="scope">
            {{ orderTransaction(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="200" align="center" fixed="right">
          <template
            slot-scope="scope"
            actions="@hbfe/jxjy-admin-trade/src/order/personal/detail.vue"
            v-if="$hasPermission('detail')"
            desc="详情"
          >
            <el-button type="text" size="mini" v-if="scope.row.orderStatus === 4" @click="refundOrder(scope.row)"
              >发起退货/款</el-button
            >
            <el-button type="text" size="mini" @click="orderDetail(scope.row)">详情</el-button>
            <template v-if="$hasPermission('fillToInvoice')" desc="补要发票" actions="@ReapplyInvoice,fillToInvoice">
              <el-button
                type="text"
                size="mini"
                v-show="
                  scope.row.enableReApplyInvoice &&
                  scope.row.payAmount > 0 &&
                  scope.row.orderStatus === 4 &&
                  !scope.row.invoiceApplyInfo &&
                  scope.row.channelType != 2 &&
                  scope.row.returnOrderStatus !== ReturnOrderStatusEnum.refunding &&
                  scope.row.returnOrderStatus !== ReturnOrderStatusEnum.refunded
                "
                @click="fillToInvoice(scope.row)"
              >
                补要发票
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <reapply-invoice
      ref="reapplyInvoiceRef"
      :phone="userInfo.phone"
      :orderNo="listOrderNo"
      :invoiceConfig="invoiceConfig"
      :visible.sync="uiConfig.dialog.reApplyInvoiceVisible"
      @searchBase="searchBase"
    />
    <refund-dialog
      ref="refundDialogRef"
      :selectClassRuleName="getSchemeType(skuDetail)"
      :orderDetail="curItem"
    ></refund-dialog>
    <refund-dialog-diff
      ref="refundDialogDiffRef"
      :selectClassRuleName="getSchemeType(skuDetail)"
      :orderDetail="curItem"
    ></refund-dialog-diff>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import UtilClass from '@/common/util'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import OrderTerminalType, {
    OrderTerminal
  } from '@api/service/management/trade/single/order/query/enum/OrderTerminalType'
  import { ReturnOrderStatusEnum } from '@api/service/management/trade/single/order/enum/returnOrderStatusEnum'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import ReapplyInvoice from '@hbfe/jxjy-admin-customerService/src/personal/components/components/reapply-invoice.vue'
  import UserModules from '@api/service/management/user/UserModule'
  import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
  import TradeConfigModule from '@api/service/common/trade-config/TradeConfigModule'
  import { InvoiceConfigResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import OrderTransactionStatus, {
    OrderTransaction
  } from '@api/service/management/trade/single/order/query/enum/OrderTransactionStatus'
  import BatchOrderDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailVo'
  import { BatchOrderPayModeEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderPayMode'
  import QueryOrder from '@api/service/diff/management/xmlg/trade/order/QueryOrder'
  import OrderDetailVo from '@api/service/diff/management/xmlg/trade/order/model/OrderDetailVo'
  import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import QueryOrderListVo from '@api/service/diff/management/xmlg/trade/order/model/QueryOrderListVo'
  import { SaleChannelEnum } from '@api/service/diff/management/xmlg/trade/enums/SaleChannelType'
  import RefundDialog from '@hbfe/jxjy-admin-trade/src/order/components/refund-dialog.vue'
  import RefundDialogDiff from '@hbfe/jxjy-admin-trade/src/diff/xmlg/order/components/refund-dialog.vue'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplicationDiff } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import {
    SchemeResourceResponse,
    SubOrderResponse
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { ResourceEnum } from '@api/service/common/enums/trade/ResourceTypes'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import { ChangeOrderType } from '@api/service/common/trade/ChangeOrderType'
  @Component({
    components: { ReapplyInvoice, RefundDialog, RefundDialogDiff }
  })
  export default class extends Vue {
    // 学员id 由主文件ref传入
    userId = ''

    /**
     * 身份证
     */
    @Prop({
      type: String,
      default: ''
    })
    idCard: string

    /**
     * 姓名
     */
    @Prop({
      type: String,
      default: ''
    })
    userName: string

    SaleChannelEnum = SaleChannelEnum
    // @Watch('userId', {
    //   immediate: true,
    //   deep: true
    // })
    async userIdChange(val: any) {
      this.userId = val
      if (this.userId) {
        await this.resetCondition()
        await this.getUserInfo()
      } else {
        this.orderList = []
      }
    }

    @Ref('reapplyInvoiceRef') reapplyInvoiceRef: ReapplyInvoice
    @Ref('refundDialogRef') refundDialogRef: RefundDialog
    @Ref('refundDialogDiffRef') refundDialogDiffRef: RefundDialogDiff

    // 页面分页控件
    page: UiPage
    // 分页查询
    query: Query = new Query()
    // 分页查询参数
    pageQueryParam: QueryOrderListVo = new QueryOrderListVo()
    // 订单列表
    orderList: OrderDetailVo[] = [] as OrderDetailVo[]
    // 订单创建时间
    orderCreateTime: string[] = []
    // 接口请求
    queryOrderRemote: QueryOrder = new QueryOrder()
    //是否退款
    ReturnOrderStatusEnum = ReturnOrderStatusEnum
    // 状态列表
    orderTransactionList: EnumOption<OrderTransaction>[] = OrderTransactionStatus.list()
    // 销售渠道列表
    orderTerminalTypeList: EnumOption<OrderTerminal>[] = OrderTerminalType.list()
    // 订单列表对应的订单号
    listOrderNo = ''
    // 订单列表对应数据
    rowData = new OrderDetailVo()
    // 补要发票表单
    reApplyInvoiceForm = {}

    // 订单号
    orderNo = ''
    // 订单状态
    orderStatus: OrderTransaction = null

    // 学员信息
    userInfo = new UserDetailVo()
    ChangeOrderType = ChangeOrderType
    // 当前选中项
    curItem: OrderDetailVo = new OrderDetailVo()
    // sku详情 方案类型
    skuDetail = new SchemeResourceResponse()
    /**
     * ui控制组
     */
    uiConfig = {
      dialog: {
        // 补要发票
        reApplyInvoiceVisible: false
      }
    }

    // 发票配置
    invoiceConfig = new InvoiceConfigResponse()

    /**
     * 获取华医网特殊方案
     */
    get hywSchemeList(): string[] {
      const list: { xmlgSchemeId: string }[] = JSON.parse(
        ConfigCenterModule.getFrontendApplicationDiff(frontendApplicationDiff.hywSchemeId)
      )
      return list.map((item) => item.xmlgSchemeId)
    }

    /**
     * 是否是华医网特殊方案
     */
    get isHywSpecialScheme(): (item: OrderDetailVo) => boolean {
      const schemeIds = this.hywSchemeList
      return (item: OrderDetailVo) => schemeIds.includes(item.schemeId)
    }

    /**
     * 是否交易失败
     */
    get orderTransaction() {
      return (item: OrderDetailVo) => {
        return this.orderTransactionList.find((el) => el.code === item.orderStatus)?.desc || ''
      }
    }
    /**
     * 判断是否面授网班或者面授班
     */
    get isMixedClass() {
      return (id: string) => {
        return id === TrainingModeEnum.mixed || id === TrainingModeEnum.offline
      }
    }

    constructor() {
      super()
      this.page = new UiPage(this.pageOrderList, this.pageOrderList)
    }

    // 获取发票配置
    async hasStudentOpenInvoice() {
      this.invoiceConfig = await TradeConfigModule.queryTradeConfigFactory.getQueryTradeConfig().hasStudentOpenInvoice()
    }

    /**
     * 获取培训方案类型
     */
    getSchemeType(row: SchemeResourceResponse) {
      return SchemeType.getSchemeType(row.schemeType, true)
    }

    /**
     * 滑动加载 原理使用 data-set和数量进行比较
     */
    async visibilityConfig(isVisible: boolean, entry: any) {
      if (isVisible) {
        if (entry.target.dataset.index >= this.page.totalSize) {
          //   最大值时不请求
          return
        }
        if (parseInt(entry.target.dataset.index) == this.orderList.length) {
          this.page.pageNo = this.page.pageNo + 1
          const list = await this.queryOrderRemote.queryOrderList(this.page, this.pageQueryParam, true)
          this.orderList = this.orderList.concat(list)
          ;(this.$refs['tableRef'] as any)?.doLayout()
        }
      }
    }

    /**
     * 查询订单分页
     */
    async searchBase() {
      this.page.pageNo = 1
      await this.pageOrderList()
    }

    // 查询用户信息
    async getUserInfo() {
      const res = await UserModules.queryUserFactory.queryStudentDetail(this.userId).queryDetail()
      if (res.status.isSuccess()) {
        this.userInfo = res.data
      }
    }

    /**
     * 重置查询条件
     */
    async resetCondition() {
      this.orderCreateTime = [] as string[]
      this.pageQueryParam = new QueryOrderListVo()
      this.pageQueryParam.buyerId = this.userId || ''
      this.orderStatus = null
      await this.searchBase()
    }

    /**
     * 查询订单分页
     */
    async pageOrderList() {
      this.query.loading = true
      try {
        this.pageQueryParam = this.getPageQueryParam()
        this.pageQueryParam.haveCollection = true
        this.orderList = [] as OrderDetailVo[]
        if (!this.pageQueryParam.buyerId) {
          return
        }
        this.orderList = await this.queryOrderRemote.queryOrderList(this.page, this.pageQueryParam, true)
      } catch (e) {
        console.log(e, '加载订单列表失败')
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['tableRef'] as any)?.doLayout()
        this.query.loading = false
      }
    }

    /**
     * 获取查询参数
     */
    getPageQueryParam(): QueryOrderListVo {
      const query = new QueryOrderListVo()
      query.buyerId = this.userId || ''
      query.orderNo = this.orderNo || ''
      query.orderCreateTime = this.getOrderCreateTime()
      query.orderStatus = this.orderStatus || undefined
      return query
    }

    /**
     * 获取订单创建时间范围
     */
    getOrderCreateTime(): string[] | undefined {
      if (Array.isArray(this.orderCreateTime) && this.orderCreateTime.length) {
        return this.orderCreateTime
      } else {
        return []
      }
    }

    /**
     * 获取订单类型
     */
    getChannelTypeName(row: OrderDetailVo) {
      switch (row.channelType) {
        case 1:
        case 5:
          return '个人报名'
        case 2:
          return '集体报名'
        case 3:
          return '导入开通'
        default:
          return ''
      }
    }

    /**
     * 获取缴费方式
     */
    getPaymentType(row: OrderDetailVo): string {
      const result = [] as string[]
      if (row.channelType === 1) {
        // 个人报名
        result.push('网上报名')
        const terminalName = this.orderTerminalTypeList.find((el) => el.code === row.terminalCode)?.desc || ''
        const payChannelName = row.payChannelName
        result.push(terminalName)
        if (payChannelName == '威富通支付') {
          result.push('兴业银行(威富通)')
        } else {
          result.push(payChannelName)
        }
        return result.join('-')
      } else if (row.channelType === 2) {
        // 集体报名
        if (row.receiveAccountType === 0) {
          // 线上
          result.push('集体报名')
          const terminalName = this.orderTerminalTypeList.find((el) => el.code === row.terminalCode)?.desc || ''
          const payChannelName = row.payChannelName
          result.push(terminalName)
          if (payChannelName == '威富通支付') {
            result.push('兴业银行(威富通)')
          } else {
            result.push(payChannelName)
          }
          return result.join('-')
        } else {
          // 线下
          return '集体报名-线下支付-线下转账汇款'
        }
      } else if (row.channelType === 3) {
        // 导入开通
        return '线下报名-导入开通'
      }
      return ''
    }

    /**
     * 是否是线下支付
     */
    get isOffLinePay() {
      return (detail: BatchOrderDetailVo) => {
        // debugger
        const payMode = detail.paymentInfo?.payMode ?? null
        console.log('payMode', payMode)
        return payMode === BatchOrderPayModeEnum.Offline_Pay ? true : false
      }
    }
    /**
     * 补要发票
     */
    async fillToInvoice(item: OrderDetailVo) {
      this.rowData = item
      await this.hasStudentOpenInvoice()
      if (this.invoiceConfig.askForInvoiceYearType === 1) {
        // 当年度
        // 先拼接索取发票截止时间然后转成时间戳比大小
        const year = item.createTime.split(' ')[0].split('-')[0]
        const invoiceYearAndMouthAndDay =
          year +
          '-' +
          this.invoiceConfig.askForInvoiceDeadline?.split('/')[0] +
          '-' +
          this.invoiceConfig.askForInvoiceDeadline?.split('/')[1] +
          ' ' +
          '23:59:59'
        const time = new Date().getTime()
        if (new Date(invoiceYearAndMouthAndDay).getTime() < time) {
          this.$confirm('当前补要发票已过有效期，无法申请发票。', ' ', {
            confirmButtonText: '我知道了',
            showCancelButton: false,
            showClose: false,
            center: true
          })
            .then(() => {
              //
            })
            .catch(() => {
              //
            })
          return
        }
      } else {
        // 下一年度
        const year = parseInt(item.createTime.split(' ')[0].split('-')[0]) + 1
        const invoiceYearAndMouthAndDay =
          year +
          '-' +
          this.invoiceConfig.askForInvoiceDeadline?.split('/')[0] +
          '-' +
          this.invoiceConfig.askForInvoiceDeadline?.split('/')[1] +
          ' ' +
          '23:59:59'
        const time = new Date().getTime()
        if (new Date(invoiceYearAndMouthAndDay).getTime() < time) {
          this.$confirm('当前补要发票已过有效期，无法申请发票。', ' ', {
            confirmButtonText: '我知道了',
            showCancelButton: false,
            showClose: false,
            center: true
          })
            .then(() => {
              //
            })
            .catch(() => {
              //
            })
          return
        }
      }
      this.listOrderNo = item.orderNo
      this.reapplyInvoiceRef.resetForm()
      this.reapplyInvoiceRef.getPickUpInfo()
      this.reapplyInvoiceRef.reapplyInvoiceForm.invoiceTitle = item.buyerInfo.userName
      this.uiConfig.dialog.reApplyInvoiceVisible = true
    }

    /**
     * 发起退款
     */
    refundOrder(order: OrderDetailVo) {
      if (order.channelType !== 2) {
        localStorage.setItem('jumpToSelectedId', 'purchaseList')
        const targetUrl = `/training/trade/order/personal/detail/${order.orderNo}`
        UtilClass.openUrl(targetUrl)
      } else {
        localStorage.setItem('jumpToSelectedId', 'purchaseList')
        const targetUrl = `/training/trade/order/collective/detail/${order.batchOrderNo}?type=purchase-list&&subOrderNo=${order.orderNo}`
        UtilClass.openUrl(targetUrl)
      }
    }

    /**
     * 订单详情
     */
    orderDetail(row: OrderDetailVo) {
      // 适配集体报名订单
      let targetUrl
      if (row.channelType === 2) {
        targetUrl = `/training/trade/order/collective/sub-detail/${row.batchOrderNo}/${row.orderNo}`
      } else {
        targetUrl = `/training/trade/order/personal/detail/${row.orderNo}`
      }
      UtilClass.openUrl(targetUrl)
    }
  }
</script>
