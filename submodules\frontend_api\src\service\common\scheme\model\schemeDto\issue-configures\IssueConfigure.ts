import TeachPlanLearning from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/TeachPlanLearning'
import IssueConfigureAssessSetting from '@api/service/common/scheme/model/schemeDto/issue-configures/assess-setting/IssueConfigureAssessSetting'
import IssueTrainingConfigConfigure from '@api/service/common/scheme/model/schemeDto/issue-configures/training-config-configure/IssueTrainingConfigConfigure'
import IssueTeachResourceConfigConfigure from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-resource-config-configure/IssueTeachResourceConfigConfigure'
import IssueAttendanceConfigConfigure from '@api/service/common/scheme/model/schemeDto/issue-configures/attendance-config-configure/IssueAttendanceConfigConfigure'
import IssueQuestionnaireLearning from '@api/service/common/scheme/model/schemeDto/issue-configures/questionnaire-learning/IssueQuestionnaireLearning'
import IssueConfigureExtendProperty from '@api/service/common/scheme/model/schemeDto/issue-configures/extend-properties/IssueConfigureExtendProperty'

/**
 * @description 期别配置
 */
class IssueConfigure {
  /**
   * 期别id
   */
  id: string
  /**
   * 学习方案id
   */
  learningSchemeId: string
  /**
   * 期别培训时段类型
   * @example external 来源外部，比如:课表
   * @example custom 自定义
   */
  trainingPeriodType: string
  /**
   * 期别编号
   */
  issueNum: string
  /**
   * 期别名称
   */
  issueName: string
  /**
   * 报到开始时间
   */
  startReportTime: string
  /**
   * 报到结束时间
   */
  endReportTime: string
  /**
   * 报名开始时间
   */
  startSignUpTime: string
  /**
   * 报名结束时间
   */
  endSignUpTime: string
  /**
   * 学习开始时间
   */
  startTrainingTime: string
  /**
   * 学习结束时间
   */
  endTrainingTime: string
  /**
   * 开放报名人数
   */
  allowSignUpNum: number
  /**
   * 已报名人数显示类型
   */
  signUpNumRevealType: number
  /**
   * 固定值显示-报名人数
   */
  fixedSignUpRevealNum: number
  /**
   * 是否展示在门户
   */
  portalDisplay: boolean
  /**
   * 展示用户范围，1-用户，2-集体报名管理员
   */
  portalDisplayScope: number[]
  /**
   * 是否开放报名
   */
  openSignUp: boolean
  /**
   * 培训须知
   */
  trainingNotice: string
  /**
   * 问卷配置
   */
  questionnaireLearning: IssueQuestionnaireLearning[]
  /**
   * 教学计划项学习方式
   */
  teachPlanLearning: TeachPlanLearning
  /**
   * 考核配置
   */
  assessSetting: IssueConfigureAssessSetting[]
  /**
   * 培训配置
   */
  trainingConfigConfigure: IssueTrainingConfigConfigure
  /**
   * 考勤配置
   */
  attendanceConfigConfigure: IssueAttendanceConfigConfigure
  /**
   * 教学资源配置
   */
  teachResourceConfigConfigure: IssueTeachResourceConfigConfigure
  /**
   * 操作类型
   */
  operation: number
  /**
   * 拓展属性
   */
  extendProperties: IssueConfigureExtendProperty[]
}

export default IssueConfigure
