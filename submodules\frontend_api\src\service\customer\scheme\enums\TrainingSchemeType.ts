import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * @description 培训方案类型枚举
 * CHOOSE_COURSE_LEARNING - 选课规则
 * AUTONOMOUS_COURSE_LEARNING - 自主选课
 */
export enum TrainingSchemeTypeEnum {
  CHOOSE_COURSE_LEARNING = 'chooseCourseLearning',
  AUTONOMOUS_COURSE_LEARNING = 'autonomousCourseLearning'
}

/**
 * @description 培训方案类型
 */
class TrainingSchemeType extends AbstractEnum<TrainingSchemeTypeEnum> {
  static enum = TrainingSchemeTypeEnum
  constructor(status?: TrainingSchemeTypeEnum) {
    super()
    this.current = status
    this.map.set(TrainingSchemeTypeEnum.CHOOSE_COURSE_LEARNING, '选课规则')
    this.map.set(TrainingSchemeTypeEnum.AUTONOMOUS_COURSE_LEARNING, '自主选课')
  }
}

export default TrainingSchemeType
