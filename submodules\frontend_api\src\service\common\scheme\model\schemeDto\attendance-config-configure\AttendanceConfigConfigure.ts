import AttendanceSignInConfig from '@api/service/common/scheme/model/schemeDto/attendance-config-configure/attendance-sign-in-config/AttendanceSignInConfig'
import AttendanceSignOutConfig from '@api/service/common/scheme/model/schemeDto/attendance-config-configure/attendance-sign-out-config/AttendanceSignOutConfig'

/**
 * @description 考勤配置
 */
class AttendanceConfigConfigure {
  /**
   * 配置id
   */
  id: string
  /**
   * 学习方案id
   */
  learningSchemeId: string
  /**
   * 考勤签到配置
   */
  attendanceSignInConfig: AttendanceSignInConfig
  /**
   * 考勤签退配置
   */
  attendanceSignOutConfig: AttendanceSignOutConfig
}

export default AttendanceConfigConfigure
