/*
 * @Description: 资讯列表中的对象模型   --- web
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-25 10:29:41
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 14:45:37
 */

import {
  CompleteNewsByPublishResponse,
  SimpleNewsByPublishResponse,
  SimpleNewsByPublishCommonResponse,
  SimpleNewsByPublishResponseWithReviewCount
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

class WebNews {
  /**
   * 资讯ID
   */
  id: string
  /**
   * 资讯标题
   */
  title: string
  /**
   * 资讯摘要
   */
  abstract: string
  /**
   * 发布时间
   */
  date: string
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 内容
   */
  content?: string
  /**
   * 浏览数量
   */
  reviewCount: number

  /**
   * 专题Id
   */
  specialId = ''

  /**
   * 分类id
   */
  necId = ''

  static from(simpleNewsByPublishResponse: SimpleNewsByPublishCommonResponse | SimpleNewsByPublishResponse) {
    // const { newId, title, isTop, summary, publishUserId, publishTime, reviewCount } = simpleNewsByPublishResponse
    const webNews = new WebNews()
    const { newId, title, isTop, summary, publishUserId, publishTime } = simpleNewsByPublishResponse

    webNews.id = newId
    webNews.title = title
    webNews.abstract = summary
    webNews.date = publishTime
    webNews.publishUserId = publishUserId
    webNews.isTop = isTop
    webNews.reviewCount = 0
    if ('necId' in simpleNewsByPublishResponse) {
      const { necId } = simpleNewsByPublishResponse
      webNews.necId = necId
    }
    return webNews
  }

  static fromWithReviewCount(
    simpleNewsByPublishResponse: SimpleNewsByPublishCommonResponse | SimpleNewsByPublishResponseWithReviewCount
  ) {
    // const { newId, title, isTop, summary, publishUserId, publishTime, reviewCount } = simpleNewsByPublishResponse
    const webNews = new WebNews()
    const { newId, title, isTop, summary, publishUserId, publishTime, reviewCount } = simpleNewsByPublishResponse

    webNews.id = newId
    webNews.title = title
    webNews.abstract = summary
    webNews.date = publishTime
    webNews.publishUserId = publishUserId
    webNews.isTop = isTop
    webNews.reviewCount = 0
    webNews.reviewCount = reviewCount
    if ('necId' in simpleNewsByPublishResponse) {
      const { necId } = simpleNewsByPublishResponse
      webNews.necId = necId
    }
    return webNews
  }

  /**
   * H5独有
   * @param content 内容
   */
  setContent(content: string) {
    this.content = content
  }

  setReviewCount(reviewCount: number) {
    this.reviewCount = reviewCount
  }
}
export default WebNews
