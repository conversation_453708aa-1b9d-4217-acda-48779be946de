"""独立部署的微服务,K8S服务名:ms-course-quiz-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""创建课后测验
		@param request 课后测验
		@return 课后测验id
	"""
	createCourseQuiz(request:CourseQuizCreateRequest):String
	"""删除课后测验
		@param id 课后测验id
	"""
	removeCourseQuiz(id:String!):Void
	"""更新课后测验
		@param request 课后测验
	"""
	updateCourseQuiz(request:CourseQuizUpdateRequest):Void
}
"""创建课后测验请求信息
	@author: zhengp 2021/11/26 16:54
"""
input CourseQuizCreateRequest @type(value:"com.fjhb.ms.course.quiz.v1.kernel.gateway.graphql.query.CourseQuizCreateRequest") {
	"""平台ID"""
	platformId:String!
	"""平台版本ID"""
	platformVersionId:String!
	"""项目ID"""
	projectId:String!
	"""子项目ID"""
	subProjectId:String!
	"""单位ID"""
	unitId:String!
	"""服务商ID"""
	servicerId:String!
	jsonConfig:String
}
"""更新课后测验
	@author: zhengp 2021/11/26 17:02
"""
input CourseQuizUpdateRequest @type(value:"com.fjhb.ms.course.quiz.v1.kernel.gateway.graphql.query.CourseQuizUpdateRequest") {
	"""课后测验id"""
	id:String!
	"""课后测验配置"""
	configJson:String
}

scalar List
