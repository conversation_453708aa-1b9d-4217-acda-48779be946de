import {
  CourseInSchemeV2Response,
  CourseResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
/**
 * 课程详情
 */
export default class CourseDetail {
  /**
   * 课程ID
   */
  courseId = ''
  /**
   *  前端隶属的节点ID
   */
  outlineId: string
  /**
   *  前端隶属的父节点ID
   */
  fatherOutlineId: string
  /**
   *  前端隶属的父节点ID
   */
  fatherOutlineName: string
  /**
   *  前端隶属的节点名称
   */
  outlineName: string
  /**
   *  后端课程隶属的节点ID --- 在提交选课的时候使用
   */
  outlineIdInBackstage: string
  /**
   * 名称
   */
  name = ''
  /**
   * 学时
   */
  period = 0
  /**
   * 综合评分
   */
  score = 5
  /**
   * 教师ID集合
   */
  teacherIds: string[] = []
  /**
   * 教师名称
   */
  teacher: string[] = []
  /**
   * 是否可以试听
   */
  isAllowAudition = true
  /**
   * 报名人数
   */
  chooseCoursePeopleCount: number = null
  /**
   * 课程封面
   */
  courseImg = ''
  /**
   * 是否选中
   */
  isSelect = false
  /**
   * 层级
   */
  level = 0
  /**
   * 是否有课程心得
   */
  hasExperience = false
  /**
   * 大纲下的课程id
   */
  outlineCourseId = ''
  /**
   * 教师样式控制(H5 UI使用)
   */
  isShowMore: boolean = null
  static from(dto: CourseInSchemeV2Response, outlineName: string) {
    const vo = new CourseDetail()
    vo.courseId = dto.course.courseId
    vo.outlineId = dto.courseOfCourseTrainingOutline.outlineId
    vo.outlineName = outlineName
    vo.name = dto.course.courseName
    vo.period = dto.courseOfCourseTrainingOutline.period
    vo.teacherIds = dto.course.teacherIds
    vo.teacher = new Array<string>()
    dto?.course?.teacherNames?.length &&
      dto.course.teacherNames.map((item: string) => {
        if (!vo.teacher.find((it: string) => it === item)) {
          vo.teacher.push(item)
        }
      })
    vo.isAllowAudition = dto.course.auditionStatus === 1
    vo.courseImg = dto.course.iconPath
    const score = dto.course.courseAppraiseInfo?.totalAppraise
      ? CalculatorObj.divide(dto.course.courseAppraiseInfo.totalAppraise, 100, 1)
      : undefined
    vo.score = score ? score : 5
    vo.outlineIdInBackstage = dto.courseOfCourseTrainingOutline.outlineId
    vo.chooseCoursePeopleCount = dto.course.chooseCoursePeopleCount ? dto.course.chooseCoursePeopleCount : 0
    vo.hasExperience = dto.hasLearningExperienceTopic
    vo.outlineCourseId = dto.courseOfCourseTrainingOutlineId
    return vo
  }
}
