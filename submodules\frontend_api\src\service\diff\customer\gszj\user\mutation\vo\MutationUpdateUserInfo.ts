import UpdateStudentRequestVo from '@api/service/diff/customer/gszj/user/mutation/vo/create/UpdateStudentRequestVo'
import MutationUpdateUserInfo from '@api/service/customer/user/mutation/MutationUpdateUserInfo'
import { Response } from '@hbfe/common'
import MsAccountGateway, {
  CreateStudentRequest,
  TokenResponse,
  UpdateStudentRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import PlatformTrainingChannelUser from '@api/platform-gateway/platform-training-channel-user-v1'
import QueryUserInfoDiff from '@api/service/diff/customer/gszj/user/QueryUserInfoDiff'
import Context from '@api/service/common/context/Context'
/**
 * 更新用户信息
 */
class MutationUpdateUserInfoDiff extends MutationUpdateUserInfo {
  updateStudentRequestVo = new UpdateStudentRequestVo()
  get areaCode() {
    return '620300'
  }
  /**
   * 更新用户信息方法
   */
  async doUpdate(): Promise<Response<TokenResponse>> {
    if (QueryUserInfoDiff.getRegionIsDiff) {
      this.updateStudentRequestVo.areaCode = this.areaCode
      return await PlatformTrainingChannelUser.updateStudent(this.updateStudentRequestVo)
    } else {
      const params = Object.assign(new UpdateStudentRequest(), this.updateStudentRequestVo)
      return await MsAccountGateway.updateStudent(params)
    }
  }
}
export default MutationUpdateUserInfoDiff
