// 请求验证码地址
export const applyCaptchaUrl = '/web/captcha/sc/apply'
// 验证验证码地址
// '/web/captcha/sc/validate/{captchaTicket}/{captchaValue}'
export const validateCaptchaUrl = '/web/captcha/sc/validate'
// 登录地址
export const loginUrl = '/web/rest/msaccount/login'
// 不带验证码的登录地址
export const loginWithoutCaptchaUrl = '/web/rest/msaccount/loginwc'
// 发送短信验证码的地址
export const sendShortMessageCodeUrl = '/web/rest/mssms/sendSmsCode'
// 发送短信验证码的地址 不需要图形验证码
export const sendShortMessageCodeWithoutCaptchaUrl = '/web/rest/mssms/sendSmsCodeWc'
// 短信验证码登录
export const shortMessageLoginUrl = '/web/rest/mssms/login'
// 钉钉用户ID登录认证
export const dingDingUserIdLoginUrl = '/web/rest/ms3rd/login/dingtalk/userid'
// 使用第三方平台帐号登录，根据华博认证的token进行登录
export const thirdTokenLoginUrl = '/web/rest/ms3rd/login/'
// 钉钉扫码二维码跳转URL的请求地址
export const applyDdWebQRCodeUrl = '/web/rest/ms3rd/apply'
// 退出登录
// '/web/rest/logout/{ticketGrantTicketId}'
export const logoutUrl = '/web/rest/logout'
// 退出闽政通
export const logoutMztUrl = '/web/rest/ms3rd/mzt/logout'
// 认证授权地址
export const authUrl = '/web/oauth/token'
export const authTokenLoginUrl = '/web/rest/msauthtoken/login'
// /web/captcha/sms/validate/{captchaTicket}/{captchaValue}
export const validateShortMessageCaptchaUrl = '/web/captcha/sms/validate'
// 发送短信验证码的地址 没有验证是否存在
export const sendShortMessageCodeUrlApply = '/web/captcha/sms/apply'
// 基于微服务的登录——帐号+密码
export const loginMsByAccountUrl = '/web/rest/ms2/account/login'
// 基于微服务的登录——帐号+密码
export const loginH5MsByAccountUrl = '/web/rest/ms2/accountid/login'
// * 手机验证码登录URL
export const loginByPhoneUrl = '/web/rest/ms2/sms/login'
// 移动端（小程序等）OpenId登录认证
export const weiXinUnionIdLoginUrl = '/web/rest/msopenid/login'
