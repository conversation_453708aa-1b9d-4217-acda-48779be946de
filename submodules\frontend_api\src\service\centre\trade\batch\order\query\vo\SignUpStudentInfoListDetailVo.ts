import {
  OrderResponse,
  SchemeResourceResponse,
  SubOrderResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import DataResolve from '@api/service/common/utils/DataResolve'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import BatchOrderUtils from '@api/service/centre/trade/batch/order/query/util/BatchOrderUtils'
import RefundInfoVo from '@api/service/centre/trade/batch/order/query/vo/RefundInfoVo'
import ExchangeInfoVo from '@api/service/centre/train-class/query/vo/ExchangeInfoVo'
import { LodgingTypeEnum } from '@api/service/common/implement/enums/LodgingTypeEnum'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * @description
 */
class SignUpStudentInfoListDetailVo {
  /**
   * 【主单】订单id
   */
  orderNo = ''

  /**
   * 【子单】订单id集合
   */
  subOrderNoList: string[] = []

  /**
   * 学员id
   */
  studentId = ''

  /**
   * 学员姓名
   */
  studentName = ''

  /**
   * 身份证号
   */
  studentAccount = ''

  /**
   * 身份证类型
   */
  idCardType = ''

  /**
   * 手机号码
   */
  studentPhone = ''

  /**
   * 培训方案名称
   */
  schemeName: string[] = []

  /**
   * 是否换班
   */
  isExchange = false

  /**
   * 学时
   */
  period = 0
  /**
   * 技术等级
   */
  technicalGrade = ''
  /**
   * 年度
   */
  year = ''
  /**
   * 地区 - 省
   */
  province = ''
  /**
   * 地区 - 市
   */
  city = ''
  /**
   * 地区 - 区县
   */
  county = ''
  /**
   * 价格
   */
  price = 0

  /**
   * 退款信息
   */
  refundInfo: RefundInfoVo = new RefundInfoVo()

  /**
   * 换货信息
   */
  exchangeInfo: ExchangeInfoVo = new ExchangeInfoVo()

  /**
   * 备注（报名失败原因）
   */
  remark = ''

  /**
   * 期别名称
   */
  issueName = ''

  /**
   * 是否需要住宿
   */
  needAccommodation = false

  /**
   * 住宿方式
   */
  accommodationType: LodgingTypeEnum = undefined

  /**
   * 培训形式
   */
  trainingMode: TrainingModeEnum = undefined

  static async from(response: OrderResponse, batchOrderNo: string): Promise<SignUpStudentInfoListDetailVo> {
    const detail = new SignUpStudentInfoListDetailVo()
    detail.orderNo = response.orderNo ?? ''
    detail.studentId = response.buyer?.userId ?? ''
    if (DataResolve.isWeightyArr(response.subOrderItems)) {
      const subOrderItems = response.subOrderItems
      detail.subOrderNoList = [...new Set(subOrderItems.map((item) => item.subOrderNo ?? '') ?? ([] as string[]))]
      //   detail.technicalGrade = subOrderItems[0].deliveryCommoditySku.skuProperty.technicalGrade.skuPropertyValueName
      detail.year = subOrderItems[0].deliveryCommoditySku.skuProperty.year.skuPropertyValueName
      detail.province = subOrderItems[0].deliveryCommoditySku.skuProperty.province.skuPropertyValueName
      detail.city = subOrderItems[0].deliveryCommoditySku.skuProperty.city.skuPropertyValueName
      detail.county = subOrderItems[0].deliveryCommoditySku.skuProperty.county.skuPropertyValueName
      detail.trainingMode = subOrderItems[0].deliveryCommoditySku.skuProperty.trainingWay
        .skuPropertyValueId as TrainingModeEnum
      detail.schemeName = [
        ...new Set(subOrderItems.map((item) => item.deliveryCommoditySku?.saleTitle ?? '') ?? ([] as string[]))
      ]
      detail.issueName = subOrderItems[0]?.deliveryCommoditySku?.issueInfo?.issueName
      detail.needAccommodation = subOrderItems[0]?.accommodation?.IsAccommodation
      detail.accommodationType = subOrderItems[0]?.accommodation?.accommodationType
      detail.isExchange = SignUpStudentInfoListDetailVo.validateIsExchange(subOrderItems)
      detail.period = SignUpStudentInfoListDetailVo.getPeriod(subOrderItems)
      detail.refundInfo = await BatchOrderUtils.getSubOrderRefundInfo(detail.subOrderNoList, batchOrderNo)
      detail.exchangeInfo = await BatchOrderUtils.getSubOrderExchangeInfo(detail.subOrderNoList, batchOrderNo)
    }
    detail.price = response.basicData?.amount ?? 0

    return detail
  }

  /**
   * 获取学时
   */
  static getPeriod(subOrderItems: SubOrderResponse[]): number {
    const total =
      subOrderItems.reduce((prev, cur) => {
        return CalculatorObj.add((cur.currentCommoditySku?.resource as SchemeResourceResponse)?.period ?? 0, prev)
      }, 0) ?? 0
    return total
  }

  /**
   * 校验是否换班
   */
  static validateIsExchange(subOrderItems: SubOrderResponse[]): boolean {
    let result = false
    const target = subOrderItems.find((item) => item.exchangeStatus !== 0)
    if (target) {
      result = true
    }
    return result
  }
}

export default SignUpStudentInfoListDetailVo
