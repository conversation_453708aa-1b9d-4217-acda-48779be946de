import checkTrainingPointName from './queries/checkTrainingPointName.graphql'
import hasSignRecord from './queries/hasSignRecord.graphql'
import listCourseCardTemplate from './queries/listCourseCardTemplate.graphql'
import printCourseCardTemplate from './queries/printCourseCardTemplate.graphql'
import studentPrintCourseCard from './queries/studentPrintCourseCard.graphql'
import tes from './queries/tes.graphql'
import applySign from './mutates/applySign.graphql'
import changeTrainingPointStatus from './mutates/changeTrainingPointStatus.graphql'
import checkPlanItem from './mutates/checkPlanItem.graphql'
import createPlanItemRepair from './mutates/createPlanItemRepair.graphql'
import createTrainingPoint from './mutates/createTrainingPoint.graphql'
import customSignIn from './mutates/customSignIn.graphql'
import deleteTrainingPoints from './mutates/deleteTrainingPoints.graphql'
import recalculatePeriodByPlanItemIdAndStudentNo from './mutates/recalculatePeriodByPlanItemIdAndStudentNo.graphql'
import removeSignRecordAndStudyResultByPlanItemId from './mutates/removeSignRecordAndStudyResultByPlanItemId.graphql'
import studentsSign from './mutates/studentsSign.graphql'
import updateTrainingPoint from './mutates/updateTrainingPoint.graphql'

export {
  checkTrainingPointName,
  hasSignRecord,
  listCourseCardTemplate,
  printCourseCardTemplate,
  studentPrintCourseCard,
  tes,
  applySign,
  changeTrainingPointStatus,
  checkPlanItem,
  createPlanItemRepair,
  createTrainingPoint,
  customSignIn,
  deleteTrainingPoints,
  recalculatePeriodByPlanItemIdAndStudentNo,
  removeSignRecordAndStudyResultByPlanItemId,
  studentsSign,
  updateTrainingPoint
}
