<template>
  <div>
    <el-drawer
      title="选择角色"
      :visible.sync="showRoleDialog"
      size="800px"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      :before-close="closeChooseRole"
      custom-class="m-drawer"
    >
      <div class="drawer-bd">
        <el-table stripe :data="roleInfoList" v-loading="loading" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column prop="name" label="角色" width="280"></el-table-column>
          <el-table-column prop="description" label="角色说明" align="center"></el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <el-checkbox
                @change="selectChange(scope.$index)"
                :checked="scope.row.isChecked"
                label="选择"
              ></el-checkbox>
            </template>
            <!--            <template slot-scope="scope">-->
            <!--              <el-button-->
            <!--                size="mini"-->
            <!--                type="text"-->
            <!--                v-if="scope.row.isChecked"-->
            <!--                @click="scope.row.isChecked = !scope.row.isChecked"-->
            <!--              >-->
            <!--                取消选择-->
            <!--              </el-button>-->
            <!--              <el-button-->
            <!--                size="mini"-->
            <!--                type="text"-->
            <!--                v-if="!scope.row.isChecked"-->
            <!--                @click="scope.row.isChecked = !scope.row.isChecked"-->
            <!--              >-->
            <!--                选择-->
            <!--              </el-button>-->
            <!--            </template>-->
          </el-table-column>
        </el-table>
        <div class="demo-drawer__footer" style="margin-top: 20px; text-align: center">
          <el-button @click="closeChooseRole">取 消</el-button>
          <el-button type="primary" @click="confirmChooseRole">确 定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'
  import { QueryRoleList } from '@api/service/management/authority/role/query/QueryRoleList'
  import { ApplicationTypeEnum } from '@api/service/management/authority/role/query/ApplicationTypeEnum'

  import { cloneDeep } from 'lodash'

  class RoleInfoResponse extends RoleInfoResponseVo {
    isChecked = false
  }
  @Component
  export default class extends Vue {
    loading = false
    roleInfoList: Array<RoleInfoResponse> = new Array<RoleInfoResponse>()
    getQueryRoleList = AuthorityModule.roleFactory.getQueryRoleList()
    /**
     * 是否展示
     */
    @Prop({
      required: true,
      type: Boolean,
      default: false
    })
    value: boolean
    /**
     *
     */
    @Prop({
      type: Array
    })
    roleList: Array<RoleInfoResponseVo>
    @Watch('value', {
      immediate: true,
      deep: true
    })
    valueChange(val: boolean) {
      this.showRoleDialog = cloneDeep(val)
    }
    @Emit('input')
    @Watch('showRoleDialog', {
      immediate: true,
      deep: true
    })
    async showRoleDialogChange(val: boolean) {
      if (this.showRoleDialog){
        this.roleInfoList.map((item) => (item.isChecked = false))
        await this.doQueryPage()
      }
      return val

    }
    showRoleDialog = false
    constructor() {
      super()
    }

    async doQueryPage() {
      try {

        this.loading = true
        const roleInfoList = await QueryRoleList.queryRoleListByArgs([ApplicationTypeEnum.school])
        this.roleInfoList = new Array<RoleInfoResponse>()
        roleInfoList?.forEach((el: RoleInfoResponseVo) => {
          const option = new RoleInfoResponse()
          Object.assign(option, el)
          // //修改需要默认选中
          if (this.roleList && this.roleList.length > 0) {
            this.roleList.forEach((el: RoleInfoResponseVo) => {
              if (el.id == option.id) {
                option.isChecked = true
              }
            })
          }
          this.$nextTick(() => {
            this.roleInfoList.push(option)
          })
        })
      } catch (e) {
        this.$message.error('请求失败')
        console.log(e)
      } finally {
        this.loading = false
      }
    }
    closeChooseRole() {
      this.showRoleDialog = false
    }
    confirmChooseRole() {
      this.showRoleDialog = false
      const result = this.roleInfoList.filter((item) => {
        return item.isChecked == true
      })
      this.$emit('confirmDialog', result)
    }

    selectChange(index: number) {
      this.roleInfoList[index].isChecked = !this.roleInfoList[index].isChecked
    }
  }
</script>

<style scoped></style>
