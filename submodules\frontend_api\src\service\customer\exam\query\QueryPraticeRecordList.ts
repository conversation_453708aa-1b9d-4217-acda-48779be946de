import { ResponseStatus, Response } from '@hbfe/common'
import MsExamQueryFrontGatewayCourseLearningForeStage, {
  AnswerPaperSort,
  Page,
  SortTypeEnum
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryForeStage'
import { ExaminationAnswerPaperResponseVo } from '@api/service/customer/exam/query/vo/ExaminationAnswerPaperResponseVo'
import { PracticeAnswerPaperResponseVo } from '@api/service/customer/exam/query/vo/PracticeAnswerPaperResponseVo'

/**
 * 练习记录列表
 */
class QueryPraticeRecordList {
  // region properties

  /**
   *总数目，类型为number
   */
  totalSize = 0
  // /**
  //  *sku过滤条件，类型为SkuPropertyVo
  //  */
  // filterSkuVo = new SkuPropertyVo()
  /**
   * 考试记录列表，类型为TrainClassCommodityVo[]
   */
  praticeRecordList: PracticeAnswerPaperResponseVo[] = []
  // endregion
  // region methods

  /**
   * 获取练习记录
   */
  async queryExamRecordList(page: Page, qualificationId: string): Promise<Array<PracticeAnswerPaperResponseVo>> {
    // const filter = new CommoditySkuRequest()
    // filter.skuPropertyRequest = this.filterSkuVo.convertToDto()
    const res = await MsExamQueryFrontGatewayCourseLearningForeStage.pageMyPracticeRecordInMyself({
      page: {
        pageSize: page.pageSize,
        pageNo: page.pageNo
      },
      qualificationId: qualificationId,
      answerPaperStatus: 2,
      answerPaperSort: AnswerPaperSort.CREATE_TIME,
      sort: SortTypeEnum.DESC
    })
    if (res.status.isSuccess()) {
      this.praticeRecordList = res.data.currentPageData as PracticeAnswerPaperResponseVo[]
      this.totalSize = res.data.totalSize
    }
    //pageCommoditySkuCustomerPurchaseInServicer
    return this.praticeRecordList
  }

  // endregion
}
export default QueryPraticeRecordList
