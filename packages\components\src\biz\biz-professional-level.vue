<route-meta>
{
"title": "职称选择器"
}
</route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    filterable
    clearable
  >
    <el-option v-for="item in placeChannelOptions" :label="item.name" :value="item.id" :key="item.id"></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import LeaderPositionLevelVo from '@api/service/common/basic-data-dictionary/query/vo/LeaderPositionLevelVo'
  import QueryLeaderPositionLevel from '@api/service/common/basic-data-dictionary/query/QueryLeaderPositionLevel'
  @Component
  export default class extends Vue {
    selected = ''
    placeChannelOptions: Array<LeaderPositionLevelVo> = new Array<LeaderPositionLevelVo>()
    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Prop({
      type: String,
      default: '请选择职称'
    })
    placeholder: string
    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }
    async created() {
      const res = await QueryLeaderPositionLevel.queryLeaderPositionLevel()
      if (res.isSuccess()) {
        this.placeChannelOptions = QueryLeaderPositionLevel.leaderPositionLevelList
      }
    }
  }
</script>
