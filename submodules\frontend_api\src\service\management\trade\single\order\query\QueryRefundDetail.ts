import MsTradeQueryFrontGatewayCourseLearningBacktage, {
  Page,
  ReturnOrderRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import ReturnOrderResponseVo from '@api/service/management/trade/single/order/query/vo/ReturnOrderResponseVo'
import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import { ResponseStatus } from '@hbfe/common'
export default class QueryRefundList {
  //退款单号
  returnOrderNo = ''

  //退款单详情对象
  returnOrderDetail = new ReturnOrderResponseVo()
  /**
   * 获取退款单详情
   *
   */
  async queryRefundOrderDetail(): Promise<ResponseStatus> {
    if (!this.returnOrderNo) {
      return new ResponseStatus(8006, '退款单号不能为空')
    }
    const res = await MsTradeQueryFrontGatewayCourseLearningBacktage.getReturnOrderInServicer(this.returnOrderNo)
    // const
    if (res.status.isSuccess()) {
      const returnVo = new ReturnOrderResponseVo()
      Object.assign(returnVo, res.data)
      returnVo.changeStatus()
      await returnVo.fillRecords()
      await returnVo.addBuyer()
      // const newData = res.data as ReturnOrderResponseVo
      //  if ()
      this.returnOrderDetail = returnVo
    }
    return res.status
  }

  /**
   * 获取退款单详情 （分销）
   *
   */
  async queryFxRefundOrderDetail(): Promise<ResponseStatus> {
    if (!this.returnOrderNo) {
      return new ResponseStatus(8006, '退款单号不能为空')
    }
    const res = await MsTradeQueryFrontGatewayCourseLearningBacktage.getReturnOrderInDistributor(this.returnOrderNo)
    // const
    if (res.status.isSuccess()) {
      const returnVo = new ReturnOrderResponseVo()
      Object.assign(returnVo, res.data)
      returnVo.changeStatus()
      await returnVo.fillRecords()
      await returnVo.addBuyer()
      // const newData = res.data as ReturnOrderResponseVo
      //  if ()
      this.returnOrderDetail = returnVo
    }
    return res.status
  }
}
