import QueryDistributionWay from '@api/service/management/online-school-config/distribution-channels-config/query/QueryDistributionWay'
import QueryExpress from '@api/service/management/online-school-config/distribution-channels-config/query/QueryExpress'
import QueryTakePlace from '@api/service/management/online-school-config/distribution-channels-config/query/QueryTakePlace'

/**
 * @description 查询配送渠道配置工厂
 */
class QueryDistributionChannelsConfigFactory {
  /**
   * @description
   */
  get queryDistributionWay() {
    return new QueryDistributionWay()
  }

  get queryExpress() {
    return new QueryExpress()
  }

  get queryTakePlace() {
    return new QueryTakePlace()
  }
}
export default new QueryDistributionChannelsConfigFactory()
