<template>
  <el-drawer :visible.sync="previewCourse" title="预览课程" size="90%" :destroy-on-close="true" @close="close">
    <template v-if="isOpen">
      <iframe v-if="url" width="100%" height="100%" :src="url" frameborder="0"></iframe>
    </template>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Emit, Prop, Vue } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    previewCourse = true
    @Prop({
      required: true,
      type: String,
      default: ''
    })
    previewCourseUrl: string

    isOpen = false

    get url() {
      return this.previewCourseUrl
    }

    @Emit('close')
    close() {
      // no
      this.isOpen = false
    }

    mounted() {
      this.isOpen = true
    }
  }
</script>
