<route-meta>
{
"title": "培训类别选择器"
}
</route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    filterable
    clearable
  >
    <el-option
      v-for="item in trainingCategoryOptions"
      :label="showLabel(item)"
      :value="item.propertyId"
      :key="item.propertyId"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import QueryTrainingCategory from '@api/service/common/basic-data-dictionary/query/QueryTrainingCategory'

  @Component
  export default class extends Vue {
    selected = ''
    // 培训类别选项
    trainingCategoryOptions: Array<TrainingCategoryVo> = new Array<TrainingCategoryVo>()

    @Prop({
      type: String,
      default: '请选培训类别'
    })
    placeholder: string

    //行业属性分类id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    // 行业id
    @Prop({
      type: String,
      default: ''
    })
    industryId: string

    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      this.$emit('updateTrainingCategory', this.selected)
      return this.selected
    }

    @Watch('industryPropertyId', {
      immediate: true,
      deep: true
    })
    async industryPropertyIdChange() {
      await this.getTrainingCategoryOptions()
    }

    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: TrainingCategoryVo) => {
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    /**
     * 获取培训类别
     */
    async getTrainingCategoryOptions() {
      const res = await QueryTrainingCategory.queryTrainingCategory(this.industryPropertyId, this.industryId)
      this.trainingCategoryOptions = res.isSuccess()
        ? QueryTrainingCategory.trainingCategoryList
        : ([] as TrainingCategoryVo[])
    }
  }
</script>
