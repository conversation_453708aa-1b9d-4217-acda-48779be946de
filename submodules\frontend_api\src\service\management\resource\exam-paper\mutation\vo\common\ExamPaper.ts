import MsExamQueryBackStageGateway, {
  AutomaticPublishPattern,
  LibraryQuestionScopeSetting,
  LibraryRequest,
  LibraryResponse,
  PaperPublishConfigureResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { PublishPatternTypes } from '../../../enum/ExamPaperPublishPatternTypes'
import { Response, ResponseStatus, UiPage } from '@hbfe/common'
import AutomaticExamPaperVo from './AutomaticExamPaperVo'
import UpdateExamPaperVo from '../update/UpdateExamPaperVo'
import ExamLibrary from './ExamLibrary'
import LibraryQuestionCountResponseVo from '../update/vo/LibraryQuestionCountResponseVo'
import LibraryQuestionCountRequestVo from '../update/vo/LibraryQuestionCountRequestVo'
/**
 * 【智能卷】
 * 试卷复制与修改基类
 */

class ExamPaper {
  examPaperParams = new UpdateExamPaperVo(AutomaticExamPaperVo)

  // 创建人id集合
  private createUserIdList = new Array<string>()
  // 题库选择集合
  private libraryList = new Array<ExamLibrary>()

  /**
   * @description: 初始化参数
   */
  constructor(id: string) {
    this.examPaperParams.id = id
  }

  /**
   * @description: 获取试卷信息
   */
  async doQueryExamPaper(): Promise<ResponseStatus> {
    const { status, data } = await this.requestExamPaperByType(this.examPaperParams.publishPattern.type)
    if (status?.isSuccess()) {
      // 填充题库相关数据
      const list = await this.getQuestionBankLibrary(data)
      this.examPaperParams.from(data, list)
    }
    return status
  }

  /**
   * @description: 获取题库相关数据
   * @param {PaperPublishConfigureResponse}
   */
  private async getQuestionBankLibrary(item: PaperPublishConfigureResponse) {
    const pattern = item.publishPattern as AutomaticPublishPattern
    const list = pattern.questionExtractRule?.questionScopes as Array<LibraryQuestionScopeSetting>
    if (list?.length) {
      const libraryIds = list[0]?.libraryIds
      const res = await this.queryQuestionBankLibrary(libraryIds)
      if (!res?.status?.isSuccess()) {
        console.log('获取题库相关数据失败！')
        this.libraryList = []
      } else {
        this.libraryList = res?.data
      }
    }
    return this.libraryList
  }

  /**
   * @description: 查询题库分页
   * @param {idList} 题库id集合
   */
  async queryQuestionBankLibrary(idList: Array<string>): Promise<Response<Array<ExamLibrary>>> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = idList?.length
    const params = new LibraryRequest()
    params.libraryIdList = [...new Set(idList)]
    const result = await MsExamQueryBackStageGateway.pageLibraryInServicer({
      page: page,
      request: params
    })
    const response = new Response<Array<ExamLibrary>>()
    if (!result?.status?.isSuccess()) {
      response.status = result.status
      return response
    }

    // 题库列表
    const resultList = new Array<ExamLibrary>()
    result.data?.currentPageData?.forEach((item: LibraryResponse) => {
      const libraryItem = new ExamLibrary()
      libraryItem.name = item.libraryName
      libraryItem.id = item.libraryId
      this.createUserIdList.push(item.createUserId)
      resultList.push(libraryItem)
    })
    response.data = resultList
    // 查询已启用的试题数量
    const queryQuestionCountResponse = await this.queryCountQuestionInServicer(idList)
    if (!queryQuestionCountResponse?.status?.isSuccess()) {
      response.status = queryQuestionCountResponse.status
      return response
    }
    resultList?.forEach((item: ExamLibrary) => {
      queryQuestionCountResponse?.data?.forEach((el: LibraryQuestionCountResponseVo) => {
        if (item.id === el.libraryId) {
          item.enabledQuestions = el.questionCount
        }
      })
    })

    response.data = resultList
    response.status = result.status
    return response
  }

  /**
   * @description: 根据题库id集合查询已启用的试题数量
   * @param {Array} idList 题库id集合
   */
  private async queryCountQuestionInServicer(
    idList: Array<string>
  ): Promise<Response<Array<LibraryQuestionCountResponseVo>>> {
    const params = new LibraryQuestionCountRequestVo()
    params.libraryIdList = idList
    params.enable = true
    const res = await MsExamQueryBackStageGateway.getQuestionCountInServicer(params)
    return res
  }

  /**
   * @description: 根据出卷模式调用对应试卷详情请求
   */
  private requestExamPaperByType(type: PublishPatternTypes) {
    const queryMethods = {
      [PublishPatternTypes.AutomaticPublishPattern]: async () => {
        return await this.queryAutomaticExamPaperDetail()
      }
    }
    if (!queryMethods[type]) {
      console.error('试卷模式不存在！')
      throw new Error('试卷模式不存在！')
    }
    return queryMethods[type]()
  }

  /**
   * @description: 获取智能出卷的试卷详情
   */
  private async queryAutomaticExamPaperDetail(): Promise<Response<PaperPublishConfigureResponse>> {
    const res = await MsExamQueryBackStageGateway.getPaperPublishConfigureInServicer(this.examPaperParams.id)
    return res
  }
}

export default ExamPaper
