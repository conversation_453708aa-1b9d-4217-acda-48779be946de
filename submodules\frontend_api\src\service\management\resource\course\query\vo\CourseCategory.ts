import { CourseCategoryResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class CourseCategory {
  id: string
  name: string
  parentId: string
  sort: number

  static from(courseCategoryResponse: CourseCategoryResponse) {
    const category = new CourseCategory()
    category.id = courseCategoryResponse.id
    category.name = courseCategoryResponse.name
    category.parentId = courseCategoryResponse.parentId
    category.sort = courseCategoryResponse.sort
    return category
  }
}

export default CourseCategory
