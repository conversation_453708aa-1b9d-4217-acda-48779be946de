import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-data-export-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-data-export-front-gateway-DataExportBackstage-ldhtqdpt'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum OrderSortField {
  ORDER_NORMAL_TIME = 'ORDER_NORMAL_TIME',
  ORDER_COMPLETED_TIME = 'ORDER_COMPLETED_TIME'
}
export enum ReturnOrderSortField {
  APPLIED_TIME = 'APPLIED_TIME'
}

// 类

/**
 * 范围查询条件
<AUTHOR>
@version 1.0
@date 2022/5/7 15:34
 */
export class DateScopeExcelRequest {
  /**
   * result >&#x3D; begin
   */
  begin?: string
  /**
   * result <&#x3D; end
   */
  end?: string
}

/**
 * 合同签订记录Request
 */
export class ElectronicLaborContractRecordRequest {
  /**
   * 合同信息
   */
  contractInfo?: ContractInfoRequest
  /**
   * 甲方信息
   */
  partyA?: PartyAInfoRequest
  /**
   * 乙方信息
   */
  partyB?: PartyBInfoRequest
  /**
   * 来源信息
   */
  sourceInfo?: SourceInfoRequest
  /**
   * 合同id
   */
  contractId?: Array<string>
  /**
   * 签订信息
   */
  contractSigningInfo?: ContractSigningInfoRequest
  /**
   * 合同服务状态
@see ContractServiceStatus
1.待履约
2.履约中
3.已终止
   */
  contractServiceStatus?: Array<number>
  jobName?: string
}

/**
 * 合同信息Request
 */
export class ContractInfoRequest {
  /**
   * 合同名称
   */
  contractName?: string
  /**
   * 第三方合同唯一标识
   */
  contractThirdId?: string
  /**
   * 合同发起时间Start
   */
  contractSignStartTimeStart?: string
  /**
   * 合同发起时间End
   */
  contractSignStartTimeEnd?: string
  /**
   * 合同完成时间Start
   */
  contractSignEndTimeStart?: string
  /**
   * 合同完成时间End
   */
  contractSignEndTimeEnd?: string
  /**
   * 合同生效时间Start
   */
  contractServerStartTimeStart?: string
  /**
   * 合同生效时间End
   */
  contractServerStartTimeEnd?: string
  /**
   * 合同截止时间Start
   */
  contractServerEndTimeStart?: string
  /**
   * 合同截止时间End
   */
  contractServerEndTimeEnd?: string
  /**
   * 合同服务提供商id
   */
  contractServerId?: string
  /**
   * 合同类型
@see ContractTypes
0.固定期限劳动合同
1.无固定期限劳动合同
2.以完成一定任务为期限劳动合同
   */
  contractType?: Array<number>
  /**
   * 距离服务到期时间 单位/天
   */
  rangeServerEndDay?: number
}

/**
 * 合同签订信息Request
 */
export class ContractSigningInfoRequest {
  /**
   * 签订状态
@see SignedStatus
1.新订
2.续订
   */
  signedStatus?: number
  /**
   * 是否续签
   */
  whetherToRenew?: boolean
}

/**
 * 甲方信息Request
 */
export class PartyAInfoRequest {
  /**
   * 单位id
   */
  unitId?: string
  /**
   * 发起人id
   */
  initiatorId?: string
  /**
   * 地区路径
   */
  regionPath?: Array<string>
  /**
   * 签署人姓名
   */
  signatoryName?: string
}

/**
 * 乙方用户信息Request
 */
export class PartyBInfoRequest {
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
}

/**
 * 来源信息Request
 */
export class SourceInfoRequest {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 商品id
   */
  productId?: string
}

export class EpelcpContractProductRequest {
  /**
   * 商品id
   */
  commoditySkuId?: Array<string>
  /**
   * 套餐类型
single单份  combo 套餐
   */
  comboType?: string
  /**
   * 交易成功开始时间
   */
  tradeBeginTime?: string
  /**
   * 交易结束时间
   */
  tradeEndTime?: string
  /**
   * 企业id
   */
  enterpriseIds?: Array<string>
  /**
   * 企业地区
   */
  region?: Array<string>
  /**
   * 服务提供商id
   */
  contractServerIds?: Array<string>
}

/**
 * 线下发票查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceExportParm {
  /**
   * 发票ID集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基本信息
   */
  basicData?: OfflineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfo?: InvoiceAssociationInfoRequest
  /**
   * 发票配送信息
   */
  invoiceDeliveryInfo?: OfflineInvoiceDeliveryInfoRequest
  /**
   * 归属信息
   */
  owner?: OwnerKParam
  jobName?: string
}

/**
 * 配送地址信息
<AUTHOR>
@date 2022/05/07
 */
export class DeliveryAddressRequest {
  /**
   * 收件人
   */
  consignee?: string
}

/**
 * 配送状态变更时间查询参数
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryStatusChangeTimeRequest {
  /**
   * 未就绪
   */
  unReady?: DateScopeExcelRequest
  /**
   * 已就绪
   */
  ready?: DateScopeExcelRequest
  /**
   * 已配送
   */
  shipped?: DateScopeExcelRequest
  /**
   * 已自取
   */
  taken?: DateScopeExcelRequest
}

/**
 * 快递信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class ExpressRequest {
  /**
   * 快递单号
   */
  expressNo?: string
}

/**
 * 发票开票状态变更时间记录查询参数
<AUTHOR>
@date 2022/04/06
 */
export class InvoiceBillStatusChangTimeRequest {
  /**
   * 发票申请开票时间
   */
  unBillDateScope?: DateScopeExcelRequest
  /**
   * 发票开票时间
   */
  successDateScope?: DateScopeExcelRequest
}

/**
 * 线下发票基本信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceBasicDataRequest {
  /**
   * 发票类型
1:电子发票 2:纸质发票
@see InvoiceTypes
   */
  invoiceTypeList?: Array<number>
  /**
   * 发票种类
1:普通发票 2:增值税普通发票 3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategory?: Array<number>
  /**
   * 发票状态
1:正常
2:作废
@see InvoiceStatus
   */
  invoiceStatus?: number
  /**
   * 发票状态变更时间记录
   */
  invoiceStatusChangeTime?: InvoiceStatusChangeTimeRequest
  /**
   * 发票开票状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  billStatusList?: Array<number>
  /**
   * 发票开票状态变更时间记录
   */
  billStatusChangTime?: InvoiceBillStatusChangTimeRequest
  /**
   * 发票是否冻结
   */
  freeze?: boolean
  /**
   * 发票号集合
   */
  invoiceNoList?: Array<string>
  /**
   * 商品id集合
   */
  commoditySkuIdList?: Array<string>
}

/**
 * 线下发票配送信息
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceDeliveryInfoRequest {
  /**
   * 配送状态
0:未就绪 1：已就绪 2：已自取 3：已配送
@see OfflineDeliveryStatus
   */
  deliveryStatusList?: Array<number>
  /**
   * 配送状态变更时间记录
0:未就绪 1：已就绪 2：已自取 3：已配送
key值 {@link OfflineDeliveryStatus}
   */
  deliveryStatusChangeTime?: DeliveryStatusChangeTimeRequest
  /**
   * 配送方式
0:无 1：自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethodList?: Array<number>
  /**
   * 快递信息
   */
  express?: ExpressRequest
  /**
   * 自取信息
   */
  takeResult?: TakeResultRequest
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddressRequest
}

/**
 * 取件信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class TakeResultRequest {
  /**
   * 领取人
   */
  takePerson?: string
}

/**
 * 线下发票查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceExportRequest {
  /**
   * 发票ID集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基本信息
   */
  basicData?: OfflineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfo?: InvoiceAssociationInfoRequest
  /**
   * 发票配送信息
   */
  invoiceDeliveryInfo?: OfflineInvoiceDeliveryInfoRequest
  jobName?: string
}

/**
 * 商品查询条件
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuRequest {
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品Sku名称
   */
  saleTitle?: string
  /**
   * 商品sku属性查询
   */
  skuProperty?: SkuPropertyRequest
}

/**
 * 商品sku属性查询参数
<AUTHOR>
 */
export class SkuPropertyRequest {
  /**
   * 套餐类型
<p> single：单份 combo：套餐
   */
  comboType?: Array<string>
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class OrderRequest {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 子订单退货状态
0: 未退货
1: 退货申请中
2: 退货中
3: 退货成功
4: 退款中
5: 退款成功
@see SubOrderReturnStatus
   */
  subOrderReturnStatus?: Array<number>
  /**
   * 订单基本信息查询参数
   */
  orderBasicData?: OrderBasicDataRequest
  /**
   * 订单支付信息查询
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 单位买家单位ID
   */
  unitBuyerUnitIdList?: Array<string>
  /**
   * 发货商品信息
   */
  deliveryCommodity?: CommoditySkuRequest
  /**
   * 订单服务商信息
   */
  orderServicers?: OrderServicersRequest
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: OrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 订单基本信息查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderBasicDataRequest {
  /**
   * 订单类型
1:常规订单 2:批次关联订单
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType?: number
  /**
   * 批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 订单状态
<br> 1:正常 2：交易完成 3：交易关闭
@see OrderStatus
   */
  orderStatusList?: Array<number>
  /**
   * 订单支付状态
<br> 0:未支付 1：支付中 2：已支付
@see com.fjhb.domain.trade.api.order.consts.OrderPaymentStatus
   */
  orderPaymentStatusList?: Array<number>
  /**
   * 订单发货状态
<br> 0:未发货 1：发货中 2：已发货
@see com.fjhb.domain.trade.api.order.consts.OrderDeliveryStatus
   */
  orderDeliveryStatusList?: Array<number>
  /**
   * 订单状态变更时间
   */
  orderStatusChangeTime?: OrderStatusChangeTimeRequest
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
  /**
   * 订单价格范围
<br> 查询非0元订单 begin填0.01
   */
  orderAmountScope?: BigDecimalScopeRequest
}

/**
 * 订单支付信息相关查询参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderPayInfoRequest {
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 付款类型
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
   */
  paymentOrderTypeList?: Array<number>
}

/**
 * 订单服务商查询参数
<AUTHOR>
 */
export class OrderServicersRequest {
  /**
   * 合同签订服务商ID
   */
  contractProviderIdList?: Array<string>
}

/**
 * 订单状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderStatusChangeTimeRequest {
  /**
   * 订单处于正常状态时间范围(创建时间范围)
   */
  normalDateScope?: DateScopeRequest
  /**
   * 订单创建时间范围
   */
  completedDatesScope?: DateScopeRequest
}

/**
 * 退货单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderRequest {
  /**
   * 退货单号
   */
  returnOrderNoList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: ReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: ReturnOrderApprovalInfoRequest
  /**
   * 退货商品id集合
   */
  returnCommoditySkuIdList?: Array<string>
  /**
   * 退货商品sku属性
   */
  skuProperty?: SkuPropertyRequest
  /**
   * 退款商品id集合
   */
  refundCommoditySkuIdList?: Array<string>
  /**
   * 退货单关联子订单查询参数
   */
  subOrderInfo?: SubOrderInfoRequest
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class ReturnSortRequest {
  /**
   * 需要排序的字段
   */
  field?: ReturnOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 退货单关联子订单的主订单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class OrderInfoRequest {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 关联批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 单位买家单位ID集合
   */
  unitBuyerUnitIdList?: Array<string>
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 原始订单交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
}

/**
 * 退货单关闭信息
<AUTHOR>
@date 2022年4月11日 11:33:35
 */
export class ReturnCloseReasonRequest {
  /**
   * 退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
@see ReturnOrderCloseTypes
   */
  closeTypeList?: Array<number>
}

/**
 * 退货单审批信息查询参数
<AUTHOR>
@date 2022/03/18
 */
export class ReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime?: DateScopeRequest
}

/**
 * 退货单基本信息查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderBasicDataRequest {
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
   */
  returnOrderStatus?: Array<number>
  /**
   * 退货单申请来源类型
SUB_ORDER
BATCH_RETURN_ORDER
@see ReturnOrderApplySourceTypes
   */
  applySourceType?: string
  /**
   * 来源ID集合
   */
  applySourceIdList?: Array<string>
  /**
   * 退货单关闭信息
   */
  returnCloseReason?: ReturnCloseReasonRequest
  /**
   * 退货单状态变更时间
   */
  returnStatusChangeTime?: ReturnOrderStatusChangeTimeRequest
  /**
   * 退款金额范围
<br> 查询非0元  begin填0.01
   */
  refundAmountScope?: BigDecimalScopeRequest
}

/**
 * 退货单状态变更时间查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied?: DateScopeRequest
  /**
   * 退货单完成时间
<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted?: DateScopeRequest
}

/**
 * <AUTHOR>
@date 2022/03/24
 */
export class SubOrderInfoRequest {
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 订单查询参数
   */
  orderInfo?: OrderInfoRequest
}

/**
 * 商品开通统计报表查询参数
<AUTHOR>
@date 2022/05/11
 */
export class TradeReportRequest {
  /**
   * 交易时间范围
   */
  tradeTime?: DateScopeRequest
  /**
   * 买家所在地区路径
   */
  buyerAreaPath?: Array<string>
  /**
   * 商品查询条件
   */
  commoditySku?: CommoditySkuRequest1
}

/**
 * 商品查询参数
<AUTHOR>
@date 2022/05/11
 */
export class CommoditySkuRequest1 {
  /**
   * 商品Sku名称
   */
  saleTitle?: string
  /**
   * 商品sku属性查询
   */
  skuProperty?: SkuPropertyRequest
}

/**
 * 发票关联订单查询参数
<AUTHOR>
@date 2022/3/18
 */
export class InvoiceAssociationInfoRequest {
  /**
   * 关联订单类型
0:订单号
1:批次单号
@see AssociationTypes
   */
  associationType?: number
  /**
   * 订单号 | 批次单号
   */
  associationIdList?: Array<string>
  /**
   * 买家信息
   */
  buyerIdList?: Array<string>
  /**
   * 企业id
   */
  unitBuyerUnitIdList?: Array<string>
  /**
   * 收款账号
   */
  receiveAccountIdList?: Array<string>
}

/**
 * 发票状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class InvoiceStatusChangeTimeRequest {
  /**
   * 正常
   */
  normal?: DateScopeExcelRequest
  /**
   * 作废
   */
  invalid?: DateScopeExcelRequest
}

export class BigDecimalScopeRequest {
  begin?: number
  end?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class OwnerKParam {
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  rootAccountId?: string
  servicerType?: number
  servicerId?: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * @description 合同物品使用情况——超管导出
   * <AUTHOR>
   * @date 11:54 2022/7/19
   * @param request
   * @return {@link }
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async adminEexportEpelcpContractProductStatistic(
    request: EpelcpContractProductRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.adminEexportEpelcpContractProductStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出企业下合同签订附件-合同文件
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportContractFileInEnterprise(
    request: ElectronicLaborContractRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportContractFileInEnterprise,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出企业下合同签订记录列表
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportContractRecordInEnterprise(
    request: ElectronicLaborContractRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportContractRecordInEnterprise,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出服务商下合同签订记录列表
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportContractRecordInServer(
    request: ElectronicLaborContractRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportContractRecordInServer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出子项目下合同签订记录列表
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportContractRecordInSubProject(
    request: ElectronicLaborContractRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportContractRecordInSubProject,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 发票导出
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOfflineInvoiceInSubProject(
    request: OfflineInvoiceExportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOfflineInvoiceInSubProject,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 发票导出——服务商导出
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOfflineInvoiceInSubProjectByService(
    request: OfflineInvoiceExportParm,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOfflineInvoiceInSubProjectByService,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人订单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOrderExcelInServicer(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOrderExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人订单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOrderExcelInSubProject(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOrderExcelInSubProject,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReconciliationExcelInServicer(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReconciliationExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReconciliationExcelInSubProject(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReconciliationExcelInSubProject,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnOrderExcelInServicer(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReturnOrderExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnOrderExcelInSubProject(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReturnOrderExcelInSubProject,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人报名退货对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnReconciliationExcelInServicer(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReturnReconciliationExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人报名退货对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnReconciliationExcelInSubProject(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReturnReconciliationExcelInSubProject,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出服务商收益统计报表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportServicerProfitReportExcelInSubProject(
    request: TradeReportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportServicerProfitReportExcelInSubProject,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * @description 合同物品使用情况——服务商导出
   * <AUTHOR>
   * @date 11:54 2022/7/19
   * @param request
   * @return {@link }
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async servicEexportEpelcpContractProductStatistic(
    request: EpelcpContractProductRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.servicEexportEpelcpContractProductStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
