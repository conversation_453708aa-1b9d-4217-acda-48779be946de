"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取组合报表统计合计数据"""
	getCombinationCommodityReportSummaryInServicer(request:QZTGTradeReportRequest):ReportSummaryResponse
	"""分销商- 获取退货单详情"""
	getReturnOrderInDistributor(returnOrderNo:String):ReturnOrderResponse
	"""获取退货单详情
		@param returnOrderNo : 退货单号
		@return 退货单信息
	"""
	getReturnOrderInServicer(returnOrderNo:String):ReturnOrderResponse
	"""分页获取商品开通统计列表（含所有商品）"""
	pageCombinationOpenReportFormsInServicer(page:Page,request:QZTGTradeReportRequest):CombinationCommodityOpenReportFormResponsePage @page(for:"CombinationCommodityOpenReportFormResponse")
	"""分销商-  退货单分页查询"""
	pageReturnOrderInDistributor(page:Page,request:ReturnOrderRequestInDistributor,sort:[ReturnSortRequest]):ReturnOrderResponsePage @page(for:"ReturnOrderResponse")
	"""退货单分页查询"""
	pageReturnOrderInServicer(page:Page,request:ReturnOrderRequest,sort:[ReturnSortRequest]):ReturnOrderResponsePage @page(for:"ReturnOrderResponse")
	"""专题管理员 - 退货单分页查询"""
	pageReturnOrderInTrainingChannel(page:Page,request:ReturnOrderRequest,sort:[ReturnSortRequest]):ReturnOrderResponsePage @page(for:"ReturnOrderResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input BigDecimalScopeRequest @type(value:"com.fjhb.ms.query.commons.BigDecimalScopeRequest") {
	begin:BigDecimal
	end:BigDecimal
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input CommodityAuthInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommodityAuthInfoRequest") {
	distributorId:String
	distributionLevel:Int
	superiorDistributorId:String
	supplierId:String
	salesmanId:String
}
input CommoditySkuRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommoditySkuRequest") {
	commoditySkuIdList:[String]
	saleTitle:String
	issueInfo:IssueInfo1
	skuProperty:SkuPropertyRequest
	externalTrainingPlatform:[String]
	trainingInstitution:[String]
}
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertySearchRequest") {
	regionSearchType:Int
	region:[RegionSkuPropertyRequest]
}
input SkuPropertyRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.SkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	trainingObject:[String]
	positionCategory:[String]
	jobLevel:[String]
	jobCategory:[String]
	grade:[String]
	subject:[String]
	learningPhase:[String]
	discipline:[String]
	trainingChannelIds:[String]
	certificatesType:[String]
	practitionerCategory:[String]
	qualificationCategory:[String]
	trainingForm:[String]
}
input IssueInfo1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.IssueInfo") {
	issueId:String
	issueName:String
	issueNum:String
	trainStartTime:DateTime
	trainEndTime:DateTime
	sourceType:String
	sourceId:String
}
input ReturnOrderRequestInDistributor @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.ReturnOrderRequestInDistributor") {
	returnOrderNoList:[String]
	basicData:ReturnOrderBasicDataRequest1
	approvalInfo:ReturnOrderApprovalInfoRequest1
	returnCommoditySkuIdList:[String]
	refundCommoditySkuIdList:[String]
	subOrderInfo:SubOrderInfoRequest1
	commodityAuthInfo:CommodityAuthInfoRequest
	portalId:String
	isDistributionExcludePortal:Boolean
	returnCommodity:CommoditySkuRequest
	refundCommodity:CommoditySkuRequest
}
input ReturnSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.ReturnSortRequest") {
	field:ReturnOrderSortField
	policy:SortPolicy
}
input OrderInfoRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.OrderInfoRequest") {
	orderNoList:[String]
	batchOrderNoList:[String]
	buyerIdList:[String]
	receiveAccountIdList:[String]
	flowNoList:[String]
	channelTypesList:[Int]
	terminalCodeList:[String]
	saleChannel:Int
	saleChannels:[Int]
	saleChannelName:String
	saleChannelIds:[String]
	policyTrainingSchemeIdList:[String]
	declarationUnitCodeList:[String]
}
input ReturnCloseReasonRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnCloseReasonRequest") {
	closeTypeList:[Int]
}
input ReturnOrderApprovalInfoRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderApprovalInfoRequest") {
	approveTime:DateScopeRequest
}
input ReturnOrderBasicDataRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderBasicDataRequest") {
	returnOrderStatus:[Int]
	returnOrderTypes:[Int]
	applySourceType:String
	applySourceIdList:[String]
	returnCloseReason:ReturnCloseReasonRequest1
	returnStatusChangeTime:ReturnOrderStatusChangeTimeRequest1
	refundAmountScope:BigDecimalScopeRequest
}
input ReturnOrderStatusChangeTimeRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderStatusChangeTimeRequest") {
	applied:DateScopeRequest
	returnCompleted:DateScopeRequest
}
input SubOrderInfoRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.SubOrderInfoRequest") {
	subOrderNoList:[String]
	orderInfo:OrderInfoRequest1
	discountType:Int
	useDiscount:Boolean
}
"""商品查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input QZTGCommoditySkuRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.QZTGCommoditySkuRequest") {
	"""商品Sku名称"""
	saleTitle:String
	"""商品sku属性查询"""
	skuProperty:SkuPropertyRequest
	"""学习方案查询参数"""
	scheme:QZTGSchemeRequest
}
"""方案查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input QZTGSchemeRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.QZTGSchemeRequest") {
	"""培训方案ID"""
	schemeIdList:[String]
	"""方案类型
		@see SchemeType
	"""
	schemeType:String
	"""方案学时"""
	schemePeriodScope:DoubleScopeRequest
}
"""商品开通统计报表查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input QZTGTradeReportRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.QZTGTradeReportRequest") {
	"""交易时间范围"""
	tradeTime:DateScopeRequest
	"""买家所在地区路径"""
	buyerAreaPath:[String]
	"""商品查询条件"""
	commoditySku:QZTGCommoditySkuRequest
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	"""销售渠道
		0-自营 1-分销 2专题 3-华医网 不传则查全部
	"""
	saleChannels:[Int]
	"""排除的销售渠道
		0-自营 1-分销 2-专题 3-华医网
	"""
	excludedSaleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
	"""分销商id"""
	distributorId:String
	"""门户id"""
	portalId:String
	"""查看非推广门户数据 | true 为勾选效果"""
	notDistributionPortal:Boolean
	"""收款账户"""
	receiveAccountIdList:[String]
	"""期别id"""
	issueId:[String]
	"""机构ID集合"""
	institutionIdList:[String]
}
"""退货单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.ReturnOrderRequest") {
	"""退货单号"""
	returnOrderNoList:[String]
	"""基本信息"""
	basicData:ReturnOrderBasicDataRequest
	"""审批信息"""
	approvalInfo:ReturnOrderApprovalInfoRequest
	"""退货商品id集合"""
	returnCommoditySkuIdList:[String]
	"""退款商品id集合"""
	refundCommoditySkuIdList:[String]
	"""退货单关联子订单查询参数"""
	subOrderInfo:SubOrderInfoRequest
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""范围查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/5/7 15:34
"""
input DateScopeExcelRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.nests.DateScopeExcelRequest") {
	"""result >= begin"""
	begin:DateTime
	"""result <= end"""
	end:DateTime
}
"""退货单关联子订单的主订单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input OrderInfoRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.nests.OrderInfoRequest") {
	"""订单号集合"""
	orderNoList:[String]
	"""关联批次单号"""
	batchOrderNoList:[String]
	"""买家id集合"""
	buyerIdList:[String]
	"""收款账号ID"""
	receiveAccountIdList:[String]
	"""原始订单交易流水号"""
	flowNoList:[String]
	"""购买渠道
		<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelTypesList:[Int]
	"""终端
		<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
		@see PurchaseChannelTerminalCodes
	"""
	terminalCodeList:[String]
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题id"""
	saleChannelIds:[String]
	"""专题名称"""
	saleChannelName:String
}
"""退货单关闭信息
	<AUTHOR>
	@date 2022年4月11日 11:33:35
"""
input ReturnCloseReasonRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.nests.ReturnCloseReasonRequest") {
	"""退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
		@see ReturnOrderCloseTypes
	"""
	closeTypeList:[Int]
}
"""退货单审批信息查询参数
	<AUTHOR>
	@date 2022/03/18
"""
input ReturnOrderApprovalInfoRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.nests.ReturnOrderApprovalInfoRequest") {
	"""审批时间"""
	approveTime:DateScopeExcelRequest
}
"""退货单基本信息查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderBasicDataRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.nests.ReturnOrderBasicDataRequest") {
	"""退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)"""
	returnOrderStatus:[Int]
	"""退货单类型
		1-仅退货
		2-仅退款
		3-退货并退款
		4-部分退货
		5-部分退款
		6-部分退货并部分退款
		7-部分退货并全额退款
		8-全部退货并部分退款
	"""
	returnOrderTypes:[Int]
	"""退货单申请来源类型
		SUB_ORDER
		BATCH_RETURN_ORDER
		@see ReturnOrderApplySourceTypes
	"""
	applySourceType:String
	"""来源ID集合"""
	applySourceIdList:[String]
	"""退货单关闭信息"""
	returnCloseReason:ReturnCloseReasonRequest
	"""退货单状态变更时间"""
	returnStatusChangeTime:ReturnOrderStatusChangeTimeRequest
	"""退款金额范围
		<br> 查询非0元  begin填0.01
	"""
	refundAmountScope:BigDecimalScopeExcelRequest
}
"""退货单状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.nests.ReturnOrderStatusChangeTimeRequest") {
	"""申请退货时间"""
	applied:DateScopeExcelRequest
	"""退货单完成时间
		<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
	"""
	returnCompleted:DateScopeExcelRequest
}
"""<AUTHOR>
	@date 2022/03/24
"""
input SubOrderInfoRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.request.nests.SubOrderInfoRequest") {
	"""子订单号集合"""
	subOrderNoList:[String]
	"""订单查询参数"""
	orderInfo:OrderInfoRequest
}
"""@Description 范围查询条件
	<AUTHOR>
	@Date 8:51 2022/5/23
"""
input BigDecimalScopeExcelRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.service.executor.param.common.request.BigDecimalScopeExcelRequest") {
	"""result >= begin"""
	begin:BigDecimal
	"""result <= end"""
	end:BigDecimal
}
type DiscountPolicyModel @type(value:"com.fjhb.ms.trade.query.common.model.DiscountPolicyModel") {
	discountPolicyId:String
	discountId:Int
}
type RegionModel @type(value:"com.fjhb.ms.trade.query.common.model.RegionModel") {
	regionId:String
	province:String
	city:String
	county:String
	path:String
}
type UserModel @type(value:"com.fjhb.ms.trade.query.common.model.UserModel") {
	userId:String
}
type BatchReturnOrderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.BatchReturnOrderResponse") {
	batchReturnOrderNo:String
	basicData:BatchReturnOrderBasicDataResponse
	batchOrderInfo:BatchOrderInfoResponse
	needManualApprove:Boolean
	approvalInfo:BatchReturnApprovalInfoResponse
	confirmUser:UserResponse
	refundInfo:RefundInfoResponse
}
type BatchOrderInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchOrderInfoResponse") {
	batchOrderNo:String
	paymentInfo:PaymentInfoResponse
	creator:UserResponse
}
type BatchReturnApprovalInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnApprovalInfoResponse") {
	approveStatus:Int
	approveResult:Int
	approveUser:UserResponse
	approveComment:String
	approveTime:DateTime
}
type BatchReturnOrderApplyInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnOrderApplyInfoResponse") {
	applyUser:UserResponse
	reasonId:String
	reasonContent:String
	description:String
}
type BatchReturnOrderBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnOrderBasicDataResponse") {
	batchReturnOrderType:Int
	refundAmount:BigDecimal
	returnOrderCount:Long
	batchReturnOrderStatus:Int
	batchReturnOrderStatusChangeTime:BatchReturnOrderStatusChangeTimeResponse
	applyInfo:BatchReturnOrderApplyInfoResponse
	closeReason:BatchReturnOrderCloseReasonResponse
	saleChannel:Int
	saleChannelId:String
	saleChannelName:String
	salePathList:[SalePathResponse]
}
type BatchReturnOrderCloseReasonResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnOrderCloseReasonResponse") {
	closeType:Int
	cancelUser:UserResponse
	cancelReason:String
	confirmFailureMessage:String
}
type BatchReturnOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnOrderStatusChangeTimeResponse") {
	created:DateTime
	confirmed:DateTime
	cancelApplying:DateTime
	returning:DateTime
	returnFailed:DateTime
	refundApplying:DateTime
	refundApplied:DateTime
	refunding:DateTime
	refundFailed:DateTime
	returned:DateTime
	refunded:DateTime
	returnedAndRefunded:DateTime
	returnCompleted:DateTime
	closed:DateTime
}
interface ResourceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.ResourceResponse") {
	resourceType:String
}
type SchemeResourceResponse implements ResourceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.SchemeResourceResponse") {
	schemeId:String
	schemeName:String
	period:BigDecimal
	schemeType:String
	resourceType:String
}
enum SortPolicy @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.SortPolicy") {
	ASC
	DESC
}
type CommodityAuthInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.CommodityAuthInfoResponse") {
	commodityAuthId:String
	distributorId:String
	distributorName:String
	distributionLevel:Int
	superiorDistributorId:String
	superiorDistributorName:String
	distributorIdPath:String
	supplierId:String
	supplierName:String
	salesmanId:String
	salesmanName:String
	distributorPhone:String
	distributorUnitCreditCode:String
	distributorPartnerType:Int
	supplierPartnerType:Int
}
type CommoditySkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.CommoditySkuPropertyResponse") {
	year:SkuPropertyResponse
	province:SkuPropertyResponse
	city:SkuPropertyResponse
	county:SkuPropertyResponse
	industry:SkuPropertyResponse
	subjectType:SkuPropertyResponse
	trainingCategory:SkuPropertyResponse
	trainingProfessional:SkuPropertyResponse
	technicalGrade:SkuPropertyResponse
	trainingObject:SkuPropertyResponse
	positionCategory:SkuPropertyResponse
	jobLevel:SkuPropertyResponse
	jobCategory:SkuPropertyResponse
	grade:SkuPropertyResponse
	subject:SkuPropertyResponse
	learningPhase:SkuPropertyResponse
	discipline:SkuPropertyResponse
	certificatesType:SkuPropertyResponse
	practitionerCategory:SkuPropertyResponse
	qualificationCategory:SkuPropertyResponse
	trainingWay:SkuPropertyResponse
}
type CommoditySkuResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.CommoditySkuResponse") {
	commoditySkuId:String
	saleTitle:String
	commodityPicturePath:String
	price:BigDecimal
	originalPrice:BigDecimal
	enableSpecialPrice:Boolean
	showPrice:Boolean
	skuProperty:CommoditySkuPropertyResponse
	resource:ResourceResponse
	tppTypeId:String
	externalTrainingPlatform:String
	trainingInstitution:String
	issueInfo:IssueInfo
}
type DiscountSchemeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.DiscountSchemeResponse") {
	specialPrice:BigDecimal
	discountPolicyList:[DiscountPolicyModel]
	discountType:Int
	hasEnabled:Boolean
}
type IssueInfo @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.IssueInfo") {
	issueId:String
	issueName:String
	issueNum:String
	trainStartTime:DateTime
	trainEndTime:DateTime
	sourceType:String
	sourceId:String
}
type OwnerInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.OwnerInfoResponse") {
	servicerId:String
	servicerName:String
}
type PaymentInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.PaymentInfoResponse") {
	payAmount:BigDecimal
	flowNo:String
	receiveAccountId:String
	paymentOrderType:Int
}
type PricingPolicyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.PricingPolicyResponse") {
	pricingPolicyId:String
	price:BigDecimal
	hasEnabled:Boolean
}
type RefundInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.RefundInfoResponse") {
	refundOrderNo:String
	refundOrderType:Int
	refundOrderStatus:Int
	refundOrderStatusChangeTime:RefundOrderStatusChangeTimeResponse
	refundFlow:String
	refundAmount:BigDecimal
	refundFailReason:String
	refundConfirmedTime:DateTime
}
type SalePathResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.SalePathResponse") {
	id:String
	fullPath:String
	currentPath:String
	currentPathLastCode:String
	currentPathLastType:Int
	isLast:Boolean
}
type UserResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.UserResponse") {
	userId:String
	userArea:RegionModel
	managementUnitRegionCode:RegionModel
	jobCategoryId:String
	professionalLevel:Int!
	jobCategoryName:String
}
type SkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.nested.SkuPropertyResponse") {
	skuPropertyValueId:String
	skuPropertyValueName:String
	skuPropertyValueShowName:String
}
enum ReturnOrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderSortField") {
	APPLIED_TIME
}
type OrderInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.OrderInfoResponse") {
	orderNo:String
	orderType:Int
	batchOrderNo:String
	channelType:Int
	terminalCode:String
	orderPaymentInfo:PaymentInfoResponse
	buyer:UserResponse
	creator:UserResponse
	saleChannel:Int
	saleChannelId:String
	saleChannelName:String
	policyTrainingSchemeIds:String
	declarationUnitCode:String
}
type RefundCommodityResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.RefundCommodityResponse") {
	quantity:BigDecimal
	commoditySku:CommoditySkuResponse
}
type RefundOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.RefundOrderStatusChangeTimeResponse") {
	waiting:DateTime
	refunding:DateTime
	refunded:DateTime
	failed:DateTime
}
type ReturnApprovalInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnApprovalInfoResponse") {
	approveStatus:Int
	approveResult:Int
	approveUser:UserResponse
	approveComment:String
	approveTime:DateTime
	cancelApproveTime:DateTime
}
type ReturnCloseReasonResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnCloseReasonResponse") {
	closeType:Int
	cancelUser:UserModel
	cancelReason:String
}
type ReturnCommodityResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnCommodityResponse") {
	quantity:BigDecimal
	commoditySku:CommoditySkuResponse
}
type ReturnOrderApplyInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnOrderApplyInfoResponse") {
	applyUser:UserResponse
	reasonId:String
	reasonContent:String
	description:String
}
type ReturnOrderBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnOrderBasicDataResponse") {
	returnOrderType:Int
	refundAmount:BigDecimal
	returnOrderStatus:Int
	returnOrderStatusChangeTime:ReturnOrderStatusChangeTimeResponse
	applyInfo:ReturnOrderApplyInfoResponse
	returnFailReason:String
	returnCloseReason:ReturnCloseReasonResponse
	applySourceType:String
	applySourceId:String
	saleChannel:Int
	saleChannelId:String
	saleChannelName:String
}
type ReturnOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnOrderStatusChangeTimeResponse") {
	applied:DateTime
	cancelApplying:DateTime
	returning:DateTime
	returnFailed:DateTime
	refundApplying:DateTime
	refundApplied:DateTime
	refunding:DateTime
	refundFailed:DateTime
	returned:DateTime
	refunded:DateTime
	returnedAndRefunded:DateTime
	returnCompleted:DateTime
	closed:DateTime
}
type SubOrderInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.SubOrderInfoResponse") {
	subOrderNo:String
	exchanged:Boolean
	orderInfo:OrderInfoResponse
	quantity:BigDecimal
	discountSourceId:String
	discountType:Int
	useDiscount:Boolean
	saleChannel:Int
	discountScheme:DiscountSchemeResponse
	pricingPolicy:PricingPolicyResponse
	salePathList:[SalePathResponse]
	isExchangeIssue:Boolean
	amount:BigDecimal
	finalPrice:BigDecimal
}
type ReportSummaryResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.response.ReportSummaryResponse") {
	totalNetAmount:BigDecimal
	tradeCountSummaryInfo:SubOrderStatisticDto
	purchaseChannelStatisticInfoList:[PurchaseChannelStatisticDto]
}
type PaymentTypeStatisticDto @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.response.nested.PaymentTypeStatisticDto") {
	paymentType:Int
	statisticInfo:SubOrderStatisticDto
}
type PurchaseChannelStatisticDto @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.response.nested.PurchaseChannelStatisticDto") {
	purchaseChannel:Int
	paymentTypeStatisticInfoList:[PaymentTypeStatisticDto]
}
type SubOrderStatisticDto @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.response.nested.SubOrderStatisticDto") {
	tradeSuccessCount:Long
	returnCount:Long
	exchangeInCount:Long
	exchangeOutCount:Long
	netTradeSuccessCount:Long
}
type PaymentTypeStatisticDto1 @type(value:"com.fjhb.ms.trade.queryv2.kernel.service.tradereport.dto.nested.PaymentTypeStatisticDto") {
	paymentType:Int
	statisticInfo:SubOrderStatisticDto1
}
type PurchaseChannelStatisticDto1 @type(value:"com.fjhb.ms.trade.queryv2.kernel.service.tradereport.dto.nested.PurchaseChannelStatisticDto") {
	purchaseChannel:Int
	paymentTypeStatisticInfoList:[PaymentTypeStatisticDto1]
}
type SubOrderStatisticDto1 @type(value:"com.fjhb.ms.trade.queryv2.kernel.service.tradereport.dto.nested.SubOrderStatisticDto") {
	tradeSuccessCount:Long
	returnCount:Long
	exchangeInCount:Long
	exchangeOutCount:Long
	netTradeSuccessCount:Long
}
"""组合商品开通统计信息
	<AUTHOR>
	@date 2022/05/10
"""
type CombinationCommodityOpenReportFormResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.response.CombinationCommodityOpenReportFormResponse") {
	"""商品ID"""
	commoditySkuId:String
	"""学习方案ID"""
	schemeId:String
	"""学习方案名称"""
	schemeName:String
	"""学习方案学时"""
	period:String
	"""组合开班统计信息"""
	combinationStatisticList:[CombinationStatisticDto]
}
"""退货单网关模型"""
type ReturnOrderResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.response.ReturnOrderResponse") {
	"""退货单号"""
	returnOrderNo:String
	"""退货单基本信息"""
	basicData:ReturnOrderBasicDataResponse
	"""退货单是否需要审批"""
	needApprove:Boolean
	"""退货单审批信息"""
	approvalInfo:ReturnApprovalInfoResponse
	"""退款确认人"""
	confirmUser:UserResponse
	"""退货单关联退款单信息"""
	refundInfo:RefundInfoResponse
	"""退货商品信息"""
	returnCommodity:ReturnCommodityResponse
	"""退款商品信息"""
	refundCommodity:RefundCommodityResponse
	"""退货子订单信息"""
	subOrderInfo:SubOrderInfoResponse
	"""来源批次退货单信息"""
	batchReturnOrder:BatchReturnOrderResponse
	"""退货商品分销信息（仅分销订单的退货单有值）"""
	commodityAuthInfo:CommodityAuthInfoResponse
	"""归属信息"""
	ownerInfo:OwnerInfoResponse
	"""是否需要人工确认退款"""
	needConfirmRefund:Boolean
	"""退货单扩展信息
		key:courseType,华医部分退款
		value:1-专业课   2-公需课  3-都退
	"""
	ext:Map
}
type CombinationStatisticDto @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.service.dto.CombinationStatisticDto") {
	"""类型  0公需  1专业"""
	type:Int
	"""合计数据"""
	summaryInfo:SubOrderStatisticDto1
	"""各渠道统计信息"""
	purchaseChannelStatisticInfoList:[PurchaseChannelStatisticDto1]
}

scalar List
type CombinationCommodityOpenReportFormResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CombinationCommodityOpenReportFormResponse]}
type ReturnOrderResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ReturnOrderResponse]}
