import msOrder from '@api/ms-gateway/ms-order-v1'
import msOrderMarket, {
  ApplyEnterSaleChannelRequest,
  CalculateOrderPriceRequest
} from '@api/ms-gateway/ms-order-marketing-v1'
import { PurchaseChannelTypeEnum } from '@api/service/customer/trade/single/enum/PurchaseChannelTypeEnum'
import { ResponseStatus } from '@hbfe/common'
import { ContextTypeEnums } from '@api/service/common/enums/ContextTypeEnums'
import platformMarketingOrder, {
  ApplyEnterSaleChannelByPortalIdRequest,
  CommodityRequest,
  VerifyMarketingOrderRequest
} from '@api/platform-gateway/platform-jxjy-marketing-order-v1'
import CreateOrderParams from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/CreateOrderParams'
import UserModule from '@api/service/customer/user/UserModule'
import FxCheckOrderCode, { FxCheckOrderCodeEnum } from '@api/service/customer/trade/single/enum/FxCheckOrderCodeEnum'
import QueryUtil, {
  RegionTreeResource
} from '@api/service/customer/trade/single/mutation/customer-order/util/QueryUtil'
import DistributionRegionInfo from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/DistributionRegionInfo'
import VerifyOrderResult from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/VerifyOrderResult'
import ScopeRangeItem from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/ScopeRangeItem'
import VerifyOrderResponse from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/VerifyOrderResponse'

export default class CreateOrder {
  //  创建订单入参
  createOrderParams: CreateOrderParams = new CreateOrderParams()

  /**
   * 下单渠道token (目前只有分销使用)
   */
  channelToken: string = undefined

  /**
   * @description: 创建订单
   * @return {*}
   */
  async doCreateOrder() {
    const request = this.createOrderParams.toGeneralCreateOrderRequest()
    const res = await msOrder.createOrder(request)
    if (res.status.isSuccess() && !res.data.success) {
      res.status.message = res.data.message
    }
    return res
  }

  /**
   * @description: 校验渠道商id是否有效 无效赋值为undefined
   * @param {*}
   * @return {*}
   */

  // private async validateChannel(): Promise<void> {
  //   // 校验渠道商id是否有效
  //   try {
  //     if (this.createOrderParams.channelVendorId) {
  //       const response = await servicerGateway.findRelationServiceListByType({
  //         inputIdList: [this.createOrderParams.channelVendorId],
  //         inPutType: ServicerTypeEnums.CHANNEL_VENDOR,
  //         outPutType: ServicerTypeEnums.TRAINING_INSTITUTION,
  //         contractStatus: ServicerContractStatusEnums.NORMAL
  //       })
  //       const { status, data } = response
  //       const index = data?.findIndex(el => el.id === this.unitId)
  //       const isExist = index !== -1
  //       if (!status.isSuccess() || !data || data?.length === 0 || !isExist) {
  //         console.error('渠道商id无效，已匹配机构列表：', ServicerModule.servicerList)
  //         this.createOrderParams.channelVendorId = undefined
  //       }
  //     }
  //   } catch (error) {
  //     console.error(error)
  //     this.createOrderParams.channelVendorId = undefined
  //   }
  // }

  /**
   * 申请进入分销销售渠道
   * @param fxPortalId 分销门户ID
   */
  async checkEnterSaleChannel(fxPortalId: string) {
    const request = new ApplyEnterSaleChannelByPortalIdRequest()
    request.portalId = fxPortalId
    const res = await platformMarketingOrder.applyEnterSaleChannelByPortalId(request, ContextTypeEnums.fx)
    if (res?.data?.code == '200') {
      this.channelToken = res.data.saleChannelPurchaseToken
      return res.data.saleChannelPurchaseToken
    } else {
      return Promise.reject(new ResponseStatus(500, '获取销售渠道凭证失败'))
    }
  }

  /**
   * 获取销售渠道凭证
   * @param fxPortalId 分销门户ID
   * @param purchaseChannelType 销售渠道id 默认为 PurchaseChannelTypeEnum.PurchaseChannelTypeEnumUserBuy
   */
  async applyEnterSaleChannel(
    fxPortalId: string,
    purchaseChannelType = PurchaseChannelTypeEnum.PurchaseChannelTypeEnumUserBuy
  ) {
    const request = new ApplyEnterSaleChannelRequest()
    request.channelTypeId = fxPortalId
    request.channelType = purchaseChannelType

    const res = await msOrderMarket.applyEnterSaleChannel(request, ContextTypeEnums.fx)

    if (res?.data?.code == '200') {
      this.channelToken = res.data.saleChannelPurchaseToken
      return res.data.saleChannelPurchaseToken
    } else {
      return Promise.reject(new ResponseStatus(500, '获取销售渠道凭证失败'))
    }
  }

  /**
   * 校验当前商品是否能下单
   */
  async verifyFxOrderCanBePlace() {
    const request = new VerifyMarketingOrderRequest()
    request.saleChannelPurchaseToken = this.channelToken
    request.buyerId = UserModule.queryUserFactory.getQueryUserInfo()?.userInfo.userInfo.userId
    request.commodities = this.createOrderParams.commodities.map(item => {
      const commodity = new CommodityRequest()
      commodity.quantity = item.quantity
      commodity.policyId = item.policyId
      commodity.policyType = item.policyType
      commodity.commodityAuthId = item.commodityAuthId
      commodity.commoditySkuId = item.skuId

      return commodity
    })
    const res = await platformMarketingOrder.verifyMarketingOrder(request, ContextTypeEnums.fx)

    if (res?.status?.isSuccess()) {
      const resultArray = new Array<VerifyOrderResult>()
      const pricingRegionResource = new Array<RegionTreeResource>()
      if (!res.data.success && res.data.skuVerifyResponseList && res.data.skuVerifyResponseList.length) {
        res.data.skuVerifyResponseList.map(item => {
          const result = new VerifyOrderResult()
          result.code = Number(item.code) as FxCheckOrderCodeEnum
          item.message = FxCheckOrderCode.map.get(Number(item.code))
          if (item?.pricingPolicySaleScopeRespList?.length) {
            item?.pricingPolicySaleScopeRespList.map(pricingItem => {
              const scopeRangeItem = new ScopeRangeItem()
              scopeRangeItem.scopeId = pricingItem.saleScopeId
              scopeRangeItem.saleUnitList = pricingItem.saleUnitList

              result.scopeRangeList.push(scopeRangeItem)

              const regionResource = new RegionTreeResource()
              regionResource.key = pricingItem.saleScopeId
              regionResource.regionPathList = pricingItem.regionList
              pricingRegionResource.push(regionResource)
            })
          }
          resultArray.push(result)
        })

        if (pricingRegionResource.length) {
          const pricingRegionRes = await QueryUtil.batchQueryRegionTree(pricingRegionResource)

          resultArray.map(item => {
            item.scopeRangeList.map(item => {
              const regionList = pricingRegionRes.get(item.scopeId)

              if (regionList) {
                item.regionList = regionList
              }
            })
          })
        }
      }
      const response = new VerifyOrderResponse()
      response.message = res.data.message
      response.code = res.data.success ? 200 : 201
      response.verifyOrderResult = resultArray.length
        ? resultArray.filter(item => item.code !== 200)
        : new Array<VerifyOrderResult>()
      return response
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 获取订单总价
   */
  async getOrderPrice() {
    const request = new CalculateOrderPriceRequest()
    request.saleChannelPurchaseToken = this.channelToken
    request.commodityRequestList = this.createOrderParams.commodities.map(item => {
      const commidity = new CommodityRequest()
      commidity.commoditySkuId = item.skuId
      commidity.commodityAuthId = item.commodityAuthId
      commidity.policyType = item.policyType
      commidity.policyId = item.policyId
      commidity.quantity = item.quantity
      return commidity
    })
    const res = await msOrderMarket.calculateOrderPrice(request, ContextTypeEnums.fx)

    if (res?.data?.code == '200') {
      return res.data
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 创建分销订单
   */
  async doCreateFxOrder() {
    const request = this.createOrderParams.toFxCreateOrderRequest(this.channelToken)
    const res = await platformMarketingOrder.createMarketingOrder(request, ContextTypeEnums.fx)

    if (res?.status?.isSuccess()) {
      return res?.data
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }
}
