import exportStudentSchemeLearning, {
  StudentSchemeLearningRequest
} from '@api/diff-gateway/yzzj-data-export-gateway-backstage'
import {
  StudentSchemeLearningRequestVo,
  SyncResultEnmu
} from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
import UserModule from '@api/service/management/user/UserModule'
class ExportDockingdata {
  /**
   * 地区管理员导出对接数据
   */
  async exportExcelManageRegion(param: StudentSchemeLearningRequestVo) {
    try {
      console.log('param参数=', param)
      let userIdList: string[] = []
      if (param.name || param.idCard || param.phone) {
        userIdList = await this.getUsers(param)
      }
      if (userIdList && userIdList.length) {
        param.student.userIdList = userIdList
      }
      param.learningRegister.status = [1, 2]
      if (param.syncResult) {
        if (param.syncResult === SyncResultEnmu.Unsynchronized) {
          param.connectManageSystem.syncStatus = 0
        }
        if (param.syncResult === SyncResultEnmu.Synchronized) {
          param.connectManageSystem.syncStatus = 1
        }
        if (param.syncResult === SyncResultEnmu.SynchronizationFailure) {
          param.connectManageSystem.syncStatus = 2
        }
      }
      const res = await exportStudentSchemeLearning.exportStudentSchemeLearningIntegrationDataExcelInServicerManageRegion(
        param
      )
      console.log('调用了exportExcel方法，返回值=', res)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，exportExcel',
        e
      )
    }
  }
  /**
   * 管理员导出对接数据
   */
  async exportExcel(param: StudentSchemeLearningRequestVo) {
    try {
      console.log('param参数=', param)
      let userIdList: string[] = []
      if (param.name || param.idCard || param.phone) {
        userIdList = await this.getUsers(param)
      }
      if (userIdList && userIdList.length) {
        param.student.userIdList = userIdList
      }
      param.learningRegister.status = [1, 2]
      if (param.syncResult) {
        if (param.syncResult === SyncResultEnmu.Unsynchronized) {
          param.connectManageSystem.syncStatus = 0
        }
        if (param.syncResult === SyncResultEnmu.Synchronized) {
          param.connectManageSystem.syncStatus = 1
        }
        if (param.syncResult === SyncResultEnmu.SynchronizationFailure) {
          param.connectManageSystem.syncStatus = 2
        }
      }
      const res = await exportStudentSchemeLearning.exportStudentSchemeLearningIntegrationDataExcelInServicer(param)
      console.log('调用了exportExcel方法，返回值=', res)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，exportExcel',
        e
      )
    }
  }
  private async getUsers(filter: StudentSchemeLearningRequestVo) {
    let userIdList: string[] = []

    const queryUser = UserModule.queryUserFactory.queryStudentList
    queryUser.queryStudentIdParams.idCard = filter.idCard
    queryUser.queryStudentIdParams.userName = filter.name
    queryUser.queryStudentIdParams.phone = filter.phone
    const res = await queryUser.queryStudentIdList()
    userIdList = res.data

    return userIdList
  }
}
export default ExportDockingdata
