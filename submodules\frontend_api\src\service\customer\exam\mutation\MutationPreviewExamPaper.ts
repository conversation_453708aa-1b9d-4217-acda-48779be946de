import msExam from '@api/ms-gateway/ms-examextraction-v1'
import { MutationCreatePaper } from '@api/service/customer/exam/mutation/MutationCreatePaper'

export class MutationPreviewExamPaper extends MutationCreatePaper {
  //卷子模板id
  paperPublishConfigureId = ''
  /**
   * 获取作答token
   */
  async getAnswerToken() {
    const res = await msExam.previewAnswerPaper(this.paperPublishConfigureId)
    // if (res.status.isSuccess()) {
    //   this.answerToken = res.data
    // }
    return res.status
  }
}
