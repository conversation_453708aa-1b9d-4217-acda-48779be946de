import LearningTypeBase from '@api/service/customer/train-class/query/vo/LearningTypeBase'
import Classification from '@api/service/customer/train-class/query/vo/Classification'

/**
 * 兴趣课
 */
class InterestCourseType extends LearningTypeBase {
  // region properties

  /**
   *分类+课程包id  其中第一层是顶级分类，如果第一级无children则代表无分类，类型为Classification[]
   */
  classification = new Classification()
  // endregion
  // region methods

  // endregion
}
export default InterestCourseType
