import { CourseTrainingOutlineResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

/**
 * 培训班课程大纲
 */
class TrainingCourseOutline {
  // 是否是特殊节点（手动加的“全部”节点）
  isSpecialNode = false
  // 大纲id
  id: string = null
  // 大纲名称
  name: string = null
  // 父类 id
  parentId: string = null
  // 子类列表
  children: Array<TrainingCourseOutline> = undefined
  // 所需学时
  requirePeriod: number = null
  // 已学学时
  learnedPeriod: number = null
  // 必学课程要求学时，仅当自主选课模式下有效
  compulsoryRequirePeriod = 0
  // 必学课程已完成学时，仅当自主选课模式下有效
  compulsoryLearnedPeriod = 0
  // 是否存在必学
  isCompulsoryCourse: boolean = null
  // 要排除的大纲id
  excludeOutlineIds?: Array<string> = new Array<string>()

  static from(response: CourseTrainingOutlineResponse) {
    const outline = new TrainingCourseOutline()
    outline.id = response.outlineId
    outline.name = response.courseTrainingOutline.name
    outline.parentId = response.courseTrainingOutline.parentId
    outline.children = new Array<TrainingCourseOutline>()
    return outline
  }

  static fromSchemeConfig(config: any): TrainingCourseOutline {
    const outline = new TrainingCourseOutline()
    outline.id = config.id
    outline.name = config.name
    outline.parentId = config.parentId
    outline.requirePeriod = config?.assessSetting?.requirePeriod
    outline.isCompulsoryCourse = config?.compulsoryCourseIdList?.length ? true : false
    outline.children = config.children?.map(TrainingCourseOutline.fromSchemeConfig)
    return outline
  }
}

export default TrainingCourseOutline
