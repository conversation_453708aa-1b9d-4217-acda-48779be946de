import UpdateStudentRequestVo from '@api/service/customer/user/mutation/vo/create/UpdateStudentRequestVo'
import CreateUserIndustryRequestVo from '@api/service/customer/user/mutation/vo/create/CreateUserIndustryRequestVo'

/*
 *创建学员信息
 */
class UpdateStudentRequestVoDiff extends UpdateStudentRequestVo {
  /**
   * 地区code
   */
  areaCode: string
  /**
   * 工作单位性质
   */
  unitNature = ''
  /**
   * 在编情况
   */
  staffingStatus = ''
  /**
   * 是否在专技岗位工作
   */
  isZjPosition = ''
  /**
   * 职称系列
   */
  titleSeries = ''
  /**
   * 职称专业
   */
  titleProfessional = ''
  /**
   * 现有职称等级
   */
  titleGrade = ''
  /**
   * 现有职称资格名称
   */
  titleQualificationName = ''
  /**
   * 现有职称有效范围
   */
  titleEffectiveRange = ''
  /**
   * 最高学历
   */
  highestEducationLevel = ''
}

export default UpdateStudentRequestVoDiff
