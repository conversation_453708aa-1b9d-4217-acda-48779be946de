import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjy-supplier-import-open-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjy-supplier-import-open-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum Direction {
  ASC = 'ASC',
  DESC = 'DESC'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 批量并行查询
 */
export class BatchParallelQueryDto {
  /**
   * 不同ParallelQueryDto间查询条件相互独立
   */
  parallelQueryDtoList?: Array<ParallelQueryDto>
}

export class ImportOpenQueryParamRequest {
  /**
   * 主任务id
   */
  mainTaskId?: string
  /**
   * 不同的BatchParallelQueryDto间查询条件不相互独立，需要同时满足条件才可以查到结果
   */
  batchParallelQueryDtoList?: Array<BatchParallelQueryDto>
  /**
   * 订单状态
0-未开通 1-开通中 2-已开通，默认null
@see OrderStateConstant
   */
  orderState?: number
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 商品id
   */
  commodityId?: string
  /**
   * 子任务状态
0 - 已创建
1 - 已就绪
2 - 执行中
3 - 执行完成
   */
  subTaskState?: number
  /**
   * 子任务处理结果
0 - 未处理
1 - 处理成功
2 - 处理失败
3 - 就绪失败
   */
  subTaskProcessResult?: number
  /**
   * 导入开始时间
   */
  importStartTime?: string
  /**
   * 导入结束时间
   */
  importEndTime?: string
  /**
   * 用户快照
   */
  userSnapshot?: UserSnapshot
}

/**
 * 单个属性元数据
<AUTHOR>
@since 2022/4/20
 */
export class MetaProperty {
  /**
   * 属性键
   */
  key?: string
  /**
   * 属性值
   */
  value?: string
}

/**
 * 单个属性元数据
<AUTHOR>
@since 2022/4/20
 */
export class MetaPropertyExist {
  /**
   * 属性键
订单状态：orderState
   */
  key?: string
  /**
   * 是否存在
   */
  value: boolean
}

export class ParallelQueryDto {
  commodityId?: string
  /**
   * 子任务状态
0 - 已创建
1 - 已就绪
2 - 执行中
3 - 执行完成
   */
  subTaskState?: number
  /**
   * 子任务处理结果
0 - 未处理
1 - 处理成功
2 - 处理失败
3 - 就绪失败
   */
  subTaskProcessResult?: number
  /**
   * 查询属性是否存在集合
   */
  existProperties?: Array<MetaPropertyExist>
  /**
   * 订单状态
0-未开通 1-开通中 2-已开通，3-无法创建订单，默认null
@see OrderStateConstant
   */
  orderState?: number
}

/**
 * 批量标记删除数据入参
<AUTHOR>
 */
export class BatchMarkDeleteSignupRequest {
  /**
   * 批次号
   */
  batchNo: string
  /**
   * 子任务集合
   */
  subTaskIdList: Array<string>
}

/**
 * 供应商导入开通导入参数
<AUTHOR>
 */
export class SupplierImportOpenRequest {
  /**
   * 批次号
允许为空，若为空则默认创建新的批次
   */
  batchNo?: string
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 文件名称
   */
  fileName?: string
  /**
   * 终端类型
<p>
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  terminalCode?: string
  /**
   * 注册方式
@see AccountRegisterTypes
   */
  registerType: number
  /**
   * 来源类型
@see AccountSourceTypes
   */
  sourceType: number
  /**
   * 默认密码
   */
  password?: string
  /**
   * 密码模式
1-默认密码——password
2-使用身份证后六位
   */
  passwordModel: number
  /**
   * 密码生效范围，1-默认仅新用户，2-全部用户（含已注册）
   */
  passwordEffectiveRange: number
  /**
   * 是否更新基础信息
   */
  updateBasicInfo: boolean
}

/**
 * 集体缴费查询对象
<AUTHOR>
 */
export class SupplierImportQueryRequest {
  /**
   * 批次编号（上传失败、上传成功列表接口调用时必填，供应商分销导入跟踪里不用填）
   */
  batchNo?: string
  /**
   * 查询属性集合
   */
  metaPropertyList?: Array<MetaProperty>
  /**
   * 模糊查询的属性集合
   */
  dimMetaPropertyKeyList?: Array<string>
  /**
   * 模糊查询的拓展属性集合
   */
  dimExpandPropertyKeyList?: Array<string>
  /**
   * 额外附加查询参数
   */
  queryParam?: ImportOpenQueryParamRequest
  /**
   * 主任务类型列表
   */
  taskCategoryList?: Array<string>
}

export class TaskBatchImportParamRequest {
  /**
   * 主任务类型列表
   */
  taskCategoryList?: Array<string>
  /**
   * 任务名称
   */
  name?: string
  /**
   * 任务执行状态
0-执行中 1-成功 2-失败 3-部分失败
@see com.fjhb.batchtask.core.enums.TaskExecuteState
   */
  taskExecuteState?: number
  /**
   * 导入时间（起始）
导入时间：:主任务最小执行时间
   */
  importStartTime?: string
  /**
   * 导入时间（终止）
   */
  importEndTime?: string
}

export class TaskExecuteParamRequest {
  /**
   * 任务名称
   */
  taskName?: string
  /**
   * 任务类型
分销导入-DISTRIBUTION_IMPORT
优惠导入-DISCOUNT_IMPORT
   */
  category?: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState?: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult?: number
  /**
   * 执行时间（起始）
   */
  executeStartTime?: string
  /**
   * 执行时间（终止）
   */
  executeEndTime?: string
}

/**
 * @Description
<AUTHOR>
@Date 2024/10/28 8:48
 */
export class UserSnapshot {
  /**
   * 名字快照
   */
  nameSnapshot?: string
  /**
   * 身份证快照
   */
  idCardSnapshot?: string
  /**
   * 身份证类型快照
   */
  idCardTypeSnapshot?: number
  /**
   * 手机号快照
   */
  phoneSnapshot?: string
  /**
   * 工作单位地区code
   */
  unitAreaCodeSnapshot?: string
  /**
   * 单位统一信用编码
   */
  companyCodeSnapshot?: string
  /**
   * 单位名称快照
   */
  companyNameSnapshot?: string
  /**
   * 单位所在省份快照
   */
  unitProvinceSnapshot?: string
  /**
   * 单位所在城市快照
   */
  unitCitySnapshot?: string
  /**
   * 单位所在区县快照
   */
  unitAreaSnapshot?: string
}

export class SortPolicy {
  orders: Array<Order>
}

export class Order {
  direction: Direction
  sortKey: string
}

/**
 * 各状态及执行结果对应数量
<AUTHOR>
 */
export class EachStateCount {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

/**
 * 单个属性元数据
<AUTHOR>
@since 2022/4/20
 */
export class MetaProperty1 {
  /**
   * 属性键
   */
  key: string
  /**
   * 属性值
   */
  value: string
}

/**
 * 数据行对象
<AUTHOR>
@since 2022/4/24
 */
export class SupplierImportOpenMetaRow {
  /**
   * 每一行的数据
   */
  row: Array<MetaProperty1>
  /**
   * 子任务状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  subTaskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 订单状态
其余情况-null
未开通-0
开通中-1
已开通-2
   */
  orderState: number
  /**
   * 是否更新密码
   */
  updatePassword: boolean
  /**
   * 是否更新基础信息
   */
  updateBasicInfo: boolean
  /**
   * 学员状态
null-暂无记录
0-默认
1-新学员
2-已注册的学员
   */
  studentState: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 完成时间
   */
  completeTime: string
  /**
   * 错误日志
   */
  errorMessage: string
  /**
   * 价格
   */
  price: number
  /**
   * 用户信息快照
   */
  userSnapshot: UserSnapshot1
  /**
   * 分销商名称
   */
  distributorName: string
  /**
   * 所属批次单编号
   */
  batchNo: string
}

export class BatchTaskExecuteByPageResponse {
  /**
   * 任务名称
   */
  name: string
  /**
   * 导入时间：批次分组下的主任务最小的执行时间
   */
  importTime: string
  /**
   * 结束时间：批次分组下主任务最大完成时间
   */
  endTime: string
  /**
   * 成功數量
   */
  successCount: number
  /**
   * 失敗數量
   */
  failureCount: number
  /**
   * 总數量
   */
  totalCount: number
}

/**
 * <AUTHOR>
@date 2024/12/20 9:32
 */
export class ImportOpenBaseResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * <AUTHOR>
@date 2024/12/13 17:15
 */
export class ImportOpenPageWithSuccessAmount {
  successAmount: number
  totalPageSize: number
  currentPageData: Array<SupplierImportOpenMetaRow>
  currentPageStartIndex: number
  pageSize: number
  totalSize: number
  pageNo: number
  sortPolicy: SortPolicy
}

/**
 * 导入开班上传模板结果
异常code
500-接口异常
3003-表头校验失败
3004-excel表最大长度校验失败
<AUTHOR>
@since 2022/5/12
 */
export class ImportOpenResponse {
  /**
   * 批次订单号
   */
  batchOrderNo: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 报名数据解析响应
<AUTHOR>
 */
export class SignupDataAnalysisResponse {
  /**
   * 解析状态
0&#x3D;未解析，1-解析中，2&#x3D;解析完成
   */
  processStatus: number
  /**
   * 总数
   */
  totalCount: number
  /**
   * 已执行数
   */
  executedCount: number
}

/**
 * 报名数据执行进度响应
<AUTHOR>
 */
export class SignupDataProcessResponse {
  /**
   * 指定批次下是否全部所有主任务执行成功
   */
  completed: boolean
  /**
   * 总数
   */
  totalCount: number
  /**
   * 已执行数
   */
  executedCount: number
}

/**
 * 任务执行情况
<AUTHOR>
@since 2022/5/5
 */
export class TaskExecuteByPageResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务分类
   */
  category: string
  /**
   * 所属批次单编号
   */
  batchNo: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 就绪时间
   */
  alreadyTime: string
  /**
   * 执行时间
   */
  executingTime: string
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount>
}

/**
 * @Description
<AUTHOR>
@Date 2024/10/28 8:48
 */
export class UserSnapshot1 {
  /**
   * 名字快照
   */
  nameSnapshot: string
  /**
   * 身份证快照
   */
  idCardSnapshot: string
  /**
   * 身份证类型快照
   */
  idCardTypeSnapshot: number
  /**
   * 手机号快照
   */
  phoneSnapshot: string
  /**
   * 工作单位地区code
   */
  unitAreaCodeSnapshot: string
  /**
   * 单位统一信用编码
   */
  companyCodeSnapshot: string
  /**
   * 单位名称快照
   */
  companyNameSnapshot: string
  /**
   * 单位所在省份快照
   */
  unitProvinceSnapshot: string
  /**
   * 单位所在城市快照
   */
  unitCitySnapshot: string
  /**
   * 单位所在区县快照
   */
  unitAreaSnapshot: string
}

export class SupplierImportOpenMetaRowPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SupplierImportOpenMetaRow>
}

export class BatchTaskExecuteByPageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BatchTaskExecuteByPageResponse>
}

export class TaskExecuteByPageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TaskExecuteByPageResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出 导入模板失败数据
   * @param batchNo 批次号
   * @return excel文件路径
   * @param query 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportAlreadyFailExcel(
    batchNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportAlreadyFailExcel,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { batchNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询导入excel就绪失败的数据列表
   * @param request 集体查询请求
   * @param page    分页信息
   * @return 分页响应数据
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findImportFailDataByPage(
    params: { request?: SupplierImportQueryRequest; page: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findImportFailDataByPage,
    operation?: string
  ): Promise<Response<SupplierImportOpenMetaRowPage>> {
    return commonRequestApi<SupplierImportOpenMetaRowPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询导入excel就绪成功的数据列表
   * @param request 集体查询请求
   * @param page    分页信息
   * @return 分页响应数据
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findImportSuccessDataByPage(
    params: { request?: SupplierImportQueryRequest; page: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findImportSuccessDataByPage,
    operation?: string
  ): Promise<Response<ImportOpenPageWithSuccessAmount>> {
    return commonRequestApi<ImportOpenPageWithSuccessAmount>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询开通学习后的数据列表
   * @param request 集体查询请求
   * @param page    分页信息
   * @return 分页响应数据
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findOpenLearningPermissionSubTuskByPage(
    params: { request?: SupplierImportQueryRequest; page: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findOpenLearningPermissionSubTuskByPage,
    operation?: string
  ): Promise<Response<SupplierImportOpenMetaRowPage>> {
    return commonRequestApi<SupplierImportOpenMetaRowPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 按批次分组查询任务执行情况，根据服务商进行筛选
   * @param page 分页信息
   * @return 执行情况
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTaskExecuteByBatchAndServicer(
    params: { param?: TaskBatchImportParamRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findTaskExecuteByBatchAndServicer,
    operation?: string
  ): Promise<Response<BatchTaskExecuteByPageResponsePage>> {
    return commonRequestApi<BatchTaskExecuteByPageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询指定批次下的任务执行情况，根据服务商进行筛选
   * @param page 分页信息
   * @return 执行情况
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTaskExecuteWithServicerResponseByPage(
    params: { param?: TaskExecuteParamRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findTaskExecuteWithServicerResponseByPage,
    operation?: string
  ): Promise<Response<TaskExecuteByPageResponsePage>> {
    return commonRequestApi<TaskExecuteByPageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询模板地址
   * 入参：任务类型
   * 供应商分销导入基础版-SUPPLIER_DISTRIBUTION_BASIC_IMPORT
   * @param query 查询 graphql 语法文档
   * @param category 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryImportOpenTemplatePath(
    category: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryImportOpenTemplatePath,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { category },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 报名数据excel解析进度
   * @param batchNo 集体报名编号
   * @return 报名数据解析响应
   * @param query 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async supplierImportDataAnalysis(
    batchNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.supplierImportDataAnalysis,
    operation?: string
  ): Promise<Response<SignupDataAnalysisResponse>> {
    return commonRequestApi<SignupDataAnalysisResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { batchNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 提交后报名数据执行进度
   * @param batchNo 集体报名编号
   * @return 报名数据执行响应
   * @param query 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async supplierImportDataProcess(
    batchNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.supplierImportDataProcess,
    operation?: string
  ): Promise<Response<SignupDataProcessResponse>> {
    return commonRequestApi<SignupDataProcessResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { batchNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量标记删除报名数据
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchMarkDeleteSignupData(
    request: BatchMarkDeleteSignupRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchMarkDeleteSignupData,
    operation?: string
  ): Promise<Response<ImportOpenBaseResponse>> {
    return commonRequestApi<ImportOpenBaseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 清空失败数据
   * @param batchNo 集体报名编号
   * @param mutate 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async clearFailureData(
    batchNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.clearFailureData,
    operation?: string
  ): Promise<Response<ImportOpenBaseResponse>> {
    return commonRequestApi<ImportOpenBaseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { batchNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 开通学习权限
   * @param batchNo 集体报名编号
   * @param mutate 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async openLearningPermission(
    batchNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.openLearningPermission,
    operation?: string
  ): Promise<Response<ImportOpenBaseResponse>> {
    return commonRequestApi<ImportOpenBaseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { batchNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商为分销商导入开通（基础版）
   * @param importRequest 导入信息
   * @param mutate 查询 graphql 语法文档
   * @param importRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async supplierImportOpenBasic(
    importRequest: SupplierImportOpenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.supplierImportOpenBasic,
    operation?: string
  ): Promise<Response<ImportOpenResponse>> {
    return commonRequestApi<ImportOpenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { importRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商为分销商导入开通（专业版）
   * @param importRequest 导入信息
   * @param mutate 查询 graphql 语法文档
   * @param importRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async supplierImportOpenMajor(
    importRequest: SupplierImportOpenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.supplierImportOpenMajor,
    operation?: string
  ): Promise<Response<ImportOpenResponse>> {
    return commonRequestApi<ImportOpenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { importRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
