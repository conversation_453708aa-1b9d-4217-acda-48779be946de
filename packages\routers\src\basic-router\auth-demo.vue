<route-meta>
{
  "title": "认证权限演示",
  "requiresAuth": true,
  "isMenu": false
}
</route-meta>

<template>
  <div>
    <!-- 直接引用我们创建的认证演示组件 -->
    <AuthDemoPage />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import AuthDemoPage from '@packages/user/src/views/auth-demo/index.vue'

@Component({
  components: {
    AuthDemoPage
  }
})
export default class AuthDemoRoute extends Vue {
  // 这个文件会自动生成路由: /auth-demo
}
</script>
