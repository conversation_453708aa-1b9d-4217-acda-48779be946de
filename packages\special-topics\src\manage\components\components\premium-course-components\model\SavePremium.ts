import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'
import { DeleteSelectedCourse, SelectedCourse } from '@api/platform-gateway/platform-training-channel-v1'
import ThematicMangementCourseItem from '@api/service/management/thematic-management/model/ThematicMangementCourseItem'

/**
 * @description 弹窗保存精品课程
 * */
class SavePremium {
  constructor(
    addList?: Array<CourseListDetail>,
    prevDelList?: Array<CourseListDetail>,
    updateList?: Array<CourseListDetail>
  ) {
    this.addList = addList || new Array<CourseListDetail>()
    this.prevDelList = prevDelList || new Array<CourseListDetail>()
    this.updateList = updateList || new Array<CourseListDetail>()
  }

  /**
   * @description 新增的精品课程
   * */
  addList: Array<CourseListDetail> = new Array<CourseListDetail>()

  /**
   * @description 曾选中但保存时被移除的精品课程
   * */
  prevDelList: Array<CourseListDetail> = new Array<CourseListDetail>()

  /**
   * @description 做变更的精品课程
   * */
  updateList: Array<CourseListDetail> = new Array<CourseListDetail>()
}

/**
 * @description 抽屉弹窗查询
 * */
export class DrawerSearchParam {
  /**
   * @description 课程名称
   * */
  name = ''

  /**
   * @description 分类id列表
   * */
  categoryIdList: Array<string> = new Array<string>()
}

export class UpdateParam {
  /**
   * @description 课程id
   */
  id = ''
  /**
   * @description 排序索引
   */
  sort = 0
}

export class UpdatePremium {
  courseCategoryId?: string
  addCourse?: Array<SelectedCourse>
  updateCourse?: Array<SelectedCourse>
  deleteCourse?: Array<DeleteSelectedCourse>
}

export class MyTools {
  /**
   * @description 模型转换
   * @mark CourseListDetail -> SelectedCourse
   * */
  toSelectedCourse(list: Array<ThematicMangementCourseItem>) {
    // todo
    const selectedCourse = new Array<SelectedCourse>()
    list.map(item => {
      const itm = new SelectedCourse()
      itm.courseId = item.courseId
      itm.sort = item.sort
      selectedCourse.push(itm)
    })
    return selectedCourse
  }

  /**
   * @description 模型转换
   * @mark ThematicMangementCourseItem -> DeleteSelectedCourse
   * */
  toDeleteSelectedCourse(list: Array<ThematicMangementCourseItem>) {
    const deleteSelectedCourse = new Array<DeleteSelectedCourse>()
    list.map(item => {
      const itm = new DeleteSelectedCourse()
      itm.courseId = item.courseId
      deleteSelectedCourse.push(itm)
    })
    return deleteSelectedCourse
  }
}

export default SavePremium
