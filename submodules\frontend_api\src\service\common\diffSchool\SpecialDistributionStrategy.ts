export enum specialDistributionIdEnum {
  // 华医网分销
  HYWFX = 'hywfx',
  // 厦门理工专题
  HYWXMLGFX = 'hywxmlgfx',
  // 漳州天天专题
  HYWZZTTFX = 'hywzzttfx'
}
import systemContext from '@api/service/common/context/Context'
import { frontendApplication, frontendApplicationDiff } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'

/**
 * 差异化专题映射管理
 */
class SpecialDistributionStrategy {
  // 专题映射关系
  specialDistributionIdMap = new Map<string, specialDistributionIdEnum>()

  /**
   * 读取阿波罗网校服务商id
   */
  getSpecialDistributionId = {
    // 福州市专题
    hywfx: () => {
      const specialDistributionId = ConfigCenterModule.getFrontendApplicationDiff(
        frontendApplicationDiff.fjzjHywFxId
      )?.split(',')
      specialDistributionId?.forEach((id) => {
        if (!this.specialDistributionIdMap.has(id)) {
          this.specialDistributionIdMap.set(id, specialDistributionIdEnum.HYWFX)
        }
        return this.specialDistributionIdMap.get(id)
      })
    },
    // 厦门分销
    hywxmlgfx: () => {
      const specialDistributionId = ConfigCenterModule.getFrontendApplicationDiff(
        frontendApplicationDiff.xmlgHywFxId
      )?.split(',')
      specialDistributionId?.forEach((id) => {
        if (!this.specialDistributionIdMap.has(id)) {
          this.specialDistributionIdMap.set(id, specialDistributionIdEnum.HYWXMLGFX)
        }
        return this.specialDistributionIdMap.get(id)
      })
    },

    // 漳州天天
    hywzzttfx: () => {
      const specialDistributionId = ConfigCenterModule.getFrontendApplicationDiff(
        frontendApplicationDiff.zzttHywFxId
      )?.split(',')
      specialDistributionId?.forEach((id) => {
        if (!this.specialDistributionIdMap.has(id)) {
          this.specialDistributionIdMap.set(id, specialDistributionIdEnum.HYWZZTTFX)
        }
        return this.specialDistributionIdMap.get(id)
      })
    }
  }

  /**
   * 构建映射关系
   */
  buildSpecialSpecialDistributionMap() {
    // TODO 构建专题映射关系
    this.getSpecialDistributionId.hywfx()
    this.getSpecialDistributionId.hywxmlgfx()
    this.getSpecialDistributionId.hywzzttfx()
  }

  // 获取当前分销
  getCurrentSpecialDistribution() {
    const serverId = systemContext.fxPortalInfo?.serviceToken?.tokenMeta?.servicerId
    if (this.specialDistributionIdMap.has(serverId)) {
      return this.specialDistributionIdMap.get(serverId)
    }
    return ''
  }
}

export default new SpecialDistributionStrategy()
