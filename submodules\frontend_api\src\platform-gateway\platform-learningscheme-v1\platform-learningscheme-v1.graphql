schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""异步创建学习方案配置接口
		@return 学习方案id
	"""
	asyncCreateLearningScheme(request:AsyncCreateLearningSchemeRequest):AsyncCreateLearningSchemeResponse
	"""异步更新学习方案配置接口
		@return 学习方案id
	"""
	asyncUpdateLearningScheme(request:AsyncUpdateLearningSchemeRequest):AsyncUpdateLearningSchemeResponse
	"""一键合格
		10001 没有剩余的考试次数,无法操作
		500 其他原因导致失败
		@param request:
		@return {@link OneKeyPassResponse}
		<AUTHOR> By Cb
		@since 2024/10/14 14:39
	"""
	oneKeyPass(request:OneKeyPassRequest):OneKeyPassResponse
	"""学员学习资源是否推送完成
		@param request
		@return true:推送完成 false:推送未完成
	"""
	studentLearningResourcePushIsCompleted(request:StudentLearningResourcePushIsCompletedRequest):Boolean!
}
"""异步创建学习方案请求
	<AUTHOR>
"""
input AsyncCreateLearningSchemeRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.AsyncCreateLearningSchemeRequest") {
	"""校验token"""
	token:String
	"""方案配置json字符串"""
	configJson:String
}
"""异步修改学习方案请求
	<AUTHOR>
"""
input AsyncUpdateLearningSchemeRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.AsyncUpdateLearningSchemeRequest") {
	"""校验token"""
	token:String
	"""方案配置json字符串"""
	configJson:String
}
"""<AUTHOR> create 2022/5/25 16:14"""
input OneKeyPassRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.OneKeyPassRequest") {
	"""参训资格id"""
	qualificationId:String
	"""合格时间配置 1：按系统当前操作成功时间"""
	passTimeType:Int!
	"""合格元数据"""
	metaDataList:[MetaData]
}
input MetaData @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.OneKeyPassRequest$MetaData") {
	"""key
		包含学习方式key+合格属性key以.分隔
		chooseCourseLearning:选课学习
		examLearning:考试
		practiceLearning:练习
		autonomousCourseLearning:自主学习
		interestCourseLearning:兴趣课学习
		@see
	"""
	key:String
	"""值"""
	value:String
}
"""学员学习资源是否推送完成接口
	return true:推送完成 false:推送未完成
	<AUTHOR>
"""
input StudentLearningResourcePushIsCompletedRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.StudentLearningResourcePushIsCompletedRequest") {
	qualificationId:String!
}
"""异步创建培训方案响应
	<AUTHOR>
	@since 2024/12/13
"""
type AsyncCreateLearningSchemeResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.AsyncCreateLearningSchemeResponse") {
	"""方案id"""
	schemeId:String
}
"""异步更新培训方案响应
	<AUTHOR>
	@since 2024/12/13
"""
type AsyncUpdateLearningSchemeResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.AsyncUpdateLearningSchemeResponse") {
	"""方案id"""
	schemeId:String
}
"""一键合格任务响应
	<AUTHOR> By Cb
	@since 2024/10/14 14:34
"""
type OneKeyPassResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.OneKeyPassResponse") {
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}

scalar List
