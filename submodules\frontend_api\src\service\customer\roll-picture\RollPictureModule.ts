import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import PlatformRollPicture, { RollPictureDetailResponse, RollPictureTypeEnum } from '@api/gateway/PlatformRollPicture'
import { ResponseStatus } from '@api/Response'
import { RollPictureDetail } from '@api/service/common/roll-picture/model/RollPictureDetail'
import Vue from 'vue'
import { UnAuthorize } from '@api/Secure'

export interface RollPictureState {
  /**
   * 轮播图列表
   * key由页面提供，页面获取分页列表时根据key再获取对应分页信息
   */
  rollPictureListMap: { [key: string]: { list: Array<RollPictureDetail> } }
}

@Module({ namespaced: true, store, dynamic: true, name: 'CustomerRollPictureModule' })
class RollPictureModule extends VuexModule implements RollPictureState {
  rollPictureListMap: { [key: string]: { list: Array<RollPictureDetail> } } = {}

  /**
   * 通过类别查询轮播图信息，key为轮播图类别，key不传时类别为RollPictureTypeEnum.DEFAULT，查全部
   * @param key
   * @see RollPictureTypeEnum
   */
  @UnAuthorize
  @Action
  async listByType(key?: RollPictureTypeEnum): Promise<ResponseStatus> {
    if (!key) {
      key = RollPictureTypeEnum.DEFAULT
    }
    const response = await PlatformRollPicture.listByType(key)
    if (!response.status.isSuccess()) {
      return response.status
    }
    this.SET_LIST({ key: key, list: response.data })
    return response.status
  }

  @Mutation
  SET_LIST(payload: { key: string; list: Array<RollPictureDetailResponse> }) {
    Vue.set(this.rollPictureListMap, payload.key, payload.list)
  }
}

export default getModule(RollPictureModule)
