/**
 * 查询列表
 */
import { Response, UiPage } from '@hbfe/common'
import { ResponsePage } from '@api/service/customer/course/query/QueryCourse'

export const queryList = async <
  T extends (params: { page: UiPage; request: P }) => Promise<Response<ResponsePage<O>>>,
  P,
  O
>(
  query: T,
  params: P
): Promise<ResponsePage<O>> => {
  const page = new UiPage()
  page.pageNo = 1
  page.pageSize = 1
  const result = await query({
    page,
    request: params
  })
  page.pageSize = result.data.totalSize
  const lastResult = await query({
    page,
    request: params
  })
  return lastResult.data
}

export function checkDomain() {
  //验证是否是域名
  const doname = /^([\w-]+\.)+((com)|(net)|(org)|(gov\.cn)|(info)|(cc)|(com\.cn)|(net\.cn)|(org\.cn)|(name)|(biz)|(tv)|(cn)|(mobi)|(name)|(sh)|(ac)|(io)|(tw)|(com\.tw)|(hk)|(com\.hk)|(ws)|(travel)|(us)|(tm)|(la)|(me\.uk)|(org\.uk)|(ltd\.uk)|(plc\.uk)|(in)|(eu)|(it)|(jp))$/
  const flag_domain = doname.test(document?.domain)
  if (!document) {
    // 移动端（小程序、H5）默认用域名
    return true
  }
  if (!flag_domain) {
    //错误的域名
    return false
  } else {
    return true
  }
}
