schema {
	query:Query
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	exportBatchReconciliation(reconciliationQueryParamsDTO:BatchReconciliationQueryParamsDTO):Boolean!
	exportReconciliation(reconciliationQueryParamsDTO:ReconciliationQueryParamsDTO):Boolean!
	getBatchReconciliationStatistic(reconciliationQueryParamsDTO:BatchReconciliationQueryParamsDTO):ReconciliationStatisticDTO
	getReconciliationStatistic(reconciliationQueryParamsDTO:ReconciliationQueryParamsDTO):ReconciliationStatisticDTO
	pageBatchReconciliation(page:Page,reconciliationQueryParamsDTO:BatchReconciliationQueryParamsDTO):BatchReconciliationDTOPage @page(for:"BatchReconciliationDTO")
	pageReconciliation(page:Page,reconciliationQueryParamsDTO:ReconciliationQueryParamsDTO):ReconciliationDTOPage @page(for:"ReconciliationDTO")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""对账管理查询参数dto
	Author:FangKunSen
	Time:2020-03-30,14:10
"""
input BatchReconciliationQueryParamsDTO @type(value:"com.fjhb.fjszyws.integrative.service.reconciliation.dto.BatchReconciliationQueryParamsDTO") {
	"""收款账号id"""
	paymentAccountId:String
	"""交易流水号"""
	tradeFlowNo:String
	"""下单渠道"""
	channel:String
	"""交易成功时间 起"""
	tradeSuccessTimeStart:DateTime
	"""交易成功时间 止"""
	tradeSuccessTimeEnd:DateTime
	"""集体缴费批次号"""
	batchNo:String
}
"""对账管理查询参数dto
	Author:FangKunSen
	Time:2020-03-30,14:10
"""
input ReconciliationQueryParamsDTO @type(value:"com.fjhb.fjszyws.integrative.service.reconciliation.dto.ReconciliationQueryParamsDTO") {
	"""收款账号id"""
	paymentAccountId:String
	"""订单号"""
	orderNo:String
	"""交易流水号"""
	tradeFlowNo:String
	"""下单渠道"""
	channel:String
	"""交易成功时间 起"""
	tradeSuccessTimeStart:DateTime
	"""交易成功时间 止"""
	tradeSuccessTimeEnd:DateTime
}
"""对账管理dto
	Author:FangKunSen
	Time:2020-03-30,14:14
"""
type BatchReconciliationDTO @type(value:"com.fjhb.fjszyws.integrative.service.reconciliation.dto.BatchReconciliationDTO") {
	"""订单号"""
	batchNo:String
	"""交易流水号"""
	tradeFlowNo:String
	"""交易成功时间"""
	tradeSuccessTime:DateTime
	"""支付成功时间"""
	paySuccessTime:DateTime
	"""买家姓名"""
	buyerName:String
	"""买家手机号"""
	buyerPhone:String
	"""买家身份证号"""
	buyerIdNumber:String
	"""实付金额"""
	amount:String
	"""收款账号"""
	paymentAccountName:String
}
"""对账管理dto
	Author:FangKunSen
	Time:2020-03-30,14:14
"""
type ReconciliationDTO @type(value:"com.fjhb.fjszyws.integrative.service.reconciliation.dto.ReconciliationDTO") {
	"""订单号"""
	orderNo:String
	"""交易流水号"""
	tradeFlowNo:String
	"""交易成功时间"""
	tradeSuccessTime:DateTime
	"""支付成功时间"""
	paySuccessTime:DateTime
	"""所属培训机构"""
	unitName:String
	"""买家姓名"""
	buyerName:String
	"""买家手机号"""
	buyerPhone:String
	"""买家身份证号"""
	buyerIdNumber:String
	"""实付金额"""
	amount:String
	"""收款账号"""
	paymentAccountName:String
}
"""对账管理合计dto
	Author:FangKunSen
	Time:2020-03-30,14:23
"""
type ReconciliationStatisticDTO @type(value:"com.fjhb.fjszyws.integrative.service.reconciliation.dto.ReconciliationStatisticDTO") {
	"""订单总数"""
	totalOrderNumber:Long!
	"""交易总额"""
	totalAmount:Double!
}

scalar List
type BatchReconciliationDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BatchReconciliationDTO]}
type ReconciliationDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ReconciliationDTO]}
