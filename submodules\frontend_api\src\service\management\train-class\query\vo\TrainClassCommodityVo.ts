import { Page, ResponseStatus } from '@hbfe/common'
import { CommoditySkuBackstageResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
import MutationTrainClassCommodityClass from '@api/service/management/train-class/mutation/MutationTrainClassCommodityClass'
import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
import MsSchemeLearningQueryFrontGatewaySchemeLearningQueryBackstage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import SaleChanelShowRange from '@api/service/management/train-class/query/vo/SaleChanelShowRange'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import ShowUserRange, { ShowUserRangeEnum } from '@api/service/management/train-class/query/enum/ShowUserRangeEnum'
import PlatformTrainingChannel, { UpdateSaleSettingRequest } from '@api/platform-gateway/platform-training-channel-v1'

/**
 * 培训班列表Vo
 */
class TrainClassCommodityVo extends CommoditySkuBackstageResponse {
  // region properties
  /**
   *班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuValueNameProperty = new SkuPropertyResponseVo()
  /**
   *培训方案类型1: 选课规则2: 自主选课，类型为number
   */
  schemeType = 0
  /**
   *报名开始时间，类型为string
   */
  registerBeginDate = ''
  /**
   *报名结束时间，类型为string
   */
  registerEndDate = ''
  /**
   *培训开始时间，类型为string
   */
  trainingBeginDate = ''
  /**
   *培训结束时间，类型为string
   */
  trainingEndDate = ''
  /**
   *班级id类型为string
   */
  schemeId = ''
  /**
   *获得学时，类型为number
   */
  period = 0
  // endregion
  // region methods
  /**
   * 方案事务状态
   * 已开始 = 1
   * 预提交 = 2
   * 提交 = 3
   * 回滚 = 4
   * 完成 = 5（仅此状态，且重算完成的情况下，方案可修改）
   */
  lastTransactionStep = 999
  /**
   * 是否重算中
   */
  recalculating = false
  /**
   * 是否智能学习中
   */
  // TODO 智能学习 列表显示处理中
  intelligentLearning = false
  /**
   * 是否挂起
   */
  hangUp = false
  /**
   * 是否异常
   */
  hasError = false
  /**
   * 异常信息
   */
  errorMsg = ''
  /**
   * 异常信息
   */
  isNewScheme = false

  /**
   * 证书模板id
   */
  certificateTemplateId = ''
  /**
   * 是否成果同步
   */
  needDataSync = false

  /**
   * 来源系统-服务商ID
   * @description 分销业务-批量分销商品授权使用
   */
  resourceServicerId = ''
  /**
   * 来源系统-单位ID
   * @description 分销业务-批量分销商品授权使用
   */
  resourceUnitId = ''
  /**
   * 是否已授权
   * @description 分销业务-批量分销商品授权使用
   */
  isAuthorize = false

  /**
   * 是否展示在专题门户
   */
  isShowSpecialTopic = false

  /**
   * 销售渠道对应门户展示范围
   */
  saleChannelShowRange: Array<SaleChanelShowRange> = new Array<SaleChanelShowRange>()

  /**
   * 获取商品业务对象
   */
  getDoCommodity(): MutationTrainClassCommodityClass {
    const mutationClass = TrainClassManagerModule.mutationTrainClassFactory.getMutationTrainClassCommodityClass()
    mutationClass.schemeId = this.schemeId
    mutationClass.commoditySkuId = this.commoditySkuId
    return mutationClass
  }

  /**
   * 根据自身数据填充专题销售渠道显示范围
   */
  fileSaleChannelShowRange() {
    // 处理销售渠道信息
    if (this.commoditySaleChannel?.length) {
      this.commoditySaleChannel.map((saleChannelDto) => {
        {
          if (saleChannelDto.available) {
            const showRange = new SaleChanelShowRange()
            showRange.saleChannel = saleChannelDto.saleChannel
            if (saleChannelDto.purchaseSubjectList?.length) {
              showRange.userShowRange = saleChannelDto.purchaseSubjectList?.map((range) => {
                if (range.isShow) {
                  return range.subjectType
                }
              })
            }

            this.saleChannelShowRange.push(showRange)

            if (showRange.saleChannel == SaleChannelEnum.topic) {
              this.isShowSpecialTopic = true
            }
          }
        }
      })
    }
  }

  /**
   * 专题销售渠道显示范围
   */
  get specialTopicShowRange(): Array<string> {
    const topicRange = this.saleChannelShowRange.find((it) => it.saleChannel == SaleChannelEnum.topic)
    let topicRangeShowList = new Array<ShowUserRangeEnum>()
    if (topicRange) {
      topicRangeShowList = topicRange.userShowRange
    }

    return topicRangeShowList.map((item) => {
      return ShowUserRange.map.get(item)
    })
  }

  /**
   * 【超管】查询当前方案下的期别列表
   * @description 适用页面：
   * 培训管理-培训方案管理-培训方案管理-面网授班/面授班tab-查看期别
   * @param schemeId 方案id
   * @returns 期别列表
   */
  async queryIssueList(schemeId = this.schemeId): Promise<IssueConfigDetail[]> {
    let result: IssueConfigDetail[] = []
    const page = new Page(1, 1)
    const { status, data } =
      await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryBackstage.pageSchemeIssueConfigListInServicer({
        page,
        request: {
          schemeIdList: [schemeId]
        }
      })
    if (status && status.isSuccess() && data && data.currentPageData && data.currentPageData.length) {
      const issueConfig = data.currentPageData[0].issueConfig
      if (issueConfig && issueConfig.length) {
        result = issueConfig.map((dto) => {
          const vo = new IssueConfigDetail()
          // 学时待确定
          vo.id = dto.issueId
          vo.issueNo = dto.issueNum
          vo.issueName = dto.issueName
          vo.isShowInPortal = dto.portalDisplay
          vo.trainingDateRange.dateRange = [dto.startTrainTime, dto.endTrainTime]
          vo.registerBeginTime = dto.startSignUpTime
          vo.registerEndTime = dto.endSignUpTime
          vo.periods = dto.periods
          vo.isEnableStudentEnroll = dto.openSignUp
          return vo
        })
      }
    }
    return result
  }

  /**
   * 【分销商】查询当前方案下的期别列表
   * @param schemeId 方案id
   */
  async queryIssueListInDistributor(schemeId = this.schemeId): Promise<IssueConfigDetail[]> {
    let result: IssueConfigDetail[] = []
    const page = new Page(1, 1)
    const { status, data } =
      await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryBackstage.pageSchemeIssueConfigListInDistributor({
        page,
        request: {
          schemeIdList: [schemeId]
        }
      })
    if (status && status.isSuccess() && data && data.currentPageData && data.currentPageData.length) {
      const issueConfig = data.currentPageData[0].issueConfig
      if (issueConfig && issueConfig.length) {
        result = issueConfig.map((dto) => {
          const vo = new IssueConfigDetail()
          // 学时待确定
          vo.id = dto.issueId
          vo.issueNo = dto.issueNum
          vo.issueName = dto.issueName
          vo.isShowInPortal = dto.portalDisplay
          vo.trainingDateRange.dateRange = [dto.startTrainTime, dto.endTrainTime]
          vo.registerBeginTime = dto.startSignUpTime
          vo.registerEndTime = dto.endSignUpTime
          vo.periods = dto.periods
          vo.isEnableStudentEnroll = dto.openSignUp
          return vo
        })
      }
    }
    return result
  }

  /**
   * 更新专题销售渠道
   * @param param
   * @param param.topicId 专题id
   * @param param.isShowTopic 是否展示在专题
   * @param param.range 展示范围
   */
  async updateTopicSaleChannelRange(param: {
    topicId: string
    isShowTopic: boolean
    range: Array<ShowUserRangeEnum>
  }): Promise<ResponseStatus> {
    const request = new UpdateSaleSettingRequest()
    request.showPortal = param.isShowTopic
    request.schemeId = this.schemeId
    request.showObjectTypes = param.range
    const res = await PlatformTrainingChannel.updateSaleSetting(request)

    if (res?.data && res?.data.code == '200') {
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  // endregion
}
export default TrainClassCommodityVo
