import CourseListDetail from '@api/service/customer/course/query/vo/CourseListDetail'

/**
 * @description 课程
 */
class CourseVo {
  /**
   * 课程id
   */
  id: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 主讲人
   */
  teacher: string
  /**
   * 学时
   */
  period: number
  /**
   * 封面图片
   */
  coverImage: string

  static from(response: CourseListDetail) {
    const course = new CourseVo()
    course.id = response.id
    course.name = response.name
    course.coverImage = response.coverImage
    course.period = response.period
    course.teacher =
      Array.from(response.teachers)
        ?.map(teacher => teacher.name)
        .join('、') || ''
    return course
  }
}

export default CourseVo
