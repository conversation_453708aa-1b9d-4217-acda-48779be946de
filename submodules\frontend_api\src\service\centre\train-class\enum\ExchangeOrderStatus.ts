/**
 * @description 换货单状态枚举
 */
import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum ExchangeOrderStatusEnum {
  // 1：申请换货
  Apply_Exchange = 1,
  // 2：退货处理中
  Returning,
  // 3：退货失败
  Return_Fail,
  // 4：申请发货
  Apply_Delivery,
  // 5：发货处理中
  Delivery_Processing,
  // 6：换货完成
  Complete_Exchange,
  // 7：申请关闭
  Cancel_Exchange
}

/**
 * @description 换货单状态枚举
 */

class ExchangeOrderStatus extends AbstractEnum<ExchangeOrderStatusEnum> {
  static enum = ExchangeOrderStatusEnum
  constructor(status?: ExchangeOrderStatusEnum) {
    super()
    this.current = status
    this.map.set(ExchangeOrderStatusEnum.Apply_Exchange, '申请换货')
    this.map.set(ExchangeOrderStatusEnum.Returning, '退货处理中')
    this.map.set(ExchangeOrderStatusEnum.Return_Fail, '退货失败')
    this.map.set(ExchangeOrderStatusEnum.Apply_Delivery, '申请发货')
    this.map.set(ExchangeOrderStatusEnum.Delivery_Processing, '发货处理中')
    this.map.set(ExchangeOrderStatusEnum.Complete_Exchange, '换货完成')
    this.map.set(ExchangeOrderStatusEnum.Cancel_Exchange, '申请关闭')
  }
}

export default new ExchangeOrderStatus()
