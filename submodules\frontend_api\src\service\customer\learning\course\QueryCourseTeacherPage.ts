import { UiPage } from '@hbfe/common'
import CourseTeacherDetail from '@api/service/customer/learning/course/vo/CourseTeacherDetail'

class QueryCourseEvaluatePage {
  constructor(courseId: string) {
    this.courseId = courseId
  }

  courseId: string
  page: UiPage
  list: Array<CourseTeacherDetail> = new Array<CourseTeacherDetail>()

  async query() {
    console.log(this.page)
    return {}
  }
}

export default QueryCourseEvaluatePage
