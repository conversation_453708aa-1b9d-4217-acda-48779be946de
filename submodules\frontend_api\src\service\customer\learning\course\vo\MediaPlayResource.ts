import {
  CoursewareMediaPlayConfigResponse,
  CoursewareMediaPlayResourceResponse,
  CoursewareMediaPlayRuleResponse,
  CoursewareOutlinePlayResourceResponse,
  DocumentPlayResourceResponse,
  LecturePlayResourceResponse,
  VideoCaptionPlayResourceResponse,
  VideoChapterPlayResourceResponse,
  VideoPlayResourceResponse,
  VideoResourceResponse
} from '@api/ms-gateway/ms-course-play-resource-v1'
import PlayResource from '@api/service/customer/learning/course/vo/PlayResource'
import ResourceTypeEnum from '@api/service/customer/learning/course/enums/ResourceTypeEnum'
import { frontend } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import MediaTypeEnum from '@api/service/customer/learning/course/enums/MediaTypeEnum'

class MediaPlayResource {
  // 支持的资源类型集合
  static supportResourceTypeList = [
    ResourceTypeEnum.type_huawei_cloud_video,
    ResourceTypeEnum.type_huawei_cloud_audio,
    ResourceTypeEnum.type_external_link
  ]
  /**
   * 课件媒体资源ID
   */
  id: string

  /**
   * 课件媒体资源类型
   */
  type: number

  /**
   * 媒体时长
   */
  timeLength: number

  /**
   * 课件目录集合
   */
  outlineList: Array<CoursewareOutlinePlayResourceResponse>

  /**
   * 文档播放资源
   */
  documentList: Array<DocumentPlayResourceResponse>

  /**
   * 视频信息
   */
  videoPlayResourceList: Array<VideoPlayResourceResponse>

  /**
   * 视频字幕
   */
  videoCaptionList: Array<VideoCaptionPlayResourceResponse>

  /**
   * 视频转码信息列表
   */
  videoTranscodingList: Array<VideoResourceResponse> = new Array<VideoResourceResponse>()

  /**
   * 视频讲义列表
   */
  lectureList: Array<LecturePlayResourceResponse>

  /**
   * 视频章节列表
   */
  videoChapterList: Array<VideoChapterPlayResourceResponse>

  /**
   * 播放配置
   */
  playConfig: CoursewareMediaPlayConfigResponse

  /**
   * 播放规则
   */
  playRule: CoursewareMediaPlayRuleResponse

  /**
   * 判断是否是 video
   */
  isVideo?() {
    return this.type === MediaTypeEnum.video || this.type === MediaTypeEnum.normal_media
  }

  isDocument?() {
    return this.type === MediaTypeEnum.document
  }

  /**
   * 获取文档播放资源
   */
  getDocumentPlayResource() {
    return `${location.origin}${this.documentList[0].path}`
  }

  /**
   * 判断当前的播放资源是否在支持范围内
   */
  hasSupportResources() {
    if (this.isVideo()) {
      return !!this.getVideoPlayResource(MediaPlayResource.supportResourceTypeList).length
    }
    return !!this.documentList?.length
  }

  /**
   * 获取播放资源
   * @param types
   */
  getVideoPlayResource?(
    types: Array<ResourceTypeEnum> = [ResourceTypeEnum.type_huawei_cloud_video, ResourceTypeEnum.type_external_link]
  ): Array<PlayResource> {
    let sourceList: Array<VideoResourceResponse> = this.videoTranscodingList
    if (types.length) {
      if (!this.videoTranscodingList) {
        throw new Error('没有播放资源')
      }
      sourceList = this.videoTranscodingList.filter((resource: VideoResourceResponse) =>
        types.includes(resource.resourceType)
      )
    }
    return sourceList.map(PlayResource.from).sort((i, a) => i.quality - a.quality)
  }

  /**
   * 判断是否又音频内容
   */
  hasAudio() {
    return !!this.videoTranscodingList?.filter(
      (resource: VideoResourceResponse) => resource.resourceType === ResourceTypeEnum.type_huawei_cloud_audio
    ).length
  }

  /**
   * 类型转换
   * @param remoteResponse
   */
  static from(remoteResponse: CoursewareMediaPlayResourceResponse) {
    const mediaPlayResource = new MediaPlayResource()
    mediaPlayResource.id = remoteResponse.id
    mediaPlayResource.type = remoteResponse.type
    mediaPlayResource.timeLength = remoteResponse.timeLength
    mediaPlayResource.outlineList = remoteResponse.outlineList
    mediaPlayResource.documentList = remoteResponse.documentList
    mediaPlayResource.videoPlayResourceList = remoteResponse.videoPlayResourceList
    mediaPlayResource.videoCaptionList = remoteResponse.videoCaptionList
    mediaPlayResource.videoTranscodingList = remoteResponse.videoTranscodingList
    mediaPlayResource.lectureList = remoteResponse.lectureList
    mediaPlayResource.videoChapterList = remoteResponse.videoChapterList
    mediaPlayResource.playConfig = remoteResponse.playConfig
    mediaPlayResource.playRule = remoteResponse.playRule
    return mediaPlayResource
  }
}

export default MediaPlayResource
