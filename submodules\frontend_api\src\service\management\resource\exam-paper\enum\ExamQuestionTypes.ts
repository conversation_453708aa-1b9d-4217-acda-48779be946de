/**
 * 试题题型
 */

import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 评定方式类型
 * 填空题 3  简答题 5 父子题 6
 */
export enum QuestionTypes {
  'RadioQuestion' = 1,
  'MultipleQuestion' = 2,
  'OpinionQuestion' = 4
}

class ExamQuestionTypes extends AbstractEnum<QuestionTypes> {
  static enum = QuestionTypes

  constructor(status?: QuestionTypes) {
    super()
    this.current = status
    this.map.set(QuestionTypes.RadioQuestion, '单选')
    this.map.set(QuestionTypes.MultipleQuestion, '多选')
    this.map.set(QuestionTypes.OpinionQuestion, '判断')
  }
}

export default new ExamQuestionTypes()
