"""独立部署的微服务,K8S服务名:ms-basicdata-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""功能描述：项目级-根据code和业务数据字典类型查询单个业务数据字典信息接口-明细接口
		描述：根据code和业务数据字典类型查询当前子项目下的业务数据字典信息
		@param request :业务数据字典查询条件
		@return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse
		@date : 2022年8月12日 15:56:50
	"""
	getBusinessDataDictionaryInSubProject(request:BusinessDataDictionaryCodeRequest):BusinessDataDictionaryResponse @optionalLogin
	"""功能描述：项目级-根据id集合查询字典信息-列表接口
		描述：查询指定id的字典列表，默认按排序字段升序排
		@param idList :字典id集合
		@return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse>
		@date : 2024年1月24日 09:35:49
	"""
	listBusinessDataDictionaryByIdInSubProject(idList:[String]):[BusinessDataDictionaryResponse] @optionalLogin
	"""功能描述：项目级-查询指定业务数据字典类型的字典信息-列表接口
		描述：查询指定业务数据字典类型的字典列表，默认按排序字段升序排
		@param request :业务数据字典查询条件
		@return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse>
		@date : 2022年8月12日 17:28:02
	"""
	listBusinessDataDictionaryInSubProject(request:BusinessDataDictionaryRequest):[BusinessDataDictionaryResponse] @optionalLogin
	"""功能描述：项目级-查询指定业务数据字典类型的字典信息-分页列表接口
		描述：查询指定业务数据字典类型的字典分页列表，默认按排序字段升序排
		@param page    :分页对象
		@param request :业务数据字典查询条件
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse>
		@date : 2022年8月12日 17:28:02
	"""
	pageBusinessDataDictionaryInSubProject(page:Page,request:BusinessDataDictionaryRequest):BusinessDataDictionaryResponsePage @page(for:"BusinessDataDictionaryResponse") @optionalLogin
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""@Description 获取单个业务数据字典信息的查询
	<AUTHOR>
	@Date 2022/9/27 9:33
	@Version 1.0
"""
input BusinessDataDictionaryCodeRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.BusinessDataDictionaryCodeRequest") {
	"""字典编码"""
	code:Int
	"""业务数据字典类型（必填项）
		DEGREE("学位", "DEGREE"),
		EDUCATION_BACKGROUND("学历", "EDUCATION_BACKGROUND"),
		ENTERPRISE_ECONOMIC_TYPE("企业经济类型", "ENTERPRISE_ECONOMIC_TYPE"),
		ENTERPRISE_TYPE("企业类型", "ENTERPRISE_TYPE"),
		EXECUTIVES_UNIT_TYPE("主管单位类型", "EXECUTIVES_UNIT_TYPE"),
		GENDER("性别", "GENDER"),
		HOUSEHOLD_REGISTRATION_TYPE("户口性质", "HOUSEHOLD_REGISTRATION_TYPE"),
		ID_CARD_TYPE("证件类型", "ID_CARD_TYPE"),
		INDUSTRY_EXECUTIVES_TYPE("行业主管类型", "INDUSTRY_EXECUTIVES_TYPE"),
		INDUSTRY_TYPE("行业类型", "INDUSTRY_TYPE"),
		NATIONALITY("民族", "NATIONALITY"),
		PERSON_TYPE("人员类型", "PERSON_TYPE"),
		PERSON_TYPE_GROUP("人员类型分组", "PERSON_TYPE_GROUP"),
		POLITICS_STATUS("政治面貌", "POLITICS_STATUS"),
		USER_REGISTER_TYPE("注册方式", "USER_REGISTER_TYPE"),
		USER_SOURCE_TYPE("注册来源", "USER_SOURCE_TYPE");
		@see BusinessDataDictionaryTypeEnum
	"""
	businessDataDictionaryType:String
}
"""业务数据字典查询"""
input BusinessDataDictionaryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.BusinessDataDictionaryRequest") {
	"""字典编码"""
	code:Int
	"""字典编码扩展"""
	codeExt:String
	"""业务数据字典id集合"""
	idList:[String]
	"""父级业务数据字典id"""
	parentId:String
	"""业务数据字典类型（必填项）"""
	businessDataDictionaryType:String
	"""业务数据字典业务配置id"""
	businessId:String
	"""字典名称"""
	name:String
	"""字典名称 匹配方式
		1:模糊查询   2:后缀查询   3:前缀查询 0:精确匹配  tips:未传参默认为精确匹配
		(1:*name*   2:*name     3:name*)
	"""
	nameMatchType:Int
	"""是否可用（0：禁用 1：启用）"""
	available:Int
	"""排序规则"""
	sortList:[BusinessDictionarySortKParam]
}
input BusinessDictionarySortKParam @type(value:"com.fjhb.ms.basicdata.query.kernel.service.param.dictionary.nested.BusinessDictionarySortKParam") {
	"""排序字段"""
	sortField:BusinessDictionarySortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
enum SortTypeEnum @type(value:"com.fjhb.ms.basicdata.enums.SortTypeEnum") {
	ASC
	DESC
}
"""功能描述：业务数据字典信息
	@Author： wtl
	@Date： 2022年8月12日 17:25:23
"""
type BusinessDataDictionaryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.response.dictionary.BusinessDataDictionaryResponse") {
	"""字典id"""
	id:String
	"""字典类型"""
	type:String
	"""父级字典id"""
	parentId:String
	"""字典编码"""
	code:Int
	"""字典编码扩展"""
	codeExt:String
	"""字典名称"""
	name:String
	"""是否可用（0：禁用 1：启用）"""
	available:Int
	"""排序"""
	sort:Int
	"""创建时间"""
	createTime:String
}
"""业务字典排序字段"""
enum BusinessDictionarySortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.BusinessDictionarySortFieldEnum") {
	"""创建时间"""
	createdTime
	"""排序字段"""
	sort
}

scalar List
type BusinessDataDictionaryResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BusinessDataDictionaryResponse]}
