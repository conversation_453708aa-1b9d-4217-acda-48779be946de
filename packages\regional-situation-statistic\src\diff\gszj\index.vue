<route-meta>
{
"isMenu": true,
"title": "地区学情统计",
"sort": 4,
"icon": "icon-ribaotongji"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="doSearch,@BizPortalSelectBiz,@DistributorSelect">
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
        <hb-search-wrapper @reset="resetQueryParam" class="m-query is-border-bottom">
          <el-form-item label="单位所在地区">
            <biz-national-region
              ref="regionValueRef"
              v-model="locationUnit"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-national-region>
          </el-form-item>
          <el-form-item label="报名时间">
            <double-date-picker
              :begin-create-time.sync="learningSituationStatisticsRequest.registerTimeStart"
              :end-create-time.sync="learningSituationStatisticsRequest.registerTimeEnd"
              beginTimePlaceholder="请选择报名成功时间"
              endTimePlaceholder="请选择报名成功时间"
              endDefaultTime="23:59:59"
            ></double-date-picker>
          </el-form-item>
          <el-form-item label="年度">
            <biz-year-select
              placeholder="请选择培训年度"
              v-model="learningSituationStatisticsRequest.trainingYear"
              :multiple="true"
            ></biz-year-select>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="doSearch()">查询</el-button>
            <el-button v-if="$hasPermission('export')" desc="导出" actions="exportListData" @click="exportListData"
              >导出列表数据</el-button
            >
          </template>
        </hb-search-wrapper>

        <!--操作栏-->
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert f-clear">
            <div class="f-fr f-csp f-flex f-align-center" @click="dialog8 = true">
              <i class="el-icon-info f-f16 f-mr5"></i>统计口径说明
            </div>
          </el-alert>
        </div>
        <!--表格-->

        <el-table
          stripe
          :data="tableList"
          row-key="id"
          lazy
          border
          v-loading="loading"
          element-loading-text="加载中..."
          max-height="500px"
          show-summary
          class="m-table is-statistical f-mt10"
          ref="schemeTable"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="区县" align="center" prop="name" min-width="100"> </el-table-column>
          <el-table-column label="公需课" header-align="center">
            <el-table-column label="报名人数" min-width="90" align="right">
              <template slot-scope="scope">{{ scope.row.publicLearningStatistics.registerCount }}</template>
            </el-table-column>
            <el-table-column label="合格人数" min-width="90" align="right">
              <template slot-scope="scope">{{ scope.row.publicLearningStatistics.qualifiedCount }}</template>
            </el-table-column>
            <el-table-column label="合格率" min-width="90" align="right">
              <template slot-scope="scope">{{ scope.row.publicLearningStatistics.qualifiedRate }}</template>
            </el-table-column>
            <el-table-column label="未完成人数" min-width="100" align="right">
              <template slot-scope="scope">{{ scope.row.publicLearningStatistics.notQualifiedCount }}</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="专业课" header-align="center">
            <el-table-column label="报名人数" min-width="90" align="right">
              <template slot-scope="scope">{{ scope.row.professionalLearningStatistics.registerCount }}</template>
            </el-table-column>
            <el-table-column label="合格人数" min-width="90" align="right">
              <template slot-scope="scope">{{ scope.row.professionalLearningStatistics.qualifiedCount }}</template>
            </el-table-column>
            <el-table-column label="合格率" min-width="90" align="right">
              <template slot-scope="scope">{{ scope.row.professionalLearningStatistics.qualifiedRate }}</template>
            </el-table-column>
            <el-table-column label="未完成人数" min-width="100" align="right">
              <template slot-scope="scope">{{ scope.row.professionalLearningStatistics.notQualifiedCount }}</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="公需+专业" header-align="center">
            <el-table-column label="报名人数" min-width="90" align="right">
              <template slot-scope="scope">{{
                scope.row.publicAndProfessionalLearningStatistics.registerCount
              }}</template>
            </el-table-column>
            <el-table-column label="合格人数" min-width="90" align="right">
              <template slot-scope="scope">{{
                scope.row.publicAndProfessionalLearningStatistics.qualifiedCount
              }}</template>
            </el-table-column>
            <el-table-column label="合格率" min-width="90" align="right">
              <template slot-scope="scope">{{
                scope.row.publicAndProfessionalLearningStatistics.qualifiedRate
              }}</template>
            </el-table-column>
            <el-table-column label="未完成人数" min-width="100" align="right">
              <template slot-scope="scope">{{
                scope.row.publicAndProfessionalLearningStatistics.notQualifiedCount
              }}</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="补学" header-align="center">
            <el-table-column label="公需课人数" min-width="100" align="right">
              <template slot-scope="scope">{{ scope.row.supplementStatistics.publicSupplementCount }}</template>
            </el-table-column>
            <el-table-column label="公需课合格人数" min-width="120" align="right">
              <template slot-scope="scope">{{
                scope.row.supplementStatistics.publicSupplementQualifiedCount
              }}</template>
            </el-table-column>
            <el-table-column label="专业课人数" min-width="100" align="right">
              <template slot-scope="scope">{{ scope.row.supplementStatistics.professionalSupplementCount }}</template>
            </el-table-column>
            <el-table-column label="专业课合格人数" min-width="120" align="right">
              <template slot-scope="scope">{{
                scope.row.supplementStatistics.professionalSupplementQualifiedCount
              }}</template>
            </el-table-column>
          </el-table-column>
        </el-table>
        <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务管理-地区开通统计</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
          </div>
        </el-dialog>

        <el-drawer title="统计口径说明" :visible.sync="dialog8" size="900px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
            </el-alert>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">搜索条件说明</span>
              （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
            </p>
            <el-table stripe :data="searchConditions" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  {{ scope.row.field }}
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  {{ scope.row.description }}
                </template>
              </el-table-column>
            </el-table>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">列表字段及详细说明</span>
              （列表下的数据显示受搜索条件的约束，统计单位：人次）
            </p>
            <el-table stripe :data="listFields" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  {{ scope.row.field }}
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  {{ scope.row.description }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-drawer>
        <!--分页-->
        <!-- <hb-pagination class="f-mt15 f-tr" :page="trainSchemePage" v-bind="trainSchemePage"> </hb-pagination> -->
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src//double-date-picker/index.vue'
  import NewRegionTreeVo from '@hbfe/jxjy-admin-common/src/models/NewRegionTreeVo'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-common/src/models/sku'
  import { RegionResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
  import {
    LearningRegisterRequest,
    LearningReportFormsRequest,
    RegionRequest
  } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import { cloneDeep } from 'lodash'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import QueryShowOffline from '@api/service/common/config/QueryShowOffline'
  import { RegionSituationStatisticsResponseVo } from '@api/service/diff/management/gszj/statisticalReport/query/vo/RegionSituationStatisticsResponseVo'
  import UserModule from '@api/service/management/user/UserModule'
  import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
  import {
    CommoditySkuSortRequest,
    RegionSkuPropertyRequest,
    RegionSkuPropertySearchRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import LearningSituationStatisticsRequestVo from '@api/service/diff/management/gszj/statisticalReport/query/vo/LearningSituationStatisticsRequestVo'
  import { RegionKParam } from '@api/diff-gateway/platform-jxjypxtypt-gszj-school'
  import QueryRegionManagerRegionSituationList from '@api/service/diff/management/gszj/statisticalReport/query/QueryRegionManagerRegionSituationList'

  class NewLearningReportFormsRequest extends LearningReportFormsRequest {
    //是否来源于专题
    subjectType = ''
    //专题名称
    subjectName = ''
  }

  @Component({
    components: {
      DoubleDatePicker,
      BizPortalSelect,
      BizDistributorSelect
    }
  })
  export default class extends Vue {
    @Ref('regionValueRef') regionValueRef: any
    @Ref('schemeTable') schemeTableRef: any
    dialog8 = false
    /**
     * 搜索条件说明
     */
    searchConditions = [
      {
        field: '工作单位所在地区',
        description:
          '默认显示福建省下各地市的数据，如福建省、省直单位、福州市等；选福州市则显示的是福州市各区县数据，如福州市、市直、鼓楼区等'
      },
      { field: '报名成功时间', description: '查看在某个开通时间内，各地区学员学习数据' }
    ]
    /**
     * 字段详细列表
     */
    listFields = [
      {
        field: '区县',
        description:
          '培训方案默认显示各地市的数据，如福建省、省直单位、福州市等；默认显示各地市的数据，如福建省、省直单位、福州市等；'
      },
      {
        field: '公需课',
        description: '统计报名培训方案年度为当前年度往前近五年，且培训方案科目类型为“公需科目”的学员对应的数据。'
      },
      {
        field: '专业课',
        description: '统计报名培训方案年度为当前年度往前近五年，且培训方案科目类型为“专业科目”的学员对应的数据。'
      },
      {
        field: '公需+专业',
        description:
          '统计报名培训方案年度为当前年度往前近五年，且培训方案科目类型为“公需+专业”的学员对应的数据。\n' +
          '注：未开设此类型班级不显示。'
      },
      {
        field: '报名人数',
        description: '统计截止到当前时间扣除退班后的实际有效的报名人次，净报名=未学习+学习中+已学完。'
      },
      {
        field: '合格人数',
        description: '统计截止到当前时间净开通人数中，已达到考核要求的人。'
      },
      {
        field: '合格率',
        description: '统计截止到当前时间净开通人数中的合格率，合格率=已合格/净报名。'
      },
      { field: '未完成人数', description: '统计截止到当前时间净开通人数中，未达到考核要求的人。' },
      {
        field: '补学',
        description: '统计报名补学年度培训方案的学员对应的数据。\n' + '\n' + '注：报名非当前年度的培训方案都算作补学。'
      },
      {
        field: '公需课人数',
        description:
          '统计截止到当前时间扣除退班后，科目类型为“公需科目”培训方案的实际有效的报名人次，净报名=未学习+学习中+已学完。'
      },
      {
        field: '公需课合格人数',
        description: '统计截止到当前时间净开通人数中，科目类型为“公需科目”培训方案已达到考核要求的人。'
      },
      {
        field: '专业课人数',
        description:
          '统计截止到当前时间扣除退班后，科目类型为“专业科目”培训方案的实际有效的报名人次，净报名=未学习+学习中+已学完。'
      },
      {
        field: '专业课合格人数',
        description: '统计截止到当前时间净开通人数中，科目类型为“专业科目”培训方案已达到考核要求的人。'
      },
      {
        field: '公需+专业人数',
        description:
          '统计截止到当前时间扣除退班后，科目类型为“公需+专业”培训方案的实际有效的报名人次，净报名=未学习+学习中+已学完。\n' +
          '\n' +
          '注：未开设此类型班级不显示。'
      },
      {
        field: '公需+专业合格人数',
        description:
          '统计截止到当前时间净开通人数中，科目类型为“公需+专业”培训方案已达到考核要求的人。\n' +
          '\n' +
          '注：未开设此类型班级不显示。'
      }
    ]
    /**
     * 列表加载
     */
    loading = false
    /**
     * 列表入参
     */
    learningSituationStatisticsRequest = new LearningSituationStatisticsRequestVo()
    /**
     * 列表数组
     */
    tableList: RegionSituationStatisticsResponseVo[] = []
    /**
     * 地区学情对象
     */
    queryRegionManagerRegionSituation = new QueryRegionManagerRegionSituationList()
    /**
     * 单位所在地数组
     */
    locationUnit: string[] = []
    //导出成功弹窗
    exportSuccessVisible = false
    async created() {
      await this.doSearch()
    }

    /**
     * 查询
     */
    async doSearch() {
      try {
        this.loading = true
        this.initSearch()
        this.tableList = await this.queryRegionManagerRegionSituation.listRegionLearningReportFormsInServicer(
          this.learningSituationStatisticsRequest
        )
      } catch (e) {
        console.log('加载统计列表失败：', e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 初始化查询数据
     */
    initSearch() {
      const localRegion = cloneDeep(this.locationUnit)
      if (Array.isArray(localRegion) && localRegion.length) {
        const option = new RegionKParam()
        option.province = localRegion.length >= 1 ? localRegion[0] : undefined
        option.city = localRegion.length >= 2 ? localRegion[1] : undefined
        option.county = localRegion.length >= 3 ? localRegion[2] : undefined
        //物理地区改为业务地区
        this.learningSituationStatisticsRequest.regionList.push(option)
      } else {
        this.learningSituationStatisticsRequest.regionList = []
      }
    }

    /**
     * 导出列表数据
     */
    async exportListData() {
      try {
        this.initSearch()
        const res = await this.queryRegionManagerRegionSituation.exportExcel(this.learningSituationStatisticsRequest)
        if (res.status.code == 200 && res.data) {
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        //console.log(e)
      } finally {
        //todo
      }
    }

    /**
     * 前往下载
     */
    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportRegionOpenStatistical' }
      })
    }
    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      this.locationUnit = []
      this.learningSituationStatisticsRequest = new LearningSituationStatisticsRequestVo()
      await this.doSearch()
    }
    // getSummaries(param: any) {
    //   const { columns, data } = param
    //   const sums: any[] = []
    //
    //   // 第一列显示“合计”
    //   sums[0] = '合计'
    //
    //   // 初始化其他列为 0
    //   const totalPublicRegister = data.reduce(
    //     (sum, item) => sum + (item.publicLearningStatistics?.registerCount || 0),
    //     0
    //   )
    //   const totalPublicQualified = data.reduce(
    //     (sum, item) => sum + (item.publicLearningStatistics?.qualifiedCount || 0),
    //     0
    //   )
    //
    //   const totalProRegister = data.reduce(
    //     (sum, item) => sum + (item.professionalLearningStatistics?.registerCount || 0),
    //     0
    //   )
    //   const totalProQualified = data.reduce(
    //     (sum, item) => sum + (item.professionalLearningStatistics?.qualifiedCount || 0),
    //     0
    //   )
    //
    //   const totalBothRegister = data.reduce(
    //     (sum, item) => sum + (item.publicAndProfessionalLearningStatistics?.registerCount || 0),
    //     0
    //   )
    //   const totalBothQualified = data.reduce(
    //     (sum, item) => sum + (item.publicAndProfessionalLearningStatistics?.qualifiedCount || 0),
    //     0
    //   )
    //
    //   const totalSupplementPublic = data.reduce(
    //     (sum, item) => sum + (item.supplementStatistics?.publicSupplementCount || 0),
    //     0
    //   )
    //   const totalSupplementPublicQualified = data.reduce(
    //     (sum, item) => sum + (item.supplementStatistics?.publicSupplementQualifiedCount || 0),
    //     0
    //   )
    //
    //   const totalSupplementPro = data.reduce(
    //     (sum, item) => sum + (item.supplementStatistics?.professionalSupplementCount || 0),
    //     0
    //   )
    //   const totalSupplementProQualified = data.reduce(
    //     (sum, item) => sum + (item.supplementStatistics?.professionalSupplementQualifiedCount || 0),
    //     0
    //   )
    //
    //   // 匹配每列的位置来设置对应的合计值
    //   columns.forEach((column: any, index: number) => {
    //     switch (column.property) {
    //       case 'publicLearningStatistics.registerCount':
    //         sums[index] = totalPublicRegister
    //         break
    //       case 'publicLearningStatistics.qualifiedCount':
    //         sums[index] = totalPublicQualified
    //         break
    //       case 'publicLearningStatistics.qualifiedRate':
    //         sums[index] = this.formatRate(totalPublicQualified / totalPublicRegister)
    //         break
    //       case 'publicLearningStatistics.notQualifiedCount':
    //         sums[index] = totalPublicRegister - totalPublicQualified
    //         break
    //
    //       case 'professionalLearningStatistics.registerCount':
    //         sums[index] = totalProRegister
    //         break
    //       case 'professionalLearningStatistics.qualifiedCount':
    //         sums[index] = totalProQualified
    //         break
    //       case 'professionalLearningStatistics.qualifiedRate':
    //         sums[index] = this.formatRate(totalProQualified / totalProRegister)
    //         break
    //       case 'professionalLearningStatistics.notQualifiedCount':
    //         sums[index] = totalProRegister - totalProQualified
    //         break
    //
    //       case 'publicAndProfessionalLearningStatistics.registerCount':
    //         sums[index] = totalBothRegister
    //         break
    //       case 'publicAndProfessionalLearningStatistics.qualifiedCount':
    //         sums[index] = totalBothQualified
    //         break
    //       case 'publicAndProfessionalLearningStatistics.qualifiedRate':
    //         sums[index] = this.formatRate(totalBothQualified / totalBothRegister)
    //         break
    //       case 'publicAndProfessionalLearningStatistics.notQualifiedCount':
    //         sums[index] = totalBothRegister - totalBothQualified
    //         break
    //
    //       case 'supplementStatistics.publicSupplementCount':
    //         sums[index] = totalSupplementPublic
    //         break
    //       case 'supplementStatistics.publicSupplementQualifiedCount':
    //         sums[index] = totalSupplementPublicQualified
    //         break
    //       case 'supplementStatistics.professionalSupplementCount':
    //         sums[index] = totalSupplementPro
    //         break
    //       case 'supplementStatistics.professionalSupplementQualifiedCount':
    //         sums[index] = totalSupplementProQualified
    //         break
    //
    //       default:
    //         if (index > 0) {
    //           sums[index] = ''
    //         }
    //         break
    //     }
    //   })
    //
    //   return sums
    // }
  }
</script>
