import UserInfoVo from '@api/service/customer/user/query/vo/UserInfoVo'
import { ResponseStatus } from '@hbfe/common'
import BasicDataQueryForestage, {
  StudentInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

/**
 * 用户信息
 */
class QueryUserInfo {
  /**
   * 用户信息
   */
  userInfo = new StudentInfoResponse()
  /**
   * 校验是否完善信息
   * @returns
   */
  async checkUserInfoPerfect() {
    await this.getUserInfo()
    if (!this.userInfo.userInfo.idCard) {
      return false
    }
    if (!this.userInfo.userInfo.userName) {
      return false
    }
    if (this.userInfo.userInfo.gender === -1) {
      return false
    }
    // 公司名称是否完善
    if (!this.userInfo.userInfo.companyName) {
      return false
    }
    return true
  }

  /**
   * 获取用户信息
   * @returns ResponseStatus
   */
  private async getUserInfo(): Promise<ResponseStatus> {
    const { status, data } = await BasicDataQueryForestage.getStudentInfoInMyself()

    if (status.isSuccess()) {
      this.userInfo = data
    }
    return status
  }
}

export default new QueryUserInfo()
