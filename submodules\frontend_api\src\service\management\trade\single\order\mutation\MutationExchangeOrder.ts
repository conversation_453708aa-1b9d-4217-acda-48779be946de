import msExchange from '@api/ms-gateway/ms-exchange-order-v1'
import { Response } from '@hbfe/common'
import { CalReturnOrderUtil } from '@api/service/management/trade/single/order/utils/CalReturnOrderUtil'
export default class MutationExchangeOrder {
  //换货单号
  exchangeOrderNo = ''
  //备注
  note = ''
  /*
   *  同意换货
   * */
  async agreeApply() {
    const res = await msExchange.agreeExchangeApply({
      exchangeOrderNo: this.exchangeOrderNo,
      approveComment: this.note
    })
    return this.filterRes(res).status
  }
  /*
   *  继续退货
   * */
  async retryRecycleResouce() {
    const res = await msExchange.retryRecycleResouce({
      exchangeOrderNo: this.exchangeOrderNo
    })
    return this.filterRes(res).status
  }
  /*
   *  继续发货
   * */
  async retryDelivery() {
    const res = await msExchange.retryDelivery({
      exchangeOrderNo: this.exchangeOrderNo
    })
    return this.filterRes(res).status
  }
  /*
   *  取消换货申请
   * */
  async cancelApply() {
    const res = await msExchange.sellerCancelExchangeApply({
      exchangeOrderNo: this.exchangeOrderNo,
      cancelReason: this.note
    })
    return this.filterRes(res).status
  }
  /*
   *  拒绝换货申请
   * */
  async rejectApply() {
    const res = await msExchange.rejectExchangeApply({
      exchangeOrderNo: this.exchangeOrderNo,
      approveComment: this.note
    })
    return this.filterRes(res).status
  }

  filterRes(res: Response<any>) {
    return CalReturnOrderUtil.filterRes(res)
  }
}
