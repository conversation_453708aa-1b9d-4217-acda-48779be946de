import {
  Course<PERSON>hapter,
  CourseDetailResponse,
  CourseOutline
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import Chapter from '@api/service/management/resource/course/mutation/vo/Chapter'
import Courseware from '@api/service/management/resource/course/mutation/vo/Courseware'
import AbstractOperateCourse from '@api/service/management/resource/course/mutation/vo/AbstractOperateCourse'
import { CourseOutlineWithSubOutlineRequest } from '@api/ms-gateway/ms-course-resource-v1'

class UpdateCourse extends AbstractOperateCourse {
  outlines: Array<CourseOutline>

  get showCategoryIdList(): Array<string> {
    return this._showCategoryIdList
  }

  id: string
  // 分类显示 id 集合， 用来前端做回显用的
  private _showCategoryIdList: Array<string> = new Array<string>()

  static from(responseDetail: CourseDetailResponse) {
    const detail = new UpdateCourse()
    detail.id = responseDetail.id
    detail.name = responseDetail.name
    detail.picture = responseDetail.iconPath
    if (responseDetail.categorys && responseDetail.categorys.length) {
      detail.categoryId = responseDetail.categorys[0].id
    }
    detail.supplierId = responseDetail.supplierId
    detail.description = responseDetail.aboutsContent
    detail.outlines = responseDetail.courseOutlines
    detail.chapters = responseDetail.courseOutlines
      .filter((c: CourseOutline) => c.parentId === '-1')
      .map((outline: CourseOutline) => {
        const chapter = Chapter.from(outline)
        const courseChapters = responseDetail.courseChapters.filter((chapter: CourseChapter) => {
          if (chapter.courseOutline.parentId === '-1') {
            return chapter.courseOutline.id === outline.id
          }
          chapter.name = chapter.courseOutline.name
          return chapter.courseOutline.parentId === outline.id
        })
        if (courseChapters) {
          chapter.coursewares = courseChapters.map(Courseware.from)
        }
        chapter.coursewares.sort((a, b) => a.sort - b.sort)
        return chapter
      })
    return detail
  }
}

export default UpdateCourse
