const fs = require('fs-extra')
const path = require('path')
const GitInfo = require('./models/GitInfo')
const SentryInfo = require('./models/SentryInfo')

const cacheDir = path.resolve(process.cwd(), './.cache')

/**
 * 根据名称从 cache 目录读取文件内容
 * @param name
 * @return {{}|any}
 */
const readFile = (name) => {
  try {
    const fsContent = fs.readFileSync(path.join(cacheDir, name)).toString()
    return JSON.parse(fsContent)
  } catch (e) {
    return {}
  }
}

/**
 * 从 git-info.json 路径获取 git 信息
 * @return {GitInfo}
 */
const getGitInfoFromPath = () => {
  const gitInfo = new GitInfo()
  const fsJsonContent = readFile('git-info.json')
  Object.assign(gitInfo, fsJsonContent)
  return gitInfo
}

/**
 * 从 sentry-info.json 路径获取 sentry 相关信息
 * @return {SentryInfo}
 */
const getSentryInfoFromPath = () => {
  const gitInfo = new SentryInfo()
  const fsJsonContent = readFile('sentry-info.json')
  Object.assign(gitInfo, fsJsonContent)
  return gitInfo
}

/**
 * 将提供的内容写入到 cache 目录
 * @param name
 * @param content
 */
const writeToCache = (name, content) => {
  fs.mkdirsSync(cacheDir)
  fs.writeFileSync(path.join(cacheDir, name), JSON.stringify(content), { encoding: 'utf-8' })
}

module.exports = {
  getGitInfoFromPath,
  getSentryInfoFromPath,
  writeToCache
}
