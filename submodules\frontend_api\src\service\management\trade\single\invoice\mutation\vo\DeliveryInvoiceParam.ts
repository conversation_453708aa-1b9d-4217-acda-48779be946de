import { ConfirmDeliveryRequest, ConfirmPickupRequest } from '@api/ms-gateway/ms-offlineinvoice-v1'

/*
 * @Description: 配送放票业务参数
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-06 09:04:04
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-07 17:39:46
 */
export default class DeliveryInvoiceParam {
  /**
   * 线下发票编号
   */
  offlineInvoiceId: string
  /**
   * 领取人
   */
  name: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 快递公司名称
   */
  courierName: string
  /**
   * 运单号
   */
  theAwb: string
  static toCourier(deliveryInvoiceParam: DeliveryInvoiceParam) {
    //快递
    const confirmDeliveryRequest = new ConfirmDeliveryRequest()
    confirmDeliveryRequest.offlineInvoiceId = deliveryInvoiceParam.offlineInvoiceId
    confirmDeliveryRequest.expressCompanyName = deliveryInvoiceParam.courierName
    confirmDeliveryRequest.expressNo = deliveryInvoiceParam.theAwb
    return confirmDeliveryRequest
  }
  static toSelffetched(deliveryInvoiceParam: DeliveryInvoiceParam) {
    //自提
    const confirmPickupRequest = new ConfirmPickupRequest()
    confirmPickupRequest.offlineInvoiceId = deliveryInvoiceParam.offlineInvoiceId
    confirmPickupRequest.takePerson = deliveryInvoiceParam.name
    confirmPickupRequest.phone = deliveryInvoiceParam.phone
    return confirmPickupRequest
  }
}
