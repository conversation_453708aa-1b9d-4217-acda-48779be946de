import QueryCourseCategory from '@api/service/management/resource/course-category/query/QueryCourseCategory'
import UpdateCourseCategory from '@api/service/management/resource/course-category/mutation/UpdateCourseCategory'
import AbstractStandardFactory from '@api/service/management/resource/AbstractStandardFactory'
import BizCourseCategory from '@api/service/management/resource/course-category/mutation/BizCourseCategory'
import CreateCourseCategory from '@api/service/management/resource/course-category/mutation/CreateCourseCategory'
import UpdateCourseCategoryVo from '@api/service/management/resource/course-category/mutation/vo/UpdateCourseCategory'

class CourseCategoryFactory extends AbstractStandardFactory<
  QueryCourseCategory,
  BizCourseCategory,
  CreateCourseCategory,
  UpdateCourseCategory
> {
  mutationBiz(id: string): BizCourseCategory {
    return new BizCourseCategory(id)
  }

  mutationCreate(): CreateCourseCategory {
    return new CreateCourseCategory()
  }

  async mutationUpdate(id: string): Promise<UpdateCourseCategory> {
    const detail = await this.query.queryById(id)
    const mutation = new UpdateCourseCategory()
    mutation.updateCourseCategory = UpdateCourseCategoryVo.from(detail)
    return mutation
  }

  query: QueryCourseCategory = new QueryCourseCategory()
}

export default CourseCategoryFactory
