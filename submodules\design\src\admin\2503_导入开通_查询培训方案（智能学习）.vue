<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--查询可报培训方案-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">查询可报培训方案</el-button>
        <el-drawer
          title="培训方案"
          :visible.sync="dialog1"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--显示5个，超出部分隐藏-->
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="8">
                  <el-form-item label="年度">
                    <el-select clearable placeholder="请选择">
                      <el-option label="选项1" value=""></el-option>
                      <el-option label="选项2" value=""></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="地区">
                    <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="培训形式">
                    <el-select clearable placeholder="请选择培训形式">
                      <el-option label="选项1" value=""></el-option>
                      <el-option label="选项2" value=""></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="培训方案类型">
                    <el-select clearable placeholder="请选择">
                      <el-option label="选项1" value=""></el-option>
                      <el-option label="选项2" value=""></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="行业">
                    <el-select v-model="select" clearable placeholder="请选择行业">
                      <el-option value="人社"></el-option>
                      <el-option value="建设"></el-option>
                      <el-option value="职业卫生"></el-option>
                      <el-option value="工勤"></el-option>
                      <el-option value="教师"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="学段">
                    <el-select v-model="select" clearable placeholder="请选择学段">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="学科">
                    <el-select v-model="select" clearable placeholder="请选择学科">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="科目类型">
                    <el-select clearable placeholder="请选择">
                      <el-option label="选项1" value=""></el-option>
                      <el-option label="选项2" value=""></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="培训类别">
                    <el-select clearable placeholder="请选择">
                      <el-option label="选项1" value=""></el-option>
                      <el-option label="选项2" value=""></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="培训专业">
                    <el-select clearable placeholder="请选择">
                      <el-option label="选项1" value=""></el-option>
                      <el-option label="选项2" value=""></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="培训方案名称">
                    <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                    <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                    <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <el-alert type="warning" :closable="false" class="m-alert f-mb10"
              >当前列表仅显示网授培训形式的培训方案。</el-alert
            >
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column label="培训方案名称" min-width="300">
                <template>
                  <p>培训方案名称</p>
                  <p><el-tag type="primary" size="mini">网授-培训班-选课规则</el-tag></p>
                </template>
              </el-table-column>
              <el-table-column label="培训属性" min-width="300">
                <template>
                  <p>行业：行业行业</p>
                  <p>地区：为空，不展示</p>
                  <p>科目类型：科目类型</p>
                  <p>培训类别：培训类别</p>
                  <p>培训专业：培训专业培训专业</p>
                  <p>培训年度：2019</p>
                </template>
              </el-table-column>
              <el-table-column label="报名状态" min-width="140">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="info" class="badge-status">关闭报名</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">开放报名</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="学习起止时间" min-width="220">
                <template>
                  <p>起始时间：2021-10-15 00:21:21</p>
                  <p>结束时间：2021-10-15 00:21:21</p>
                </template>
              </el-table-column>
              <el-table-column label="操作" min-width="220">
                <template>
                  <el-button type="text">查看期别(3)</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
