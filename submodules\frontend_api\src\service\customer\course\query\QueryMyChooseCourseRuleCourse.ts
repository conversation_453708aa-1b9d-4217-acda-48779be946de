import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  CourseInfoRequest,
  CourseLearningSortRequest,
  CourseResponse,
  StudentCourseLearningRequest,
  StudentCourseLearningResponse,
  StudentCourseLearningV2Response,
  StudentCourseLearningV2ResponsePage,
  TeacherResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import { Page, UiPage } from '@hbfe/common'
import QueryMyLearningCoursePageParam from '@api/service/customer/course/query/vo/QueryMyLearningCoursePageParam'
import MyLearningCourse from '@api/service/customer/course/query/vo/MyLearningCourse'
import QueryCourseList from '@api/service/customer/course/query/QueryCourseList'
import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'
import AfterCourseTestLastResult from '@api/service/customer/course/query/vo/AfterCourseTestLastResult'
import Classification from '../../train-class/query/vo/Classification'
import Utils from './utils/index'

class QueryMyChooseCourseRuleCourse {
  private studentNo: string

  constructor(studentNo: string) {
    this.studentNo = studentNo
  }

  private async getCoursePageMap(idList: Array<string>): Promise<Map<string, CourseResponse>> {
    if (!idList.length) return
    const queryCourseList = new QueryCourseList()
    const resourceCourse = await queryCourseList.queryCoursePageByIdList(Array.from(idList))
    const resultMap = new Map<string, CourseResponse>()
    resourceCourse.forEach((course: CourseResponse) => {
      resultMap.set(course.id, course)
    })
    return resultMap
  }

  /**
   * 查询选课规则学习课程分页
   * @param page
   * @param queryParams
   * @param sort
   */
  async queryPage(
    page: Page,
    queryParams: QueryMyLearningCoursePageParam,
    sort?: Array<CourseLearningSortRequest>,
    outlineTree?: Array<Classification>
  ): Promise<Array<MyLearningCourse>> {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneV2InMyself(
      {
        page,
        request: queryParams.to(),
        sort
      }
    )
    page.totalPageSize = result.data.totalPageSize
    page.totalSize = result.data.totalSize
    // 原课程 id 集合
    const resultList = result.data.currentPageData.map((response: StudentCourseLearningV2Response) => {
      return MyLearningCourse.from(response)
    })
    resultList.forEach((learningCourse: MyLearningCourse) => {
      learningCourse.detail.categoryPath = this.getCategoryPath(
        outlineTree,
        learningCourse.courseOfCourseTrainingOutline.outlineId
      )
    })

    return resultList
  }
  /**
   * 查询选课规则学习课程全量
   * @param queryParams
   * @param sort
   */
  async queryAllCourse(
    queryParams: QueryMyLearningCoursePageParam,
    sort?: Array<CourseLearningSortRequest>,
    outlineTree?: Array<Classification>
  ): Promise<Array<MyLearningCourse>> {
    const resultTotal = new Array<StudentCourseLearningV2Response>()
    const pageSign = new Page(1, 1)
    const getTotalRes = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneV2InMyself(
      {
        page: pageSign,
        request: queryParams.to(),
        sort
      }
    )
    const total = getTotalRes?.data?.totalSize || 0
    for (let i = 0; i < total / 200; i++) {
      const pages = new Page()
      pages.pageNo = i + 1
      pages.pageSize = 200
      const totalRes = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneV2InMyself(
        {
          page: pages,
          request: queryParams.to(),
          sort
        }
      )
      resultTotal.push(...totalRes?.data?.currentPageData)
    }
    // const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneInMyself(
    //   {
    //     page,
    //     request: queryParams.to(),
    //     sort
    //   }
    // )
    // page.totalPageSize = result.data.totalPageSize
    // page.totalSize = result.data.totalSize
    // 原课程 id 集合
    const sourceCourseIdList: Set<string> = new Set<string>()
    const resultList = resultTotal?.map((response: StudentCourseLearningV2Response) => {
      response.course.courseId && sourceCourseIdList.add(response.course.courseId)
      return MyLearningCourse.from(response)
    })

    /**
     * 返回课程信息
     */
    const courseMap = await this.getCoursePageMap(Array.from(sourceCourseIdList.values()))
    const teacherIdList: Set<string> = new Set<string>()
    resultList.forEach((learningCourse: MyLearningCourse) => {
      const responseCourse = courseMap.get(learningCourse.detail.id)
      learningCourse.detail.id = responseCourse.id
      learningCourse.detail.name = responseCourse.name
      learningCourse.detail.coverImage = responseCourse.iconPath
      learningCourse.detail.period = responseCourse.period
      learningCourse.detail.categoryPath = this.getCategoryPath(
        outlineTree,
        learningCourse.courseOfCourseTrainingOutline.outlineId
      )
      responseCourse.teacherIds.forEach(item => {
        const vo = new SimpleUserInfo()
        vo.id = item
        learningCourse.detail.teachers.push(vo)
      })
      responseCourse.teacherIds.forEach((id: string) => {
        teacherIdList.add(id)
      })
    })

    // 加载教师信息
    if (teacherIdList.size) {
      const listTeacher = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.listTeacherInServicer(
        Array.from(teacherIdList.values())
      )
      const idMap = new Map<string, TeacherResponse>()
      listTeacher.data.forEach(item => {
        idMap.set(item.id, item)
      })
      resultList.forEach((learningCourse: MyLearningCourse) => {
        learningCourse.detail.teachers.forEach(item => {
          const info = idMap.get(item.id)
          item.name = info.name
          item.avatar = info.photo
          item.description = info.aboutsContent
        })
      })
    }
    return resultList
  }
  /**
   * 获取要求学时
   * @param queryParams
   * @param sort
   */
  async statisticsCourseLearningSceneChooseCourseByOutlineOfInMyself(StudentNo: string) {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.statisticsCourseLearningSceneChooseCourseByOutlineOfInMyself(
      StudentNo
    )
    return result.data
  }

  // * 通过学号课程ID获取评价ID
  async getEvaluateId(studentNo: string, courseId: string) {
    const params = new StudentCourseLearningRequest()
    params.course = new CourseInfoRequest()
    params.studentNo = studentNo
    params.course.courseId = courseId
    console.log('params', params)
    let result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneInMyself(
      {
        page: new Page(1, 1),
        request: params
      }
    )
    if (result.status.code !== 200 || !result.status.isSuccess()) {
      console.error('返回报错', result)
      return Promise.reject(result)
    }
    result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneInMyself(
      {
        page: new Page(1, result.data.totalSize),
        request: params
      }
    )
    if (result.status.code !== 200 || !result.status.isSuccess()) {
      console.error('返回报错', result)
      return Promise.reject(result)
    }
    return result.data.currentPageData.find(res => res.course.courseId === courseId)?.studentCourseAppraised
      ?.studentCourseAppraisalId
  }

  async queryAfterCourseTestLastResult(studentCourseId: string): Promise<AfterCourseTestLastResult> {
    const request = new StudentCourseLearningRequest()
    request.studentNo = this.studentNo
    request.course = {
      courseId: studentCourseId
    }
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneInMyself(
      {
        page,
        request
      }
    )
    if (result.data?.currentPageData?.length) {
      const lastResult = result.data.currentPageData[0]
      return AfterCourseTestLastResult.from(lastResult.studentCourseQuiz)
    }
    return new AfterCourseTestLastResult()
  }

  private getCategoryPath(tree: Array<Classification>, leafOutlineId: string): string {
    const categoryList = Utils.treeFindPath(
      tree,
      (node: Classification) => {
        return node.id === leafOutlineId
      },
      'name',
      'childOutlines'
    )
    return categoryList.length ? categoryList.join(' > ') : ''
  }
}

export default QueryMyChooseCourseRuleCourse
