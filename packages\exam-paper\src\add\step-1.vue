<template>
  <div>
    <el-row type="flex" justify="center" class="width-limit" v-loading="loading">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form ref="form" :rules="rules" label-width="auto" class="m-form f-mt20 f-mb40">
          <el-form-item label="试卷名称：" prop="name">
            <el-input clearable v-model="createExamPaper.name" placeholder="请输入试卷名称" />
          </el-form-item>
          <el-form-item label="组卷方式：" prop="type">
            <el-select
              clearable
              placeholder="请选择组卷方式"
              v-model="createExamPaper.publishPattern.type"
              class="form-m"
            >
              <el-option
                v-for="(item, index) in configTypeList"
                :key="index"
                :label="item.title"
                :value="item.value"
                :disabled="item.disabled"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属试卷分类：" required>
            <paper-classify-tree
              v-model="createExamPaper.paperPublishConfigureCategoryId"
              :defaultNode="paperClassify"
              notOnlyId
              v-if="classifyTree"
              @valName="valName"
              @selectNode="selectNode"
            />
            <el-button type="text" class="f-ml15" @click="toCategory">新建分类</el-button>
          </el-form-item>
          <el-form-item label="出题范围：">
            <el-radio v-model="createExamPaper.publishPattern.questionScopes" :label="1">按题库出题</el-radio>
            <el-radio v-model="createExamPaper.publishPattern.questionScopes" :label="2">按学员课程ID出题</el-radio>
          </el-form-item>
          <el-form-item label="试卷总分："> <span class="f-cr f-f20 f-fb">100</span> 分 </el-form-item>
          <el-form-item label="考试时长：" prop="suggestionTimeLengthByMin">
            <el-input-number
              :min="0"
              :precision="1"
              controls-position="right"
              class="input-num"
              style="width: 200px"
              v-model="createExamPaper.publishPattern.suggestionTimeLengthByMin"
              @input="changeTime($event)"
            />
            <span class="f-ml5">分钟</span>
          </el-form-item>
          <el-form-item label="出题题库：" v-if="createExamPaper.publishPattern.questionScopes !== 2">
            <el-button type="primary" plain @click="openQuestionLibray()">新增题库</el-button>
            <el-table :data="questionLibray" max-height="500px" class="m-table f-mt15" v-loading="tableLoading">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="题库名称" prop="name" min-width="250"></el-table-column>
              <el-table-column label="已启用的试题数量" prop="enabledQuestions" width="180" align="center">
              </el-table-column>
              <el-table-column label="操作" width="80" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button type="text" size="mini" @click="remove(scope.row)">移除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
        <paper-choose-question-library
          ref="paperChooseQuestionLibraryRef"
          :change-data-num="changeDataNum"
          :visible.sync="showQuestionLibray"
          :change-exam-list="clearCurrentData"
          :is-modify-data="questionLibray"
          @close="closeQuestionLibray"
          @childByValue="childByValue"
          @updateIdList="handleUpdateIdList"
        ></paper-choose-question-library>
        <div class="m-btn-bar f-tc is-sticky-1 f-mb30">
          <el-button @click="$router.push('/resource/exam-paper')">放弃编辑</el-button>
          <el-button @click="commitDraft">保存为草稿</el-button>
          <el-button type="primary" @click.stop="nextStep">保存并进入下一步</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
  import { ValidResult } from '@hbfe/jxjy-admin-examPaper/src/ValidResult'
  import { Component, PropSync, Vue, Watch, Prop, Ref } from 'vue-property-decorator'
  import examClassify from '@hbfe/jxjy-admin-examPaper/src/add/exam-classify.vue'
  import PaperClassifyTree from '@hbfe/jxjy-admin-components/src/paper-classify-tree.vue'
  import { ExamPaperClassifyUI } from '@hbfe/jxjy-admin-components/src/models/ExamPaperClassifyUI'
  import ExamLibrary from '@api/service/management/resource/exam-paper/mutation/vo/common/ExamLibrary'
  import ExamPaperUIModule from '@/store/modules-ui/exam/ExamPaperUIModule'
  import PaperChooseQuestionLibrary from '@hbfe/jxjy-admin-components/src/paper-choose-question-library.vue'
  import LibraryResponseVo from '@api/service/management/resource/question-library/query/vo/LibraryResponseVo'
  import CreateExamPaperVo from '@api/service/management/resource/exam-paper/mutation/vo/create/CreateExamPaperVo'
  import AutomaticExamPaperVo from '@api/service/management/resource/exam-paper/mutation/vo/common/AutomaticExamPaperVo'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { cloneDeep } from 'lodash'
  import PaperPublishCategoryResponseVo from '@api/service/management/resource/exam-paper-category/query/vo/PaperPublishCategoryResponseVo'
  import { UiPage } from '@hbfe/common'
  class HasPaperPublishCategoryResponseVo extends PaperPublishCategoryResponseVo {
    hasChildren = true
    children? = new Array<HasPaperPublishCategoryResponseVo>()
  }
  @Component({
    components: { PaperClassifyTree, PaperChooseQuestionLibrary, examClassify }
  })
  export default class extends Vue {
    tableLoading = false
    unitName = ''
    queryExamPaperCategory = ResourceModule.queryExamPaperCategoryFactory
    @Ref('paperChooseQuestionLibraryRef') paperChooseQuestionLibraryRef: PaperChooseQuestionLibrary

    @PropSync('createExampaperInfo', {
      type: Object
    })
    createExamPaper!: CreateExamPaperVo<AutomaticExamPaperVo>

    @Watch('createExamPaper', {
      deep: true,
      immediate: true
    })
    async createExamPaperChange(val: any) {
      if (val) {
        if (this.isModify) {
          await this.echoDisplay(this.createExamPaper)
        }
      }
    }

    @Prop({
      type: Boolean,
      default: false
    })
    isModify: boolean
    idList: string[]
    changeDataNum = 1
    valNameRecord = 1

    configTypeList = [
      {
        title: '智能组卷',
        value: 0
      }
    ]
    /**
     * 选择的试题类型对象
     */
    paperTypeItem: any = {}
    loading = false
    /**
     *  回显的试卷分类
     */
    paperClassify = new ExamPaperClassifyUI()
    // 回显的分类id
    selectClassifyId = ''
    /**
     * 题库显示数据(分级)
     */
    questionLibray = new Array<ExamLibrary>()
    clearCurrentData = new LibraryResponseVo()
    classifyTree = true // 主要是 tree这个组件生产了数据树之后无法重新更新，只能这样删除了
    isUpdateFirst = false
    showQuestionLibray = false
    cachPathName: string[] = new Array<string>()
    currentStep = 1
    modifyShowData = false
    /**
     * 刷新组件
     */
    refresh = false
    page = new UiPage()
    /**
     * 题库数据
     */
    get pageData() {
      return ExamPaperUIModule.questionLibrarys
    }

    // 1:新增  2：修改
    get operationType() {
      return ExamPaperUIModule.operationType
    }

    async created() {
      if (this.isModify) {
        await this.echoDisplay(this.createExamPaper)
      }
    }
    async activated() {
      this.classifyTree = true
      if (this.refresh && this.createExamPaper.paperPublishConfigureCategoryId !== '-1') {
        await this.echoDisplay(this.createExamPaper)
      }
    }
    deactivated() {
      this.classifyTree = false
      this.refresh = true
    }

    // 数据回显函数
    async echoDisplay(item?: CreateExamPaperVo<AutomaticExamPaperVo>) {
      const arr = new Array<ExamLibrary>()
      item.publishPattern.libraryIds?.forEach(p => {
        const examLibrary = new ExamLibrary()
        examLibrary.name = p.name
        examLibrary.id = p.id
        examLibrary.enabledQuestions = p.enabledQuestions
        arr.push(examLibrary)
      })
      this.questionLibray = arr
      this.changeDataNum++
      this.paperClassify.id = item.paperPublishConfigureCategoryId || '-1'
      this.paperClassify.name = item.paperPublishConfigureCategoryName || '试卷分类'
      // 修改时，初始化的时候需要给一个选中id
      this.selectClassifyId = this.paperClassify.id

      // await this.resolveQuestionLibrayList()
    }

    // 清空题库
    clean() {
      ExamPaperUIModule.setQuestionLibrary([])
      this.questionLibray = []
    }

    /**
     * 打开选择题库
     */
    openQuestionLibray() {
      this.idList = [] as string[]
      this.questionLibray.forEach(p => {
        this.idList.push(p.id)
      })
      this.paperChooseQuestionLibraryRef.idList = cloneDeep(this.idList)
      this.showQuestionLibray = true
    }

    async handleUpdateIdList(idList: string[]) {
      this.questionLibray = await Promise.all(
        idList?.map(async id => {
          const item = await ResourceModule.queryQuestionLibraryFactory.queryQuestionLibraryMultiton.queryQuestionBankLibraryDetail(
            id
          )
          const data: LibraryResponseVo = item.data || new LibraryResponseVo()
          const option = new ExamLibrary()
          option.name = data.name
          option.id = data.id
          option.enabledQuestions = data.enabledQuestionCount
          return option
        })
      )
    }

    changeTime() {
      this.$forceUpdate()
    }

    /**
     * 选择题库界面关闭时触发
     */
    closeQuestionLibray() {
      this.tableLoading = true
      this.showQuestionLibray = false
      this.questionLibray = []

      if (ExamPaperUIModule.updateQuestionLibrary.length > 0 && ExamPaperUIModule.questionLibrarys.length === 0) {
        this.questionLibray = this.questionLibray.concat(ExamPaperUIModule.updateQuestionLibrary as any)
      }
    }

    /**
     * 清除题库
     */
    remove(item: LibraryResponseVo) {
      this.clearCurrentData = item
      const idx = this.questionLibray.findIndex((el: any) => {
        return el.id === item.id
      })
      if (idx !== -1) {
        this.questionLibray.splice(idx, 1)
      }
      this.changeDataNum++
    }

    selectNode(node: any) {
      this.selectClassifyId = node.id
    }

    /**
     * 数据校验
     */
    async valid() {
      const result = new ValidResult()

      if (!this.createExamPaper.name) {
        return result.withNullError('试卷名称不能为空')
      }
      if (!this.createExamPaper.paperPublishConfigureCategoryId) {
        return result.withNullError('请配置试卷分类')
      }
      const res = await this.requestSubPaperClassificationList(this.selectClassifyId)
      const hasChild = res
      //  要选到末级节点，不能仅判断paperPublishConfigureCategoryId
      if (this.createExamPaper.paperPublishConfigureCategoryId === '-1') {
        return result.withNullError('请选择末级试卷分类')
      }
      // 查询回显节点是否含有子节点
      if (hasChild == 'err') {
        return result.withNullError('获取校验失败')
      }
      if (hasChild > 0) {
        return result.withNullError('请选择末级试卷分类')
      }
      if (!this.createExamPaper.publishPattern.suggestionTimeLengthByMin) {
        return result.withNullError('请配置考试时长')
      }
      if (!this.validNumber(this.createExamPaper.publishPattern.suggestionTimeLengthByMin)) {
        return result.withInvalidError('请配置整数格式的考试时长')
      }
      if (this.questionLibray?.length === 0 && this.createExamPaper.publishPattern.questionScopes === 1) {
        return result.withInvalidError('出题题库至少选择1个')
      }
      return result
    }

    rules = {
      name: [
        {
          required: true,
          message: '请填写试卷名称',
          trigger: 'focus'
        }
      ],
      paperType: [
        {
          required: true,
          message: '请选择试卷分类',
          trigger: 'change'
        }
      ],
      configType: [
        {
          required: true,
          message: '请选择组卷方式',
          trigger: 'change'
        }
      ],
      suggestionTimeLengthByMin: [
        {
          required: true,
          message: '请填写试考试时长',
          trigger: 'focus'
        }
      ],
      unitId: [
        {
          required: true,
          message: '请选择培训机构',
          trigger: 'blur'
        }
      ]
    }

    /**
     * 校验正整数
     * @param input
     */
    validNumber(input: any) {
      const pattern = /^[1-9][0-9]*$/
      const result = pattern.test(input)
      return result
    }

    /**
     * 保存草稿
     */
    async commitDraft() {
      const arr = new Array<ExamLibrary>()

      this.questionLibray?.forEach(p => {
        const examLibrary = new ExamLibrary()
        examLibrary.name = p.name
        examLibrary.id = p.id
        examLibrary.enabledQuestions = p.enabledQuestions
        arr.push(examLibrary)
      })
      this.createExamPaper.publishPattern.libraryIds = arr
      this.$emit('commitDraft', true)
    }

    /**
     * 保存下一步
     */
    async nextStep() {
      if (this.createExamPaper.publishPattern.questionScopes == 2) {
        this.createExamPaper.publishPattern.libraryIds = []
      }
      const arr = new Array<ExamLibrary>()
      if (this.createExamPaper.publishPattern.questionScopes == 1) {
        this.questionLibray?.forEach(p => {
          const examLibrary = new ExamLibrary()
          examLibrary.name = p.name
          examLibrary.id = p.id
          examLibrary.enabledQuestions = p.enabledQuestions
          arr.push(examLibrary)
        })
        this.createExamPaper.publishPattern.libraryIds = arr
      }
      const result = await this.valid()
      if (!result.isSuccess()) {
        this.$message.warning(result.message)
        return
      }
      this.$emit('getCurrentStep', 2)
    }

    childByValue(value: any) {
      const arr = new Array<ExamLibrary>()
      value?.forEach((p: any) => {
        const examLibrary = new ExamLibrary()
        examLibrary.name = p.name
        examLibrary.id = p.id
        examLibrary.enabledQuestions = p.enabledQuestionCount
        arr.push(examLibrary)
      })
      this.createExamPaper.publishPattern.libraryIds = arr
      this.questionLibray = arr
      this.$emit('toParent', this.questionLibray)
    }

    async valName(value: any) {
      this.createExamPaper.paperPublishConfigureCategoryName = value
    }

    toCategory() {
      this.$router.push('/resource/exam-paper/category')
    }
    // 查询子节点
    async requestSubPaperClassificationList(parentId: string) {
      const res = await this.queryExamPaperCategory.queryQueryExamCategory.queryExamPaperCategoryList(
        this.page,
        parentId
      )
      if (res.data) return res.data.length
      return 'err'
    }
  }
</script>

<style lang="less" scoped>
  .m-btn-bar.is-sticky-1 {
    background-color: #fff;
  }
</style>
