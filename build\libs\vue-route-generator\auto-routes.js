const routerGenerator = require('./lib/index')
const path = require('path')
const yargs = require('yargs')
let viewPath = ''
const cachePath = path.resolve(process.cwd(), '.cache')
const fs = require('fs')
if (!process.env.VIEWS || !process.env.API) {
  return new Error('需要配置 VIEWS 告知处理器 views 的路径是哪里，API 网关路径是哪里')
}
viewPath = path.resolve(process.cwd(), process.env.VIEWS)
if (!fs.existsSync(cachePath)) {
  fs.mkdirSync(cachePath)
}
const code = routerGenerator.generateRoutes({
  importPrefix: '@/views/',
  // nested: true,
  pages: viewPath,
  chunkNamePrefix: '@/views/',
  withSecure: yargs.argv.withSecure
})

fs.writeFileSync(`${cachePath}/routes.js`, code)
