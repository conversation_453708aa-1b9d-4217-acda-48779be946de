<template>
  <div>
    <el-date-picker
      v-model="internalDateRange"
      type="daterange"
      :start-placeholder="beginTimePlaceholder"
      :end-placeholder="endTimePlaceholder"
      :value-format="valueFormat"
      :picker-options="pickerOptions"
      @change="handleDateChange"
      :clearable="false"
    />
  </div>
</template>

<script lang="ts">
  import { Vue, Component, Prop, Watch, Ref } from 'vue-property-decorator'

  @Component
  export default class DateRangePicker extends Vue {
    @Prop({ type: String, default: 'yyyy-MM-dd' }) valueFormat!: string
    @Prop({ type: String, default: '开始日期' }) beginTimePlaceholder!: string
    @Prop({ type: String, default: '结束日期' }) endTimePlaceholder!: string
    @Prop({ type: Array, default: () => ['', ''] }) dateRange!: [string, string]
    // 内部维护的日期范围
    internalDateRange: [string, string] = ['', '']
    isShow = false
    created() {
      this.internalDateRange = [...this.dateRange]
    }

    @Watch('dateRange')
    onDateRangeChange(newVal: [string, string]) {
      this.internalDateRange = [...newVal]
    }

    get pickerOptions() {
      return {
        // shortcuts: this.shortcuts,   2025-06-03需求 移除快捷选项
        // disabledDate: this.disabledDate,
        onPick: ({ maxDate, minDate }: { maxDate?: Date | null; minDate?: Date | null }) => {
          if (minDate && !maxDate) {
            this.currentMinDate = minDate
            this.currentMaxDate = null
          } else if (minDate && maxDate) {
            this.currentMinDate = minDate
            this.currentMaxDate = maxDate
          } else {
            this.currentMinDate = null
            this.currentMaxDate = null
          }
        }
      }
    }

    // 当前选择的临时日期（用于实时限制）
    currentMinDate: Date | null = null
    currentMaxDate: Date | null = null

    disabledDate(time: Date) {
      if (!this.currentMinDate || (this.currentMinDate && this.currentMaxDate)) {
        return false // 无临时选择时全部可选
      }
      const currentTime = time.getTime()
      const oneYearMs = 90 * 24 * 60 * 60 * 1000
      const minTime = this.currentMinDate.getTime()

      if (!this.currentMaxDate) {
        // 仅选择开始日期时，限制只能选±1年范围内的日期
        return currentTime < minTime - oneYearMs || currentTime > minTime + oneYearMs
      }
      return false // 其他情况不做限制
    }
    handleDateChange(val: [string, string] | null) {
      console.log('handleDateChange', val)
      if (val && val[0] && val[1]) {
        const start = new Date(val[0]).getTime()
        const end = new Date(val[1]).getTime()
        if (end - start > 90 * 24 * 60 * 60 * 1000) {
          this.$message.warning('日期范围不能超过3个月')
          this.internalDateRange = [val[0], '']
          this.isShow = true
          this.$emit('emitChange', true)
          return
        } else {
          this.$emit('emitChange', false)
        }
      }
      this.$emit('update:dateRange', val || ['', ''])
    }

    shortcuts = [
      {
        text: '最近一个月',
        onClick: (picker: any) => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: '最近三个月',
        onClick: (picker: any) => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: '最近六个月',
        onClick: (picker: any) => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 190)
          picker.$emit('pick', [start, end])
        }
      },
      {
        text: '最近一年',
        onClick: (picker: any) => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
          // 直接调用 handleDateChange 方法
          picker.$emit('pick', [start, end])
        }
      }
    ]
  }
</script>
