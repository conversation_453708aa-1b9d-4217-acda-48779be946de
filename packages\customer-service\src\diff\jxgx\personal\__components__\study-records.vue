<template>
  <JxgxStudyRecords ref="studyRecordsRef"> </JxgxStudyRecords>
</template>
<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import StudyRecords from '@hbfe/jxjy-admin-customerService/src/personal/components/study-records.vue'

  import TrainClassManagerModule from '@api/service/diff/management/jxgx/train-class/TrainClassManagerModule'
  import StudentTrainClassDetailVo from '@api/service/diff/management/jxgx/train-class/StudentTrainClassDetailVo'
  import MutationBatchPrintTraining from '@api/service/diff/management/jxgx/personal-leaning/mutation/MutationBatchPrintTraining'
  import { SinglePrintCertificateReponse } from '@api/diff-gateway/platform-jxjypxtypt-jxgx-certificate'
  import { FileTypesEnum } from '@api/service/common/enums/personal-leaning/FileTypes'
  import { Response } from '@hbfe/common'
  class JxgxStudyRecords extends StudyRecords {
    // 查询实例培训班列表
    queryStudentTrainClassObj = TrainClassManagerModule.queryTrainClassFactory.queryStudentTrainClass
    trainClassTableData = new Array<StudentTrainClassDetailVo>()
    queryTrainClassTableData = new StudentTrainClassDetailVo()
  }
  @Component({
    components: {
      JxgxStudyRecords
    }
  })
  export default class extends Vue {
    @Ref('studyRecordsRef') studyRecordsRef: JxgxStudyRecords

    // 打印证明实例
    batchPrintTrainingObj = new MutationBatchPrintTraining()
    // 打印证明入参
    batchPrintTrainingParams = this.batchPrintTrainingObj.printCertificateParams

    mounted() {
      this.studyRecordsRef.doPrintTraining = this.doPrintTraining
    }

    async userIdChange(val: string) {
      return this.studyRecordsRef.userIdChange(val)
    }

    // 打印学时证明
    async doPrintTraining(val: StudentTrainClassDetailVo): Promise<any> {
      this.batchPrintTrainingParams.studentNo = val?.studentNo
      this.batchPrintTrainingParams.fileType = FileTypesEnum.PDF
      this.batchPrintTrainingParams.qualificationId = val.qualificationId
      const res = await this.batchPrintTrainingObj.doPrintTraining()
      if (res?.data) {
        if (res.data.code === '5001') {
          return this.$confirm('当前学员尚未完善信息,无法打印证明', '提示', {
            confirmButtonText: '我知道了',
            showCancelButton: false
          })
        } else {
          // 新页面打开证明模板
          const newPage = window.open('', '_blank')
          newPage.document.write('<h2>加载中...</h2>')
          newPage.location.href = res.data.data
        }
      } else {
        // this.$message.error('打印学时证明请求失败！')
        this.errorResTips(res)
      }
    }
    errorResTips(res: Response<SinglePrintCertificateReponse>) {
      const code = res.status?.errors[0]?.code
      if (code === 46002) {
        return this.$message.error('该学员还未上传证书照片')
      } else {
        return this.$message.error('打印学时证明请求失败！')
      }
    }
  }
</script>
