import MsExamQueryBackStageGateway, {
  MultipleQuestionResponse,
  OpinionQuestionResponse,
  RadioQuestionResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { QuestionTypeEnum } from '@api/service/common/enums/question/QuestionType'
import { Response, ResponseStatus, UiPage } from '@hbfe/common'
import RadioQuestionDetailVo from './vo/RadioQuestionDetailVo'
import OpinionQuestionDetailVo from './vo/OpinionQuestionDetailVo'
import MultipleQuestionDetailVo from './vo/MultipleQuestionDetailVo'
import QuestionDetail from './common/QuestionDetail'

import MsCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  CourseRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class QueryQuestionDetail {
  questionDetail: QuestionDetail = new QuestionDetail()

  // 关联课程数组名称
  courseNameList = new Array<string>()

  constructor(questionId: string, questionType: QuestionTypeEnum) {
    /* 单选，多选，判断 */
    const QuestionContructors = {
      [QuestionTypeEnum.radio]: () => {
        return new RadioQuestionDetailVo()
      },
      [QuestionTypeEnum.multiple]: () => {
        return new MultipleQuestionDetailVo()
      },
      [QuestionTypeEnum.opinion]: () => {
        return new OpinionQuestionDetailVo()
      }
    }

    this.questionDetail = QuestionContructors[questionType]()
    this.questionDetail.questionId = questionId
    this.questionDetail.questionType = questionType
  }

  /**
   * @description: 查询试题详情
   * @param {*}
   * @return {*}
   */
  async queryQuestionDetail(): Promise<ResponseStatus> {
    const { data, status } = await this.doQueryByQuestionType(this.questionDetail.questionType)

    if (status?.isSuccess()) {
      if (data?.relateCourseIds?.length) {
        const list = data?.relateCourseIds || []
        this.courseNameList = await this.getCourseNameList(list)
      }
      this.questionDetail.from(data, this.courseNameList)
    }
    return status
  }

  // 返回关联课程名数组
  private async getCourseNameList(list: Array<string>) {
    const res = await this.queryCourseByIdList(list)
    if (!res?.status?.isSuccess()) {
      return []
    }
    return res?.data
  }

  // 获取关联课程名称
  private async queryCourseByIdList(idList: Array<string>): Promise<Response<Array<string>>> {
    const request = new CourseRequest()
    request.courseIdList = [...new Set(idList)]
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = request.courseIdList?.length
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
      page,
      request
    })
    const response = new Response<Array<string>>()
    if (!result?.status?.isSuccess()) {
      response.status = result.status
      return response
    }
    const list =
      result?.data?.currentPageData?.map(item => {
        return item?.name
      }) || []
    response.status = result.status
    response.data = list
    return response
  }

  // 根据试题类型调用查询试题详情接口
  async doQueryByQuestionType(
    type: QuestionTypeEnum
  ): Promise<Response<RadioQuestionResponse & MultipleQuestionResponse & OpinionQuestionResponse>> {
    const QueryByQuestionTypes = {
      [QuestionTypeEnum.radio]: async () => {
        return await this.queryRadioQuestionDetail()
      },
      [QuestionTypeEnum.multiple]: async () => {
        return await this.queryMultipleQuestionDetail()
      },
      [QuestionTypeEnum.opinion]: async () => {
        return await this.queryOpinionQuestionDetail()
      }
    }
    return await QueryByQuestionTypes[type]()
  }

  // 单选试题详情
  private async queryRadioQuestionDetail(): Promise<Response<RadioQuestionResponse>> {
    const res = await MsExamQueryBackStageGateway.getRadioQuestionInServicer(this.questionDetail.questionId)
    return res
  }

  // 多选试题详情
  private async queryMultipleQuestionDetail(): Promise<Response<MultipleQuestionResponse>> {
    const res = await MsExamQueryBackStageGateway.getMultipleQuestionInServicer(this.questionDetail.questionId)
    return res
  }

  // 判断题试题详情
  private async queryOpinionQuestionDetail(): Promise<Response<OpinionQuestionResponse>> {
    const res = await MsExamQueryBackStageGateway.getOpinionQuestionInServicer(this.questionDetail.questionId)
    return res
  }
}

export default QueryQuestionDetail
