"""独立部署的微服务,K8S服务名:ms-learning-constraint-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""无作用返回json 结构
		CommonResponse code=300  data字段的json 结构
		@return
	"""
	overlap:VerifyResult
}
type Mutation {
	"""创建规则
		@param request
		@return
	"""
	createStudyConstraintRule(request:StudyConstraintRuleCreateRequest):CommonResponse
	"""禁用规则
		@param request
	"""
	disableStudyConstraintRule(request:StudyConstraintRuleStatusRequest):Void
	"""启用规则
		@param request
	"""
	enableStudyConstraintRule(request:StudyConstraintRuleStatusRequest):CommonResponse
	"""更新规则
		@param request
		@return
	"""
	updateStudyConstraintRule(request:StudyConstraintRuleUpdateRequest):CommonResponse
}
"""创建学习规则请求
	<AUTHOR>
	@since
"""
input StudyConstraintRuleCreateRequest @type(value:"com.fjhb.ms.learningconstraint.v1.kernel.gateway.graphql.request.StudyConstraintRuleCreateRequest") {
	"""适用行业范围"""
	suitIndustryRangeList:[SuitIndustryRangeDto]
	"""新增非适用方案"""
	addNotSuitSchemeList:[NotSuitSchemeDto]
	"""规则类型
		1=时长
		2=课时
		@see com.fjhb.ms.studyconstraint.v1.api.consts.OnlineStudyConstraintRuleTypes
	"""
	ruleType:Int!
	"""最大学习限制值"""
	maxStudyTimeLength:Double!
	"""学习时长限制方式
		0 = 按日限制(默认)
		@see TimeLengthLimitWays
	"""
	timeLengthLimitWay:Int!
	"""限制值(默认1) 默认限制窗口大小为一天"""
	timeLengthLimitValue:Int!
	"""指定方案设置规则"""
	specifySchemeRuleList:[SpecifySchemeRuleDto]
}
"""状态切换请求
	<AUTHOR>
	@since
"""
input StudyConstraintRuleStatusRequest @type(value:"com.fjhb.ms.learningconstraint.v1.kernel.gateway.graphql.request.StudyConstraintRuleStatusRequest") {
	"""规则id"""
	ruleId:String
}
"""创建学习规则请求
	<AUTHOR>
	@since
"""
input StudyConstraintRuleUpdateRequest @type(value:"com.fjhb.ms.learningconstraint.v1.kernel.gateway.graphql.request.StudyConstraintRuleUpdateRequest") {
	"""规则id"""
	ruleId:String
	"""适用行业范围"""
	suitIndustryRangeList:[SuitIndustryRangeDto]
	"""新增非适用方案"""
	addNotSuitSchemeList:[NotSuitSchemeDto]
	"""规则类型
		1=时长
		2=课时
		@see com.fjhb.ms.studyconstraint.v1.api.consts.OnlineStudyConstraintRuleTypes
	"""
	ruleType:Int!
	"""最大学习限制值"""
	maxStudyTimeLength:Double!
	"""学习时长限制方式
		0 = 按日限制(默认)
		@see TimeLengthLimitWays
	"""
	timeLengthLimitWay:Int!
	"""限制值(默认1) 默认限制窗口大小为一天"""
	timeLengthLimitValue:Int!
	"""移除非适用方案"""
	removeSchemeIds:[String]
	"""指定方案设置规则(添加)"""
	addSpecifySchemeRuleList:[SpecifySchemeRuleDto]
	"""指定方案设置规则(更新)"""
	updateSpecifySchemeRuleList:[SpecifySchemeRuleDto]
	"""指定方案设置规则(需要删除的指定方案规则id)"""
	removeSpecifySchemeRuleList:[String]
}
"""非适用方案
	<AUTHOR>
	@since
"""
input NotSuitSchemeDto @type(value:"com.fjhb.ms.learningconstraint.v1.kernel.gateway.graphql.request.dto.NotSuitSchemeDto") {
	"""学习方案id"""
	schemeId:String
}
"""<AUTHOR>
	@since 2024-12-19
"""
input SpecifySchemeRuleDto @type(value:"com.fjhb.ms.learningconstraint.v1.kernel.gateway.graphql.request.dto.SpecifySchemeRuleDto") {
	"""规则id(为创建时不传)"""
	specifySchemeRuleId:String
	"""学习方案id集合"""
	schemeIdList:[String]
	"""最大学习限制值"""
	maxStudyTimeLength:Double!
	"""学习时长限制方式
		0 = 按日限制(默认)
		@see TimeLengthLimitWays
	"""
	timeLengthLimitWay:Int!
	"""学习时间限制值(按日时 为天数 当前默认1天)"""
	timeLengthLimitValue:Int!
	"""规则类型
		1=时长
		2=课时
		@see com.fjhb.ms.studyconstraint.v1.api.consts.OnlineStudyConstraintRuleTypes
	"""
	ruleType:Int!
}
"""适用行业范围
	<AUTHOR>
	@since
"""
input SuitIndustryRangeDto @type(value:"com.fjhb.ms.learningconstraint.v1.kernel.gateway.graphql.request.dto.SuitIndustryRangeDto") {
	"""行业id"""
	industryId:String
	"""附加范围
		year 年度
		subjectType 科目类型
		trainingCategory 培训类别
		trainingObject 培训对象
		discipline 学科
		positionCategory 岗位类别
		jobLevel 技术等级
		learningPhase 学段
		trainingProfessional 培训专业
		@see AdditionalRangeTypes
	"""
	additionalRange:Map
}
"""<AUTHOR>
	@since
"""
type ConflictAttribute @type(value:"com.fjhb.ms.learningconstraint.v1.kernel.appservice.rangematch.ConflictAttribute") {
	"""类型"""
	type:String
	"""当前值"""
	value:[String]
	"""冲突值"""
	conflictValue:[String]
}
"""<AUTHOR>
	@since
"""
type VerifyResult @type(value:"com.fjhb.ms.learningconstraint.v1.kernel.appservice.rangematch.VerifyResult") {
	"""规则id"""
	ruleId:String
	"""行业id"""
	industryId:String
	"""行业名称"""
	industryName:String
	"""冲突列表"""
	conflictAttributes:[ConflictAttribute]
}
"""通用响应
	400 一天学习时长不能超过1440分钟
	401 一天学习时长不能超过32学时
	410 适用行业与指定方案设置规则间存在不符合的方案
	411 指定方案设置规则不同特殊规则间存在重复方案
	412 不包含的方案、指定方案设置规则存在重复方案
	300  规则冲突
	500 操作错误提示
	<AUTHOR>
	@since
"""
type CommonResponse @type(value:"com.fjhb.ms.learningconstraint.v1.kernel.gateway.graphql.response.CommonResponse") {
	"""code 成功=200"""
	code:Int!
	"""错误信息"""
	errMsg:String
	"""json string"""
	data:String
}

scalar List
