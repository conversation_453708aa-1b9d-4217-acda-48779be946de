import MsMySchemeQueryFrontGatewayCourseLearningForeStage, {
  SchemeResponse,
  SchemeSkuPropertyResponse,
  StudentSchemeLearningResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
// import StudentTrainClassDetailVo from '@api/service/management/train-class/query/vo/StudentTrainClassDetailVo'

import StudentTrainClassDetailVo from '@api/service/diff/management/jxgx/train-class/StudentTrainClassDetailVo'
import QueryMyTrainClassDetail from '@api/service/management/train-class/query/QueryMyTrainClassDetail'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { getStudentSchemeLearningInServicer } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage/graphql-importer'
import {
  ComplexSkuPropertyResponse,
  SchemeSkuInfo,
  SkuPropertyConvertUtils
} from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import ConfigJsonUtil from '@api/service/management/train-class/Utils/ConfigJsonUtil'
import SkuPropertyConvertUtilsV2 from '@api/service/management/train-class/Utils/SkuPropertyConvertUtilsV2'

/**
 * @description 批量查询工具类
 */
class BatchQueryUtil {
  static async ConvertToStudentTrainClassList(
    responses: StudentSchemeLearningResponse[]
  ): Promise<StudentTrainClassDetailVo[]> {
    const map = new Map<string, StudentSchemeLearningResponse>()
    const result = [] as StudentTrainClassDetailVo[]
    /** 遍历接口返回值并实例各项 **/
    responses?.forEach((response) => {
      map.set(response.qualificationId, response)
      const detail = new StudentTrainClassDetailVo()
      Object.assign(detail, response)
      result.push(detail)
    })
    /** 构建一个方案信息列表、用于批量查询信息 **/
    const schemeDetailList = result.map((item) => {
      const opt = new QueryMyTrainClassDetail()
      opt.qualificationId = item.qualificationId ?? null
      return opt
    })

    for (const [key, value] of map.entries()) {
      const target = schemeDetailList.find((el) => el.qualificationId === key)
      if (target) {
        target.studentSchemeLearningResponse = value
        target.schemeId = value?.scheme?.schemeId
        target.getInfoFromStudentResponse(value)
      }
    }
    /** 批量查询sku属性 **/
    const schemeSkuInfos = [] as SchemeSkuInfo[]
    // SKU 优化 缓存版本
    const skuInfoByCache = new Array<SchemeSkuPropertyResponse>()
    for (const [key, value] of map.entries()) {
      skuInfoByCache.push(value.scheme.skuProperty)
      const opt = new SchemeSkuInfo(value.scheme.schemeId, value.scheme.skuProperty as ComplexSkuPropertyResponse)
      schemeSkuInfos.push(opt)
    }
    await SkuPropertyConvertUtilsV2.queryAllSku(skuInfoByCache)
    const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(schemeSkuInfos)
    schemeDetailList.forEach((item) => {
      const skuInfo = skuInfos?.find((el) => el.id === item.schemeId)
      if (skuInfo) item.trainClassDetail.trainClassBaseInfo.skuProperty = skuInfo.skuName
    })
    /** 批量请求方案json **/
    const schemeIdList = [...new Set(schemeDetailList.map((el) => el.schemeId)?.filter(Boolean))]
    const configJsonUtil = new ConfigJsonUtil()
    const schemeJSONMap = await configJsonUtil.batchQuerySchemeJsonConfigRespMap(schemeIdList)
    schemeDetailList.forEach((item) => {
      const schemeJSONResp = schemeJSONMap.get(item.schemeId)
      if (schemeJSONResp) {
        item.getInfoFromJSON(schemeJSONResp)
      }
    })
    /** 回填到返回数组 **/
    result.forEach((item) => {
      const target = schemeDetailList.find((el) => el.qualificationId === item.qualificationId)
      item.trainClassDetail = target.trainClassDetail ?? null
      item.trainClassJsonConfig = target?.jsonString ?? null
      StudentTrainClassDetailVo.convertFromTrainClassDetail(item)
      StudentTrainClassDetailVo.fillAssessInfo(item)
    })
    return result
  }
}

export default BatchQueryUtil
