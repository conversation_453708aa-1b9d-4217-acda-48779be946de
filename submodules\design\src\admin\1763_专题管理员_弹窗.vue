<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--当前账号管理的专题-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">当前账号管理的专题</el-button>
        <el-drawer
          title="当前账号管理的专题"
          :visible.sync="dialog1"
          :direction="direction"
          size="1100px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table" max-height="600px">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="专题名称" min-width="240" fixed="left">
                <template>专题名称专题名称专题名称专题名称</template>
              </el-table-column>
              <el-table-column label="专题类型" min-width="240">
                <template>类型</template>
              </el-table-column>
              <el-table-column label="是否显示在网校" min-width="240">
                <template>是</template>
              </el-table-column>
              <el-table-column label="状态" min-width="240">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="info" class="badge-status">停用</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">正常</el-badge>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <div class="f-mt10">
              <!--分页-->
              <el-pagination
                background
                class="f-tc"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确 定</el-button>
          </div>
        </el-drawer>
        <!--选择角色-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20 f-mb20">选择角色</el-button>
        <el-drawer title="选择角色" :visible.sync="dialog2" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="角色名称" min-width="180">
                <template>角色</template>
              </el-table-column>
              <el-table-column label="角色说明" min-width="300">
                <template>说明说明说明说明说明说明说明说明</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-checkbox>操作</el-checkbox>
                </template>
              </el-table-column>
              <el-empty slot="empty" :image-size="40" description="暂无数据，请添加角色~" />
            </el-table>
            <div class="f-mt10">
              <!--分页-->
              <el-pagination
                background
                class="f-tc"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确 定</el-button>
          </div>
        </el-drawer>
        <!--网校功能暂未开启-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20 f-mb20">网校专题功能暂未开启</el-button>
        <el-dialog title="系统提醒" :visible.sync="dialog3" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">网校专题功能暂未开启，请先完成专题基础配置，再进行具体专题页的发布</span>
          </div>
          <div slot="footer">
            <el-button type="primary">立即前往</el-button>
          </div>
        </el-dialog>
        <!--暂无专题-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20 f-mb20">暂无专题</el-button>
        <el-dialog title="系统提醒" :visible.sync="dialog4" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">暂无专题，是否前往新建？</span>
          </div>
          <div slot="footer">
            <el-button type="primary">立即前往</el-button>
          </div>
        </el-dialog>
        <!--选择专题-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">选择专题</el-button>
        <el-drawer
          title="选择专题"
          :visible.sync="dialog5"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--显示5个，超出部分隐藏-->
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="8">
                  <el-form-item label="专题名称">
                    <el-input v-model="input" clearable placeholder="请输入专题名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="专题类型">
                    <el-select clearable placeholder="请选择专题类型">
                      <el-option label="选项1" value=""></el-option>
                      <el-option label="选项2" value=""></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="专题名称" min-width="150">
                <template>福州地区专题页</template>
              </el-table-column>
              <el-table-column label="专题类型" min-width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-tooltip placement="top" effect="light">
                      <div slot="content">
                        <i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        >
                      </div>
                      <el-button type="text" class="f-to-three"
                        ><i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        ></el-button
                      >
                    </el-tooltip>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <p><el-tag size="mini" class="f-mt5">行业</el-tag>工艺美术</p>
                  </div>
                  <div v-else-if="scope.$index === 2">
                    <p><el-tag type="success" size="mini" class="f-mt5">单位</el-tag>华博教育</p>
                  </div>
                  <div v-else>
                    <el-tooltip placement="top" effect="light">
                      <div slot="content">
                        <i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        >
                      </div>
                      <el-button type="text" class="f-to-three"
                        ><i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        ></el-button
                      >
                    </el-tooltip>
                    <p><el-tag size="mini" class="f-mt5">行业</el-tag>工艺美术</p>
                    <p><el-tag type="success" size="mini" class="f-mt5">单位</el-tag>华博教育</p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="显示在网校" min-width="120" align="center">
                <template>显示</template>
              </el-table-column>
              <el-table-column label="状态" min-width="100">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-button type="text">选择</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
            <div class="f-f18 f-fb f-pt30 f-pb10">已选择的专题<span class="f-cr">X</span>个</div>
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="专题名称" min-width="150">
                <template>福州地区专题页</template>
              </el-table-column>
              <el-table-column label="专题类型" min-width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-tooltip placement="top" effect="light">
                      <div slot="content">
                        <i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        >
                      </div>
                      <el-button type="text" class="f-to-three"
                        ><i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        ></el-button
                      >
                    </el-tooltip>
                  </div>
                  <div v-else><el-tag type="warning" size="mini" class="f-mt5">行业</el-tag>工艺美术</div>
                </template>
              </el-table-column>
              <el-table-column label="显示在网校" min-width="120" align="center">
                <template>显示</template>
              </el-table-column>
              <el-table-column label="状态" min-width="100">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-button type="text">取消选择</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确 定</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames: ['1'],
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleChange(val) {
        console.log(val)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
