/**
 * @description 通用弹窗配置
 */
class DialogConfig {
  // 弹窗是否可见
  visible = false
  // 是否需要取消按钮，默认不需要
  needCancel = false
  // 取消文本
  cancelText = '取消'
  // 确认文本
  confirmText = '确认'
  // 文本内容
  content = ''
  // 场景值【页面自定义】
  sceneType = 0

  /**
   * 设置弹窗内容
   * @param {string} content - 文本内容
   * @param {boolean} needCancel - 是否需要取消按钮
   * @param {number} sceneType - 场景值
   * @param {string} confirmText - 确认文本
   * @param {string} cancelText - 取消文本
   */
  static setDialogContent(
    content: string,
    needCancel = false,
    sceneType: number,
    confirmText?: string,
    cancelText?: string
  ) {
    const newConfig = new DialogConfig()
    newConfig.content = content
    if (needCancel) newConfig.needCancel = needCancel
    newConfig.sceneType = sceneType
    if (confirmText) newConfig.confirmText = confirmText
    if (cancelText) newConfig.cancelText = cancelText
    newConfig.visible = true
    return newConfig
  }
}

export default DialogConfig
