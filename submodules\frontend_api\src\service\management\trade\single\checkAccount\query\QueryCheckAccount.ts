/*
 * @Description: 个人对账查询
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:32:55
 * @LastEditors: <PERSON><PERSON>ong
 * @LastEditTime: 2022-11-08 10:31:47
 */
import {
  default as Ms<PERSON>rade<PERSON><PERSON><PERSON>,
  default as MsTradeQueryFrontGatewayCourseLearningBacktage,
  OrderRequest,
  OrderSortField,
  OrderSortRequest,
  OrderStatisticResponse,
  ReturnOrderRequest,
  ReturnOrderSortField,
  ReturnSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { Page } from '@hbfe/common'
import statisticOrderInServicer from '@api/service/management/trade/single/order/query/graphql/statisticOrderInServicer.graphql'
import CheckAccountListResponse from './vo/CheckAccountListResponse'
import CheckAccountParam from './vo/CheckAccountParam'
import RefundCheckAccountListResponse from './vo/RefundCheckAccountListResponse'
import RefundCheckAccountParam from './vo/RefundCheckAccountParam'
import QueryCheckAccountBase from '@api/service/management/trade/single/checkAccount/query/vo/QueryCheckAccountBase'
import { ReturnOrderBasicDataRequest } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'

export default class QueryCheckAccount extends QueryCheckAccountBase {
  /**
   * 报名订单数
   */
  orderCount = 0
  /**
   * 交易总金额
   */
  amount = 0
  /**
   * 退款订单数
   */
  refundTradeCount = 0
  /**
   * 退款总金额
   */
  refundAmountCount = 0

  /**
   * 报名订单分页查询
   * @param page 页数
   * @param queryCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<CheckAccountListResponse>
   */
  async queryOfRegistrationOrder(
    page: Page,
    checkAccountParam: CheckAccountParam
  ): Promise<Array<CheckAccountListResponse>> {
    const request = CheckAccountParam.to(checkAccountParam)
    const sort = new OrderSortRequest()
    sort.field = OrderSortField.ORDER_NORMAL_TIME
    sort.policy = SortPolicy.DESC
    const { data } = await MsTradeQuery.pageOrderInServicer({
      page,
      request,
      sortRequest: [sort]
    })
    await this.statisticOrderInServicerRemoveTotalPeriod(request)
    await this.statisticOrderInServicer(request)
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const orderArr = new Array<CheckAccountListResponse>()
    const userIdList = new Array<string>()
    data.currentPageData.map(item => {
      const data = CheckAccountListResponse.from(item)
      userIdList.push(data.userId)
      orderArr.push(data)
    })
    const map = await this.getUserInfo(userIdList)

    orderArr.map(item => {
      const userInfo = map.get(item.userId)
      if (userInfo) {
        item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
      }
    })
    return orderArr
  }
  async statisticOrderInServicer(orderRequest: OrderRequest) {
    const { data } = await MsTradeQuery.statisticOrderInServicer(orderRequest)
    this.orderCount = data?.totalOrderCount || 0
    this.amount = data?.totalOrderAmount || 0
  }
  async statisticOrderInServicerRemoveTotalPeriod(orderRequest: OrderRequest) {
    const response = await MsTradeQuery._commonQuery<OrderStatisticResponse>(statisticOrderInServicer, {
      request: orderRequest
    })
    this.orderCount = response.data?.totalOrderCount || 0
    this.amount = response.data?.totalOrderAmount || 0
  }

  /**
   * 报名订单分页查询
   * @param page 页数
   * @param queryCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<CheckAccountListResponse>
   */
  async queryOfFxRegistrationOrder(
    page: Page,
    checkAccountParam: CheckAccountParam
  ): Promise<Array<CheckAccountListResponse>> {
    const request = CheckAccountParam.to(checkAccountParam)
    const sort = new OrderSortRequest()
    sort.field = OrderSortField.ORDER_NORMAL_TIME
    sort.policy = SortPolicy.DESC
    const { data } = await MsTradeQuery.pageOrderInDistributor({
      page,
      request,
      sortRequest: [sort]
    })
    await this.statisticFxOrderInServicer(request)
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const orderArr = new Array<CheckAccountListResponse>()
    const userIdList = new Array<string>()
    data.currentPageData.map(item => {
      const data = CheckAccountListResponse.from(item)
      userIdList.push(data.userId)
      orderArr.push(data)
    })
    const map = await this.getUserInfo(userIdList)

    orderArr.map(item => {
      const userInfo = map.get(item.userId)
      if (userInfo) {
        item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
      }
    })
    return orderArr
  }
  private async statisticFxOrderInServicer(orderRequest: OrderRequest) {
    const { data } = await MsTradeQuery.statisticOrderInDistributor(orderRequest)
    this.orderCount = data?.totalOrderCount || 0
    this.amount = data?.totalOrderAmount || 0
  }

  /**
   * 退款订单分页查询
   * @param page 页数
   * @param queryRefundCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<RefundCheckAccountListResponse>
   */
  async queryOfRefundOrder(
    page: Page,
    queryRefundCheckAccountParam: RefundCheckAccountParam
  ): Promise<Array<RefundCheckAccountListResponse>> {
    const request = RefundCheckAccountParam.refurnTo(queryRefundCheckAccountParam)
    const sort = new ReturnSortRequest()
    sort.field = ReturnOrderSortField.APPLIED_TIME
    sort.policy = SortPolicy.DESC
    const { data } = await MsTradeQueryFrontGatewayCourseLearningBacktage.pageReturnOrderInServicer({
      page: page,
      request,
      sort: [sort]
    })
    await this.queryStatisticReturnOrder(request)
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const refundArr = new Array<RefundCheckAccountListResponse>()
    const userIdList = new Array<string>()
    data.currentPageData.map(item => {
      const data = RefundCheckAccountListResponse.from(item)
      userIdList.push(data.userId)
      refundArr.push(data)
    })
    const map = await this.getUserInfo(userIdList)
    refundArr.map(item => {
      const userInfo = map.get(item.userId)
      if (userInfo) {
        item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
      }
      item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
    })
    return refundArr
  }
  async queryStatisticReturnOrder(request: ReturnOrderRequest) {
    const totalRes = await MsTradeQueryFrontGatewayCourseLearningBacktage.statisticReturnOrderInServicer(request)
    if (totalRes.status.isSuccess()) {
      if (totalRes.data) {
        this.refundAmountCount = totalRes.data.totalRefundAmount
        this.refundTradeCount = totalRes.data.totalReturnOrderCount
      } else {
        this.refundAmountCount = 0
        this.refundTradeCount = 0
      }
    }
  }

  /**
   * 分销退款订单分页查询
   * @param page 页数
   * @param queryRefundCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<RefundCheckAccountListResponse>
   */
  async queryOfFxRefundOrder(
    page: Page,
    queryRefundCheckAccountParam: RefundCheckAccountParam
  ): Promise<Array<RefundCheckAccountListResponse>> {
    const request = RefundCheckAccountParam.refurnTo(queryRefundCheckAccountParam)
    const sort = new ReturnSortRequest()
    sort.field = ReturnOrderSortField.APPLIED_TIME
    sort.policy = SortPolicy.DESC

    const { data } = await MsTradeQueryFrontGatewayCourseLearningBacktage.pageReturnOrderInDistributor({
      page: page,
      request,
      sort: [sort]
    })
    await this.queryFxStatisticReturnOrder(request)
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const refundArr = new Array<RefundCheckAccountListResponse>()
    const userIdList = new Array<string>()
    data.currentPageData.map(item => {
      const data = RefundCheckAccountListResponse.from(item)
      userIdList.push(data.userId)
      refundArr.push(data)
    })
    const map = await this.getUserInfo(userIdList)
    refundArr.map(item => {
      const userInfo = map.get(item.userId)
      if (userInfo) {
        item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
      }
      item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
    })
    return refundArr
  }
  private async queryFxStatisticReturnOrder(request: ReturnOrderRequest) {
    const totalRes = await MsTradeQueryFrontGatewayCourseLearningBacktage.statisticReturnOrderInDistributor(request)
    if (totalRes.status.isSuccess()) {
      if (totalRes.data) {
        this.refundAmountCount = totalRes.data.totalRefundAmount
        this.refundTradeCount = totalRes.data.totalReturnOrderCount
      } else {
        this.refundAmountCount = 0
        this.refundTradeCount = 0
      }
    }
  }
}
