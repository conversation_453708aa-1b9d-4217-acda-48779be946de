import { CourseCategoryResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class CourseCategoryListDetail {
  id: string = undefined
  name: string = undefined
  parentId: string = undefined
  sort: number = undefined
  enable: boolean = undefined
  createTime: string = undefined
  hasChildren = true
  children: Array<CourseCategoryListDetail> = new Array<CourseCategoryListDetail>()

  static from(courseCategoryResponse: CourseCategoryResponse) {
    const detail = new CourseCategoryListDetail()
    detail.id = courseCategoryResponse.id
    detail.name = courseCategoryResponse.name
    detail.sort = courseCategoryResponse.sort
    detail.parentId = courseCategoryResponse.parentId
    return detail
  }
}

export default CourseCategoryListDetail
