/**
 * 供应商分销商销售统计——搜索字段说明
 */
export const supplierDistributorSalesStatisticsSearchData = [
  { field: '分销商品', des: '输入文字分销商品名称查询分销商品的销售数据' },
  { field: '分销商', des: '选择分销商查询分销销售数据' },
  { field: '推广门户简称', des: '选择某个推广门户查询分销商销售数据' },
  { field: '报名时间', des: '查询在指定报名时间段内的销售数据' }
]
/**
 * 供应商分销商销售统计——列表字段说明
 */
export const supplierDistributorSalesStatisticsFieldData = [
  { field: '分销商', des: '分销商的名称，默认显示全部分销商' },
  { field: '合计', des: '分销商的销售数据合计，包括分销商自己推广的订单数据以及关联的二级分销商推广数据' },
  { field: '个人缴费', des: '学员通过分销商门户自主报名的数据' },
  { field: '集体报名', des: '通过集体报名管理员报名的数据' },
  { field: '导入开通', des: '通过后台导入开通报名的数据' },
  { field: '线上支付', des: '通过网络在线的形式进行缴费' },
  { field: '线下支付', des: '通过线下的形式进行缴费' },
  {
    field: '开通',
    des: '根据查询日期，统计该分销商作为作为一级分销的直接分销数据和与他关联的二级分销数据。学员成功开通培训班的人次。缴费成功订单状态为交易成功的培训班则开通数+1'
  },
  {
    field: '退班',
    des: '根据查询日期，统计该分销商作为作为一级分销的直接分销数据和与他关联的二级分销数据。在查询时间段内，学员退款成功培训班的人次则退班数+1'
  },
  { field: '净开通', des: '净开通= 开通数 + 换入数 - 退班数 - 换出数' },
  { field: '分销总额', des: '净开通班级的分销商品总金额' }
]
/**
 * 分销商品开通统计——搜索字段说明
 */
export const distributionGoodsOpeningStatisticsSearchData = [
  { field: '分销商品', des: '输入文字分销商品名称查询分销商品的销售数据' },
  { field: '分销商', des: '选择分销商查询分销销售数据' },
  { field: '推广门户简称', des: '选择某个推广门户查询分销商销售数据' },
  { field: '报名时间', des: '查询在指定报名时间段内的销售数据' },
  { field: '分销价格', des: '查询分销商品定价方案在这个区间内的或优惠申请的价格在区间内的销售数据' }
]
/**
 * 分销商品开通统计——列表字段说明
 */
export const distributionGoodsOpeningStatisticsFieldData = [
  { field: '分销商品', des: '分销商的名称，默认显示有销售数据的分销商品' },
  { field: '培训属性', des: '分销商品对应培训方案的培训属性' },
  { field: '分销商', des: '分销商的名称' },
  { field: '定价方式', des: '显示分销商品销售的定价方式，包括定价方案和优惠申请和具体的销售价格' },
  { field: '合计', des: '分销商的销售数据合计，包括分销商自己推广的订单数据以及关联的二级分销商推广数据' },
  { field: '个人缴费', des: '学员通过分销商门户自主报名的数据' },
  { field: '集体报名', des: '通过集体报名管理员报名的数据' },
  { field: '导入开通', des: '通过后台导入开通报名的数据' },
  { field: '线上支付', des: '通过网络在线的形式进行缴费' },
  { field: '线下支付', des: '通过线下的形式进行缴费' },
  {
    field: '开通',
    des: '根据查询日期，统计该分销商作为作为一级分销的直接分销数据和与他关联的二级分销数据。学员成功开通培训班的人次。缴费成功订单状态为交易成功的培训班则开通数+1'
  },
  {
    field: '退班',
    des: '根据查询日期，统计该分销商作为作为一级分销的直接分销数据和与他关联的二级分销数据。在查询时间段内，学员退款成功培训班的人次则退班数+1'
  },
  { field: '净开通', des: '净开通= 开通数 + 换入数 - 退班数 - 换出数' },
  { field: '分销总额', des: '净开通班级的分销商品总金额' }
]
