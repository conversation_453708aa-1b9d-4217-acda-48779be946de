import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'
import MutationCreateCourse from '@api/service/management/resource/course/mutation/MutationCreateCourse'
import MutationUpdateCourse from '@api/service/management/resource/course/mutation/MutationUpdateCourse'
import UpdateCourseVo from '@api/service/management/resource/course/mutation/vo/UpdateCourse'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import QueryStudentCourse from '@api/service/management/resource/course/query/QueryStudentCourse'
import QueryCourseDistribution from '@api/service/management/resource/course/query/QueryCourseDistribution'
import { CheckCourseRequest } from '@api/ms-gateway/ms-course-resource-v1'
import MsCourseResourceV1 from '@api/ms-gateway/ms-course-resource-v1'

class CourseFactory {
  get queryCourse() {
    return new QueryCourse()
  }

  get queryStudentCourse() {
    return new QueryStudentCourse()
  }

  get createCourse() {
    return new MutationCreateCourse()
  }
  /**
   * 查课程名称是否重复
   */
  async getCheckCourse(courseName: string, id?: string) {
    const request = new CheckCourseRequest()
    request.name = courseName
    request.id = id
    const { data } = await MsCourseResourceV1.checkCourse(request)
    return data
  }
  /**
   * 查询待分配课程
   */
  get queryCourseDistribution() {
    return new QueryCourseDistribution()
  }

  async getUpdateCourse(courseId: string): Promise<MutationUpdateCourse> {
    const { data: detail } = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.getCourseInServicer(
      courseId
    )
    const mutation = new MutationUpdateCourse()
    mutation.updateCourseVo = UpdateCourseVo.from(detail)
    return mutation
  }
}

export default CourseFactory
