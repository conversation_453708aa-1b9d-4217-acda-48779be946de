import {
  CoursewareMediaPreviewResourceResponse,
  DocumentPlayResourceResponse,
  LecturePlayResourceResponse,
  VideoCaptionPlayResourceResponse,
  VideoChapterPlayResourceResponse,
  VideoPlayResourceResponse,
  VideoResourceResponse
} from '@api/ms-gateway/ms-course-play-resource-v1'
import PlayResource from '@api/service/customer/learning/course/vo/PlayResource'
import ResourceTypeEnum from '@api/service/customer/learning/course/enums/ResourceTypeEnum'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import MediaTypeEnum from '@api/service/customer/learning/course/enums/MediaTypeEnum'
import ObsReceive from '@api/service/common/obs/ObsReceive'

class MediaPlayResource {
  // 支持的资源类型集合
  static supportResourceTypeList = [
    ResourceTypeEnum.type_huawei_cloud_video,
    ResourceTypeEnum.type_huawei_cloud_audio,
    ResourceTypeEnum.type_external_link
  ]
  /**
   * 课件媒体资源ID
   */
  id: string

  /**
   * 课件媒体资源类型
   */
  type: number

  /**
   * 媒体时长
   */
  timeLength: number

  /**
   * 文档播放资源
   */
  documentList: Array<DocumentPlayResourceResponse>

  /**
   * 视频信息
   */
  videoPlayResourceList: Array<VideoPlayResourceResponse>

  /**
   * 视频字幕
   */
  videoCaptionList: Array<VideoCaptionPlayResourceResponse>

  /**
   * 视频转码信息列表
   */
  videoTranscodingList: Array<VideoResourceResponse> = new Array<VideoResourceResponse>()

  /**
   * 视频讲义列表
   */
  lectureList: Array<LecturePlayResourceResponse>

  /**
   * 视频章节列表
   */
  videoChapterList: Array<VideoChapterPlayResourceResponse>

  /**
   * 判断是否是 video
   */
  isVideo?() {
    return this.type === MediaTypeEnum.video || this.type === MediaTypeEnum.normal_media
  }

  isDocument?() {
    return this.type === MediaTypeEnum.document
  }

  /**
   * 获取文档播放资源
   */
  async getDocumentPlayResource(token?: string) {
    if (this.documentList[0].path.includes('obs')) {
      localStorage.setItem('admin/customer.Access-Token', token)
      const obsReceive = new ObsReceive()
      return location.origin + (await obsReceive.getResolveFilePath(this.documentList[0].path)).path
    } else {
      return `${ConfigCenterModule.getFrontendApplication(frontendApplication.mfsHost)}/${this.documentList[0].path}`
    }
  }

  /**
   * 判断当前的播放资源是否在支持范围内
   */
  hasSupportResources() {
    if (this.isVideo()) {
      return !!this.getVideoPlayResource(MediaPlayResource.supportResourceTypeList).length
    }
    return !!this.documentList?.length
  }

  /**
   * 获取播放资源
   * @param types
   */
  getVideoPlayResource?(
    types: Array<ResourceTypeEnum> = [ResourceTypeEnum.type_huawei_cloud_video, ResourceTypeEnum.type_external_link]
  ): Array<PlayResource> {
    let sourceList: Array<VideoResourceResponse> = this.videoTranscodingList
    if (types.length) {
      if (!this.videoTranscodingList) {
        throw new Error('没有播放资源')
      }
      sourceList = this.videoTranscodingList.filter((resource: VideoResourceResponse) =>
        types.includes(resource.resourceType)
      )
    }
    return sourceList.map(PlayResource.from).sort((i, a) => i.quality - a.quality)
  }

  /**
   * 判断是否又音频内容
   */
  hasAudio() {
    return !!this.videoTranscodingList?.filter(
      (resource: VideoResourceResponse) => resource.resourceType === ResourceTypeEnum.type_huawei_cloud_audio
    ).length
  }

  /**
   * 类型转换
   * @param remoteResponse
   */
  static from(remoteResponse: CoursewareMediaPreviewResourceResponse) {
    const mediaPlayResource = new MediaPlayResource()
    mediaPlayResource.id = remoteResponse.id
    mediaPlayResource.type = remoteResponse.type
    mediaPlayResource.timeLength = remoteResponse.timeLength
    mediaPlayResource.documentList = remoteResponse.documentList
    mediaPlayResource.videoPlayResourceList = remoteResponse.videoPlayResourceList
    mediaPlayResource.videoCaptionList = remoteResponse.videoCaptionList
    mediaPlayResource.videoTranscodingList = remoteResponse.videoTranscodingList
    mediaPlayResource.lectureList = remoteResponse.lectureList
    return mediaPlayResource
  }

  hasNoResourceToPlay() {
    return (
      !this.documentList &&
      !this.videoCaptionList &&
      !this.videoChapterList &&
      !this.videoTranscodingList &&
      !this.videoPlayResourceList
    )
  }
}

export default MediaPlayResource
