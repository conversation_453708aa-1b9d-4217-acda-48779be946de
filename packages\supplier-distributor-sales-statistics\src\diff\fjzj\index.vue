<template>
  <SupplierDistributorSalesStatisticsFjzj ref="supplierDistributorSalesStatisticsRef">
    <template #remove-training-plan="{ supplierDistributorSalesStatisticsData }">
      <el-form-item label="剔除商品">
        <biz-remove-training-plan
          v-model="supplierDistributorSalesStatisticsData.param.excludeCommodityIdList"
          placeholder="请选择不纳入统计的商品"
        ></biz-remove-training-plan>
      </el-form-item>
    </template>
  </SupplierDistributorSalesStatisticsFjzj>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import SupplierDistributorSalesStatistics from '@hbfe/jxjy-admin-supplierDistributorSalesStatistics/src/index.vue'
  import SupplierDistributorSalesStatisticsData from '@api/service/diff/management/fjzj/statistical-report/DistributorSalesStatistics/SupplierDistributorSalesStatistics'
  import SupplierDistributorSalesStatisticsParams from '@api/service/diff/management/fjzj/statistical-report/DistributorSalesStatistics/model/SupplierDistributorSalesStatisticsParams'
  @Component
  class SupplierDistributorSalesStatisticsFjzj extends SupplierDistributorSalesStatistics {
    /**
     * 分销商销售统计
     */
    supplierDistributorSalesStatisticsData = new SupplierDistributorSalesStatisticsData()
  }

  @Component({
    components: {
      SupplierDistributorSalesStatisticsFjzj
    }
  })
  export default class extends Vue {
    @Ref('supplierDistributorSalesStatisticsRef')
    supplierDistributorSalesStatisticsRef: SupplierDistributorSalesStatistics
  }
</script>
