import MsAutonomouscourselearningsceneV1, {
  StudentCourseLearningTokenResponse
} from '@api/ms-gateway/ms-autonomouscourselearningscene-v1'
import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import { Response, ResponseStatus } from '@hbfe/common'

/**
 * 学习课程token
 */
class ApplyAutonomousCourseLearningToken extends AbstractApplyToken {
  private readonly applyStudentLearningToken: string
  private readonly courseId: string
  private readonly outlineId: string
  /**
   * 申请课程学习token返回值
   */
  response = new Response<StudentCourseLearningTokenResponse>()

  constructor(courseId: string, applyStudentLearningToken: string, outlineId: string) {
    super()
    this.applyStudentLearningToken = applyStudentLearningToken
    this.courseId = courseId
    this.outlineId = outlineId
  }

  async apply(): Promise<ResponseStatus> {
    // 学习方案场景申请用户 token
    const tokenValue = await MsAutonomouscourselearningsceneV1.applyCourseLearning({
      studentLearningToken: this.applyStudentLearningToken,
      courseId: this.courseId,
      outlineId: this.outlineId
    })
    this.response = tokenValue
    if (!tokenValue.data.token) {
      return Promise.reject(
        new ResponseStatus(parseInt(tokenValue.data.applyResult.code), tokenValue.data.applyResult.message)
      )
    }
    this.token = tokenValue.data.token
    this.code = Number(tokenValue.data?.applyResult?.code)
    return new ResponseStatus(tokenValue.status.code, '')
  }
}

export default ApplyAutonomousCourseLearningToken
