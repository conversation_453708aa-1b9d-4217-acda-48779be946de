import msTradeConfigurationV1 from '@api/ms-gateway/ms-trade-configuration-v1'
import { Response } from '@hbfe/common'
import PurchaseChannelTypeVo from './vo/PurchaseChannelTypeVo'

class MutationPreparePurchaseChannel {
  // 购买渠道类型列表
  purchaseChannelList = new Array<PurchaseChannelTypeVo>()

  /**
   * 获取当前网校配置的渠道列表
   * @returns purchaseChannelList
   */
  async preparePurchaseChannel(): Promise<Response<boolean>> {
    const msRes = await msTradeConfigurationV1.preparePurchaseChannel()
    const response = new Response<boolean>()
    if (msRes.status.isSuccess()) {
      this.purchaseChannelList = msRes.data?.purchaseChannelList?.map(PurchaseChannelTypeVo.from)
      response.status = msRes.status
      response.data = true
    }
    response.status = msRes.status
    return response
  }
}
export default new MutationPreparePurchaseChannel()
