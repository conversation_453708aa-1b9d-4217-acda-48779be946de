<template>
  <el-card shadow="never" class="m-card f-mb15">
    <div slot="header" class="">
      <span class="tit-txt">培训要求</span>
    </div>
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form ref="form" label-width="150px" class="m-text-form is-column f-mt10">
          <el-form-item label="考核规则：" v-show="enableCourseLearning || enableExamLearning">
            <p v-show="enableCourseLearning">
              {{ assessmentRuleNo('courseLearning') }}. 课程学习要求不低于
              <i class="f-cr">{{ schemeRequirePeriodTotal }}</i> 学时。
            </p>
            <div class="f-ml15" v-show="enableCourseLearning">
              <p>① 每门课程学习进度=100%</p>
              <p v-show="courseLearningInfo.courseQuizPagerStandard">
                ② 课程测验纳入考核，每门课程学习进度达
                <i class="f-cr">{{ courseLearningInfo.quizConfigModel.minCourseSchedule }}%</i>
                可参加，测验及格分 ≥
                <i class="f-cr">{{ courseLearningInfo.quizConfigModel.passScore }}</i>
                分，次数不限次。
              </p>
            </div>
            <p :class="enableCourseLearning ? 'f-mt10' : ''" v-show="enableExamLearning">
              {{ assessmentRuleNo('exam') }}.
              {{ examLearningInfo.isExamAssessed ? '考试纳入考核' : '班级考试不纳入考核' }}，班级考试成绩 ≥
              {{ examLearningInfo.examPassScore }}
              分，{{ examLearningInfo.isExamAssessed ? '视为通过' : '考试合格' }}
            </p>
            <div v-show="enableExperienceLearning">
              {{ assessmentRuleNo('learningExperience') }}. 学习心得纳入考核
              <div class="f-ml15">
                <p>① 各项学习心得要求以具体配置为准</p>
                <p>
                  ②学习心得纳入考核，至少参加
                  <i class="f-cr">{{ enableExperiencejoinCount }}</i> 个心得，且每项心得均为通过。
                </p>
              </div>
            </div>
            <p v-show="questionnaireStandard">
              {{ assessmentRuleNo('questionnaire') }}. 调研问卷纳入考核，按具体问卷要求提交。<el-button
                type="text"
                class="f-ml10"
                @click="dialog.questionnaireAssessVisible = true"
                >[查看详情]</el-button
              >
            </p>
            <p v-show="issuePeriodStandard">
              {{ assessmentRuleNo('issue') }}. 培训期别：至少完成一个期别并考核通过。<el-button
                type="text"
                class="f-ml10"
                @click="dialog.issueAssessVisible = true"
                >[查看详情]</el-button
              >
            </p>
          </el-form-item>
          <el-form-item label="获得学时：">
            考核通过后将获得 {{ schemeDetail.trainClassBaseInfo.period }} 学时
          </el-form-item>
          <slot
            name="hide-printTemplate"
            :getLearningResultInfo="getLearningResultInfo"
            :hasLearningResult="hasLearningResult"
            :templateName="templateName"
            :closePrintTemplate="closePrintTemplate"
          >
            <el-form-item label="培训成果：">{{ getLearningResultInfo() }}</el-form-item>
            <!--选择提供证明后显示-->
            <el-form-item label="培训证明模板：" v-show="hasLearningResult">
              <p>{{ templateName }}</p>
              <el-checkbox class="f-mt5" :checked="closePrintTemplate" disabled>
                不开放学员和集体报名人员打印，仅管理员可打印
              </el-checkbox>
            </el-form-item>
          </slot>
        </el-form>
      </el-col>
    </el-row>
    <questionnaire-assess
      :questionnaire-assess.sync="dialog.questionnaireAssessVisible"
      :questionnaireList="questionnaireList"
      :trainClassBaseInfo="schemeDetail.trainClassBaseInfo"
    ></questionnaire-assess>
    <issue-assess
      :issue-assess.sync="dialog.issueAssessVisible"
      :questionnaire="schemeDetail.learningTypeModel.questionnaire"
      :issue="schemeDetail.learningTypeModel.issue"
      :trainClassBaseInfo="schemeDetail.trainClassBaseInfo"
    ></issue-assess>
  </el-card>
</template>

<script lang="ts">
  import { Component, PropSync, Vue } from 'vue-property-decorator'
  import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import TrainingCertificateModule from '@api/service/management/personal-leaning/TrainingCertificateModule'
  import { UiPage } from '@hbfe/common'
  import CertificateTemplateResponseVo from '@api/service/management/personal-leaning/query/vo/CertificateTemplateResponseVo'
  import { QuestionnaireAppliedRangeTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'
  import IssueAssess from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/issue-assess.vue'
  import QuestionnaireAssess from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/questionnaire-assess.vue'

  @Component({
    components: { QuestionnaireAssess, IssueAssess }
  })
  export default class extends Vue {
    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: TrainClassDetailClassVo }) schemeDetail: TrainClassDetailClassVo
    /**
     * 培训模板
     */
    queryCertificateTemplateList = TrainingCertificateModule.queryTrainingCertificateFactory.certificateTemplate
    page = new UiPage()
    templateName = ''
    // 对话框是否展示
    dialog = {
      //   问卷考核
      questionnaireAssessVisible: false,
      //   期别考核
      issueAssessVisible: false
    }
    async created() {
      await this.getTrainingCertificateName()
    }
    /**
     * 是否勾选课程学习
     */
    get enableCourseLearning() {
      return this.schemeDetail.learningTypeModel.courseLearning.isSelected
    }

    /**
     * 是否勾选考试学习
     */
    get enableExamLearning() {
      return this.schemeDetail.learningTypeModel.exam.isSelected
    }

    /**
     * 课程学习要求学时
     */
    get schemeRequirePeriodTotal() {
      /** 选课规则 */
      if (this.schemeDetail.trainClassBaseInfo.schemeType == 1) {
        const compulsoryRequirePeriod = Number(
          this.schemeDetail.learningTypeModel.courseLearning.compulsoryRequirePeriod
        )
        const electiveRequirePeriod = Number(this.schemeDetail.learningTypeModel.courseLearning.electiveRequirePeriod)
        return CalculatorObj.add(compulsoryRequirePeriod, electiveRequirePeriod)
      }
      /** 自主选课 */
      if (this.schemeDetail.trainClassBaseInfo.schemeType == 2) {
        return Number(this.schemeDetail.learningTypeModel.courseLearning.requirePeriod)
      }
      return 0
    }

    /**
     * 课程学习配置
     */
    get courseLearningInfo() {
      return this.schemeDetail.learningTypeModel.courseLearning
    }

    /**
     * 考试配置
     */
    get examLearningInfo() {
      return this.schemeDetail.learningTypeModel.exam
    }

    /**
     * 获取考试次数
     */
    get examAllowCountConfigure() {
      return this.examLearningInfo.allowCount === -1 ? '不限' : this.examLearningInfo.allowCount
    }

    /**
     * 是否提供培训证明
     */
    get hasLearningResult() {
      return this.schemeDetail.trainClassBaseInfo.hasLearningResult
    }

    /**
     * 是否关闭证明打印
     */
    get closePrintTemplate() {
      return !this.schemeDetail.trainClassBaseInfo.openPrintTemplate
    }

    /**
     * 是否学习心得被纳入考核 并勾选
     */
    get enableExperienceLearning() {
      return (
        this.schemeDetail.learningTypeModel.learningExperience.isExamine &&
        this.schemeDetail.learningTypeModel.learningExperience.isSelected
      )
    }

    /**
     * 是否学习心得被纳入考核
     */
    get enableExperiencejoinCount() {
      return this.schemeDetail.learningTypeModel.learningExperience.joinCount
    }
    /**
     * 学习期别是否有考核
     */
    get issuePeriodStandard() {
      return this.schemeDetail.learningTypeModel.issue.isSelected
    }
    /**
     * 问卷是否有线上或方案级
     */
    get questionnaireStandard() {
      return this.schemeDetail.learningTypeModel.questionnaire.isSelected && this.questionnaireList.length
    }
    /**
     * 列表显示及方案问卷
     */
    get questionnaireList() {
      return this.schemeDetail.learningTypeModel.questionnaire.questionnaireConfigList.filter(
        (item) =>
          [QuestionnaireAppliedRangeTypeEnum.scheme, QuestionnaireAppliedRangeTypeEnum.online_course].includes(
            item.appliedRangeType
          ) && item.isAssessed
      )
    }
    /**
     * 考核规则数组
     */
    get assessmentRuleList() {
      const ruleList: string[] = []
      if (this.enableCourseLearning) {
        ruleList.push('courseLearning')
      }
      if (this.enableExamLearning) {
        ruleList.push('exam')
      }
      if (this.enableExperienceLearning) {
        ruleList.push('learningExperience')
      }
      if (this.questionnaireStandard) {
        ruleList.push('questionnaire')
      }
      if (this.issuePeriodStandard) {
        ruleList.push('issue')
      }
      return ruleList.map((item, index) => {
        return {
          type: item,
          no: index + 1
        }
      })
    }
    /**
     * 考核规则序号
     */
    get assessmentRuleNo() {
      return (type: string) => {
        return this.assessmentRuleList.find((item) => item.type === type)?.no
      }
    }

    /**
     * 获取整班重学配置信息
     */
    getProvideRelearn() {
      return '不提供'
    }

    /**
     * 获取培训成果说明文字
     */
    getLearningResultInfo() {
      return this.hasLearningResult ? '提供培训证明，达到培训要求后可打印' : '不提供培训证明'
    }

    /**
     * 获取培训证明名称
     */
    async getTrainingCertificateName() {
      // TODO 根据培训证明id获取名称
      this.page.pageNo = 1
      this.page.pageSize = 1
      const pageData = await this.getAllTemeplate()
      for (let i = 0; i < pageData.length; i++) {
        const element = pageData[i]
        if (this.schemeDetail.trainClassBaseInfo.learningResultId === element.id) {
          this.templateName = element.name
        }
      }
    }
    async getAllTemeplate(proResult?: Array<CertificateTemplateResponseVo>) {
      const result = proResult ? proResult : new Array<CertificateTemplateResponseVo>()
      const response = await this.queryCertificateTemplateList.queryCertificateTemplateList(this.page)

      result.push(...response.data)
      if (this.page.totalSize === result.length) {
        return result
      } else {
        ++this.page.pageNo
        await this.getAllTemeplate(result)
      }
      return result
    }
  }
</script>
