<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--点击批量同意退款确认-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20 f-mb20">批量同意退款确认</el-button>
        <el-dialog title="提示" :visible.sync="dialog3" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <div class="txt">
              当前选择<span class="f-ci f-fb">10</span>笔退款订单，退款金额<span class="f-ci f-fb">¥6.44</span
              >，是否确认批量同意退款？
            </div>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确认同意退款</el-button>
          </div>
        </el-dialog>
        <!--点击批量确认退款确认-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20 f-mb20">批量确认退款确认</el-button>
        <el-dialog title="提示" :visible.sync="dialog4" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <div class="txt">
              当前选择<span class="f-ci f-fb">10</span>笔退款订单，退款金额<span class="f-ci f-fb">¥6.44</span
              >，是否确认批量同意退款？
            </div>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确认退款</el-button>
          </div>
        </el-dialog>
        <!--点击批量按钮勾选提示-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20 f-mb20">批量勾选提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog5" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <div class="txt">请先选择需批量同意退款的订单。</div>
          </div>
          <div slot="footer">
            <el-button type="primary">关闭</el-button>
          </div>
        </el-dialog>
        <!--取消退款-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">取消退款</el-button>
        <el-drawer
          title="取消退款申请"
          :visible.sync="dialog1"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              确认取消该订单的退款申请？取消后需要重新发起退款！
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="取消原因：" required>
                    <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入取消原因" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--拒绝退款-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">拒绝退款</el-button>
        <el-drawer
          title="拒绝退款申请"
          :visible.sync="dialog2"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="拒绝退款原因：" required>
                    <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入拒绝退款原因" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
