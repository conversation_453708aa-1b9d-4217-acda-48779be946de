import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum PurchaseSourceTypeEnum {
  /**
   * 门户
   */
  PurchaseSourceTypeEnumWebSchool = 1,
  /**
   * 专题
   */
  PurchaseSourceTypeEnumSpecial = 2,
  /**
   * 清洗用 门户
   */
  gateway = 'GATEWAY',
  /**
   * 清洗用 专题
   */
  training = 'TRAINING'
}
class PurchaseSourceType extends AbstractEnum<PurchaseSourceTypeEnum> {
  static enum = PurchaseSourceTypeEnum
  constructor(status?: PurchaseSourceTypeEnum) {
    super()
    this.current = status
    this.map.set(PurchaseSourceTypeEnum.PurchaseSourceTypeEnumWebSchool, '门户')
    this.map.set(PurchaseSourceTypeEnum.PurchaseSourceTypeEnumSpecial, '专题')
  }
}
