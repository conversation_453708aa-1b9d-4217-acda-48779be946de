import BatchOrderDetailBuyerInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailBuyerInfoVo'
import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
import { MetaRow, MetaSchema } from '@api/ms-gateway/ms-collectivesign-v1'
import ReplaceableTrainClassDetailVo from '@api/service/management/train-class/query/vo/ReplaceableTrainClassDetailVo'
import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'
import UserModule from '@api/service/management/user/UserModule'
import DataResolve from '@api/service/common/utils/DataResolve'

/**
 * @description 【集体报名订单】报名列表详情
 */
class BatchOrderSignUpListDetailVo {
  /**
   * 学员信息
   */
  studentInfo: BatchOrderDetailBuyerInfoVo = new BatchOrderDetailBuyerInfoVo()

  /**
   * 培训方案名称
   */
  schemeId = ''
  /**
   * 培训方案名称
   */
  schemeName = ''

  /**
   * 班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuValueNameProperty: SkuPropertyResponseVo = new SkuPropertyResponseVo()

  /**
   * 学时
   */
  period: number = null

  /**
   * 价格
   */
  price: number = null

  /**
   * 是否是人社行业
   */
  isSocietyIndustry: boolean = null

  /**
   * 培训班商品信息
   */
  commodityInfo: ReplaceableTrainClassDetailVo = new ReplaceableTrainClassDetailVo()

  /**
   * 获取培训方案
   */
  static async from(response: MetaRow): Promise<BatchOrderSignUpListDetailVo> {
    const detail = new BatchOrderSignUpListDetailVo()
    detail.studentInfo.buyerName = this.getValueByKey(response, 'userInfo_name')
    detail.studentInfo.buyerAccount = this.getValueByKey(response, 'userInfo_idCard')
    detail.studentInfo = await this.getStudentInfo(detail.studentInfo.buyerAccount, detail.studentInfo.buyerName)
    detail.studentInfo.buyerName = this.getValueByKey(response, 'userInfo_name')
    detail.studentInfo.buyerAccount = this.getValueByKey(response, 'userInfo_idCard')
    detail.schemeId = response.runtimeProperties?.find(item => item?.key === 'SCHEME_ID')?.value ?? ''
    return detail
  }

  static getValueByKey(response: MetaRow, key: string): string {
    return response.row?.find(item => item?.key === key)?.value ?? ''
  }

  /**
   * 获取学员信息
   */
  static async getStudentInfo(idCard: string, userName: string): Promise<BatchOrderDetailBuyerInfoVo> {
    const result = new BatchOrderDetailBuyerInfoVo()
    const queryRemote: QueryStudentList = UserModule.queryUserFactory.queryStudentList
    queryRemote.queryStudentIdParams.idCard = idCard
    queryRemote.queryStudentIdParams.userName = userName
    const response = await queryRemote.queryStudentIdList()
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data)) {
      const userId = response.data[0]
      console.log('userId', userId)
      const queryStudent = UserModule.queryUserFactory.queryStudentDetail(userId)
      const response1 = await queryStudent.queryDetail()
      if (response1.status?.isSuccess()) {
        result.buyerName = response1.data?.userName ?? ''
        result.buyerAccount = response1.data?.idCard ?? ''
        result.buyerId = response1.data?.userId ?? ''
        result.buyerPhone = response1.data?.phone ?? ''
      }
    }
    return result
  }
}

export default BatchOrderSignUpListDetailVo
