import LearningTypeBase from '@api/service/common/scheme/model/LearningTypeBase'

/**
 * @description 练习学习方式
 */
class PracticeLearningType extends LearningTypeBase {
  /**
   *出题类型
   * @description 1、按题库id出题，2、按课程id出题，3：同考试
   */
  type = 0
  /**
   * 出卷配置id，
   * @description 当type=3时需要提供
   */
  paperPublishConfigureId = ''
  /**
   * 是否开放题析
   */
  openDissects = false
  /**
   * 题库id集合
   * @description 当type=1时需要提供
   */
  libraryIds: string[] = []
  /**
   * 多选题漏选得分方式
   * @description 0不得分，1得全部分数，2得一半分数，3每个选项按平均得分
   */
  multipleQuestionMissScorePatterns = 0
}

export default PracticeLearningType
