import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * 管理员状态枚举
 */
export enum AdministratorStatusEnum {
  /**
   * 启用
   */
  ENABLE = 1,
  /**
   * 停用
   */
  DISABLE
}

class AdministratorStatus extends AbstractEnum<AdministratorStatusEnum> {
  static enum = AdministratorStatusEnum

  constructor(status?: AdministratorStatusEnum) {
    super()
    this.current = status
    this.map.set(AdministratorStatusEnum.DISABLE, '停用')
    this.map.set(AdministratorStatusEnum.ENABLE, '启用')
  }
}

export default AdministratorStatus
