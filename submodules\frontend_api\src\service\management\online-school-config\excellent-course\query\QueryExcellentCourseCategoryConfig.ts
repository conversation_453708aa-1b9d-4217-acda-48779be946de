import ExcellentCourseCategoryConfig from '@api/service/management/online-school-config/excellent-course/query/vo/ExcellentCourseCategoryConfig'
import Mockjs from 'mockjs'
import MsServicerSeriesV1 from '@api/ms-gateway/ms-servicer-series-v1'

/**
 * 查询课程分类配置对象
 */
class QueryExcellentCourseCategoryConfig {
  // 过滤的集合
  get list() {
    return this.cacheList
      .filter(config => !config.isRemoved)
      .sort(
        (config: ExcellentCourseCategoryConfig, config1: ExcellentCourseCategoryConfig) => config.sort - config1.sort
      )
  }

  // 缓存集合
  private cacheList: Array<ExcellentCourseCategoryConfig> = new Array<ExcellentCourseCategoryConfig>()

  /**
   * 执行查询的方法
   */
  async queryList() {
    await MsServicerSeriesV1.getExcellentCourses()
    this.cacheList = Mockjs.mock({
      'data|10': [
        {
          id: '@uuid',
          name: '@csentence(10)',
          sort: '@integer(10)'
        }
      ]
    }).data.map(ExcellentCourseCategoryConfig.from)
  }

  async doSort(from: number, to: number) {
    return true
  }
}

export default QueryExcellentCourseCategoryConfig
