<route-meta>
  {
  "isMenu": true,
  "title": "个人报名退款订单",
  "sort": 1,
  "icon": "icon_menhuxinxiguanli"
  }
  </route-meta>
<template>
  <el-main>
    <div
      class="f-p15"
      v-if="$hasPermission('query,queryfx,queryzt')"
      desc="query:查询,queryfx:查询分销,queryzt:查询专题"
      query
      actions="query:doQueryPage,@BizPortalSelect,@BizDistributorSelect#queryfx:doQueryPagefx,@FxLearningSchemeSelect,@BizPortalDistributorSelect#queryzt:doQueryPageZt"
    >
      <el-card shadow="never" class="m-card f-mb15">
        <hb-search-wrapper expand :model="returnOrderRequestVo" @reset="resetParams">
          <el-form-item label="收款账号" v-if="!isZtlogin">
            <el-input
              id="input"
              v-model="accountName"
              clearable
              placeholder="请选择收款账号"
              @focus="editInvoicePopup()"
            />
          </el-form-item>

          <el-form-item label="订单号">
            <el-input v-model="orderNo" clearable placeholder="请输入订单号" />
          </el-form-item>
          <!-- returnOrderRequestVo.subOrderInfo.orderInfo.orderNoList -->
          <el-form-item label="交易流水号">
            <el-input v-model="flowNo" clearable placeholder="请输入交易流水号" />
          </el-form-item>
          <!-- returnOrderRequestVo.subOrderInfo.orderInfo.flowNoList -->
          <el-form-item label="退款状态">
            <el-select v-model="returnOrderRequestVo.returnStatusVo" clearable placeholder="请选择">
              <el-option
                v-for="status in ReturnStatusFilterOptions"
                :key="status"
                :label="OrderRefundStatus.map.get(status)"
                :value="status"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="登录账号" v-if="queryShowLoginAccount.isShowLoginAccount">
            <el-input v-model="returnOrderRequestVo.loginAccount" clearable placeholder="请输入省平台ID" />
          </el-form-item>
          <!-- 证件号、姓名字段未对接 -->
          <el-form-item label="证件号">
            <el-input v-model="returnOrderRequestVo.idCard" clearable placeholder="请输入证件号" />
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="returnOrderRequestVo.name" clearable placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item label="缴费渠道">
            <el-select v-model="paymentChannels" clearable filterable placeholder="请选择">
              <el-option label="web端" value="1"></el-option>
              <el-option label="H5" value="2"></el-option>
              <el-option label="导入开通" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="培训方案" v-if="!isFxlogin && !isZtlogin">
            <biz-learning-scheme-select v-model="trainingProgramme"></biz-learning-scheme-select>
          </el-form-item>

          <el-form-item label="培训方案" v-if="isFxlogin">
            <fx-learning-scheme-select v-model="trainingProgramme"></fx-learning-scheme-select>
          </el-form-item>
          <el-form-item label="培训方案" v-if="isZtlogin">
            <zt-learning-scheme-select v-model="trainingProgramme"></zt-learning-scheme-select>
          </el-form-item>
          <el-form-item label="期别名称" v-if="showPeriodName && !isFxlogin">
            <biz-period-select :scheme-id="trainingProgramme[0].id" v-model="returnOrderRequestVo.periodId" />
          </el-form-item>
          <el-form-item label="期别名称" v-if="showPeriodName && isFxlogin">
            <biz-fx-period-select :scheme-id="trainingProgramme[0].id" v-model="returnOrderRequestVo.periodId" />
          </el-form-item>
          <el-form-item label="销售渠道" v-if="!isFxlogin && !isZtlogin">
            <el-select v-model="returnOrderRequestVo.saleSource" clearable filterable placeholder="请选择销售渠道">
              <el-option
                v-for="item in SaleChannelType"
                :key="item.code"
                :value="item.code"
                :label="item.desc"
              ></el-option>
            </el-select>
          </el-form-item>

          <!--  v-if="isFXshow" -->
          <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="分销商">
            <biz-distributor-select
              v-model="returnOrderRequestVo.distributorId"
              :name="distributorName"
            ></biz-distributor-select>
          </el-form-item>
          <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
            <biz-portal-select
              v-model="returnOrderRequestVo.portalId"
              :disabled="returnOrderRequestVo.isDistributionExcludePortal"
              :name="promotionPortalName"
            ></biz-portal-select>
          </el-form-item>
          <el-form-item v-if="isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
            <biz-portal-distributor-select
              v-model="returnOrderRequestVo.portalId"
              :disabled="returnOrderRequestVo.isDistributionExcludePortal"
              :name="promotionPortalName"
            ></biz-portal-distributor-select>
          </el-form-item>
          <el-form-item v-if="!isZtlogin">
            <el-checkbox
              label="查看非门户推广数据"
              name="type"
              @change="returnOrderRequestVo.portalId = ''"
              v-model="returnOrderRequestVo.isDistributionExcludePortal"
            ></el-checkbox>
          </el-form-item>

          <el-form-item label="专题名称" v-if="topPicNameFilterShow && !isFxlogin">
            <el-input
              v-model="returnOrderRequestVo.subOrderInfo.orderInfo.saleChannelName"
              clearable
              placeholder="请输入专题进行查询"
            />
          </el-form-item>

          <!--  v-if="isFXshow" -->
          <el-form-item label="退款申请时间">
            <double-date-picker
              :begin-create-time.sync="returnOrderRequestVo.basicData.returnStatusChangeTime.applied.begin"
              :end-create-time.sync="returnOrderRequestVo.basicData.returnStatusChangeTime.applied.end"
            ></double-date-picker>
          </el-form-item>
          <el-form-item label="审批时间">
            <double-date-picker
              :begin-create-time.sync="returnOrderRequestVo.approvalInfo.approveTime.begin"
              :end-create-time.sync="returnOrderRequestVo.approvalInfo.approveTime.end"
            ></double-date-picker>
          </el-form-item>
          <el-form-item label="退款成功时间">
            <double-date-picker
              :begin-create-time.sync="returnOrderRequestVo.basicData.returnStatusChangeTime.returnCompleted.begin"
              :end-create-time.sync="returnOrderRequestVo.basicData.returnStatusChangeTime.returnCompleted.end"
            ></double-date-picker>
          </el-form-item>
          <el-form-item label="退货/款类别">
            <biz-refund-category v-model="returnOrderRequestVo.refundType" :is-diff="true"></biz-refund-category>
          </el-form-item>

          <template slot="actions">
            <el-button type="primary" @click="search">查询</el-button>
          </template>
        </hb-search-wrapper>
        <div class="more-actions">
          <template v-if="$hasPermission('batchAgreeRefund')" mutation desc="批量同意退款" actions="handleBatchAgree">
            <el-button type="primary" v-if="!isZtlogin" @click="showBatchDialog(true)">批量同意退款</el-button>
          </template>
          <template
            v-if="$hasPermission('batchConfirmRefund')"
            mutation
            desc="批量确认退款"
            actions="handleBatchConfirm"
          >
            <el-button type="primary" v-if="!isZtlogin" @click="showBatchDialog(false)">批量确认退款</el-button>
          </template>
          <template
            v-if="$hasPermission('export,exportfx,exportzt')"
            desc="export:导出,exportfx:导出（分销）,exportzt:导出（专题）"
            query
            actions="export:exportRefundty#exportfx:exportRefundfx#exportzt:exportRefundZt"
          >
            <el-button @click="exportRefund">导出列表数据</el-button>
          </template>
        </div>
        <!--操作栏-->
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert">
            <div class="f-c6">
              当前共有
              <span class="f-fb f-co">{{
                isZtlogin
                  ? queryZtRefund.returnOrderStatisic.totalReturnOrderCount
                  : queryRefundOrder.returnOrderStatisic.totalReturnOrderCount || 0
              }}</span>
              笔退款订单，退款金额
              <span class="f-fb f-co"
                >¥
                {{
                  isZtlogin
                    ? queryZtRefund.returnOrderStatisic.totalRefundAmount
                    : queryRefundOrder.returnOrderStatisic.totalRefundAmount || 0
                }}</span
              >。
            </div>
          </el-alert>
        </div>
        <!--表格-->
        <el-table
          ref="returnOrderTable"
          :data="returnOrderResponseVo"
          max-height="500px"
          @selection-change="tableSelect"
          class="m-table f-mt10"
          v-loading="query.loading"
        >
          <el-table-column v-if="!isZtlogin" type="selection" width="55" align="center" fixed="left"></el-table-column>
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="订单号" min-width="220">
            <template slot-scope="scope">
              <!-- <div v-if="scope.$index === 0"> -->
              <div>
                {{ scope.row.subOrderInfo.orderInfo.orderNo }}
                <hb-copy :content="scope.row.subOrderInfo.orderInfo.orderNo"></hb-copy>
              </div>
              <!-- <el-tag type="primary" v-if="scope.row.subOrderInfo.exchanged" size="small">换班</el-tag> -->
              <el-tag
                type="primary"
                v-if="scope.row.changeOrderStatus && scope.row.changeOrderStatus.includes(ChangeOrderType.CLASS_TYPE)"
                size="small"
                >换班</el-tag
              >
              <el-tag
                type="warning"
                size="small"
                v-if="scope.row.changeOrderStatus && scope.row.changeOrderStatus.includes(ChangeOrderType.PERIOD_TYPE)"
                >换期</el-tag
              >
              <el-tag type="warning" size="small" v-if="scope.row.commodityAuthInfo">分销推广</el-tag>
              <el-tag type="success" size="small" v-if="scope.row.subOrderInfo.saleChannel == SaleChannelEnum.topic"
                >专题</el-tag
              >
              <el-tag type="danger" size="small" v-if="scope.row.thirdPartyPlatform">华医网</el-tag>
              <!-- </div> -->
            </template>
          </el-table-column>
          <el-table-column label="交易流水号" prop="subOrderInfo.orderInfo.orderPaymentInfo.flowNo" min-width="300">
          </el-table-column>
          <el-table-column label="退款物品" min-width="280">
            <template slot-scope="scope">
              <div v-if="isHywSpecialScheme(scope.row) && scope.row.courseType == 2">2025年公需课</div>
              <div v-else-if="isHywSpecialScheme(scope.row) && scope.row.courseType == 1">华医网2025年专业课</div>
              <div v-else>
                {{ scope.row.commodityDto.commoditySku.saleTitle || '-' }}
              </div>
              <p v-if="scope.row.trainingPeriodName">
                <el-tag type="info" size="mini">培训期别</el-tag>
                {{ scope.row.trainingPeriodName }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="退货商品数量" min-width="120" prop="commodityDto.quantity" align="center">
          </el-table-column>
          <el-table-column
            label="实付金额(元)"
            width="140"
            prop="subOrderInfo.orderInfo.orderPaymentInfo.payAmount"
            align="right"
          >
          </el-table-column>
          <el-table-column label="退款金额(元)" width="140" prop="basicData.refundAmount" align="right">
          </el-table-column>
          <el-table-column label="购买人信息" min-width="240">
            <template slot-scope="scope">
              <p>姓名：{{ scope.row.buyer.userName || '-' }}</p>
              <p v-if="queryShowLoginAccount.isShowLoginAccount">登录账号：{{ scope.row.buyer.loginAccount }}</p>
              <p>证件号：{{ scope.row.buyer.idCard || '-' }}</p>
              <p>手机号：{{ scope.row.buyer.phone || '-' }}</p>
            </template>
          </el-table-column>
          <el-table-column label="申请时间 / 审批时间" min-width="220">
            <template slot-scope="scope">
              <p>申请：{{ scope.row.basicData.returnOrderStatusChangeTime.applied }}</p>
              <p v-if="scope.row.approvalInfo">审批：{{ scope.row.approvalInfo.approveTime || '-' }}</p>
            </template>
          </el-table-column>
          <el-table-column label="退款成功时间" min-width="180" prop="refundTime">
            <template slot-scope="scope">
              <div v-if="scope.row.basicData.returnOrderType === 3 || scope.row.basicData.returnOrderType === 2">
                {{ scope.row.basicData.returnOrderStatusChangeTime.returnedAndRefunded || '-' }}
              </div>
              <div v-else>{{ scope.row.basicData.returnOrderStatusChangeTime.returnCompleted || '-' }}</div>
            </template>
            >
          </el-table-column>
          <el-table-column label="退货/款状态" min-width="150">
            <template slot-scope="scope">
              <el-badge
                is-dot
                :type="returnOrderStatusClassName(scope.row).type"
                class="badge-status"
                v-if="OrderRefundStatus.map.get(returnOrderStatusClassName(scope.row).status)"
              >
                {{ OrderRefundStatus.map.get(returnOrderStatusClassName(scope.row).status) }}
              </el-badge>
              <!-- <div v-if="scope.row.UIReturnOrderStatue === 0">
                <el-badge is-dot type="primary" class="badge-status">退款审批中</el-badge>
              </div>
              <div v-else-if="scope.row.UIReturnOrderStatue === 1">
                <el-badge is-dot type="primary" class="badge-status">退款处理中</el-badge>
              </div>
              <div v-else-if="scope.row.UIReturnOrderStatue === 2">
                <el-badge is-dot type="danger" class="badge-status">拒绝退款申请</el-badge>
              </div>
              <div v-else-if="scope.row.UIReturnOrderStatue === 3">
                <el-badge is-dot type="danger" class="badge-status">已取消</el-badge>
              </div>
              <div v-else-if="scope.row.UIReturnOrderStatue === 4">
                <el-badge is-dot type="success" class="badge-status">退款成功</el-badge>
              </div>
              <div v-else-if="scope.row.UIReturnOrderStatue === 5">
                <el-badge is-dot type="danger" class="badge-status">退款失败</el-badge>
              </div> -->
            </template>
          </el-table-column>
          <el-table-column label="退货/款类型" width="150">
            <template slot-scope="scope">
              {{ OrderRefundType.map.get(scope.row.basicData.returnOrderType) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" :width="useBigWidth ? '260' : '80'" align="center" fixed="right">
            <template slot-scope="scope">
              <template v-if="$hasPermission('refundDetail')" query desc="退款详情(入口控制)">
                <el-button type="text" size="mini" @click="goDetail(scope.row.returnOrderNo)">详情</el-button>
              </template>
              <template
                v-if="$hasPermission('refundApproval')"
                desc="退款审批"
                mutation
                actions="cancelRefund,refuseRefund,agreeRefund,doReturn"
              >
                <span v-if="scope.row.UIReturnOrderStatue === 0 && !isZtlogin">
                  <el-button type="text" size="mini" @click="openCancelRefund(scope.row)" :loading="query.loading"
                    >取消退货/款</el-button
                  >
                  <el-button type="text" size="mini" @click="openRefuseRefund(scope.row)" :loading="query.loading"
                    >拒绝退货/款</el-button
                  >
                  <el-button
                    type="text"
                    size="mini"
                    v-if="isWft(scope.row)"
                    @click="wftDialog(scope.row)"
                    :loading="query.loading"
                    >同意退货/款</el-button
                  >
                  <el-button type="text" size="mini" v-else @click="agreeRefund(scope.row)" :loading="query.loading"
                    >同意退货/款</el-button
                  >
                </span>
                <span v-else-if="scope.row.UIReturnOrderStatue === 1 && !isZtlogin && scope.row.canConfirmReturn">
                  <el-button type="text" size="mini" @click="doReturn(scope.row)" :loading="query.loading"
                    >确认退款</el-button
                  >
                </span>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
        <!-- 收款账号组件 -->
        <template v-if="$hasPermission('editInvoicePopup')" query desc="选择收款账号" actions="@accountNumber">
          <account-number
            :visible.sync="editInvoiceDialog"
            ref="accountNumberRef"
            :get-data="getData"
            @getAccountNumber="getAccountNumber"
          ></account-number>
        </template>
        <el-drawer title="取消退款申请" :visible.sync="cancelRefundDialog" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              确认取消该订单的退款申请？取消后需要重新发起退款！
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="取消原因：" required>
                    <el-input type="textarea" :rows="6" v-model="cancelReason" placeholder="请输入取消原因" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button @click="cancelRefundDialog = false">取消</el-button>
                    <el-button type="primary" @click="cancelRefund">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <el-drawer title="拒绝退款申请" :visible.sync="refuseRefundDialog" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="拒绝退款原因：" required>
                    <el-input type="textarea" :rows="6" v-model="cancelReason" placeholder="请输入拒绝退款原因" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button @click="refuseRefundDialog = false">取消</el-button>
                    <el-button type="primary" @click="refuseRefund">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务管理-个人报名订单退款</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
          </div>
        </el-dialog>
        <el-dialog title="提示" :visible.sync="showBatchAgree" width="450px" class="m-dialog">
          <div class="f-c6">
            当前选择 <span class="f-fb f-co">{{ selectTotal.agreeTotal }}</span> 笔退款订单， 退款金额
            <span class="f-fb f-co">¥ {{ selectTotal.agreeSum }}</span
            >，是否确认批量同意退款？
          </div>
          <div slot="footer">
            <el-button @click="showBatchAgree = false">取消</el-button>
            <el-button :loading="btnLoading" type="primary" @click="handleBatchAgree">确认同意退款</el-button>
          </div>
        </el-dialog>
        <el-dialog title="提示" :visible.sync="showBatchConfirm" width="450px" class="m-dialog">
          <div class="f-c6">
            当前选择 <span class="f-fb f-co">{{ selectTotal.confirmTotal }}</span> 笔退款订单， 退款金额
            <span class="f-fb f-co">¥ {{ selectTotal.confirmSum }}</span
            >，是否确认批量同意退款？
          </div>
          <div slot="footer">
            <el-button @click="showBatchConfirm = false">取消</el-button>
            <el-button :loading="btnLoading" type="primary" @click="handleBatchConfirm">确认退款</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
  import ReturnOrderResponseVo from '@api/service/diff/management/fjzj/trade/order/model/ReturnOrderResponseVo'
  // import MutationReturnOrder from '@api/service/management/trade/single/order/mutation/MutationReturnOrder'
  import MutationReturnOrder from '@api/service/diff/management/fjzj/trade/single/order/mutation/MutationReturnOrder'
  import accountNumber from '@hbfe/jxjy-admin-trade/src/refund/components/account-number.vue'
  import ReceiveAccountVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountVo'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/fjzj/trade/enums/SaleChannelType'
  import QueryRefundList from '@api/service/diff/management/fjzj/trade/order/QueryRefundList'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import { ElTable } from 'element-ui/types/table'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'

  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import FxLearningSchemeSelect from '@hbfe/fx-manage/src/components/biz/biz-learning-scheme-select.vue'
  import { bind, debounce } from 'lodash-decorators'
  import ZtLearningSchemeSelect from '@hbfe/jxjy-admin-trade/src/order/personal/components/zt-learning-scheme-select.vue'
  import { ChangeOrderType } from '@api/service/common/trade/ChangeOrderType'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import QueryRefundListInTrainingChannel from '@api/service/diff/management/fjzj/trade/single/order/query/QueryRefundListInTrainingChannel'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplicationDiff } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import OrderRefundStatus, {
    ReturnStatusFilterOptions
  } from '@api/service/common/return-order/enums/OrderRefundStatus'
  import OrderRefundType from '@api/service/common/return-order/enums/OrderRefundType'
  import { OrderReturnPair } from '@api/ms-gateway/ms-return-order-v1'

  @Component({
    components: {
      DoubleDatePicker,
      accountNumber,
      BizDistributorSelect,
      BizPortalSelect,
      FxLearningSchemeSelect,
      BizPortalDistributorSelect,
      ZtLearningSchemeSelect
    }
  })
  export default class extends Vue {
    SaleChannelEnum = SaleChannelEnum
    @Ref('returnOrderTable')
    returnOrderTable: ElTable
    input = ''
    // 页面分页控件
    page: UiPage
    // 分页查询
    query: Query = new Query()
    //缴费渠道
    paymentChannels = ''
    //控制侧边栏宽度
    useBigWidth = false
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    form = {
      data1: ''
    }
    //接口查询参数
    returnOrderRequestVo: ReturnOrderRequestVo = new ReturnOrderRequestVo()
    //导出查询参数
    exportQueryParam: ReturnOrderRequestVo = new ReturnOrderRequestVo()
    //查询接口请求
    queryRefundOrder: QueryRefundList = new QueryRefundList()
    //业务接口请求
    mutationReturnOrder: MutationReturnOrder = new MutationReturnOrder()
    //查询接口结果
    returnOrderResponseVo: Array<ReturnOrderResponseVo> = new Array<ReturnOrderResponseVo>()
    //取消退款弹窗标识
    cancelRefundDialog = false
    //拒绝退款弹窗标识
    refuseRefundDialog = false
    //批量同意退款弹窗标识
    showBatchAgree = false
    //批量确认退款弹窗标识
    showBatchConfirm = false
    //确认按钮loading
    btnLoading = false
    //退款原因
    cancelReason = ''
    //退款当前项数据
    currentItem: ReturnOrderResponseVo = new ReturnOrderResponseVo()
    //退款状态
    returnOrderStatus = ''
    //培训方案
    trainingProgramme: Array<HasSelectSchemeMode> = []
    //订单号
    orderNo = ''
    //流水号
    flowNo = ''
    //选择列表
    selectdList: Array<ReturnOrderResponseVo> = new Array<ReturnOrderResponseVo>()
    //选择列表订单号
    selectdNoList: string[] = []
    //专题查询请求
    queryZtRefund = new QueryRefundListInTrainingChannel()
    ChangeOrderType = ChangeOrderType
    //退货状态
    OrderRefundStatus = OrderRefundStatus
    ReturnStatusFilterOptions = ReturnStatusFilterOptions
    // 退款类型
    OrderRefundType = OrderRefundType
    //收款账号Ref
    @Ref('accountNumberRef') accountNumberRef: any
    /**
     * 打开-弹窗标识
     */
    editInvoiceDialog = false
    accountName = ''
    getData = new ReceiveAccountVo()
    //打开导出标识
    exportSuccessVisible = false
    // 获取销售渠道列表
    SaleChannelType = SaleChannelType.list()
    TrainingModeEnum = TrainingModeEnum

    get showPeriodName() {
      return [this.TrainingModeEnum.mixed, this.TrainingModeEnum.offline].includes(
        this.trainingProgramme[0]?.trainingMode?.skuPropertyValueId
      )
    }

    @Watch('trainingProgramme', { deep: true })
    trainingProgrammeChange() {
      this.returnOrderRequestVo.periodId = ''
    }
    @Watch('returnOrderRequestVo.saleSource')
    sourceFilterChange() {
      this.$set(this.returnOrderRequestVo.subOrderInfo.orderInfo, 'saleChannelName', '')
    }
    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount

    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    promotionPortalName = ''
    distributorName = ''

    /**
     * 获取华医网特殊方案
     */
    get hywSchemeList(): string[] {
      const list: { fjzjSchemeId: string }[] = JSON.parse(
        ConfigCenterModule.getFrontendApplicationDiff(frontendApplicationDiff.hywSchemeId)
      )
      return list.map((item) => item.fjzjSchemeId)
    }

    /**
     * 是否是华医网特殊方案
     */
    get isHywSpecialScheme(): (item: ReturnOrderResponseVo) => boolean {
      const schemeIds = this.hywSchemeList
      return (item: ReturnOrderResponseVo) => {
        const schemeId = (item.returnCommodity.commoditySku.resource as any)?.schemeId
        return schemeIds.includes(schemeId)
      }
    }

    // 专题名称筛选显示
    get topPicNameFilterShow() {
      return (
        this.returnOrderRequestVo.saleSource === SaleChannelEnum.topic ||
        (!this.returnOrderRequestVo.saleSource && this.returnOrderRequestVo.saleSource !== SaleChannelEnum.self)
      )
    }
    /**
     * 退货状态样式
     */
    get returnOrderStatusClassName() {
      return (item: ReturnOrderResponseVo) => {
        // const obj = {
        //   type: '',
        //   status: 0
        // }
        // if ([1, 4, 5, 8, 9].includes(item.basicData.returnOrderStatus)) {
        //   return 'primary'
        // } else if ([6, 10, 12].includes(item.basicData.returnOrderStatus)) {
        //   return 'success'
        // } else if ([2, 3, 7, 11].includes(item.basicData.returnOrderStatus)) {
        //   return 'danger'
        // } else {
        //   return '' // 或者其他默认值
        // }
        let type = ''
        const status = OrderRefundStatus.transferDtoToCurrentEnum(
          item.basicData.returnOrderStatus,
          item.basicData.returnCloseReason.closeType
        )
        if ([1, 4, 5, 8, 9].includes(status)) {
          type = 'primary'
        } else if ([6, 10, 12].includes(status)) {
          type = 'success'
        } else if ([2, 3, 7, 11].includes(status)) {
          type = 'danger'
        } else {
          type = '' // 或者其他默认值
        }
        return { type, status }
      }
    }
    //计算选择列表金额
    get selectTotal() {
      console.log(this.selectdList, 'selectdList')
      let agreeTotal = 0
      let agreeSum = 0
      let confirmTotal = 0
      let confirmSum = 0
      this.selectdList.forEach((item: ReturnOrderResponseVo) => {
        if (item.UIReturnOrderStatue === 0) {
          agreeTotal++
          agreeSum = CalculatorObj.add(agreeSum, item.basicData.refundAmount)
        }
        if (item.UIReturnOrderStatue === 1) {
          confirmTotal++
          confirmSum = CalculatorObj.add(confirmSum, item.basicData.refundAmount)
        }
      })
      return { agreeTotal, agreeSum, confirmTotal, confirmSum }
    }

    tableSelect(selection: ReturnOrderResponseVo[]) {
      this.selectdList = selection
    }

    constructor() {
      super()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.page = new UiPage(this.doQueryPagefx, this.doQueryPagefx)
      } else if (this.isZtlogin) {
        this.page = new UiPage(this.doQueryPageZt, this.doQueryPageZt)
      } else {
        this.page = new UiPage(this.doQueryPage, this.doQueryPage)
      }
    }

    async doQueryPage() {
      this.query.loading = true
      // this.page.pageNo = 2
      try {
        this.returnOrderResponseVo = [] as ReturnOrderResponseVo[]
        this.returnOrderRequestVo.basicData.applySourceType = 'SUB_ORDER'
        this.returnOrderResponseVo = await this.queryRefundOrder.queryRefundOrderList(
          this.page,
          this.returnOrderRequestVo
        )
      } catch (e) {
        console.log(e, '加载个人报名退款订单失败')
      } finally {
        this.query.loading = false
        this.page.totalSize = this.queryRefundOrder.totalSize
        console.log(this.returnOrderResponseVo, 'this.returnOrderResponseVo')
        //侧边栏宽度自适应
        this.useBigWidth = false
        this.returnOrderResponseVo.forEach((item) => {
          if (item.UIReturnOrderStatue === 0) {
            this.useBigWidth = true
          }
        })
        // this.$nextTick(async () => {
        //   //处理切换页数后行数错位问题
        //   const ref = this.$refs['returnOrderTable'] as any
        //   ref && (await ref?.doLayout())
        //   console.log(this.$refs['returnOrderTable'], 'llool')
        // })
      }
      this.returnOrderTable.doLayout()
    }
    async search() {
      this.initSetting()
      // if(this.returnOrderRequestVo.)
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else if (this.isZtlogin) {
        await this.doQueryPageZt()
      } else {
        await this.doQueryPage()
      }
    }
    async doQueryPageZt() {
      this.query.loading = true
      // this.page.pageNo = 2
      try {
        this.returnOrderResponseVo = [] as ReturnOrderResponseVo[]
        this.returnOrderRequestVo.basicData.applySourceType = 'SUB_ORDER'
        this.returnOrderResponseVo = await this.queryZtRefundOrderList()
      } catch (e) {
        console.log(e, '加载个人报名退款订单失败')
      } finally {
        this.query.loading = false
        this.page.totalSize = this.queryZtRefund.totalSize
        console.log(this.returnOrderResponseVo, 'this.returnOrderResponseVo')
        //侧边栏宽度自适应
        this.useBigWidth = false
        this.returnOrderResponseVo.forEach((item) => {
          if (item.UIReturnOrderStatue === 0) {
            this.useBigWidth = true
          }
        })
        // this.$nextTick(async () => {
        //   //处理切换页数后行数错位问题
        //   const ref = this.$refs['returnOrderTable'] as any
        //   ref && (await ref?.doLayout())
        //   console.log(this.$refs['returnOrderTable'], 'llool')
        // })
      }
      this.returnOrderTable.doLayout()
    }
    /**
     * 查询分销退款订单
     */
    async queryZtRefundOrderList() {
      return await this.queryZtRefund.queryRefundOrderListDiff(this.page, this.returnOrderRequestVo)
    }
    initSetting() {
      this.page.pageNo = 1
      this.returnOrderRequestVo.basicData.returnCloseReason.closeTypeList = []

      if (this.paymentChannels === '1') {
        this.returnOrderRequestVo.subOrderInfo.orderInfo.terminalCodeList = ['Web']
        this.returnOrderRequestVo.subOrderInfo.orderInfo.channelTypesList = [1]
      } else if (this.paymentChannels === '2') {
        this.returnOrderRequestVo.subOrderInfo.orderInfo.terminalCodeList = ['WechatOfficial', 'H5']
        this.returnOrderRequestVo.subOrderInfo.orderInfo.channelTypesList = [1]
      } else if (this.paymentChannels === '3') {
        this.returnOrderRequestVo.subOrderInfo.orderInfo.terminalCodeList = ['Web']
        this.returnOrderRequestVo.subOrderInfo.orderInfo.channelTypesList = [3]
      } else {
        this.returnOrderRequestVo.subOrderInfo.orderInfo.terminalCodeList = []
        this.returnOrderRequestVo.subOrderInfo.orderInfo.channelTypesList = []
      }
      if (this.returnOrderStatus === '0') {
        this.returnOrderRequestVo.basicData.returnOrderStatus = [0, 1]
      } else if (this.returnOrderStatus === '1') {
        this.returnOrderRequestVo.basicData.returnOrderStatus = [11]
        this.returnOrderRequestVo.basicData.returnCloseReason.closeTypeList = [3]
      } else if (this.returnOrderStatus === '2') {
        this.returnOrderRequestVo.basicData.returnOrderStatus = [2, 3, 4, 5, 6]
      } else if (this.returnOrderStatus === '3') {
        this.returnOrderRequestVo.basicData.returnOrderStatus = [8, 9, 10]
      } else if (this.returnOrderStatus === '4') {
        this.returnOrderRequestVo.basicData.returnOrderStatus = [11]
        this.returnOrderRequestVo.basicData.returnCloseReason.closeTypeList = [1, 2]
      } else if (this.returnOrderStatus === '5') {
        this.returnOrderRequestVo.basicData.returnOrderStatus = [7]
      } else {
        this.returnOrderRequestVo.basicData.returnOrderStatus = []
      }
      if (this.trainingProgramme) {
        this.returnOrderRequestVo.returnCommoditySkuIdList =
          this.trainingProgramme.length > 0 ? [this.trainingProgramme[0].schemeId] : []
      }

      if (this.orderNo) {
        this.returnOrderRequestVo.subOrderInfo.orderInfo.orderNoList = [this.orderNo]
      } else {
        this.returnOrderRequestVo.subOrderInfo.orderInfo.orderNoList = []
      }
      if (this.flowNo) {
        this.returnOrderRequestVo.subOrderInfo.orderInfo.flowNoList = [this.flowNo]
      } else {
        this.returnOrderRequestVo.subOrderInfo.orderInfo.flowNoList = []
      }
    }

    async doQueryPagefx() {
      this.query.loading = true
      // this.page.pageNo = 2
      try {
        this.returnOrderResponseVo = [] as ReturnOrderResponseVo[]
        this.returnOrderRequestVo.basicData.applySourceType = 'SUB_ORDER'
        this.returnOrderResponseVo = await this.queryRefundOrder.queryFxRefundOrderList(
          this.page,
          this.returnOrderRequestVo
        )
      } catch (e) {
        console.log(e, '加载个人报名退款订单失败')
      } finally {
        this.query.loading = false
        this.page.totalSize = this.queryRefundOrder.totalSize
        console.log(this.returnOrderResponseVo, 'this.returnOrderResponseVo')
        //侧边栏宽度自适应
        this.useBigWidth = false
        this.returnOrderResponseVo.forEach((item) => {
          if (item.UIReturnOrderStatue === 0) {
            this.useBigWidth = true
          }
        })
        // this.$nextTick(async () => {
        //   //处理切换页数后行数错位问题
        //   const ref = this.$refs['returnOrderTable'] as any
        //   ref && (await ref?.doLayout())
        //   console.log(this.$refs['returnOrderTable'], 'llool')
        // })
      }
      this.returnOrderTable.doLayout()
    }

    async searchFX() {
      this.initSetting()
      // if(this.returnOrderRequestVo.)
      await this.doQueryPagefx()
    }
    async exportRefundty() {
      return await this.queryRefundOrder.exportReturnOrderExcelInServicer(this.exportQueryParam)
    }

    async exportRefundfx() {
      return await this.queryRefundOrder.exportFxReturnOrderExcelInDistributor(this.exportQueryParam)
    }
    async exportRefundZt() {
      return await this.queryZtRefund.exportReturnOrderExcelInServicer(this.exportQueryParam)
    }

    async resetParams() {
      this.page.pageNo = 1
      this.trainingProgramme = []
      this.returnOrderRequestVo.periodId = ''
      this.paymentChannels = ''
      this.accountName = ''
      this.orderNo = ''
      this.flowNo = ''
      this.returnOrderStatus = ''
      this.promotionPortalName = ''
      this.distributorName = ''
      this.returnOrderRequestVo = new ReturnOrderRequestVo()
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else if (this.isZtlogin) {
        await this.doQueryPageZt()
      } else {
        await this.doQueryPage()
      }
    }
    goDetail(id: string) {
      this.$router.push('/training/trade/refund/personal/detail/' + id)
    }

    /*
     * 取消退款
     * */
    @bind
    @debounce(200)
    async cancelRefund() {
      if (!this.cancelReason) {
        this.$message.error('请填写取消退款原因')
        return
      }
      this.query.loading = true
      this.mutationReturnOrder.returnOrderNo = this.currentItem.returnOrderNo
      this.mutationReturnOrder.note = this.cancelReason
      this.mutationReturnOrder.orderNo = this.currentItem.subOrderInfo.orderInfo.orderNo
      let status
      // 如果是差异化方案，调用差异化方法
      if (this.isHywSpecialScheme(this.currentItem)) {
        status = await this.mutationReturnOrder.cancelReturnApplyDiff()
      } else {
        status = await this.mutationReturnOrder.cancelReturnApply()
      }
      if (!status.isSuccess()) {
        this.query.loading = false

        this.$message.error('取消退款失败')
        return
      }
      this.query.loading = false
      this.cancelRefundDialog = false
      this.$message.success('取消退款成功')
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else {
        await this.doQueryPage()
      }
    }
    /*
     * 拒绝退款
     * */
    async refuseRefund() {
      if (!this.cancelReason) {
        this.$message.error('请填写拒绝退款原因')
        return
      }
      this.query.loading = true
      this.mutationReturnOrder.returnOrderNo = this.currentItem.returnOrderNo
      this.mutationReturnOrder.note = this.cancelReason
      this.mutationReturnOrder.orderNo = this.currentItem.subOrderInfo.orderInfo.orderNo
      let status
      // 如果是差异化方案，调用差异化方法
      if (this.isHywSpecialScheme(this.currentItem)) {
        status = await this.mutationReturnOrder.rejectReturnApplyDiff()
      } else {
        status = await this.mutationReturnOrder.rejectReturnApply()
      }
      if (!status.isSuccess()) {
        this.query.loading = false
        this.$message.error('拒绝退款失败')
        return
      }
      this.query.loading = false
      this.refuseRefundDialog = false
      this.$message.success('拒绝退款成功')
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else {
        await this.doQueryPage()
      }
    }

    /**
     * 判断是否是威富通以及线下
     */
    isWft(item: ReturnOrderResponseVo) {
      if (item.subOrderInfo.orderInfo.orderPaymentInfo.flowNo.includes('WFT') && item.refundInfo.refundOrderType == 1) {
        return true
      } else {
        return false
      }
    }

    /**
     * 威富通退款点击事件
     * @param item
     */
    wftDialog(item: ReturnOrderResponseVo) {
      this.$alert('兴业聚合支付（威富通）线上退款到账需要1~3个工作日，确认退款吗?', '提示', {
        confirmButtonText: '确认',
        type: 'warning'
      }).then(() => {
        this.agreeRefund(item)
      })
    }
    /*
     * 同意退款
     * */
    async agreeRefund(item: ReturnOrderResponseVo) {
      this.query.loading = true
      // try {
      this.mutationReturnOrder.returnOrderNo = item.returnOrderNo
      this.mutationReturnOrder.orderNo = item.subOrderInfo.orderInfo.orderNo
      let status
      // 如果是差异化方案，调用差异化方法
      if (this.isHywSpecialScheme(item)) {
        status = await this.mutationReturnOrder.agreeReturnApplyDiff()
      } else {
        status = await this.mutationReturnOrder.agreeReturnApply()
      }
      // } catch (e) {
      //   console.log(e, '取消退款失败')
      // } finally {
      if (!status.isSuccess()) {
        this.query.loading = false
        this.$message.error('同意退款失败')
        return
      }
      this.$message.success('同意退款成功')
      this.query.loading = false
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else {
        await this.doQueryPage()
      }
      // }
    }
    async editInvoicePopup() {
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.accountNumberRef.doQueryPagefx()
      } else {
        await this.accountNumberRef.doQueryPage()
      }
      const inputEl = document.getElementById('input')
      inputEl.blur()
      this.editInvoiceDialog = true
    }
    getAccountNumber(idList: ReceiveAccountVo[]) {
      this.getData = idList[0]
      this.returnOrderRequestVo.subOrderInfo.orderInfo.receiveAccountIdList = this.getData?.id
        ? [this.getData.id]
        : ([] as string[])
      this.accountName = this.getData?.accountName
    }
    @Watch('accountName', {
      deep: true,
      immediate: true
    })
    accountNameChange() {
      if (this.accountName === '') {
        this.getAccountNumber([])
      }
    }
    /*
     * 取消退款弹窗
     * */
    openCancelRefund(item: ReturnOrderResponseVo) {
      this.currentItem = item
      this.cancelReason = ''
      this.cancelRefundDialog = true
    }
    /*
     * 拒绝退款弹窗
     * */
    openRefuseRefund(item: ReturnOrderResponseVo) {
      this.currentItem = item
      this.cancelReason = ''
      this.refuseRefundDialog = true
    }
    //打开批量退款弹窗
    showBatchDialog(isAgree: boolean) {
      if (this.selectdList.length === 0) {
        this.$message.error({ message: '请先选择需批量' + (isAgree ? '同意' : '确认') + '退款的订单', offset: 120 })
        return
      }
      if (
        this.selectdList.some((item) => (isAgree ? item.UIReturnOrderStatue !== 0 : item.UIReturnOrderStatue !== 1))
      ) {
        this.$message.error({ message: '退款' + (isAgree ? '审批中' : '处理中') + '才能同意退款', offset: 120 })
        return
      }
      this[isAgree ? 'showBatchAgree' : 'showBatchConfirm'] = true
    }
    //处理批量同意退款
    async handleBatchAgree() {
      this.btnLoading = true
      const selectdNoList = this.selectdList.map((item) => item.returnOrderNo)
      const batchReturnOrderNoList = [] as OrderReturnPair[]
      this.selectdList.forEach((item) => {
        const i = new OrderReturnPair()
        i.orderNo = item.subOrderInfo.orderInfo.orderNo
        i.returnOrderNo = item.returnOrderNo
        batchReturnOrderNoList.push(i)
      })

      const res = await this.mutationReturnOrder.batchAgreeReturnApply(batchReturnOrderNoList)
      console.log(res.data, '批量同意退款返回结果')
      if (res.data.returnOrderAgreeApplyResponseList?.every((item: any) => item.code === '200')) {
        this.$message.success({ message: '同意退款成功', offset: 120 })
      } else {
        const length =
          res.data.returnOrderAgreeApplyResponseList?.reduce(
            (i: number, item: any) => (item.code !== '200' ? i + 1 : i),
            0
          ) || selectdNoList.length
        this.$message.error({ message: length + '个订单同意退款失败', offset: 120 })
      }
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else {
        await this.doQueryPage()
      }
      this.btnLoading = false
      this.showBatchAgree = false
    }
    //处理批量确认退款
    async handleBatchConfirm() {
      this.btnLoading = true
      const selectdNoList = this.selectdList.map((item) => item.returnOrderNo)
      console.log(selectdNoList, 'selectdNoList')
      const res = await this.mutationReturnOrder.batchConfirmRefund(selectdNoList)
      console.log(res.data, '批量确认退款返回结果')
      if (res.data.returnOrderConfirmRefundResponseList?.every((item: any) => item.code === '200')) {
        this.$message.success({ message: '确认退款成功', offset: 120 })
      } else {
        const length =
          res.data.returnOrderConfirmRefundResponseList?.reduce(
            (i: number, item: any) => (item.code !== '200' ? i + 1 : i),
            0
          ) || selectdNoList.length
        this.$message.error({ message: length + '个订单确认退款失败', offset: 120 })
      }
      this.showBatchConfirm = false
      this.btnLoading = false
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doQueryPagefx()
      } else {
        await this.doQueryPage()
      }
    }
    async exportRefund() {
      try {
        this.exportQueryParam = Object.assign(new ReturnOrderRequestVo(), this.returnOrderRequestVo)
        let res
        if (this.isFxlogin && this.isHadFxAbility) {
          res = await this.exportRefundfx()
        } else if (this.isZtlogin) {
          res = await this.exportRefundZt()
        } else {
          res = await this.exportRefundty()
        }
        if (res) {
          //   this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }

    /**
     * 确认退款
     */
    async doReturn(item: ReturnOrderResponseVo) {
      this.mutationReturnOrder.returnOrderNo = item.returnOrderNo
      this.query.loading = true
      const res = await this.mutationReturnOrder.confirmRefund()
      // if (res.code === 300) {
      //   this.$message.error('系统已完成退款')
      // }
      // } catch (e) {
      //   console.log(e, '确认退款失败')
      // } finally {
      if (!res.isSuccess()) {
        this.$message.error('确认退款失败')
        this.query.loading = false
        return
      }
      this.$message.success('发起确认退款中')
      this.query.loading = false
      setTimeout(async () => {
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.doQueryPagefx()
        } else {
          await this.doQueryPage()
        }
      }, 500)
    }
    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportReturnOrder' }
      })
    }
    async activated() {
      await this.search()
    }
  }
</script>
<style scoped>
  ::v-deep .el-form-item--small .el-form-item__content {
    line-height: 31px;
  }
  .more-actions {
    display: flex;
    justify-content: flex-end;
    position: relative;
    top: -10px;
  }
</style>
