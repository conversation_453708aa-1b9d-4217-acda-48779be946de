import OriginTrainClassCommodityVo from '@api/service/customer/train-class/query/vo/TrainClassDetailClassVo'
import { ValidateCommodityRequest, ValidateCommodityResponse } from '@api/ms-gateway/ms-commodity-v1'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import MSCommodity from '@api/ms-gateway/ms-commodity-v1'
import { validateCommodity } from '@api/ms-gateway/ms-commodity-v1/graphql-importer'
import { reservingSchemeValidate } from '@api/ms-gateway/ms-learningscheme-v1/graphql-importer'
import MsLearningScheme, {
  ReservingSchemeValidateRequest,
  ReservingSchemeValidateResponse
} from '@api/ms-gateway/ms-learningscheme-v1'
import CheckTrainClassParam from '@api/service/diff/customer/qztg/train-class/query/vo/CheckTrainClassParam'
import CheckTrainClassResponse from '@api/service/diff/customer/qztg/train-class/query/vo/CheckTrainClassResponse'
import MsTradeQueryFrontGatewayCourseLearningForestage, {
  OrderRequest,
  OrderResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { CheckTrainClassEnum } from '@api/service/diff/customer/qztg/train-class/query/enums/CheckTrainClassEnum'

export default class TrainClassDetailClassVo extends OriginTrainClassCommodityVo {
  /**
   * 批量校验商品是否可报名
   * @param params
   */
  async checkTrainClassByList(params: Array<CheckTrainClassParam>) {
    const commodityRequests = new Array<ValidateCommodityRequest>()
    const schemeRequest = new Array<ReservingSchemeValidateRequest>()

    // 初始化参数
    const resultList = new Array<CheckTrainClassResponse>()
    params.map((item) => {
      const commodityRq = new ValidateCommodityRequest()
      const schemeRq = new ReservingSchemeValidateRequest()
      commodityRq.commoditySkuId = item.commodityId
      commodityRq.channelType = item.channelType
      commodityRq.terminalCode = item.terminalCode

      schemeRq.schemeId = item.schemeId

      commodityRequests.push(commodityRq)
      schemeRequest.push(schemeRq)
    })

    // 执行商品校验
    const commodityValidateMap = await this.batchValidateCommodity(commodityRequests)
    let checkCommodityPass = true
    for (const [key, value] of commodityValidateMap.entries()) {
      const validateCommodityRes = new CheckTrainClassResponse()
      validateCommodityRes.commodityId = key
      validateCommodityRes.schemeId = params.find((it) => it.commodityId == key)?.schemeId
      validateCommodityRes.code = value.code as CheckTrainClassEnum
      if (value.code != '200') {
        checkCommodityPass = false
      }
      resultList.push(validateCommodityRes)
    }

    // 商品校验通过直接打出
    if (!checkCommodityPass) {
      return resultList
    }

    // 执行方案校验
    const schemeValidateMap = await this.batchValidateScheme(schemeRequest)
    const repeatOrderList = new Array<string>()
    for (const [key, value] of schemeValidateMap.entries()) {
      const result = resultList.find((it) => it.schemeId === key)
      if (result) {
        result.code = value.code as CheckTrainClassEnum
        // 重复报名去取未支付的订单号
        if (value.code === CheckTrainClassEnum.duplicateRegistration) {
          if (value.duplicateReservingInfos?.length) {
            repeatOrderList.push(value.duplicateReservingInfos[0].sourceId)
            result.orderNo = value.duplicateReservingInfos[0].sourceId
          }
        }
      }
    }

    // 查询未支付的订单
    if (repeatOrderList.length) {
      const request = new OrderRequest()
      request.subOrderNoList = repeatOrderList
      const orderListRes = await MsTradeQueryFrontGatewayCourseLearningForestage.pageOrderInMyself({
        page: {
          pageNo: 1,
          pageSize: repeatOrderList.length
        },
        request: request
      })
      const orderList = orderListRes?.data?.currentPageData || new Array<OrderResponse>()
      if (orderList.length) {
        resultList.map((item) => {
          if (item.orderNo) {
            const order = orderList.find((orderItem) => {
              if (orderItem.subOrderItems.length) {
                const findSubOrder = orderItem.subOrderItems.find((subOrder) => subOrder.subOrderNo === item.orderNo)
                if (findSubOrder) {
                  return true
                } else {
                  return false
                }
              } else {
                return false
              }
            })
            if (order) {
              if ([0, 1].includes(order.basicData?.orderPaymentStatus)) {
                if (order.basicData?.channelType == 2) {
                  // 集体报名重复
                  item.code = CheckTrainClassEnum.unpaidGroupOrderExists
                } else {
                  // 个人报名重复
                  item.code = CheckTrainClassEnum.unpaidOrderExists
                }
              }
            }
          }
        })
      }
    }

    return resultList
  }

  /**
   * 批量校验商品
   * @param requests
   * @private
   */
  private async batchValidateCommodity(
    requests: Array<ValidateCommodityRequest>
  ): Promise<Map<string, ValidateCommodityResponse>> {
    const reWriteGQL = new RewriteGraph<ValidateCommodityResponse, ValidateCommodityRequest>(
      MSCommodity._commonQuery,
      validateCommodity
    )

    await reWriteGQL.request(requests, 'commoditySkuId')

    return reWriteGQL.itemMap
  }

  /**
   * 批量校验方案
   * @param requests
   * @private
   */
  private async batchValidateScheme(
    requests: Array<ReservingSchemeValidateRequest>
  ): Promise<Map<string, ReservingSchemeValidateResponse>> {
    const reWriteGQL = new RewriteGraph<ReservingSchemeValidateResponse, ReservingSchemeValidateRequest>(
      MsLearningScheme._commonQuery,
      reservingSchemeValidate
    )

    await reWriteGQL.request(requests, 'schemeId')

    return reWriteGQL.itemMap
  }
}
