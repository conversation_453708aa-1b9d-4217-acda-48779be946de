import HistoryTrainingArchivesVo from '@api/service/customer/personal-leaning/query/vo/HistoryTrainingArchivesVo'
import { UiPage } from '@hbfe/common'
import StudentSchemeLearningQueryGateway, {
  Direction,
  HistoryStudentTrainingInfoSortEnum
} from '@api/platform-gateway/platform-student-scheme-learning-query-front-gateway'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import systemContext from '@api/service/common/context/Context'
import HistoryCertificateConfigVo from '@api/service/customer/personal-leaning/query/vo/HistoryCertificateConfigVo'
export default class QueryHistoryTrainingArchivesList {
  /**
   * @description: 历史培训档案配置项
   * @date: 2024/02/01 11:50:00
   */
  get historyCertificateConfig() {
    // 当前网校服务商id
    const servicerId = systemContext.servicerInfo.id
    const historyCertificateConfig = new HistoryCertificateConfigVo()
    if (!servicerId) return historyCertificateConfig
    const currentConfigJson = ConfigCenterModule.getFrontendApplication(frontendApplication.historyCertificateConfig)
    const currentConfig = currentConfigJson ? JSON.parse(currentConfigJson) : {}
    if (currentConfig[servicerId]) {
      historyCertificateConfig.isOpen = currentConfig[servicerId].isOpen === true
      historyCertificateConfig.isOldPlatformPrinted = currentConfig[servicerId].isOldPlatformPrinted === true
    }
    return historyCertificateConfig
  }
  /**
   * @description: 查询历史培训证明列表
   * @date: 2024/01/09 16:10:20
   * @param page
   */
  async queryLearningArchivesList(page: UiPage) {
    const { data, status } = await StudentSchemeLearningQueryGateway.pageHistoryStudentTrainingInfoResponseInMyself({
      page: page,
      sort: [
        {
          historyStudentTrainingInfoSortEnum: HistoryStudentTrainingInfoSortEnum.TRAIN_YEAR,
          policy: Direction.DESC
        }
      ]
    })
    if (data && status?.isSuccess()) {
      page.totalSize = data.totalSize
      page.totalPageSize = data.totalPageSize
      return HistoryTrainingArchivesVo.fromList(data.currentPageData)
    }
    return [] as HistoryTrainingArchivesVo[]
  }
}
