import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-account-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-account-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 重新设置账户的帐号密码认证方式认证标识信息
<AUTHOR>
 */
export class AccountPwdAuthIdentityResetInfo {
  /**
   * 【必填】账户ID
   */
  accountId?: string
  /**
   * 【必填】认证标识类型 1用户名，2手机，3身份证，4电子邮箱
@see com.fjhb.domain.basicdata.api.account.consts.AuthenticationIdentityTypes
   */
  identityType: number
  /**
   * 【必填】认证标识：帐号
   */
  identity?: string
}

/**
 * <AUTHOR>  2022/1/25 11:02
 */
export class AdministratorUpdateRequest {
  /**
   * 账户id
   */
  id?: string
  /**
   * 姓名
   */
  name?: string
  /**
   * 性别
   */
  gender?: number
  /**
   * 手机
   */
  phone?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 启用状态
   */
  status: number
  /**
   * 登录账户
   */
  identity?: string
  /**
   * 角色id集合
   */
  roleIds?: Array<string>
}

/**
 * <AUTHOR>  2022/1/25 11:02
 */
export class AreaAdministratorUpdateRequest {
  /**
   * 用户id
   */
  id?: string
  /**
   * 姓名
   */
  name?: string
  /**
   * 手机
   */
  phone?: string
  /**
   * 管辖地区
   */
  managementAreas?: Array<string>
}

/**
 * 批量导入学员请求信息
@author: zhengp 2022/2/22 9:08
 */
export class BatchImportStudentRequest {
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 密码
   */
  password?: string
}

/**
 * 换绑手机号请求(管理端)
<AUTHOR>
 */
export class BindPhoneByAdminRequest {
  /**
   * 【必填】用户输入的新手机号
   */
  phone?: string
  /**
   * 【必填】用户输入的图片验证码
   */
  captcha: string
  /**
   * 【必填】用户输入的短信验证码
   */
  smsCode: string
  /**
   * 【必填】token(图片验证码校验通过生成)
   */
  token: string
}

/**
 * 换绑手机号请求(学员端)
<AUTHOR>
 */
export class BindPhoneRequest {
  /**
   * 【必填】用户输入的新手机号
   */
  phone?: string
  /**
   * 【必填】用户输入的短信验证码
   */
  smsCode: string
  /**
   * 【必填】token(短信验证码校验通过生成)
   */
  token: string
}

/**
 * 绑定平台账号【微信】的请求
<AUTHOR>
 */
export class BindPlatformAccountRequest {
  /**
   * 【必填】认证标识(证件号或手机号)
   */
  identity: string
  /**
   * 【必填】密码
   */
  password: string
  /**
   * 【必填】用户输入的图片验证码
   */
  captcha: string
  /**
   * 【必填】token(图片验证码校验成功生成的)
   */
  token: string
  /**
   * 【必填】单点登录Token
   */
  loginToken: string
}

/**
 * base64验证码校验请求
<AUTHOR>
 */
export class CaptchaValidRequest {
  /**
   * 【必填】用户输入的验证码
   */
  captcha: string
  /**
   * 【必填】token
   */
  token: string
}

/**
 * <AUTHOR> 2022/9/9 9:06
 */
export class CreateAdminRequest {
  /**
   * 角色列表
   */
  roleIds?: Array<string>
}

/**
 * <AUTHOR>  2022/1/25 11:02
 */
export class CreateAreaAdminCreateRequest {
  /**
   * 姓名
   */
  name?: string
  /**
   * 手机
   */
  phone?: string
  /**
   * 登录账户
   */
  identity?: string
  /**
   * 管辖地区
   */
  managementAreas?: Array<string>
}

/**
 * @author: zhengp 2022/1/25 9:59
 */
export class CreateCollectiveRequest {
  /**
   * 【必填】用户名称
   */
  name: string
  /**
   * 【必填】手机号
   */
  phone: string
  /**
   * 【必填】密码
   */
  password: string
  /**
   * 【必填】用户输入的图片验证码
   */
  captcha: string
  /**
   * 【必填】短信验证码
   */
  smsCode: string
  /**
   * 【必填】token(图片校验成功生成的token)
   */
  token: string
}

/**
 * @author: zhengp 2022/1/25 9:59
 */
export class CreateStudentRequest {
  /**
   * 【必填】证件号码
   */
  idCard: string
  /**
   * 【必填】密码
   */
  password: string
  /**
   * 【必填】短信验证码
   */
  smsCode: string
  /**
   * 【必填】用户输入的图片验证码
   */
  captcha: string
  /**
   * 【必填】图片验证码校验通过生成的token
   */
  token: string
  /**
   * 单点登录Token
   */
  loginToken?: string
  /**
   * 【必填】用户名称
   */
  name?: string
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 【必填】所属区域
   */
  area?: string
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * [必填]加密值
   */
  encrypt?: string
  /**
   * 行业信息
   */
  userIndustryInfos?: Array<CreateUserIndustryRequest>
}

/**
 * <AUTHOR>  2022/1/25 11:02
 */
export class CreateSubAdminRequest {
  /**
   * 姓名
   */
  name?: string
  /**
   * 性别  女: 0 男:1
   */
  gender?: number
  /**
   * 手机
   */
  phone?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 启用状态  正常: 1 禁用: 2
   */
  status: number
  /**
   * 登录账户
   */
  identity?: string
  /**
   * 密码
   */
  password?: string
  /**
   * 角色id集合
   */
  roleIds?: Array<string>
}

/**
 * @author: zhengp 2022/1/24 15:27
 */
export class CreateUserIndustryRequest {
  /**
   * 所属行业
   */
  industryId?: string
  /**
   * 一级专业类别
   */
  firstProfessionalCategory?: string
  /**
   * 二级专业类别
   */
  secondProfessionalCategory?: string
  /**
   * 职称等级
   */
  professionalQualification?: string
  /**
   * 证书信息
   */
  certificateInfos?: Array<CertificateInfo>
}

export class CertificateAttachment {
  certificateUrl?: string
  name?: string
}

export class CertificateInfo {
  /**
   * 更新时必填
   */
  certificateId?: string
  /**
   * 证书编号
   */
  certificateNo?: string
  /**
   * 证书类别
   */
  certificateCategory?: string
  /**
   * 注册专业
   */
  registerProfessional?: string
  /**
   * 发证日期（起）
   */
  releaseStartTime?: string
  /**
   * 证书有效期（止）
   */
  certificateEndTime?: string
  /**
   * 证书附件
   */
  certificateAttachments?: Array<CertificateAttachment>
}

/**
 * 变更当前登录帐户的密码信息
<AUTHOR>
 */
export class CurrentAccountChangePasswordRequest {
  /**
   * 【必填】原始密码
   */
  originalPassword?: string
  /**
   * 【必填】新密码
   */
  newPassword?: string
}

/**
 * 当前登录用户的用户修改信息
<AUTHOR>
 */
export class CurrentUserUpdateRequest {
  /**
   * 身份证号
   */
  idCard?: string
  /**
   * 用户名称
   */
  name?: string
  /**
   * 用户昵称
   */
  nickName?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 头像地址
   */
  photo?: string
  /**
   * 所属区域
   */
  area?: string
  /**
   * 联系地址
   */
  address?: string
  /**
   * 性别
@see com.fjhb.domain.basicdata.api.user.consts.Genders
   */
  gender?: number
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * 所属人群
   */
  peoples?: string
  /**
   * 电子邮箱
   */
  email?: string
}

/**
 * 身份认证请求
<AUTHOR>
 */
export class IdentifyRequest {
  /**
   * 【必填】姓名
   */
  realName: string
  /**
   * 【必填】登录账号
   */
  identity: string
}

/**
 * 立即重置密码
<AUTHOR>
 */
export class ImmediateResetPasswordRequest {
  /**
   * 【必填】帐户ID
   */
  accountId?: string
  /**
   * 【必填】重置后的密码
   */
  password?: string
}

/**
 * 加载忘记密码所需基础验证数据的请求
<AUTHOR>
 */
export class LoadRetrievePasswordRequest {
  /**
   * 账户ID
   */
  accountId: string
  /**
   * token
   */
  token: string
}

/**
 * 分页查询学员导入任务数据请求信息
@author: zhengp 2022/5/12 15:39
 */
export class PageStudentImportTaskRequest {
  /**
   * 任务名称
   */
  taskName?: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState?: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult?: number
  /**
   * 执行时间（起始）
   */
  executeStartTime?: string
  /**
   * 执行时间（终止）
   */
  executeEndTime?: string
}

/**
 * 重置密码请求
<AUTHOR>
 */
export class ResetPasswordRequest {
  /**
   * 账户ID
   */
  accountId: string
  /**
   * 【必填】认证ID
   */
  authId: string
  /**
   * 【必填】新密码
   */
  password: string
  /**
   * 【必填】token
   */
  token: string
}

/**
 * 发送短信验证码基础请求
<AUTHOR>
 */
export class SendSmsCodeBasicRequest {
  /**
   * 账户ID
   */
  accountId?: string
  /**
   * 手机号码(完整手机号，若无则提供账户ID)
   */
  phone?: string
  /**
   * 【必填】token
   */
  token: string
}

/**
 * 发送短信验证码请求
<AUTHOR>
 */
export class SendSmsCodeRequest {
  /**
   * 【必填】用户输入的图片验证码
   */
  captcha: string
  /**
   * 账户ID
   */
  accountId?: string
  /**
   * 手机号码(完整手机号，若无则提供账户ID)
   */
  phone?: string
  /**
   * 【必填】token
   */
  token: string
}

/**
 * 短信验证码校验请求
<AUTHOR>
 */
export class SmsCodeValidRequest {
  /**
   * 账户ID
   */
  accountId?: string
  /**
   * 手机号码(完整手机号，若无则提供账户ID)
   */
  phone?: string
  /**
   * 【必填】用户输入的图片验证码
   */
  captcha: string
  /**
   * 【必填】用户输入的短信验证码
   */
  smsCode: string
  /**
   * 【必填】token
   */
  token: string
}

/**
 * @author: zhengp 2022/1/25 9:59
 */
export class UpdateStudentRequest {
  /**
   * 短信验证码
   */
  smsCode?: string
  /**
   * 用户输入的图片验证码
   */
  captcha?: string
  /**
   * 图片验证码校验通过生成的token
   */
  token?: string
  /**
   * 【必填】默认为普通更新false，强制更新true则会携带短信验证码和token
   */
  forcedUpdate?: boolean
  /**
   * 【必填】用户名称
   */
  name?: string
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 【必填】所属区域
   */
  area?: string
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * [必填]加密值
   */
  encrypt?: string
  /**
   * 行业信息
   */
  userIndustryInfos?: Array<CreateUserIndustryRequest>
}

/**
 * @author: zhengp 2022/1/25 9:59
 */
export class UpdateStudentSystemRequest {
  /**
   * 【必填】账户id
   */
  accountId?: string
  /**
   * 【必填】证件号码
   */
  idCard?: string
  /**
   * 性别
@see com.fjhb.domain.basicdata.api.user.consts.Genders
   */
  gender: number
  /**
   * 【必填】用户名称
   */
  name?: string
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 【必填】所属区域
   */
  area?: string
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * [必填]加密值
   */
  encrypt?: string
  /**
   * 行业信息
   */
  userIndustryInfos?: Array<CreateUserIndustryRequest>
}

/**
 * 用户更新信息，不设置或者设置null表示字段不更新
<AUTHOR>
 */
export class UserUpdateRequest {
  /**
   * 【必填】用户ID
   */
  id?: string
  /**
   * 身份证号
   */
  idCard?: string
  /**
   * 用户名称
   */
  name?: string
  /**
   * 用户昵称
   */
  nickName?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 头像地址
   */
  photo?: string
  /**
   * 所属区域
   */
  area?: string
  /**
   * 联系地址
   */
  address?: string
  /**
   * 性别
@see com.fjhb.domain.basicdata.api.user.consts.Genders
   */
  gender?: number
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * 所属人群
   */
  peoples?: string
  /**
   * 电子邮箱
   */
  email?: string
}

/**
 * 基础验证数据响应(用于返回当前账号已绑定的手机号、图片验证码等)
<AUTHOR>
 */
export class BasicValidationDataResponse {
  /**
   * 当前账户已绑定的数据脱敏的手机号
   */
  boundDesensitizationPhone: string
  /**
   * 图片验证码
   */
  captcha: string
  /**
   * token
   */
  token: string
}

/**
 * 图片验证码校验结果响应
<AUTHOR>
 */
export class CaptchaValidResultResponse {
  /**
   * token
   */
  token: string
  /**
   * 新的验证码
   */
  refreshCaptcha: string
  /**
   * 默认200成功
其他为异常：
40001:手机号已被使用
40002:未绑定手机号码
40003:帐号密码不匹配
40004:已绑定其他微信帐号
40005:姓名与帐号不匹配
40006:身份认证失败
40007:账户被禁用
41000:图片验证码错误
41001:图片验证码已过期
41002:图片验证码校验未通过
42000:短信验证码校验未通过
42001:短信验证码已过期
42002:短信验证码错误
42003:校验短信验证码
42004:校验短信验证码，输入的短信验证码不得为空
43000:不支持的Token类型
43001:Token已过期
43002:Token解析异常
43003:Token生成失败
43004:单点登录Token不允许为空
44000:获取用户微信信息异常
44001:当前账户未绑定微信开放平台
44002:当前账户未绑定微信开放平台，但存在微信开放平台类型的认证绑定信息，请联系管理员检查绑定信息的一致性
   */
  code: string
  /**
   * 携带的消息
   */
  msg: string
}

/**
 * 获取导入学员信息执行结果返回信息
@author: zhengp 2022/5/9 14:52
 */
export class ExportStudentImportResultResponse {
  /**
   * 导入学员信息执行结果文件地址
   */
  fileUrl: string
}

/**
 * 身份认证结果响应
<AUTHOR>
 */
export class IdentifyResultResponse {
  /**
   * 账户ID(身份认证通过才会返回)
   */
  accountId: string
  /**
   * 认证ID(身份认证通过才会返回)
   */
  authId: string
  /**
   * token(身份认证通过才会生成)
   */
  token: string
  /**
   * 默认200成功
其他为异常：
40001:手机号已被使用
40002:未绑定手机号码
40003:帐号密码不匹配
40004:已绑定其他微信帐号
40005:姓名与帐号不匹配
40006:身份认证失败
40007:账户被禁用
41000:图片验证码错误
41001:图片验证码已过期
41002:图片验证码校验未通过
42000:短信验证码校验未通过
42001:短信验证码已过期
42002:短信验证码错误
42003:校验短信验证码
42004:校验短信验证码，输入的短信验证码不得为空
43000:不支持的Token类型
43001:Token已过期
43002:Token解析异常
43003:Token生成失败
43004:单点登录Token不允许为空
44000:获取用户微信信息异常
44001:当前账户未绑定微信开放平台
44002:当前账户未绑定微信开放平台，但存在微信开放平台类型的认证绑定信息，请联系管理员检查绑定信息的一致性
   */
  code: string
  /**
   * 携带的消息
   */
  msg: string
}

/**
 * 发送短信验证码结果响应
<AUTHOR>
 */
export class SendSmsCodeResultResponse {
  /**
   * 默认200成功
其他为异常：
40001:手机号已被使用
40002:未绑定手机号码
40003:帐号密码不匹配
40004:已绑定其他微信帐号
40005:姓名与帐号不匹配
40006:身份认证失败
40007:账户被禁用
41000:图片验证码错误
41001:图片验证码已过期
41002:图片验证码校验未通过
42000:短信验证码校验未通过
42001:短信验证码已过期
42002:短信验证码错误
42003:校验短信验证码
42004:校验短信验证码，输入的短信验证码不得为空
43000:不支持的Token类型
43001:Token已过期
43002:Token解析异常
43003:Token生成失败
43004:单点登录Token不允许为空
44000:获取用户微信信息异常
44001:当前账户未绑定微信开放平台
44002:当前账户未绑定微信开放平台，但存在微信开放平台类型的认证绑定信息，请联系管理员检查绑定信息的一致性
   */
  code: string
  /**
   * 携带的消息
   */
  msg: string
}

/**
 * 短信验证码校验结果响应
<AUTHOR>
 */
export class SmsCodeValidResultResponse {
  /**
   * token
   */
  token: string
  /**
   * 默认200成功
其他为异常：
40001:手机号已被使用
40002:未绑定手机号码
40003:帐号密码不匹配
40004:已绑定其他微信帐号
40005:姓名与帐号不匹配
40006:身份认证失败
40007:账户被禁用
41000:图片验证码错误
41001:图片验证码已过期
41002:图片验证码校验未通过
42000:短信验证码校验未通过
42001:短信验证码已过期
42002:短信验证码错误
42003:校验短信验证码
42004:校验短信验证码，输入的短信验证码不得为空
43000:不支持的Token类型
43001:Token已过期
43002:Token解析异常
43003:Token生成失败
43004:单点登录Token不允许为空
44000:获取用户微信信息异常
44001:当前账户未绑定微信开放平台
44002:当前账户未绑定微信开放平台，但存在微信开放平台类型的认证绑定信息，请联系管理员检查绑定信息的一致性
   */
  code: string
  /**
   * 携带的消息
   */
  msg: string
}

/**
 * 学员导入任务数据信息
@author: zhengp 2022/5/12 15:40
 */
export class StudentImportTaskResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 处理时间
   */
  executingTime: string
  /**
   * 结束（完成）时间
   */
  completedTime: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount>
}

/**
 * 各状态及执行结果对应数量
 */
export class EachStateCount {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

export class StudentImportTaskResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<StudentImportTaskResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出全部学员导入数据
   * @param mainTaskId 任务id
   * @return 学员导入数据
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportAllStudentResult(
    mainTaskId: string,
    query: DocumentNode = GraphqlImporter.exportAllStudentResult,
    operation?: string
  ): Promise<Response<ExportStudentImportResultResponse>> {
    return commonRequestApi<ExportStudentImportResultResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出失败学员导入数据
   * @param mainTaskId 任务id
   * @return 失败学员导入数据
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportErrorStudentResult(
    mainTaskId: string,
    query: DocumentNode = GraphqlImporter.exportErrorStudentResult,
    operation?: string
  ): Promise<Response<ExportStudentImportResultResponse>> {
    return commonRequestApi<ExportStudentImportResultResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询学员导入任务数据
   * @param page    分页信息
   * @param request 查询条件
   * @return 学员导入任务数据
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentImportTask(
    params: { page?: Page; request?: PageStudentImportTaskRequest },
    query: DocumentNode = GraphqlImporter.pageStudentImportTask,
    operation?: string
  ): Promise<Response<StudentImportTaskResponsePage>> {
    return commonRequestApi<StudentImportTaskResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量导入学员
   * @param request 批量导入学员请求信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchImportStudent(
    request: BatchImportStudentRequest,
    mutate: DocumentNode = GraphqlImporter.batchImportStudent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 绑定新手机号(学员端)
   * @param request {输入的短信验证码、输入的新手机号、token(短信验证码校验成功生成的token)}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async bindPhone(
    request: BindPhoneRequest,
    mutate: DocumentNode = GraphqlImporter.bindPhone,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 绑定新手机号(管理端)
   * @param request {输入的短信验证码、输入的新手机号、输入的图片验证码、token(图片验证码校验成功生成的token)}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async bindPhoneByAdmin(
    request: BindPhoneByAdminRequest,
    mutate: DocumentNode = GraphqlImporter.bindPhoneByAdmin,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 变更当前登录帐户的密码
   * @param changeInfo 更新信息
   * @param mutate 查询 graphql 语法文档
   * @param changeInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changePasswordByCurrent(
    changeInfo: CurrentAccountChangePasswordRequest,
    mutate: DocumentNode = GraphqlImporter.changePasswordByCurrent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { changeInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 集体报名管理员身份认证
   * @param request {真实姓名,登录账号(手机号码)}
   * @return {身份认证结果代码和消息,accountId,认证ID,token(确认身份成功返回的token)}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async collectiveRegistrationAdminIdentify(
    request: IdentifyRequest,
    mutate: DocumentNode = GraphqlImporter.collectiveRegistrationAdminIdentify,
    operation?: string
  ): Promise<Response<IdentifyResultResponse>> {
    return commonRequestApi<IdentifyResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createAdmin(
    request: CreateAdminRequest,
    mutate: DocumentNode = GraphqlImporter.createAdmin,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 禁用当前帐户微信小程序通知
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableNoticeForWebChatApplet(
    mutate: DocumentNode = GraphqlImporter.disableNoticeForWebChatApplet,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 启用当前帐户微信小程序通知
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableNoticeForWebChatApplet(
    mutate: DocumentNode = GraphqlImporter.enableNoticeForWebChatApplet,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 冻结帐户
   * @param accountId 【必填】帐户ID
   * @param mutate 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async freezeAccount(
    accountId: string,
    mutate: DocumentNode = GraphqlImporter.freezeAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { accountId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 立即重置密码
   * @param resetInfo 重置密码信息
   * @param mutate 查询 graphql 语法文档
   * @param resetInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async immediateResetPassword(
    resetInfo: ImmediateResetPasswordRequest,
    mutate: DocumentNode = GraphqlImporter.immediateResetPassword,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { resetInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 加载所需基础验证数据
   * @return {图片验证码,token}
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async loadBasicValidationData(
    mutate: DocumentNode = GraphqlImporter.loadBasicValidationData,
    operation?: string
  ): Promise<Response<BasicValidationDataResponse>> {
    return commonRequestApi<BasicValidationDataResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 加载所需基础验证数据(已登录)
   * @return {当前账户已绑定的数据脱敏的手机号,图片验证码,token}
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadBasicValidationDataWithBindPhone(
    mutate: DocumentNode = GraphqlImporter.loadBasicValidationDataWithBindPhone,
    operation?: string
  ): Promise<Response<BasicValidationDataResponse>> {
    return commonRequestApi<BasicValidationDataResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 加载忘记密码所需基础验证数据
   * @param request {accountId,token(确认身份成功生成的token)}
   * @return {当前账户已绑定的数据脱敏的手机号,图片验证码,token}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async loadRetrievePasswordBasicValidationData(
    request: LoadRetrievePasswordRequest,
    mutate: DocumentNode = GraphqlImporter.loadRetrievePasswordBasicValidationData,
    operation?: string
  ): Promise<Response<BasicValidationDataResponse>> {
    return commonRequestApi<BasicValidationDataResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 登录并绑定平台账号【微信】
   * @param request {账户ID(证件号或手机号),密码,输入的图片验证码,token(图片验证码校验成功生成的),单点登录Token}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async loginAndBindOpenPlatform(
    request: BindPlatformAccountRequest,
    mutate: DocumentNode = GraphqlImporter.loginAndBindOpenPlatform,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 平台管理端管理员身份认证
   * @param request {真实姓名,登录账号(非手机号码)}
   * @return {身份认证结果代码和消息,accountId,认证ID,token(确认身份成功返回的token)}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async platformManagementAdminIdentify(
    request: IdentifyRequest,
    mutate: DocumentNode = GraphqlImporter.platformManagementAdminIdentify,
    operation?: string
  ): Promise<Response<IdentifyResultResponse>> {
    return commonRequestApi<IdentifyResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建地区管理员信息
   * @param createInfo 【必填】地区管理员信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async registerAreaAdmin(
    createInfo: CreateAreaAdminCreateRequest,
    mutate: DocumentNode = GraphqlImporter.registerAreaAdmin,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 注册集体报名账号[企业账户]
   * @param createInfo {真实姓名,手机号码,输入的短信验证码,输入的图片验证码,登录密码,token(图片验证码校验成功生成的token)}
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async registerCollective(
    createInfo: CreateCollectiveRequest,
    mutate: DocumentNode = GraphqlImporter.registerCollective,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 注册学员账号
   * @param createInfo 注册学员账号信息 {输入的短信验证码、输入手机号、输入的图片验证码、token(图片验证码校验成功生成的token)}
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async registerStudent(
    createInfo: CreateStudentRequest,
    mutate: DocumentNode = GraphqlImporter.registerStudent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建管理员信息（企业个人）
   * @param createInfo 【必填】管理员创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async registerSubAdmin(
    createInfo: CreateSubAdminRequest,
    mutate: DocumentNode = GraphqlImporter.registerSubAdmin,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 重新设置账户的帐号认证方式的帐号
   * @param mutate 查询 graphql 语法文档
   * @param resetInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resetAccountPwdAuthIdentity(
    resetInfo: AccountPwdAuthIdentityResetInfo,
    mutate: DocumentNode = GraphqlImporter.resetAccountPwdAuthIdentity,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { resetInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 忘记密码-重置密码
   * @param request {账户ID,认证ID,新密码,token(短信验证码校验通过生成的token)}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async resetPasswordWithToken(
    request: ResetPasswordRequest,
    mutate: DocumentNode = GraphqlImporter.resetPasswordWithToken,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 恢复冻结的帐户
   * @param accountId 【必填】帐户ID
   * @param mutate 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resumeAccount(
    accountId: string,
    mutate: DocumentNode = GraphqlImporter.resumeAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { accountId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 发送短信验证码(注册相关业务使用)
   * @param request {账户ID或手机号,输入的图片验证码,校验图片验证码成功生成的token}
   * @return {code,message}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async sendSmsCodeByRegister(
    request: SendSmsCodeRequest,
    mutate: DocumentNode = GraphqlImporter.sendSmsCodeByRegister,
    operation?: string
  ): Promise<Response<SendSmsCodeResultResponse>> {
    return commonRequestApi<SendSmsCodeResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 发送短信验证码(更换手机相关业务使用)
   * @param request {账户ID或手机号,输入的图片验证码,校验图片验证码成功生成的token}
   * @return {code,message}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async sendSmsCodeByUpdatePhone(
    request: SendSmsCodeRequest,
    mutate: DocumentNode = GraphqlImporter.sendSmsCodeByUpdatePhone,
    operation?: string
  ): Promise<Response<SendSmsCodeResultResponse>> {
    return commonRequestApi<SendSmsCodeResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 发送短信验证码(更换手机相关业务使用)
   * @param request {账户ID或手机号,校验短信验证码成功生成的token}
   * @return {code,message}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async sendSmsCodeByUpdatePhoneWithSmsValidToken(
    request: SendSmsCodeBasicRequest,
    mutate: DocumentNode = GraphqlImporter.sendSmsCodeByUpdatePhoneWithSmsValidToken,
    operation?: string
  ): Promise<Response<SendSmsCodeResultResponse>> {
    return commonRequestApi<SendSmsCodeResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 发送短信验证码(修改密码相关业务使用)
   * @param request {账户ID或手机号,输入的图片验证码,校验图片验证码成功生成的token}
   * @return {code,message}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async sendSmsCodeByUpdatePwd(
    request: SendSmsCodeRequest,
    mutate: DocumentNode = GraphqlImporter.sendSmsCodeByUpdatePwd,
    operation?: string
  ): Promise<Response<SendSmsCodeResultResponse>> {
    return commonRequestApi<SendSmsCodeResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 学员身份认证
   * @param request {真实姓名,登录账号(证件号码/手机号码)}
   * @return {身份认证结果代码和消息,accountId,认证ID,token(确认身份成功返回的token)}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async studentIdentify(
    request: IdentifyRequest,
    mutate: DocumentNode = GraphqlImporter.studentIdentify,
    operation?: string
  ): Promise<Response<IdentifyResultResponse>> {
    return commonRequestApi<IdentifyResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 解绑账户第三方绑定信息
   * @param accountId
   * @param mutate 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unbind(
    accountId: string,
    mutate: DocumentNode = GraphqlImporter.unbind,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { accountId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 解绑账户绑定的微信开放平台信息
   * @param accountId 账户id
   * @param mutate 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unbindWechatOpenPlatform(
    accountId: string,
    mutate: DocumentNode = GraphqlImporter.unbindWechatOpenPlatform,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { accountId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 修改管理员信息
   * @param updateInfo 【必填】要更新的用户信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateAdmin(
    updateInfo: AdministratorUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateAdmin,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 修改地区管理员信息
   * @param updateInfo 【必填】要更新地区管理员信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateAreaAdmin(
    updateInfo: AreaAdministratorUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateAreaAdmin,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新学员账号信息（学员端）
   * @param updateInfo 更新学员账号信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateStudent(
    updateInfo: UpdateStudentRequest,
    mutate: DocumentNode = GraphqlImporter.updateStudent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新学员账号信息（管理端）
   * @param updateInfo 更新学员账号信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateStudentSystem(
    updateInfo: UpdateStudentSystemRequest,
    mutate: DocumentNode = GraphqlImporter.updateStudentSystem,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新用户信息
   * @param updateInfo 【必填】要更新的用户信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateUser(
    updateInfo: UserUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateUser,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新当前登录用户信息
   * @param updateInfo 【必填】要更新的用户信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateUserByCurrent(
    updateInfo: CurrentUserUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateUserByCurrent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 校验图片验证码
   * @param request {captcha 输入的验证码,token (加载基础验证数据中生成的token)}
   * @return {校验结果响应码,响应消息,token,refreshCaptcha刷新的图片验证码}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async validCaptcha(
    request: CaptchaValidRequest,
    mutate: DocumentNode = GraphqlImporter.validCaptcha,
    operation?: string
  ): Promise<Response<CaptchaValidResultResponse>> {
    return commonRequestApi<CaptchaValidResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 校验短信验证码(单独流程使用)
   * 1.校验Token(校验图片验证码成功生成的token)
   * 1.1 Token校验通过
   * 2.校验短信验证码
   * 2.1 短信验证码校验通过
   * 整体校验通过，返回成功code，生成短信校验成功的Token
   * 2.2 短信验证码校验失败(错误或过期)
   * 返回失败code和message，无Token返回
   * 1.2 Token校验失败(可能是Token过期或输入的图片验证码被修改)
   * 返回失败code和message，无Token返回
   * @param request {账户ID,手机号,输入的图片验证码,短信验证码,String token(校验图片验证码成功生成的token)}
   * @return {校验结果响应code,响应消息,token(短信校验成功生成)}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async validSmsCode(
    request: SmsCodeValidRequest,
    mutate: DocumentNode = GraphqlImporter.validSmsCode,
    operation?: string
  ): Promise<Response<SmsCodeValidResultResponse>> {
    return commonRequestApi<SmsCodeValidResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
