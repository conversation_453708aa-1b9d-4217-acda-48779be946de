'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const fs = require('fs')
const path = require('path')
const fg = require('fast-glob')
const routes = require('./template/routes')
const resolve = require('./resolve')
const cachePath = path.resolve(process.cwd(), '.cache')

function createSecure(metaList, list) {
  metaList.forEach(meta => {
    const item = {
      path: meta.path,
      name: meta.name,
      meta: meta.routeMeta,
      children: []
    }
    if (meta.children) {
      createSecure(meta.children, item.children)
    }
    list.push(item)
  })
}

function generateRoutes({
  pages,
  importPrefix = '@/pages/',
  dynamicImport = true,
  chunkNamePrefix = '',
  nested = false,
  withSecure = false
}) {
  const patterns = ['**/*.vue', '!**/__*__.vue', '!**/__*__/**']
  const pagePaths = fg.sync(patterns, {
    cwd: pages,
    onlyFiles: true
  })

  const metaList = resolve.resolveRoutePaths(
    pagePaths,
    importPrefix,
    nested,
    file => {
      return fs.readFileSync(path.join(pages, file), 'utf8')
    },
    pages,
    withSecure
  )
  if (withSecure) {
    const result = []
    createSecure(metaList, result)
    fs.writeFileSync(`${cachePath}/secure-group-info.json`, JSON.stringify(result), { encoding: 'UTF-8' })
  }
  return routes.createRoutes(metaList, dynamicImport, chunkNamePrefix)
}

exports.generateRoutes = generateRoutes
