import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjypxtypt-distribution-service-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-distribution-service-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class GetDistributionServiceRequest {
  /**
   * 服务商id【必填】
   */
  servicerId: string
}

export class OpenDistributionServiceRequest {
  /**
   * 网校 服务商id
   */
  servicerId?: string
  key?: string
}

export class GetDistributionServiceResponse {
  /**
   * 是否开启分销服务
   */
  distributionService: boolean
  /**
   * 分销服务类型
@see com.fjhb.domain.basicdata.api.servicercontract.consts.DistributionServiceType
基础版 0（默认） 专业版 1
   */
  distributionServiceType: number
}

export class OpenDistributionServiceResponse {
  /**
   * 供应商定制角色id
   */
  customGysRoleId: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取指定服务商id是否开启分销服务
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getDistributionService(
    request: GetDistributionServiceRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDistributionService,
    operation?: string
  ): Promise<Response<GetDistributionServiceResponse>> {
    return commonRequestApi<GetDistributionServiceResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 开通分销服务
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async openDistributionService(
    request: OpenDistributionServiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.openDistributionService,
    operation?: string
  ): Promise<Response<OpenDistributionServiceResponse>> {
    return commonRequestApi<OpenDistributionServiceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
