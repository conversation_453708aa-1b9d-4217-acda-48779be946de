/**
 *
 * 模拟卷查询参数
 * @author: eleven
 * @date: 2020/4/4
 */
export class PreExamMockExaminationPaperParam {
  /**
   * 试卷名称
   */
  name?: string
  /**
   * 试卷分类id
   */
  examTypeIds?: Array<string>
  /**
   * 配置类型， -1:不查 1:固定卷 2:AB卷 3:智能卷 默认 -1
   */
  configType: number
  /**
   * 是否可用 -1/0/1 不查/可用/不可用
   */
  enable: number
  /**
   * 创建时间起
   */
  beginCreateTime?: string
  /**
   * 创建时间止
   */
  endCreateTime?: string
  /**
   * 是否草稿  -1:不查  0:是 1:否
   */
  draft = -1
}
