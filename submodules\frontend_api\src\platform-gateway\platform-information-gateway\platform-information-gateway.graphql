schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取当前系统时间
		@return
	"""
	getCurrentTime:DateTime @optionalLogin
	"""模糊查询企业信息
		@return
	"""
	listInformationByFuzzyInDistributor(request:InformationRequest):[InformationResponse] @optionalLogin
	"""模糊查询企业信息
		@return
	"""
	listInformationByFuzzyInPublic(request:InformationRequest):[InformationResponse] @optionalLogin
}
input InformationRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.InformationRequest") {
	"""查询条件"""
	key:String
	"""接口类型 1-天眼查  2-企查查"""
	type:Int
}
type InformationResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.information.InformationResponse") {
	"""企业id"""
	id:String
	"""统一社会信用代码"""
	creditCode:String
	"""企业名称"""
	name:String
	"""法人"""
	legalPersonName:String
	"""注册号"""
	regNo:String
	"""企业状态"""
	status:String
	"""成立日期 成立"""
	establishTime:DateTime
	"""基本信息来源类型 1-天眼查 2-企查查"""
	basicInfoSourceType:Int
	"""是否来自于缓存 1-是 2-否"""
	isFromCache:Int
}

scalar List
