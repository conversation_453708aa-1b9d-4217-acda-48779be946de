import CourseLogItem from '@api/service/management/intelligence-learning/model/CourseLogItem'
import ExamLogItem from '@api/service/management/intelligence-learning/model/ExamLogItem'
import { ResponseStatus } from '@hbfe/common'
import MsAutoLearningLog, {
  CourseLearningDetailRequest,
  ExamLearningDetailRequest
} from '@api/ms-gateway/ms-autolearning-log-v1'
import { LogTypeEnum } from '@api/service/management/intelligence-learning/enum/LogType'
import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'

export default class LogDetail {
  constructor(logType: LogTypeEnum) {
    this.logType = logType
  }
  /**
   * 日志类型
   */
  private logType: LogTypeEnum
  /**
   * 任务id-唯一标识
   */
  logId = ''
  /**
   * 期望开始学习时间
   */
  expectStartLearningTime = ''
  /**
   * 课程学习日志列表
   */
  courseLogList: CourseLogItem[] = []
  /**
   * 考试日志列表
   */
  examLogList: ExamLogItem[] = []

  /**
   * 查询课程日志详情
   */
  async queryCourseLogDetail() {
    const request = new CourseLearningDetailRequest()
    request.logId = this.logId
    request.logType = this.logType
    const { status, data } = await MsAutoLearningLog.queryCourseLearningDetail(request)
    if (status.isSuccess()) {
      this.expectStartLearningTime = data.expectStartStudyTime
      this.courseLogList = data.courseDetails?.map(CourseLogItem.from) ?? []
      // 课程按开始学习时间从早到晚排序
      this.courseLogList.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime())
      const courseIdList = this.courseLogList.map((item) => item.courseId).filter(Boolean)
      if (courseIdList?.length) {
        const courseList = await new QueryCourse().queryCourseByIdList(Array.from(new Set(courseIdList)))
        this.courseLogList.forEach((item) => {
          const course = courseList.find((ite) => ite.id === item.courseId)
          item.courseName = course?.name
        })
      }
    }
    return status
  }

  /**
   * 查询考试日志详情
   */
  async queryExamLogDetail() {
    const request = new ExamLearningDetailRequest()
    request.logId = this.logId
    request.logType = this.logType
    const { status, data } = await MsAutoLearningLog.queryExamLearningDetail(request)
    if (status.isSuccess()) {
      if (data.code == '200') this.examLogList = [ExamLogItem.from(data)]
      return new ResponseStatus(Number(data.code), data.message)
    }
    return status
  }
}
