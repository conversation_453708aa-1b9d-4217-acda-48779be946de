<template>
  <el-select v-model="selectItem" :placeholder="placeholder" class="form-l" multiple filterable clearable>
    <el-option
      v-for="item in studyPeriodOptions"
      :label="item.name"
      :value="item.propertyId"
      :key="item.propertyId"
      :disabled="isDisabled && item.name != '全部'"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Mixins } from 'vue-property-decorator'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import QueryGrade from '@api/service/common/basic-data-dictionary/query/QueryGrade'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import CommonSkuMixins from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSkuMixins'

  @Component
  export default class extends Mixins(CommonSkuMixins) {
    // 学段选项
    studyPeriodOptions = new Array<TrainingPropertyResponse>()
    /**
     * 选项值
     */
    get selectItem() {
      return this.value
    }
    /**
     * 重新设置选项值
     */
    set selectItem(val: string[]) {
      const removeDialogContent =
        '当前属性对应的培训方案存在被特殊规则引用的方案，取消属性，将移除特殊规则里已添加的对应属性培训方案。是否确定取消？'
      // 当前点击移除的信息
      this.removeValue = this.value.filter((item) => {
        return !val.includes(item)
      })
      if (val.includes('-1')) {
        // 全部直接赋值
        this.$emit('input', ['-1'])
        // 同步查询学科信息
        this.$emit('getDiscipline', val)
      } else if (this.value.includes('-1')) {
        if (!this.selectSchemeIndustry.length && !this.specialIndustry.length) {
          // 若全部数据中不存在已设置的行业属性，直接删除
          this.$emit('input', val)
          // 同步查询学科信息
          this.$emit('getDiscipline', val)
        } else {
          this.$emit('removeSku', this.industryId, this.selectType, this.removeValue)
          this.$confirm(removeDialogContent, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          })
            .then((e) => {
              this.$emit('removeSku', this.industryId, this.selectType, this.removeValue)
              this.$emit('input', val)
            })
            .catch((e) => {
              console.log(e)
            })
        }
      } else if (val.length > this.value.length || (!this.selectSchemeValue && !this.specialSchemeValue)) {
        this.$emit('input', val)
        // 同步查询学科信息
        this.$emit('getDiscipline', val)
      } else {
        this.$confirm(removeDialogContent, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
          .then((e) => {
            this.$emit('removeSku', this.industryId, this.selectType, this.removeValue)
            this.$emit('input', val)
          })
          .catch((e) => {
            console.log(e)
          })
        // this.$emit('removeSku', 'removeItem')
      }
    }
    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: TrainingCategoryVo) => {
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    /**
     * 获取学段
     */
    async created() {
      await QueryGrade.queryGradeByIndustry()
      this.studyPeriodOptions = QueryGrade.gradeList
      // this.$emit('getlearningPhase', this.studyPeriodOptions)
      const param = new TrainingPropertyResponse()
      param.propertyId = '-1'
      param.name = '全部'
      param.sort = 0
      param.showName = ''
      this.studyPeriodOptions.unshift(param)
    }
  }
</script>
