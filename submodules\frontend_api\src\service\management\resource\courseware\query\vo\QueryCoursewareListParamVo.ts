import CoursewareType from '@api/service/management/resource/courseware/enum/CoursewareType'
import MediaSourceType, { MediaSourceTypeEnum } from '@api/service/management/resource/courseware/enum/MediaSourceType'
import CoursewareTransformStatus from '@api/service/management/resource/courseware/enum/CoursewareTransformStatus'
import { CoursewareRequest } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import TimeRequest from '@api/service/common/models/TimeRequest'

class QueryCoursewareListParamVo {
  /*
   名称
   */
  name = ''

  /*
   课件供应商
   */
  providers: Array<string> = new Array<string>()

  /*
   所属课件分类
   */
  categoryIdList: Array<string> = new Array<string>()

  /*
   课件类型
   */
  type: CoursewareType = new CoursewareType()

  /*
   课件状态
   true: 正常
   false: 停用
   */
  enable: number = undefined

  /*
   转换状态
   1: 转换中
   2: 转换成功
   3: 转换失败
   */
  transformStatus: CoursewareTransformStatus = new CoursewareTransformStatus()

  createTime?: TimeRequest | Array<string> | string | any = []

  /*
   是否被引用
   */
  isBeingUsed: boolean = undefined

  /*
   媒体源
   1：华为云
   2：外链地址
   */
  mediaType: MediaSourceType = new MediaSourceType()

  /**
   * 是否外部链接
   */
  isOuter: number = null
  reset() {
    return Object.assign(this, new QueryCoursewareListParamVo())
  }

  to(): CoursewareRequest {
    const request = new CoursewareRequest()
    request.name = this.name
    request.enable = this.enable
    request.status = this.transformStatus.getValue()
    if (Array.isArray(this.categoryIdList)) {
      request.categoryIdList = this.categoryIdList
    } else {
      request.categoryIdList = [this.categoryIdList]
    }
    request.createTimeScope = {
      beginTime: this.createTime[0],
      endTime: this.createTime[1]
    }
    request.supplierIdList = this.providers
    request.coursewareType = (this.type.current as unknown) as number
    if (this.mediaType.current) {
      request.resourceType = this.mediaType.current == MediaSourceTypeEnum.huawei ? 2 : 4
    } else {
      if (this.isOuter == 1) {
        request.resourceType = 4
      } else if (this.isOuter == 2) {
        request.resourceType = 2
      } else {
        request.resourceType = null
      }
    }
    return request
  }
}

export default QueryCoursewareListParamVo
