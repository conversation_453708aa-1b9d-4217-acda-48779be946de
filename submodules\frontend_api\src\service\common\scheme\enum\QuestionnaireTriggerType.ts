import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 问卷触发类型枚举
 * before_exam 进入考试前
 * before_print_cert 打印证明前
 */
export enum QuestionnaireTriggerTypeEnum {
  before_exam = 1,
  before_print_cert = 2
}

/**
 * @description 问卷触发类型
 */
class QuestionnaireTriggerType extends AbstractEnum<QuestionnaireTriggerTypeEnum> {
  static enum = QuestionnaireTriggerTypeEnum

  constructor(status?: QuestionnaireTriggerTypeEnum) {
    super()
    this.current = status
    this.map.set(QuestionnaireTriggerTypeEnum.before_exam, '进入考试前')
    this.map.set(QuestionnaireTriggerTypeEnum.before_print_cert, '打印证明前')
  }
}

export default new QuestionnaireTriggerType()
