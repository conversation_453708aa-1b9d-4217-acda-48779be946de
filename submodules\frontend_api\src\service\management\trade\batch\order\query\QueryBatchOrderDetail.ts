import BatchOrderDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailVo'
import { Page, ResponseStatus } from '@hbfe/common'
import QueryBatchOrderSignUpListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderSignUpListVo'
import BatchOrderSignUpListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderSignUpListDetailVo'
import QueryBatchOrderMainOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderMainOrderListVo'
import BatchOrderMainOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderListDetailVo'
import ImportTaskResultListDetailVo from '@api/service/management/trade/batch/order/query/vo/ImportTaskResultListDetailVo'
import BatchOrderMainOrderListStatisticVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderListStatisticVo'
import msTradeQuery, {
  BatchReturnOrderBasicDataRequest,
  BatchReturnOrderRequest,
  BatchReturnOrderResponse,
  BatchReturnOrderSortRequest,
  CommoditySkuRequest,
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  OrderInfoRequest,
  OrderResponse,
  OrderSortField,
  OrderSortRequest,
  ReturnOrderBasicDataRequest,
  ReturnOrderRequest,
  ReturnOrderResponse,
  ReturnOrderSortField,
  ReturnSortRequest,
  SchemeRequest,
  SortPolicy,
  SubOrderInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'
import UserModule from '@api/service/management/user/UserModule'
import DataResolve from '@api/service/common/utils/DataResolve'
import BatchOrderDetailBuyerInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailBuyerInfoVo'
import BatchOrderUtils from '@api/service/management/trade/batch/order/query/utils/BatchOrderUtils'
import msCollectiveSign, { MetaSchema } from '@api/ms-gateway/ms-collectivesign-v1'
import ReplaceableTrainClassDetailVo from '@api/service/management/train-class/query/vo/ReplaceableTrainClassDetailVo'
import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'

/**
 * @description 查询批次订单详情
 */
class QueryBatchOrderDetail {
  private metaSchemas: MetaSchema[] = new Array<MetaSchema>()
  /**
   * 【集体报名订单】查看批次单基础信息
   * @param {string} batchOrderNo - 批次单id
   * @return {Promise<BatchOrderDetailVo>}
   */
  async queryBatchOrderDetail(batchOrderNo: string): Promise<BatchOrderDetailVo> {
    const response = await msTradeQuery.getBatchOrderInServicer(batchOrderNo)
    let result = new BatchOrderDetailVo()
    if (response.status?.isSuccess()) {
      result = await BatchOrderDetailVo.from(response.data)
    }
    return result
  }

  /**
   * 【集体报名订单】查看批次单基础信息（分销）
   * @param {string} batchOrderNo - 批次单id
   * @return {Promise<BatchOrderDetailVo>}
   */
  async queryFxBatchOrderDetail(batchOrderNo: string): Promise<BatchOrderDetailVo> {
    const response = await msTradeQuery.getBatchOrderInDistributor(batchOrderNo)
    let result = new BatchOrderDetailVo()
    if (response.status?.isSuccess()) {
      result = await BatchOrderDetailVo.from(response.data)
    }
    return result
  }

  /**
   * 查询表头字段【查询待下单列表先请求这个】
   */
  async findCollectiveSignupMetaSchema(): Promise<ResponseStatus> {
    const response = await msCollectiveSign.findCollectiveSignupMetaSchema()
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.metaSchemas)) {
      this.metaSchemas = response.data.metaSchemas
    }
    return response.status
  }
  /**
   * 【集体报名订单】查看批次单报名列表
   * @param {Page} page - 分页参数
   * @param {QueryBatchOrderSignUpListVo} queryParams - 查询参数
   * @return {Promise<BatchOrderSignUpListDetailVo[]>}
   */
  async queryBatchOrderSignUpList(
    page: Page,
    queryParams: QueryBatchOrderSignUpListVo
  ): Promise<BatchOrderSignUpListDetailVo[]> {
    let result = [] as BatchOrderSignUpListDetailVo[]
    const request = queryParams.to()
    const response = await msCollectiveSign.findImportCollectiveSignupCompleteSuccessDataByPage({
      request,
      page
    })
    page.totalSize = response.data?.totalSize ?? 0
    page.totalPageSize = response.data?.totalPageSize ?? 0
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = await Promise.all(
        response.data.currentPageData.map(async item => {
          return await BatchOrderSignUpListDetailVo.from(item)
        })
      )
    }
    return await this.fillCommodityInfoById(result)
  }

  /**
   * 【集体报名订单】查询批次单主单列表
   * @param {Page} page - 分页参数
   * @param {QueryBatchOrderMainOrderListVo} queryParams - 查询参数
   * @return {Promise<BatchOrderMainOrderListDetailVo[]>}
   */
  async queryBatchOrderMainOrderList(
    page: Page,
    queryParams: QueryBatchOrderMainOrderListVo
  ): Promise<BatchOrderMainOrderListDetailVo[]> {
    console.log('【query】batchOrderMainOrderList-queryParams', queryParams, page)
    let result = [] as BatchOrderMainOrderListDetailVo[]
    if (queryParams.buyerName || queryParams.buyerAccount) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return result
    }
    const request = await queryParams.toQuerySignUpParams()
    const sortOption = new OrderSortRequest()
    sortOption.field = OrderSortField.ORDER_NORMAL_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(sortOption) as OrderSortRequest[]
    const response = await msTradeQuery.pageOrderInServicer({
      page,
      request,
      sortRequest
    })
    page.totalSize = response.data?.totalSize ?? 0

    const resList: Array<OrderResponse> =
      (response?.data?.currentPageData?.length && response.data.currentPageData) || []

    const batchSubOrderIds = resList.map(item => {
      return item.orderNo
    })

    // 获取子订单退款列表
    let subOrderRefundlist = new Array<ReturnOrderResponse>()
    const subOrderMap = new Map<string, string>()
    const subOrderBatchRefundMap = new Map<string, BatchReturnOrderResponse>()

    if (batchSubOrderIds.length) {
      const subOrderPage = new Page(1, 200)
      const subOrderRequest = new ReturnOrderRequest()
      subOrderRequest.subOrderInfo = new SubOrderInfoRequest()
      subOrderRequest.subOrderInfo.orderInfo = new OrderInfoRequest()
      subOrderRequest.subOrderInfo.orderInfo.orderNoList = batchSubOrderIds
      subOrderRequest.basicData = new ReturnOrderBasicDataRequest()
      subOrderRequest.basicData.applySourceType = 'BATCH_RETURN_ORDER'
      const sort = new ReturnSortRequest()
      sort.policy = SortPolicy.DESC
      sort.field = ReturnOrderSortField.APPLIED_TIME
      const subOrderRefundRes = await msTradeQuery.pageReturnOrderInServicer({
        page: subOrderPage,
        request: subOrderRequest,
        sort: [sort]
      })

      if (subOrderRefundRes?.data?.currentPageData?.length) {
        let batchRefundIds = new Array<string>()

        subOrderRefundlist = subOrderRefundRes.data.currentPageData.filter(item => {
          return !item.basicData?.returnCloseReason?.closeType
        })
        subOrderRefundlist.map(item => {
          if (item.basicData?.applySourceId) {
            subOrderMap.set(item.basicData.applySourceId, item.subOrderInfo?.orderInfo?.orderNo)
            batchRefundIds.push(item.basicData?.applySourceId)
            batchRefundIds = Array.from(new Set(batchRefundIds))
          }
        })

        if (batchRefundIds.length) {
          const batchRefundPage = new Page(1, 200)
          const request = new BatchReturnOrderRequest()
          request.batchReturnOrderList = batchRefundIds
          const batchSort = new BatchReturnOrderSortRequest()
          sort.policy = SortPolicy.DESC
          sort.field = ReturnOrderSortField.APPLIED_TIME
          const batchRefundRes = await msTradeQuery.pageBatchReturnOrderInServicer({
            page: batchRefundPage,
            request: request,
            sortRequest: [batchSort]
          })

          if (batchRefundRes?.data?.currentPageData?.length) {
            batchRefundRes?.data?.currentPageData.map(item => {
              const orderId = subOrderMap.get(item.batchReturnOrderNo)
              if (orderId) {
                subOrderBatchRefundMap.set(orderId, item)
              }
            })
          }
        }
      }
    }


    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = await Promise.all(
        response.data.currentPageData.map(async item => {
          return await BatchOrderMainOrderListDetailVo.from(item, subOrderBatchRefundMap)
        })
      )
    }
    return await this.fillStudentInfo(result)
  }

  /**
   * 【集体报名订单】查询批次单主单列表 （分销）
   * @param {Page} page - 分页参数
   * @param {QueryBatchOrderMainOrderListVo} queryParams - 查询参数
   * @return {Promise<BatchOrderMainOrderListDetailVo[]>}
   */
  async queryFxBatchOrderMainOrderList(
    page: Page,
    queryParams: QueryBatchOrderMainOrderListVo
  ): Promise<BatchOrderMainOrderListDetailVo[]> {
    console.log('【query】batchOrderMainOrderList-queryParams', queryParams, page)
    let result = [] as BatchOrderMainOrderListDetailVo[]
    if (queryParams.buyerName || queryParams.buyerAccount) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return result
    }
    const request = await queryParams.toQuerySignUpParams()
    const sortOption = new OrderSortRequest()
    sortOption.field = OrderSortField.ORDER_NORMAL_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(sortOption) as OrderSortRequest[]
    const response = await msTradeQuery.pageOrderInDistributor({
      page,
      request,
      sortRequest
    })
    page.totalSize = response.data?.totalSize ?? 0
    page.totalPageSize = response.data?.totalPageSize ?? 0

    const resList: Array<OrderResponse> =
      (response?.data?.currentPageData?.length && response.data.currentPageData) || []

    const batchSubOrderIds = resList.map(item => {
      return item.orderNo
    })

    // 获取子订单退款列表
    let subOrderRefundlist = new Array<ReturnOrderResponse>()
    const subOrderMap = new Map<string, string>()
    const subOrderBatchRefundMap = new Map<string, BatchReturnOrderResponse>()

    if (batchSubOrderIds.length) {
      const subOrderPage = new Page(1, 200)
      const subOrderRequest = new ReturnOrderRequest()
      subOrderRequest.subOrderInfo = new SubOrderInfoRequest()
      subOrderRequest.subOrderInfo.orderInfo = new OrderInfoRequest()
      subOrderRequest.subOrderInfo.orderInfo.orderNoList = batchSubOrderIds
      subOrderRequest.basicData = new ReturnOrderBasicDataRequest()
      subOrderRequest.basicData.applySourceType = 'BATCH_RETURN_ORDER'
      const sort = new ReturnSortRequest()
      sort.policy = SortPolicy.DESC
      sort.field = ReturnOrderSortField.APPLIED_TIME
      const subOrderRefundRes = await msTradeQuery.pageReturnOrderInDistributor({
        page: subOrderPage,
        request: subOrderRequest,
        sort: [sort]
      })

      if (subOrderRefundRes?.data?.currentPageData?.length) {
        let batchRefundIds = new Array<string>()

        subOrderRefundlist = subOrderRefundRes.data.currentPageData.filter(item => {
          return !item.basicData?.returnCloseReason?.closeType
        })
        subOrderRefundlist.map(item => {
          if (item.basicData?.applySourceId) {
            subOrderMap.set(item.basicData.applySourceId, item.subOrderInfo?.orderInfo?.orderNo)
            batchRefundIds.push(item.basicData?.applySourceId)
            batchRefundIds = Array.from(new Set(batchRefundIds))
          }
        })

        if (batchRefundIds.length) {
          const batchRefundPage = new Page(1, 200)
          const request = new BatchReturnOrderRequest()
          request.batchReturnOrderList = batchRefundIds
          const batchSort = new BatchReturnOrderSortRequest()
          sort.policy = SortPolicy.DESC
          sort.field = ReturnOrderSortField.APPLIED_TIME
          const batchRefundRes = await msTradeQuery.pageBatchReturnOrderInDistributor({
            page: batchRefundPage,
            request: request,
            sortRequest: [batchSort]
          })

          if (batchRefundRes?.data?.currentPageData?.length) {
            batchRefundRes?.data?.currentPageData.map(item => {
              const orderId = subOrderMap.get(item.batchReturnOrderNo)
              if (orderId) {
                subOrderBatchRefundMap.set(orderId, item)
              }
            })
          }
        }
      }
    }

    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = await Promise.all(
        response.data.currentPageData.map(async item => {
          return await BatchOrderMainOrderListDetailVo.from(item, subOrderBatchRefundMap)
        })
      )
    }
    return await this.fillStudentInfo(result)
  }

  /**
   * 【集体报名订单】查询批次单主单列表统计数据
   * @param {QueryBatchOrderMainOrderListVo} queryParams - 查询参数
   */
  async queryMainOrderListStatistic(
    queryParams: QueryBatchOrderMainOrderListVo
  ): Promise<BatchOrderMainOrderListStatisticVo> {
    console.log('【query】batchOrderMainOrderListStatistic-queryParams', queryParams)
    const result = new BatchOrderMainOrderListStatisticVo()
    if (queryParams.buyerName || queryParams.buyerAccount) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return result
    }
    const orderStatistic = await BatchOrderUtils.getBatchOrderSignUpStatistic(queryParams)
    const refundStatistic = await BatchOrderUtils.getBatchOrderRefundStatistic(queryParams)
    const payAmount = await BatchOrderUtils.getBatchOrderPayAmount(queryParams)
    // 报名人次
    result.signUpPersonTime = orderStatistic.totalOrderCount
    // 总学时
    result.totalPeriod = orderStatistic.totalPeriod
    // 退款人次
    result.refundPersonTime = refundStatistic.totalReturnOrderCount
    // 商品总金额
    result.commodityTotalAmount = orderStatistic.totalOrderAmount
    // 实付金额
    result.payAmount = payAmount
    // 退款金额
    result.refundAmount = refundStatistic.totalRefundAmount
    console.log('【query】batchOrderMainOrderListStatistic', result)
    return result
  }

  /**
   * 【集体报名订单】查询批次单主单列表统计数据 （分销）
   * @param {QueryBatchOrderMainOrderListVo} queryParams - 查询参数
   */
  async queryFxMainOrderListStatistic(
    queryParams: QueryBatchOrderMainOrderListVo
  ): Promise<BatchOrderMainOrderListStatisticVo> {
    console.log('【query】batchOrderMainOrderListStatistic-queryParams', queryParams)
    const result = new BatchOrderMainOrderListStatisticVo()
    if (queryParams.buyerName || queryParams.buyerAccount) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return result
    }
    const orderStatistic = await BatchOrderUtils.getFxBatchOrderSignUpStatistic(queryParams)
    const refundStatistic = await BatchOrderUtils.getFxBatchOrderRefundStatistic(queryParams)
    const payAmount = await BatchOrderUtils.getFxBatchOrderPayAmount(queryParams)
    // 报名人次
    result.signUpPersonTime = orderStatistic.totalOrderCount
    // 总学时
    result.totalPeriod = orderStatistic.totalPeriod
    // 退款人次
    result.refundPersonTime = refundStatistic.totalReturnOrderCount
    // 商品总金额
    result.commodityTotalAmount = orderStatistic.totalOrderAmount
    // 实付金额
    result.payAmount = payAmount
    // 退款金额
    result.refundAmount = refundStatistic.totalRefundAmount
    console.log('【query】batchOrderMainOrderListStatistic', result)
    return result
  }

  /**
   * 查询导入任务结果列表
   * @param {string} batchOrderNo - 批次单id
   * @param {Page} page - 分页参数
   */
  async queryImportTaskResultList(batchOrderNo: string, page: Page): Promise<ImportTaskResultListDetailVo[]> {
    let result = [] as ImportTaskResultListDetailVo[]
    const response = await msCollectiveSign.findTaskExecuteResponsePage({
      collectiveSignupNo: batchOrderNo,
      page
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = response.data.currentPageData.map(ImportTaskResultListDetailVo.from)
    }
    page.totalSize = response.data?.totalSize ?? 0
    page.totalPageSize = response.data?.totalPageSize ?? 0
    return result
  }

  /**
   * 校验是否有效学员
   */
  private async validateExistUser(queryParams: QueryBatchOrderMainOrderListVo): Promise<boolean> {
    const queryRemote: QueryStudentList = UserModule.queryUserFactory.queryStudentList
    queryRemote.queryStudentIdParams.userName = queryParams.buyerName ?? undefined
    queryRemote.queryStudentIdParams.idCard = queryParams.buyerAccount ?? undefined
    const response = await queryRemote.queryStudentIdList()
    return DataResolve.isWeightyArr(response.data)
  }

  /**
   * 填充学员信息
   */
  private async fillStudentInfo(list: BatchOrderMainOrderListDetailVo[]): Promise<BatchOrderMainOrderListDetailVo[]> {
    const userIds = [...new Set(list.map(item => item.buyerInfo.buyerId ?? ''))]
    if (DataResolve.isWeightyArr(userIds)) {
      const queryRemote: QueryStudentList = UserModule.queryUserFactory.queryStudentList
      const response = await queryRemote.queryStudentList(userIds)
      const result: Map<string, BatchOrderDetailBuyerInfoVo> = new Map<string, BatchOrderDetailBuyerInfoVo>()
      if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data)) {
        response.data.map(item => {
          const info = new BatchOrderDetailBuyerInfoVo()
          info.buyerId = item.userId ?? ''
          info.buyerName = item.userName ?? ''
          info.buyerAccount = item.idCard ?? ''
          info.buyerPhone = item.phone ?? ''
          result.set(item.userId, info)
        })
      }
      list.forEach(item => {
        item.buyerInfo = result.get(item.buyerInfo.buyerId) ?? new BatchOrderDetailBuyerInfoVo()
      })
    }
    return list
  }

  /**
   *  填充商品信息----ID
   */
  private async fillCommodityInfoById(list: BatchOrderSignUpListDetailVo[]): Promise<BatchOrderSignUpListDetailVo[]> {
    const schemeIdList = [...new Set(list.map(item => item.schemeId ?? ''))]
    if (!DataResolve.isWeightyArr(schemeIdList)) return list
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    const queryRequest = new CommoditySkuRequest()
    queryRequest.schemeRequest = new SchemeRequest()
    queryRequest.schemeRequest.schemeIdList = schemeIdList
    const sortOption = new CommoditySkuSortRequest()
    sortOption.policy = SortPolicy.DESC
    sortOption.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    const sortRequest = Array(1).fill(sortOption)
    if (list.length === 0) {
      return list
    }
    const response = await msTradeQuery.pageCommoditySkuInServicer({
      page,
      queryRequest,
      sortRequest
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      const dataMap: Map<string, ReplaceableTrainClassDetailVo> = new Map<string, ReplaceableTrainClassDetailVo>()
      const commodityInfoList = await Promise.all(response.data.currentPageData.map(ReplaceableTrainClassDetailVo.from))
      commodityInfoList?.forEach(item => {
        dataMap.set(item.schemeId, item)
      })
      list.forEach(item => {
        item.commodityInfo = dataMap.get(item.schemeId) ?? new ReplaceableTrainClassDetailVo()
        item.skuValueNameProperty = item.commodityInfo?.skuValueNameProperty ?? new SkuPropertyResponseVo()
        item.period = item.commodityInfo?.period ?? 0
        item.price = item.commodityInfo?.price ?? 0
        item.isSocietyIndustry = item.skuValueNameProperty?.industry?.skuPropertyName === '人社行业' ? true : false
      })
    }
    return list
  }
  /**
   *  填充商品信息
   */
  private async fillCommodityInfo(list: BatchOrderSignUpListDetailVo[]): Promise<BatchOrderSignUpListDetailVo[]> {
    const schemeNameList = [...new Set(list.map(item => item.schemeName ?? ''))]
    if (!DataResolve.isWeightyArr(schemeNameList)) return list
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    const queryRequest = new CommoditySkuRequest()
    queryRequest.saleTitleList = schemeNameList
    const sortOption = new CommoditySkuSortRequest()
    sortOption.policy = SortPolicy.DESC
    sortOption.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    const sortRequest = Array(1).fill(sortOption)
    if (list.length === 0) {
      return list
    }
    const response = await msTradeQuery.pageCommoditySkuInServicer({
      page,
      queryRequest,
      sortRequest
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      const dataMap: Map<string, ReplaceableTrainClassDetailVo> = new Map<string, ReplaceableTrainClassDetailVo>()
      const commodityInfoList = await Promise.all(response.data.currentPageData.map(ReplaceableTrainClassDetailVo.from))
      commodityInfoList?.forEach(item => {
        dataMap.set(item.schemeName, item)
      })
      list.forEach(item => {
        item.commodityInfo = dataMap.get(item.schemeName) ?? new ReplaceableTrainClassDetailVo()
        item.skuValueNameProperty = item.commodityInfo?.skuValueNameProperty ?? new SkuPropertyResponseVo()
        item.period = item.commodityInfo?.period ?? 0
        item.price = item.commodityInfo?.price ?? 0
        item.isSocietyIndustry = item.skuValueNameProperty?.industry?.skuPropertyName === '人社行业' ? true : false
      })
    }
    return list
  }
}

export default QueryBatchOrderDetail
