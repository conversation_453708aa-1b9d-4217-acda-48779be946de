import { MetaRow } from '@api/ms-gateway/ms-importopen-v1'

export enum ProcessStateEnum {
  /**
   * 处理中
   */
  PEDDING,
  /**
   * 成功
   */
  SUCCESS,
  /**
   * 失败
   */
  FAIL
}
export enum OpenClassStateEnum {
  /**
   * 未发货
   */
  UNPEDDING = 0,
  /**
   * 发货中
   */
  PEDDING = 100,
  /**
   * 已发货
   */
  SUCCESS = 200,
  /**
   * 发货失败
   */
  FAIL = 400
}
export default class PageImportResultResponse {
  /**
   * 姓名
   */
  name?: string
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 培训方案名称
   */
  trainingSchemeName?: string
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 处理状态 0 已创建 1 已就绪 2 执行中 3 已完成
   */
  processState?: number
  /**
   * 处理结果 0 未处理 1 成功 2 失败 3 就绪失败
   */
  processResult?: number
  /**
   * 错误日志
   */
  errorMessage?: string
  /**
   * 创建时间
   */
  startTime?: string
  /**
   * 完成时间
   */
  endTime?: string
  /**
   * 流程还没到下单-null
开通失败-3
未开通-0
开通中-1
已开通-2
   */
  orderState: number
  /**
 * 学习状态
流程还没到学习-null
待处理-0
进行中-1
处理成功-2
处理失败-3
 */
  learningState: number
  /**
   * 是否更新密码/基础信息
   */
  isRejuvenation: string
  /**
   * 期别名称
   */
  issueName: string

  private static getStudentStatus(newStudent: number, updateBasicInfo: boolean, updatePassword: boolean): string {
    if (newStudent === 1) {
      return `-`
    } else {
      return `${updatePassword ? '是' : '否'}/${updateBasicInfo ? '是' : '否'}`
    }
  }
  static from(metaRow: MetaRow) {
    //
    const temp = new PageImportResultResponse()
    metaRow.row.map((item) => {
      if (item.key == 'userInfo_name') {
        temp.name = item.value
      }
      if (item.key == 'userInfo_idCard') {
        temp.idCard = item.value
      }
      if (item.key == 'userInfo_phone') {
        temp.phone = item.value
      }
      if (item.key == 'signUp_schemeName') {
        temp.trainingSchemeName = item.value
      }
      if (item.key == 'signUp_issueName') {
        temp.issueName = item.value
      }
    })
    temp.isRejuvenation =
      metaRow.studentState === null
        ? '暂无记录'
        : this.getStudentStatus(metaRow.studentState, metaRow.updateBasicInfo, metaRow.updatePassword)
    temp.orderNo = metaRow.orderNo
    temp.processState = metaRow.subTaskState
    temp.errorMessage = metaRow.errorMessage
    temp.endTime = metaRow.completeTime
    temp.startTime = metaRow.createTime
    temp.processResult = metaRow.result
    temp.orderState = metaRow.orderState
    return temp
  }
}
