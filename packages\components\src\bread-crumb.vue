<template>
  <div class="pos-nav-box">
    <i class="pos-ico ico"></i>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <template v-for="(match, index) in matchedRoutes">
        <el-breadcrumb-item :to="{ path: match.path }" :key="match.path" v-if="index">
          {{ match.meta.title }}
        </el-breadcrumb-item>
      </template>
    </el-breadcrumb>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        matchedRoutes: []
      }
    },
    created() {
      this.matchedRoutes = this.$route.matched.filter(match => match.path)
      console.log(this.matchedRoutes)
    }
  }
</script>
