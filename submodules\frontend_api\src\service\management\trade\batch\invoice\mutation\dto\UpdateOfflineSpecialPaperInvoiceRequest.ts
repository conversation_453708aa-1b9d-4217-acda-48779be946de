/*
 * @Description: 更新专票
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-22 09:30:44
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-24 09:06:22
 */

import {
  DeliveryAddress,
  TakePoint,
  UpdateOfflineSpecialPaperInvoiceRequest
} from '@api/ms-gateway/ms-offlineinvoice-v1'
import { DeliveryWayEnum } from '../../enum/DeliveryInvoiceEnum'
import OffLinePageInvoiceResponseVo from '../../query/vo/OffLinePageInvoiceResponseVo'
export default class OffLineInvoiceUpdateVo extends OffLinePageInvoiceResponseVo {
  //
  static to(offLinePageInvoiceResponseVo: OffLinePageInvoiceResponseVo) {
    const updateOfflineInvoiceRequest = new UpdateOfflineSpecialPaperInvoiceRequest()
    updateOfflineInvoiceRequest.offlineInvoiceId = offLinePageInvoiceResponseVo.invoiceId
    updateOfflineInvoiceRequest.invoiceType = null
    updateOfflineInvoiceRequest.invoiceCategory = offLinePageInvoiceResponseVo.invoiceCategory
    updateOfflineInvoiceRequest.title = offLinePageInvoiceResponseVo.title
    updateOfflineInvoiceRequest.taxpayerNo = offLinePageInvoiceResponseVo.taxpayerNo
    updateOfflineInvoiceRequest.address = offLinePageInvoiceResponseVo.address
    updateOfflineInvoiceRequest.phone = offLinePageInvoiceResponseVo.rePhone
    updateOfflineInvoiceRequest.bankName = offLinePageInvoiceResponseVo.bankName
    updateOfflineInvoiceRequest.account = offLinePageInvoiceResponseVo.account
    updateOfflineInvoiceRequest.businessLicensePath = offLinePageInvoiceResponseVo.businessLicenseUrl
    updateOfflineInvoiceRequest.accountOpeningLicensePath = offLinePageInvoiceResponseVo.permitUrl
    updateOfflineInvoiceRequest.shippingMethod = offLinePageInvoiceResponseVo.shippingMethod
    updateOfflineInvoiceRequest.remark = offLinePageInvoiceResponseVo.remark
    if (updateOfflineInvoiceRequest.shippingMethod === DeliveryWayEnum.SELFFETCHED) {
      // 自取
      updateOfflineInvoiceRequest.takePoint = new TakePoint()
      updateOfflineInvoiceRequest.takePoint.pickupLocation = offLinePageInvoiceResponseVo.takePointPickupLocation
      updateOfflineInvoiceRequest.takePoint.pickupTime = offLinePageInvoiceResponseVo.takePointPickupTime
      updateOfflineInvoiceRequest.takePoint.remark = offLinePageInvoiceResponseVo.takePointRemark
    } else if (updateOfflineInvoiceRequest.shippingMethod === DeliveryWayEnum.COURIER) {
      // 快递
      updateOfflineInvoiceRequest.deliveryAddress = new DeliveryAddress()
      updateOfflineInvoiceRequest.deliveryAddress.address = offLinePageInvoiceResponseVo.deliveryAddress
      updateOfflineInvoiceRequest.deliveryAddress.consignee = offLinePageInvoiceResponseVo.consignee
      updateOfflineInvoiceRequest.deliveryAddress.phone = offLinePageInvoiceResponseVo.deliveryphone
      updateOfflineInvoiceRequest.deliveryAddress.region = offLinePageInvoiceResponseVo.deliveryRegion
    }

    return updateOfflineInvoiceRequest
  }
}
