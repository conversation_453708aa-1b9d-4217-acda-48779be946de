import { TerminalTypeEnumEnum } from '@api/service/common/url-transfer/enum/TerminalTypeEnum'
import { DomainTypeEnumEnum } from '@api/service/common/url-transfer/enum/DomainTypeEnum'
import { ProxyEnvEnum } from '@api/service/common/utils/Env'

/**
 * @description 环境信息
 */
export class EnvInfo {
  /**
   * 是否是内网环境
   */
  isInnerNetwork: boolean
  /**
   * 当前内网环境
   */
  curEnv: ProxyEnvEnum
}

/**
 * @description URL转换参数
 */
class UrlConvertParam {
  /**
   * 端类型
   */
  terminalType: TerminalTypeEnumEnum = TerminalTypeEnumEnum.h5
  /**
   * 域名类型
   */
  domainType: DomainTypeEnumEnum
  /**
   * id
   * @description 在网校就是服务商ID、在专题就是专题ID、在分销就是分销推广门户ID
   */
  id = ''
  /**
   * 环境信息
   */
  envInfo = new EnvInfo()
  /**
   *  需要回调的URL地址
   */
  callbackUrl = ''
}

export default UrlConvertParam
