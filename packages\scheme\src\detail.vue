<route-params content="/:id"></route-params>
<route-meta>
{
"title": "查看培训方案"
}
</route-meta>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click.stop="goBack">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/scheme/manage' }">培训方案管理</el-breadcrumb-item>
      <el-breadcrumb-item>培训方案详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <basic-info v-if="show" :trainSchemeDetail.sync="schemeDetail.trainClassDetail" />
      <learn-way v-if="show" :trainSchemeDetail.sync="schemeDetail.trainClassDetail" />
      <training-require v-if="show" :trainSchemeDetail.sync="schemeDetail.trainClassDetail" />
      <stage v-if="show" :trainSchemeDetail.sync="schemeDetail.trainClassDetail" />
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Provide, Vue } from 'vue-property-decorator'
  import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
  import QueryTrainClassDetailClass from '@api/service/management/train-class/query/QueryTrainClassDetailClass'
  import BasicInfo from '@hbfe/jxjy-admin-scheme/src/components/detail/basic-info.vue'
  import LearnWay from '@hbfe/jxjy-admin-scheme/src/components/detail/learn-way.vue'
  import TrainingRequire from '@hbfe/jxjy-admin-scheme/src/components/detail/training-require.vue'
  import Stage from '@hbfe/jxjy-admin-scheme/src/components/detail/stage.vue'
  import SchemeDetailUIModule from '@/store/modules-ui/scheme/SchemeDetailUIModule'
  import { UiPage } from '@hbfe/common'
  import TrainingPlaceManage from '@api/service/management/resource/training-place-manage/TrainingPlaceManage'

  @Component({
    components: { TrainingRequire, BasicInfo, LearnWay, Stage }
  })
  export default class extends Vue {
    @Provide('getLearningTypeModelCopy')
    getLearningTypeModelCopy() {
      return this.schemeDetail.trainClassDetail.learningTypeModelCopy
    }

    @Provide('getTrainClassBaseInfo')
    getTrainClassBaseInfo() {
      return this.schemeDetail.trainClassDetail.trainClassBaseInfo
    }
    @Provide('getLearningTypeModel')
    getLearningTypeModel() {
      return this.schemeDetail.trainClassDetail.learningTypeModel
    }
    // 当前培训方案id
    currentSchemeId = ''
    // 培训方案详情
    schemeDetail: QueryTrainClassDetailClass

    /**
     * 是否展示组件
     */
    show = false

    load() {
      // TODO
    }

    /**
     * 页面初始化
     */
    async created() {
      this.currentSchemeId = (this.$route.params?.id as string) || ''
      if (this.currentSchemeId) {
        await this.getSchemeDetail()
      }
    }

    /**
     * 获取培训方案详情
     */
    async getSchemeDetail() {
      this.schemeDetail = TrainClassManagerModule.queryTrainClassFactory.getQueryTrainClassDetailClass()
      this.schemeDetail.commodityId = this.currentSchemeId
      // this.mockData()
      await this.schemeDetail.queryTrainClassDetail()
      await this.setLocation()
      // 保存到数据持久层
      SchemeDetailUIModule.setSchemeDetail(this.schemeDetail.trainClassDetail)
      console.log('schemeDetail', this.schemeDetail)
      this.show = true
    }

    /**
     * 填充培训点名称
     */
    async setLocation() {
      const ids: string[] = []
      this.schemeDetail.trainClassDetail.learningTypeModel.issue.issueConfigList.map((item) => {
        if (!item.trainingPointLng || !item.trainingPointLat || !item.trainingPointName) {
          ids.push(item.trainingPointId)
        }
      })
      if (ids.length) {
        const page = new UiPage()
        page.pageSize = ids.length
        page.pageNo = 1
        const trainingPlaceManage = new TrainingPlaceManage()
        trainingPlaceManage.params.ids = ids
        await trainingPlaceManage.queryList(page)
        this.schemeDetail.trainClassDetail.learningTypeModel.issue.issueConfigList =
          this.schemeDetail.trainClassDetail.learningTypeModel.issue.issueConfigList.map((item) => {
            const trainingPlace = trainingPlaceManage.list.find((ite) => ite.id === item.trainingPointId)
            if (trainingPlace) {
              item.trainingPointName = trainingPlace.trainingPlaceName
            }
            return item
          })
      }
    }

    /**
     * 返回列表
     */
    goBack() {
      this.$router.push('/training/scheme/manage')
    }
  }
</script>
