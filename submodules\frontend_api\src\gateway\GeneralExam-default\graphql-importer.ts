import findQuestion from './queries/findQuestion.graphql'
import getAllRootPaperClassificationList from './queries/getAllRootPaperClassificationList.graphql'
import getExamAnswerPaper from './queries/getExamAnswerPaper.graphql'
import getExamAnswerPaperByLearningId from './queries/getExamAnswerPaperByLearningId.graphql'
import getExamPaper from './queries/getExamPaper.graphql'
import getExamPaperByLearningId from './queries/getExamPaperByLearningId.graphql'
import getPracticeAnswerList from './queries/getPracticeAnswerList.graphql'
import getPracticePaper from './queries/getPracticePaper.graphql'
import getSubPaperClassificationList from './queries/getSubPaperClassificationList.graphql'
import getSubQuestionLibraryList from './queries/getSubQuestionLibraryList.graphql'
import hasReference from './queries/hasReference.graphql'
import isFavoriteQuestion from './queries/isFavoriteQuestion.graphql'
import isNameExists from './queries/isNameExists.graphql'
import paperClassificationHasChild from './queries/paperClassificationHasChild.graphql'
import paperClassificationHasReference from './queries/paperClassificationHasReference.graphql'
import paperClassificationNameExists from './queries/paperClassificationNameExists.graphql'
import questionLibraryHasChild from './queries/questionLibraryHasChild.graphql'
import questionLibraryHasReference from './queries/questionLibraryHasReference.graphql'
import questionLibraryNameExists from './queries/questionLibraryNameExists.graphql'
import validateFavorite from './queries/validateFavorite.graphql'
import addUserFavoriteQuestion from './mutates/addUserFavoriteQuestion.graphql'
import batchMoveQuestionToAnotherLibrary from './mutates/batchMoveQuestionToAnotherLibrary.graphql'
import copyExamPaper from './mutates/copyExamPaper.graphql'
import createExamPaper from './mutates/createExamPaper.graphql'
import createPaperClassification from './mutates/createPaperClassification.graphql'
import createQuestion from './mutates/createQuestion.graphql'
import createQuestionLibrary from './mutates/createQuestionLibrary.graphql'
import deleteExamPaper from './mutates/deleteExamPaper.graphql'
import deletePaperClassification from './mutates/deletePaperClassification.graphql'
import deleteQuestion from './mutates/deleteQuestion.graphql'
import deleteQuestionLibrary from './mutates/deleteQuestionLibrary.graphql'
import deleteUserExamAnswerInfo from './mutates/deleteUserExamAnswerInfo.graphql'
import disableExamPaper from './mutates/disableExamPaper.graphql'
import disableQuestion from './mutates/disableQuestion.graphql'
import disableQuestionLibrary from './mutates/disableQuestionLibrary.graphql'
import enableExamPaper from './mutates/enableExamPaper.graphql'
import enableQuestion from './mutates/enableQuestion.graphql'
import enableQuestionLibrary from './mutates/enableQuestionLibrary.graphql'
import getPreviewTokenByPaper from './mutates/getPreviewTokenByPaper.graphql'
import goExamination from './mutates/goExamination.graphql'
import goExaminationByToken from './mutates/goExaminationByToken.graphql'
import goPractice from './mutates/goPractice.graphql'
import goPracticeByToken from './mutates/goPracticeByToken.graphql'
import goPracticeByTokenAndSpecifyQuestionCount from './mutates/goPracticeByTokenAndSpecifyQuestionCount.graphql'
import redoExamination from './mutates/redoExamination.graphql'
import redoPractice from './mutates/redoPractice.graphql'
import removeUserFavoriteQuestion from './mutates/removeUserFavoriteQuestion.graphql'
import updateExamPaper from './mutates/updateExamPaper.graphql'
import updatePaperClassification from './mutates/updatePaperClassification.graphql'
import updateQuestion from './mutates/updateQuestion.graphql'
import updateQuestionLibrary from './mutates/updateQuestionLibrary.graphql'

export {
  findQuestion,
  getAllRootPaperClassificationList,
  getExamAnswerPaper,
  getExamAnswerPaperByLearningId,
  getExamPaper,
  getExamPaperByLearningId,
  getPracticeAnswerList,
  getPracticePaper,
  getSubPaperClassificationList,
  getSubQuestionLibraryList,
  hasReference,
  isFavoriteQuestion,
  isNameExists,
  paperClassificationHasChild,
  paperClassificationHasReference,
  paperClassificationNameExists,
  questionLibraryHasChild,
  questionLibraryHasReference,
  questionLibraryNameExists,
  validateFavorite,
  addUserFavoriteQuestion,
  batchMoveQuestionToAnotherLibrary,
  copyExamPaper,
  createExamPaper,
  createPaperClassification,
  createQuestion,
  createQuestionLibrary,
  deleteExamPaper,
  deletePaperClassification,
  deleteQuestion,
  deleteQuestionLibrary,
  deleteUserExamAnswerInfo,
  disableExamPaper,
  disableQuestion,
  disableQuestionLibrary,
  enableExamPaper,
  enableQuestion,
  enableQuestionLibrary,
  getPreviewTokenByPaper,
  goExamination,
  goExaminationByToken,
  goPractice,
  goPracticeByToken,
  goPracticeByTokenAndSpecifyQuestionCount,
  redoExamination,
  redoPractice,
  removeUserFavoriteQuestion,
  updateExamPaper,
  updatePaperClassification,
  updateQuestion,
  updateQuestionLibrary
}
