import {
  CreateRoleByAdminTypeRequest,
  CreateRoleRequest,
  UpdateRoleByAdminTypeRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
// import UpdateRole from '@api/service/management/authority/role/UpdateRole'

export enum CategoryEnums {
  // 全部
  all,
  // 网校管理员
  wxgly = 510,
  // 供应商
  gys = 17,
  // 分销商
  fxs = 18,
  // 专题管理员
  ztgly = 19
}

export default class RoleBaseInfo extends CreateRoleRequest {
  onSaving = false
  /**
   * 角色类别
   */
  category: CategoryEnums = CategoryEnums.wxgly
  /**
   * 角色名称
   */
  name = ''
  /**
   * 角色描述
   */
  description = ''
  /**
   * 角色来源
   */
  source = ''

  toJSON() {
    return {
      name: this.name,
      functionalAuthorityIds: this.functionalAuthorityIds,
      description: this.description,
      category: this.category
    }
  }

  static toCreateRoleByAdminTypeRequest(dto: RoleBaseInfo) {
    const vo = new CreateRoleByAdminTypeRequest()
    vo.name = dto.name
    vo.description = dto.description
    vo.category = dto.category
    vo.functionalAuthorityIds = dto.functionalAuthorityIds
    return vo
  }

  /**
   * 此处模型为 UpdateRole
   * 文件地址 src/service/management/authority/role/UpdateRole.ts
   * 调整成 any 是安全对象解析此处会循环引用
   * @param dto
   */
  static toUpdateRoleByAdminTypeRequest(dto: any) {
    const vo = new UpdateRoleByAdminTypeRequest()
    vo.id = dto.id
    vo.name = dto.name
    vo.description = dto.description
    vo.functionalAuthorityIds = dto.functionalAuthorityIds
    return vo
  }
}
