import DateScope from '@api/service/common/models/DateScope'
import { CourseRequest } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'

class DistributionCourseParams {
  /**
   * 课程名称
   */
  courseName = ''
  /**
   * 创建时间
   */
  createTime: DateScope = new DateScope()

  static async to(params: DistributionCourseParams) {
    const getQueryChooseCourseStatisticList = StaticticalReportManagerModule.queryStaticticalReportFactory.getQueryChooseCourseStatisticList()
    const vo = new CourseRequest()
    vo.supplierId = '-1'
    vo.createTimeBegin = params.createTime.begin
    vo.createTimeEnd = params.createTime.end
    if (params.courseName) {
      vo.courseIdList = await getQueryChooseCourseStatisticList.batchQueryCourseId(params.courseName)
    }
    return vo
  }
}

export default DistributionCourseParams
