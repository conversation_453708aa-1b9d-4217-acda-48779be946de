import { ProviderStatusEnum } from '@api/service/management/authority/service-provider/mutation/vo/ProviderStatusEnum'
import DateScope from '@api/service/common/models/DateScope'

class QueryProviderParams {
  /**
   * 课件供应商名称
   */
  name = ''
  /**
   * 管理员账号
   */
  account = ''
  /**
   * 账号状态
   */
  accountStatus: ProviderStatusEnum = null
  /**
   * 管辖地区
   */
  region: string[] = []
  /**
   * 创建日期
   */
  createDate: DateScope = new DateScope()
}

export default QueryProviderParams
