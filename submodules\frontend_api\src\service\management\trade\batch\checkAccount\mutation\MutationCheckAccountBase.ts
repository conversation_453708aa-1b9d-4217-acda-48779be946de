import CheckAccountParam from '@api/service/management/trade/batch/checkAccount/query/vo/CheckAccountParam'
import {
  BatchOrderSortRequest,
  BatchReturnOrderSortRequest
} from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import RefundCheckAccountParam from '@api/service/management/trade/batch/checkAccount/query/vo/RefundCheckAccountParam'

export default abstract class MutationCheckAccountBase {
  /**
   * 集体报名对账导出
   */
  abstract listExport(
    checkAccountParam: CheckAccountParam,
    sortRequest?: Array<BatchOrderSortRequest>
  ): Promise<boolean>

  /**
   * 集体退款对账导出
   */
  abstract listReturnExport(
    checkAccountParam: RefundCheckAccountParam,
    sortRequest?: Array<BatchReturnOrderSortRequest>
  ): Promise<boolean>
}
