<route-meta>
{
"isMenu": true,
"title": "题库管理",
"sort": 4,
"icon": "icon-tiku"
}
</route-meta>
<template>
  <el-main :key="refreshKey">
    <div class="f-p15">
      <div class="f-mb15" v-if="$hasPermission('create')" desc="创建" actions="@QuestionLibraryList">
        <el-button type="primary" @click="openDialog('add')">+新建题库</el-button>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <el-table
          :data="pageData"
          row-key="id"
          lazy
          :load="load"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          class="m-table"
          tooltip-effect="dark"
          size="small"
        >
          <el-table-column
            prop="name"
            label="分类名称"
            header-align="center"
            align="left"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column prop="field02" label="操作" width="250" align="center" fixed="right">
            <template slot-scope="scope">
              <template
                v-if="$hasPermission('detail')"
                desc="详情"
                actions="@hbfe/jxjy-admin-question/src/index.vue"
              >
                <el-button type="text" size="mini" @click="goQuestion(scope.row)">查看试题详情</el-button>
              </template>

              <template v-if="$hasPermission('modify')" desc="修改" actions="saveCreateExamSort">
                <el-button type="text" size="mini" @click="clickItemModify(scope.row)">修改</el-button>
              </template>

              <!-- <el-button type="text" size="mini" @click="clickItemDel(scope.row)">删除</el-button> -->
              <template v-if="$hasPermission('remove')" desc="修改" actions="clickItemDel">
                <hb-popconfirm placement="top" title="确定要删除该题库吗？" @confirm="clickItemDel(scope.row)">
                  <el-button type="text" slot="reference" size="mini">删除</el-button>
                </hb-popconfirm>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <!-- <hb-pagination :page="page" v-bind="page"></hb-pagination> -->

        <el-drawer :title="title" :visible.sync="dialog1" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form
                  ref="categoryFromRef"
                  :model="target.createLibraryParams"
                  :rules="rules"
                  label-width="auto"
                  class="m-form f-mt20"
                >
                  <!-- {{ target.createLibraryParams.parentId }} -->
                  <el-form-item label="所属节点：" prop="id" :rules="rules.id">
                    <!-- <template> -->
                    <!-- <QuestionLibraryList
                      style="width: 100%"
                      v-model="target.createLibraryParams.parentId"
                      ref="questionLibraryListRef"
                      :default-node="toChild"
                      :unit-id="currentInfo.unitId"
                      :key="timer"
                    ></QuestionLibraryList> -->
                    <template v-if="editMode === 'create'">
                      <question-node-cascader
                        v-model="target.createLibraryParams.id"
                        @handleCascaderChange="handleCascaderChange"
                      ></question-node-cascader>
                    </template>
                    <template v-if="editMode === 'edit'">
                      <question-node-cascader
                        v-model="selectedParentNodeId"
                        @handleCascaderChange="handleEditCascaderChange"
                      ></question-node-cascader>
                    </template>

                    <!-- <template> -->
                    <!-- <span v-else>{{ currentNodeName }}</span> -->
                    <!-- </template> -->
                    <!-- </template> -->
                  </el-form-item>
                  <el-form-item label="分类名称：" prop="name" :rules="rules.name">
                    <el-input
                      v-model="target.createLibraryParams.name"
                      clearable
                      maxlength="30"
                      show-word-limit
                      placeholder="请输入分类名称，不超过30个字"
                    />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button @click="cancelCreateExamSort">取消</el-button>
                    <el-button type="primary" @click="saveCreateExamSort()" :loading="saveLoading">保存 </el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Watch, Ref } from 'vue-property-decorator'
  import QuestionNodeCascader from '@hbfe/jxjy-admin-questionLibrary/src/components/question-node-cascader.vue'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { UiPage, Query } from '@hbfe/common'
  import LibraryResponseVo from '@api/service/management/resource/question-library/query/vo/LibraryResponseVo'
  import UpdateLibraryDto from '@api/service/management/resource/question-library/mutation/vo/UpdateLibraryVo'
  import QuestionLibraryAction from '@api/service/management/resource/question-library/mutation/QuestionLibraryAction'
  import CreateQuestionLibrary from '@api/service/management/resource/question-library/mutation/CreateQuestionLibrary'
  import LibraryRequestVo from '@api/service/management/resource/question-library/query/vo/LibraryRequestVo'
  import NewLibraryResponseVo from '@hbfe/jxjy-admin-questionLibrary/src/models/index'
  import { ElForm } from 'element-ui/types/form'
  import { bind, debounce } from 'lodash-decorators'
  //   import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  //   import {
  //     // 江苏工考一体化平台子项目管理员
  //     ZXMGLY,
  //     // 内置地区管理员角色
  //     DQGLY,
  //     // 施教机构管理员
  //     WXGLY,
  //     // 学员
  //     XY
  //   } from '@/models/RoleTypes'
  //   @RoleTypeDecorator({
  //     query: [],
  //     create: [],
  //     detail: [],
  //     remove: [],
  //     modify: []
  //   })
  @Component({
    components: {
      QuestionNodeCascader
    }
  })
  export default class extends Vue {
    currentInfo = new UpdateLibraryDto()
    saveItemData = new LibraryResponseVo()
    formCheck = true
    currentNodeName = ''
    showTree = false
    // updateInfo = new QuestionLibraryUpdateRequest()
    // addInfo = new QuestionLibraryCreateRequest()
    refName = 'categoryFrom'
    // 暂不使用，分类没有分页
    page: UiPage
    query: Query = new Query()
    // 分类没有分页，但是名称叫pageData
    editCategory: QuestionLibraryAction
    target: CreateQuestionLibrary = new CreateQuestionLibrary()
    pageData: Array<NewLibraryResponseVo> = new Array<NewLibraryResponseVo>()
    toChild: NewLibraryResponseVo = new NewLibraryResponseVo()
    libraryFactory = ResourceModule.queryQuestionLibraryFactory
    uiConfig = {
      loadData: false,
      // 当前模式 1 新增 2编辑
      currentMode: 0,
      saveCtrl: false,
      delCtrl: false
    }
    dialog1 = false
    saveLoading = false
    chooseNode = ''
    title = ''
    form = {
      region: '',
      name: ''
    }
    rules = {
      parentId: [
        {
          required: true,
          message: '所属节点不可为空',
          trigger: ['change', 'focus']
        }
      ],
      id: [
        {
          required: true,
          message: '所属节点不可为空',
          trigger: ['change', 'focus']
        }
      ],
      name: [
        {
          required: true,
          // validator: this.nameValidator,
          message: '分类名称不可为空',
          trigger: ['blur', 'change', 'focus']
        },
        {
          validator: async (rule: any, value: any, callback: any) => {
            await this.checkName(rule, value, callback)
          },
          trigger: 'blur'
        }
      ]
    }
    // 编辑状态
    editMode: 'create' | 'edit' = 'create'
    // 选中的父级题库节点ID
    selectedParentNodeId = ''

    // nameValidator(rule: any, value: any, callback: any) {}

    mutationQuestionLibrary = ResourceModule.mutationQuestionLibraryFactory
    questionLibrary = ResourceModule.queryQuestionLibraryFactory.queryQuestionLibraryMultiton
    libraryResponseVo: LibraryResponseVo = new LibraryResponseVo()
    LibraryRequestVo: LibraryRequestVo = new LibraryRequestVo()
    classifyTree = false // 主要是 tree这个组件生产了数据树之后无法重新更新，只能这样删除了

    refreshKey = 1 //用于刷新表单

    refreshTime = 1500 //表单刷新延时
    timer = 0

    @Ref('categoryFromRef')
    categoryFromRef: ElForm

    async doCheck() {
      const el: any = this.$refs[this.refName]
      return new Promise(resolve => {
        //表单规则效验
        el.validate((valid: any) => {
          this.formCheck = valid
          resolve(valid)
        })
      })
    }

    handleCascaderChange(val: any) {
      this.target.createLibraryParams.parentId = val.parentNodeId
    }

    handleEditCascaderChange(val: any) {
      this.target.createLibraryParams.parentId = val.nodeId
    }

    // 懒加载
    async load(tree: NewLibraryResponseVo, treeNode: any, resolve: (arr: Array<NewLibraryResponseVo>) => void) {
      this.subLoad(tree, resolve)
    }

    // 一级节点-父节点是机构 or 没有机构的平台试题
    async rootLoad(tree: NewLibraryResponseVo, resolve: (arr: Array<NewLibraryResponseVo>) => void) {
      return
    }

    // 次级节点-父节点是机构
    async subLoad(tree: NewLibraryResponseVo, resolve: (arr: Array<LibraryResponseVo>) => void) {
      const parentId = tree.id || '-1'

      const res = await this.questionLibrary.queryQuestionBankLibraryItem(this.page, parentId)

      const categoryUIList = new Array<NewLibraryResponseVo>()

      res.data?.forEach(p => {
        const category = new NewLibraryResponseVo()
        category.parentId = p.parentId
        category.name = p.name
        category.parentlibraryName = p.parentlibraryName
        category.id = p.id
        categoryUIList.push(category)
      })

      resolve(categoryUIList)
    }

    async checkName(rule: any, value: any, callback: any) {
      //   return callback(new Error('名称不能重复'))
      const res = await this.questionLibrary.queryQuestionBankLibraryItem(
        this.page,
        this.target.createLibraryParams.parentId
      )
      let flag = false
      res.data?.forEach(p => {
        if (p.name == this.target.createLibraryParams.name) {
          flag = true
        }
      })
      if (flag) return callback(new Error('名称不能重复'))
      else return callback()
    }

    /**
     * 新建题库
     * @param type
     */
    openDialog(type: string) {
      this.editMode = 'create'
      const el: any = this.$refs.categoryFromRef
      this.classifyTree = false
      this.currentInfo = new UpdateLibraryDto()
      this.target = new CreateQuestionLibrary()
      this.target.createLibraryParams.parentId = ''
      // this.selectedParentNodeId = ''
      this.saveItemData = new LibraryResponseVo()
      this.title = '新建分类'
      this.dialog1 = true
      if (el) {
        el.resetFields()
      }
      this.classifyTree = true
      this.uiConfig.currentMode = 1
      this.toChild.parentId = '-1'
      this.toChild.libraryName = '题库分类'
    }

    /**
     * 编辑题库
     * @param item 要修改的题库节点
     */
    async clickItemModify(item: LibraryResponseVo) {
      this.editMode = 'edit'
      //编辑分类
      const el: any = this.$refs[this.refName]
      if (el) {
        el.resetFields()
      }
      this.editCategory = new QuestionLibraryAction(item.id, item.name, item.parentId)
      this.title = '编辑分类'
      this.uiConfig.currentMode = 3
      this.target.createLibraryParams.id = item.id
      if (item.parentId == undefined) {
        this.selectedParentNodeId = '-1'
        this.target.createLibraryParams.parentId = '-1'
      } else {
        this.selectedParentNodeId = item.parentId
        this.target.createLibraryParams.parentId = item.parentId
      }
      this.chooseNode = item.id

      this.target.createLibraryParams.name = item.name

      // if (!item.parentId) {
      //   this.currentNodeName = '题库分类'
      // } else {
      //   const res = await this.questionLibrary.queryQuestionBankLibraryDetail(item.id)
      //   this.currentNodeName = res.data.parentlibraryName
      // }

      this.dialog1 = true
    }

    async clickItemDel(item: LibraryResponseVo) {
      //删除
      const res = await this.mutationQuestionLibrary
        .questionLibraryAction(item.id, item.libraryName, item.parentId)
        .doDeleteQuestionLibrary()
      if (res.code == 200) {
        this.$message.success('删除成功！')
        this.delayToRefreshTable(() => {
          this.doQueryPage()
          this.refreshKey++
        })
      } else {
        this.$message.error('题库资源已被试题使用不可删除')
      }
    }

    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }

    goQuestion(item: LibraryResponseVo) {
      let url = ''
      url = '/resource/question'
      this.$router.push({
        path: url,
        query: { id: item.id }
      })
    }

    cancelCreateExamSort() {
      //取消弹窗

      this.currentInfo = new UpdateLibraryDto()
      this.dialog1 = false
    }
    @bind
    @debounce(200)
    async saveCreateExamSort() {
      //弹窗（新增/修改）保存
      // this.showTree = false
      // this.uiConfig.saveCtrl = true //表单验证
      // const check = await this.doCheck()
      // if (!check) {
      //   //不通过表单按钮禁止选择
      //   this.uiConfig.saveCtrl = false
      //   return
      // }
      this.categoryFromRef.validate(async (val: any) => {
        if (val) {
          this.saveLoading = false
          if (this.uiConfig.currentMode == 1) {
            this.doAdd()
          } else {
            this.doUpdate()
          }
        }
      })

      this.showTree = true
    }

    @Watch('target.createLibraryParams', {
      deep: true,
      immediate: true
    })
    createParamsChange(val: any) {
      if (val) {
        console.log(val, 'parentId---------------')
      }
    }

    @Watch('target.createLibraryParams.parentId', {
      deep: true
    })
    parentIdChange(val: any) {
      if (val) {
        this.categoryFromRef && this.categoryFromRef.clearValidate('parentId')
      }
      // else {
      //   this.categoryFromRef && this.categoryFromRef.validateField('parentId')
      // }
    }

    async checkQuestionLibrary() {
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 200
      const res = await this.libraryFactory.queryQuestionLibraryMultiton.queryQuestionBankLibraryItem(
        page,
        this.target.createLibraryParams.parentId
      )
      const questionNumRes = await this.libraryFactory.queryQuestionLibraryMultiton.queryCountQuestionInServicer([
        this.target.createLibraryParams.parentId
      ])
      if (!res.data?.length && questionNumRes?.data?.length && questionNumRes?.data[0].questionCount > 0) {
        this.$message.error('该题库下已有试题，创建失败')
        return false
      } else return true
    }

    async doAdd() {
      // if()
      let isName = true
      console.log(this.pageData.length)

      if (this.pageData.length > 0) {
        console.log(this.pageData.length, 'aaaaaaaaaaaaaaa')
        this.pageData.find(item => {
          if (item.name == this.target.createLibraryParams.name) {
            isName = false
          }
        })
        if (!isName) return this.$message.error('创建题库失败，题库名称不可重复！')
      }
      //新增
      this.saveLoading = true
      this.target.createLibraryParams.parentId = this.target.createLibraryParams.id
      delete this.target.createLibraryParams.id
      if (!(await this.checkQuestionLibrary())) {
        this.saveLoading = false
        return
      }
      const res: any = await this.target.doCreateQuestionLibrary()
      if (res.status.code == 200) {
        this.uiConfig.saveCtrl = false
        this.dialog1 = false
        this.$message.success('创建题库成功！')
        // setTimeout(() => {
        //   this.$nextTick(() => {
        this.delayToRefreshTable(() => {
          this.doQueryPage()
          this.refreshKey++
        })
        //   })
        // }, 1000)
      } else {
        this.saveLoading = false
        return this.$message.error(`${res.errors[0].message}`)
        // return  this.$message.error(`${res.errors[0].message}`)
        // this.$message.error(`${res.status.}`)
      }
      this.saveLoading = false

      this.currentInfo = new UpdateLibraryDto()
    }

    async doUpdate() {
      //编辑
      this.saveLoading = true
      if (this.chooseNode === this.target.createLibraryParams.parentId) {
        this.saveLoading = false
        return this.$message.error('题库所属节点不能修改为本身！')
      }

      let parentId = this.target.createLibraryParams.parentId
      if (this.chooseNode !== this.target.createLibraryParams.id) {
        parentId = this.target.createLibraryParams.id
      }
      // this.updateInfo.enabled = true
      const res = await this.mutationQuestionLibrary
        .questionLibraryAction(this.chooseNode, this.target.createLibraryParams.name, parentId)
        .doUpdateQuestionLibrary()
      if (res.code == 200) {
        this.dialog1 = false
        this.$message.success('更新题库成功！')
        // setTimeout(() => {
        //   this.$nextTick(() => {
        this.delayToRefreshTable(() => {
          this.doQueryPage()
          this.refreshKey++
        })
        //   })
        // }, 1000)
      } else {
        this.saveLoading = false
        return this.$message.error(`${res.errors[0].message}`)
      }
      this.saveLoading = false
    }

    /**
     * 初始化
     */
    async doQueryPage() {
      this.pageData = new Array<NewLibraryResponseVo>()
      this.page.pageSize = 200
      const res = await this.questionLibrary.queryQuestionBankLibrary(this.page, this.LibraryRequestVo)
      this.pageData = res.data.map(p => {
        const category = new NewLibraryResponseVo()
        category.name = p.name
        category.parentId = p.parentId
        category.parentlibraryName = p.parentlibraryName
        category.id = p.id
        return category
      })
    }

    async mounted() {
      await this.doQueryPage()
      this.showTree = true
    }

    //延时刷新表单
    delayToRefreshTable(f: Function) {
      setTimeout(() => {
        f()
      }, this.refreshTime)
    }
  }
</script>

<style scoped></style>
