import store from '@api/store'
import { VuexModule, getModule, Module } from 'vuex-module-decorators'
import MutationUserFactory from '@api/service/management/user/MutationUserFactory'
import QueryUserFactory from '@api/service/management/user/QueryUserFactory'

@Module({ namespaced: true, name: 'UserModule', dynamic: true, store })
class UserModule extends VuexModule {
  /* 
    查询用户工厂  
  */
  get queryUserFactory() {
    return QueryUserFactory
  }

  /* 
    用户业务工厂
  */
  get mutationUserFactory() {
    return MutationUserFactory
  }
}

export default getModule(UserModule)
