import AbstractEnum from '@api/service/common/enums/AbstractEnum'
import TrainClassSchemeType, {
  TrainClassSchemeEnum
} from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
export enum SchemeTypeEnum {
  /**
   * 选课规则
   */
  chooseCourseLearning = 1,
  /**
   * 自主选课
   */
  autonomousCourseLearning,
  /**
   * 合作办学
   */
  trainingCooperation
}
class SchemeType extends AbstractEnum<SchemeTypeEnum> {
  static enum = SchemeTypeEnum
  constructor(status?: SchemeTypeEnum) {
    super()
    this.current = status
    this.map.set(SchemeTypeEnum.chooseCourseLearning, '选课规则')
    this.map.set(SchemeTypeEnum.autonomousCourseLearning, '自主选课')
    this.map.set(SchemeTypeEnum.trainingCooperation, '合作办学')
  }

  /**
   * 获取类型名称
   * @param type 方案类型
   * @param hasClass 是否拼接标签展示 ’培训班-‘
   * @return {string}
   */
  getSchemeType(type: string | number, hasClass = false) {
    let name = hasClass ? '培训班-' : ''
    switch (type) {
      case SchemeTypeEnum.chooseCourseLearning:
      case SchemeTypeEnum[SchemeTypeEnum.chooseCourseLearning]:
        name += this.map.get(SchemeTypeEnum.chooseCourseLearning)
        break
      case SchemeTypeEnum.autonomousCourseLearning:
      case SchemeTypeEnum[SchemeTypeEnum.autonomousCourseLearning]:
        name += this.map.get(SchemeTypeEnum.autonomousCourseLearning)
        break
      case SchemeTypeEnum.trainingCooperation:
      case SchemeTypeEnum[SchemeTypeEnum.trainingCooperation]:
        name = this.map.get(SchemeTypeEnum.trainingCooperation)
        break
      default:
        name = ''
    }
    return name
  }
  getSchemeTypeNameById(val: number) {
    const res = TrainClassSchemeType.map.get(val) || '-'
    return res
  }

  getSchemeTrainingType(val: TrainingModeEnum) {
    const res = TrainingMode.map.get(val) || '-'
    return res
  }
  getNewSchemeType(val: any, IsShowMode: boolean) {
    let trainingWay
    if (val.skuValueNameProperty !== undefined) {
      trainingWay = this.getSchemeTrainingType(val.skuValueNameProperty.trainingMode.skuPropertyValueId)
    } else {
      trainingWay = this.getSchemeTrainingType(val.type)
    }
    const schemeType = '培训班'
    let selectClassWay
    if (trainingWay === '面授') {
      selectClassWay = '-'
    } else {
      selectClassWay = this.getSchemeTypeNameById(val.schemeType)
    }
    if (IsShowMode) {
      const nameArr = []
      if (trainingWay !== '-') {
        nameArr.push(trainingWay)
      }
      nameArr.push(schemeType)
      if (selectClassWay !== '-' && (trainingWay === '网授' || trainingWay === '面网授')) {
        nameArr.push(selectClassWay)
      }
      return nameArr.join('-')
    } else {
      const nameArr = []
      nameArr.push(schemeType)
      if (selectClassWay !== '-') {
        nameArr.push(selectClassWay)
      }
      return nameArr.join('-')
    }
  }
  // 面授班方案类型只有培训班
  getFaceSchemeType(val: any) {
    const schemeType = '培训班'
    return schemeType
  }

  isMixOrOffline(val: any) {
    const trainingWay = this.getSchemeTrainingType(val.skuValueNameProperty.trainingMode.skuPropertyValueId)
    if (trainingWay === '面授' || trainingWay === '面网授') {
      return true
    }
    return false
  }
}

export default new SchemeType()
