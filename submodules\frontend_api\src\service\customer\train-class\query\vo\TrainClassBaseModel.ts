import SkuPropertyResponseVo from '@api/service/customer/train-class/query/vo/SkuPropertyResponseVo'

/**
 * 培训班基础信息
 */
class TrainClassBaseModel {
  // region properties
  /**
   * 学号id
   */
  studentNo: string
  /**
   *  是否提供整班重学
   */
  provideRelearn = false
  /**
   * 培训结果
   <p>
   -1:未知，培训尚未完成
   1:培训合格
   0:培训不合格
   @see StudentTrainingResults
   */
  trainingResult: number = null
  /**
   * 取得培训结果时间
   */
  trainingResultTime: string = null
  /**
   *开放证明打印
   */
  openPrintTemplate = false
  /**
   *班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuProperty = new SkuPropertyResponseVo()
  /**
   *培训方案id，类型为string
   */
  id = ''
  /**
   *参训资格id，类型为string
   */
  qualificationId = ''

  /**
   *培训方案类型1: 选课规则2: 自主选课，类型为number
   */
  schemeType = 0
  /**
   *培训班名称，类型为string
   */
  name = ''
  /**
   * 当前培训班名称，类型为string
   */
  currentName = ''
  /**
   *图片url，类型为string
   */
  picture = ''
  /**
   *方案介绍内容，类型为string
   */
  comment = ''
  /**
   * 考核Id
   */
  assessSettingId = ''
  /**
   * 考核模板名称
   */
  assessSettingName = ''
  /**
   * 学分成果id
   */
  creditId = ''
  /**
   *报名开始时间，类型为string
   */
  registerBeginDate = ''
  /**
   *报名结束时间，类型为string
   */
  registerEndDate = ''
  /**
   *培训开始时间，类型为string
   */
  trainingBeginDate = ''
  /**
   *培训结束时间，类型为string
   */
  trainingEndDate = ''
  /**
   *获得学时，类型为number
   */
  period = 0
  /**
   *是否有培训证明，类型为boolean
   */
  hasLearningResult = false
  /**
   *培训证明模板id，类型为string
   */
  learningResultId = ''
  /**
   *培训证明成果id，类型为string
   */
  learningResultAchievementsId = ''
  /**
   *班级练习来源1按题库2按学员课程id出题3同考试，类型为number
   * 废弃，班级练习来源应从学习方式获取
   */
  practiceLearningType = 0

  /**
   * 培训方案须知
   */
  notice = ''
  /**
   * 培训期别须知
   */
  issueNotice = ''

  /**
   * 培训方案简介id
   */
  introId = ''

  /**
   * 培训方案简介
   */
  introContent = ''
  /**
   * 培训方案须知弹窗
   */
  showNoticeDialog = false
  // endregion
  // region methods

  // endregion
}
export default TrainClassBaseModel
