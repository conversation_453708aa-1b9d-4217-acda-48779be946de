import prepareReturn from './queries/prepareReturn.graphql'
import agreeReturnApply from './mutates/agreeReturnApply.graphql'
import agreeReturnBatchApply from './mutates/agreeReturnBatchApply.graphql'
import confirmBatchRefund from './mutates/confirmBatchRefund.graphql'
import confirmRefund from './mutates/confirmRefund.graphql'
import rejectReturnApply from './mutates/rejectReturnApply.graphql'
import retryRecycleResource from './mutates/retryRecycleResource.graphql'
import retryRefund from './mutates/retryRefund.graphql'
import sellerCancelReturnApply from './mutates/sellerCancelReturnApply.graphql'

export {
  prepareReturn,
  agreeReturnApply,
  agreeReturnBatchApply,
  confirmBatchRefund,
  confirmRefund,
  rejectReturnApply,
  retryRecycleResource,
  retryRefund,
  sellerCancelReturnApply
}
