<template>
  <el-select clearable v-model="selectId" @change="handleSelect" @clear="clearSelected" placeholder="请选择期别">
    <el-option v-for="item in options" :key="item.id" :label="item.issueName" :value="item.id"></el-option>
  </el-select>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import { UiPage } from '@hbfe/common'

  @Component
  export default class extends Vue {
    // v-model绑定的值
    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Prop({
      type: Boolean,
      default: false
    })
    disabled: boolean

    @Prop({
      type: String,
      default: ''
    })
    name: string

    @Prop({
      type: String,
      default: ''
    })
    schemeId: string
    @Prop({
      type: Array,
      default: () => {
        return new Array<IssueConfigDetail>()
      }
    })
    initOptions: Array<IssueConfigDetail>
    options = new Array<IssueConfigDetail>()
    /**
     * 页码
     * */
    page: UiPage = new UiPage()

    /**
     * 选中的id
     */
    selectId = ''
    /**
     * 期别列表
     */
    trainClassCommodityVo = new TrainClassCommodityVo()

    /**
     * value赋值
     */
    @Watch('value', {
      immediate: true
    })
    valueChange(val: string) {
      this.selectId = val
    }

    @Watch('schemeId', { immediate: true })
    schemeChange(val: string) {
      this.querySearchAsync()
    }
    @Watch('initOptions', { immediate: true })
    optionChange(val: Array<IssueConfigDetail>) {
      this.options = this.initOptions
    }
    /**
     * 初始化
     */
    async activated() {
      await this.querySearchAsync()
    }

    /**
     * 查询方法
     */
    async searchQuery() {
      // await queryInstitutionList.queryInstitutionListByNoPage(this.value)
    }

    /**
     * 远程搜索
     */
    async querySearchAsync() {
      this.options = await this.trainClassCommodityVo.queryIssueListInDistributor(this.schemeId)
    }

    /**
     * 清空选项
     */
    clearSelected() {
      this.$emit('input', '')
    }

    /**
     * 选中方法
     */
    handleSelect(id: string) {
      this.$emit('input', id)
    }
  }
</script>
