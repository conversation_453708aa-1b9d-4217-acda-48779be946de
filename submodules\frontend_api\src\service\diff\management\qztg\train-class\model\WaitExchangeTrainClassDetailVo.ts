import {
  BuyerValidCommodityResponse,
  SchemeResourceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { TrainClassResourceEnum } from '@api/service/management/train-class/query/enum/TrainClassResourceType'
import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
import { TrainClassStatusEnum } from '@api/service/management/train-class/query/enum/TrainClassStatusType'
import { TrainClassAssessmentStatusEnum } from '@api/service/management/train-class/query/enum/TrainClassAssessmentStatusType'
import { Page } from '@hbfe/common'
import MsSchemeLearningQuery, {
  LearningRegisterRequest,
  SchemeRequest,
  StudentSchemeLearningRequest,
  UserRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import WaitExchangeTrainClassDetailSchoolVo from '@api/service/management/train-class/query/vo/WaitExchangeTrainClassDetailVo'
import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/qztg/trade/enums/SaleChannelType'
import QueryPlatform from '@api/service/diff/common/qztg/dictionary/QueryPlatform'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'

/**
 * @description 待更换班级列表下详情
 */
class WaitExchangeTrainClassDetailVo extends WaitExchangeTrainClassDetailSchoolVo {
  /**
   * 第三方平台
   */
  thirdPartyPlatform = ''
  /**
   * 转换远端模型
   * @param {string} userId - 用户id，用于查询考核情况以及状态
   */
  static async from(userId: string, response: BuyerValidCommodityResponse): Promise<WaitExchangeTrainClassDetailVo> {
    const detail = new WaitExchangeTrainClassDetailVo()
    detail.schemeId = (response.commoditySku?.resource as SchemeResourceResponse)?.schemeId || ''
    detail.commoditySkuId = response.commoditySku?.commoditySkuId || ''
    detail.schemeType = WaitExchangeTrainClassDetailVo.getSchemeType(response)
    detail.schemeName = (response.commoditySku?.resource as SchemeResourceResponse)?.schemeName || ''
    detail.industryPropertyId = response.commoditySku?.skuProperty?.industry?.skuPropertyValueId
    detail.trainingObject =
      response.commoditySku?.skuProperty?.trainingObject?.skuPropertyValueShowName ||
      response.commoditySku?.skuProperty?.trainingObject?.skuPropertyValueName
    detail.positionCategory =
      response.commoditySku?.skuProperty?.positionCategory?.skuPropertyValueShowName ||
      response.commoditySku?.skuProperty?.positionCategory?.skuPropertyValueName
    detail.jobLevel =
      response.commoditySku?.skuProperty?.jobLevel?.skuPropertyValueShowName ||
      response.commoditySku?.skuProperty?.jobLevel?.skuPropertyValueName
    detail.jobCategory =
      response.commoditySku?.skuProperty?.jobCategory?.skuPropertyValueShowName ||
      response.commoditySku?.skuProperty?.jobCategory?.skuPropertyValueName
    detail.industry = response.commoditySku?.skuProperty?.industry?.skuPropertyValueName || ''
    detail.industryId = response.commoditySku?.skuProperty?.industry?.skuPropertyValueId || ''

    detail.year = response.commoditySku?.skuProperty?.year?.skuPropertyValueName || ''
    detail.subjectType = response.commoditySku?.skuProperty?.subjectType?.skuPropertyValueName || ''
    detail.trainingCategory = response.commoditySku?.skuProperty?.trainingCategory?.skuPropertyValueName || ''
    detail.trainingProfessional = response.commoditySku?.skuProperty?.trainingProfessional?.skuPropertyValueName || ''

    detail.subject = response.commoditySku?.skuProperty?.discipline?.skuPropertyValueName || ''
    detail.grade = response.commoditySku?.skuProperty?.learningPhase?.skuPropertyValueName || ''
    detail.certificatesType = response.commoditySku?.skuProperty?.certificatesType?.skuPropertyValueName || ''
    detail.practitionerCategory = response.commoditySku?.skuProperty?.practitionerCategory?.skuPropertyValueName || ''
    detail.price = response.order.basicData.amount || 0
    detail.resourceType = WaitExchangeTrainClassDetailVo.getResourceType(response)
    detail.saleChannelName = response.saleChannelName || ''
    detail.saleChannelId = response.saleChannelId || ''
    detail.saleChannel = response.saleChannel || null
    detail.distributorId = response.distributorId || ''
    // detail.professionalName = await QueryTechnologyLevel.queryTechnologyLevelById(
    //   response.commoditySku.skuProperty.technicalGrade.skuPropertyValueId
    // )
    const { assessmentStatus, status } = await WaitExchangeTrainClassDetailVo.getUserStatusInScheme(
      userId,
      detail.schemeId
    )
    detail.assessmentStatus = assessmentStatus
    detail.status = status
    detail.orderNo = response.orderNo || ''
    detail.subOrderNo = response.subOrderNo || ''
    return detail
  }

  /**
   * 批量转换远端模型
   * @param userId
   * @param responseList
   */
  static batchFrom(responseList: BuyerValidCommodityResponse[]) {
    const result = new Array<WaitExchangeTrainClassDetailVo>()
    responseList.forEach((response) => {
      const detail = new WaitExchangeTrainClassDetailVo()
      detail.schemeId = (response.commoditySku?.resource as SchemeResourceResponse)?.schemeId || ''
      detail.commoditySkuId = response.commoditySku?.commoditySkuId || ''
      detail.schemeType = SchemeTypeEnum[(response.commoditySku?.resource as SchemeResourceResponse)?.schemeType]
      detail.schemeName = (response.commoditySku?.resource as SchemeResourceResponse)?.schemeName || ''
      detail.industryPropertyId = response.commoditySku?.skuProperty?.industry?.skuPropertyValueId
      detail.trainingObject =
        response.commoditySku?.skuProperty?.trainingObject?.skuPropertyValueShowName ||
        response.commoditySku?.skuProperty?.trainingObject?.skuPropertyValueName
      detail.positionCategory =
        response.commoditySku?.skuProperty?.positionCategory?.skuPropertyValueShowName ||
        response.commoditySku?.skuProperty?.positionCategory?.skuPropertyValueName
      detail.jobLevel =
        response.commoditySku?.skuProperty?.jobLevel?.skuPropertyValueShowName ||
        response.commoditySku?.skuProperty?.jobLevel?.skuPropertyValueName
      detail.jobCategory =
        response.commoditySku?.skuProperty?.jobCategory?.skuPropertyValueShowName ||
        response.commoditySku?.skuProperty?.jobCategory?.skuPropertyValueName
      detail.industry = response.commoditySku?.skuProperty?.industry?.skuPropertyValueName || ''
      detail.industryId = response.commoditySku?.skuProperty?.industry?.skuPropertyValueId || ''

      detail.year = response.commoditySku?.skuProperty?.year?.skuPropertyValueName || ''
      detail.subjectType = response.commoditySku?.skuProperty?.subjectType?.skuPropertyValueName || ''
      detail.trainingCategory = response.commoditySku?.skuProperty?.trainingCategory?.skuPropertyValueName || ''
      detail.trainingProfessional = response.commoditySku?.skuProperty?.trainingProfessional?.skuPropertyValueName || ''

      detail.subject = response.commoditySku?.skuProperty?.discipline?.skuPropertyValueName || ''
      detail.grade = response.commoditySku?.skuProperty?.learningPhase?.skuPropertyValueName || ''
      detail.certificatesType = response.commoditySku?.skuProperty?.certificatesType?.skuPropertyValueName || ''
      detail.practitionerCategory = response.commoditySku?.skuProperty?.practitionerCategory?.skuPropertyValueName || ''
      detail.price = response.order.basicData.amount || 0
      detail.resourceType = WaitExchangeTrainClassDetailVo.getResourceType(response)
      detail.saleChannelName = response.saleChannelName || ''
      detail.saleChannelId = response.saleChannelId || ''
      detail.saleChannel = response.saleChannel || null
      detail.distributorId = response.distributorId || ''
      // detail.assessmentStatus = assessmentStatus
      // detail.status = status
      detail.orderNo = response.orderNo || ''
      detail.subOrderNo = response.subOrderNo || ''
      if (response.saleChannel == SaleChannelEnum.huayi) {
        detail.thirdPartyPlatform = SaleChannelType.map.get(response.saleChannel)
      } else if (response.commoditySku?.tppTypeId) {
        detail.thirdPartyPlatform = QueryPlatform.map.get(response.commoditySku.tppTypeId)?.name
      }
      result.push(detail)
    })
    return result
  }
  /**
   * 获取培训方案类型
   */
  static getSchemeType(response: BuyerValidCommodityResponse): TrainClassSchemeEnum | null {
    const schemeResource = (response.commoditySku?.resource as SchemeResourceResponse)?.schemeType
    if (schemeResource) {
      if (schemeResource === 'chooseCourseLearning') {
        return TrainClassSchemeEnum.Choose_Course_Learning
      }
      if (schemeResource === 'autonomousCourseLearning') {
        return TrainClassSchemeEnum.Autonomous_Course_Learning
      }
      return null
    }
    return null
  }

  /**
   * 获取用户培训班内状态
   */
  static async getUserStatusInScheme(userId: string, schemeId: string) {
    const data: {
      assessmentStatus: TrainClassAssessmentStatusEnum | null
      status: TrainClassStatusEnum | null
    } = {
      assessmentStatus: null,
      status: null
    }
    // 构建查询参数
    const page: Page = new Page()
    page.pageNo = 1
    page.pageSize = 1
    const request: StudentSchemeLearningRequest = new StudentSchemeLearningRequest()
    // 学员id
    request.student = new UserRequest()
    request.student.userIdList = [userId]
    // 方案id
    request.scheme = new SchemeRequest()
    request.scheme.schemeId = schemeId
    // 学员学习状态
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.status = [1, 2]
    const response = await MsSchemeLearningQuery.pageStudentSchemeLearningInServicer({
      page,
      request
    })
    // 获取查询结果
    const assessmentStatus: number = response.data.currentPageData[0]?.studentLearning?.trainingResult || null
    const status: number = response.data.currentPageData[0]?.learningRegister?.status || 0
    // 设置考核情况
    switch (assessmentStatus) {
      // 未知（待考核）
      case -1:
        data.assessmentStatus = TrainClassAssessmentStatusEnum.For_Inspection
        break
      // 培训合格
      case 1:
        data.assessmentStatus = TrainClassAssessmentStatusEnum.Qualified
        break
      // 培训不合格
      case 0:
        data.assessmentStatus = TrainClassAssessmentStatusEnum.Unqualified
        break
      default:
        data.assessmentStatus = null
        break
    }
    // 设置状态
    switch (status) {
      // 正常
      case 1:
        data.status = TrainClassStatusEnum.Effective
        break
      // 冻结
      case 2:
        data.status = TrainClassStatusEnum.Frozen
        break
      // 失效
      case 3:
        data.status = TrainClassStatusEnum.Failure
        break
      default:
        data.status = null
        break
    }
    return data
  }

  static async batchGetUserStatusInScheme(userId: string, schemeIds: Array<string>) {
    const result = new Map<
      string,
      {
        assessmentStatus: TrainClassAssessmentStatusEnum | null
        status: TrainClassStatusEnum | null
      }
    >()
    const request = new StudentSchemeLearningRequest()
    // 学员id
    request.student = new UserRequest()
    request.student.userIdList = [userId]
    // 方案id
    request.scheme = new SchemeRequest()
    request.scheme.schemeIdList = schemeIds
    // 学员学习状态
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.status = [1, 2]
    const page: Page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    const response = await MsSchemeLearningQuery.pageStudentSchemeLearningInServicer({
      page,
      request
    })
    if (response.status.isSuccess()) {
      response.data.currentPageData.forEach((studentScheme) => {
        schemeIds.forEach((id) => {
          if (studentScheme.scheme.schemeId === id) {
            const data: {
              assessmentStatus: TrainClassAssessmentStatusEnum | null
              status: TrainClassStatusEnum | null
            } = {
              assessmentStatus: null,
              status: null
            }
            const assessmentStatus: number =
              studentScheme?.studentLearning?.trainingResult === 0
                ? 0
                : studentScheme?.studentLearning?.trainingResult || null
            const status: number = studentScheme?.learningRegister?.status || 0
            // 设置考核情况
            switch (assessmentStatus) {
              // 未知（待考核）
              case -1:
                data.assessmentStatus = TrainClassAssessmentStatusEnum.For_Inspection
                break
              // 培训合格
              case 1:
                data.assessmentStatus = TrainClassAssessmentStatusEnum.Qualified
                break
              // 培训不合格
              case 0:
                data.assessmentStatus = TrainClassAssessmentStatusEnum.Unqualified
                break
              default:
                data.assessmentStatus = null
                break
            }
            // 设置状态
            switch (status) {
              // 正常
              case 1:
                data.status = TrainClassStatusEnum.Effective
                break
              // 冻结
              case 2:
                data.status = TrainClassStatusEnum.Frozen
                break
              // 失效
              case 3:
                data.status = TrainClassStatusEnum.Failure
                break
              default:
                data.status = null
                break
            }
            result.set(id, data)
          }
        })
      })
    }
    return result
  }

  /**
   * 获取来源类型
   */
  static getResourceType(response: BuyerValidCommodityResponse): TrainClassResourceEnum | null {
    if (response.channelType === 1) {
      return TrainClassResourceEnum.Personal_Register
    }
    if (response.channelType === 2) {
      return TrainClassResourceEnum.Collective_Register
    }
    if (response.channelType === 3) {
      return TrainClassResourceEnum.Import_Open
    }
    if (response.formExchangeOrder) {
      return TrainClassResourceEnum.Exchange_Train_Class
    }
    if (response.channelType === 5) {
      return 5
    }
    return null
  }
}

export default WaitExchangeTrainClassDetailVo
