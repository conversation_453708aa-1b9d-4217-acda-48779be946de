<template>
  <div>
    <template
      v-if="
        lineData.assessRequire.hasConfigCourse ||
        lineData.assessRequire.hasConfigExam ||
        lineData.trainClassDetail.learningTypeModel.learningExperience.isExamine ||
        (isMixedClass && issueInfo.isOpenAttendance) ||
        (isMixedClass && issueInfo.isOpenGraduationTest) ||
        lineData.periodStudy.questionnaire.require
      "
    >
      <!-- 是否配置课程-->
      <div class="f-flex" v-if="lineData.assessRequire.hasConfigCourse">
        <span>1.</span>
        <div class="f-flex-sub">
          <p>整体考核学时：{{ lineData.assessRequire.totalPeriod }}</p>
          <div>
            <!-- 是否配置测验 -->
            <p v-if="lineData.basicInfo.schemeType == 1">① 班级学习进度=100%</p>
            <div>
              <!-- 开放评价并且强制评价 -->
              <p v-if="lineData.assessRequire.enableAppraisal && lineData.assessRequire.enableCompulsoryAppraisal">
                {{ lineData.basicInfo.schemeType == 1 ? '②' : '①' }} 课程评价：需评价
              </p>
              <p v-else>
                {{ lineData.basicInfo.schemeType == 1 ? '②' : '①' }} 每门课程进度=100%
                <span v-if="lineData.assessRequire.hasConfigCourseQuiz">
                  且测验达{{ lineData.assessRequire.courseQuizPassScore }}分</span
                >
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- 是否配置考试 -->
      <div class="f-flex" v-if="lineData.assessRequire.hasConfigExam">
        <template v-if="lineData.assessRequire.isExamAssessed">
          <span v-if="lineData.hasConfigExam && lineData.hasConfigCourse">2.</span>
          <span v-else>1.</span>
          <div class="f-flex-sub">
            <p>考试成绩>={{ lineData.assessRequire.examPassScore }}分</p>
          </div>
        </template>
        <template v-else>
          <span>2.</span>
          <div class="f-flex-sub">
            <p>班级考试不纳入考核，考试及格分{{ lineData.basicInfo.assessScore }}分</p>
          </div>
        </template>
      </div>
      <!-- 学习心得 -->
      <div class="f-flex" v-if="lineData.trainClassDetail.learningTypeModel.learningExperience.isExamine">
        <span v-show="lineData.hasConfigCourse && lineData.hasConfigExam">3.</span>
        <span
          v-show="
            (!lineData.hasConfigCourse && lineData.hasConfigExam) ||
            (lineData.hasConfigCourse && !lineData.hasConfigExam)
          "
          >2.</span
        >
        <span v-show="!lineData.hasConfigCourse && !lineData.hasConfigExam">1.</span>
        <div class="f-flex-sub">
          <p>学习心得纳入考核</p>
          <div>
            <!-- 是否配置测验 -->
            <p>① 各项学习心得要求以具体配置为准</p>
            <div>
              <p>
                ② 至少参加
                {{ lineData.trainClassDetail.learningTypeModel.learningExperience.joinCount }}
                个心得，其中必选心得
                {{ lineData.trainClassDetail.learningTypeModel.learningExperience.requireCount }} 个。
              </p>
            </div>
            <div>
              <p>③ 学习心得纳入考核，且每项心得均为通过。</p>
            </div>
          </div>
        </div>
      </div>
      <!-- 期别-考勤率 -->
      <div class="f-flex" v-if="isMixedClass && issueInfo.isOpenAttendance">
        <span
          v-text="
            autoIndex(
              lineData.hasConfigCourse,
              lineData.hasConfigExam,
              lineData.trainClassDetail.learningTypeModel.learningExperience.isExamine,
              isMixedClass && issueInfo.isOpenAttendance
            )
          "
        ></span>
        <div class="f-flex-sub">
          <p>考勤考核要求考勤率>={{ lineData.periodStudy.requireAttendanceRate }}%</p>
        </div>
      </div>
      <!-- 期别-结业测试 -->
      <div class="f-flex" v-if="isMixedClass && issueInfo.isOpenGraduationTest">
        <span
          v-text="
            autoIndex(
              lineData.hasConfigCourse,
              lineData.hasConfigExam,
              lineData.trainClassDetail.learningTypeModel.learningExperience.isExamine,
              isMixedClass && issueInfo.isOpenAttendance,
              isMixedClass && issueInfo.isOpenGraduationTest
            )
          "
        ></span>
        <div class="f-flex-sub">
          <p>需完成结业测试</p>
        </div>
      </div>
      <!-- 问卷 -->
      <div
        class="f-flex"
        v-if="lineData.periodStudy.questionnaire.require || lineData.periodStudy.schemeQuestionnaire.require"
      >
        <span
          v-text="
            autoIndex(
              lineData.hasConfigCourse,
              lineData.hasConfigExam,
              lineData.trainClassDetail.learningTypeModel.learningExperience.isExamine,
              isMixedClass && issueInfo.isOpenAttendance,
              isMixedClass && issueInfo.isOpenGraduationTest,
              lineData.periodStudy.questionnaire.require
            )
          "
        ></span>
        <div class="f-flex-sub">
          <p>需完成调研问卷，按具体问卷要求</p>
        </div>
      </div>
    </template>
    <!-- 课程考试都没有配置 -->
    <template
      v-if="
        !lineData.hasConfigCourse &&
        !lineData.hasConfigExam &&
        !lineData.trainClassDetail.learningTypeModel.learningExperience.isExamine &&
        !(isMixedClass && issueInfo.isOpenAttendance) &&
        !(isMixedClass && issueInfo.isOpenGraduationTest) &&
        !lineData.periodStudy.questionnaire.require
      "
    >
      无考核要求
    </template>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import StudentTrainClassDetailVo from '@api/service/management/train-class/query/vo/StudentTrainClassDetailVo'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'

  @Component
  export default class extends Vue {
    /**
     * @description 行数据
     * */
    @Prop({
      type: Object,
      default: () => {
        return new StudentTrainClassDetailVo()
      }
    })
    lineData: StudentTrainClassDetailVo

    /**
     * @description 培训形式枚举
     * */
    TrainingModeEnum = TrainingModeEnum

    /**
     * @description 根据 true 的数量自动返回序号
     * */
    get autoIndex() {
      return (...bools: boolean[]) => {
        let serialNumber = 0
        bools.map((item) => {
          if (item) ++serialNumber
        })
        return serialNumber + '.'
      }
    }

    /**
     * @description 获取期别信息
     * */
    get issueInfo() {
      return this.lineData?.getIssueConfigById(this.lineData?.periodStudy?.periodId) ?? new IssueConfigDetail()
    }

    /**
     * @description 是否面授网班
     * */
    get isMixedClass() {
      const trainingModePropertyValueId =
        this.lineData?.basicInfo?.skuValueNameProperty?.trainingMode?.skuPropertyValueId
      if (!trainingModePropertyValueId) return false
      return (
        trainingModePropertyValueId == TrainingModeEnum.mixed || trainingModePropertyValueId == TrainingModeEnum.offline
      )
    }
  }
</script>
