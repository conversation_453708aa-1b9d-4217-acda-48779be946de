schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""1007:统一社会信用代码不满足18位
		1009:统一社会信用代码不满足只有数字和字母
		1008:身份证号不满足18位
		1010:身份证号不满足只有数字和字母
		2001:社会统一信用代码填了，但是身份证号也填了，或者都没填
		3001:已存在
	"""
	checkMiniDistributor(request:CheckMiniDistributorRequest):CheckMiniDistributorResponse
	"""根据域名查找域名类型
		@return 域名类型(1-网校 2-分销商 3-专题)
	"""
	findInfoByDomainName(domainName:String):FindInfoByDomainResponse @optionalLogin
	"""学员端
		根据推广门户标识与门户类型查询推广门户是否已发布
		@param request
		@return
	"""
	findPublishedInfoByIdentifier(request:FindPublishedInfoByIdentifierRequest):FindPublishedInfoByIdentifierResponse @optionalLogin
	"""当前上下文为分销上下文 判断该 分销商 对应的网校是否过期
		@return
	"""
	isOnlineSchoolContractExpired:Boolean! @optionalLogin
	"""校验分销商是否已存在
		100003 统一社会信用代码格式不合法
		100005 18位身份证格式异常
		100007 分销商已存在
		200002 单位不存在
		@param request
		@return
	"""
	validDistributor(request:ValidDistributorRequest):ValidDistributorResponse
	"""分销商推广门户校验
		@param request
		@return published（是否发布）、status(可用状态)
	"""
	validDistributorPortal(request:ValidDistributorPortalRequest):ValidDistributorPortalResponse @optionalLogin
	"""学员端
		查询该推广门户是否属于该分销商
		@param request
		@return 500 否 200 是
	"""
	whetherBelongDistributor(request:WhetherBelongDistributorRequest):GeneralResponse @optionalLogin
}
type Mutation {
	"""创建分销商
		@param request
		@return
	"""
	createDistributor(request:CreateDistributorRequest):CreateDistributorResponse
	"""创建分销管理员
		@param request
		@return
	"""
	createDistributorAdmin(request:CreateDistributorAdminRequest):GeneralResponse
	"""创建渠道商
		@param request
		@return
	"""
	createMiniDistributor(request:CreateDistributorNewRequest):CreateDistributorNewResponse
	"""启停用推广门户
		@param request
		@return
	"""
	enablePortal(request:EnablePromotionPortalRequest):GeneralResponse
	"""批量导入定价方案"""
	importSupplyPricingSchemeBatch(request:ImportPricingSchemeBatchRequest):ImportPricingSchemeResponse
	"""批量导入定价方案设置"""
	importSupplyPricingSchemeSetupBatch(request:ImportPricingSchemeSetupBatchRequest):ImportPricingSchemeResponse
	"""保存分销商banner列表
		@param request
		@return
	"""
	saveDistributorBannerList(request:DistributorBannerListSaveByPortalTypeRequest):GeneralResponse
	"""保存门户基础信息设置"""
	savePortalBasicMessage(request:SavePortalBasicMessageUpdateRequest):SavePortalBasicMessageUpdateResponse
	"""保存海报配置
		@param request
		@return
	"""
	savePosterConfiguration(request:SavePosterConfigurationRequest):GeneralResponse
	"""启用或停用分销商管理员
		@param request
		@return
	"""
	saveStatusDistributorAdmin(request:SaveStatusDistributorAdminRequest):GeneralResponse
	"""更新分销商
		@param request
		@return
	"""
	updateDistributor(request:UpdateDistributorRequest):GeneralResponse
	"""修改分销管理员
		@param request
		@return
	"""
	updateDistributorAdmin(request:UpdateDistributorAdminRequest):GeneralResponse
	"""更新迷你分销商
		@param request
		@return
	"""
	updateMiniDistributor(request:UpdateDistributorNewRequest):GeneralNewResponse
}
input Attach @type(value:"com.fjhb.domain.basicdata.api.supplier.model.Attach") {
	fileName:String
	filePath:String
}
input FriendLinkRequest @type(value:"com.fjhb.ms.servicer.v1.api.command.request.FriendLinkRequest") {
	title:String
	picture:String
	friendLinkType:Int!
	link:String
	sort:Int
}
"""导入批量授权分销商品
	<AUTHOR>
	@date 2022/11/7 14:12
"""
input ImportPricingSchemeBatchRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.ImportPricingSchemeBatchRequest") {
	"""excel文件路径"""
	filePath:String
	"""文件名称"""
	fileName:String
}
"""导入批量设置定价方案"""
input ImportPricingSchemeSetupBatchRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.ImportPricingSchemeSetupBatchRequest") {
	"""excel文件路径"""
	filePath:String
	"""文件名称"""
	fileName:String
}
"""@author: xucenhao
	@time: 2024-12-09
	@description:
"""
input CheckMiniDistributorRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.CheckMiniDistributorRequest") {
	"""社会统一信用代码"""
	unitCode:String
	"""证件号码"""
	idCard:String
}
"""@author: xucenhao
	@time: 2024-09-10
	@description:
"""
input CreateDistributorAdminRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.CreateDistributorAdminRequest") {
	"""初始token【必传】"""
	token:String!
	"""网校id"""
	onlineSchoolId:String
	"""登录账户【必填】"""
	identity:String!
	"""姓名【必填】"""
	name:String!
	"""性别  女: 0 男:1【必填】"""
	gender:Int!
	"""手机"""
	phone:String
	"""邮箱"""
	email:String
	"""启用状态  正常: 1 禁用: 2【必填】"""
	status:Int!
	"""密码【必填】"""
	password:String!
	"""添加的角色id集合【必填】"""
	addRoleIds:[String]!
}
"""创建分销商请求"""
input CreateDistributorNewRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.CreateDistributorNewRequest") {
	"""统一社会信用代码（类型为企业时必填）"""
	unitCode:String
	"""身份证号（类型为个人时必填）"""
	idCard:String
	"""登录账号（认证方式-用户名）"""
	loginAccount:String
	"""【单位管理员】
		分销商管理员   姓名
	"""
	name:String
	"""【单位管理员】
		分销商管理员   手机号
	"""
	phone:String
	"""分销商类型【必填】
		@see PartnerType
	"""
	distributorType:Int
	"""分销商姓名"""
	distributorName:String
	"""附件信息"""
	attachList:[Attach]
}
"""创建分销商请求"""
input CreateDistributorRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.CreateDistributorRequest") {
	"""统一社会信用代码（类型为企业时必填）"""
	unitCode:String
	"""身份证号（类型为个人时必填）"""
	idCard:String
	"""登录账号（认证方式-用户名）"""
	loginAccount:String!
	"""【单位管理员】
		分销商管理员   姓名
	"""
	name:String!
	"""【单位管理员】
		分销商管理员   手机号
	"""
	phone:String!
	"""分销商类型【必填】
		@see com.fjhb.domain.basicdata.api.supplier.consts.PartnerType
	"""
	distributorType:Int!
	"""附件信息"""
	attachList:[Attach]
	"""分销商姓名"""
	distributorName:String
	"""域名类型
		系统默认域名 1
		自有域名 2
		@see com.fjhb.domain.basicdata.api.servicer.consts.DomainNameTypeConsts
	"""
	domainNameType:Int!
	"""分销商推广门户域名"""
	domainName:String!
}
"""分销商轮播图保存请求"""
input DistributorBannerBannerSaveRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.DistributorBannerBannerSaveRequest") {
	"""轮播图名称"""
	bannerName:String
	"""轮播图链接"""
	bannerLink:String
	"""轮播图路径"""
	bannerPath:String
	"""分类"""
	sort:Int!
}
input DistributorBannerListSaveByPortalTypeRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.DistributorBannerListSaveByPortalTypeRequest") {
	"""分销商id"""
	distributorId:String
	"""门户标识"""
	identifier:String
	"""门户类型"""
	portalType:Int!
	"""分销商轮播图保存请求"""
	bannerSaveRequestList:[DistributorBannerBannerSaveRequest]
}
input DistributorBannerListSaveRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.DistributorBannerListSaveRequest") {
	"""门户类型"""
	portalType:Int!
	"""分销商轮播图保存请求"""
	bannerSaveRequestList:[DistributorBannerBannerSaveRequest]
}
input EnablePromotionPortalRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.EnablePromotionPortalRequest") {
	"""分销商编号"""
	distributorId:String!
	"""门户标识"""
	identifier:String
	"""状态 1启用 0禁用"""
	type:Int!
}
"""@author: xucenhao
	@time: 2024-09-04
	@description:
"""
input FindPublishedInfoByIdentifierRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.FindPublishedInfoByIdentifierRequest") {
	"""分销商id"""
	distributorId:String
	"""门户id"""
	promotionId:String
	"""门户类型"""
	portalType:Int
}
input SavePortalBasicMessageUpdateRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.SavePortalBasicMessageUpdateRequest") {
	"""域名类型
		使用系统域名 1
		自有域名 2
		使用单位域名自动生成 4
		@see com.fjhb.domain.basicdata.api.servicer.consts.DomainNameTypeConsts
	"""
	domainNameType:Int!
	"""推广门户域名"""
	domainName:String
	"""分销商编号"""
	distributorId:String!
	"""门户标识"""
	identifier:String
	"""门户标题"""
	title:String
	"""门户简称"""
	shortName:String
	"""客服电话"""
	csPhone:String
	"""客服咨询时间"""
	csCallTime:String
	"""底部内容(底部落款)"""
	footContent:String
	"""底部内容id(底部落款id)"""
	footContentId:String
	"""轮播图"""
	bannerListSaveRequestList:[DistributorBannerListSaveRequest]
	"""web端图片"""
	webPortMessageRequest:SaveWebPortMessageRequest
	"""线下集体报名配置入口状态 1启用 0禁用"""
	offlineCollectiveRegisterType:Int!
}
"""@author: xucenhao
	@time: 2024-08-26
	@description: 保存海报
"""
input SavePosterConfigurationRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.SavePosterConfigurationRequest") {
	"""海报配置id"""
	id:String
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""门户标识"""
	identifier:String
	"""海报标题"""
	title:String
	"""宣传文案"""
	promotionalCopy:String
	"""ui模版id"""
	templateId:String
	"""客服电话"""
	csPhone:String
	"""二维码相关列表"""
	qrcodeList:[QRCode]
}
"""@author: xucenhao
	@time: 2024-09-12
	@description: 禁用分销商管理员
"""
input SaveStatusDistributorAdminRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.SaveStatusDistributorAdminRequest") {
	"""网校id"""
	onlineSchoolId:String
	"""账户id"""
	accountId:String
	"""角色id集合"""
	roleIds:[String]
	"""状态 0 禁用 1 启用"""
	status:Int!
}
input SaveWebPortMessageRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.SaveWebPortMessageRequest") {
	"""门户logo"""
	logo:String
	"""浏览器图标"""
	icon:String
	"""移动二维码"""
	mobileQRCode:String
	"""客服电话图片"""
	csPhonePicture:String
	"""在线客服代码内容id"""
	csOnlineCodeId:String
	"""培训流程图片"""
	trainingFlowPicture:String
	"""友情链接类型
		@see com.fjhb.domain.basicdata.api.servicer.consts.FriendLinkTypes
		1-文本  2-图片
	"""
	friendLinkTypes:Int!
	"""友情链接集合"""
	friendLinks:[FriendLinkRequest]
	"""cnzz信息"""
	cnzz:String
	"""目录名"""
	dirName:String
}
"""@author: xucenhao
	@time: 2024-09-10
	@description:
"""
input UpdateDistributorAdminRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.UpdateDistributorAdminRequest") {
	"""初始token【必传】"""
	token:String!
	"""网校id"""
	onlineSchoolId:String
	"""被修改的管理员账户ID【必填】"""
	accountId:String!
	"""登录账户【为null，表示不更新】"""
	identity:String
	"""姓名【为null，表示不更新】"""
	name:String
	"""性别【为null，表示不更新】"""
	gender:Int
	"""手机【为null，表示不更新】"""
	phone:String
	"""邮箱【为null，表示不更新】"""
	email:String
	"""启用状态  正常: 1 禁用: 2【必填】"""
	status:Int!
	"""添加的角色id集合"""
	addRoleIds:[String]
	"""移除的角色id集合"""
	removeRoleIds:[String]
}
"""@author: linxiquan
	@Date: 2023/12/8 13:54
	@Description: 修改分销商请求
"""
input UpdateDistributorNewRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.UpdateDistributorNewRequest") {
	"""分销商id"""
	distributorId:String
	"""分销商类型【必填】
		@see PartnerType
	"""
	distributorType:Int
	"""分销商姓名"""
	distributorName:String
	"""附件信息"""
	attachList:[Attach]
}
"""@author: linxiquan
	@Date: 2023/12/8 13:54
	@Description: 修改分销商请求
"""
input UpdateDistributorRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.UpdateDistributorRequest") {
	"""分销商id"""
	distributorId:String!
	"""分销商类型【必填】
		@see com.fjhb.domain.basicdata.api.supplier.consts.PartnerType
	"""
	distributorType:Int!
	"""附件信息"""
	attachList:[Attach]
	"""分销商姓名"""
	distributorName:String
	"""域名类型
		系统默认域名 1
		自有域名 2
		@see com.fjhb.domain.basicdata.api.servicer.consts.DomainNameTypeConsts
	"""
	domainNameType:Int!
	"""分销商推广门户域名"""
	domainName:String!
}
"""@Description 分销商推广门户校验入参请求
	<AUTHOR>
	@Date 2025/3/31 8:56
"""
input ValidDistributorPortalRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.ValidDistributorPortalRequest") {
	"""分销商id"""
	distributorId:String
	"""门户id"""
	promotionId:String
	"""门户类型"""
	portalType:Int
}
input ValidDistributorRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.ValidDistributorRequest") {
	"""服务商类型【必填】
		@see  com.fjhb.domain.basicdata.api.supplier.consts.PartnerType
	"""
	servicerType:Int
	"""统一社会信用代码（类型为企业时必填）"""
	unitCode:String
	"""身份证号（类型为个人时必填）"""
	idCard:String
}
"""@author: xucenhao
	@time: 2024-09-04
	@description:
"""
input WhetherBelongDistributorRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distributor.WhetherBelongDistributorRequest") {
	"""分销商id"""
	distributorId:String
	"""门户id"""
	promotionId:String
}
input QRCode @type(value:"com.fjhb.platform.jxjy.v1.kernel.repository.mysql.dto.PosterConfigurationDto$QRCode") {
	"""二维码图片"""
	mobileQrcode:String
	"""二维码操作提示"""
	qrcodeTip:String
}
"""<AUTHOR> [2023/7/11 20:54]"""
type GeneralNewResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.GeneralNewResponse") {
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""<AUTHOR> [2023/7/11 20:54]"""
type GeneralResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.GeneralResponse") {
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""@Description
	<AUTHOR>
	@Date 2024/3/6 10:42
"""
type ImportPricingSchemeResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.ImportPricingSchemeResponse") {
	"""批次号"""
	batchNo:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""@author: xucenhao
	@time: 2024-08-23
	@description:
"""
type SavePortalBasicMessageUpdateResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distribution.SavePortalBasicMessageUpdateResponse") {
	webPortalId:String
	identifier:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""@author: xucenhao
	@time: 2024-12-09
	@description:
"""
type CheckMiniDistributorResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distributor.CheckMiniDistributorResponse") {
	"""渠道商id"""
	id:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
type CreateDistributorNewResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distributor.CreateDistributorNewResponse") {
	"""服务商id"""
	servicerId:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
type CreateDistributorResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distributor.CreateDistributorResponse") {
	"""服务商id"""
	servicerId:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""@Description 根据域名查询响应
	<AUTHOR>
	@Date 2025/3/31 10:06
"""
type FindInfoByDomainResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distributor.FindInfoByDomainResponse") {
	"""域名类型(1-网校 2-分销商 3-专题)
		@See com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.consts.DomainTypeEnum
	"""
	domainType:Int!
}
"""@author: xucenhao
	@time: 2024-09-04
	@description:
"""
type FindPublishedInfoByIdentifierResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distributor.FindPublishedInfoByIdentifierResponse") {
	"""是否发布"""
	published:Boolean!
}
"""@Description 分销商推广门户校验
	<AUTHOR>
	@Date 2025/3/31 8:53
"""
type ValidDistributorPortalResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distributor.ValidDistributorPortalResponse") {
	code:String
	message:String
	"""是否发布"""
	published:Boolean!
	"""可用状态"""
	status:Boolean
}
type ValidDistributorResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distributor.ValidDistributorResponse") {
	"""服务商id"""
	servicerId:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}

scalar List
