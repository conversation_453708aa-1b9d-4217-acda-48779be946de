import QuestionLibraryAction from './mutation/QuestionLibraryAction'
import CreateQuestionLibraryCategory from './mutation/CreateQuestionLibrary'
class MutationQuestionLibraryFactory {
  /**
   * @description: 获取创建题库分类业务类
   * @param {*}
   * @return {*}
   */

  get createQuestionLibraryCategory() {
    return new CreateQuestionLibraryCategory()
  }

  /**
   * @description: 获取题库基础操作类-删除修改
   * @param {*}
   * @return {*}
   */
  get questionLibraryAction() {
    return (id: string, name: string, parentId: string, description?: string) => {
      return new QuestionLibraryAction(id, name, parentId, description)
    }
  }
}
export default new MutationQuestionLibraryFactory()
