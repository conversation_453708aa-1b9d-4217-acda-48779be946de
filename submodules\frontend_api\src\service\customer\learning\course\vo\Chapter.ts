import { CourseOutlinePlayResourceResponse } from '@api/ms-gateway/ms-course-play-resource-v1'
import Courseware from '@api/service/customer/learning/course/vo/Courseware'

class Chapter {
  id: string
  name: string
  coursewares: Array<Courseware> = new Array<Courseware>()
  parentId: string
  sort: number

  // 是否有课件
  hasCourseware() {
    return !!this.coursewares.length
  }

  static from(response: CourseOutlinePlayResourceResponse) {
    const detail = new Chapter()
    detail.id = response.id
    detail.name = response.name
    detail.parentId = response.parentId
    detail.sort = response.sort
    return detail
  }
}

export default Chapter
