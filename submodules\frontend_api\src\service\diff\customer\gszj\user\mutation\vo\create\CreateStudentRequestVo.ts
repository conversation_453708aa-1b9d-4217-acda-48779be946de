import CreateStudentRequestVo from '@api/service/customer/user/mutation/vo/create/CreateStudentRequestVo'
import CreateUserIndustryRequestVo from '@api/service/customer/user/mutation/vo/create/CreateUserIndustryRequestVo'

/*
 *创建学员信息
 */
class CreateStudentRequestVoDiff extends CreateStudentRequestVo {
  /**
   * 地区code
   */
  areaCode?: string
  /**
   * 工作单位性质
   */
  unitNature?: string
  /**
   * 在编情况
   */
  staffingStatus?: string
  /**
   * 是否在专技岗位工作
   */
  isZjPosition?: string
  /**
   * 职称系列
   */
  titleSeries?: string
  /**
   * 职称专业
   */
  titleProfessional?: string
  /**
   * 现有职称等级
   */
  titleGrade?: string
  /**
   * 现有职称资格名称
   */
  titleQualificationName?: string
  /**
   * 现有职称有效范围
   */
  titleEffectiveRange?: string
  /**
   * 最高学历
   */
  highestEducationLevel?: string
  // 给证件类型赋初始值
  constructor() {
    super()
    this.idCardType = null
  }
}

export default CreateStudentRequestVoDiff
