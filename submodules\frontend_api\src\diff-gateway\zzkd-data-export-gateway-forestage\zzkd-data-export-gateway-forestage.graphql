"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""集体报名管理员导出明细数据新口
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInCollective(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""查询导出任务信息
		@param page
		@param jobRequest
		@return
	"""
	pageExportTaskInfoInMyself(page:Page,jobRequest:JobRequest):UserJobLogResponsePage @page(for:"UserJobLogResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input StudentSchemeLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningRequest") {
	studentNoList:[String]
	student:UserRequest
	learningRegister:LearningRegisterRequest
	scheme:SchemeRequest
	studentLearning:StudentLearningRequest
	dataAnalysis:DataAnalysisRequest
	connectManageSystem:ConnectManageSystemRequest
	extendedInfo:ExtendedInfoRequest
	openPrintTemplate:Boolean
	saleChannels:[Int]
	trainingChannelName:String
	trainingChannelId:String
	notDistributionPortal:Boolean
	trainingType:String
	issueId:String
}
input ConnectManageSystemRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ConnectManageSystemRequest") {
	syncStatus:Int
}
input DataAnalysisRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.DataAnalysisRequest") {
	trainingResultPeriod:DoubleScopeRequest
	requirePeriod:DoubleScopeRequest
	acquiredPeriod:DoubleScopeRequest
}
input ExtendedInfoRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ExtendedInfoRequest") {
	whetherToPrint:Boolean
	applyCompanyCode:String
	policyTrainingSchemeId:String
	policyTrainingSchemeName:String
}
input LearningRegisterRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.LearningRegisterRequest") {
	registerType:Int
	sourceType:String
	sourceId:String
	status:[Int]
	registerTime:DateScopeRequest
	saleChannels:[Int]
	orderNoList:[String]
	subOrderNoList:[String]
	batchOrderNoList:[String]
	distributorId:String
	portalId:String
}
input RegionRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.RegionRequest") {
	province:String
	city:String
	county:String
}
input StudentLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.learning.StudentLearningRequest") {
	trainingResultList:[Int]
	trainingResultTime:DateScopeRequest
	notLearningTypeList:[Int]
	courseScheduleStatus:Int
	examAssessResultList:[Int]
}
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertySearchRequest") {
	region:[RegionSkuPropertyRequest]
	regionSearchType:Int
}
input SchemeRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeRequest") {
	schemeId:String
	schemeIdList:[String]
	schemeType:String
	schemeName:String
	skuProperty:SchemeSkuPropertyRequest
}
input SchemeSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeSkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	positionCategory:[String]
	trainingObject:[String]
	jobLevel:[String]
	jobCategory:[String]
	subject:[String]
	grade:[String]
	learningPhase:[String]
	discipline:[String]
	qualificationCategory:[String]
	trainingWay:[String]
	trainingInstitution:[String]
	mainAdditionalItem:[String]
}
input UserPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserPropertyRequest") {
	regionList:[RegionRequest]
	companyName:String
	payOrderRegionList:[RegionRequest]
}
input UserRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserRequest") {
	userIdList:[String]
	accountIdList:[String]
	userProperty:UserPropertyRequest
}
"""功能描述：任务查询参数
	@Author： wtl
	@Date： 2022/1/18 15:13
"""
input JobRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.zzkd.v1.kernel.geteway.request.export.JobRequest") {
	"""任务组名（必填）"""
	group:String
	"""任务组名匹配方式（EQ：完全匹配 LIKE：模糊匹配[*group*] LLIKE：左模糊匹配[group*] RLIKE：右模糊匹配[*group]，不传值默认为完全匹配）"""
	groupOperator:String
	"""任务名（模糊查询）"""
	jobName:String
	"""任务状态(executing:运行中 executed:运行完成 fail:运行失败)
		@see UserJobState
	"""
	jobState:String
	"""任务执行时间 yyyy-MM-dd HH:mm:ss"""
	executeTimeScope:DateScopeRequest
	"""异步任务处理结果（true:成功 false:失败）"""
	jobResult:Boolean
	"""分割粒度
		null-无 1-单位
	"""
	granularity:Int
}
"""功能描述：异步任务日志返回对象
	@Author： wtl
	@Date： 2022/4/11 17:18
"""
type UserJobLogResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.zzkd.v1.kernel.geteway.response.export.UserJobLogResponse") {
	"""任务id"""
	jobId:String
	"""任务组名"""
	group:String
	"""任务名"""
	jobName:String
	"""任务开始时间"""
	beginTime:DateTime
	"""任务结束时间"""
	endTime:DateTime
	"""任务状态(executing:运行中 executed:运行完成 fail:运行失败)
		@see UserJobState
	"""
	jobState:String
	"""异步任务处理结果（true:成功 false:失败）"""
	jobResult:Boolean
	"""任务执行成功或失败的信息"""
	message:String
	"""导出文件路径"""
	exportFilePath:String
	"""是否受保护"""
	isProtected:Boolean!
	"""资源id"""
	fileResourceId:String
	"""操作人id"""
	operatorUserId:String
	"""操作人帐户id"""
	operatorAccountId:String
}

scalar List
type UserJobLogResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserJobLogResponse]}
