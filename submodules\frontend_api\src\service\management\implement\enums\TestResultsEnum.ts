import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * 测试结果
 */
export enum TestResultsEnum {
  /**
   * 合格
   */
  qualified = 1,

  /**
   * 不合格
   */
  unqualified = 2
}

class TestResults extends AbstractEnum<TestResultsEnum> {
  static enum = TestResultsEnum
  constructor(status?: TestResultsEnum) {
    super()
    this.current = status
    this.map.set(TestResultsEnum.qualified, '合格')
    this.map.set(TestResultsEnum.unqualified, '不合格')
  }
}

export default new TestResults()
