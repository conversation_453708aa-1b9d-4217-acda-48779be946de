import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 行业属性id枚举
 */

export enum PersonIndustryIdEnum {
  /**
   * 人社行业 福建省
   */
  RS = '7D2B1D748029491FBF649153F8518B95',
  /**
   * 河南省
   */
  HN = '65F6A794292E443DBE78D5C13EA31462',
  /**
   * 安徽省
   */
  AH = '6ba7b8109dad11d180b400c04fd430c8',
  /**
   * 甘肃省
   */
  GS = 'CB8A5975CDB341AE831E25ED9889242F',

  /**
   * 江西省
   */
  JX = '052E23B08935457DBE2FF77D727180D2',

  /**
   * 四川省人社
   */
  SCRS = 'CB5DF8BC2B6340E19D9CBC347D0D1B01',

  /**
   * 建设行业
   */
  JS = 'F5715E60547111EFA5F937C05DB78097',

  /**
   * 四川建设
   */
  SC = 'B8FE31AC3C36472EA21AD90A9803D6BC',

  /**
   * 卫生行业
   */
  WS = 'AECCC6DBB3484EBA9E2C4F7D35BF7296',

  /**
   * 工勤行业
   */
  GQ = '4964B77F46D04C98983E83665B084EBD',
  /**
   * 教师行业
   */
  LS = '19C0AABA37CF4A6CBF65E923F67FAD4B',

  /**
   * 江苏省
   */
  JSS = 'BAFC867AD2984C0E96C516588F83CDAA',

  /**
   * 药师行业
   */
  YS = '887AA20A2C874447AB006F444C25E36D'
}
export default class PersonIndustryId extends AbstractEnum<PersonIndustryIdEnum> {
  static enum = PersonIndustryIdEnum

  constructor(status?: PersonIndustryIdEnum) {
    super()
    this.current = status
    this.map.set(PersonIndustryIdEnum.RS, '福建省')
    this.map.set(PersonIndustryIdEnum.HN, '河南省')
    this.map.set(PersonIndustryIdEnum.AH, '安徽省')
    this.map.set(PersonIndustryIdEnum.GS, '甘肃省')
    this.map.set(PersonIndustryIdEnum.JS, '建设')
    this.map.set(PersonIndustryIdEnum.SC, '四川省')
    this.map.set(PersonIndustryIdEnum.JSS, '江苏省')
    this.map.set(PersonIndustryIdEnum.JX, '江西省')
    this.map.set(PersonIndustryIdEnum.WS, '职业卫生')
    this.map.set(PersonIndustryIdEnum.GQ, '工勤')
    this.map.set(PersonIndustryIdEnum.LS, '教师')
    this.map.set(PersonIndustryIdEnum.YS, '药师')
    this.map.set(PersonIndustryIdEnum.SCRS, '四川省人社')
  }
}
