import {
  CommoditySkuRequest,
  RegionSkuPropertyRequest,
  RegionSkuPropertySearchRequest,
  SkuPropertyRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'

/**
 * 地区sku查询条件
 */
export class RegionSkuRequest {
  /**
   * 省级id
   */
  provinceId = ''
  /**
   * 市级id
   */
  cityId = ''
  /**
   * 区县级id
   */
  countyId = ''

  to(): RegionSkuPropertyRequest {
    const to = new RegionSkuPropertyRequest()
    to.province = this.provinceId ? this.provinceId : undefined
    to.city = this.cityId ? this.cityId : undefined
    to.county = this.countyId ? this.countyId : undefined
    return to
  }
}

/**
 * @description 方案Sku属性请求
 */
class SchemeSkuPropertyRequest {
  /**
   * 行业id
   */
  industryId = ''
  /**
   * 年度id
   */
  yearId = ''
  /**
   * 科目类型id
   */
  subjectTypeId = ''
  /**
   * 培训类别id
   */
  trainingCategoryId = ''
  /**
   * 培训专业id
   */
  trainingProfessionalId = ''
  /**
   * 地区id集合
   */
  region: RegionSkuRequest[] = []

  /**
   * 查询sku请求
   */
  toSkuPropertyRequest(): SkuPropertyRequest {
    const to = new SkuPropertyRequest()
    to.industry = this.industryId ? [this.industryId] : undefined
    if (this.region && this.region.length) {
      to.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      to.regionSkuPropertySearch.regionSearchType = 1
      to.regionSkuPropertySearch.region = this.region.map(item => item.to())
    }
    to.year = this.yearId ? [this.yearId] : undefined
    to.subjectType = this.subjectTypeId ? [this.subjectTypeId] : undefined
    to.trainingCategory = this.trainingCategoryId ? [this.trainingCategoryId] : undefined
    to.trainingProfessional = this.trainingProfessionalId ? [this.trainingProfessionalId] : undefined
    return to
  }
}

export default SchemeSkuPropertyRequest
