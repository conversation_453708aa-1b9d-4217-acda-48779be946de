import { StudentCourseLearningResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class StudentCourseVo extends StudentCourseLearningResponse {
  /**
   * 课程id
   */
  id = ''
  /**
   * 课程大纲id
   */
  outlineId = ''
  /**
   * 课程名称
   */
  name = ''

  /**
   * 课程学时
   */
  period = 0

  /**
   * 课程学习进度
   */
  schedule = 0

  /**
   * 开始学习时间
   */
  startLearningTime = ''

  /**
   * 最后学习时间
   */
  lastLearningTime = ''

  /**
   * 课程学习状态 0：未评定 1：未合格 2：合格
   */
  status: number

  /**
   * 课程学习取得结果时间
   */
  learningResultTime: string

  /**
   * 测验得分
   */
  quizScore = 0

  /**
   * 是否测验合格 0：未评定 1：未合格 2：合格
   */
  courseQuizStatus: number = null

  /**
   * 课程学习方式资源类型(1：选课学习场景 2：自主课程学习场景 3：兴趣课程学习场景)
   */
  courseLearningResourceType: string = null

  /**
   * 大纲内课程类型(1：必修 2：选修 3：兴趣课)，仅当课程学习方式为选课规则时有效
   */
  courseType: number = null

  /**
   * 课程时长
   */
  courseTimeLength = 0

  /**
   * 学员课程id
   */
  studentCourseId = ''

  static from(response: StudentCourseLearningResponse) {
    const detail = new StudentCourseVo()
    Object.assign(detail, response)
    detail.id = response.course?.courseId ?? null
    detail.name = response.course?.courseName ?? null
    detail.schedule = response.studentCourseMediaLearningRecord?.schedule ?? 0
    detail.startLearningTime = response.studentCourseMediaLearningRecord?.startLearningTime ?? null
    detail.lastLearningTime = response.studentCourseMediaLearningRecord?.lastLearningTime ?? null
    detail.status = response.studentCourse?.courseLearningStatus ?? null
    detail.learningResultTime = response.studentCourse?.learningResultTime ?? null
    detail.quizScore = response.studentCourseQuiz?.score ?? 0
    detail.courseQuizStatus = response.studentCourseQuiz?.courseQuizStatus ?? null
    detail.courseLearningResourceType = response.range?.courseLearningResourceType ?? null
    detail.courseType = response.courseOfCourseTrainingOutline?.courseType ?? null
    detail.studentCourseId = response.studentCourse?.studentCourseId
    detail.outlineId = response.courseOfCourseTrainingOutline.outlineId
    detail.period = response.courseOfCourseTrainingOutline.period
    detail.courseTimeLength = response.course?.courseTimeLength
    return detail
  }
}
export default StudentCourseVo
