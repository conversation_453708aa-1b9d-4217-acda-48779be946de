import CourseCategoryDetail from '@api/service/management/resource/course-category/query/vo/CourseCategoryDetail'

class UpdateCourseCategory {
  /**
   * 课程分类id
   */
  id: string = undefined
  /**
   * 课程分类名称
   */
  name: string = undefined
  /**
   * 父级课程分类 顶级为-1
   */
  parentId: string = undefined
  /**
   * 排序
   */
  sort: number = undefined
  parentName: string

  /**
   * 类型转换静态方法
   * @param responseDetail
   */
  static from(responseDetail: CourseCategoryDetail) {
    const detail = new UpdateCourseCategory()
    detail.id = responseDetail.id
    detail.name = responseDetail.name
    detail.parentId = responseDetail.parentId
    detail.sort = responseDetail.sort
    return detail
  }

  reset() {
    return ''
  }
}

export default UpdateCourseCategory
