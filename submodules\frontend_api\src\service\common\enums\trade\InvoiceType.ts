/**
 * 发票类型：
 *  1、普通发票
 *  2、增值税普通发票
 *  3、增值税专用发票
 */
import AbstractEnum from '../AbstractEnum'

enum InvoiceTypeEnum {
  normal = 0,
  vat_normal = 1,
  vat_special = 2
}

export { InvoiceTypeEnum }

class InvoiceType extends AbstractEnum<InvoiceTypeEnum> {
  static enum = InvoiceTypeEnum

  constructor() {
    super()
    this.map[InvoiceTypeEnum.normal] = '普通发票'
    this.map[InvoiceTypeEnum.vat_normal] = '增值税普通发票'
    this.map[InvoiceTypeEnum.vat_special] = '增值税专用发票'
  }
}

export default InvoiceType
