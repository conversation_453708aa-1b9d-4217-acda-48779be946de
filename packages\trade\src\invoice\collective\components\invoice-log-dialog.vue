<template>
  <el-drawer title="操作记录" :visible.sync="logVisible" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <div class="f-mt20 f-mlr40" v-if="operationLogList.length">
        <el-timeline>
          <el-timeline-item v-for="(item, index) in operationLogList" :key="index">
            {{ item.operationStr || '-' }}
          </el-timeline-item>
        </el-timeline>
      </div>
      <el-alert type="info" :closable="false" class="m-alert" v-else>
        <div class="f-ptb30 f-tc">该发票暂时还没有操作记录！</div>
      </el-alert>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop } from 'vue-property-decorator'
  import QueryOffLineInvoice from '@api/service/management/trade/batch/invoice/query/QueryOffLineInvoice'
  import OperationLogItem from '@api/service/management/trade/batch/invoice/query/vo/OperationLogItem'
  import QueryOperationInvoiceLog from '@api/service/management/trade/single/invoice/query/QueryOperationInvoiceLog'

  @Component
  export default class extends Vue {
    // 加载
    loading = false
    // 开关
    logVisible = false
    // 查询类（线下/专用）
    queryOffLineInvoice = new QueryOffLineInvoice()
    // 查询类（自动）
    queryOperationInvoiceLog = new QueryOperationInvoiceLog()
    // 列表
    operationLogList = new Array<OperationLogItem>()
    // 是否自动开票
    @Prop({
      type: Boolean,
      default: true
    })
    isAuto: boolean
    init(id: string) {
      this.logVisible = true
      this.loading = true
      const queryReq = this.isAuto
        ? this.queryOperationInvoiceLog.queryOperationLog(id)
        : this.queryOffLineInvoice.listOfflineInvoiceOperationRecord(id)
      queryReq
        .then((res) => {
          this.operationLogList = res
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
</script>
