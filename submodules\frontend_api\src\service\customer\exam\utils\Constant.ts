// 0 表示混合题型，即该大题下存在多种试题类型的组合
// 1 表示单选题
// 2 表示多选题
// 3 表示填空题
// 4 表示判断题
// 5 表示简答题
// 6 表示父子题
export enum QuestionTypeEnum {
  QuestionTypeEnumMinxins = 0,
  QuestionTypeEnumRadio = 1,
  QuestionTypeEnumMul,
  QuestionTypeEnumFill,
  QuestionTypeEnumOpi,
  QuestionTypeEnumAsk,
  QuestionTypeEnumFather
}
export class QuestionKeyValue {
  static getQuestionAnswerKey(questionType: QuestionTypeEnum) {
    switch (questionType) {
      case QuestionTypeEnum.QuestionTypeEnumAsk:
        return ''
        break
      case QuestionTypeEnum.QuestionTypeEnumFather:
        return ''
        break
      case QuestionTypeEnum.QuestionTypeEnumFill:
        return ''
        break
      case QuestionTypeEnum.QuestionTypeEnumMinxins:
        return ''
        break
      case QuestionTypeEnum.QuestionTypeEnumMul:
        return 'multipleAnswer'
        break
      case QuestionTypeEnum.QuestionTypeEnumOpi:
        return 'opinionAnswer'
        break
      case QuestionTypeEnum.QuestionTypeEnumRadio:
        return 'radioAnswer'
        break
      default:
        return ''
        break
    }
  }
  static getQuestionName(questionType: number) {
    switch (questionType) {
      case QuestionTypeEnum.QuestionTypeEnumRadio:
        return '单选题'
        break
      case QuestionTypeEnum.QuestionTypeEnumMinxins:
        return '混合题型'
        break
      case QuestionTypeEnum.QuestionTypeEnumMul:
        return '多选题'
        break
      case QuestionTypeEnum.QuestionTypeEnumFill:
        return '填空题'
        break
      case QuestionTypeEnum.QuestionTypeEnumOpi:
        return '判断题'
        break
      case QuestionTypeEnum.QuestionTypeEnumAsk:
        return '简答题'
        break
      case QuestionTypeEnum.QuestionTypeEnumFather:
        return '父子题'
        break

      default:
        return '未知'
        break
    }
  }
}
