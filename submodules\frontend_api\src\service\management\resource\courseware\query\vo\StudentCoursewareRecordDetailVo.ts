import { CoursewareLearningRecordResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

/**
 * @description 学员课件学习记录详情
 */
class StudentCoursewareRecordDetailVo extends CoursewareLearningRecordResponse {
  /**
   * 课件id
   */
  id = ''

  /**
   * 课件名称
   */
  name = ''

  /**
   * 课件学习进度
   */
  schedule = 0

  /**
   * 课件开始学习时间
   */
  startLearningTime = ''

  /**
   * 最后学习时间
   */
  lastLearningTime = ''

  /**
   * 课件时长
   */
  coursewareTimeLength = 0

  static from(response: CoursewareLearningRecordResponse): StudentCoursewareRecordDetailVo {
    const detail = response as StudentCoursewareRecordDetailVo
    detail.id = response.courseware?.coursewareId ?? null
    detail.schedule = response.coursewareLearningRecord?.schedule ?? null
    detail.startLearningTime = response.coursewareLearningRecord?.startLearningTime ?? null
    detail.lastLearningTime = response.coursewareLearningRecord?.lastLearningTime ?? null
    return detail
  }
}

export default StudentCoursewareRecordDetailVo
