import CreateOrderCommodity from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/CreateOrderCommodity'
import { Commodity, CreateOrderRequest, InvoiceInfoRequest, IssueInfo } from '@api/ms-gateway/ms-order-v1'
import { CommodityRequest, CreatedMarketingOrderRequest } from '@api/platform-gateway/platform-jxjy-marketing-order-v1'
import UserModule from '@api/service/customer/user/UserModule'
import { countCommodityChannelVendorOpenNumber } from '@api/gateway/PlatformCommodity/graphql-importer'
import AccommodationInformation from '@api/service/customer/trade/single/query/vo/AccommodationInformation'
import { LodgingTypeEnum } from '@api/service/common/implement/enums/LodgingTypeEnum'

export default class CreateOrderParams {
  /**
   * 买家编号
   */
  buyerId: string = undefined

  /**
   * 商品信息
   */
  commodities: Array<CreateOrderCommodity> = new Array<CreateOrderCommodity>()

  /**
   * 购买渠道类型
   1-用户自主购买
   2-集体缴费
   3-管理员导入
   */
  purchaseChannelType: number = undefined

  /**
   * 终端类型
   <p>
   Web端：Web
   IOS端：IOS
   安卓端：Android
   微信小程序：WechatMini
   微信公众号：WechatOfficial
   */
  terminalCode: string = undefined

  // /**
  //  * 渠道商编号
  //  */
  // channelVendorId?: string

  /**
   * 是否需要发票
   */
  needInvoice: boolean

  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceInfoRequest = new InvoiceInfoRequest()

  /**
   * 参训单位id
   */
  participatingUnitId?: string

  /**
   * 销售渠道Id（自营渠道为空，专题渠道时必填）
   */
  saleChannelId?: string = undefined

  /**
   * 销售渠道类型
   0-自营渠道
   2-专题渠道
   */
  saleChannel: number = undefined

  /**
   * 购买来源类型，1-门户，2-专题
   @see com.fjhb.ms.order.v1.api.consts.PurchaseSourceType
   */
  purchaseSourceType?: number = undefined

  /**
   * 住宿采集信息
   */
  accommodationInformation: AccommodationInformation = new AccommodationInformation()

  /**
   * 转换成通用的下单模型
   */
  toGeneralCreateOrderRequest() {
    const request = new CreateOrderRequest()
    request.buyerId = UserModule.queryUserFactory.getQueryUserInfo()?.userInfo.userInfo.userId
    request.commodities = this.commodities.map((item) => {
      const commodity = new Commodity()
      commodity.quantity = item.quantity
      commodity.skuId = item.skuId
      if (item.issueId) {
        commodity.issueInfo = new IssueInfo()
        commodity.issueInfo.issueId = item.issueId
        if (this.accommodationInformation.isAccommodation) {
          commodity.issueInfo.accommodationType = this.accommodationInformation.accommodationType
        } else {
          commodity.issueInfo.accommodationType = LodgingTypeEnum.noNeed
        }
      }
      return commodity
    })
    request.needInvoice = this.needInvoice
    if (this.needInvoice) {
      request.invoiceInfo = this.invoiceInfo
    }
    request.participatingUnitId = this.participatingUnitId
    request.purchaseChannelType = this.purchaseChannelType
    request.saleChannel = this.saleChannel
    request.saleChannelId = this.saleChannelId
    request.terminalCode = this.terminalCode

    return request
  }

  /**
   * 转换成分销下单模型
   */
  toFxCreateOrderRequest(token: string) {
    const request = new CreatedMarketingOrderRequest()
    request.buyerId = UserModule.queryUserFactory.getQueryUserInfo()?.userInfo.userInfo.userId
    request.commodities = this.commodities.map((item) => {
      const commodity = new CommodityRequest()
      commodity.quantity = item.quantity
      commodity.policyId = item.policyId
      commodity.policyType = item.policyType
      commodity.commodityAuthId = item.commodityAuthId
      commodity.commoditySkuId = item.skuId
      if (item.issueId) {
        commodity.issueInfo = new IssueInfo()
        commodity.issueInfo.issueId = item.issueId
        if (this.accommodationInformation.isAccommodation) {
          commodity.issueInfo.accommodationType = this.accommodationInformation.accommodationType
        } else {
          commodity.issueInfo.accommodationType = LodgingTypeEnum.noNeed
        }
      }
      return commodity
    })
    request.needInvoice = this.needInvoice
    if (this.needInvoice) {
      request.invoiceInfo = this.invoiceInfo
    }
    request.purchaseChannelType = this.purchaseChannelType
    request.participatingUnitId = this.participatingUnitId
    request.terminalCode = this.terminalCode
    request.saleChannelPurchaseToken = token

    return request
  }
}
