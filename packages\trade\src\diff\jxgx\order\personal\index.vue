<route-meta>
{
"isMenu": true,
"title": "个人报名订单",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <jxgx-trade></jxgx-trade>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import Trade from '@hbfe/jxjy-admin-trade/src/order/personal/index.vue'
  import TradeExport from '@api/service/diff/management/jxgx/trade/TradeExport'
  @Component
  class JxgxTrade extends Trade {
    // 导出订单实例
    exportOrderObj = new TradeExport()

    /**
     * 导出订单
     */
    async doExportListTy() {
      return await this.exportOrderObj.exportOrder(this.queryParams)
    }

    /**
     * 导出订单分销
     */
    async doExportListFx() {
      return await this.exportOrderObj.exportFxOrder(this.queryParams)
    }
  }
  @Component({
    components: {
      JxgxTrade
    }
  })
  export default class extends Vue {}
</script>
