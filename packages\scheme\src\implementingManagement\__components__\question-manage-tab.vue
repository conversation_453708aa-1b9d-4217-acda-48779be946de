<template>
  <div>
    <el-card shadow="never" class="m-card">
      <!--条件查询-->
      <el-row :gutter="16" class="m-query is-border-bottom">
        <el-form :inline="true" label-width="auto">
          <el-col :sm="12" :md="8" :xl="6">
            <el-form-item label="问卷名称">
              <el-input v-model="questionnaireInfoManage.params.name" clearable placeholder="请输入问卷名称" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="8" :xl="6">
            <el-form-item label="问卷类型">
              <el-select placeholder="请选择问卷类型" v-model="questionnaireInfoManage.params.type">
                <el-option
                  v-for="item in QuestionnaireType.list()"
                  :label="item.desc"
                  :value="item.code"
                  :key="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="8" :xl="8" class="f-fr">
            <el-form-item class="f-tr">
              <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
              <el-button @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table
        stripe
        v-if="questionnaireInfoManage.list.length > 0"
        :data="questionnaireInfoManage.list"
        v-loading="query.loading"
        max-height="500px"
        class="m-table f-mt15"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column prop="name" label="问卷名称" min-width="220"></el-table-column>
        <el-table-column label="问卷类型" min-width="100" align="center">
          <template slot-scope="scope">{{ QuestionnaireType.map.get(scope.row.type) }}</template>
        </el-table-column>
        <el-table-column label="提交问卷人数" min-width="100" align="center">
          <template slot-scope="scope">{{ scope.row.submitNum || 0 }}</template>
        </el-table-column>
        <el-table-column label="操作" min-width="100" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="goReport(scope.row)">查看统计报告</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page" v-if="questionnaireInfoManage.list.length > 0"></hb-pagination>
      <div class="m-no-date" v-if="questionnaireInfoManage.list.length === 0">
        <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
        <div class="date-bd">
          <p class="f-f15 f-c9">暂无数据~</p>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts">
  import QuestionnaireType from '@api/service/management/resource/question-naire/enums/QuestionnaireType'
  import { Query, UiPage } from '@hbfe/common'
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import QuestionnaireInfoManage from '@api/service/management/implement/QuestionnaireInfoManage'
  import QuestionnaireQueryParam from '@api/service/management/resource/question-naire/models/QuestionnaireQueryParam'
  import QuestionnaireImplementItem from '@api/service/management/implement/models/QuestionnaireImplementItem'

  @Component
  export default class extends Vue {
    @Prop({
      type: String,
      default: ''
    })
    schemeId: string
    farewellId = '' //期别id
    questionnaireInfoManage: QuestionnaireInfoManage //查询请求

    // params = this.questionnaireInfoManage.params //查询入参
    // list = this.questionnaireInfoManage.list //返回结果
    QuestionnaireType = new QuestionnaireType() //问卷类型枚举
    page: UiPage
    query: Query = new Query()
    constructor() {
      super()
      this.questionnaireInfoManage = new QuestionnaireInfoManage(this.schemeId) //查询请求

      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    /**
     * 查询
     */
    async doQueryPage() {
      try {
        this.query.loading = true
        await this.questionnaireInfoManage.queryListInScheme(this.page)
      } catch (e) {
        console.log(e)
      } finally {
        this.query.loading = false
      }
    }
    /**
     * 重置
     */
    resetQuery() {
      this.questionnaireInfoManage.params = new QuestionnaireQueryParam()
      this.page.currentChange(1)
    }
    created() {
      this.doQueryPage()
    }
    goReport(row: QuestionnaireImplementItem) {
      this.$router.push(`/training/scheme/holisticReport/${row.id}?schemeId=${this.schemeId}&&issueId=${row.periodId}`)
    }
  }
</script>
