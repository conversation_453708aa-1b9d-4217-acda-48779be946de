import { ExamRecordDTO } from '@api/gateway/PlatformExam'
import MockExaminationPaper from '@api/service/common/models/exam/mock-exam/MockExaminationPaper'
/**
 * 模拟卷信息
 * <AUTHOR>
 * @Date 2020/8/25/0025 20:31
 */
export class MockExaminationInfoDTO {
  // 方案id
  schemeId: string
  // 方式id
  learningId: string
  // 是否启用考试学习方式
  enabled: boolean
  /**
   * 模拟考试卷id
   */
  paperId = ''
  /**
   * 试卷信息
   */
  paper: MockExaminationPaper = new MockExaminationPaper()
  /**
   * 及格分数
   */
  passScore = 0
  /**
   * 考试时长(分钟)
   **/
  examTimeLength = 0
  /**
   * 是否开放题解析
   */
  openResolvedExam = false
  answerRecord: Array<ExamRecordDTO> = new Array<ExamRecordDTO>()

  build(schemeId: string, learningId: string) {
    this.schemeId = schemeId
    this.learningId = learningId
  }
}
