import MsAh<PERSON>xamquestion, {
  ChooseAnswerOptionRequest,
  CreateAskQuestionRequest,
  CreateMultipleQuestionRequest,
  // CreateQuestionRequest,
  CreateRadioQuestionRequest,
  CreateScaleQuestionRequest,
  UpdateAskQuestionRequest,
  UpdateMultipleQuestionRequest,
  UpdateQuestionRequest,
  UpdateRadioQuestionRequest,
  UpdateScaleQuestionRequest
} from '@api/platform-gateway/platform-jxjy-examquestion-v1'
import { QuestionTypeEnum } from '@api/service/common/enums/question-naire/QuestionType'
import AnswerQuestion from '@api/service/common/question-naire/AnswerQuestion'
import GaugeQuestion from '@api/service/common/question-naire/GaugeQuestion'
import OptionsQuestion from '@api/service/common/question-naire/OptionsQuestion'
import {
  MultipleQuestionResponse,
  RadioQuestionResponse,
  ScaleQuestionResponse,
  AskQuestionResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import QuestionSingleOption from '@api/service/common/question-naire/QuestionSingleOption'
import QuetionReport from '@api/service/common/question-naire/QuetionReport'
import ScaleStatistic from '@api/service/common/question-naire/models/ScaleStatistic'
import OptionStatistic from '@api/service/common/question-naire/models/OptionStatistic'
import { TeacherQuestionTypeEnum } from '@api/service/common/question-naire/enums/TeacherQuestionType'
import { TeacherEvaluationQuestionTypeEnum } from '@api/service/common/question-naire/enums/TeacherEvaluationQuestionType'
import MsExamQuestion, {
  CreateNoCheckQuestionRequest,
  CreateQuestionRequest,
  UpdateNoCheckQuestionRequest
} from '@api/ms-gateway/ms-examquestion-v1'
import { QuestionSourceTypeEnum } from './enums/QuestionSourceType'
import { ResponseStatus } from '@hbfe/common'

/**
 * 问卷题目
 */
export default class Question {
  /**
   * 试题id
   */
  id = ''
  /**
   * 题目类型
   */
  type: QuestionTypeEnum = null
  /**
   * 是否必答
   */
  isMustAnswered = true
  /**
   * 是否已作答
   */
  isAnswered = false
  /**
   * 是否继续 ui交互用
   */
  isContinue = false
  /**
   * 是否教师评价
   */
  isTeacherEvaluate = false
  /**
   * 线上/线下
   */
  onlineOrOffline: QuestionSourceTypeEnum = QuestionSourceTypeEnum.normal
  /**
   * 单选题
   */
  optionQuestion: OptionsQuestion = new OptionsQuestion()
  /**
   * 多选题
   */
  multipleOptionQuestion: OptionsQuestion = new OptionsQuestion()
  /**
   * 问答题
   */
  answerQuestion: AnswerQuestion = new AnswerQuestion()
  /**
   * 量表题
   */
  gaugeQuestion: GaugeQuestion = new GaugeQuestion()
  /**
   * 创建试题
   */
  async createQuestion() {
    let request

    switch (this.type) {
      case QuestionTypeEnum.single:
        request = new CreateRadioQuestionRequest()
        request.questionType = 1
        request.topic = this.optionQuestion.described
        request.answerOptions = this.optionQuestion.options.map((item, index) => {
          const temp = new ChooseAnswerOptionRequest()
          temp.id = (index + 1).toString()
          temp.content = item.content
          temp.enableFillContent = item.completion
          temp.mustFillContent = item.isRequire
          return temp
        })
        // 问卷没有正确答案 给-1
        request.correctAnswerId = '-1'
        break
      case QuestionTypeEnum.multiple:
        request = new CreateMultipleQuestionRequest()
        request.questionType = 2
        request.topic = this.multipleOptionQuestion.described
        request.answerOptions = this.multipleOptionQuestion.options.map((item, index) => {
          const temp = new ChooseAnswerOptionRequest()
          temp.id = index.toString()
          temp.content = item.content
          temp.enableFillContent = item.completion
          temp.mustFillContent = item.isRequire
          return temp
        })
        // 问卷没有正确答案 给-1
        request.correctAnswerIds = ['-1']
        break
      case QuestionTypeEnum.answer:
        request = new CreateAskQuestionRequest()
        request.questionType = 5
        request.topic = this.answerQuestion.described
        break
      case QuestionTypeEnum.gauge:
        request = new CreateScaleQuestionRequest()
        request.questionType = 7
        request.topic = this.gaugeQuestion.described
        request.scaleType = this.gaugeQuestion.type
        request.startDegree = this.gaugeQuestion.minDeepTip
        request.endDegree = this.gaugeQuestion.maxDeepTip
        request.series = this.gaugeQuestion.levelNum
        request.initialValue = this.gaugeQuestion.startLevel
        break
      default:
        break
    }
    request.buildIn = true
    request.enabled = false
    request.libraryId = '-1'
    // const res = await MsAhExamquestion.createQuestionnaireQuestion(request)
    const req = new CreateNoCheckQuestionRequest()
    if (this.isTeacherEvaluate) {
      req.tagCode = [TeacherEvaluationQuestionTypeEnum.TEACHER_EVALUATION_QUESTION]
    }
    req.createQuestionRequest = request
    const res = await MsExamQuestion.createTagQuestion(req)
    if (res.status.isSuccess()) {
      this.id = res.data
    }
    return res.status
  }
  /**
   * 修改试题
   */
  async updateQuestion(isInCopy = false) {
    let request
    switch (this.type) {
      case QuestionTypeEnum.single:
        request = new UpdateRadioQuestionRequest()
        request.questionType = 1
        request.topic = this.optionQuestion.described
        request.answerOptions = this.optionQuestion.options.map((item, index) => {
          const temp = new ChooseAnswerOptionRequest()
          temp.id = (index + 1).toString()
          temp.content = item.content
          temp.enableFillContent = item.completion
          temp.mustFillContent = item.isRequire
          return temp
        })
        // 问卷没有正确答案 给-1
        request.correctAnswerId = '-1'
        break
      case QuestionTypeEnum.multiple:
        request = new UpdateMultipleQuestionRequest()
        request.questionType = 2
        request.topic = this.multipleOptionQuestion.described
        request.answerOptions = this.multipleOptionQuestion.options.map((item, index) => {
          const temp = new ChooseAnswerOptionRequest()
          temp.id = index.toString()
          temp.content = item.content
          temp.enableFillContent = item.completion
          temp.mustFillContent = item.isRequire
          return temp
        })
        // 问卷没有正确答案 给-1
        request.correctAnswerIds = ['-1']
        break
      case QuestionTypeEnum.answer:
        request = new UpdateAskQuestionRequest()
        request.questionType = 5
        request.topic = this.answerQuestion.described
        break
      case QuestionTypeEnum.gauge:
        request = new UpdateScaleQuestionRequest()
        request.questionType = 7
        request.topic = this.gaugeQuestion.described
        request.scaleType = this.gaugeQuestion.type
        request.startDegree = this.gaugeQuestion.minDeepTip
        request.endDegree = this.gaugeQuestion.maxDeepTip
        request.series = this.gaugeQuestion.levelNum
        request.initialValue = this.gaugeQuestion.startLevel
        break
      default:
        break
    }
    request.id = this.id
    request.libraryId = '-1'
    const req = new UpdateNoCheckQuestionRequest()
    req.updateQuestionRequest = request
    if (this.isTeacherEvaluate) {
      req.tagCode = [TeacherEvaluationQuestionTypeEnum.TEACHER_EVALUATION_QUESTION]
    }
    if (!isInCopy) {
      const res = await MsExamQuestion.updateTagQuestion(req)
      if (res.status.isSuccess()) {
        this.id = res.data
      }
      return res.status
    } else {
      // 复制修改 不调口 本地处理
      return new ResponseStatus(200, '修改成功')
    }
  }
  /**
   * 删除试题
   */
  async deleteQuestion() {
    const res = await MsExamQuestion.removeTagQuestion(this.id)
    return res
  }

  /**
   * 用于复制问卷试题
   * 临时调整
   */
  to() {
    let request
    switch (this.type) {
      case QuestionTypeEnum.single:
        request = new CreateRadioQuestionRequest()
        request.questionType = 1
        request.topic = this.optionQuestion.described
        request.answerOptions = this.optionQuestion.options.map((item, index) => {
          const temp = new ChooseAnswerOptionRequest()
          temp.id = (index + 1).toString()
          temp.content = item.content
          temp.enableFillContent = item.completion
          temp.mustFillContent = item.isRequire
          return temp
        })
        request.correctAnswerId = '-1'

        // 问卷没有正确答案 给-1
        break
      case QuestionTypeEnum.multiple:
        request = new CreateMultipleQuestionRequest()
        request.questionType = 2
        request.topic = this.multipleOptionQuestion.described
        request.answerOptions = this.multipleOptionQuestion.options.map((item, index) => {
          const temp = new ChooseAnswerOptionRequest()
          temp.id = index.toString()
          temp.content = item.content
          temp.enableFillContent = item.completion
          temp.mustFillContent = item.isRequire
          return temp
        })
        // 问卷没有正确答案 给-1
        request.correctAnswerIds = ['-1']
        break
      case QuestionTypeEnum.answer:
        request = new CreateAskQuestionRequest()
        request.questionType = 5
        request.topic = this.answerQuestion.described
        break
      case QuestionTypeEnum.gauge:
        request = new CreateScaleQuestionRequest()
        request.questionType = 7
        request.topic = this.gaugeQuestion.described
        request.scaleType = this.gaugeQuestion.type
        request.startDegree = this.gaugeQuestion.minDeepTip
        request.endDegree = this.gaugeQuestion.maxDeepTip
        request.series = this.gaugeQuestion.levelNum
        request.initialValue = this.gaugeQuestion.startLevel
        break
      default:
        break
    }
    request.buildIn = true
    request.enabled = false
    request.libraryId = '-1'
    return request
  }
  /**
   * 问卷参转
   */
  static fromQuestion(
    item: RadioQuestionResponse | MultipleQuestionResponse | AskQuestionResponse | ScaleQuestionResponse
  ) {
    const temp = new Question()
    temp.id = item.questionId
    const ite = item as ScaleQuestionResponse
    const ite2 = item as AskQuestionResponse
    switch (item.questionType) {
      case 1:
        temp.type = QuestionTypeEnum.single
        temp.optionQuestion.described = item.topic
        temp.optionQuestion.options = (item as RadioQuestionResponse).radioAnswerOptions?.map((item) => {
          const temp = new QuestionSingleOption()
          temp.id = item.id
          temp.content = item.content
          temp.completion = item.enableFillContent
          temp.isRequire = item.mustFillContent
          return temp
        })
        break
      case 2:
        temp.type = QuestionTypeEnum.multiple
        temp.multipleOptionQuestion.described = item.topic
        temp.multipleOptionQuestion.options = (item as MultipleQuestionResponse)?.multipleAnswerOptions.map((item) => {
          const temp = new QuestionSingleOption()
          temp.id = item.id
          temp.content = item.content
          temp.completion = item.enableFillContent
          temp.isRequire = item.mustFillContent
          return temp
        })
        break
      case 5:
        temp.type = QuestionTypeEnum.answer
        temp.answerQuestion.described = item.topic
        break
      case 7:
        temp.type = QuestionTypeEnum.gauge
        temp.gaugeQuestion.described = item.topic
        temp.gaugeQuestion.levelNum = ite.series
        temp.gaugeQuestion.minDeepTip = ite.startDegree
        temp.gaugeQuestion.maxDeepTip = ite.endDegree
        temp.gaugeQuestion.type = ite.scaleType
        temp.gaugeQuestion.startLevel = ite.initialValue
        break
      default:
        break
    }
    if (
      item?.relateTagCodes &&
      item.relateTagCodes.includes(TeacherEvaluationQuestionTypeEnum.TEACHER_EVALUATION_QUESTION)
    ) {
      temp.isTeacherEvaluate = true
    }
    return temp
  }
  /**
   * 问卷报告参转
   */
  static fromQuestionReport(
    item: RadioQuestionResponse | MultipleQuestionResponse | ScaleQuestionResponse | AskQuestionResponse
  ) {
    const temp = new QuetionReport()
    temp.id = item.questionId
    temp.described = item.topic

    const ite = item as ScaleQuestionResponse
    const scaleList: ScaleStatistic[] = []
    switch (item.questionType) {
      case 1:
        temp.type = QuestionTypeEnum.single
        temp.optionsStatisc = (item as RadioQuestionResponse).radioAnswerOptions.map((item) => {
          const tmp = new OptionStatistic()
          tmp.id = item.id
          tmp.content = item.content
          return tmp
        })
        break
      case 2:
        temp.type = QuestionTypeEnum.multiple
        temp.optionsStatisc = (item as MultipleQuestionResponse).multipleAnswerOptions.map((item) => {
          const tmp = new OptionStatistic()
          tmp.id = item.id
          tmp.content = item.content
          return tmp
        })
        break
      case 5:
        temp.type = QuestionTypeEnum.answer
        break
      case 7:
        temp.type = QuestionTypeEnum.gauge
        for (let i = ite.initialValue; i < ite.initialValue + ite.series; i++) {
          scaleList.push({
            levelNum: i,
            type: ite.scaleType,
            maxDeepTip: ite.endDegree,
            minDeepTip: ite.startDegree
          } as ScaleStatistic)
        }
        temp.scaleStatisc = scaleList
        break
      default:
        break
    }
    return temp
  }
}
