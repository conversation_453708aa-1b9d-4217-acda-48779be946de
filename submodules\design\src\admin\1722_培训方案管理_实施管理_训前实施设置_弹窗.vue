<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">训前实施设置</span>
        </div>
        <!--期别报到设置-->
        <el-button @click="dialog01 = true" type="primary" class="f-mr20 f-mb20">期别报到设置</el-button>
        <el-drawer
          title="期别报到设置"
          :visible.sync="dialog01"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="150px" class="m-form">
              <el-form-item label="报到形式：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="打卡报到"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="打卡有效范围：" required>
                在培训班办班地点定位打卡，允许微调地点，微调半径：<el-input
                  class="input-num"
                  placeholder="请输入"
                ></el-input
                >米，建议设为500米
              </el-form-item>
              <el-form-item label="报到打卡时间：" required>
                培训班期别报到时间开始至培训班培训结束时间期间都允许打卡报到
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--设置考勤规则模板-->
        <el-button @click="dialog02 = true" type="primary" class="f-mr20 f-mb20">设置考勤规则模板</el-button>
        <el-drawer
          title="设置考勤规则模板"
          :visible.sync="dialog02"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
              统一设置面授期别的考勤规则模板，设置完成后期别可以选择考勤规则模板设置考勤规则。
            </el-alert>
            <el-form ref="form" :model="form" label-width="150px" class="m-form">
              <el-form-item label="是否开启签到：" required>
                <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
              </el-form-item>
              <el-form-item label="签到频率：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="每半天签到一次"></el-radio>
                  <el-radio label="每节课签到一次"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="签到地点：" required>
                在培训班办班地点定位签到，允许微调地点，微调半径：<el-input
                  class="input-num"
                  placeholder="请输入"
                ></el-input>
                米，建议设为500米
              </el-form-item>
              <el-form-item label="签到时间：" required>
                第一节课开始授课前 <el-input class="input-num" placeholder="请输入"></el-input> 分钟和开始授课后
                <el-input class="input-num" placeholder="请输入"></el-input> 分钟之间，需签到1次。
              </el-form-item>
              <el-divider></el-divider>
              <el-form-item label="是否开启签退：" required>
                <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
              </el-form-item>
              <el-form-item label="签退频率：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="每半天签退一次"></el-radio>
                  <el-radio label="每节课签退一次"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="签退地点：" required>
                在培训班办班地点定位签退，允许微调地点，微调半径：<el-input
                  class="input-num"
                  placeholder="请输入"
                ></el-input>
                米，建议设为500米
              </el-form-item>
              <el-form-item label="签退时间：" required>
                最后一节课结束授课前 <el-input class="input-num" placeholder="请输入"></el-input> 分钟和结束授课后
                <el-input class="input-num" placeholder="请输入"></el-input> 分钟之间，需签退1次。
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--期别考勤设置-->
        <el-button @click="dialog03 = true" type="primary" class="f-mr20 f-mb20">期别考勤设置</el-button>
        <el-drawer
          title="期别考勤设置"
          :visible.sync="dialog03"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
              <p>1、设置期别的考勤规则，设置后学员按规进行面授课程的考勤打卡</p>
              <p>2、当期别有学员报名后，则无法修改签到和签退的开关以及签到频率</p>
              <p>3、修改签到/签退地点和签到/签退时间后，学员按照新设置的规则进行考勤打卡</p>
            </el-alert>
            <div class="f-tr">
              <el-tooltip effect="dark" placement="top" class="f-mr10">
                <span class="el-icon-info f-co"></span>
                <div slot="content">根据模板设置考勤规则，若未配置模板，则按钮置灰</div>
              </el-tooltip>
              <!--灰显添加属性disabled-->
              <el-button type="primary">按考勤模板设置</el-button>
            </div>
            <el-form ref="form" :model="form" label-width="150px" class="m-form">
              <el-form-item label="是否开启签到：" required>
                <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
              </el-form-item>
              <el-form-item label="签到频率：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="每半天签到一次"></el-radio>
                  <el-radio label="每节课签到一次"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="签到地点：" required>
                在培训班办班地点定位签到，允许微调地点，微调半径：<el-input
                  class="input-num"
                  placeholder="请输入"
                ></el-input>
                米，建议设为500米
              </el-form-item>
              <el-form-item label="签到时间：" required>
                第一节课开始授课前 <el-input class="input-num" placeholder="请输入"></el-input> 分钟和开始授课后
                <el-input class="input-num" placeholder="请输入"></el-input> 分钟之间，需签到1次。
              </el-form-item>
              <el-divider></el-divider>
              <el-form-item label="是否开启签退：" required>
                <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
              </el-form-item>
              <el-form-item label="签退频率：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="每半天签退一次"></el-radio>
                  <el-radio label="每节课签退一次"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="签退地点：" required>
                在培训班办班地点定位签退，允许微调地点，微调半径：<el-input
                  class="input-num"
                  placeholder="请输入"
                ></el-input>
                米，建议设为500米
              </el-form-item>
              <el-form-item label="签退时间：" required>
                第一节课开始授课前 <el-input class="input-num" placeholder="请输入"></el-input> 分钟和开始授课后
                <el-input class="input-num" placeholder="请输入"></el-input> 分钟之间，需签退1次。
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--期别学习资料设置-->
        <el-button @click="dialog04 = true" type="primary" class="f-mr20 f-mb20">期别学习资料设置</el-button>
        <el-drawer
          title="期别学习资料设置"
          :visible.sync="dialog04"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="150px" class="m-form">
              <el-form-item label="添加学习资料：">
                <el-upload drag action="https://jsonplaceholder.typicode.com/posts/" multiple>
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                  <div class="el-upload__tip" slot="tip">
                    附件资料格式支持：doc、docx、xls、xlsx、ppt、pptx、pdf、txt等格式
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item label="已添加学习资料：">
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column label="附件名称" min-width="100">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-input placeholder="请输入" class="u-w180"></el-input>
                        <span class="el-icon-check f-cb f-csp f-f18 f-ml10"></span>
                        <span class="el-icon-close f-ci f-csp f-f18 f-ml10"></span>
                      </div>
                      <div v-else>001<span class="el-icon-edit f-cb f-csp f-ml10"></span></div>
                    </template>
                  </el-table-column>
                  <el-table-column label="格式" min-width="120">
                    <template>AAA</template>
                  </el-table-column>
                  <el-table-column label="操作" width="100" align="center" fixed="right">
                    <template>
                      <el-button type="text">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--选择面授期别-->
        <el-button @click="dialog05 = true" type="primary" class="f-mr20 f-mb20">选择面授期别</el-button>
        <el-drawer
          title="选择面授期别"
          :visible.sync="dialog05"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--条件查询-->
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="10">
                  <el-form-item label="期别名称">
                    <el-input v-model="input" clearable placeholder="请输入期别名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="期别名称" min-width="140">
                <template>这是期别完整名称这是期别完整名称</template>
              </el-table-column>
              <el-table-column label="归属培训方案" min-width="140" align="center">
                <template>这是培训方案完整名称</template>
              </el-table-column>
              <el-table-column label="培训属性" min-width="120">
                <template>
                  <p>行业：xxx</p>
                  <p>年度：xxx</p>
                  <p>地区：xxx</p>
                  <p>培训类别：xxx</p>
                  <p>科目类型：xxx</p>
                  <p>培训专业：xxx</p>
                  <p>主项/增项：xxx</p>
                </template>
              </el-table-column>
              <el-table-column label="培训起止时间" width="200" align="center">
                <template>
                  <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
                </template>
              </el-table-column>
              <el-table-column label="报名起止时间" width="200" align="center">
                <template>
                  <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-radio v-model="radio" label="1">选择</el-radio>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--选择模板/修改模板-->
        <el-button @click="dialog06 = true" type="primary" class="f-mr20 f-mb20">选择模板/修改模板</el-button>
        <el-drawer
          title="选择模板"
          :visible.sync="dialog06"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="模板名称" min-width="140">
                <template>这是期别完整名称这是期别完整名称</template>
              </el-table-column>
              <el-table-column label="模板说明" min-width="140" align="center">
                <template>这是培训方案完整名称</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text">取消选择</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text">选择</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>返回</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        num: 100,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        activeNames: ['1'],
        props: { multiple: true },
        radio: 3,
        checked: false,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog01: false,
        dialog02: false,
        dialog03: false,
        dialog04: false,
        dialog05: false,
        dialog06: false,
        dialog07: false,
        dialog08: false,
        dialog09: false,
        dialog10: false,
        dialog001: false,
        dialog002: false,
        dialog003: false,
        dialog004: false,
        dialog005: false,
        dialog0001: false,
        dialog0002: false,
        dialog0003: false,
        dialog0004: false,
        dialog0005: false,
        dialog00001: false,
        dialog00002: false,
        dialog00003: false,
        dialog00004: false,
        dialog00005: false,
        dialog000001: false,
        dialog000002: false,
        dialog000003: false,
        dialog000004: false,
        dialog000005: false,
        dialog0000001: false,
        dialog0000002: false,
        dialog0000003: false,
        dialog0000004: false,
        dialog0000005: false,
        dialog00000001: false,
        dialog00000002: false,
        dialog00000003: false,
        dialog00000004: false,
        dialog00000005: false,

        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      open3() {
        this.$message({
          message: '本次修改内容如涉及到考核重算，重算任务于程序后台自动执行，即将自动为您跳转到方案管理列表页。',
          type: 'warning',
          duration: 5000,
          customClass: 'm-message'
        })
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
