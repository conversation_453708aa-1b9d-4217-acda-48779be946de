<template>
  <el-main>
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
      <p>配置相关说明：</p>
      <p>
        1. 当前系统提供个人缴费、集体报名及教务导入三个报名方式，请针对实际渠道的运用情况配置对应的收款账户及发票信息；
      </p>
      <p>2. 个人缴费渠道、单位缴费渠道可允许添加多个收款账号，导入开通渠道默认收款账号帐号无法修改。</p>
    </el-alert>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="学员缴费" name="first">
        <div class="f-p15">
          详见 0601_报名方式配置_学员缴费.vue
        </div>
      </el-tab-pane>
      <el-tab-pane label="集体报名" name="second">
        <div class="f-p15">
          详见 0602_报名方式配置_集体报名.vue
        </div>
      </el-tab-pane>
      <el-tab-pane label="导入开通" name="third">
        <div class="f-p15">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header">
              <span class="tit-txt">销售渠道配置</span>
            </div>
            <div class="f-p20 f-tc">
              <div class="m-no-date">
                <img class="img" src="./assets/images/no-data-news.png" alt="" />
                <p class="txt">导入开通为系统默认帐号无需配置~</p>
              </div>
            </div>
          </el-card>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="f-flex">
              <span class="tit-txt f-flex-sub">渠道发票配置</span>
            </div>
            <div class="f-p30">
              <el-row type="flex" justify="center" class="width-limit">
                <el-col :md="20" :lg="16" :xl="13">
                  <el-form ref="form" :model="form" label-width="auto" class="m-form">
                    <el-form-item label="是否提供发票：" required>
                      <el-radio-group v-model="form.resource">
                        <el-radio label="不提供发票" checked="true"></el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'third',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
