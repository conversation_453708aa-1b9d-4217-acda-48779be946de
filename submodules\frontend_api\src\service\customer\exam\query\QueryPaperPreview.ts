import { ResponseStatus, Response } from '@hbfe/common'
import msExam from '@api/ms-gateway/ms-examextraction-v1'

import MsExamQueryFrontGatewayCourseLearningForeStage, {
  AnswerPaperResponse as AnswerPaperResponseA
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryForeStage'
import {
  AnswerPaperResponse,
  FillQuestionResponse,
  MultipleQuestionResponse,
  OpinionQuestionResponse,
  QuestionGroupViewResponse,
  RadioQuestionResponse
} from '@api/service/customer/exam/mutation/vo/AnswerPaper'
import {
  OpinionQuestionResponse as OpinionQuestionResponseA,
  MultipleQuestionResponse as MultipleQuestionResponseA,
  RadioQuestionResponse as RadioQuestionResponseA
} from '@api/ms-gateway/ms-examextraction-v1'
import { cloneDeep } from 'lodash'
import { QuestionKeyValue, QuestionTypeEnum } from '@api/service/customer/exam/utils/Constant'
import { TimeOut } from '@api/service/common/utils/TimeOut'

/**
 * 用户域获取培训班商品列表
 */
class QueryPaperPreview {
  // region properties
  /**
   * 卷子id
   */
  paperId = ''
  /**
   * 试卷
   */
  answerPaper = new AnswerPaperResponse()
  // endregion
  // region methods
  async judgeFunc(res: Response<AnswerPaperResponseA>) {
    return res.status.isSuccess() && res.data.markStatus == 1
  }
  async getRecord(paperId: string) {
    return msExam.previewAnswerPaper(paperId)
  }
  /**
   * 获取预览试卷
   */
  async queryPreview(): Promise<ResponseStatus> {
    // const filter = new CommoditySkuRequest()
    // filter.skuPropertyRequest = this.filterSkuVo.convertToDto()
    const res = await msExam.previewAnswerPaper(this.paperId)
    // const res = await TimeOut.timeoutWithCount<Response<AnswerPaperResponseA>>(this['getRecord'], this['judgeFunc'], [
    //   this.paperId
    // ])
    //&& res.data.markStatus == 1
    if (res.status.isSuccess()) {
      Object.assign(this.answerPaper, res.data)
      res.data.questions.forEach((tmpItem, index) => {
        this.answerPaper.questions[index].id = tmpItem.id
        // this.answerPaper.questions[index].answered =
        //   tmpItem[QuestionKeyValue.getQuestionAnswerKey(tmpItem.questionType)] !== null
        const item = this.answerPaper.questions[index]
        if (item.questionType == QuestionTypeEnum.QuestionTypeEnumRadio) {
          ;(item as RadioQuestionResponse).trueRadioAnswer = (tmpItem as RadioQuestionResponseA).radioQuestionCorrectAnswerId
        }
        // 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
        if (item.questionType == QuestionTypeEnum.QuestionTypeEnumMul) {
          ;(item as MultipleQuestionResponse).trueMultipleAnswer = (tmpItem as MultipleQuestionResponseA).multipleQuestionCorrectAnswerIds
        }
        // if (item.questionType == 3) {
        //   ;(item as FillQuestionResponse).fillAnswer = (answerM as FillQuestionResponseA).fillCorrectAnswer
        // }
        if (item.questionType == QuestionTypeEnum.QuestionTypeEnumOpi) {
          const trueItem = item as OpinionQuestionResponse
          trueItem.answerOptions = [
            {
              id: 'true',
              content: trueItem.correctAnswerText
            },
            {
              id: 'false',
              content: trueItem.incorrectAnswerText
            }
          ]
          trueItem.opinionCorrectAnswer = (tmpItem as OpinionQuestionResponseA).opinionQuestionCorrectAnswer
          trueItem.trueOpinionAnswer = (tmpItem as OpinionQuestionResponseA).opinionQuestionCorrectAnswer
        }
      })
      // const questionIdList = res.data.questions.map(tmpItem => {
      //   return tmpItem.id
      // })
      // const questionDetailRes = await MsExamQueryFrontGatewayCourseLearningForeStage.pageQuestionInMySelf({
      //   page: {
      //     pageNo: 1,
      //     pageSize: questionIdList.length
      //   },
      //   request: {
      //     questionIdList: questionIdList
      //   }
      // })
      // if (questionDetailRes.status.isSuccess()) {
      //   for (let i = 0; i < this.answerPaper.questions.length; i++) {
      //     const item = this.answerPaper.questions[i]
      //     const answerM = questionDetailRes.data.currentPageData.find(answerTitem => answerTitem.questionId == item.id)
      //     item.dissects = answerM.dissects
      //     item.topic = answerM.topic
      //     if (item.questionType == QuestionTypeEnum.QuestionTypeEnumRadio) {
      //       ;(item as RadioQuestionResponse).trueRadioAnswer = (answerM as RadioQuestionResponseA).correctAnswerId
      //       ;(item as RadioQuestionResponse).answerOptions = (answerM as RadioQuestionResponseA).radioAnswerOptions
      //     }
      //     // 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
      //     if (item.questionType == QuestionTypeEnum.QuestionTypeEnumMul) {
      //       ;(item as MultipleQuestionResponse).trueMultipleAnswer = (answerM as MultipleQuestionResponseA).correctAnswerIds
      //       ;(item as MultipleQuestionResponse).answerOptions = (answerM as MultipleQuestionResponseA).multipleAnswerOptions
      //     }
      //     // if (item.questionType == 3) {
      //     //   ;(item as FillQuestionResponse).fillAnswer = (answerM as FillQuestionResponseA).fillCorrectAnswer
      //     // }
      //     if (item.questionType == QuestionTypeEnum.QuestionTypeEnumOpi) {
      //       const trueItem = item as OpinionQuestionResponse
      //       trueItem.answerOptions = [
      //         {
      //           id: 'true',
      //           content: trueItem.correctAnswerText
      //         },
      //         {
      //           id: 'false',
      //           content: trueItem.incorrectAnswerText
      //         }
      //       ]
      //
      //       trueItem.trueOpinionAnswer = (answerM as OpinionQuestionResponseA).opinionCorrectAnswer
      //     }
      //   }
      // }
      // this.calPaper()
      this.answerPaper = cloneDeep(this.answerPaper)
    }

    //pageCommoditySkuCustomerPurchaseInServicer
    return res.status
  }
  calPaper() {
    if (!this.answerPaper.groups) {
      this.answerPaper.groups = []
    }
    this.answerPaper.questions.forEach(item => {
      const questionGroup = this.answerPaper.groups.find(tmpItem => tmpItem.questionType == item.questionType)
      if (questionGroup) {
        questionGroup.questionCount++
      } else {
        const insertGroup = new QuestionGroupViewResponse()
        insertGroup.questionCount = 1
        insertGroup.questionType = item.questionType
        insertGroup.groupName = QuestionKeyValue.getQuestionName(insertGroup.questionType)
        insertGroup.sequence = this.answerPaper.groups.length
        this.answerPaper.groups.push(insertGroup)
      }
    })
  }
  // endregion
}
export default QueryPaperPreview
