import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum CheckTypeEnum {
  auto = 'AUTO_AUDIT',
  artificial = 'MANUAL_AUDIT'
}

class CheckType extends AbstractEnum<CheckTypeEnum> {
  constructor(status?: CheckTypeEnum) {
    super()
    this.current = status
    this.map.set(CheckTypeEnum.auto, '提交自动通过')
    this.map.set(CheckTypeEnum.artificial, '人工审核')
  }
}

export default CheckType
