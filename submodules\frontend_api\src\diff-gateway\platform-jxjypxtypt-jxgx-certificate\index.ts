import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-jxgx-certificate'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum StudentSchemeLearningSortField {
  REGISTER_TIME = 'REGISTER_TIME',
  SCHEME_YEAR = 'SCHEME_YEAR'
}

// 类

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

export class StudentSchemeLearningRequest {
  studentNoList?: Array<string>
  student?: UserRequest
  learningRegister?: LearningRegisterRequest
  scheme?: SchemeRequest
  studentLearning?: StudentLearningRequest
  dataAnalysis?: DataAnalysisRequest
  connectManageSystem?: ConnectManageSystemRequest
  extendedInfo?: ExtendedInfoRequest
  openPrintTemplate?: boolean
  saleChannels?: Array<number>
  trainingChannelName?: string
  trainingChannelId?: string
  notDistributionPortal?: boolean
  trainingType?: string
  issueId?: string
}

export class StudentSchemeLearningSortRequest {
  field?: StudentSchemeLearningSortField
  policy?: SortPolicy
}

export class ConnectManageSystemRequest {
  syncStatus?: number
}

export class DataAnalysisRequest {
  trainingResultPeriod?: DoubleScopeRequest
  requirePeriod?: DoubleScopeRequest
  acquiredPeriod?: DoubleScopeRequest
}

export class ExtendedInfoRequest {
  whetherToPrint?: boolean
  applyCompanyCode?: string
  policyTrainingSchemeId?: string
  policyTrainingSchemeName?: string
}

export class LearningRegisterRequest {
  registerType?: number
  sourceType?: string
  sourceId?: string
  status?: Array<number>
  registerTime?: DateScopeRequest
  saleChannels?: Array<number>
  orderNoList?: Array<string>
  subOrderNoList?: Array<string>
  batchOrderNoList?: Array<string>
  distributorId?: string
  portalId?: string
}

export class RegionRequest {
  province?: string
  city?: string
  county?: string
}

export class StudentLearningRequest {
  trainingResultList?: Array<number>
  trainingResultTime?: DateScopeRequest
  notLearningTypeList?: Array<number>
  courseScheduleStatus?: number
  examAssessResultList?: Array<number>
}

export class RegionSkuPropertyRequest {
  province?: string
  city?: string
  county?: string
}

export class RegionSkuPropertySearchRequest {
  region?: Array<RegionSkuPropertyRequest>
  regionSearchType?: number
}

export class SchemeRequest {
  schemeId?: string
  schemeIdList?: Array<string>
  schemeType?: string
  schemeName?: string
  skuProperty?: SchemeSkuPropertyRequest
}

export class SchemeSkuPropertyRequest {
  year?: Array<string>
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  industry?: Array<string>
  subjectType?: Array<string>
  trainingCategory?: Array<string>
  trainingProfessional?: Array<string>
  technicalGrade?: Array<string>
  positionCategory?: Array<string>
  trainingObject?: Array<string>
  jobLevel?: Array<string>
  jobCategory?: Array<string>
  subject?: Array<string>
  grade?: Array<string>
  learningPhase?: Array<string>
  discipline?: Array<string>
  qualificationCategory?: Array<string>
  trainingWay?: Array<string>
  trainingInstitution?: Array<string>
  mainAdditionalItem?: Array<string>
}

export class UserPropertyRequest {
  regionList?: Array<RegionRequest>
  companyName?: string
  payOrderRegionList?: Array<RegionRequest>
}

export class UserRequest {
  userIdList?: Array<string>
  accountIdList?: Array<string>
  userProperty?: UserPropertyRequest
}

export class Extend {
  key?: string
  value?: string
}

export class ImportStudentBatchPrintingRequest {
  name?: string
  idCard?: string
  schemeName?: string
  importStatus: number
}

export class JXJYJXCertificatePrintRequest {
  templateId?: string
  fileType: number
  printType: number
  merge?: string
  printSource: number
  printPort: number
}

export class CertificateBatchPrintRequest {
  /**
   * 打印请求
   */
  jxjyjxCertificatePrintRequest?: JXJYJXCertificatePrintRequest
  /**
   * 查询条件
   */
  studentSchemeLearningRequest?: StudentSchemeLearningRequest
  /**
   * 查询排序条件
   */
  sort?: Array<StudentSchemeLearningSortRequest>
  /**
   * 打印类型 1-导入 2-正常
   */
  importStudentPrint: number
  importStudentBatchPrintingRequest?: ImportStudentBatchPrintingRequest
}

/**
 * 江西单个打印接口请求
<AUTHOR>
@date 2024/8/29 8:43
 */
export class SinglePrintCertificateRequest {
  /**
   * 参训资格id
   */
  qualificationId?: string
  /**
   * 学号
   */
  studentNo?: string
  /**
   * 文件类型 1-PDF   2-IMAGE
@see FileTypes
   */
  fileType: number
  /**
   * 扩展数据
key 对应courseId
   */
  data?: Array<Extend>
  /**
   * 方案id
   */
  schemeId?: string
}

/**
 * 江西单个打印返回值
<AUTHOR>
@date 2024/8/29 9:34
 */
export class SinglePrintCertificateReponse {
  /**
   * code
   */
  code: string
  /**
   * message
   */
  message: string
  /**
   * data
   */
  data: string
  /**
   * 是否需要强制完成调查问卷
   */
  needForceQuestionnaire: boolean
  /**
   * 需要强制完成的调查问卷信息
   */
  unaccomplishedQuestionnaire: Array<UnaccomplishedQuestionnaire>
}

/**
 * 未完成问卷信息
<AUTHOR>
@date 2025/4/15 10:15
 */
export class UnaccomplishedQuestionnaire {
  /**
   * 未完成问卷id
   */
  unaccomplishedQuestionnaireId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 允许开始时间
   */
  allowStartTime: string
  /**
   * 允许结束时间
   */
  allowEndTime: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量打印接口(包含校验完善信息)
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchPrintCertificate(
    request: CertificateBatchPrintRequest,
    mutate: DocumentNode = GraphqlImporter.batchPrintCertificate,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 单个打印接口(包含校验完善信息)
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async singlePrintCertificate(
    request: SinglePrintCertificateRequest,
    mutate: DocumentNode = GraphqlImporter.singlePrintCertificate,
    operation?: string
  ): Promise<Response<SinglePrintCertificateReponse>> {
    return commonRequestApi<SinglePrintCertificateReponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员端打印证书(需要判断是否强制调查问卷)
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async studentPrintCertificate(
    request: SinglePrintCertificateRequest,
    mutate: DocumentNode = GraphqlImporter.studentPrintCertificate,
    operation?: string
  ): Promise<Response<SinglePrintCertificateReponse>> {
    return commonRequestApi<SinglePrintCertificateReponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
