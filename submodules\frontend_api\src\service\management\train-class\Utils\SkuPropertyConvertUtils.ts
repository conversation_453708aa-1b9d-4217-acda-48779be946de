import { CommoditySkuPropertyResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import SkuPropertyVo from '@api/service/management/train-class/query/vo/SkuPropertyVo'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import BasicDataQueryBackstage, {
  BusinessRegionResponse,
  RegionResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import SkuVo from '@api/service/management/train-class/query/vo/SkuVo'
import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
import QueryBasicdataDictionaryFactory from '@api/service/common/basic-data-dictionary/QueryBasicdataDictionaryFactory'
import { RegionVo } from '@api/service/management/train-class/query/vo/RegionVo'
import SubjectTypeVo from '@api/service/common/basic-data-dictionary/query/vo/SubjectTypeVo'
import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
import { SchemeSkuPropertyResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { SubjectTypeRequest } from '@api/service/common/basic-data-dictionary/query/QuerySubjectType'
import { TrainingCategoryRequest } from '@api/service/common/basic-data-dictionary/query/QueryTrainingCategory'
import { TrainingProfessionalRequest } from '@api/service/common/basic-data-dictionary/query/QueryTrainingMajor'
import QueryPropertyDetail, {
  BatchQueryPropertyRequest
} from '@api/service/common/basic-data-dictionary/query/QueryPropertyDetail'
import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import QueryGrade from '@api/service/common/basic-data-dictionary/query/QueryGrade'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
import SkuPropertyConvertUtilsV2 from '@api/service/management/train-class/Utils/SkuPropertyConvertUtilsV2'
import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * 复杂sku属性返回值
 */
export type ComplexSkuPropertyResponse = SchemeSkuPropertyResponse & CommoditySkuPropertyResponse

/**
 * 方案sku信息
 */
export class SchemeSkuInfo {
  /**
   * 培训方案id
   */
  id = ''
  /**
   * sku属性
   */
  sku: ComplexSkuPropertyResponse = null
  /**
   * sku属性名称
   */
  skuName: SkuPropertyResponseVo = new SkuPropertyResponseVo()

  constructor(id: string, sku: ComplexSkuPropertyResponse) {
    this.id = id
    this.sku = sku
  }
}

/**
 * SkuPropertyResponse转换SkuPropertyVo
 */
export class SkuPropertyConvertUtils {
  /**
   * 【江苏工勤】后端方案sku转为状态层sku
   * @param resp 返回值
   */
  static convertSkuValueNameProperty(resp: CommoditySkuPropertyResponse): SkuPropertyResponseVo {
    const result = new SkuPropertyResponseVo()
    // 年度
    result.year = new SkuVo()
    result.year.skuPropertyValueId = resp.year?.skuPropertyValueId
    result.year.skuPropertyName = resp.year?.skuPropertyValueName
    // 地区
    result.region = new SkuVo()
    const regionCode = [] as string[]
    const regionName = [] as string[]
    const province = resp.province
    const city = resp.city
    const county = resp.county
    if (province?.skuPropertyValueId) {
      regionCode.push(province?.skuPropertyValueId)
      regionName.push(province?.skuPropertyValueName)
    }
    if (city?.skuPropertyValueId) {
      regionCode.push(city?.skuPropertyValueId)
      regionName.push(city?.skuPropertyValueName)
    }
    if (county?.skuPropertyValueId) {
      regionCode.push(county?.skuPropertyValueId)
      regionName.push(county?.skuPropertyValueName)
    }
    result.region.skuPropertyValueId = regionCode.join('/')
    result.region.skuPropertyName = regionName.join('/')
    // 行业
    result.industry = new SkuVo()
    result.industry.skuPropertyValueId = resp.industry?.skuPropertyValueId
    result.industry.skuPropertyName = resp.industry?.skuPropertyValueName
    // 培训等级
    result.technicalGrade = new SkuVo()
    result.technicalGrade.skuPropertyValueId = resp.technicalGrade?.skuPropertyValueId
    result.technicalGrade.skuPropertyName = resp.technicalGrade?.skuPropertyValueName
    // 科目类型
    result.subjectType = new SkuVo()
    result.subjectType.skuPropertyValueId = resp.subjectType?.skuPropertyValueId
    result.subjectType.skuPropertyName = resp.subjectType?.skuPropertyValueName
    // 培训类别
    result.trainingCategory = new SkuVo()
    result.trainingCategory.skuPropertyValueId = resp.trainingCategory?.skuPropertyValueId
    result.trainingCategory.skuPropertyName = resp.trainingCategory?.skuPropertyValueName
    // 培训专业
    result.trainingMajor = new SkuVo()
    result.trainingMajor.skuPropertyValueId = resp.trainingProfessional?.skuPropertyValueId
    result.trainingMajor.skuPropertyName = resp.trainingProfessional?.skuPropertyValueName
    // 培训对象
    result.trainingObject = new SkuVo()
    result.trainingObject.skuPropertyValueId = resp.trainingObject?.skuPropertyValueId
    result.trainingObject.skuPropertyName = resp.trainingObject?.skuPropertyValueName
    // 岗位类别
    result.positionCategory = new SkuVo()
    result.positionCategory.skuPropertyValueId = resp.positionCategory?.skuPropertyValueId
    result.positionCategory.skuPropertyName = resp.positionCategory?.skuPropertyValueName
    // 技术等级
    result.jobLevel = new SkuVo()
    result.jobLevel.skuPropertyValueId = resp.jobLevel?.skuPropertyValueId
    result.jobLevel.skuPropertyName = resp.jobLevel?.skuPropertyValueName
    // 学段
    result.learningPhase = new SkuVo()
    result.learningPhase.skuPropertyValueId = resp.learningPhase?.skuPropertyValueId
    result.learningPhase.skuPropertyName = resp.learningPhase?.skuPropertyValueName
    // 学科
    result.discipline = new SkuVo()
    result.discipline.skuPropertyValueId = resp.discipline?.skuPropertyValueId
    result.discipline.skuPropertyName = resp.discipline?.skuPropertyValueName
    // 证书类型
    result.certificatesType = new SkuVo()
    result.certificatesType.skuPropertyValueId = resp.certificatesType?.skuPropertyValueId
    result.certificatesType.skuPropertyName = resp.certificatesType?.skuPropertyValueName
    // 执业类别
    result.practitionerCategory = new SkuVo()
    result.practitionerCategory.skuPropertyValueId = resp.practitionerCategory?.skuPropertyValueId
    result.practitionerCategory.skuPropertyName = resp.practitionerCategory?.skuPropertyValueName
    // 培训形式
    result.trainingMode = new SkuVo<TrainingModeEnum>(
      resp.trainingForm?.skuPropertyValueId as TrainingModeEnum,
      resp.trainingForm?.skuPropertyValueName
    )
    return result
  }

  //废弃
  static filterSku(skuProperty: CommoditySkuPropertyResponse, skuProperties: SkuPropertyVo): SkuPropertyResponseVo {
    const skuPro = new SkuPropertyResponseVo()
    skuPro.year = skuProperties.year.find((item) => item.skuPropertyValueId == skuProperty.year.skuPropertyValueId)
    skuPro.subjectType = skuProperties.subjectType.find(
      (item) => item.skuPropertyValueId == skuProperty.subjectType.skuPropertyValueId
    )
    skuPro.trainingCategory = skuProperties.trainingCategory.find(
      (item) => item.skuPropertyValueId == skuProperty.trainingCategory.skuPropertyValueId
    )
    skuPro.trainingMajor = skuProperties.trainingMajor.find(
      (item) => item.skuPropertyValueId == skuProperty.trainingProfessional.skuPropertyValueId
    )
    skuPro.industry = skuProperties.industry.find(
      (item) => item.skuPropertyValueId == skuProperty.industry.skuPropertyValueId
    )
    const proVo = SkuPropertyConvertUtils.findRegion(skuProperties.RegionVos, skuProperty.province.skuPropertyValueId)
    const cityVo = SkuPropertyConvertUtils.findRegion(skuProperties.RegionVos, skuProperty.city.skuPropertyValueId)
    const countyVo = SkuPropertyConvertUtils.findRegion(skuProperties.RegionVos, skuProperty.county.skuPropertyValueId)
    const skuV = new SkuVo()
    let regionSkuId = '',
      regionSkuName = ''
    regionSkuId = proVo.id + (proVo.id ? '/' + cityVo.id : cityVo.id) + (cityVo.id ? '/' + countyVo.id : countyVo.id)
    regionSkuName =
      proVo.name + (proVo.name ? '/' + cityVo.name : cityVo.name) + (cityVo.name ? '/' + countyVo.name : countyVo.name)

    skuV.skuPropertyValueId = regionSkuId
    skuV.skuPropertyName = regionSkuName
    skuPro.region = skuV
    return skuPro
  }
  static findRegion(regionVos: RegionVo[], regionId: string) {
    regionVos.forEach((regionVo) => {
      if (regionVo.id == regionId) {
        return regionVo
      }
      if (regionVo.child.length) {
        SkuPropertyConvertUtils.findRegion(regionVo.child, regionId)
      }
    })
    return new RegionVo()
  }
  /**
   ** SkuPropertyListResponse转换SkuPropertyVo
   */
  // static async convertToSkuPropertyVo(source: SkuPropertyListResponse): Promise<SkuPropertyVo> {
  //   const factory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
  //
  //   const skuVo = new SkuPropertyVo()
  //   let SkuIds = []
  //   //年度
  //   if (source.year.length) {
  //     SkuIds = source.year.map(item => item.skuPropertyValueId)
  //     skuVo.year = await SkuPropertyConvertUtils.calYear(SkuIds, factory)
  //   }
  //   //地区
  //   if (source.region.length) {
  //     SkuIds = source.region.map(item => item.skuPropertyValueId)
  //     const regionArr = await SkuPropertyConvertUtils.requestBusinessRegionResponse(SkuIds, factory)
  //     skuVo.RegionVos = await SkuPropertyConvertUtils.filterRegionFirstStep(regionArr, factory)
  //   }
  //   //行业
  //   if (source.industry.length) {
  //     SkuIds = source.industry.map(item => item.skuPropertyValueId)
  //     skuVo.industry = await SkuPropertyConvertUtils.calIndustry(SkuIds, factory)
  //   }
  //   //科目类型
  //   if (source.subjectType.length) {
  //     SkuIds = source.subjectType.map(item => item.skuPropertyValueId)
  //     skuVo.subjectType = await SkuPropertyConvertUtils.calTrainingProperty(SkuIds, factory)
  //   }
  //   //培训类别
  //   if (source.trainingCategory.length) {
  //     SkuIds = source.trainingCategory.map(item => item.skuPropertyValueId)
  //     skuVo.trainingCategory = await SkuPropertyConvertUtils.calTrainingCategory(SkuIds, factory)
  //   }
  //   //培训专业
  //   if (source.trainingProfessional.length) {
  //     SkuIds = source.trainingProfessional.map(item => item.skuPropertyValueId)
  //     skuVo.trainingMajor = await SkuPropertyConvertUtils.calTrainingMajor(SkuIds, factory)
  //   }
  //
  //   return skuVo
  // }
  //处理地区
  static async calBusinessRegion(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const skuVos: SkuVo[] = []
    const dataList: Array<BusinessRegionResponse> = await SkuPropertyConvertUtils.requestBusinessRegionResponse(
      SkuIds,
      factory
    )
    dataList.forEach((tmpItem: BusinessRegionResponse) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.id
      skuV.skuPropertyName = tmpItem.name
      skuVos.push(skuV)
    })
    return skuVos.reverse()
  }
  static async requestBusinessRegionResponse(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const dataList: Array<BusinessRegionResponse> = await factory.queryPhysicalRegion.querRegionDetil(SkuIds)
    return dataList
  }
  //处理行业
  static async calIndustry(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const skuVos: SkuVo[] = []
    const dataList: Array<IndustryVo> = await factory.queryIndustry.getIndustryByIdList(SkuIds)
    dataList.forEach((tmpItem: IndustryVo) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.id
      skuV.skuPropertyName = tmpItem.name
      skuVos.push(skuV)
    })
    // console.log('找到的行业', dataList, skuVos)
    return skuVos
  }
  //处理年度
  static calYear(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const skuVos: SkuVo[] = []
    SkuIds?.forEach((item) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = item
      skuV.skuPropertyName = item
      skuVos.push(skuV)
    })
    return skuVos
    /*await QueryYearDict.queryYearList()
    const dataList = QueryYearDict.yearList
    dataList.forEach((tmpItem: YearVo) => {
      if (tmpItem.id === SkuIds[0]) {
        const skuV = new SkuVo()
        skuV.skuPropertyValueId = tmpItem.id
        skuV.skuPropertyName = tmpItem.year
        skuVos.push(skuV)
      }
    })
    return skuVos*/
  }
  //处理专业
  static async calTrainingMajor(SkuIds: string[], factory: QueryBasicdataDictionaryFactory, industryId?: string) {
    const skuVos: SkuVo[] = []
    const dataList: Array<TrainingCategoryVo> = await factory.queryTrainingMajor.getTrainingMajorByIdList({
      industryId: industryId || '',
      trainingMajorIdList: SkuIds
    })
    for (let i = 0; i < dataList.length; i++) {
      const tmpItem = dataList[i]
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.propertyId
      skuV.skuPropertyName = tmpItem.name + (tmpItem.showName ? '(' + tmpItem.showName + ')' : '')
      if (tmpItem.parentId && tmpItem.parentId != '-1') {
        const fatherSkuVos = await SkuPropertyConvertUtils.calTrainingMajor([tmpItem.parentId], factory, industryId)
        skuV.skuPropertyValueId = fatherSkuVos[0].skuPropertyValueId + '/' + skuV.skuPropertyValueId
        skuV.skuPropertyName = fatherSkuVos[0].skuPropertyName + '/' + skuV.skuPropertyName
      }
      skuVos.push(skuV)
    }
    return skuVos
  }
  // static async getAllId(item: TrainingCategoryVo,factory: QueryBasicdataDictionaryFactory,  industryId?: string){
  //   const SkuIds = [item.propertyId]
  //   const dataList: Array<TrainingCategoryVo> = await factory.queryTrainingMajor.getTrainingMajorByIdList({
  //     industryId: industryId || '',
  //     trainingMajorIdList: SkuIds
  //   })
  //  if (dataList[0].parentId) {
  //
  //  }
  // }
  //处理类别
  static async calTrainingCategory(SkuIds: string[], factory: QueryBasicdataDictionaryFactory, industryId?: string) {
    const skuVos: SkuVo[] = []
    const dataList: Array<TrainingCategoryVo> = await factory.queryTrainingCategory.getTrainingCategoryByIdList({
      industryId: industryId || '',
      trainingCategoryIdList: SkuIds
    })
    dataList.forEach((tmpItem: TrainingCategoryVo) => {
      const skuV = new SkuVo()
      skuV.parentId = tmpItem.parentId
      skuV.skuPropertyValueId = tmpItem.propertyId
      skuV.skuPropertyName = tmpItem.name + (tmpItem.showName ? '(' + tmpItem.showName + ')' : '')
      skuVos.push(skuV)
    })
    return skuVos
  }
  // 处理行业等级
  static async calTechnicalGrade(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const skuVos: SkuVo[] = []
    await factory.queryTechnologyLevel.query()
    const dataList = factory.queryTechnologyLevel.data
    dataList.forEach((item) => {
      if (item.id === SkuIds[0]) {
        const skuV = new SkuVo()
        skuV.parentId = ''
        skuV.skuPropertyName = item.showName
        skuV.skuPropertyValueId = item.id
        skuVos.push(skuV)
      }
    })
    return skuVos
  }
  //处理科目类型
  static async calTrainingProperty(SkuIds: string[], factory: QueryBasicdataDictionaryFactory, industryId?: string) {
    const skuVos: SkuVo[] = []
    const dataList: Array<SubjectTypeVo> = await factory.querySubjectType.getSubjectTypeByIdList({
      industryId: industryId || '',
      subjectTypeIdList: SkuIds
    })
    dataList.forEach((tmpItem: SubjectTypeVo) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.propertyId
      skuV.skuPropertyName = tmpItem.name + (tmpItem.showName ? '(' + tmpItem.showName + ')' : '')
      skuVos.push(skuV)
    })
    return skuVos
  }
  static async filterRegionFirstStep(
    regionArr: Array<BusinessRegionResponse>,
    factory: QueryBasicdataDictionaryFactory
  ) {
    const areaCodeSet = new Set()
    regionArr.forEach((tmpItem: BusinessRegionResponse) => {
      tmpItem.regionPath.split('/').forEach((areaCode) => {
        areaCode && areaCodeSet.add(areaCode)
      })
    })
    const region2List: Array<BusinessRegionResponse> = await factory.queryPhysicalRegion.querRegionDetil(
      Array.from(areaCodeSet) as string[]
    )
    return SkuPropertyConvertUtils.filterRegionArr(region2List)
  }
  //将地区对象数组组装成树状对象
  static filterRegionArr(regionArr: Array<BusinessRegionResponse>, parentId = '') {
    const regionVoArr: RegionVo[] = []
    regionArr.forEach((item) => {
      if (item.parentId == parentId) {
        const regionV = new RegionVo()
        // regionV.enable = item.enable
        regionV.sort = item.sort
        regionV.name = item.name
        regionV.regionPath = item.regionPath
        regionV.parentId = item.parentId
        regionV.id = item.id
        regionV.child = SkuPropertyConvertUtils.filterRegionArr(regionArr, item.id)
        regionVoArr.push(regionV)
      }
    })
    return regionVoArr
  }

  /**
   * 通用属性id获取属性名称
   * @param skuIds 属性id数组
   * @param industryId 行业id
   */
  static async calPropertyObject(skuIds: string[], industryId?: string) {
    const skuVos = new Array<SkuVo>()

    const trainingObjectList = await QueryPropertyDetail.getPropertyDetailByIds(industryId, skuIds)

    trainingObjectList?.length &&
      trainingObjectList.map((item) => {
        const sku = new SkuVo()

        sku.skuPropertyName = item.name + (item.showName ? '(' + item.showName + ')' : '')
        sku.parentId = item.parentId
        sku.skuPropertyValueId = item.propertyId
        skuVos.push(sku)
      })

    return skuVos
  }

  /**
   ** CommoditySkuPropertyResponse转换SkuPropertyResponseVo
   */
  static async convertToSkuPropertyResponseVo(source: ComplexSkuPropertyResponse): Promise<SkuPropertyResponseVo> {
    const factory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
    const skuVo = new SkuPropertyResponseVo()
    let SkuIds = []
    // Sku 地区id集合
    const skuRegionIds = new Array<string>()
    await SkuPropertyConvertUtilsV2.queryAllSku([source])
    const industryMap = SkuPropertyConvertUtilsV2.industryChildrenMap.get(source.industry.skuPropertyValueId)
    //年度
    if (source?.year) {
      SkuIds = [source.year.skuPropertyValueId]
      const skuVos = SkuPropertyConvertUtils.calYear(SkuIds, factory)
      skuVo.year = skuVos[0]
    }
    //地区
    if (source?.province?.skuPropertyValueId) skuRegionIds.push(source.province.skuPropertyValueId)
    if (source?.city?.skuPropertyValueId) skuRegionIds.push(source.city.skuPropertyValueId)
    if (source?.county?.skuPropertyValueId) skuRegionIds.push(source.county.skuPropertyValueId)
    if (skuRegionIds && skuRegionIds.length) {
      const regionSku = new Array<SkuVo>()
      skuRegionIds.forEach((id) => {
        if (SkuPropertyConvertUtilsV2.resigonMap.has(id)) {
          regionSku.push(SkuPropertyConvertUtilsV2.resigonMap.get(id))
        }
      })
      let regionSkuId = '',
        regionSkuName = ''
      regionSku.forEach((item) => {
        regionSkuId += !regionSkuId ? item.skuPropertyValueId : '/' + item.skuPropertyValueId
        regionSkuName += !regionSkuName ? item.skuPropertyName : '/' + item.skuPropertyName
      })
      const region = new SkuVo()
      region.skuPropertyName = regionSkuName
      region.skuPropertyValueId = regionSkuId
      skuVo.region = region
    }
    //行业
    if (source.industry) {
      skuVo.industry = SkuPropertyConvertUtilsV2.industryMap.get(source.industry.skuPropertyValueId)
    }
    // 技术等级
    if (source.technicalGrade) {
      skuVo.technicalGrade = SkuPropertyConvertUtilsV2.technologyLevelMap.get(source.technicalGrade.skuPropertyValueId)
    }
    //科目类型
    if (source.subjectType) {
      skuVo.subjectType = industryMap?.get(source.subjectType.skuPropertyValueId)
    }
    //培训类别
    if (source.trainingCategory) {
      skuVo.trainingCategory = industryMap?.get(source.trainingCategory.skuPropertyValueId)
    }
    //培训专业
    if (source.trainingProfessional) {
      skuVo.trainingMajor = industryMap?.get(source.trainingProfessional.skuPropertyValueId)
    }
    if (source.trainingObject) {
      skuVo.trainingObject = industryMap?.get(source.trainingObject.skuPropertyValueId)
    }
    if (source.positionCategory) {
      skuVo.positionCategory = industryMap?.get(source.positionCategory.skuPropertyValueId)
    }
    if (source.jobLevel) {
      skuVo.jobLevel = industryMap?.get(source.jobLevel.skuPropertyValueId)
    }
    // 学科、学段转换
    if (source.learningPhase) {
      skuVo.learningPhase = industryMap?.get(source.learningPhase.skuPropertyValueId)
    }
    if (source.discipline) {
      skuVo.discipline = industryMap?.get(source.discipline.skuPropertyValueId)
    }
    if (source.certificatesType) {
      skuVo.certificatesType = industryMap?.get(source.certificatesType.skuPropertyValueId)
    }
    if (source.practitionerCategory) {
      skuVo.practitionerCategory = industryMap?.get(source.practitionerCategory.skuPropertyValueId)
    }
    if (source.trainingForm?.skuPropertyValueId || source.trainingWay?.skuPropertyValueId) {
      const skuPropertyValueId = source.trainingForm?.skuPropertyValueId || source.trainingWay?.skuPropertyValueId
      skuVo.trainingMode = new SkuVo<TrainingModeEnum>(
        skuPropertyValueId as TrainingModeEnum,
        TrainingMode.map.get(skuPropertyValueId as TrainingModeEnum)
      )
    }
    console.log(skuVo, '返给ui的sku')
    return skuVo
  }

  // 处理学段
  static async calTrainingPhase(SkuIds: string[], industry: string) {
    await QueryIndustry.queryIndustry()
    const industryProperty = QueryIndustry.industryList.find((item) => item.id == industry)
    const dataList = await QueryGrade.queryGradeByIndustryV2(industryProperty.id, industryProperty.propertyId)
    const skuVos: SkuVo[] = []
    if (!dataList.length) {
      return skuVos
    }
    dataList.forEach((tmpItem: TrainingPropertyResponse) => {
      if (SkuIds.indexOf(tmpItem.propertyId) !== -1) {
        const skuV = new SkuVo()
        skuV.skuPropertyValueId = tmpItem.propertyId
        // skuV.skuPropertyName = tmpItem.name + (tmpItem.showName ? '(' + tmpItem.showName + ')' : '')
        skuV.skuPropertyName = tmpItem.name
        skuV.parentId = tmpItem.parentId
        skuVos.push(skuV)
      }
    })
    return skuVos
  }
  // 处理学科、学段
  static async calTrainingDiscipline(skuIds: string[], industryId?: string) {
    const skuVos = new Array<SkuVo>()

    const trainingObjectList = await QueryPropertyDetail.getPropertyDetailByIds(industryId, skuIds)

    trainingObjectList?.length &&
      trainingObjectList.map((item) => {
        const sku = new SkuVo()

        sku.skuPropertyName = item.showName ? item.showName : item.name
        sku.parentId = item.parentId
        sku.skuPropertyValueId = item.propertyId
        skuVos.push(sku)
      })

    return skuVos
  }
  // 处理证书类型、执业类别
  static async calCertAndPractitioner(skuIds: string[], industryId?: string) {
    const skuVos = new Array<SkuVo>()
    const trainingObjectList = await QueryPropertyDetail.getPropertyDetailByIds(industryId, skuIds)
    trainingObjectList?.length &&
      trainingObjectList.map((item) => {
        const sku = new SkuVo()
        sku.skuPropertyName = item.showName ? item.showName : item.name
        sku.parentId = item.parentId
        sku.skuPropertyValueId = item.propertyId
        skuVos.push(sku)
      })
    return skuVos
  }
  /**
   * @description ConvertToSkuPropertyResponseVo的批量版
   * @param source 方案sku集合
   */
  static async batchConvertToSkuPropertyResponseVo(source: SchemeSkuInfo[]): Promise<SchemeSkuInfo[]> {
    const factory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
    let yearList: SkuVo[] = []
    const regionList: SkuVo[] = [],
      industryList: SkuVo[] = [],
      subjectTypeList: SkuVo[] = [],
      trainingCategoryList: SkuVo[] = [],
      trainingProfessionalList: SkuVo[] = [],
      trainingObjectList: SkuVo[] = [],
      jobLevelList: SkuVo[] = [],
      positionCategoryList: SkuVo[] = [],
      phaseList: SkuVo[] = [],
      disciplineList: SkuVo[] = [],
      certificatesTypeList: SkuVo[] = [],
      practitionerCategoryList: SkuVo[] = []
    const schemeSkus = source.map((scheme) => {
      return scheme.sku
    })
    await SkuPropertyConvertUtilsV2.queryAllSku(schemeSkus)
    // 查询年度
    const yearIds = [...new Set(source.map((item) => item.sku.year?.skuPropertyValueId)?.filter(Boolean))]
    if (yearIds.length) {
      yearList = SkuPropertyConvertUtils.calYear(yearIds, factory)
    }
    // 查询地区
    const regionIds = [
      ...new Set(source.map((item) => item.sku.province?.skuPropertyValueId)?.filter(Boolean)),
      ...new Set(source.map((item) => item.sku.city?.skuPropertyValueId)?.filter(Boolean)),
      ...new Set(source.map((item) => item.sku.county?.skuPropertyValueId)?.filter(Boolean))
    ]
    // 获取 SKU 地区
    if (regionIds.length) {
      regionIds.forEach((id) => {
        regionList.push(SkuPropertyConvertUtilsV2.resigonMap.get(id))
      })
    }
    // 查询行业
    const industryIds = [...new Set(source.map((item) => item.sku.industry?.skuPropertyValueId)?.filter(Boolean))]
    if (industryIds.length) {
      industryIds.forEach((id) => {
        industryList.push(SkuPropertyConvertUtilsV2.industryMap.get(id))
      })
    }
    // 查询科目类型
    // 1-计算【行业id-科目类型id集合】数组
    const subjectTypeGroup = source.reduce((prev, cur) => {
      const industryId = cur.sku.industry?.skuPropertyValueId
      const subjectTypeId = cur.sku.subjectType?.skuPropertyValueId
      if (industryId && subjectTypeId) {
        const target = prev.find((el) => el.industryId === industryId)
        if (target) {
          if (!target.subjectTypeIdList?.includes(subjectTypeId)) target.subjectTypeIdList.push(subjectTypeId)
        } else prev.push(new SubjectTypeRequest(industryId, [subjectTypeId]))
      }
      return prev
    }, [] as SubjectTypeRequest[])
    if (subjectTypeGroup.length) {
      subjectTypeGroup.forEach((subject) => {
        const industryMap = SkuPropertyConvertUtilsV2.industryChildrenMap.get(subject.industryId)
        subject.subjectTypeIdList.forEach((subjectTypeId) => {
          if (industryMap?.has(subjectTypeId)) {
            subjectTypeList.push(industryMap.get(subjectTypeId))
          }
        })
      })
    }
    // 查询培训类别
    const trainingCategoryGroup = source.reduce((prev, cur) => {
      const industryId = cur.sku.industry?.skuPropertyValueId
      const trainingCategoryId = cur.sku.trainingCategory?.skuPropertyValueId
      if (industryId && trainingCategoryId) {
        const target = prev.find((el) => el.industryId === industryId)
        if (target) {
          if (!target.trainingCategoryIdList?.includes(trainingCategoryId))
            target.trainingCategoryIdList.push(trainingCategoryId)
        } else prev.push(new TrainingCategoryRequest(industryId, [trainingCategoryId]))
      }
      return prev
    }, [] as TrainingCategoryRequest[])
    if (trainingCategoryGroup.length) {
      trainingCategoryGroup.forEach((subject) => {
        const industryMap = SkuPropertyConvertUtilsV2.industryChildrenMap.get(subject.industryId)
        subject.trainingCategoryIdList.forEach((trainingCategoryId) => {
          if (industryMap?.has(trainingCategoryId)) {
            trainingCategoryList.push(industryMap.get(trainingCategoryId))
          }
        })
      })
    }
    // 查询培训专业
    const trainingProfessionalGroup = source.reduce((prev, cur) => {
      const industryId = cur.sku.industry?.skuPropertyValueId
      const trainingProfessionalId = cur.sku.trainingProfessional?.skuPropertyValueId
      if (industryId && trainingProfessionalId) {
        const target = prev.find((el) => el.industryId === industryId)
        if (target) {
          if (!target.trainingProfessionalIdList?.includes(trainingProfessionalId))
            target.trainingProfessionalIdList.push(trainingProfessionalId)
        } else prev.push(new TrainingProfessionalRequest(industryId, [trainingProfessionalId]))
      }
      return prev
    }, [] as TrainingProfessionalRequest[])
    if (trainingProfessionalGroup.length) {
      trainingProfessionalGroup.forEach((subject) => {
        const industryMap = SkuPropertyConvertUtilsV2.industryChildrenMap.get(subject.industryId)
        subject.trainingProfessionalIdList.forEach((trainingProfessionalId) => {
          if (industryMap?.has(trainingProfessionalId)) {
            trainingProfessionalList.push(industryMap.get(trainingProfessionalId))
          }
        })
      })
    }
    const trainingObjectIds = new Array<BatchQueryPropertyRequest>()
    const positionCategoryIds = new Array<BatchQueryPropertyRequest>()
    const jobLevelIds = new Array<BatchQueryPropertyRequest>()
    // 学段
    const phaseIds = new Array<BatchQueryPropertyRequest>()
    // 学科
    const disciplineIds = new Array<BatchQueryPropertyRequest>()
    const certTypeIds = new Array<BatchQueryPropertyRequest>()
    const practitionerCategoryIds = new Array<BatchQueryPropertyRequest>()
    source.map((item) => {
      if (item.sku?.industry?.skuPropertyValueId) {
        item.sku?.trainingObject?.skuPropertyValueId &&
          trainingObjectIds.push(
            new BatchQueryPropertyRequest(item.sku.industry.skuPropertyValueId, [
              item.sku?.trainingObject?.skuPropertyValueId
            ])
          )
        item.sku?.positionCategory?.skuPropertyValueId &&
          positionCategoryIds.push(
            new BatchQueryPropertyRequest(item.sku.industry.skuPropertyValueId, [
              item.sku?.positionCategory?.skuPropertyValueId
            ])
          )
        item.sku?.jobLevel?.skuPropertyValueId &&
          jobLevelIds.push(
            new BatchQueryPropertyRequest(item.sku.industry.skuPropertyValueId, [
              item.sku?.jobLevel?.skuPropertyValueId
            ])
          )
        item.sku?.discipline?.skuPropertyValueId &&
          disciplineIds.push(
            new BatchQueryPropertyRequest(item.sku.industry.skuPropertyValueId, [
              item.sku.discipline.skuPropertyValueId
            ])
          )
        item.sku?.learningPhase?.skuPropertyValueId &&
          phaseIds.push(
            new BatchQueryPropertyRequest(item.sku.industry.skuPropertyValueId, [
              item.sku.learningPhase.skuPropertyValueId
            ])
          )
        item.sku?.certificatesType?.skuPropertyValueId &&
          certTypeIds.push(
            new BatchQueryPropertyRequest(item.sku.industry.skuPropertyValueId, [
              item.sku.certificatesType.skuPropertyValueId
            ])
          )
        item.sku?.practitionerCategory?.skuPropertyValueId &&
          practitionerCategoryIds.push(
            new BatchQueryPropertyRequest(item.sku.industry.skuPropertyValueId, [
              item.sku.practitionerCategory.skuPropertyValueId
            ])
          )
      }
    })
    if (trainingObjectIds.length) {
      trainingObjectIds.forEach((subject) => {
        const industryMap = SkuPropertyConvertUtilsV2.industryChildrenMap.get(subject.industryId)
        subject.propertyIds.forEach((propertyId) => {
          if (industryMap?.has(propertyId)) {
            trainingObjectList.push(industryMap.get(propertyId))
          }
        })
      })
    }
    if (positionCategoryIds.length) {
      positionCategoryIds.forEach((subject) => {
        const industryMap = SkuPropertyConvertUtilsV2.industryChildrenMap.get(subject.industryId)
        subject.propertyIds.forEach((propertyId) => {
          if (industryMap?.has(propertyId)) {
            positionCategoryList.push(industryMap.get(propertyId))
          }
        })
      })
    }
    if (jobLevelIds.length) {
      jobLevelIds.forEach((subject) => {
        const industryMap = SkuPropertyConvertUtilsV2.industryChildrenMap.get(subject.industryId)
        subject.propertyIds.forEach((propertyId) => {
          if (industryMap?.has(propertyId)) {
            jobLevelList.push(industryMap.get(propertyId))
          }
        })
      })
    }
    // 获取学段list数据
    if (phaseIds.length) {
      phaseIds.forEach((subject) => {
        const industryMap = SkuPropertyConvertUtilsV2.industryChildrenMap.get(subject.industryId)
        subject.propertyIds.forEach((propertyId) => {
          if (industryMap?.has(propertyId)) {
            phaseList.push(industryMap.get(propertyId))
          }
        })
      })
    }
    // 获取学科list
    if (disciplineIds.length) {
      disciplineIds.forEach((subject) => {
        const industryMap = SkuPropertyConvertUtilsV2.industryChildrenMap.get(subject.industryId)
        subject.propertyIds.forEach((propertyId) => {
          if (industryMap?.has(propertyId)) {
            disciplineList.push(industryMap.get(propertyId))
          }
        })
      })
    }
    if (certTypeIds.length) {
      certTypeIds.forEach((cert) => {
        const industryMap = SkuPropertyConvertUtilsV2.industryChildrenMap.get(cert.industryId)
        cert.propertyIds.forEach((propertyId) => {
          if (industryMap?.has(propertyId)) {
            certificatesTypeList.push(industryMap.get(propertyId))
          }
        })
      })
    }
    if (practitionerCategoryIds.length) {
      practitionerCategoryIds.forEach((practitioner) => {
        const industryMap = SkuPropertyConvertUtilsV2.industryChildrenMap.get(practitioner.industryId)
        practitioner.propertyIds.forEach((propertyId) => {
          if (industryMap?.has(propertyId)) {
            practitionerCategoryList.push(industryMap.get(propertyId))
          }
        })
      })
    }

    source.forEach((item) => {
      // 填充年度
      item.skuName.year =
        yearList.find((el) => el.skuPropertyValueId === item.sku.year?.skuPropertyValueId) || new SkuVo()
      // 填充地区
      const province =
        regionList.find((el) => el.skuPropertyValueId === item.sku.province?.skuPropertyValueId) || new SkuVo()
      const city = regionList.find((el) => el?.skuPropertyValueId === item.sku.city?.skuPropertyValueId) || new SkuVo()
      const county =
        regionList.find((el) => el?.skuPropertyValueId === item.sku.county?.skuPropertyValueId) || new SkuVo()
      const regionInfoList = [province, city, county]
      item.skuName.region.skuPropertyValueId = regionInfoList
        .map((el) => el.skuPropertyValueId)
        ?.filter(Boolean)
        ?.join('/')
      item.skuName.region.skuPropertyName = regionInfoList
        .map((el) => el.skuPropertyName)
        ?.filter(Boolean)
        ?.join('/')
      // 填充行业
      item.skuName.industry =
        industryList.find((el) => el.skuPropertyValueId === item.sku.industry?.skuPropertyValueId) || new SkuVo()
      console.log(item.skuName.industry, 'item.skuName.industry')
      // 填充科目类型
      item.skuName.subjectType =
        subjectTypeList.find((el) => el.skuPropertyValueId === item.sku.subjectType?.skuPropertyValueId) || new SkuVo()
      // 填充培训类别
      item.skuName.trainingCategory =
        trainingCategoryList.find((el) => el.skuPropertyValueId === item.sku.trainingCategory?.skuPropertyValueId) ||
        new SkuVo()
      // 填充培训专业
      item.skuName.trainingMajor =
        trainingProfessionalList.find((el) =>
          el.skuPropertyValueId.split('/').includes(item.sku.trainingProfessional?.skuPropertyValueId)
        ) || new SkuVo()
      item.skuName.trainingObject =
        trainingObjectList.find((el) => el.skuPropertyValueId === item.sku.trainingObject?.skuPropertyValueId) ||
        new SkuVo()
      item.skuName.positionCategory =
        positionCategoryList.find((el) => el.skuPropertyValueId === item.sku.positionCategory?.skuPropertyValueId) ||
        new SkuVo()
      item.skuName.jobLevel =
        jobLevelList.find((el) => el.skuPropertyValueId === item.sku.jobLevel?.skuPropertyValueId) || new SkuVo()
      // 教师行业填充
      item.skuName.learningPhase =
        phaseList.find((el) => el.skuPropertyValueId === item.sku.learningPhase?.skuPropertyValueId) || new SkuVo()

      item.skuName.discipline =
        disciplineList.find((el) => el.skuPropertyValueId === item.sku.discipline?.skuPropertyValueId) || new SkuVo()
      item.skuName.certificatesType =
        certificatesTypeList.find((el) => el.skuPropertyValueId === item.sku.certificatesType?.skuPropertyValueId) ||
        new SkuVo()

      item.skuName.practitionerCategory =
        practitionerCategoryList.find(
          (el) => el.skuPropertyValueId === item.sku.practitionerCategory?.skuPropertyValueId
        ) || new SkuVo()
      // 培训形式
      const trainingForm = item.sku.trainingForm || item.sku.trainingWay
      item.skuName.trainingMode = trainingForm?.skuPropertyValueId
        ? new SkuVo<TrainingModeEnum>(
            trainingForm.skuPropertyValueId as TrainingModeEnum,
            TrainingMode.map.get(trainingForm.skuPropertyValueId as TrainingModeEnum)
          )
        : new SkuVo()
    })
    return source
  }

  /**
   * 批量查询科目类型
   * @param subjectTypeGroup 【行业id-科目类型id集合】数组
   * @param factory 查询工厂
   */
  static async batchCalTrainingSubjectType(
    subjectTypeGroup: SubjectTypeRequest[],
    factory: QueryBasicdataDictionaryFactory
  ) {
    const result = [] as SkuVo[]
    const dataList: Array<SubjectTypeVo> = await factory.querySubjectType.batchGetSubjectTypeByIdList(subjectTypeGroup)
    dataList?.forEach((item) => {
      const opt = new SkuVo()
      opt.skuPropertyValueId = item.propertyId
      opt.skuPropertyName = item.showName ? `${item.name}(${item.showName})` : item.name
      result.push(opt)
    })
    return result
  }

  /**
   * 批量查询培训类别
   * @param trainingCategoryGroup 请求入参
   * @param factory 查询工厂
   */
  static async batchCalTrainingCategory(
    trainingCategoryGroup: TrainingCategoryRequest[],
    factory: QueryBasicdataDictionaryFactory
  ) {
    const result = [] as SkuVo[]
    const dataList: Array<TrainingCategoryVo> = await factory.queryTrainingCategory.batchGetTrainingCategoryByIdList(
      trainingCategoryGroup
    )
    dataList?.forEach((item) => {
      const opt = new SkuVo()
      opt.skuPropertyValueId = item.propertyId
      opt.skuPropertyName = item.showName ? `${item.name}(${item.showName})` : item.name
      result.push(opt)
    })
    return result
  }

  /**
   * 通用批量查询行业属性
   * @param ids 请求入参
   */
  static async batchCalProperty(ids: BatchQueryPropertyRequest[]) {
    const result = [] as SkuVo[]
    const dataList: Array<TrainingPropertyResponse> = await QueryPropertyDetail.batchGetPropertyDetailByIds(ids)
    dataList?.forEach((item) => {
      const opt = new SkuVo()
      opt.skuPropertyValueId = item.propertyId
      opt.skuPropertyName = item.showName ? `${item.name}(${item.showName})` : item.name
      result.push(opt)
    })
    return result
  }

  /**
   * 批量查询培训专业
   * @param trainingProfessionalGroup 请求入参
   * @param factory 查询工厂
   */
  static async batchCalTrainingProfessional(
    trainingProfessionalGroup: TrainingProfessionalRequest[],
    factory: QueryBasicdataDictionaryFactory
  ) {
    const result = [] as SkuVo[]
    const dataList: Array<TrainingCategoryVo> = await factory.queryTrainingMajor.batchGetTrainingMajorByIdList(
      trainingProfessionalGroup
    )
    // id、父级id
    const idMap = new Map<string, string>()
    dataList?.forEach((item) => {
      const opt = new SkuVo()
      opt.skuPropertyValueId = item.propertyId
      opt.skuPropertyName = item.showName ? `${item.name}(${item.showName})` : item.name
      if (item.parentId && item.parentId !== '-1') {
        idMap.set(item.propertyId, item.parentId)
      }
      result.push(opt)
    })
    console.log('###trainingProfessionalGroup', trainingProfessionalGroup)
    if (idMap.size) {
      const parentTrainingProfessionalGroup = trainingProfessionalGroup.reduce((prev, cur) => {
        cur.trainingProfessionalIdList.forEach((childId) => {
          // 判断父级id是否存在于Map中
          const parentId = idMap.get(childId)
          if (parentId) {
            // 如果存在需要重新构造查询参数
            const item = prev.find((el) => el.industryId === cur.industryId)
            if (item) {
              // 如果行业id存在、push
              item.trainingProfessionalIdList.push(parentId)
            } else {
              // 不存在则新增一项
              prev.push(new TrainingProfessionalRequest(cur.industryId, [parentId]))
            }
          }
        })
        return prev
      }, [] as TrainingProfessionalRequest[])
      console.log('###parentTrainingProfessionalGroup', parentTrainingProfessionalGroup)
      if (parentTrainingProfessionalGroup.length) {
        const parentDataList: Array<TrainingCategoryVo> =
          await factory.queryTrainingMajor.batchGetTrainingMajorByIdList(parentTrainingProfessionalGroup)
        result.forEach((item) => {
          const data = dataList.find((el) => el.propertyId === item.skuPropertyValueId)
          const parentInfo = parentDataList.find((el) => el.propertyId === data.parentId)
          if (parentInfo) {
            item.skuPropertyValueId = parentInfo.propertyId + '/' + item.skuPropertyValueId
            item.skuPropertyName = parentInfo.showName
              ? `${parentInfo.name}(${parentInfo.showName})/${item.skuPropertyName}`
              : `${parentInfo.name}/${item.skuPropertyName}`
          }
        })
      }
    }
    return result
  }

  /**
   * 获取所有行业
   * @returns {Promise<Array<BusinessDataDictionaryResponse>>}
   */
  static async getIndustryAll() {
    const response = await QueryIndustry.getIndustryDICT()
    return (
      response?.map((item) => {
        const sku = new SkuVo()
        sku.skuPropertyValueId = item.id
        sku.skuPropertyName = item.name
        return sku
      }) || []
    )
  }
  static serverRegion = new Array<RegionResponse>()
  /**
   * 批量查询地区
   * @param skuIds skuId集合
   */
  static async batchCalBusinessRegion(skuIds: string[]) {
    const skuVos: SkuVo[] = []
    if (!this.serverRegion.length) {
      const response = await BasicDataQueryBackstage.getServiceOrIndustryRegion(1)
      if (response.status?.isSuccess() && response.data && response.data?.length) {
        this.serverRegion = response.data
      }
    }
    // const response = await BasicDataQueryBackstage.getServiceOrIndustryRegion(1)
    // if (response.status?.isSuccess() && response.data && response.data?.length) {
    skuIds.forEach((item) => {
      const target = this.serverRegion.find((el) => el.code === item)
      console.log(target, 'target')
      if (target) {
        skuVos.push({
          skuPropertyValueId: target.code,
          skuPropertyName: target.name
        } as SkuVo)
      }
    })
    if (skuVos.length < skuIds.length) {
      const skuVosList = skuVos.map((ite) => ite.skuPropertyValueId)
      const otherRegion = skuIds.filter((item) => {
        return !skuVosList.includes(item)
      })
      const regionList = await QueryBusinessRegion.queryRegionsNameByIds(otherRegion)
      otherRegion.map((item) => {
        const skuVo = new SkuVo()
        skuVo.skuPropertyName = regionList.get(item)
        skuVo.skuPropertyValueId = item
        skuVos.push(skuVo)
      })
    }
    // }
    return skuIds.map((item) => {
      return skuVos.find((ite) => ite.skuPropertyValueId == item) || new SkuVo()
    })
    // return skuVos
  }
}
