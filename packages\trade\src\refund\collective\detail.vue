<route-params content="/:id"></route-params>
<route-meta>
  {
  "isMenu":true,
  "hideMenu": true,
  "onlyShowOnTab":true,
  "title": "集体退款详情"
  }
</route-meta>
<template>
  <el-main v-if="$hasPermission('refundDetail')" desc="退款详情详情" actions="created">
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/training/trade/refund/collective')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/trade/refund/collective' }">集体报名退款</el-breadcrumb-item>
      <el-breadcrumb-item>详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <!--退款审批中    refoundStatus==1,2 -->
      <el-card
        shadow="never"
        class="m-card is-header m-order-state"
        v-if="
          refundDetailList.refoundStatus === 0 ||
          refundDetailList.refoundStatus === 1 ||
          refundDetailList.refoundStatus === 2
        "
      >
        <div class="info">
          <p>退款单号：{{ refundDetailList.refoundNo }}</p>
          <p class="state f-cb">退款审批中</p>
          <!-- 退款审批中👇 -->
          <div
            class="op f-mt15"
            v-if="$hasPermission('approve') && !isZtlogin"
            desc="退款审批"
            actions="cancelRefund,refuseRefund,agreeRefund"
          >
            <el-button type="warning" @click="openCancelRefund()" size="mini" plain>取消退款</el-button>
            <el-button type="warning" @click="openRefuseRefund" size="mini" plain>拒绝退款</el-button>
            <el-button type="warning" size="mini" v-if="isWft()" @click="wftDialog" plain>同意退款</el-button>
            <el-button type="warning" size="mini" v-else @click="agreeRefund" plain>同意退款</el-button>
          </div>
        </div>
        <el-steps :active="1" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundDetailList.refoundDate"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step title="审批通过" icon="hb-iconfont icon-success"></el-step>
          <el-step title="退款成功" icon="hb-iconfont icon-s-pay"></el-step>
        </el-steps>
      </el-card>
      <!--退款被拒绝-->
      <el-card
        shadow="never"
        class="m-card is-header m-order-state"
        v-if="refundDetailList.refoundStatus === 12 && refundDetailList.closeType === 2"
      >
        <div class="info">
          <p>退款单号：{{ refundDetailList.refoundNo }}</p>
          <p class="state f-c9">退款被拒绝</p>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundDetailList.refoundDate"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="退款被拒绝"
            :description="refundDetailList.approvalDate"
            icon="hb-iconfont icon-s-close"
          ></el-step>
        </el-steps>
      </el-card>
      <!--资源回收中-->
      <el-card shadow="never" class="m-card is-header m-order-state" v-if="refundDetailList.refoundStatus === 3">
        <div class="info">
          <p>退款单号：{{ refundDetailList.refoundNo }}</p>
          <p class="state f-cb">资源回收中</p>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundDetailList.refoundDate"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="审批通过"
            :description="refundDetailList.approvalDate"
            icon="hb-iconfont icon-success"
          ></el-step>
          <el-step title="退款成功" icon="hb-iconfont icon-s-pay"></el-step>
        </el-steps>
      </el-card>
      <!--资源回收失败-->
      <el-card shadow="never" class="m-card is-header m-order-state" v-if="refundDetailList.refoundStatus === 4">
        <div class="info">
          <p>退款单号：{{ refundDetailList.refoundNo }}</p>
          <p class="state f-cr">资源回收失败</p>
          <div
            class="op f-mt15"
            v-if="$hasPermission('retryRecycleRefund')"
            desc="重新回收资源"
            actions="retryRecycleRefund"
          >
            <el-button type="warning" size="mini" v-if="!isZtlogin" @click="retryRecycleRefund" plain
              >重新回收资源</el-button
            >
          </div>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundDetailList.refoundDate"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="审批通过"
            :description="refundDetailList.approvalDate"
            icon="hb-iconfont icon-success"
          ></el-step>
          <el-step title="退款成功" icon="hb-iconfont icon-s-pay"></el-step>
        </el-steps>
      </el-card>
      <!--退款处理中-->
      <el-card
        shadow="never"
        class="m-card is-header m-order-state"
        v-if="
          refundDetailList.refoundStatus === 5 ||
          refundDetailList.refoundStatus === 6 ||
          refundDetailList.refoundStatus === 7
        "
      >
        <div class="info">
          <p>退款单号：{{ refundDetailList.refoundNo }}</p>
          <p class="state f-cb">退款处理中</p>
          <div class="op f-mt15" v-if="$hasPermission('confirmRefund')" desc="确认退款" actions="confirmRefund">
            <el-button
              type="warning"
              v-if="refundDetailList.refoundStatus === 6 && !isZtlogin"
              size="mini"
              @click="confirmRefund"
              plain
              >确认退款</el-button
            >
          </div>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundDetailList.refoundDate"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="审批通过"
            :description="refundDetailList.approvalDate"
            icon="hb-iconfont icon-success"
          ></el-step>
          <el-step title="退款成功" icon="hb-iconfont icon-s-pay"></el-step>
        </el-steps>
      </el-card>
      <!--退款银行处理中-->
      <!-- <el-card
        shadow="never"
        class="m-card is-header m-order-state"
        v-if="refundList.basicData.returnOrderStatus === 6"
      >
        <div class="info">
          <p>退款单号：{{ refundList.refoundNo }}</p>
          <p class="state f-cb">退款银行处理中</p>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundList.basicData.returnOrderStatusChangeTime.applied"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="审批通过"
            :description="refundList.basicData.returnOrderStatusChangeTime.returning"
            icon="hb-iconfont icon-success"
          ></el-step>
          <el-step title="退款成功" icon="hb-iconfont icon-s-pay"></el-step>
        </el-steps>
      </el-card> -->
      <!--退款失败-->
      <el-card shadow="never" class="m-card is-header m-order-state" v-if="refundDetailList.refoundStatus === 8">
        <div class="info">
          <p>退款单号：{{ refundDetailList.refoundNo }}</p>
          <p class="state f-cr">退款失败</p>
          <div class="op f-mt15" v-if="$hasPermission('continueRefund')" desc="继续退款" actions="continueRefund">
            <el-button type="warning" size="mini" v-if="!isZtlogin" @click="continueRefund" plain>继续退款</el-button>
          </div>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundDetailList.refoundDate"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="审批通过"
            :description="refundDetailList.approvalDate"
            icon="hb-iconfont icon-success"
          ></el-step>
          <el-step title="退款成功" icon="hb-iconfont icon-s-pay"></el-step>
        </el-steps>
      </el-card>
      <!--退款成功-->
      <el-card
        shadow="never"
        class="m-card is-header m-order-state"
        v-if="
          refundDetailList.refoundStatus === 9 ||
          refundDetailList.refoundStatus === 10 ||
          refundDetailList.refoundStatus === 11
        "
      >
        <div class="info">
          <p>退款单号：{{ refundDetailList.refoundNo }}</p>
          <p class="state f-cg">退款成功</p>
        </div>
        <el-steps :active="3" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundDetailList.refoundDate"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="审批通过"
            :description="refundDetailList.approvalDate"
            icon="hb-iconfont icon-success"
          ></el-step>
          <el-step
            title="退款成功"
            :description="refundDetailList.refoundSuccessDate"
            icon="hb-iconfont icon-s-pay"
          ></el-step>
        </el-steps>
      </el-card>
      <!--退款已取消-->
      <el-card
        shadow="never"
        class="m-card is-header m-order-state"
        v-if="
          refundDetailList.refoundStatus === 12 &&
          (refundDetailList.closeType === 1 || refundDetailList.closeType === 3 || refundDetailList.closeType === 4)
        "
      >
        <div class="info">
          <p>退款单号：{{ refundDetailList.refoundNo }}</p>
          <p class="state f-c9">退款已取消</p>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundDetailList.refoundDate"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="退款取消"
            :description="refundDetailList.cancelDate"
            icon="hb-iconfont icon-s-close"
          ></el-step>
        </el-steps>
      </el-card>
      <!--订单信息-->

      <el-card shadow="never" class="m-card is-header m-order-info f-mb15">
        <div class="f-flex-sub f-plr20 f-pt10">
          <div class="m-tit">
            <span class="tit-txt">退款信息</span>
          </div>
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="12">
              <el-form-item label="退款类型：">
                <!-- {{refundDetailList.refoundType == 1
                  ? '仅退货'
                  : refundList.basicData.returnOrderType == 2
                  ? '仅退款'
                  : '退货并退款'
              }} -->
                退货退款
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款金额：">￥{{ refundDetailList.refoundAmount }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款方式：">{{
                refundDetailList.refoundWay === 1 ? '线上退款' : '线下退款'
              }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款原因：">{{ refundDetailList.refoundReason || '-' }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款说明：">{{ refundDetailList.refoundExplain || '-' }}</el-form-item>
            </el-col>
            <el-col
              :span="24"
              class="f-ml10"
              v-if="
                refundDetailList.refoundStatus === 5 ||
                refundDetailList.refoundStatus === 6 ||
                refundDetailList.refoundStatus === 7
              "
            >
              <el-alert type="warning" show-icon :closable="false" class="m-alert f-ml50">
                <p>收款帐号是微信支付/支付宝，且退款方式为“线上退款”，确认退款之后，无需输入密码，即触发款项退款。</p>
              </el-alert>
            </el-col>
          </el-form>
        </div>
        <div class="right f-plr20 f-ptb10">
          <div class="m-tit">
            <span class="tit-txt">退款单号</span>
          </div>
          <el-form label-width="auto" class="m-text-form is-column f-pt10 f-pl20">
            <el-form-item label="退款单号：">{{ refundDetailList.refoundNo || '-' }}</el-form-item>
          </el-form>
          <div class="m-tit">
            <span class="tit-txt">退款操作记录</span>
          </div>
          <div class="f-pl20" v-for="(item, index) in refundDetailList.refundRecord" :key="index">
            <p>
              <span class="f-fb">{{ item.name || '-' }}</span>
              在
              <span class="f-fb">{{ item.time || '-' }}</span>
              {{
                item.UIReturnOrderStatue === BatchRefundTradeStatusEnum.REFUNDING
                  ? '发起退款申请'
                  : item.UIReturnOrderStatue === BatchRefundTradeStatusEnum.REFUNDDISPOSE
                  ? '同意退款申请'
                  : item.UIReturnOrderStatue === BatchRefundTradeStatusEnum.REFUSEDREFUND
                  ? '拒绝退款申请'
                  : item.UIReturnOrderStatue === BatchRefundTradeStatusEnum.CANCELREFUND
                  ? '发起“取消退款”操作'
                  : '退款成功'
              }}
            </p>
            <el-form label-width="auto" class="m-text-form is-column f-pt10 f-pb20">
              <el-form-item label="退款金额：" v-if="item.UIReturnOrderStatue === BatchRefundTradeStatusEnum.REFUNDING"
                >¥ {{ item.money }}</el-form-item
              >
              <el-form-item
                label="退款状态："
                v-if="item.UIReturnOrderStatue === BatchRefundTradeStatusEnum.REFUNDING"
                >{{ statusMapType[item.UIReturnOrderStatue] || '-' }}</el-form-item
              >
              <el-form-item
                label="退款说明："
                v-if="item.UIReturnOrderStatue === BatchRefundTradeStatusEnum.REFUNDING"
                >{{ item.tipMsg || '-' }}</el-form-item
              >
              <el-form-item
                label="拒绝原因："
                v-if="item.UIReturnOrderStatue === BatchRefundTradeStatusEnum.REFUSEDREFUND"
                >{{ item.tipMsg || '-' }}</el-form-item
              >
              <el-form-item
                label="取消原因："
                v-if="item.UIReturnOrderStatue === BatchRefundTradeStatusEnum.CANCELREFUND"
                >{{ item.tipMsg || '-' }}</el-form-item
              >
            </el-form>
          </div>
        </div>
      </el-card>
      <!--报名批次信息-->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="f-plr20 f-pt10">
          <div class="m-tit is-small">
            <span class="tit-txt">报名批次信息</span>
          </div>
          <el-form label-width="auto" class="m-text-form f-pt10 f-pb5">
            <el-col :span="8">
              <el-form-item label="购买人：">{{ refundDetailList.name || '-' }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="帐号：">{{ refundDetailList.idCard || '-' }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报名批次号：">
                <a @click="viewDetail(refundDetailList.batchOrderNo)" class="f-cb f-link f-underline">{{
                  refundDetailList.batchOrderNo || '-'
                }}</a>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="退款人次：">{{ refundDetailList.refoundCount || 0 }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="交易流水号：">{{ refundDetailList.flowNo || '-' }}</el-form-item>
            </el-col>
          </el-form>
          <el-table :data="returnOrderResponseVo" max-height="500px" class="m-table" ref="subOrderTable">
            <el-table-column type="index" label="No." width="60" align="center"
              ><template slot-scope="scope"
                ><span
                  v-observe-visibility="
                    scope.$index === returnOrderResponseVo.length - 1
                      ? (isVisible, entry) => {
                          lastSubOrderVisible(isVisible, scope.$index)
                        }
                      : null
                  "
                  >{{ scope.$index + 1 }}</span
                ></template
              ></el-table-column
            >
            <el-table-column label="订单号" min-width="300">
              <template slot-scope="scope">
                <div>
                  <p>
                    <span
                      @click="toOrderView(scope.row.subOrderInfo.orderInfo.orderNo)"
                      class="f-link f-underline f-cb"
                    >
                      {{ scope.row.subOrderInfo.orderInfo.orderNo }}</span
                    >
                  </p>
                  <el-tag type="danger" size="small" v-if="scope.row.subOrderInfo.exchanged">换班</el-tag>
                  <el-tag type="warning" size="small" v-if="scope.row.subOrderInfo.isExchangeIssue">换期</el-tag>

                  <el-tag type="success" size="small" v-if="scope.row.subOrderInfo.saleChannel">专题</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="物品名称" min-width="300">
              <template slot-scope="scope">
                <p>{{ scope.row.returnCommodity.commoditySku.saleTitle }}</p>
                <p class="f-c9 f-f13">所属方案：{{ scope.row.returnCommodity.commoditySku.resource.schemeName }}</p>
                <p v-if="scope.row.returnCommodity.commoditySku.issueInfo" class="f-c9 f-f13">
                  所属期别：{{ scope.row.returnCommodity.commoditySku.issueInfo.issueName }}
                </p>
              </template>
            </el-table-column>
            <el-table-column label="学时" min-width="120" align="center">
              <template slot-scope="scope">{{
                scope.row.returnCommodity.commoditySku.resource.period || '-'
              }}</template>
            </el-table-column>
            <el-table-column label="数量" min-width="120" prop="returnCommodity.quantity" align="center">
            </el-table-column>
            <el-table-column label="单价(元)" min-width="150" align="right">
              <template slot-scope="scope">{{
                scope.row.returnCommodity.commoditySku.price / scope.row.returnCommodity.quantity
              }}</template>
            </el-table-column>
            <el-table-column
              label="实付金额(元)"
              min-width="150"
              prop="subOrderInfo.orderInfo.orderPaymentInfo.payAmount"
              align="right"
            >
              <template slot-scope="scope">
                {{ scope.row.returnCommodity.commoditySku.price }}
              </template>
            </el-table-column>
            <el-table-column label="状态" min-width="150">
              <template slot-scope="scope">
                <div v-if="scope.row.basicData.returnOrderStatus === 0">
                  <el-badge is-dot type="primary" class="badge-status">退款审批中</el-badge>
                </div>
                <div
                  v-else-if="
                    scope.row.basicData.returnOrderStatus === 11 && scope.row.basicData.returnCloseReason.closeType == 3
                  "
                >
                  <el-badge is-dot type="primary" class="badge-status">退款被拒绝</el-badge>
                </div>
                <div v-else-if="scope.row.basicData.returnOrderStatus === 2">
                  <el-badge is-dot type="danger" class="badge-status">资源回收中</el-badge>
                </div>
                <div v-else-if="scope.row.basicData.returnOrderStatus === 3">
                  <el-badge is-dot type="danger" class="badge-status">资源回收失败</el-badge>
                </div>
                <div
                  v-else-if="
                    scope.row.basicData.returnOrderStatus === 6 ||
                    scope.row.basicData.returnOrderStatus === 5 ||
                    scope.row.basicData.returnOrderStatus === 4
                  "
                >
                  <el-badge is-dot type="danger" class="badge-status">退款处理中</el-badge>
                </div>
                <div v-else-if="scope.row.basicData.returnOrderStatus === 7">
                  <el-badge is-dot type="danger" class="badge-status">退款失败</el-badge>
                </div>
                <div v-else-if="scope.row.basicData.returnOrderStatus === 8">
                  <el-badge v-if="returnSubOrderStatus === '退款成功'" is-dot type="success" class="badge-status">{{
                    returnSubOrderStatus
                  }}</el-badge>
                  <el-badge v-if="returnSubOrderStatus === '退款处理中'" is-dot type="primary" class="badge-status">{{
                    returnSubOrderStatus
                  }}</el-badge>
                </div>
                <div
                  v-else-if="
                    scope.row.basicData.returnOrderStatus === 9 || scope.row.basicData.returnOrderStatus === 10
                  "
                >
                  <el-badge is-dot type="success" class="badge-status">退款成功</el-badge>
                </div>
                <div
                  v-else-if="
                    scope.row.basicData.returnOrderStatus === 11 &&
                    (scope.row.basicData.returnCloseReason.closeType === 1 ||
                      scope.row.basicData.returnCloseReason.closeType === 2)
                  "
                >
                  <el-badge is-dot type="danger" class="badge-status">退款已取消</el-badge>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="toDetail(scope.row.returnOrderNo)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-drawer title="取消退款申请" :visible.sync="cancelRefundDialog" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              确认取消该订单的退款申请？取消后需要重新发起退款！
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="取消原因：" required>
                    <el-input type="textarea" :rows="6" v-model="cancelReason" placeholder="请输入取消原因" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button @click="cancelRefundDialog = false">取消</el-button>
                    <el-button type="primary" @click="cancelRefund">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <el-drawer title="拒绝退款申请" :visible.sync="refuseRefundDialog" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="拒绝退款原因：" required>
                    <el-input type="textarea" :rows="6" v-model="cancelReason" placeholder="请输入拒绝退款原因" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button @click="refuseRefundDialog = false">取消</el-button>
                    <el-button type="primary" @click="refuseRefund">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import QueryRefundList from '@api/service/management/trade/single/order/query/QueryRefundList'
  import {
    ReturnCommodityResponse,
    TradeStatisticDateHistogramBucketResponse
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { RefundOrderStatusEnum } from '@api/service/common/trade/RefundOrderStatusEnum'
  import ReturnOrderResponseVo from '@api/service/management/trade/single/order/query/vo/ReturnOrderResponseVo'
  import MutationBatchOrderRefund from '@api/service/management/trade/batch/order/mutation/MutationBatchOrderRefund'
  import QueryBatchRefundList from '@api/service/management/trade/batch/order/query/QueryBatchRefund'
  import BatchRefoundDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchRefoundDetailVo'
  import QueryBatchOrderDetail from '@api/service/management/trade/batch/order/query/QueryBatchOrderDetail'
  import QueryBatchOrderMainOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderMainOrderListVo'
  import BatchOrderMainOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderListDetailVo'
  import BatchOrderMainOrderListStatisticVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderListStatisticVo'
  import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
  import {
    BatchRefundTradeStatusEnum,
    BatchRefundTradeTypeEnum,
    BatchRefundTradeWayEnum
  } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
  import { ElTable } from 'element-ui/types/table'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import { bind, debounce } from 'lodash-decorators'

  @Component
  export default class extends Vue {
    @Ref('subOrderTable')
    subOrderTable: ElTable
    tableData: Array<ReturnCommodityResponse> = new Array<ReturnCommodityResponse>()
    cancelRefundDialog = false //取消退款弹窗标识
    refuseRefundDialog = false //拒绝退款弹窗标识
    // 页面分页控件
    page: UiPage
    // 分页查询
    query: Query = new Query()
    form = {
      data1: ''
    }
    BatchRefundTradeStatusEnum = BatchRefundTradeStatusEnum

    // 退款状态
    statusMapType = {
      [BatchRefundTradeStatusEnum.REFUNDING]: '退款审批中',
      [BatchRefundTradeStatusEnum.REFUNDDISPOSE]: '退款处理中',
      [BatchRefundTradeStatusEnum.REFUSEDREFUND]: '退款拒绝',
      [BatchRefundTradeStatusEnum.CANCELREFUND]: '退款取消',
      [BatchRefundTradeStatusEnum.REFUNDSUCCESS]: '退款成功',
      [BatchRefundTradeStatusEnum.REFUNDFAIL]: '退款失败'
    }
    cancelReason = ''
    //接口请求
    // queryRefundOrderDetail: QueryRefundList = TradeModule.singleTradeBatchFactor.orderFactor.getQueryRefundOrderDetail()
    //查询接口请求
    queryBatchRefound: QueryBatchRefundList =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchRefound
    //查询批次信息请求
    queryBatchOrderDetail: QueryBatchOrderDetail =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchOrderDetail
    queryParams: QueryBatchOrderMainOrderListVo = new QueryBatchOrderMainOrderListVo()
    batchOrderMainOrderListDetailVo: Array<BatchOrderMainOrderListDetailVo> =
      new Array<BatchOrderMainOrderListDetailVo>()
    batchOrderMainOrderListStatisticVo: BatchOrderMainOrderListStatisticVo = new BatchOrderMainOrderListStatisticVo()
    //业务接口请求
    mutationBatchOrderRefund: MutationBatchOrderRefund = new MutationBatchOrderRefund()
    //退款单详情对象
    refundList: ReturnOrderResponseVo = new ReturnOrderResponseVo()
    refundDetailList: BatchRefoundDetailVo = new BatchRefoundDetailVo()
    batchOrderNo = ''
    //查询接口请求
    queryRefundOrder: QueryRefundList = new QueryRefundList()
    //接口查询参数
    returnOrderRequestVo: ReturnOrderRequestVo = new ReturnOrderRequestVo()
    //查询接口结果
    returnOrderResponseVo: Array<ReturnOrderResponseVo> = new Array<ReturnOrderResponseVo>()

    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    // 判断当前登录角色是否是专题管理员

    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    // 当子单退款状态为退货成功需要特殊判断
    get returnSubOrderStatus() {
      if ([10, 11].includes(this.refundDetailList.refoundStatus)) {
        return '退款成功'
      }
      if ([5, 6, 7].includes(this.refundDetailList.refoundStatus)) {
        return '退款处理中'
      } else if (
        this.refundDetailList.refoundType === BatchRefundTradeTypeEnum.RETURN_REFUND &&
        this.refundDetailList.refoundWay === BatchRefundTradeWayEnum.ONLINEREFUND
      ) {
        return '退款处理中'
      } else {
        return '退款成功'
      }
    }

    /*
     * 跳转订单详情页
     * */
    viewDetail(id: string) {
      this.$router.push('/training/trade/order/collective/detail/' + id)
    }
    toOrderView(subId: string) {
      this.$router.push(
        '/training/trade/order/collective/sub-detail/' + this.refundDetailList.batchOrderNo + '/' + subId
      )
    }
    /*
     * 取消退款弹窗
     * */
    openCancelRefund() {
      this.cancelReason = ''
      this.cancelRefundDialog = true
    }
    /*
     * 拒绝退款弹窗
     * */
    openRefuseRefund() {
      this.cancelReason = ''
      this.refuseRefundDialog = true
    }
    async created() {
      this.batchOrderNo = this.$route.params.id
      await this.geteRfundOrderDetail()
      // await this.getRefundNum()
      await this.getBatchInformation()
    }
    /*
     * 退款详情
     * */
    async geteRfundOrderDetail() {
      try {
        if (this.isFxlogin && this.isHadFxAbility) {
          this.refundDetailList = await this.queryBatchRefound.queryFxBatchRefoundDetail(this.batchOrderNo)
        } else {
          this.refundDetailList = await this.queryBatchRefound.queryBatchRefoundDetail(this.batchOrderNo)
        }
        await this.refundDetailList.fillRecords()
      } catch (e) {
        console.log(e, '加载个人报名退款订单详情页失败')
      } finally {
        console.log(this.refundDetailList, 'this.refundDetailList')
      }
    }
    constructor() {
      super()
      this.page = new UiPage(this.getBatchInformation, this.getBatchInformation)
      this.page.pageSize = 10
    }
    /*
     * 报名批次信息
     * */
    async getBatchInformation() {
      try {
        this.returnOrderRequestVo.basicData.applySourceType = 'BATCH_RETURN_ORDER'
        this.returnOrderRequestVo.basicData.applySourceIdList = [this.refundDetailList.refoundNo]
        if (this.isHadFxAbility && this.isFxlogin) {
          this.returnOrderResponseVo = await this.queryRefundOrder.queryFxRefundOrderList(
            this.page,
            this.returnOrderRequestVo
          )
        } else {
          this.returnOrderResponseVo = await this.queryRefundOrder.queryRefundOrderList(
            this.page,
            this.returnOrderRequestVo
          )
        }
      } catch (e) {
        console.log(e, '获取批次单信息失败')
      } finally {
        console.log('this.returnOrderResponseVo*****', this.returnOrderResponseVo)
      }
    }
    /*
     * 报名批次信息（滚载）
     * */
    async getPollingBatchInformation() {
      try {
        this.page.pageNo += 1
        this.returnOrderRequestVo.basicData.applySourceType = 'BATCH_RETURN_ORDER'
        this.returnOrderRequestVo.basicData.applySourceIdList = [this.refundDetailList.refoundNo]
        const res = await this.queryRefundOrder.queryRefundOrderList(this.page, this.returnOrderRequestVo)
        this.returnOrderResponseVo.push(...res)
        this.subOrderTable.doLayout()
      } catch (e) {
        console.log(e, '获取批次单信息失败')
      } finally {
        console.log(this.returnOrderResponseVo, '---this.returnOrderResponseVo+++')
      }
    }
    /*
     * 获取退款人次
     * */
    // async getRefundNum() {
    //   try {
    //     this.batchOrderMainOrderListStatisticVo = await this.queryBatchOrderDetail.queryMainOrderListStatistic(
    //       this.queryParams
    //     )
    //   } catch (e) {
    //     console.log(e, '获取退款人次失败')
    //   } finally {
    //     222
    //   }
    // }
    /*
     * 取消退款
     * */
    @bind
    @debounce(200)
    async cancelRefund() {
      if (!this.cancelReason) {
        this.$message.error('请填写取消退款原因')
        return
      }

      const status = await this.mutationBatchOrderRefund.cancelRefund(
        this.refundDetailList.refoundNo,
        this.cancelReason
      )
      if (status?.code == '200') {
        this.cancelRefundDialog = false
        this.$message.success('取消退款成功')
        const timeOut = setTimeout(async () => {
          await this.geteRfundOrderDetail()
          clearTimeout(timeOut)
        }, 1000)
      } else {
        this.$message.error('该批次退款申请正在处理中，暂无法执行其他操作。')
        return
      }
    }
    /*
     * 拒绝退款
     * */
    @bind
    @debounce(200)
    async refuseRefund() {
      if (!this.cancelReason) {
        this.$message.error('请填写拒绝退款原因')
        return
      }

      const status = await this.mutationBatchOrderRefund.refuseRefund(
        this.refundDetailList.refoundNo,
        this.cancelReason
      )
      if (status?.code == '200') {
        this.refuseRefundDialog = false
        this.$message.success('拒绝退款成功')
        const timeOut = setTimeout(async () => {
          await this.geteRfundOrderDetail()
          clearTimeout(timeOut)
        }, 1000)
      } else {
        this.$message.error('该批次退款申请正在处理中，暂无法执行其他操作。')
        return
      }
    }
    /**
     * 判断是否是威富通以及线下
     */
    isWft() {
      if (
        this.refundDetailList.flowNo.includes('WFT') &&
        this.refundDetailList.refoundWay == BatchRefundTradeWayEnum.ONLINEREFUND
      ) {
        return true
      } else {
        return false
      }
    }

    /**
     * 威富通退款点击事件
     * @param item
     */
    wftDialog() {
      this.$alert('兴业聚合支付（威富通）线上退款到账需要1~3个工作日，确认退款吗?', '提示', {
        confirmButtonText: '确认',
        type: 'warning'
      }).then(() => {
        this.agreeRefund()
      })
    }
    /*
     * 同意退款
     * */
    async agreeRefund() {
      const status = await this.mutationBatchOrderRefund.agreeRefund(this.refundDetailList.refoundNo)
      if (status?.code === '200') {
        this.$message.success('同意退款成功')
        this.query.loading = false
        const timeOut = setTimeout(async () => {
          await this.geteRfundOrderDetail()
          clearTimeout(timeOut)
        }, 1000)
      } else {
        this.query.loading = false
        this.$message.error('该批次退款申请正在处理中，暂无法执行其他操作。')
        return
      }
    }
    /*
     * 确认退款
     * */
    async confirmRefund() {
      const status = await this.mutationBatchOrderRefund.confirmRefund(this.refundDetailList.refoundNo)
      if (status?.code === '200') {
        this.$message.success('退款处理中')
        const timeOut = setTimeout(async () => {
          await this.geteRfundOrderDetail()
          clearTimeout(timeOut)
        }, 1000)
      } else {
        this.$message.error('退款处理失败')
        return
      }
    }
    /*
     * 继续退款
     * */
    async continueRefund() {
      const status = await this.mutationBatchOrderRefund.retryRefund(this.refundDetailList.refoundNo)
      if (status?.code === 200) {
        this.$message.success('继续退款成功')
        const timeOut = setTimeout(async () => {
          await this.geteRfundOrderDetail()
          clearTimeout(timeOut)
        }, 1000)
      } else {
        this.$message.error('继续退款失败')
        return
      }
    }
    /*
     *  重新回收做资源
     * */
    async retryRecycleRefund() {
      const status = await this.mutationBatchOrderRefund.againRecyclingbatchReturnOrderNo(
        this.refundDetailList.refoundNo
      )
      if (status?.code === 200) {
        this.$message.success('系统已重新发起回收资源！')
        const timeOut = setTimeout(async () => {
          await this.geteRfundOrderDetail()
          clearTimeout(timeOut)
        }, 1000)
      } else {
        this.$message.error('重新回收做资源失败')
        return
      }
    }
    /**
     * 查看子单
     */
    toDetail(subId: string) {
      this.$router.push('/training/trade/refund/collective/sub-detail/' + this.batchOrderNo + '/' + subId)
    }

    lastSubOrderVisible(isVisible: boolean, index: number) {
      if (isVisible && index === this.returnOrderResponseVo.length - 1) {
        if (this.page.pageNo >= this.page.totalPageSize) {
          return
        }
        this.getPollingBatchInformation()
      }
    }

    async load() {
      // await this.getBatchInformation()
      22
    }

    async activated() {
      await this.geteRfundOrderDetail()
      await this.getBatchInformation()
    }
  }
</script>

<style scoped></style>
