/**
 * 身份证枚举
 */
import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum IdentityCardType {
  /**
   * 身份证
   */
  ID_CARD = 'ID_CARD',

  /**
   * 护照
   */
  PASSPORT = 'PASSPORT',

  /**
   * 军官证
   */
  OFFICER_CARD = 'OFFICER_CARD',

  /**
   * 外国人永久居留证
   */
  FOREIGNER_RESIDENCE_CARD = 'FOREIGNER_RESIDENCE_CARD',

  /**
   * 港澳居民来往内地通行证
   */
  HK_FOREIGNER_RESIDENCE_CARD = 'HK_FOREIGNER_RESIDENCE_CARD',
  /**
   * 台湾居民来往大陆通行证
   */
  TAIWAN_FOREIGNER_RESIDENCE_CARD = 'TAIWAN_FOREIGNER_RESIDENCE_CARD',
  /**
   * 台湾居民身份证
   */
  TAIWAN_RESIDENCE_CARD = 'TAIWAN_RESIDENCE_CARD',

  /**
   * 港澳居民居住证
   */
  HK_RESIDENCE_CARD = 'HK_RESIDENCE_CARD'
}

export default class IdentityCardTypeEnum extends AbstractEnum<IdentityCardType> {
  static enum = IdentityCardType

  constructor(status?: IdentityCardType) {
    super()
    this.current = status
    this.map.set(IdentityCardType.ID_CARD, '身份证')
    this.map.set(IdentityCardType.PASSPORT, '护照')
    this.map.set(IdentityCardType.OFFICER_CARD, '军官证')
    this.map.set(IdentityCardType.FOREIGNER_RESIDENCE_CARD, '外国人永久居留证')
    this.map.set(IdentityCardType.HK_FOREIGNER_RESIDENCE_CARD, '港澳居民来往内地通行证')
    this.map.set(IdentityCardType.TAIWAN_FOREIGNER_RESIDENCE_CARD, '台湾居民来往大陆通行证')
    this.map.set(IdentityCardType.TAIWAN_RESIDENCE_CARD, '台湾居民身份证')
    this.map.set(IdentityCardType.HK_RESIDENCE_CARD, '港澳居民居住证')
  }
}
