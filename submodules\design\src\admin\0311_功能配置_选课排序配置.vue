<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="增值税电子普遍发票（自动开票）" name="first">
        <div class="f-p15">
          <el-card shadow="never" class="m-card">
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="规则名称" min-width="240">
                <template>按指定课件供应商的课程数量排（前20门课程）</template>
              </el-table-column>
              <el-table-column label="是否乱序" min-width="100">
                <template>是</template>
              </el-table-column>
              <el-table-column label="排序更新周期" min-width="150">
                <template>每天00:00</template>
              </el-table-column>
              <el-table-column label="使用范围" min-width="180">
                <template>全平台</template>
              </el-table-column>
              <el-table-column label="状态" min-width="120">
                <template>启用</template>
              </el-table-column>
              <el-table-column label="编辑" width="160" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">查看</el-button>
                  <el-button type="text" size="mini">编辑</el-button>
                  <el-button type="text" size="mini">禁用</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="培训证明" name="second">
        <div class="f-p15">
          <el-tabs v-model="activeName2" type="card" class="m-tab-card">
            <el-tab-pane label="培训证明" name="first">
              <el-card shadow="never" class="m-card f-mb15">
                <el-alert type="warning" :closable="false" class="m-alert">
                  系统当前已配置 <span class="f-fb">2</span> 个培训证明模板
                </el-alert>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="模板名称" min-width="200" fixed="left">
                    <template>培训证明（显示培训科目）</template>
                  </el-table-column>
                  <el-table-column label="模板说明" min-width="200">
                    <template>模板显示培训科目、培训专业</template>
                  </el-table-column>
                  <el-table-column label="所属行业" min-width="100">
                    <template>人社</template>
                  </el-table-column>
                  <el-table-column label="使用范围" min-width="140">
                    <template>全网校</template>
                  </el-table-column>
                  <el-table-column label="适用培训方案形式" min-width="150">
                    <template>培训班学习</template>
                  </el-table-column>
                  <el-table-column label="是否应用电子章配置" min-width="170" align="center">
                    <template>是</template>
                  </el-table-column>
                  <el-table-column label="创建时间" min-width="170">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="操作" width="120" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">预览</el-button>
                      <el-button type="text" size="mini">下载</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-card>
            </el-tab-pane>
            <el-tab-pane label="培训证明电子章配置" name="second">
              <el-card shadow="never" class="m-card f-mb15">
                <el-row type="flex" justify="center" class="width-limit">
                  <el-col :md="20" :lg="16" :xl="13">
                    <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                      <el-form-item label="落款名称：">
                        <el-input v-model="form.name1" clearable class="form-l" />
                      </el-form-item>
                      <el-form-item label="培训证明盖章：">
                        <el-upload action="#" list-type="picture-card" :auto-upload="false" class="m-pic-upload">
                          <div slot="default" class="upload-placeholder">
                            <i class="el-icon-plus"></i>
                            <p class="txt">上传电子章</p>
                          </div>
                          <div slot="file" slot-scope="{ file }" class="img-file">
                            <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                            <div class="el-upload-list__item-actions">
                              <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                <i class="el-icon-zoom-in"></i>
                              </span>
                              <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                                <i class="el-icon-delete"></i>
                              </span>
                            </div>
                          </div>
                          <div slot="tip" class="el-upload__tip">
                            <i class="el-icon-warning"></i>
                            <span class="txt">
                              上传培训证明盖章（透明底色），先设计好后上传，建议尺寸：宽度140px * 高度140px。
                            </span>
                          </div>
                        </el-upload>
                        <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                          <img :src="dialogImageUrl" alt="" />
                        </el-dialog>
                      </el-form-item>
                      <el-form-item class="m-btn-bar">
                        <el-button>取消</el-button>
                        <el-button type="primary">保存</el-button>
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }],
        form: {
          name: '',
          name1: '网校名称',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
