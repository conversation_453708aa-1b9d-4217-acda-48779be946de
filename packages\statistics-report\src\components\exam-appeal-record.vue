<!-- 考试申诉记录 -->
<template>
  <!-- 考试申诉记录 -->
  <div class="f-p20">
    <el-table stripe :data="tableData" max-height="500" highlight-current-row class="m-table">
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="申诉来源" min-width="240">
        <template>
          <div class="f-fb">考试名称：选修必修选修必修</div>
          <div>进考时间：2021-07-12 18:32:58</div>
        </template>
      </el-table-column>
      <el-table-column label="所属班级" min-width="240">
        <template>所属班级所属班级所属班级所属班级所属班级所属班级</template>
      </el-table-column>
      <el-table-column label="申诉内容" min-width="150">
        <template>申诉内容申诉内容申诉内容申诉内容申诉内容</template>
      </el-table-column>
      <el-table-column label="申诉时间" min-width="200">
        <template>2017-12-12 17:23:23</template>
      </el-table-column>
      <el-table-column label="申诉状态" min-width="140" align="center">
        <template slot-scope="scope">
          <div v-if="scope.$index === 0">
            <el-badge is-dot type="danger" class="badge-status">审批不通过</el-badge>
          </div>
          <div v-else>
            <el-badge is-dot type="success" class="badge-status">审批通过</el-badge>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    // 考试申诉记录
    @Prop({
      type: Object,
      default: () => {
        return {}
      }
    })
    appealRecord: object

    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]

    activeName = 'first'
  }
</script>
