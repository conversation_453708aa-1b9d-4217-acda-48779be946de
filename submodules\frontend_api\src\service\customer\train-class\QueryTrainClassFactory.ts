import QueryCollectivePaymentTrainClassCommodityList from '@api/service/customer/train-class/query/QueryCollectivePaymentTrainClassCommodityList'
import QueryMyTrainClassDetailByOrder from '@api/service/customer/train-class/query/QueryMyTrainClassDetailByOrder'
import QueryMyTrainClassDetail from '@api/service/customer/train-class/query/QueryMyTrainClassDetail'
import QueryMyTrainClassCommodityList from '@api/service/customer/train-class/query/QueryMyTrainClassCommodityList'
import QueryTrainClassDetail from '@api/service/customer/train-class/query/QueryTrainClassDetail'
import QueryTrainClassCommodityList from '@api/service/customer/train-class/query/QueryTrainClassCommodityList'
import { QueryClassStatue } from './query/QueryClassStatue'

/**
 * 培训班查询工厂类
 */
class QueryTrainClassFactory {
  // region properties
  // endregion
  // region methods
  /**
   *获取获取集体缴费培训班列表
   */
  getQueryCollectivePaymentTrainClassCommodityList() {
    return new QueryCollectivePaymentTrainClassCommodityList()
  }
  /**
   *获取用户域根据子订单号获取我的培训班详情
   */
  getQueryMyTrainClassDetailByOrder() {
    return new QueryMyTrainClassDetailByOrder()
  }
  /**
   *获取用户域获取我的培训班详情
   */
  getQueryMyTrainClassDetail() {
    return new QueryMyTrainClassDetail()
  }
  /**
   *获取用户域获取我的培训班商品列表
   */
  getQueryMyTrainClassCommodityList() {
    return new QueryMyTrainClassCommodityList()
  }
  /**
   *获取用户域获取培训班商品详情
   */
  getQueryTrainClassDetail() {
    return new QueryTrainClassDetail()
  }
  /**
   *获取用户域获取培训班商品列表
   */
  getQueryTrainClassCommodityList() {
    return new QueryTrainClassCommodityList()
  }
  /**
   *获取校验培训班状态类
   */
  getQueryClassStatue() {
    return new QueryClassStatue()
  }
  // endregion
}
export default QueryTrainClassFactory
