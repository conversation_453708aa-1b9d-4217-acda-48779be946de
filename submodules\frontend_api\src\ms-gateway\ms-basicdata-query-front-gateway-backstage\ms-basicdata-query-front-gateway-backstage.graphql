"""独立部署的微服务,K8S服务名:ms-basicdata-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""功能描述：项目级-根据code和业务数据字典类型查询单个业务数据字典信息接口-明细接口
		描述：根据code和业务数据字典类型查询当前子项目下的业务数据字典信息
		@param request :业务数据字典查询条件
		@return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse
		@date : 2022年8月12日 15:56:50
	"""
	getBusinessDataDictionaryInSubProject(request:BusinessDataDictionaryCodeRequest):BusinessDataDictionaryResponse @optionalLogin
	"""功能描述：项目级-根据id查询单个证书类型字典信息接口-明细接口
		描述：根据id查询当前子项目下的证书类型信息
		@param certificateCategoryId :证书类型id
		@return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateCategoryResponse
		@date : 2022年8月12日 14:08:53
	"""
	getCertificateCategoryInSubProject(certificateCategoryId:String):CertificateCategoryResponse @optionalLogin
	"""功能描述：项目级-根据id查询单个证书等级字典信息-明细接口
		描述：查询当前子项目下的证书等级信息
		@param certificateLevelId :证书等级id
		@return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateLevelResponse
		@date : 2022年8月12日 14:08:53
	"""
	getCertificateLevelInSubProject(certificateLevelId:String):CertificateLevelResponse @optionalLogin
	"""功能描述：项目级-根据id查询单个证书专业字典信息-明细接口
		描述：查询当前子项目下的证书专业信息
		@param certificateMajorId :证书专业id
		@return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateMajorResponse
		@date : 2022年9月27日 14:08:53
	"""
	getCertificateMajorInSubProject(certificateMajorId:String):CertificateMajorResponse @optionalLogin
	"""功能描述：项目级-根据id查询单个证书类别字典信息-明细接口
		描述：查询当前子项目下的证书类别信息
		<AUTHOR>
		@Date 2022/9/27 14:38
		@param certificateTypeId :证书类别id
		@return com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateTypeResponse
	"""
	getCertificateTypeInSubProject(certificateTypeId:String):CertificateTypeResponse @optionalLogin
	"""功能描述：项目级-根据工种id查询单个工种字典信息-明细接口
		描述：根据工种id查询当前子项目下的工种信息
		@param id :工种id
		@return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.JobCategoryResponse
		@date : 2022年8月12日 15:56:50
	"""
	getJobCategoryInSubProject(id:String):JobCategoryResponse @optionalLogin
	"""功能描述：项目级-根据地区编码查询单个地区字典信息-明细接口
		描述：根据地区编码查询当前子项目下的地区信息
		@param request : 地区查询条件
		@return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.RegionResponse
		@date : 2022年10月19日18:32:12
	"""
	getRegionInSubProject(request:RegionCodeRequest):RegionResponse @optionalLogin
	"""功能描述：项目级-所有地区-列表接口
		描述：查询当前子项目下的指定地区字典业务配置id的所有地区信息，默认按排序字段升序排
		@param businessId : 地区字典业务配置id
		@return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.RegionResponse>
	"""
	listAllRegionInSubProject(businessId:String):[RegionResponse] @optionalLogin
	"""功能描述：项目级-根据id集合查询字典信息-列表接口
		描述：查询指定id的字典列表，默认按排序字段升序排
		@param idList :字典id集合
		@return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse>
		@date : 2024年1月24日 09:35:49
	"""
	listBusinessDataDictionaryByIdInSubProject(idList:[String]):[BusinessDataDictionaryResponse] @optionalLogin
	"""功能描述：项目级-查询指定业务数据字典类型的字典信息-列表接口
		描述：查询指定业务数据字典类型的字典列表，默认按排序字段升序排
		@param request :业务数据字典查询条件
		@return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse>
		@date : 2022年8月12日 17:28:02
	"""
	listBusinessDataDictionaryInSubProject(request:BusinessDataDictionaryRequest):[BusinessDataDictionaryResponse] @optionalLogin
	"""功能描述：项目级-查询下一级地区信息-列表接口
		描述：根据父级地区编码查询当前子项目下的地区信息，默认按排序字段升序排
		@param request : 地区查询条件
		@return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.RegionResponse>
		@date : 2022年10月19日18:32:12
	"""
	listChildRegionInSubProject(request:RegionCodeRequest):[RegionResponse] @optionalLogin
	"""功能描述：项目级-根据地区编码集合查询地区信息-列表接口
		描述：根据地区编码集合查询当前子项目下的地区信息集合，默认按排序字段升序排
		@param request : 地区集合查询条件
		@return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.RegionResponse>
		@date : 2022年10月19日18:32:12
	"""
	listRegionByCodeInSubProject(request:RegionCodeListRequest):[RegionResponse] @optionalLogin
	"""功能描述：项目级-根据地区编码查询指定级别的地区信息-列表接口
		描述：根据地区编码查询指定级别的地区信息，默认按排序字段升序排
		@param request : 地区查询条件
		@return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.RegionResponse>
		@date : 2022年9月22日 20:00:00
	"""
	listRegionInSubProject(request:RegionRequest):[RegionResponse] @optionalLogin
	"""功能描述：项目级-查询指定业务数据字典类型的字典信息-分页列表接口
		描述：查询指定业务数据字典类型的字典分页列表，默认按排序字段升序排
		@param page    :分页对象
		@param request :业务数据字典查询条件
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse>
		@date : 2022年8月12日 17:28:02
	"""
	pageBusinessDataDictionaryInSubProject(page:Page,request:BusinessDataDictionaryRequest):BusinessDataDictionaryResponsePage @page(for:"BusinessDataDictionaryResponse") @optionalLogin
	"""功能描述：项目级-查询证书类型字典信息-分页列表接口
		描述：查询当前子项目下的证书类型信息，默认按排序字段升序排
		@param page    :分页对象
		@param request :证书类型查询条件
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateCategoryResponse>
		@date : 2022年8月12日 14:08:53
	"""
	pageCertificateCategoryInSubProject(page:Page,request:CertificateCategoryRequest):CertificateCategoryResponsePage @page(for:"CertificateCategoryResponse") @optionalLogin
	"""功能描述：项目级-查询证书等级字典信息-分页列表接口
		描述：查询当前子项目下的证书等级信息，默认按排序字段升序排
		@param page    :分页对象
		@param request :证书等级查询条件
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateLevelResponse>
		@date : 2022年8月12日 14:08:53
	"""
	pageCertificateLevelInSubProject(page:Page,request:CertificateLevelRequest):CertificateLevelResponsePage @page(for:"CertificateLevelResponse") @optionalLogin
	"""功能描述：项目级-查询证书专业字典信息-分页列表接口
		描述：查询当前子项目下的证书专业信息，默认按排序字段升序排
		@param page    :分页对象
		@param request :证书专业查询条件
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateMajorResponse>
		@date : 2022年9月27日 14:08:53
	"""
	pageCertificateMajorInSubProject(page:Page,request:CertificateMajorRequest):CertificateMajorResponsePage @page(for:"CertificateMajorResponse") @optionalLogin
	"""功能描述：项目级-查询证书类别字典信息-分页列表接口
		描述：查询当前子项目下的证书类别信息，默认按排序字段升序排
		<AUTHOR>
		@Date 2022/9/27 14:30
		@param page    :分页对象
		@param request :证书类别查询条件
		@return com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.CertificateTypeResponse>
	"""
	pageCertificateTypeInSubProject(page:Page,request:CertificateTypeRequest):CertificateTypeResponsePage @page(for:"CertificateTypeResponse") @optionalLogin
	"""功能描述：项目级-查询工种字典信息-分页列表接口
		描述：查询当前子项目下的工种信息，默认按排序字段升序排
		@param page    :分页对象
		@param request :工种查询条件
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.JobCategoryResponse>
		@date : 2022年8月12日 14:08:53
	"""
	pageJobCategoryInSubProject(page:Page,request:JobCategoryRequest):JobCategoryResponsePage @page(for:"JobCategoryResponse") @optionalLogin
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""@Description 获取单个业务数据字典信息的查询
	<AUTHOR>
	@Date 2022/9/27 9:33
	@Version 1.0
"""
input BusinessDataDictionaryCodeRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.BusinessDataDictionaryCodeRequest") {
	"""字典编码"""
	code:Int
	"""业务数据字典类型（必填项）
		DEGREE("学位", "DEGREE"),
		EDUCATION_BACKGROUND("学历", "EDUCATION_BACKGROUND"),
		ENTERPRISE_ECONOMIC_TYPE("企业经济类型", "ENTERPRISE_ECONOMIC_TYPE"),
		ENTERPRISE_TYPE("企业类型", "ENTERPRISE_TYPE"),
		EXECUTIVES_UNIT_TYPE("主管单位类型", "EXECUTIVES_UNIT_TYPE"),
		GENDER("性别", "GENDER"),
		HOUSEHOLD_REGISTRATION_TYPE("户口性质", "HOUSEHOLD_REGISTRATION_TYPE"),
		ID_CARD_TYPE("证件类型", "ID_CARD_TYPE"),
		INDUSTRY_EXECUTIVES_TYPE("行业主管类型", "INDUSTRY_EXECUTIVES_TYPE"),
		INDUSTRY_TYPE("行业类型", "INDUSTRY_TYPE"),
		NATIONALITY("民族", "NATIONALITY"),
		PERSON_TYPE("人员类型", "PERSON_TYPE"),
		PERSON_TYPE_GROUP("人员类型分组", "PERSON_TYPE_GROUP"),
		POLITICS_STATUS("政治面貌", "POLITICS_STATUS"),
		USER_REGISTER_TYPE("注册方式", "USER_REGISTER_TYPE"),
		USER_SOURCE_TYPE("注册来源", "USER_SOURCE_TYPE");
		@see BusinessDataDictionaryTypeEnum
	"""
	businessDataDictionaryType:String
}
"""业务数据字典查询"""
input BusinessDataDictionaryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.BusinessDataDictionaryRequest") {
	"""字典编码"""
	code:Int
	"""字典编码扩展"""
	codeExt:String
	"""业务数据字典id集合"""
	idList:[String]
	"""父级业务数据字典id"""
	parentId:String
	"""业务数据字典类型（必填项）"""
	businessDataDictionaryType:String
	"""业务数据字典业务配置id"""
	businessId:String
	"""字典名称"""
	name:String
	"""字典名称 匹配方式
		1:模糊查询   2:后缀查询   3:前缀查询 0:精确匹配  tips:未传参默认为精确匹配
		(1:*name*   2:*name     3:name*)
	"""
	nameMatchType:Int
	"""是否可用（0：禁用 1：启用）"""
	available:Int
	"""排序规则"""
	sortList:[BusinessDictionarySortKParam]
}
"""证书类型查询"""
input CertificateCategoryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.CertificateCategoryRequest") {
	"""证书类型名称"""
	certificateCategoryName:String
}
"""证书等级查询"""
input CertificateLevelRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.CertificateLevelRequest") {
	"""关联实体id（证书类型id/证书类别id/证书专业id）"""
	relationId:String
	"""证书等级名称"""
	certificateLevelName:String
}
"""@Description 证书专业查询
	<AUTHOR>
	@Date 2022/9/23 14:44
	@Version 1.0
"""
input CertificateMajorRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.CertificateMajorRequest") {
	"""证书专业名称"""
	certificateMajorName:String
}
"""@Description 证书类别查询
	<AUTHOR>
	@Date 2022/9/23 14:44
	@Version 1.0
"""
input CertificateTypeRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.CertificateTypeRequest") {
	"""证书类别名称"""
	certificateTypeName:String
}
"""工种查询"""
input JobCategoryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.JobCategoryRequest") {
	"""工种基本查询条件"""
	jobCategoryBase:JobCategoryBaseRequest
	"""工种排序"""
	sortList:[JobCategorySortRequest]
}
"""@Description 地区code集合查询条件
	<AUTHOR>
	@Date 2022/9/27 10:57
	@Version 1.0
"""
input RegionCodeListRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.RegionCodeListRequest") {
	"""地区字典业务配置id
		@see BusinessDataDictionaryConfigEnum
	"""
	businessId:String
	"""地区编码集合"""
	codeList:[String]
}
"""@Description 地区code查询
	<AUTHOR>
	@Date 2022/9/27 10:44
	@Version 1.0
"""
input RegionCodeRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.RegionCodeRequest") {
	"""地区字典业务配置id
		@see BusinessDataDictionaryConfigEnum
	"""
	businessId:String
	"""地区编码"""
	code:String
}
"""地区查询"""
input RegionRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.RegionRequest") {
	"""地区字典业务配置id
		@see BusinessDataDictionaryConfigEnum
	"""
	businessId:String
	"""地区编码"""
	code:String
	"""级别|1省级 2市级 3区县级"""
	level:Int
}
"""工种基本查询条件"""
input JobCategoryBaseRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.nested.JobCategoryBaseRequest") {
	"""工种id集合"""
	idList:[String]
	"""工种类别id"""
	typeId:String
	"""工种编号"""
	code:String
	"""工种名称"""
	name:String
	"""工种名称 匹配方式"""
	nameMatchType:Int
	"""父级工种id集合"""
	parentIdList:[String]
	"""是否可用（0：禁用 1：启用）"""
	available:Int
	"""工种来源(1：国家职业大典 2：平台自定义)"""
	source:Int
	"""工种字典业务配置id
		@see  BusinessDataDictionaryConfigEnum
	"""
	businessId:String
}
"""功能描述：工种排序
	@Author： wtl
	@Date： 2022年8月12日 15:44:42
"""
input JobCategorySortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.request.dictionary.nested.JobCategorySortRequest") {
	"""工种排序字段"""
	sortField:JobCategorySortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
input BusinessDictionarySortKParam @type(value:"com.fjhb.ms.basicdata.query.kernel.service.param.dictionary.nested.BusinessDictionarySortKParam") {
	"""排序字段"""
	sortField:BusinessDictionarySortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
enum SortTypeEnum @type(value:"com.fjhb.ms.basicdata.enums.SortTypeEnum") {
	ASC
	DESC
}
"""功能描述：业务数据字典信息
	@Author： wtl
	@Date： 2022年8月12日 17:25:23
"""
type BusinessDataDictionaryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.response.dictionary.BusinessDataDictionaryResponse") {
	"""字典id"""
	id:String
	"""字典类型"""
	type:String
	"""父级字典id"""
	parentId:String
	"""字典编码"""
	code:Int
	"""字典编码扩展"""
	codeExt:String
	"""字典名称"""
	name:String
	"""是否可用（0：禁用 1：启用）"""
	available:Int
	"""排序"""
	sort:Int
	"""创建时间"""
	createTime:String
}
"""功能描述：证书类型字典信息
	@Author： wtl
	@Date： 2022年8月12日 11:54:38
"""
type CertificateCategoryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.response.dictionary.CertificateCategoryResponse") {
	"""证书类型id"""
	certificateCategoryId:String
	"""证书类型名称"""
	certificateCategoryName:String
	"""排序"""
	sort:Int!
}
"""功能描述：证书等级字典信息
	@Author： wtl
	@Date： 2022年8月12日 11:54:38
"""
type CertificateLevelResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.response.dictionary.CertificateLevelResponse") {
	"""证书等级id"""
	certificateLevelId:String
	"""证书等级名称"""
	certificateLevelName:String
	"""排序"""
	sort:Int!
}
"""@Description 证书专业字典信息
	<AUTHOR>
	@Date 2022/9/23 14:30
	@Version 1.0
"""
type CertificateMajorResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.response.dictionary.CertificateMajorResponse") {
	"""证书专业id"""
	certificateMajorId:String
	"""证书专业名称"""
	certificateMajorName:String
	"""排序"""
	sort:Int!
}
"""@Description 证书类别字典信息
	<AUTHOR>
	@Date 2022/9/23 14:30
	@Version 1.0
"""
type CertificateTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.response.dictionary.CertificateTypeResponse") {
	"""证书类别id"""
	certificateTypeId:String
	"""证书类别名称"""
	certificateTypeName:String
	"""排序"""
	sort:Int!
}
"""功能描述：工种字典信息
	@Author： wtl
	@Date： 2022年8月12日 11:54:38
"""
type JobCategoryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.response.dictionary.JobCategoryResponse") {
	"""工种id"""
	id:String
	"""工种类别id"""
	typeId:String
	"""工种编号"""
	code:String
	"""工种名称"""
	name:String
	"""父级工种id"""
	parentId:String
	"""工种简介"""
	about:String
	"""工种来源(1：国家职业大典 2：平台自定义)"""
	source:Int
	"""是否可用（0：禁用 1：启用）"""
	available:Int
	"""工种排序"""
	sort:Int
	"""工种创建时间"""
	createTime:DateTime
	"""工种最后修改时间"""
	updateTime:DateTime
}
"""功能描述：地区字典信息
	@Author： wtl
	@Date： 2022年8月12日 11:54:38
"""
type RegionResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.graphql.response.dictionary.RegionResponse") {
	"""地区编码"""
	code:String
	"""父级地区编码"""
	parentCode:String
	"""地区编码路径"""
	codePath:String
	"""地区名称"""
	name:String
	"""级别|1省级 2市级 3区县级"""
	level:Int
	"""地区排序"""
	sort:Int
}
"""业务字典排序字段"""
enum BusinessDictionarySortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.BusinessDictionarySortFieldEnum") {
	"""创建时间"""
	createdTime
	"""排序字段"""
	sort
}
"""功能描述：工种排序字段
	@Author： wtl
	@Date： 2022年8月12日 15:45:15
"""
enum JobCategorySortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mysql.enums.JobCategorySortFieldEnum") {
	"""工种编号字段"""
	code
	"""排序字段"""
	sort
	"""最后修改时间字段"""
	updateTime
	"""是否可用字段"""
	available
	"""工种名称字段"""
	name
}

scalar List
type BusinessDataDictionaryResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BusinessDataDictionaryResponse]}
type CertificateCategoryResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CertificateCategoryResponse]}
type CertificateLevelResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CertificateLevelResponse]}
type CertificateMajorResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CertificateMajorResponse]}
type CertificateTypeResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CertificateTypeResponse]}
type JobCategoryResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [JobCategoryResponse]}
