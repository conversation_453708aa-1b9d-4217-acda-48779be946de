schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""导出学员
		@param queryDTO
		@return
	"""
	exportStudent(queryDTO:StudentQueryDTO):Boolean!
	"""获取管理员分页信息"""
	findAdminPage(page:Page,query:AdministratorQueryDTO):AdministratorDTOPage @page(for:"AdministratorDTO")
	"""获取管理员分页信息
		为渠道商提供
		@param page
		@param query
		@return
	"""
	findAdminPageForChannelVendor(page:Page,query:AdministratorQueryDTO):AdministratorDTOPage @page(for:"AdministratorDTO")
	"""获取管理员分页信息
		为课件供应商提供
		@param page
		@param query
		@return
	"""
	findAdminPageForCoursewareSupplier(page:Page,query:AdministratorQueryDTO):AdministratorDTOPage @page(for:"AdministratorDTO")
	"""获取管理员分页信息
		为机构提供
		@param page
		@param query
		@return
	"""
	findAdminPageForTrainingInstitution(page:Page,query:AdministratorQueryDTO):AdministratorDTOPage @page(for:"AdministratorDTO")
	"""查询单位下的所有学员（含子单位学员）
		@param page
		@param query
		@return
	"""
	findStudentPage(page:Page,query:StudentQueryDTO):StudentItemDTOPage @page(for:"StudentItemDTO")
	"""获取管理员
		@param userId
		@return
	"""
	getAdminInfo(userId:String):AdministratorDTO
	"""获取当前用户绑定的账号信息"""
	getCurrentUserBindedThirdAccounts:[UserBindedThirdAccountDTO]
	"""获取当前用户的用户信息"""
	getCurrentUserInfo:UserInfoDTO @NotAuthenticationRequired
	"""获取学员详情
		@return
	"""
	getStudent(userId:String):StudentDTO
	"""统计用户数量
		@return
	"""
	getStudentCount:Long! @NotAuthenticationRequired
	"""获取指定用户的第三方绑定账号信息"""
	getUserBindedThirdAccounts(userId:String):[UserBindedThirdAccountDTO]
	"""根据微信授权码解析openId和unionId
		ps:接口弃用，状态层直接调用微服务接口
		@param param
		@return
	"""
	getWXUserInfo(param:WXInfoQueryParam):WXIdInfo @NotAuthenticationRequired
	"""是否绑定微信第三方账号
		@param openId
		@param unionId
		@param connectType 2：微信登录，21：微信小程序
		@param wxNickname  微信昵称，若其他类型已绑定将使用这个微信昵称自动绑定，为空则使用用户昵称
		@return
	"""
	haveBindWXByOpenIdAndUnionIdForOpenAPI(openId:String,unionId:String,connectType:Int!,wxNickname:String):Boolean! @NotAuthenticationRequired
	identityExists(identityType:LoginType,identity:String,exceptUserId:String):Boolean!
	"""手机号是否已有学员使用
		@param phoneNumber
		@param exceptUserId
		@return
	"""
	isPhoneNumberExists(phoneNumber:String!,exceptUserId:String):Boolean! @NotAuthenticationRequired
	"""当前用户是否已完善手机号
		@return
	"""
	isUserPerfectedPhoneNumber:Boolean!
	"""查询用户信息
		上限userId:300
		<p>
		todo 暂无需鉴权，用户评价记录用到，需调整
		@param param
		@return
	"""
	listUserInfo(param:UserParamDTO):[UserSimpleInfoDTO] @NotAuthenticationRequired
	"""服务商登录校验"""
	preValidServicerLogin(identity:String):Void @NotAuthenticationRequired
	"""用户是否存在
		@return
	"""
	userExistsWithIdentityAndName(identity:String!,name:String!):Boolean!
}
type Mutation {
	"""当前用户绑定微信小程序(新增用户头像路径字段)
		@param userBindWXDto
	"""
	bindUserWXMiniProgramForOpenAPI(userBindWXDto:UserBindWXDto):Void
	"""当前用户绑定微信小程序
		@param openId
		@param unionId
		@param wxNickname
	"""
	bindWXMiniProgramForOpenAPI(openId:String,unionId:String,wxNickname:String):Void
	"""当前用户绑定微信公众号
		@param openId
		@param unionId
		@param wxNickname
	"""
	bindWXOfficialAccountForOpenAPI(openId:String,unionId:String,wxNickname:String):Void
	"""创建服务商子管理员"""
	createServicerSubAdmin(request:AdministratorCreateDTO):String
	"""创建平台管理员
		@return
	"""
	createSubAdmin(request:AdministratorCreateDTO):String
	"""发送绑定通知小程序的通知"""
	sendBindingSuccessMessage:Void
	"""解绑微信"""
	unBindCurrentUserWX:Void
	"""根据账户id解绑微信"""
	unBindStudentWX(accountId:String):Void
	"""当前用户换绑手机号
		@return
	"""
	updateCurrentPhoneWithPhoneCaptchaToken(captchaToken:String):Void
	"""当前服务商主账户换绑手机号
		渠道商
		@return
	"""
	updateCurrentServicerAdminPhoneForChannelVendor(captchaToken:String):Void
	"""当前服务商主账户换绑手机号
		课件供应商
		@return
	"""
	updateCurrentServicerAdminPhoneForCoursewareSupplier(captchaToken:String):Void
	"""用户完善手机号
		@param captchaToken
	"""
	userPerfectPhoneNumber(captchaToken:String):Void
	"""微信小程序用户完善手机号接口
		@param phoneNum
	"""
	xwMiniProgramUserPerfectPhoneNumber(phoneNum:String):Void
}
"""<AUTHOR> create 2021/7/26 11:02"""
input AdministratorCreateDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.AdministratorCreateDTO") {
	"""姓名"""
	name:String
	"""昵称"""
	nickName:String
	"""头像地址"""
	photo:String
	"""性别"""
	gender:Int
	"""手机"""
	phone:String
	"""邮箱"""
	email:String
	"""启用状态"""
	status:Int!
	"""简介"""
	remark:String
	"""登录方式（默认账号密码登录）
		用户名 1
		手机 2
		身份证 3
		邮箱 4
		第三方OpenId 5
	"""
	identityType:Int!
	"""登录账户"""
	identity:String
	"""密码"""
	password:String
	"""角色id集合"""
	roleIds:[String]
}
input AdministratorQueryDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.AdministratorQueryDTO") {
	"""主账户id"""
	rootAccountId:String
	"""认证标识（登录账号）"""
	identity:String
	"""姓名"""
	name:String
	"""账户状态，1：正常，2：停用"""
	status:Int
	"""所属角色id"""
	roleIds:[String]
}
"""用户管理查询条件
	<AUTHOR>
	@Date 2020/8/11/0011 9:20
"""
input StudentQueryDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.StudentQueryDTO") {
	"""用户id集合"""
	userIds:[String]
	"""姓名"""
	name:String
	"""身份证号"""
	idCard:String
	"""地区路径"""
	areaPath:String
	"""工作单位名称，模糊搜索"""
	companyName:String
	"""手机号"""
	phone:String
	"""激活时间（起）"""
	activeTimeStart:DateTime
	"""激活时间（止）"""
	activeTimeEnd:DateTime
}
"""用户绑定微信信息DTO
	<AUTHOR>
"""
input UserBindWXDto @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.UserBindWXDto") {
	"""用户的唯一标识"""
	openId:String
	"""只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段"""
	unionId:String
	"""用户昵称"""
	wxNickname:String
	"""用户头像路径"""
	headImgUrl:String
}
"""用户查询条件
	@author: eleven
	@date: 2020/4/19
"""
input UserParamDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.UserParamDTO") {
	"""用户id集合"""
	userIdList:[String]
}
"""获取微信opneiId和unionId等信息的入参
	Author:FangKunSen
	Time:2020-05-18,18:55
"""
input WXInfoQueryParam @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.WXInfoQueryParam") {
	"""@see com.fjhb.support.WXConstant
		type: 0公众号；1小程序
	"""
	type:Int!
	"""微信appId，不填表示当前项目默认只有一个微信开发平台程序，自动从配置中获取"""
	appId:String
	"""授权临时票据code"""
	code:String
	"""国家地区语言版本，默认为中文简体"""
	lang:String
	encryptedData:String
	iv:String
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
type RoleDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.role.dto.RoleDTO") {
	"""角色ID"""
	id:String
	"""角色级别值"""
	levelValue:Int!
	"""角色名称"""
	name:String
	"""角色描述"""
	description:String
	"""创建方式 | 1:自建; 2:导入"""
	createType:Int!
	"""数据类型 | 1:普通; 2:内置"""
	dataType:Int!
	"""创建人ID"""
	creatorId:String
	"""创建时间"""
	createDate:DateTime
	"""是否可用"""
	available:Boolean!
}
type AdministratorDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.AdministratorDTO") {
	"""账户id"""
	accountId:String
	"""用户id"""
	userId:String
	"""姓名"""
	name:String
	"""昵称"""
	nickName:String
	"""电话"""
	phone:String
	"""邮件"""
	email:String
	"""性别:，1：男，2：女，3：中性，4：未知"""
	gender:Int!
	"""头像url"""
	photo:String
	"""创建时间"""
	createTime:DateTime
	"""创建人id"""
	creatorId:String
	"""所拥有的角色集合"""
	roles:[RoleDTO]
	"""账户状态，1：正常，2：停用"""
	status:Int!
	"""登陆账号集合"""
	loginAuthentications:[LoginAuthenticationDTO]
}
"""用户登录账号对象"""
type LoginAuthenticationDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.LoginAuthenticationDTO") {
	"""认证方式ID"""
	id:String
	"""认证方式类型
		帐号密码认证方式 1
		【第三方】微信认证方式 2
		【第三方】补贴管理系统 3
		@see AuthenticationPatternTypes
	"""
	patternType:Int!
	"""认证标识类型
		用户名 1
		手机 2
		身份证 3
		邮箱 4
		第三方OpenId 5
		@see com.fjhb.domain.basicdata.api.account.consts.AuthenticationIdentityTypes
	"""
	identityType:Int!
	"""认证标识"""
	identity:String
	"""创建时间"""
	createdTime:DateTime
}
"""用户信息
	<AUTHOR>
	@Date 2020/8/11/0011 9:20
"""
type StudentDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.StudentDTO") {
	"""账户id"""
	accountId:String
	"""用户id"""
	userId:String
	"""姓名"""
	name:String
	"""昵称"""
	nickName:String
	"""性别
		男: 1; 女: 2; 中性: 3; 未知: 4;
	"""
	gender:Int!
	"""头像地址"""
	photo:String
	"""身份证号"""
	idCard:String
	"""联系地址"""
	address:String
	"""电话"""
	phone:String
	"""激活时间"""
	createTime:DateTime
	"""注册方式
		@see com.fjhb.domain.basicdata.api.user.consts.UserRegisterTypes
	"""
	registerType:Int!
	"""来源类型
		@see com.fjhb.domain.basicdata.api.user.consts.UserSourceTypes
	"""
	sourceType:Int!
	"""所在地区路径"""
	areaPath:String
	"""所属地区名称路径
		例：福建省-福州市-鼓楼区
	"""
	areaPathName:String
	"""工作单位名称"""
	companyName:String
	"""所属人群"""
	peoples:[String]
}
"""用户信息
	<AUTHOR>
	@Date 2020/8/11/0011 9:20
"""
type StudentItemDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.StudentItemDTO") {
	"""用户id"""
	userId:String
	"""姓名"""
	name:String
	"""昵称"""
	nickName:String
	"""性别
		男: 1; 女: 2; 中性: 3; 未知: 4;
	"""
	gender:Int!
	"""头像地址"""
	photo:String
	"""身份证号"""
	idCard:String
	"""联系地址"""
	address:String
	"""电话"""
	phone:String
	"""激活时间"""
	createTime:DateTime
	"""注册方式
		@see com.fjhb.domain.basicdata.api.user.consts.UserRegisterTypes
	"""
	registerType:Int!
	"""来源类型
		@see com.fjhb.domain.basicdata.api.user.consts.UserSourceTypes
	"""
	sourceType:Int!
	"""所在地区路径"""
	areaPath:String
	"""所属地区名称路径
		例：福建省-福州市-鼓楼区
	"""
	areaPathName:String
	"""工作单位名称"""
	companyName:String
	"""所属人群"""
	peoples:[String]
}
"""用户绑定的第三方登陆账号信息
	<AUTHOR>
"""
type UserBindedThirdAccountDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.UserBindedThirdAccountDTO") {
	id:String
	"""账户id"""
	accountId:String
	"""用户ID"""
	userId:String
	"""互联方式,21:微信小程序
		@see OpenPlatformTypes
	"""
	openPlatformType:Int!
	"""用户唯一标识"""
	openId:String
	"""授权时间"""
	authorizeTime:DateTime
	"""是否可用"""
	available:Boolean!
}
"""用户信息对象,其中6个字段用与前端描述当前用户上下文信息"""
type UserInfoDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.UserInfoDTO") {
	"""账户id"""
	accountId:String
	"""账户类型
		"企业帐户", 1
		"企业个人帐户", 2
		"个人帐户", 3
		@see com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.AccountTypeEnums
	"""
	accountType:Int!
	"""用户id"""
	userId:String
	"""姓名"""
	name:String
	"""昵称"""
	nickName:String
	"""身份证号"""
	idCard:String
	"""头像地址"""
	photo:String
	"""性别"""
	gender:Int!
	"""手机号码"""
	phone:String
	"""所属地区编码"""
	areaPath:String
	"""所属地区编码"""
	areaPathName:String
	"""联系地址"""
	address:String
	"""用户创建时间"""
	createTime:DateTime
	"""工作单位名称"""
	companyName:String
	"""注册方式
		@see com.fjhb.domain.basicdata.api.user.consts.UserRegisterTypes
	"""
	registerType:Int!
	"""注册来源
		@see com.fjhb.domain.basicdata.api.user.consts.UserSourceTypes
	"""
	sourceType:Int!
	"""当前登录用户所拥有的角色，可同时拥有多种角色"""
	roleList:[RoleDTO]
	"""登陆账号集合"""
	loginAuthentications:[LoginAuthenticationDTO]
	"""所属人群"""
	peoples:String
}
"""用户对象
	<AUTHOR> create 2020/3/13 9:42
"""
type UserSimpleInfoDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.UserSimpleInfoDTO") {
	"""账户id"""
	accountId:String
	"""用户id"""
	userId:String
	"""姓名"""
	name:String
	"""昵称"""
	nickName:String
	"""身份证号"""
	idCard:String
	"""头像地址"""
	photo:String
	"""性别"""
	gender:Int!
	"""手机号码"""
	phone:String
	"""所属地区编码"""
	areaPath:String
}
"""获取微信必要信息"""
type WXIdInfo @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.WXIdInfo") {
	openId:String
	unionId:String
	accessToken:String
	nickname:String
	refreshToken:String
	sex:String
	headimgurl:String
	purePhoneNumber:String
}
"""登录类型
	<AUTHOR> create 2019/9/29 20:45
"""
enum LoginType @type(value:"com.fjhb.btpx.support.constants.LoginType") {
	"""未知，创建时不可指定为未知，查询时会忽略类型"""
	UNKNOWN
	"""普通账号,通常也用作身份证"""
	NORMAL
	"""邮箱"""
	EMAIL
	"""手机号"""
	PHONE
	"""身份证号,通常不使用，用{@link #NORMAL}代替"""
	IDENTITYCARD
}

scalar List
type AdministratorDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [AdministratorDTO]}
type StudentItemDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentItemDTO]}
