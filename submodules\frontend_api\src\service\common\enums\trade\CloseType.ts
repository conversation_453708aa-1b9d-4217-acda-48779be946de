/**
 * 未关闭 = 0
 * 买家取消 = 1
 * 买家取消 = 2
 * 超时取消 = 3
 * 批次关联取消 = 4
 */
import AbstractEnum from '../AbstractEnum'

enum CloseTypeEnum {
  not_closed = 0,
  buyer_cancel = 1,
  seller_cancel = 2,
  timeout_cancel = 3,
  batch_relation_cancel = 4
}

export { CloseTypeEnum }

class CloseType extends AbstractEnum<CloseTypeEnum> {
  static enum = CloseTypeEnum

  constructor() {
    super()
    this.map[CloseTypeEnum.not_closed] = '未关闭'
    this.map[CloseTypeEnum.buyer_cancel] = '买家取消'
    this.map[CloseTypeEnum.seller_cancel] = '买家取消'
    this.map[CloseTypeEnum.timeout_cancel] = '超时取消'
    this.map[CloseTypeEnum.batch_relation_cancel] = '批次关联取消'
  }
}

export default CloseType
