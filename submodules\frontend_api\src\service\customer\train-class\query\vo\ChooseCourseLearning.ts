import CourseLearningLearningType from '@api/service/customer/train-class/query/vo/CourseLearningLearningType'
import ChooseCourseRule from '@api/service/customer/train-class/query/vo/ChooseCourseRule'

/**
 * 选课规则类型的课程学习
 */
class ChooseCourseLearning extends CourseLearningLearningType {
  // region properties

  /**
   *必修课获得学时，类型为number
   */
  compulsoryPeriod = 0
  /**
   *选修课获得学时，类型为number
   */
  electivePeriod = 0
  /**
   *必修课要求学时，类型为number
   */
  compulsoryRequirePeriod = 0
  /**
   *选修课要求学时，类型为number
   */
  electiveRequirePeriod = 0
  /**
   *选课规则，类型为ChooseCourseRule
   */
  chooseCourseRule = new ChooseCourseRule()
  // endregion
  // region methods

  // endregion
}
export default ChooseCourseLearning
