import getPaymentChannelIdList from './queries/getPaymentChannelIdList.graphql'
import isAllowUpdate from './queries/isAllowUpdate.graphql'
import createElectronicInvoiceTaxpayer from './mutates/createElectronicInvoiceTaxpayer.graphql'
import createPurchaseChannel from './mutates/createPurchaseChannel.graphql'
import createPurchaseChannelForAdmin from './mutates/createPurchaseChannelForAdmin.graphql'
import createPurchaseChannelForImportRequest from './mutates/createPurchaseChannelForImportRequest.graphql'
import createReceiveAccount from './mutates/createReceiveAccount.graphql'
import deleteReceiveAccount from './mutates/deleteReceiveAccount.graphql'
import disableReceiveAccount from './mutates/disableReceiveAccount.graphql'
import enableReceiveAccount from './mutates/enableReceiveAccount.graphql'
import findElectronicInvoiceTaxpayer from './mutates/findElectronicInvoiceTaxpayer.graphql'
import findElectronicInvoiceTaxpayerList from './mutates/findElectronicInvoiceTaxpayerList.graphql'
import operateUserLoginConfig from './mutates/operateUserLoginConfig.graphql'
import prepareElectronicInvoiceConfig from './mutates/prepareElectronicInvoiceConfig.graphql'
import preparePurchaseChannel from './mutates/preparePurchaseChannel.graphql'
import preparePurchaseChannelForAdmin from './mutates/preparePurchaseChannelForAdmin.graphql'
import updateElectronicInvoiceTaxpayer from './mutates/updateElectronicInvoiceTaxpayer.graphql'
import updatePurchaseChannel from './mutates/updatePurchaseChannel.graphql'
import updateReceiveAccount from './mutates/updateReceiveAccount.graphql'

export {
  getPaymentChannelIdList,
  isAllowUpdate,
  createElectronicInvoiceTaxpayer,
  createPurchaseChannel,
  createPurchaseChannelForAdmin,
  createPurchaseChannelForImportRequest,
  createReceiveAccount,
  deleteReceiveAccount,
  disableReceiveAccount,
  enableReceiveAccount,
  findElectronicInvoiceTaxpayer,
  findElectronicInvoiceTaxpayerList,
  operateUserLoginConfig,
  prepareElectronicInvoiceConfig,
  preparePurchaseChannel,
  preparePurchaseChannelForAdmin,
  updateElectronicInvoiceTaxpayer,
  updatePurchaseChannel,
  updateReceiveAccount
}
