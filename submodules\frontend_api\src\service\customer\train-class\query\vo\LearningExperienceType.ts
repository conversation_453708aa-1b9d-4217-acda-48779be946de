import LearningTypeBase from '@api/service/customer/train-class/query/vo/LearningTypeBase'
// import Classification from '@api/service/customer/train-class/query/vo/Classification'
import { LearningFeelEnum } from '@api/service/management/train-class/mutation/Enum/LearningFeelEnum'

/**
 * 兴趣课
 */
class LearningExperienceType extends LearningTypeBase {
  // 是否纳入考核
  isExamed = false
  // 是否达标
  isPassed = false
  // 展示名称
  showName = ''
  // 学员已参加且通过的学习心得数
  learningExperiencePassNum = 0
  // 方案考核要求中至少需参加的心得数
  learningExperienceNeedNum = 0

  // 班级心得前置条件
  classCondition: LearningFeelEnum = null
  // 课程心得前置条件
  courseCondition: LearningFeelEnum = null
  // region properties
  // endregion
  // region methods
  // endregion
}
export default LearningExperienceType
