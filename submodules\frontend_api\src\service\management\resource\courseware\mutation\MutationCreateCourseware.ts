import CreateCoursewareDto from '@api/service/management/resource/courseware/mutation/dto/CreateCoursewareDto'
import { ResponseStatus } from '@hbfe/common'
import MsCourseResource, {
  CoursewareCreateRequest,
  GeneralMutationResponse
} from '@api/ms-gateway/ms-course-resource-v1'

class MutationCreateCourseware {
  createCoursewareDto: CreateCoursewareDto

  async doCreate(): Promise<GeneralMutationResponse> {
    const res = await MsCourseResource.createCourseware(this.createCoursewareDto.toDto())

    return res?.data || new GeneralMutationResponse()
  }
}

export default MutationCreateCourseware
