import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'
import MsBasicdataDomainGatewayV1, {
  FindCurrentRoleListByArgsRequest,
  FindRoleByOwnerRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { RoleDetailVo } from '@api/service/management/authority/role/query/vo/RoleDetailVo'
import DataResolve from '@api/service/common/utils/DataResolve'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import { ApplicationTypeEnum } from '@api/service/management/authority/role/query/ApplicationTypeEnum'

export class QueryRoleList {
  onLoading = false

  get roleList(): RoleInfoResponseVo[] {
    return this._roleList.filter((role) => !role.removed)
  }

  /**
   *总数目，类型为number
   */
  totalSize = 0

  /**
   * 角色列表
   */
  private _roleList = new Array<RoleInfoResponseVo>()

  /**
   * 用户拥有的角色列表
   */
  userRoleList = new Array<RoleInfoResponseVo>()

  /**
   * 获取角色列表
   */
  async queryRoleList() {
    this.onLoading = true
    try {
      const params = new FindCurrentRoleListByArgsRequest()
      const currentDistrbutionId = QueryManagerDetail?.currentUnitInfo?.applicationMemberId
      // if (currentDistrbutionId) {
      //   params.applicationMemberId = currentDistrbutionId
      // }
      // 分销商角色
      if (currentDistrbutionId) {
        params.applicationTypes = [ApplicationTypeEnum.distribution]
      } else {
        // 超管角色
        params.applicationTypes = [ApplicationTypeEnum.thematic, ApplicationTypeEnum.school]
      }
      const res = await MsBasicdataDomainGatewayV1.findCurrentRoleListByArgs(params)
      this._roleList = res.data.map(RoleInfoResponseVo.from)
      this.totalSize = this._roleList.length
    } catch (e) {
      // todo
      console.log(e, '查询角色报错')
    } finally {
      this.onLoading = false
    }
  }

  /**
   * todo 目前该接口是专题管理员管理 新增-修改有需要使用
   * 根据应用放类型取角色列表
   * @param applicationTypes 应用放类型数组 7 专题 1002网校
   */
  static async queryRoleListByArgs(applicationTypes: Array<ApplicationTypeEnum>) {
    const params = new FindCurrentRoleListByArgsRequest()
    params.applicationTypes = applicationTypes
    const res = await MsBasicdataDomainGatewayV1.findCurrentRoleListByArgs(params)

    if (res.data) {
      return res.data.map(RoleInfoResponseVo.from)
    } else {
      return new Array<RoleInfoResponseVo>()
    }
  }

  /**
   * 通过帐号Id查询角色列表
   */
  async getRoleListByAccountId(): Promise<Array<RoleDetailVo>> {
    let result = [] as RoleDetailVo[]
    const response = await MsBasicdataDomainGatewayV1.findRoleByAccountId()
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data)) {
      result = response.data.map(RoleDetailVo.from)
    }
    return result
  }

  /**
   * 通过单位id查询角色列表
   */
  async getRoleListByUnitId(unitId: string): Promise<Array<RoleDetailVo>> {
    try {
      const request = new FindRoleByOwnerRequest()
      request.ownerMemberType = 5
      request.ownerMemberId = unitId
      const res = await MsBasicdataDomainGatewayV1.findRoleByOwner(request)
      const tmpArr = []

      if (res.status.isSuccess()) {
        for (const tmpArrElement of res.data) {
          let roleInfoVo = new RoleDetailVo()
          roleInfoVo = RoleDetailVo.from(tmpArrElement)
          if (this._roleList?.length) {
            for (const role of this.userRoleList) {
              if (role.id === roleInfoVo.id) {
                roleInfoVo.isSelect = true
              }
            }
          }
          tmpArr.push(roleInfoVo)
        }
      }

      console.log('调用了queryRoleList方法，返回值=', tmpArr)
      return tmpArr
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/query/QueryRoleList.ts所处方法，getRoleListByUnitId',
        e
      )
    }
  }
}
