/**
 * @description 数据处理
 */
class DataResolve {
  /**
   * 获取不重复的集合
   * @param list 源集合
   */
  static unique<T = string | number>(list: T[]): T[] {
    return list.length ? [...new Set(list.map((el) => el).filter(Boolean))] : ([] as T[])
  }

  /**
   * 数组是否有重量（是否不为空）
   */
  static isWeightyArr<T>(arr: T[]) {
    return Array.isArray(arr) && arr.length ? true : false
  }

  /**
   * 判断简单类型值是否不为空（不为null、undefined、'') true：是 false：否
   */
  static validIsNotEmpty<T>(val: T): boolean {
    return (val ?? '') !== ''
  }

  /**
   * 校验正整数
   */
  static validNumber(input: any) {
    const pattern = /^[1-9][0-9]*$/
    const result = pattern.test(input)
    return result
  }

  /**
   * 比较开始时间是否早于截止时间
   * @param beginDateStr
   * @param endDateStr
   */
  static validTwoDateOrder(beginDateStr: string, endDateStr: string) {
    const beginDate = new Date(beginDateStr)
    const endDate = new Date(endDateStr)
    return endDate.getTime() > beginDate.getTime()
  }

  /**
   * 获取mfs上图片
   */
  static getMFSImage(imageUrl: string) {
    const mfsHeadReg = /^\/mfs\/\.*/
    return mfsHeadReg.test(imageUrl) ? imageUrl : `/mfs${imageUrl}`
  }

  /**
   * 与当前时间比较大小
   * @param data 时间
   * @returns boolean |true 比当前时间小| false 比当前时间大
   */
  static compareDates(date: string): boolean {
    const reg = new RegExp('-', 'g')
    const newDate = date.replace(reg, '/')
    const currentDate = new Date()
    const compareData = new Date(newDate)
    if (currentDate.getTime() > compareData.getTime()) {
      return true
    } else {
      return false
    }
  }

  /**
   * 判断两个数组是否相等
   * @description 考虑乱序，
   * a1 = [1, 2, 3]、b1 = [3, 1, 2]、b2 = [3, 4, 2],则a1、b1相等；a1、b2不相等
   */
  static compareEquals<T = string | number>(a: T[], b: T[]): boolean {
    let result = true
    a?.forEach((el) => {
      if (!b.some((item) => item === el)) {
        result = false
      }
    })
    b?.forEach((el) => {
      if (!a.some((item) => item === el)) {
        result = false
      }
    })
    return result
  }
  /**
   * 模拟网络请求返回值
   * @param result 返回值
   * @param delay 延迟，默认值：1000毫秒
   */
  static async mockReqReturn<T = any>(result: T, delay = 1000): Promise<T> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(result)
      }, delay)
    })
  }

  /**
   * 根据枚举类型生成随机枚举值
   * @param anyEnum 枚举类型
   */
  static randomEnumValue<T extends Record<string, number | string>>(anyEnum: T): T[keyof T] {
    const values: (string | number)[] = Object.entries(anyEnum)
      .filter((item) => isNaN(Number(item[0])))
      .map((item) => item[1])
    const randomIndex = Math.floor(Math.random() * values.length)
    return values[randomIndex] as T[keyof T]
  }

  /**
   * 数组比较，返回a、b中都有的元素、a中有而b中没有的元素、b中有而a中没有的元素
   * @param a
   * @param b
   */
  static arrayComparison<A extends string | number, B extends string | number>(
    a: A[],
    b: B[]
  ): {
    both: (A & B)[]
    onlyInA: Exclude<A, B>[]
    onlyInB: Exclude<B, A>[]
  } {
    const both: (A & B)[] = a.filter((item) => b.includes(item as any)) as (A & B)[]
    const onlyInA: Exclude<A, B>[] = a.filter((item) => !b.includes(item as any)) as Exclude<A, B>[]
    const onlyInB: Exclude<B, A>[] = b.filter((item) => !a.includes(item as any)) as Exclude<B, A>[]
    return {
      both,
      onlyInA,
      onlyInB
    }
  }
}

export default DataResolve
