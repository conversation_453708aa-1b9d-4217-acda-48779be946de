import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-exam-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-exam-query-front-gateway-QuestionnaireQueryForeStage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum QuestionTypeEnum {
  RADIO = 'RADIO',
  MULTIPLE = 'MULTIPLE',
  FILL = 'FILL',
  OPINION = 'OPINION',
  ASK = 'ASK',
  FATHER = 'FATHER',
  SCALE = 'SCALE'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 根据用户答卷id查询答卷作答详情
 */
export class GetQuestionnaireDetailRequest {
  /**
   * 用户答卷id
   */
  id?: string
}

/**
 * 分页查询我的学习方案内期别调查问卷
 */
export class PageQuestionnaireIssueRequest {
  /**
   * 期别参训资格id
   */
  qualificationId?: string
  /**
   * 问卷id
   */
  questionnaireId?: string
}

/**
 * 分页查询我的学习方案内方案调查问卷
 */
export class PageQuestionnaireSchemeRequest {
  /**
   * 学习方案参训资格id
   */
  qualificationId?: string
  /**
   * 问卷id
   */
  questionnaireId?: string
}

/**
 * 分页查询方案下学员调查问卷信息
 */
export class QuestionnaireAnswerStaitsticsRequest {
  /**
   * 调查问卷id
   */
  questionnaireId?: string
}

export class QuestionnaireTemplateDetailRequest {
  /**
   * 模版id
   */
  templateId?: string
}

/**
 * @Description （跟底层返回的一致）答卷信息
<AUTHOR>
@Date 15:19 2022/2/24
 */
export class AnswerPaperResponse {
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属服务商id
   */
  servicerId: string
  /**
   * id
   */
  id: string
  /**
   * 答卷状态 发布中 1  取消发布 2  已发布 3
   */
  status: number
  /**
   * 试卷作答状态 未开始作答 -1  作答中0 交卷中 1 已交卷 2
   */
  answerStatus: number
  /**
   * 试卷阅卷状态  未交卷-1 阅卷中 0 阅卷完成 1
   */
  markStatus: number
  /**
   * 用户答卷评定结果常量 未评定 -1  无评定结果 0 合格 1 不合格 2
   */
  evaluateResult: number
  /**
   * 场景类型
   */
  sceneType: number
  /**
   * 场景id
   */
  sceneId: string
  qualificationId: string
  userId: string
  /**
   * 答题数
   */
  answerCount: number
  /**
   * 开始作答时间
   */
  beginAnswerTime: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 发布时间
   */
  publishedTime: string
  /**
   * 交卷时间
   */
  handingTime: string
  /**
   * 交卷完成时间
   */
  handedTime: string
  /**
   * 阅卷开始时间
   */
  markingTime: string
  /**
   * 阅卷完成时间
   */
  markedTime: string
  /**
   * 发布时间
   */
  pusblishedTime: string
  /**
   * 考试时长
   */
  timeLength: number
  /**
   * 阅卷人ID
   */
  markUserId: string
  /**
   * 得分，-1表示不是为分数评定方式
   */
  score: number
  /**
   * 正确题数，-1表示试题不进行评定
   */
  correctCount: number
  /**
   * 错误题数，-1表示试题不进行评定
   */
  incorrectCount: number
  studentNo: string
  name: string
  description: string
  /**
   * 总分
   */
  totalScore: number
  cancelReason: string
  createUserId: string
  systemHanded: boolean
  groups: Array<QuestionGroup>
  /**
   * 试题
   */
  questions: Array<Question>
  /**
   * 答卷时长
   */
  answerTimeLength: number
}

/**
 * 试题作答统计
 */
export class AnswerStaitsticsResponse {
  /**
   * 试题id
   */
  questionId: string
  /**
   * 试题类型
@see QuestionTypes
   */
  questionType: number
  /**
   * 作答总人数
   */
  totalCount: number
  /**
   * 是否为教师评价题
   */
  isTeacherQuestion: boolean
  /**
   * 作答统计
   */
  statisticsAnswerContents: Array<StatisticsAnswerContentResponse>
  /**
   * 题目标识
   */
  tag: string
  /**
   * 原试题id
   */
  originalQuestionId: string
}

/**
 * 出卷配置分类主题模型
 */
export class PaperPublishConfigureCategoryResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 出卷配置分类ID
   */
  id: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 父级分类id
   */
  parentCategory: PaperPublishConfigureCategoryResponse
  /**
   * 排序
   */
  sort: number
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 查询当前网校下调查问卷内试题作答统计
 */
export class QuestionnaireAnswerStaitsticsResponse {
  /**
   * 调查问卷信息
   */
  surveyInformationResponse: SurveyInformationResponse
  /**
   * 调查问卷总作答人数
   */
  totalAnswerNum: number
  /**
   * 试题作答统计
   */
  answerStaitsticsList: Array<AnswerStaitsticsResponse>
}

/**
 * 出卷配置主题模型
 */
export class QuestionnairePaperPublishConfigureResponse {
  /**
   * 问卷类型
1 普通问卷
2 量表问卷
   */
  type: number
  /**
   * 是否被引用
   */
  isReferenced: boolean
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 出卷配置ID
   */
  id: string
  /**
   * 出卷配置名称
   */
  name: string
  /**
   * 数据归属信息
   */
  paperPublishConfigureCategory: PaperPublishConfigureCategoryResponse
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2
   */
  paperPublishPatterns: number
  /**
   * 出卷模式
   */
  publishPattern: PublishPattern
  /**
   * 是否启用 1 启用 2禁用
   */
  status: number
  /**
   * 适用范围 用于筛选自定义的分类
   */
  usageScope: number
  /**
   * 是否是草稿 1是  2不是
   */
  isDraft: number
}

export class StatisticsAnswerContentResponse {
  /**
   * 作答项
   */
  answerItem: string
  /**
   * 选项选择人数
   */
  chooseCount: number
}

export class SurveyQuestionnaireResponse {
  /**
   * 调查问卷信息
   */
  surveyInformationResponse: SurveyInformationResponse
  /**
   * 用户答卷基本信息
   */
  basicInformationofUserResponse: BasicInformationofUserResponse
}

/**
 * 数据归属模型
 */
export class DataBelongModel {
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属服务商id
   */
  servicerId: string
}

/**
 * 智能卷出卷模式
 */
export class AutomaticPublishPattern implements PublishPattern {
  /**
   * 建议作答时长
   */
  suggestionTimeLength: number
  /**
   * 抽题规则
   */
  questionExtractRule: QuestionExtractRule
  /**
   * 评定方式
   */
  evaluatePattern: EvaluatePattern
  /**
   * 出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2
   */
  type: number
}

/**
 * 固定卷出卷模式
 */
export class FixedPaper implements PublishPattern {
  /**
   * 试卷id
   */
  id: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷描述
   */
  description: string
  /**
   * 作答时长
   */
  timeLength: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 大题集合
   */
  groups: Array<QuestionGroup>
  /**
   * 试题集合
   */
  questions: Array<PaperQuestion>
  /**
   * 出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2
   */
  type: number
}

/**
 * AB出卷模式
 */
export class MultipleFixedPaperPublishPattern implements PublishPattern {
  /**
   * 固定卷集合
   */
  fixedPapers: Array<FixedPaper>
  /**
   * 出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2
   */
  type: number
}

/**
 * <AUTHOR> create 2021/6/3 17:35
 */
export class PaperQuestion {
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 分数，-1表示不为分数评定方式为
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 是否必答
   */
  answerRequired: boolean
}

/**
 * @Description 初级模式
<AUTHOR>
@Date 9:14 2022/3/1
 */
export interface PublishPattern {
  /**
   * 出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2
   */
  type: number
}

/**
 * 试卷大题信息
<AUTHOR>
 */
export class QuestionGroup {
  sequence: number
  questionType: number
  groupName: string
  eachQuestionScore: number
}

/**
 * 正确率评定方式
<AUTHOR>
 */
export class CorrectRateEvaluatePattern implements EvaluatePattern {
  /**
   * 要求答题总数
   */
  answerQuestionCount: number
  /**
   * 合格正确率
   */
  qualifiedCorrectRate: number
  /**
   * 评定方式类型
   */
  type: number
}

/**
 * 评定方式基类
<AUTHOR>
 */
export interface EvaluatePattern {
  /**
   * 评定方式类型
   */
  type: number
}

/**
 * 无评定方式
<AUTHOR>
 */
export class NoneEvaluatePattern implements EvaluatePattern {
  /**
   * 评定方式类型
   */
  type: number
}

/**
 * @Description试题指定分值设置
<AUTHOR>
@Date 15:15 2022/3/3
 */
export class QuestionMapScoreSetting {
  /**
   * 试题id
   */
  questionId: string
  /**
   * 分数
   */
  score: number
}

/**
 * 试题分数设置信息
<AUTHOR>
 */
export class QuestionScoreSetting {
  /**
   * 大题序号
   */
  sequence: number
  /**
   * 试题类型
   */
  questionType: number
  /**
   * 每题平均分
   */
  eachQuestionScore: number
  /**
   * 具体试题分数
   */
  questionScores: Array<QuestionMapScoreSetting>
}

/**
 * 分数评定方式
<AUTHOR>
 */
export class ScoreEvaluatePattern implements EvaluatePattern {
  /**
   * 总分
   */
  totalScore: number
  /**
   * 合格分数
   */
  qualifiedScore: number
  /**
   * 每道试题分数
   */
  questionScores: Array<QuestionScoreSetting>
  /**
   * 多选题漏选得分模式
   */
  multipleMissScorePattern: number
  /**
   * 评定方式类型
   */
  type: number
}

/**
 * 试题抽题规则
<AUTHOR>
 */
export class QuestionExtractRule {
  /**
   * 试题总数
   */
  questionCount: number
  /**
   * 出题范围
   */
  questionScopes: Array<QuestionScopeSetting>
  /**
   * 出题描述
   */
  questionExtracts: Array<QuestionExtractSetting>
}

/**
 * 试题抽题设置信息
<AUTHOR>
 */
export class QuestionExtractSetting {
  /**
   * 试题类型
   */
  questionType: number
  /**
   * 大题序号
   */
  sequence: number
  /**
   * 大题名称
   */
  groupName: string
  /**
   * 试题数
   */
  questionCount: number
  /**
   * 出题范围
   */
  questionScopes: Array<QuestionScopeSetting>
}

/**
 * 出题范围设置基类
<AUTHOR>
 */
export interface QuestionScopeSetting {
  /**
   * 出题类型
   */
  type: number
}

/**
 * 题库出题配置
<AUTHOR> create 2021/8/19 11:18
 */
export class LibraryFixedQuestionScopeSetting implements QuestionScopeSetting {
  /**
   * 题库对应试题数设置对象
   */
  libraryMapQuestionNumSettings: Array<LibraryMapQuestionNumSetting>
  /**
   * 出题类型
   */
  type: number
}

/**
 * @Description 题库对应试题数设置对象
<AUTHOR>
@Date 14:20 2022/3/3
 */
export class LibraryMapQuestionNumSetting {
  /**
   * 题库id
   */
  libraryId: string
  /**
   * 试题数量
   */
  questionNum: number
}

/**
 * 题库出题配置
<AUTHOR> create 2021/8/19 11:18
 */
export class LibraryQuestionScopeSetting implements QuestionScopeSetting {
  /**
   * 题库id集合
   */
  libraryIds: Array<string>
  /**
   * 出题类型
   */
  type: number
}

/**
 * 用户课程抽题维度
<AUTHOR> create 2021/11/26 13:55
 */
export class UserCourseScopeSetting implements QuestionScopeSetting {
  /**
   * 课程来源
@see UserCourseSources
   */
  userCourseSource: number
  /**
   * 要求的组卷信息key.
当{@link #userCourseSource} &#x3D; 用户课程题库时需要指定
@see ExtractionMessageKeys
   */
  requireKeys: Array<string>
  /**
   * 出题类型
   */
  type: number
}

/**
 * @Description
<AUTHOR>
@Date 17:13 2022/3/9
 */
export class Answer {
  key: number
  /**
   * 答案
   */
  answer: string
}

/**
 * @Description 问答题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class AskQuestion implements Question {
  /**
   * 答案
   */
  askAnswer: string
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * @Description 父子题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class FatherQuestion implements Question {
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * @Description 填空题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class FillQuestion implements Question {
  /**
   * 答案
   */
  fillAnswer: Array<Answer>
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * @Description 多选题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class MultipleQuestion implements Question {
  /**
   * 答案
   */
  multipleAnswer: Array<string>
  /**
   * 填空的内容
   */
  fillContents: Array<MultipleQuestionFillContent>
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

export class MultipleQuestionFillContent {
  /**
   * 选项id
   */
  id: string
  /**
   * 填空内容
   */
  fillContent: string
}

/**
 * @Description 判断题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class OpinionQuestion implements Question {
  /**
   * 答案
   */
  opinionAnswer: boolean
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * @Description 试题
<AUTHOR>
@Date 16:07 2022/2/28
 */
export interface Question {
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * @Description 单选题和底层一样
<AUTHOR>
@Date 15:40 2022/2/28
 */
export class RadioQuestion implements Question {
  /**
   * 答案
   */
  radioAnswer: string
  /**
   * 需要填空的内容
   */
  fillContent: string
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * @Description 量表题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class ScaleQuestion implements Question {
  /**
   * 答案
   */
  answer: number
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * 用户答卷基本信息
 */
export class BasicInformationofUserResponse {
  /**
   * 答卷id
   */
  answerPaperId: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 服务商ID
   */
  servicerId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 平台租户id
   */
  tenantId: string
  /**
   * 作答用户类型 1-学员
   */
  userType: number
  /**
   * 作答用户id
   */
  userId: string
  /**
   * 学员学号
   */
  studentNo: string
  /**
   * 学员参训资格id
   */
  qualificationId: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 期别id
   */
  issueId: string
  /**
   * 应用场景类型 1-考试 2-练习 3-课后测验 4-问卷调查
   */
  sceneType: number
  /**
   * 场景id
   */
  sceneId: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷描述
   */
  description: string
  /**
   * 作答时长 单位秒 -1表示没有时长限制
   */
  timeLength: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 大题信息
   */
  questionGroups: string
  /**
   * 答卷状态 1发布中 2.取消发布 3.已发布 4.已作废
   */
  answerPaperStatus: number
  /**
   * 取消发布原因
   */
  publicCanceledReason: string
  /**
   * 答卷作答状态 -1未开始 0.作答中 1.交卷中 2.已交卷
   */
  answerStatus: number
  /**
   * 开始作答时间
   */
  beginAnswerTime: string
  /**
   * 完成作答时间
   */
  endAnswerTime: string
  /**
   * 答卷发布时间
   */
  publishedTime: string
  /**
   * 答卷取消发布时间
   */
  cancelTime: string
  /**
   * 答卷作废时间
   */
  invalidTime: string
  /**
   * 阅卷完成时间
   */
  markedTime: string
  /**
   * 阅卷开始时间
   */
  markingTime: string
  /**
   * 重新阅卷时间
   */
  remarkTime: string
  /**
   * 是否系统强制交卷 0否 1是
   */
  isSystemCommitted: number
  /**
   * 答卷阅卷状态 -1未交卷 0阅卷中 1阅卷完成
   */
  markStatus: number
  /**
   * 阅卷人id
   */
  markUserId: string
  /**
   * 合格分
   */
  qualifiedScore: number
  /**
   * 得分
   */
  score: number
  /**
   * 评定结果 -1未评定 0无评定结果 1合格 2不合格
   */
  evaluateResult: number
  /**
   * 答题总数
   */
  answerCount: number
  /**
   * 正确题目数
   */
  correctCount: number
  /**
   * 错误题目数
   */
  incorrectCount: number
  /**
   * 是否系统评定 0否 1是
   */
  IsSystemHanded: number
  /**
   * 是否重新阅卷 0否 1是
   */
  isRemark: number
  /**
   * 用户答卷创建时间
   */
  createdTime: string
}

/**
 * 前置条件
 */
export class PreconditionResponse {
  /**
   * 前置条件名称
   */
  name: string
  /**
   * 前置条件表达式
   */
  expression: string
  /**
   * 学习方式id
   */
  learningId: string
}

/**
 * 调查问卷信息
 */
export class SurveyInformationResponse {
  /**
   * 问卷id
   */
  questionnaireId: string
  /**
   * 问卷模版id
   */
  templateId: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 拥有者id
   */
  ownerId: string
  /**
   * 试卷名称
   */
  questionnaireName: string
  /**
   * 应用范围
0-SCHEME 1-ONLINECOURSES 2-TRAININGSTAGE
   */
  usedRange: number
  /**
   * 是否纳入考核
   */
  includedInAssessment: boolean
  /**
   * 是否强制完成
   */
  forceToComplete: boolean
  /**
   * 问卷类型
   */
  type: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 问卷开放时间 起
   */
  questionnaireStartTime: string
  /**
   * 问卷开放时间 止
   */
  questionnaireEndTime: string
  /**
   * 问卷状态
启用 1
停用 2
@see com.fjhb.domain.exam.api.questionnaire.consts.QuestionnaireStatus
   */
  status: number
  /**
   * 是否被引用
   */
  isReferenced: boolean
  /**
   * 问卷描述
   */
  description: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 前置条件
   */
  precondition: PreconditionResponse
  /**
   * 是否开放结果
true-开放
false-不开放
   */
  openResults: boolean
}

export class SurveyQuestionnaireResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SurveyQuestionnaireResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询当前网校下调查问卷内试题作答统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getQuestionnaireAnswerStaitsticsInMyself(
    request: QuestionnaireAnswerStaitsticsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getQuestionnaireAnswerStaitsticsInMyself,
    operation?: string
  ): Promise<Response<QuestionnaireAnswerStaitsticsResponse>> {
    return commonRequestApi<QuestionnaireAnswerStaitsticsResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据用户答卷id查询答卷作答详情
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getQuestionnaireDetailInMyself(
    request: GetQuestionnaireDetailRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getQuestionnaireDetailInMyself,
    operation?: string
  ): Promise<Response<AnswerPaperResponse>> {
    return commonRequestApi<AnswerPaperResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据问卷模板id获取问卷模板详情(学员端)
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getQuestionnaireTemplateDetailInMyself(
    request: QuestionnaireTemplateDetailRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getQuestionnaireTemplateDetailInMyself,
    operation?: string
  ): Promise<Response<QuestionnairePaperPublishConfigureResponse>> {
    return commonRequestApi<QuestionnairePaperPublishConfigureResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询我的学习方案内期别调查问卷
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageQuestionnaireIssueInMyself(
    params: { page?: Page; rquest?: PageQuestionnaireIssueRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageQuestionnaireIssueInMyself,
    operation?: string
  ): Promise<Response<SurveyQuestionnaireResponsePage>> {
    return commonRequestApi<SurveyQuestionnaireResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询我的学习方案内方案调查问卷
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageQuestionnaireSchemeInMyself(
    params: { page?: Page; rquest?: PageQuestionnaireSchemeRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageQuestionnaireSchemeInMyself,
    operation?: string
  ): Promise<Response<SurveyQuestionnaireResponsePage>> {
    return commonRequestApi<SurveyQuestionnaireResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
