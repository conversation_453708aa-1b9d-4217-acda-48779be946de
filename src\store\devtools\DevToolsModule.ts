import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import SystemLog from '@hbfe/jxjy-admin-components/src/system-event-logs/models/SystemLog'
import MockItem from '@/store/devtools/models/MockItem'
import DevelopmentSettings from '@/store/devtools/models/DevelopmentSettings'
import { merge, uniq } from 'lodash'

const storeKey = 'developmentConfig'
let localDevelopmentConfig = new DevelopmentSettings()
try {
  const config = JSON.parse(localStorage.getItem(storeKey))
  localDevelopmentConfig = merge(localDevelopmentConfig, config)
} catch (e) {
  localDevelopmentConfig = new DevelopmentSettings()
}

@Module({
  name: 'DevToolsModule',
  namespaced: true,
  store: store,
  dynamic: true
})
class DevToolsModule extends VuexModule {
  logIfBigThan = 999
  maxTipCount = 100
  receiveCount = 0
  systemLogs: Array<SystemLog> = new Array<SystemLog>()
  mockList: Array<MockItem> = new Array<MockItem>()
  developmentSettings: DevelopmentSettings = localDevelopmentConfig

  @Action
  setDevelopmentSetting(devConfig: DevelopmentSettings) {
    this.SET_DEVELOPMENT_SETTING(devConfig)
  }

  @Action
  addSystemLog(systemLog: SystemLog) {
    this.DO_ADD_SYSTEM_LOG(systemLog)
  }

  @Action
  clearReceiveCount() {
    this.CLEAR_RECEIVE_COUNT()
  }

  @Action
  initMockList(mockList: Array<MockItem>) {
    this.SET_MOCK_LIST(mockList)
  }

  @Mutation
  SET_DEVELOPMENT_SETTING(devConfig: DevelopmentSettings) {
    // if (this.developmentSettings.provider !== devConfig.provider || this.developmentSettings.openMock !== devConfig.openMock) {
    // 切换都会刷新页面
    window.location.reload()
    // }
    this.developmentSettings = devConfig
    localStorage.setItem(storeKey, JSON.stringify(devConfig))
  }

  @Mutation
  SET_MOCK_LIST(mockList: Array<MockItem>) {
    this.mockList = uniq(this.mockList.concat(mockList))
  }

  @Mutation
  DO_ADD_SYSTEM_LOG(systemLog: SystemLog) {
    this.systemLogs.push(systemLog)
    this.receiveCount++
  }

  @Mutation
  CLEAR_RECEIVE_COUNT() {
    this.receiveCount = 0
  }

  get getByQuery() {
    return (params: { schemaName: string; method: string }): MockItem => {
      return this.mockList.find((mockItem) => {
        return (
          mockItem.method === params.method &&
          mockItem.clientIp === process.env.CURRENT_IP &&
          mockItem.name === params.schemaName
        )
      })
    }
  }
}

export default getModule(DevToolsModule)
