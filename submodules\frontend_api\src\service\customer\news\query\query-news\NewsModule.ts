import NewsSimple from './models/NewsSimple'
import NewsDetails from './models/NewsDetails'
import newsGateway, { NewsNoticeDetailDTO } from '@api/gateway/PlatformNewNotice'
import PlatformNewNotice, {
  CategoryDetailDTO,
  NewsNoticeCondition,
  Page,
  PopNewsNoticeDTO
} from '@api/gateway/PlatformNewNotice'
import NewsParam from './models/NewsParam'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { ResponseStatus } from '../../../../../Response'
import { CategoryDetail } from '@api/service/common/models/info-content/CategoryDetail'
import { UnAuthorize } from '@api/Secure'

interface NewsModuleState {
  // 首页资讯
  newsList: Array<NewsSimple>
  // 资讯分页
  newsPageData: Array<NewsSimple>
  // 资讯 总数
  totalSize: number
  // 资讯 总页数
  totalPageSize: number
  // 首页默认读取条数
  defaultSize: number
  // 资讯详情
  newsDetails: NewsDetails
  // 资讯类别
  newsType: string
  /**
   * 当前时间内的弹窗公告
   */
  popNewsNoticeDTO: PopNewsNoticeDTO
  /**
   * 子分类
   */
  subCategoryList: Array<CategoryDetail>
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerNewsModule', store })
class NewsModule extends VuexModule implements NewsModuleState {
  // 首页资讯
  newsList = new Array<NewsSimple>()
  // 资讯分页
  newsPageData = new Array<NewsSimple>()
  // 资讯 总数
  totalSize = 0
  // 资讯 总页数
  totalPageSize = 0
  // 首页默认读取条数
  defaultSize = 5
  // 资讯详情
  newsDetails = new NewsDetails()
  // 资讯类别
  newsType = ''
  defaultNewsType = 'PORTAL_CENTER'

  // 资讯分页推荐
  newsHotPageData = new Array<NewsSimple>()

  /**
   * 当前时间内的弹窗公告
   */
  popNewsNoticeDTO: PopNewsNoticeDTO = new PopNewsNoticeDTO()
  /**
   * 子分类
   */
  subCategoryList: Array<CategoryDetail> = new Array<CategoryDetail>()

  lastOrNextNewsNotice: NewsNoticeDetailDTO = new NewsNoticeDetailDTO()

  @Action
  init(newsType: string) {
    this.SET_NEW_TYPE(newsType)
  }

  /**
   * 获取当前时间的弹窗公告
   */
  @Action
  async getPopNewsNotice() {
    const res = await newsGateway.getPopNewsNotice()
    if (res.status.isSuccess()) {
      this.SET_POP_NEWS(res.data)
    }
    return res.status
  }

  @Action
  async getNews(id: string): Promise<ResponseStatus> {
    const { status, data } = await newsGateway.getNewsNotice(id)
    const detail = new NewsDetails()
    Object.assign(detail, data)
    this.SET_NEWS_DETAILS(detail)
    return status
  }

  @Action
  @UnAuthorize
  async getList(params: NewsParam): Promise<ResponseStatus> {
    const page = new Page()
    page.pageNo = 1
    page.pageSize = this.defaultSize
    const paramRemote = new NewsNoticeCondition()
    Object.assign(paramRemote, params)
    paramRemote.status = 1
    paramRemote.type = -1
    if (!paramRemote.categoryType) {
      paramRemote.categoryType = this.defaultNewsType
    }
    const { status, data } = await newsGateway.pageNewsNotice({ page: page, queryParam: paramRemote })
    if (status.isSuccess()) {
      const newsList = new Array<NewsSimple>()
      Object.assign(newsList, data.currentPageData)
      this.SET_NEWS_LIST(newsList)
    }
    return status
  }

  /*   根据资讯id查询上一条或下一条资讯信息 */
  @Action
  @UnAuthorize
  async getLastOrNextNewsNotice(params: {
    id?: string
    queryParameter?: NewsParam
    location: number
  }): Promise<ResponseStatus> {
    const paramRemote = new NewsNoticeCondition()
    Object.assign(paramRemote, params.queryParameter)
    paramRemote.status = 1
    paramRemote.type = -1
    if (!paramRemote.categoryType) {
      paramRemote.categoryType = this.defaultNewsType
    }
    const { status, data } = await newsGateway.getLastOrNextNewsNotice({
      id: params.id,
      queryParameter: paramRemote,
      location: params.location
    })
    if (status.isSuccess()) {
      this.SET_LAST_OR_NEXT_NEWS_NOTICE(data)
    }
    return status
  }

  // 获取资讯分页
  @Action
  @UnAuthorize
  async getPage(params: { page: Page; queryParam: NewsParam }): Promise<ResponseStatus> {
    const paramRemote = new NewsNoticeCondition()
    Object.assign(paramRemote, params.queryParam)
    paramRemote.status = 1
    paramRemote.type = -1
    if (!paramRemote.categoryType) {
      paramRemote.categoryType = this.defaultNewsType
    }
    const { status, data } = await newsGateway.pageNewsNotice({ page: params.page, queryParam: paramRemote })
    if (status.isSuccess()) {
      const newsList = new Array<NewsSimple>()
      Object.assign(newsList, data.currentPageData)
      this.SET_NEWS_PAGE_DATA(newsList)
      this.SET_NEW_TYPE(params.queryParam.categoryType)
      this.SET_TOTAL_PAGE_SIZE(data.totalPageSize)
      this.SET_TOTAL_SIZE(data.totalSize)
    }
    return status
  }

  // 获取资讯分页的热门推荐
  @Action
  @UnAuthorize
  async getHotPage(params: { page: Page; queryParam: NewsParam }): Promise<ResponseStatus> {
    const paramRemote = new NewsNoticeCondition()
    Object.assign(paramRemote, params.queryParam)
    paramRemote.status = 1
    paramRemote.type = -1
    if (!paramRemote.categoryType) {
      paramRemote.categoryType = this.defaultNewsType
    }
    const { status, data } = await newsGateway.pageNewsNotice({ page: params.page, queryParam: paramRemote })
    if (status.isSuccess()) {
      const newsList = new Array<NewsSimple>()
      Object.assign(newsList, data.currentPageData)
      this.SET_NEWS_HOT_PAGE_DATA(newsList)
    }
    return status
  }

  /**
   * 获取子分类
   * @param parentId
   */
  @Action
  @UnAuthorize
  async listSubCategoryByParentId(parentId: string) {
    const res = await PlatformNewNotice.listSubCategoryByParentId(parentId)
    if (!res.status.isSuccess()) {
      return res.status
    }
    this.SET_SUB_CATEGORY_LIST(res.data)
    return res.status
  }

  @Mutation
  private SET_LAST_OR_NEXT_NEWS_NOTICE(list: NewsNoticeDetailDTO) {
    this.lastOrNextNewsNotice = list
  }

  @Mutation
  private SET_SUB_CATEGORY_LIST(list: Array<CategoryDetailDTO>) {
    this.subCategoryList = new Array<CategoryDetail>()
    list.map(p => {
      const item = new CategoryDetail()
      Object.assign(item, p)
      this.subCategoryList.push(item)
    })
  }

  @Mutation
  private SET_POP_NEWS(popNewsNoticeDTO: PopNewsNoticeDTO) {
    this.popNewsNoticeDTO = popNewsNoticeDTO
  }

  @Mutation
  SET_NEWS_LIST(newsList: Array<NewsSimple>) {
    this.newsList = newsList
  }

  @Mutation
  SET_NEWS_HOT_PAGE_DATA(newsPageData: Array<NewsSimple>) {
    this.newsHotPageData = newsPageData
  }

  @Mutation
  SET_NEWS_PAGE_DATA(newsPageData: Array<NewsSimple>) {
    this.newsPageData = newsPageData
  }

  @Mutation
  SET_TOTAL_SIZE(totalSize: number) {
    this.totalSize = totalSize
  }

  @Mutation
  SET_TOTAL_PAGE_SIZE(totalPageSize: number) {
    this.totalPageSize = totalPageSize
  }

  @Mutation
  SET_NEWS_DETAILS(newsDetails: NewsDetails) {
    this.newsDetails = newsDetails
  }

  @Mutation
  SET_NEW_TYPE(newsType: string) {
    this.newsType = newsType
  }

  get newestNew() {
    return this.newsPageData[0]
  }
}

export default getModule(NewsModule)
