<template>
  <div>
    <!--表格-->
    <el-table
      ref="learningRules"
      :data="learningRuleList"
      stripe
      max-height="500px"
      class="m-table"
      v-loading="loading"
    >
      <el-table-column type="index" label="No." width="60"></el-table-column>
      <el-table-column label="适用行业" min-width="150">
        <template v-slot="{ row }">{{ row.basicInfo.industryName }}</template>
      </el-table-column>
      <el-table-column label="适用年度" min-width="180">
        <template v-slot="{ row }"><div v-html="getYears(row.basicInfo)"></div></template>
      </el-table-column>
      <el-table-column label="适用行业属性" min-width="380">
        <template v-slot="{ row }">
          <div class="m-online-20">
            <div class="con">{{ showSku(row) }}</div>
            <el-button type="text" v-if="showSku(row).length > 20" @click="openSkuDialog(row)">查看更多</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="每天学习时长" min-width="120">
        <template v-slot="{ row }">{{ studyTime(row) }}</template>
      </el-table-column>
      <el-table-column label="状态" min-width="100">
        <template v-slot="{ row }">
          <div v-if="row.enable == true">
            <el-badge is-dot type="success" class="badge-status">启用</el-badge>
          </div>
          <div v-else>
            <el-badge is-dot type="info" class="badge-status">停用</el-badge>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" min-width="220">
        <template v-slot="{ row }">
          <p><el-tag type="info" size="mini">修改时间</el-tag>{{ row.updateTime ? row.updateTime : '-' }}</p>
          <p><el-tag type="info" size="mini">生效时间</el-tag>{{ row.effectiveTime ? row.effectiveTime : '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="操作人" min-width="100">
        <template v-slot="{ row }">{{ row.operator }}</template>
      </el-table-column>
      <el-table-column label="操作" min-width="150" align="center" fixed="right">
        <template v-slot="{ row }">
          <el-button type="text" @click="startOrStop('停用', row)" v-if="row.enable">停用</el-button>
          <el-button type="text" @click="startOrStop('启用', row)" v-else>启用</el-button>
          <el-button type="text" @click="goDetail(row.id)">详情</el-button>
          <el-button type="text" @click="editBaseConfig(row.id)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"> </hb-pagination>
    <!-- 适用行业属性弹窗 -->
    <sku-value-dialog ref="skuValueDialogRef"></sku-value-dialog>
  </div>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import LearningRuleList from '@api/service/management/online-learning-rule/LearningRuleList'
  import LearningRuleItem from '@api/service/management/online-learning-rule/LearningRuleItem'
  import BasicInfo from '@api/service/management/online-learning-rule/model/BasicInfo'
  import { TimeModeEnum } from '@api/service/management/online-learning-rule/enum/TimeModeEnum'
  import SkuValueDialog from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/sku-value-dialog.vue'
  @Component({
    components: {
      SkuValueDialog
    }
  })
  export default class extends Vue {
    @Ref('skuValueDialogRef') skuValueDialogRef: SkuValueDialog
    constructor() {
      super()
      this.page = new UiPage(this.queryList, this.queryList)
    }
    page: UiPage

    // 学习规则模型
    learningRuleForm = new LearningRuleList()
    // 学习规则列表
    learningRuleList: Array<LearningRuleItem> = []
    // 加载
    loading = false

    get showSku() {
      return (item: LearningRuleItem) => {
        let skuValue = ''
        // 存在人社行业
        if (item.basicInfo.RSProperty.isTraverse) {
          skuValue = '人社行业：'
          if (item.basicInfo.RSProperty.subjectTypeName) {
            skuValue += `科目类型（${item.basicInfo.RSProperty.subjectTypeName}）`
          }
          if (item.basicInfo.RSProperty.subjectTypeName && item.basicInfo.RSProperty.trainingProfessionalName) {
            skuValue += `、`
          }
          if (item.basicInfo.RSProperty.trainingProfessionalName) {
            skuValue += `培训专业（${item.basicInfo.RSProperty.trainingProfessionalName}）`
          }
        }
        // 存在建设行业
        if (item.basicInfo.JSProperty.isTraverse) {
          skuValue = '建设行业：'
          if (item.basicInfo.JSProperty.subjectTypeName) {
            skuValue += `科目类型（${item.basicInfo.JSProperty.subjectTypeName}）`
          }
          if (item.basicInfo.JSProperty.subjectTypeName && item.basicInfo.JSProperty.trainingCategoryName) {
            skuValue += `、`
          }
          if (item.basicInfo.JSProperty.trainingCategoryName) {
            skuValue += `培训类别（${item.basicInfo.JSProperty.trainingCategoryName}）`
          }
        }
        // 存在卫生行业
        if (item.basicInfo.WSProperty.isTraverse) {
          skuValue = '卫生行业：'
          if (item.basicInfo.WSProperty.trainingCategoryName) {
            skuValue += `培训类别（${item.basicInfo.WSProperty.trainingCategoryName}）`
          }
          if (item.basicInfo.WSProperty.trainingCategoryName && item.basicInfo.WSProperty.trainingObjectName) {
            skuValue += `、`
          }
          if (item.basicInfo.WSProperty.trainingObjectName) {
            skuValue += `培训对象（${item.basicInfo.WSProperty.trainingObjectName}）`
          }
          if (item.basicInfo.WSProperty.trainingObjectName && item.basicInfo.WSProperty.positionCategoryName) {
            skuValue += `、`
          }
          if (item.basicInfo.WSProperty.positionCategoryName) {
            skuValue += `岗位类别（${item.basicInfo.WSProperty.positionCategoryName}）`
          }
        }
        // 存在工勤行业
        if (item.basicInfo.GQProperty.isTraverse) {
          skuValue = '工勤行业：'
          if (item.basicInfo.GQProperty.jobLevelName) {
            skuValue += `技术等级（${item.basicInfo.GQProperty.jobLevelName}）`
          }
        }
        // 存在教师行业
        if (item.basicInfo.LSProperty.isTraverse) {
          skuValue = '教师行业：'
          if (item.basicInfo.LSProperty.learningPhaseName) {
            skuValue += `学段（${item.basicInfo.LSProperty.learningPhaseName}）`
          }
          if (item.basicInfo.LSProperty.learningPhaseName && item.basicInfo.LSProperty.disciplineName) {
            skuValue += `、`
          }
          if (item.basicInfo.LSProperty.disciplineName) {
            skuValue += `学科（${item.basicInfo.LSProperty.disciplineName}）`
          }
        }
        // 存在药师行业
        if (item.basicInfo.YSProperty.isTraverse) {
          skuValue = '药师行业：'
          if (item.basicInfo.YSProperty.subjectTypeName) {
            skuValue += `科目类型（${item.basicInfo.YSProperty.subjectTypeName}）`
          }
        }
        return skuValue
      }
    }

    /**
     * 每天学习时间
     */
    get studyTime() {
      return (item: LearningRuleItem) => {
        if (item.basicInfo.timeMode == TimeModeEnum.learning) {
          return `${item.basicInfo.everyDayLearningHours}学时`
        } else if (item.basicInfo.timeMode == TimeModeEnum.physical) {
          return `${item.basicInfo.everyDayLearningTime}分钟`
        } else {
          return '--'
        }
      }
    }

    async activated() {
      this.loading = true
      setTimeout(() => {
        this.page.currentChange(1)
        this.loading = false
      }, 1000)
    }
    async created() {
      this.loading = true
      setTimeout(() => {
        this.page.currentChange(1)
        this.loading = false
      }, 1000)
    }

    async queryList() {
      this.loading = true
      await this.learningRuleForm.queryLearningRuleList(this.page)
      await this.learningRuleForm.queryOperationUser()
      this.learningRuleList = this.learningRuleForm.learningRuleList
      this.loading = false
      ;(this.$refs['learningRules'] as any)?.doLayout()
    }

    // 启停用
    async startOrStop(title: string, row: LearningRuleItem) {
      this.$confirm(`确认${title}规则吗？${title}后隔天生效。`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async () => {
        const res = await row.changeStatus()
        if (res.isSuccess()) {
          this.$message.success(`${title}成功`)
          setTimeout(async () => {
            await this.queryList()
          }, 1000)
        } else {
          this.$message.error('系统异常')
        }
      })
    }

    getYears(row: BasicInfo) {
      let html = ''
      if (row.RSProperty && row.RSProperty.year.length) {
        html = html + `人社行业：${row.RSProperty.yearName}<br>`
      }
      if (row.JSProperty && row.JSProperty.year.length) {
        html = html + `建设行业：${row.JSProperty.yearName}<br>`
      }
      if (row.WSProperty && row.WSProperty.year.length) {
        html = html + `职业卫生行业：${row.WSProperty.yearName}<br>`
      }
      if (row.GQProperty && row.GQProperty.year.length) {
        html = html + `工勤行业：${row.GQProperty.yearName}<br>`
      }
      if (row.LSProperty && row.LSProperty.year.length) {
        html = html + `教师行业：${row.LSProperty.yearName}<br>`
      }
      if (row.YSProperty && row.YSProperty.year.length) {
        html = html + `药师行业：${row.YSProperty.yearName}`
      }
      return html
    }

    // 去详情
    goDetail(id: string) {
      this.$router.push('/basic-data/platform/function/online-learning-rules/detail/' + id)
    }

    // 修改基础配置
    editBaseConfig(id: string) {
      this.$router.push(`/basic-data/platform/function/online-learning-rules/modify/` + id)
    }

    /**
     * 打开适用行业属性弹窗
     */
    openSkuDialog(row: LearningRuleItem) {
      this.skuValueDialogRef.skuItem = row?.basicInfo
      this.skuValueDialogRef.initData()
    }
  }
</script>
