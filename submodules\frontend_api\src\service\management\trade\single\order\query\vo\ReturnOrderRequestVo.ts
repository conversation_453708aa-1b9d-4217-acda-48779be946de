import {
  CommoditySkuRequest1,
  DateScopeRequest,
  OrderInfoRequest,
  ReturnCloseReasonRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { OrderRefundTypeEnum } from '@api/service/common/return-order/enums/OrderRefundType'
import { OrderRefundStatusEnum } from '@api/service/common/return-order/enums/OrderRefundStatus'
/**
 * 退货单审批信息查询参数
 <AUTHOR>
 @date 2022/03/18
 */
export class ReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime = new DateScopeRequest()
}
/**
 * 退货单状态变更时间查询参数
 <AUTHOR>
 @date 2022/03/24
 */
export class ReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied = new DateScopeRequest()
  /**
   * 退货单完成时间
   <br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted = new DateScopeRequest()
}
/**
 * 发货单基本信息查询参数
 <AUTHOR>
 @date 2022/03/24
 */
export class ReturnOrderBasicDataRequest {
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
   */
  returnOrderStatus?: Array<number>
  /**
   * 退货单类型
   1-仅退货
   2-仅退款
   3-退货并退款
   4-部分退货
   5-部分退款
   6-部分退货并部分退款
   7-部分退货并全额退款
   8-全部退货并部分退款
   */
  returnOrderTypes: Array<number> = new Array<number>()
  /**
   * 退货单申请来源类型
   SUB_ORDER
   BATCH_RETURN_ORDER
   @see ReturnOrderApplySourceTypes
   */
  applySourceType?: string
  /**
   * 来源ID集合
   */
  applySourceIdList?: Array<string>
  /**
   * 退货单状态变更时间
   */
  returnStatusChangeTime = new ReturnOrderStatusChangeTimeRequest()
  /**
   * 退货单关闭信息
   */
  returnCloseReason = new ReturnCloseReasonRequest()
}
/**
 * <AUTHOR>
 @date 2022/03/24
 */
export class SubOrderInfoRequest {
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 订单查询参数
   */
  orderInfo?: OrderInfoRequest = new OrderInfoRequest()
}
/**
 * 退货单查询参数
 */
export class ReturnOrderRequestVo {
  /**
   * 退货单号
   */
  returnOrderNoList: Array<string> = []
  /**
   * 基本信息
   */
  basicData = new ReturnOrderBasicDataRequest()
  /**
   * 审批信息
   */
  approvalInfo = new ReturnOrderApprovalInfoRequest()
  /**
   * 退货商品id集合
   */
  returnCommoditySkuIdList: Array<string> = []
  /**
   * 退款商品id集合
   */
  refundCommoditySkuIdList: Array<string> = []
  /**
   * 退货单关联子订单查询参数
   */
  subOrderInfo = new SubOrderInfoRequest()
  /**
   * 退货商品查询条件
   */
  returnCommodity: CommoditySkuRequest1 = new CommoditySkuRequest1()

  /**
   * 名字
   */
  name = ''
  /**
   * 身份证
   */

  idCard = ''

  /**
   * 登录账号
   */
  loginAccount = ''

  /**
   * 销售渠道
   */
  saleSource: SaleChannelEnum = null

  /**
   * 分销商Id
   */
  distributorId = ''

  /**
   * 推广门户Id
   */
  portalId = ''

  /**
   * 是否非门户推广数据
   */
  isDistributionExcludePortal = false

  /**
   * 退货款类别
   */
  refundType: OrderRefundTypeEnum = undefined

  /**
   * 退货单状态
   */
  returnStatusVo: OrderRefundStatusEnum = undefined

  /**
   * 期别id
   */
  periodId: string = undefined

  /**
   * 根据vo退货单状态填充dto退货单状态
   */
  fillDtoReturnStatusWithVo() {
    switch (this.returnStatusVo) {
      case OrderRefundStatusEnum.pendingAudit:
        this.basicData.returnOrderStatus = [0, 1]
        break
      case OrderRefundStatusEnum.cancelled:
        this.basicData.returnOrderStatus = [11]
        this.basicData.returnCloseReason.closeTypeList = [1, 2]
        break
      case OrderRefundStatusEnum.rejected:
        this.basicData.returnOrderStatus = [11]
        this.basicData.returnCloseReason.closeTypeList = [3]
        break
      case OrderRefundStatusEnum.pendingReturn:
        this.basicData.returnOrderStatus = [2]
        break
      case OrderRefundStatusEnum.returnFailed:
        this.basicData.returnOrderStatus = [3]
        break
      case OrderRefundStatusEnum.pendingConfirmRefund:
        this.basicData.returnOrderStatus = [4, 5]
        break
      case OrderRefundStatusEnum.pendingRefund:
        this.basicData.returnOrderStatus = [6]
        break
      case OrderRefundStatusEnum.refundFailed:
        this.basicData.returnOrderStatus = [7]
        break
      case OrderRefundStatusEnum.refundSuccess:
        this.basicData.returnOrderStatus = [8, 9, 10, 99]
        break
      default:
        this.basicData.returnOrderStatus = []
        this.basicData.returnCloseReason.closeTypeList = []
    }
  }
}
