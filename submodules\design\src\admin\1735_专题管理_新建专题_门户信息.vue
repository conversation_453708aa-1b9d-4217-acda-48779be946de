<template>
  <el-main>
    <div class="f-tr f-pt15">
      <el-button type="primary" size="medium" class="f-mr5"
        ><i class="hb-iconfont icon-complelearn f-mr5"></i>预览专题web</el-button
      >
      <el-button type="primary" size="medium" class="f-mr15"
        ><i class="hb-iconfont icon-complelearn f-mr5"></i>预览专题h5</el-button
      >
    </div>
    <div class="f-p15">
      <!--第二步-->
      <el-card shadow="never" class="m-card is-header">
        <el-row type="flex" justify="center">
          <el-col :sm="20" :lg="12">
            <el-steps :active="2" align-center class="m-steps f-pt40 f-pb10">
              <el-step title="设置专题基础信息"></el-step>
              <el-step title="设置专题门户信息"></el-step>
              <el-step title="设置专题培训方案"></el-step>
              <el-step title="保存专题"></el-step>
            </el-steps>
          </el-col>
        </el-row>
        <div class="m-tit is-border-bottom f-justify-between">
          <span class="tit-txt">基本信息</span>
          <el-link type="primary" :underline="false" class="m-specialimg-pop"
            ><i class="el-icon-picture f-f20 f-mr5 f-vm"></i>查看专题示例<el-image
              class="transparent-pic"
              src="/assets/images/transparent-pic.png"
              :preview-src-list="['/assets/images/demo-special-web-001.png']"
          /></el-link>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20 f-mb40">
              <el-form-item label="专题门户logo类型：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="文字" border class="f-mr10"></el-radio>
                  <el-radio label="图片" border></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="专题门户logo：" required>
                <el-input v-model="form.name" clearable placeholder="请输入当前专题门户显示的名称" />
              </el-form-item>
              <el-form-item label="专题门户logo：" required>
                <el-upload action="#" list-type="picture-card" :auto-upload="false" class="m-pic-upload long-pic">
                  <div slot="default" class="upload-placeholder">
                    <i class="el-icon-plus"></i>
                    <p class="txt">上传logo</p>
                  </div>
                  <div slot="file" slot-scope="{ file }" class="img-file">
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                    <div class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </div>
                  </div>
                  <div slot="tip" class="el-upload__tip">
                    <i class="el-icon-warning"></i>
                    <span class="txt"
                      >上传当前专题门户显示的名称图片，请先设计好后再上传，尺寸：宽度850px ，高度80px。
                      <i class="f-link m-specialimg-pop"
                        >查看示例图片
                        <el-image
                          class="transparent-pic"
                          src="/assets/images/transparent-pic.png"
                          :preview-src-list="['/assets/images/demo-special-logo.png']"
                      /></i>
                    </span>
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item label="客服电话图片：" required>
                <el-upload action="#" list-type="picture-card" :auto-upload="false" class="m-pic-upload long-pic">
                  <div slot="default" class="upload-placeholder">
                    <i class="el-icon-plus"></i>
                    <p class="txt">上传图片</p>
                  </div>
                  <div slot="file" slot-scope="{ file }" class="img-file">
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                    <div class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </div>
                  </div>
                  <div slot="tip" class="el-upload__tip">
                    <i class="el-icon-warning"></i>
                    <span class="txt">
                      上传客服电话图片，请先设计好后再上传，尺寸：295px * 90px。
                      <i class="f-link" @click="dialog2 = true">查看示例图片</i>
                    </span>
                    <!--示例图片弹窗-->
                    <el-dialog :visible.sync="dialog2" width="1100px" class="m-dialog-pic">
                      <img src="./assets/images/demo-special-tel.png" alt="" />
                    </el-dialog>
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item label="客服电话类型：">
                <el-radio-group v-model="form.resource">
                  <el-radio label="同本网校" border class="f-mr10"></el-radio>
                  <el-radio label="自定义" border></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="客服电话：" required>
                <el-input
                  v-model="form.name"
                  clearable
                  placeholder="请输入当前专题门户右侧息停区域显示的客服电话信息"
                />
                <div class="el-upload__tip">
                  <i class="el-icon-warning"></i>
                  <span class="txt">
                    输入专题门户右侧息停区域显示的客服电话，如需展示多个客服电话，请用顿号“、”分隔。例如96882301、96882302
                  </span>
                </div>
              </el-form-item>
              <el-form-item label="咨询时间：">
                <el-input
                  v-model="form.name"
                  clearable
                  placeholder="请输入当前专题门户右侧息停区域显示的客服咨询时间信息"
                />
              </el-form-item>
              <el-form-item label="培训流程：">
                <el-upload action="#" list-type="picture-card" :auto-upload="false" class="m-pic-upload long-pic">
                  <div slot="default" class="upload-placeholder">
                    <i class="el-icon-plus"></i>
                    <p class="txt">上传培训流程图片</p>
                  </div>
                  <div slot="file" slot-scope="{ file }" class="img-file">
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                    <div class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </div>
                  </div>
                  <div slot="tip" class="el-upload__tip">
                    <i class="el-icon-warning"></i>
                    <span class="txt">
                      上传培训流程图片，尺寸：1200px * 100px。
                      <i class="f-link" @click="dialog3 = true">查看示例图片</i>
                    </span>
                    <!--示例图片弹窗-->
                    <el-dialog :visible.sync="dialog3" width="1100px" class="m-dialog-pic">
                      <img src="./assets/images/demo-special-process-default.jpg" alt="" />
                    </el-dialog>
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item label="企业微信客服：">
                <el-radio-group v-model="form.resource">
                  <el-radio label="同本网校" border class="f-mr10"></el-radio>
                  <el-radio label="自定义" border></el-radio>
                </el-radio-group>
                <el-upload action="#" list-type="picture-card" :auto-upload="false" class="m-pic-upload f-mt20">
                  <div slot="default" class="upload-placeholder">
                    <i class="el-icon-plus"></i>
                    <p class="txt">上传企业微信客服图片</p>
                  </div>
                  <div slot="file" slot-scope="{ file }" class="img-file">
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                    <div class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </div>
                  </div>
                  <div slot="tip" class="el-upload__tip">
                    <i class="el-icon-warning"></i>
                    <span class="txt">上传企业微信客服图片，尺寸不小于：160px * 160px。</span>
                  </div>
                </el-upload>
                <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                  <img :src="dialogImageUrl" alt="" />
                </el-dialog>
              </el-form-item>
              <!--<el-form-item label="咨询时间：">-->
              <!--  <el-input-->
              <!--    v-model="form.name"-->
              <!--    clearable-->
              <!--    placeholder="请输入当前专题门户右侧息停区域显示的客服咨询时间信息"-->
              <!--  />-->
              <!--</el-form-item>-->
              <el-form-item label="底部落款类型：">
                <el-radio-group v-model="form.resource">
                  <el-radio label="同本网校" border class="f-mr10"></el-radio>
                  <el-radio label="自定义" border></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="底部落款：" required>
                <el-input type="textarea" :rows="10" placeholder="请输入内容" v-model="textarea"> </el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">轮播图配置</span>
        </div>
        <div class="f-plr20 f-mt20">
          <el-tabs v-model="activeName" type="card" class="m-tab-card is-badge">
            <el-tab-pane label="" name="first">
              <div slot="label">web端<el-badge value="未配置" class="u-badge"></el-badge></div>
              <el-card shadow="never" class="m-card f-mb15">
                <div class="f-p15">
                  <el-button type="primary" icon="el-icon-plus" class="f-mb15">添加轮播图</el-button>
                  <!--表格-->
                  <el-table stripe :data="tableData" max-height="500px" class="m-table">
                    <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                    <el-table-column label="排序" min-width="80" align="center">
                      <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
                    </el-table-column>
                    <el-table-column label="轮播图" min-width="450">
                      <template>
                        <el-image
                          class="web-banner"
                          src="/assets/images/web-default-banner.jpg"
                          :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column label="链接地址" min-width="280">
                      <template>https://www.baidu.com/</template>
                    </el-table-column>
                    <el-table-column label="创建时间" min-width="170">
                      <template>2020-11-11 12:20:20</template>
                    </el-table-column>
                    <el-table-column label="操作" width="150" align="center" fixed="right">
                      <template>
                        <el-button type="text" size="mini">修改</el-button>
                        <el-button type="text" size="mini">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-card>
            </el-tab-pane>
            <el-tab-pane label="" name="second">
              <div slot="label">H5端<el-badge value="未配置" class="u-badge"></el-badge></div>
              <el-card shadow="never" class="m-card f-mb15">
                <div class="f-p15">
                  <el-button type="primary" icon="el-icon-plus" class="f-mb15">添加轮播图</el-button>
                  <!--表格-->
                  <el-table stripe :data="tableData" max-height="500px" class="m-table">
                    <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                    <el-table-column label="排序" min-width="80" align="center">
                      <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
                    </el-table-column>
                    <el-table-column label="轮播图" min-width="450">
                      <template>
                        <el-image
                          class="web-banner"
                          src="/assets/images/web-default-banner.jpg"
                          :preview-src-list="['/assets/images/web-default-banner.jpg']"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column label="链接地址" min-width="280">
                      <template>https://www.baidu.com/</template>
                    </el-table-column>
                    <el-table-column label="创建时间" min-width="170">
                      <template>2020-11-11 12:20:20</template>
                    </el-table-column>
                    <el-table-column label="操作" width="150" align="center" fixed="right">
                      <template>
                        <el-button type="text" size="mini">修改</el-button>
                        <el-button type="text" size="mini">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="m-btn-bar f-tc is-sticky f-pt15" style="z-index: 1999">
          <el-button>取 消</el-button>
          <el-button>返回上一步</el-button>
          <el-button type="primary">保存并进入下一步</el-button>
        </div>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
