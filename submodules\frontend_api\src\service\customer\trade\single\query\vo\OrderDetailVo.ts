import MyOrderVo from '@api/service/customer/trade/single/query/vo/MyOrderVo'
import ReturnOrderResponseVo from '@api/service/customer/trade/single/query/vo/ReturnOrderResponseVo'
import { ReturnOrderRequestVo } from '@api/service/customer/trade/single/query/vo/ReturnOrderRequestVo'
import tradeQueryGateway, {
  ReturnOrderSortField,
  ReturnSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import MsLearningQueryFrontGatewayCourseLearningForestage, {
  LearningRegisterRequest,
  StudentSchemeLearningRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import TradeModule from '@api/service/customer/trade/TradeModule'

export default class OrderDetailVo extends MyOrderVo {
  /*
   *    获取参训资格id，仅在已报名状态才能获取
   * */
  async getQualificationId() {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    const firstOrder = this.subOrderItems[0]
    request.learningRegister.sourceType = firstOrder.currentCommoditySourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
    request.learningRegister.sourceId = firstOrder.currentCommoditySourceId

    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      return res.data.currentPageData[0].qualificationId
    }
    return ''
  }
}
