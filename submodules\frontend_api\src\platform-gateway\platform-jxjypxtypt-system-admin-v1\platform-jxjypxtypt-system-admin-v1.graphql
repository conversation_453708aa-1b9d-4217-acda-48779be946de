schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""运营管理 创建管理员
		@param request
		@return
	"""
	createSystemAdministrator(request:CreateSystemAdministratorRequest):GeneralResponse
	"""更新系统管理员
		@param request
	"""
	updateSystemAdministrator(request:UpdateSystemAdministratorRequest):GeneralResponse
}
input CreateSystemAdministratorRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.systemadmin.CreateSystemAdministratorRequest") {
	"""姓名【必填】"""
	name:String!
	"""手机号"""
	phone:String!
	"""添加的角色id集合【必填】"""
	addRoleIds:[String]!
	"""管理员类型
		注：
		创建系统管理员时，  传 4
		创建供应商管理员时，传 6
		创建分销商管理员时，传 7
	"""
	adminType:Int!
}
input UpdateSystemAdministratorRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.systemadmin.UpdateSystemAdministratorRequest") {
	"""账户id【必填】"""
	accountId:String!
	"""姓名【必填】"""
	name:String!
	"""添加的角色id集合"""
	addRoleIds:[String]
	"""移除的角色id集合"""
	removeRoleIds:[String]
}
"""<AUTHOR> [2023/7/11 20:54]"""
type GeneralResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.GeneralResponse") {
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}

scalar List
