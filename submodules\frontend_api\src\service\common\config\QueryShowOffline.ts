import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import { default as ConfigCenterModule } from '@api/service/common/config/ConfigCenterModule'

class QueryShowOffline {
  /**
   * 获取阿波罗配置
   */
  getShowOfflineApolloConfig() {
    const show = ConfigCenterModule.getFrontendApplication(frontendApplication.hideOnlineAndOfflineTeach)
    return !show || show !== 'false'
  }
}

export default new QueryShowOffline()
