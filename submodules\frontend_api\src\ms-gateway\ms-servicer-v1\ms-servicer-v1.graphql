"""独立部署的微服务,K8S服务名:ms-servicer-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询签约状态
		@param request
	"""
	getContractStatus(request:CVendorForTInstitutionRequest):Int @optionalLogin
	"""根据当前请求头中的分销商的ServicerProvider来获取网校的ServicerProvider
		@return
	"""
	getOnlineSchoolServicerProviderByHeader:ServicerTokenResponse @optionalLogin
}
type Mutation {
	"""企业新增 二级栏目
		栏目名称已存在 705
		该一级栏目已发布过资讯，请将资讯移除后再添加二级栏目通过校验即可成功添加二级栏目！ 701
	"""
	addSecondEnterpriseServicerMenu(request:MenuCreateRequest):GeneralMutationResponse
	"""院校新增 二级栏目
		栏目名称已存在 705
		该一级栏目已发布过资讯，请将资讯移除后再添加二级栏目通过校验即可成功添加二级栏目！ 701
	"""
	addSecondMechanicAcademyMenu(request:MenuCreateRequest):GeneralMutationResponse
	"""服务商申请服务凭证（获取服务商对应服务提供商信息）
		@param servicerId 服务商编号
		@return 服务凭证
	"""
	applyForService(servicerId:String):ServicerTokenResponse @optionalLogin
	"""域名申请服务商凭证
		@param domainName 域名
		@return 服务凭证
	"""
	applyForServiceByDomainName(domainName:String):ServicerTokenResponse @optionalLogin
	"""取消培训机构下的某个培训班的所有渠道商授权
		@param cancelAllCVendorAuthParam 取消授权参数
	"""
	cancelAllCVendorAuthPromotionTraining(cancelAllCVendorAuthParam:CancelAllCVendorAuthPromotionTrainingRequest):Void
	"""网校创建内置的栏目（自用）
		@param
		@return
	"""
	createBuildInPlate(request:BuildInPlateCreateRequest):GeneralMutationResponse @optionalLogin
	"""创建平台下渠道商
		<p>
		该方法仅创建平台下渠道商类型的服务商
		@param createRequest 渠道商信息
		@return 渠道商编号
	"""
	createChannelVendor(createRequest:ChannelVendorCreateRequest):String
	"""创建平台下课件供应商
		<p>
		该方法仅创建平台下课件供应商类型的服务商
		@param createRequest 课件供应商信息
		@return 课件供应商编号
	"""
	createCoursewareSupplier(createRequest:CoursewareSupplierCreateRequest):String
	"""网校新增一级栏目（自用）
		@param request
		@return
	"""
	createJxjyCommonMenu(request:CommonMenuCreateRequest):String @optionalLogin
	"""创建咨询分类栏目
		@param request
	"""
	createNewsCategoryTrainingInstitutionMenu(request:TrainingInstitutionMenuCreateRequest):String
	"""创建平台下参训单位
		<p>
		该方法仅创建平台下参训单位类型的服务商
		@param createRequest 参训单位信息
		@return 参训单位编号
	"""
	createParticipatingUnit(createRequest:ParticipatingUnitCreateRequest):String
	"""创建平台下培训机构
		<p>
		该方法仅创建平台下培训机构类型的服务商
		@param request 培训机构信息
		@return 培训机构编号
	"""
	createTrainingInstitution(request:TrainingInstitutionCreateRequest):String
	"""创建培训机构门户
		@param request 门户信息
		@return 门户编号
	"""
	createTrainingInstitutionPortal(request:TrainingInstitutionPortalCreateRequest):String
	"""企业删除 栏目
		该栏目的资讯分类下已存在相关资讯，无法删除！ 700
		该栏目下含有子级，不能删除 1004
	"""
	deleteEnterpriseServicerMenu(menuId:String):GeneralMutationResponse
	"""院校删除 栏目
		该栏目的资讯分类下已存在相关资讯，无法删除！ 700
		该栏目下含有子级，不能删除 1004
	"""
	deleteMechanicAcademyMenu(menuId:String):GeneralMutationResponse
	"""删除栏目
		@param menuId
	"""
	deleteTrainingInstitutionMenu(menuId:String):Void
	"""停用合同供应商
		@param request
	"""
	disableContractProvider(request:ContractProviderDisableRequest):Void
	"""禁用培训机构
		@param request
	"""
	disableTrainingInstitution(request:TrainingInstitutionDisableRequest):Void
	"""停用栏目
		@param menuId
	"""
	disableTrainingInstitutionMenu(menuId:String):Void
	"""启用合同供应商
		@param request
	"""
	enableContractProvider(request:ContractProviderEnableRequest):Void
	"""企业启用 禁用栏目"""
	enableEnterpriseServicerMenu(request:MenuEnableRequest):GeneralMutationResponse
	"""企业启用 禁用板块
		@param request
	"""
	enableEnterpriseServicerPlate(request:PlateEnableRequest):Void
	"""院校启用 禁用栏目"""
	enableMechanicAcademyMenu(request:MenuEnableRequest):GeneralMutationResponse
	"""启用培训机构
		@param request
	"""
	enableTrainingInstitution(request:TrainingInstitutionEnableRequest):Void
	"""启用栏目
		@param menuId
	"""
	enableTrainingInstitutionMenu(menuId:String):Void
	"""院校启用 禁用板块
		@param request
	"""
	enableTrainingInstitutionPlate(request:PlateEnableRequest):Void
	"""根据单位id查询服务商信息"""
	getServicerInfoByUnitId(unitId:String):[ServicerInfoByQueryResponse]
	"""发布培训机构门户 企业"""
	publishEnterprisePortal(request:PublishEnterprisePortalRequest):Void
	"""发布培训机构门户"""
	publishTrainingInstitutionPortal(request:PublishTrainingInstitutionPortalRequest):Void
	"""移除培训机构门户
		@param request 门户信息
	"""
	removeTrainingInstitutionPortal(request:TrainingInstitutionPortalRemoveRequest):Void
	"""培训机构恢复与渠道商合作
		@param request 恢复信息
	"""
	resumeWithChannelVendorSigned(request:ResumeSignedCVendorForTInstitutionRequest):Void
	"""培训机构恢复与课件供应商合作
		@param request 恢复信息
	"""
	resumeWithCoursewareSupplierSigned(request:ResumeSignedCSupplierForTInstitutionRequest):Void
	"""参训单位恢复与培训机构合作
		@param request 恢复信息
	"""
	resumeWithTrainingInstitutionSigned(request:ResumeSignedTInstitutionForPUnitRequest):Void
	"""保存轮播图集合（请求的轮播图列表将完全覆盖原先的所有轮播图） 企业
		@param request 保存轮播图集合请求
	"""
	saveEnterpriseServicerBannerList(request:TrainingInstitutionBannerListSaveRequest):Void
	"""保存轮播图集合（请求的轮播图列表将完全覆盖原先的所有轮播图）
		@param request 保存轮播图集合请求
	"""
	saveTrainingInstitutionBannerList(request:TrainingInstitutionBannerListSaveRequest):Void
	"""培训机构签约自己的渠道商
		<p>
		签约培训机构（甲方）与渠道商关系（乙方）
		@param request 签约信息
		@return 签约编号
	"""
	signUpChannelVendor(request:SignUpCVendorForTInstitutionRequest):String
	"""培训机构签约自己的课件供应商
		<p>
		签约培训机构（甲方）与课件供应商关系（乙方）
		@param request 签约信息
		@return 签约编号
	"""
	signUpCoursewareSupplier(request:SignUpCSupplierForTInstitutionRequest):String
	"""参训单位签约培训机构(参训单位角色操作)
		<p>
		培训机构（乙方）与参训单位关系（甲方）
		@param request 签约信息
		@return 签约编号
	"""
	signUpTrainingInstitution(request:SignUpTInstitutionForPUnitRequest):String
	"""企业 排序板块"""
	sortEnterpriseServicerPlate(request:PlateSortRequest):GeneralMutationResponse
	"""院校 排序板块"""
	sortTrainingInstitutionPlate(request:PlateSortRequest):GeneralMutationResponse
	"""培训机构中止与渠道商合作
		@param request 中止信息
	"""
	suspendWithChannelVendorSigned(request:SuspendSignedCVendorForTInstitutionRequest):Void
	"""培训机构中止与课件供应商合作
		@param request 中止信息
	"""
	suspendWithCoursewareSupplierSigned(request:SuspendSignedCSupplierForTInstitutionRequest):Void
	"""参训单位中止与培训机构合作
		@param request 中止信息
	"""
	suspendWithTrainingInstitutionSigned(request:SuspendSignedTInstitutionForPUnitRequest):Void
	"""取消发布培训机构门户 企业"""
	unpublishEnterprisePortal(request:UnpublishEnterprisePortalRequest):Void
	"""取消发布培训机构门户"""
	unpublishTrainingInstitutionPortal(request:UnpublishTrainingInstitutionPortalRequest):Void
	"""更新平台下渠道商
		<p>
		该方法仅更新平台下渠道商类型服务商的信息
		@param updateRequest 渠道商信息
	"""
	updateChannelVendor(updateRequest:ChannelVendorUpdateRequest):Void
	"""更新平台下课件供应商
		<p>
		该方法仅更新平台下课件供应商类型服务商的信息
		@param updateRequest 课件供应商信息
	"""
	updateCoursewareSupplier(updateRequest:CoursewareSupplierUpdateRequest):Void
	"""更新企业H5门户基本配置"""
	updateEnterpriseH5Portal(request:EnterpriseH5PortalUpdateRequest):GeneralMutationResponse
	"""企业更新栏目
		栏目名称已存在 705
	"""
	updateEnterpriseServicerMenu(request:MenuUpdateRequest):GeneralMutationResponse
	"""企业更新板块
		板块名称已存在 705
	"""
	updateEnterpriseServicerPlate(request:PlateUpdateRequest):GeneralMutationResponse
	"""更新企业web门户
		@param request
	"""
	updateEnterpriseWebPortal(request:WebPortalUpdateRequest):GeneralMutationResponse
	"""更新院校H5门户基本配置"""
	updateMechanicAcademyH5Portal(request:MechanicAcademyH5PortalUpdateRequest):GeneralMutationResponse
	"""院校更新栏目
		栏目名称已存在 705
	"""
	updateMechanicAcademyMenu(request:MenuUpdateRequest):GeneralMutationResponse
	"""更新培训机构web门户
		@param request
	"""
	updateMechanicAcademyWebPortal(request:WebPortalUpdateRequest):GeneralMutationResponse
	"""更新平台下参训单位
		<p>
		该方法仅更新平台下参训单位类型服务商的信息
		@param updateRequest 参训单位信息
	"""
	updateParticipatingUnit(updateRequest:ParticipatingUnitUpdateRequest):Void
	"""更新平台下培训机构
		<p>
		该方法仅更新平台下培训机构类型服务商的信息
		@param request 培训机构信息
	"""
	updateTrainingInstitution(request:TrainingInstitutionUpdateRequest):Void
	"""更新培训机构H5门户 开关
		@param request 更新培训机构H5门户请求
	"""
	updateTrainingInstitutionH5Portal(request:TrainingInstitutionH5PortalUpdateRequest):Void
	"""更新栏目"""
	updateTrainingInstitutionMenu(request:TrainingInstitutionMenuUpdateRequest):Void
	"""院校更新板块
		板块名称已存在 705
	"""
	updateTrainingInstitutionPlate(request:PlateUpdateRequest):GeneralMutationResponse
	"""更新培训机构门户
		@param request 门户信息
	"""
	updateTrainingInstitutionPortal(request:TrainingInstitutionPortalUpdateRequest):Void
	"""更新培训机构web门户
		@param request
	"""
	updateTrainingInstitutionWebPortal(request:TrainingInstitutionWebPortalUpdateRequest):Void
	"""更新门户主题颜色请求
		@param request
	"""
	updateTrainingInstitutionWebPortalThemeColor(request:TrainingInstitutionPortalThemeColorUpdateRequest):Void
	"""验证培训机构下对已签约的渠道商授权推广班级是否有效
		@param verifyAuthParam 验证授权参数
	"""
	verifyAuthCVendorPromotionTrainingEffective(verifyAuthParam:VerifyAuthCVendorPromotionTrainingRequest):Boolean!
	"""验证培训机构与渠道商签约是否有效
		@param request 参数
	"""
	verifyContractEffective(request:CVendorForTInstitutionRequest):Boolean! @optionalLogin
}
"""<AUTHOR>
	@description:板块
	@date 2024/2/27 20:25
"""
input Plate @type(value:"com.fjhb.ms.servicer.v1.api.command.entities.Plate") {
	"""板块id"""
	id:String
	"""板块名称"""
	name:String
	"""栏目展示名称"""
	disPlayName:String
	"""父板块id"""
	parentId:String
	"""板块类型
		@see PlateTypes
	"""
	plateType:Int!
	"""来源类型
		@see PlateSourceTypes
	"""
	sourceType:Int!
	"""链接"""
	link:String
	"""业务code"""
	code:String
	"""引用id(咨询类型的栏目，引用的是资讯分类id)"""
	referenceId:String
	"""是否允许添加子级"""
	allowChildren:Boolean!
	"""是否启用"""
	enable:Boolean!
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
}
"""友情链接请求
	<AUTHOR>
"""
input FriendLinkRequest @type(value:"com.fjhb.ms.servicer.v1.api.command.request.FriendLinkRequest") {
	"""友情链接标题"""
	title:String
	"""友情链接图片"""
	picture:String
	"""友情链接类型"""
	friendLinkType:Int!
	"""链接"""
	link:String
	"""排序"""
	sort:Int
}
"""客户端
	<AUTHOR>
"""
input Client @type(value:"com.fjhb.ms.servicer.v1.kernel.aggregate.traininginstitution.entities.Client") {
	"""客户端类型
		@see ClientTypes
	"""
	clientType:Int!
	"""门户类型
		@see PortalTypes
	"""
	portalType:Boolean!
	"""域名类型
		@see DomainNameTypes
	"""
	domainNameType:Int!
	"""域名"""
	domainName:String
	"""前端模板id"""
	portalTemplateId:String
	"""cnzz信息"""
	cnzz:String
	"""目录名"""
	dirName:String
}
"""友情链接
	<AUTHOR>
"""
input FriendLink @type(value:"com.fjhb.ms.servicer.v1.kernel.aggregate.traininginstitution.entities.FriendLink") {
	"""友情链接id"""
	id:String
	"""友情链接标题"""
	title:String
	"""友情链接图片"""
	picture:String
	"""友情链接类型"""
	friendLinkType:Int!
	"""链接"""
	link:String
	"""排序"""
	sort:Int
	"""创建时间"""
	createTime:DateTime
}
input CVendorForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.CVendorForTInstitutionRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String
	"""渠道商编号"""
	channelVendorId:String
}
input CancelAllCVendorAuthPromotionTrainingRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.CancelAllCVendorAuthPromotionTrainingRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String
	"""培训班编号"""
	trainingId:String
}
"""渠道商创建信息
	<AUTHOR>
	@since 2021/7/7
"""
input ChannelVendorCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ChannelVendorCreateRequest") {
	"""渠道商名称"""
	name:String!
	"""所属企业账户编号"""
	accountId:String!
	"""所在地区"""
	region:String
	"""渠道商LOGO"""
	logo:String
	"""联系人"""
	contactPerson:String
	"""手机号"""
	phone:String
	"""渠道商简介"""
	abouts:String
	"""渠道商介绍"""
	content:String
	"""电子公章文件路径"""
	electronicSealPath:String
}
"""渠道商更新信息
	<AUTHOR>
	@since 2021/7/7
"""
input ChannelVendorUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ChannelVendorUpdateRequest") {
	"""渠道商编号"""
	channelVendorId:String!
	"""渠道商名称，为null，表示不更新"""
	name:String
	"""所在地区，为null，表示不更新"""
	region:String
	"""渠道商Logo，为null，表示不更新"""
	logo:String
	"""联系人，为null，表示不更新"""
	contactPerson:String
	"""手机号，为null，表示不更新"""
	phone:String
	"""渠道商简介，为null，表示不更新"""
	abouts:String
	"""渠道商介绍，为null，表示不更新"""
	content:String
	"""电子公章文件路径，为null，表示不更新"""
	electronicSealPath:String
}
"""<AUTHOR> [2023/8/3 16:20]"""
input CommonMenuCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.CommonMenuCreateRequest") {
	"""网校id"""
	servicerId:String
	"""单位id"""
	unitId:String
	"""栏目名称"""
	menuName:String
	"""父栏目id"""
	parentId:String
	"""栏目链接"""
	link:String
	"""业务code"""
	code:String
	"""排序"""
	sort:Int!
}
"""合同供应商停用请求"""
input ContractProviderDisableRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ContractProviderDisableRequest") {
	"""合同供应商编号"""
	contractProviderId:String!
}
"""合同供应商启用请求"""
input ContractProviderEnableRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ContractProviderEnableRequest") {
	"""合同供应商编号"""
	contractProviderId:String!
}
"""课件供应商信息
	<AUTHOR>
	@since 2021/7/8
"""
input CoursewareSupplierCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.CoursewareSupplierCreateRequest") {
	"""所属企业主账号编号"""
	accountId:String!
	"""课件供应商名称"""
	name:String!
	"""所在地区"""
	region:String
	"""课件供应商Logo"""
	logo:String
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""课件供应商简介"""
	abouts:String
	"""课件供应商介绍"""
	content:String
	"""电子公章文件路径"""
	electronicSealPath:String
}
"""课件供应商信息
	<AUTHOR>
	@since 2021/7/8
"""
input CoursewareSupplierUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.CoursewareSupplierUpdateRequest") {
	"""课件供应商编号"""
	coursewareSupplierId:String!
	"""课件供应商名称，为null，表示不更新"""
	name:String
	"""所在地区，为null，表示不更新"""
	region:String
	"""课件供应商Logo，为null，表示不更新"""
	logo:String
	"""联系人，为null，表示不更新"""
	contactPerson:String
	"""联系电话，为null，表示不更新"""
	phone:String
	"""课件供应商简介，为null，表示不更新"""
	abouts:String
	"""课件供应商介绍，为null，表示不更新"""
	content:String
	"""电子公章文件路径，为null，表示不更新"""
	electronicSealPath:String
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
	@Author: XuCenHao
	@CreateTime: 2024-03-05  21:10
	@Description:
"""
input EnterpriseH5PortalUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.EnterpriseH5PortalUpdateRequest") {
	"""院校logo"""
	logo:String
	"""首页底图"""
	homepageBasemap:String
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
	@Author: XuCenHao
	@CreateTime: 2024-03-05  21:10
	@Description:
"""
input MechanicAcademyH5PortalUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.MechanicAcademyH5PortalUpdateRequest") {
	"""院校logo"""
	logo:String
	"""首页底图"""
	homepageBasemap:String
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
	@Author: XuCenHao
	@CreateTime: 2024-02-29  15:30
	@Description:
"""
input MenuCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.MenuCreateRequest") {
	"""栏目名称"""
	menuDisPlayName:String!
	"""父栏目id"""
	menuParentId:String!
	"""栏目类型"""
	menuType:Int!
	"""栏目链接"""
	menuLink:String!
	"""业务code"""
	code:String!
	"""排序"""
	sort:Int!
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
	@Author: XuCenHao
	@CreateTime: 2024-02-29  15:30
	@Description:
"""
input MenuEnableRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.MenuEnableRequest") {
	"""栏目id"""
	menuId:String!
	"""是否启用"""
	enabled:Boolean!
	"""客户端类型
		@see ClientTypes
	"""
	clientType:Int!
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
	@Author: XuCenHao
	@CreateTime: 2024-03-05  19:23
	@Description:
"""
input MenuUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.MenuUpdateRequest") {
	"""栏目id"""
	menuId:String!
	"""栏目名称"""
	menuDisPlayName:String!
	"""客户端类型
		@see ClientTypes
	"""
	clientType:Int!
	"""父栏目id"""
	menuParentId:String
	"""栏目类型"""
	menuType:Int!
	"""栏目链接"""
	menuLink:String
	"""业务code"""
	code:String
	"""分类"""
	sort:Int!
}
"""参训单位信息"""
input ParticipatingUnitCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ParticipatingUnitCreateRequest") {
	"""所属企业主账号编号"""
	accountId:String!
	"""参训单位名称"""
	name:String!
	"""所在地区"""
	region:String
	"""参训单位Logo"""
	logo:String
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""参训单位简介"""
	abouts:String
	"""参训单位介绍"""
	content:String
	"""电子公章文件路径"""
	electronicSealPath:String
}
"""参训单位信息"""
input ParticipatingUnitUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ParticipatingUnitUpdateRequest") {
	"""参训单位编号"""
	participatingUnitId:String!
	"""参训单位名称，为null，表示不更新"""
	name:String
	"""所在地区，为null，表示不更新"""
	region:String
	"""参训单位Logo，为null，表示不更新"""
	logo:String
	"""联系人，为null，表示不更新"""
	contactPerson:String
	"""联系电话，为null，表示不更新"""
	phone:String
	"""参训单位简介，为null，表示不更新"""
	abouts:String
	"""参训单位介绍，为null，表示不更新"""
	content:String
	"""电子公章文件路径，为null，表示不更新"""
	electronicSealPath:String
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
	@Author: XuCenHao
	@CreateTime: 2024-02-29  15:30
	@Description:
"""
input PlateEnableRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.PlateEnableRequest") {
	"""板块id"""
	plateId:String!
	"""是否启用"""
	enabled:Boolean!
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
	@Author: XuCenHao
	@CreateTime: 2024-02-29  16:16
	@Description:
"""
input PlateSortRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.PlateSortRequest") {
	plateList:[Plate]!
}
"""更新栏目请求
	<AUTHOR>
"""
input PlateUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.PlateUpdateRequest") {
	"""板块id"""
	plateId:String!
	"""板块名称"""
	plateDisPlayName:String!
}
"""门户轮播图
	<AUTHOR>
	@since 2021/7/13
"""
input PortalBanner @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.PortalBanner") {
	"""轮播图编号，新建时可以不填"""
	id:String
	"""轮播图名称"""
	name:String
	"""轮播图路径"""
	path:String
	"""链接地址"""
	link:String
	"""轮播图排序"""
	sort:Int!
	"""是否启用"""
	enable:Boolean!
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
	@Author: XuCenHao
	@CreateTime: 2024-03-15  17:58
	@Description:
"""
input PublishEnterprisePortalRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.PublishEnterprisePortalRequest") {
	"""企业编号"""
	enterpriseId:String!
	"""门户类型
		<p>
		1-WEB
		2-移动端
	"""
	portalType:Int!
}
"""发布培训机构门户请求
	<AUTHOR>
"""
input PublishTrainingInstitutionPortalRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.PublishTrainingInstitutionPortalRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String!
	"""门户类型
		<p>
		1-WEB
		2-移动端
	"""
	portalType:Int!
}
"""培训机构恢复与课件供应商合作信息
	<AUTHOR>
	@since 2021/7/8
"""
input ResumeSignedCSupplierForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ResumeSignedCSupplierForTInstitutionRequest") {
	"""培训机构与课件供应商签约编号"""
	servicerContractId:String
}
"""培训机构恢复与渠道商合作信息
	<AUTHOR>
	@since 2021/7/8
"""
input ResumeSignedCVendorForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ResumeSignedCVendorForTInstitutionRequest") {
	"""培训机构与渠道商签约编号"""
	servicerContractId:String
}
"""参训单位恢复与培训机构合作信息"""
input ResumeSignedTInstitutionForPUnitRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ResumeSignedTInstitutionForPUnitRequest") {
	"""参训单位与培训机构签约编号"""
	servicerContractId:String
}
"""签约成为培训机构的课件供应商合作
	<AUTHOR>
	@since 2021/7/8
"""
input SignUpCSupplierForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.SignUpCSupplierForTInstitutionRequest") {
	"""课件供应商编号"""
	coursewareSupplierId:String
	"""合约内容"""
	content:String
}
"""签约成为培训机构的渠道商合作
	<AUTHOR>
	@since 2021/7/8
"""
input SignUpCVendorForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.SignUpCVendorForTInstitutionRequest") {
	"""渠道商编号"""
	channelVendorId:String
	"""合约内容"""
	content:String
}
input SignUpTInstitutionForPUnitRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.SignUpTInstitutionForPUnitRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String
	"""合约内容"""
	content:String
}
"""培训机构中止与课件供应商合作信息
	<AUTHOR>
	@since 2021/7/8
"""
input SuspendSignedCSupplierForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.SuspendSignedCSupplierForTInstitutionRequest") {
	"""培训机构与课件供应商签约编号"""
	servicerContractId:String
}
"""培训机构中止与渠道商合作信息
	<AUTHOR>
	@since 2021/7/8
"""
input SuspendSignedCVendorForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.SuspendSignedCVendorForTInstitutionRequest") {
	"""培训机构与渠道商签约编号"""
	servicerContractId:String
}
"""参训单位中止与培训机构合作信息"""
input SuspendSignedTInstitutionForPUnitRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.SuspendSignedTInstitutionForPUnitRequest") {
	"""参训单位与培训机构签约编号"""
	servicerContractId:String
}
"""培训机构轮播图列表保存请求
	<AUTHOR>
"""
input TrainingInstitutionBannerListSaveRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionBannerListSaveRequest") {
	"""门户类型"""
	portalType:Int!
	"""培训机构轮播图保存请求"""
	bannerSaveRequestList:[TrainingInstitutionBannerSaveRequest]
}
"""培训机构轮播图保存请求
	<AUTHOR>
"""
input TrainingInstitutionBannerSaveRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionBannerSaveRequest") {
	"""轮播图名称"""
	bannerName:String
	"""轮播图链接"""
	bannerLink:String
	"""轮播图路径"""
	bannerPath:String
	"""分类"""
	sort:Int!
}
"""培训机构创建信息
	<AUTHOR>
	@since 2021/7/7
"""
input TrainingInstitutionCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionCreateRequest") {
	"""培训机构名称"""
	name:String!
	"""所属企业账户编号"""
	accountId:String!
	"""所在地区"""
	region:String
	"""联系人"""
	contactPerson:String
	"""手机号"""
	phone:String
	"""培训机构LOGO"""
	logo:String
	"""培训机构简介"""
	abouts:String
	"""培训机构介绍"""
	content:String
	"""电子公章文件路径"""
	electronicSealPath:String
}
"""培训机构禁用请求"""
input TrainingInstitutionDisableRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionDisableRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String!
}
"""培训机构启用请求"""
input TrainingInstitutionEnableRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionEnableRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String!
}
"""培训机构H门户更新请求
	<AUTHOR>
"""
input TrainingInstitutionH5PortalUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionH5PortalUpdateRequest") {
	"""培训机构id"""
	trainingInstitutionId:String
	"""是否提供服务号"""
	idProvideServiceAccount:Boolean!
}
"""新增咨询类别栏目栏目请求
	<AUTHOR>
"""
input TrainingInstitutionMenuCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionMenuCreateRequest") {
	"""栏目名称"""
	menuName:String
	"""父栏目id"""
	parentId:String
	"""栏目链接"""
	link:String
	"""业务code"""
	code:String
	"""排序"""
	sort:Int!
}
"""更新栏目请求
	<AUTHOR>
"""
input TrainingInstitutionMenuUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionMenuUpdateRequest") {
	"""栏目id"""
	menuId:String
	"""栏目名称"""
	menuName:String
	"""父栏目id"""
	menuParentId:String
	"""栏目类型"""
	menuType:Int!
	"""栏目链接"""
	menuLink:String
	"""业务code"""
	code:String
	"""分类"""
	sort:Int!
}
"""培训机构门户创建信息
	<AUTHOR>
	@since 2021/7/13
"""
input TrainingInstitutionPortalCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionPortalCreateRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String!
	"""门户类型
		<p>
		1-WEB
		2-移动端
	"""
	portalType:Int!
	"""门户标题"""
	title:String
	"""门户logo"""
	logo:String
	"""浏览器图标"""
	icon:String
	"""移动二维码"""
	mobileQRCode:String
	"""客服电话图片"""
	CSPhonePicture:String
	"""客服电话"""
	CSPhone:String
	"""客服咨询时间"""
	CSCallTime:String
	"""企业客服微信"""
	CSWechat:String
	"""在线客服代码内容id"""
	CSOnlineCodeId:String
	"""培训流程图片"""
	trainingFlowPicture:String
	"""底部内容(底部落款)"""
	footContent:String
	"""友情链接类型"""
	friendLinkTypes:Int!
	"""友情链接集合"""
	friendLinks:[FriendLink]
	"""主题颜色"""
	themeColor:String
	"""宣传口号"""
	slogan:String
	"""门户简介说明内容"""
	content:String
	"""域名"""
	domainName:String
	"""轮播图列表"""
	banners:[PortalBanner]
	"""cnzz信息"""
	cnzz:String
	"""目录名"""
	dirName:String
}
"""培训机构门户创建信息
	<AUTHOR>
	@since 2021/7/13
"""
input TrainingInstitutionPortalRemoveRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionPortalRemoveRequest") {
	"""门户编号"""
	id:String!
	"""培训机构编号"""
	trainingInstitutionId:String!
}
"""更新门户（web+h5）主题颜色请求
	<AUTHOR>
"""
input TrainingInstitutionPortalThemeColorUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionPortalThemeColorUpdateRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String!
	"""主题颜色"""
	themeColor:String
}
"""培训机构门户创建信息
	<AUTHOR>
	@since 2021/7/13
"""
input TrainingInstitutionPortalUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionPortalUpdateRequest") {
	"""门户编号"""
	id:String!
	"""培训机构编号"""
	trainingInstitutionId:String!
	"""门户标题"""
	title:String
	"""宣传口号"""
	slogan:String
	"""门户简介说明内容"""
	content:String
	"""域名"""
	domainName:String
	"""轮播图列表"""
	banners:[PortalBanner]
	"""cnzz信息"""
	cnzz:String
	"""目录名"""
	dirName:String
}
"""培训机构创建信息
	<AUTHOR>
	@since 2021/7/7
"""
input TrainingInstitutionUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionUpdateRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String!
	"""培训机构名称，为null，表示不更新"""
	name:String
	"""所在地区，为null，表示不更新"""
	region:String
	"""联系人，为null，表示不更新"""
	contactPerson:String
	"""手机号，为null，表示不更新"""
	phone:String
	"""培训机构LOGO，为null，表示不更新"""
	logo:String
	"""培训机构简介，为null，表示不更新"""
	abouts:String
	"""培训机构介绍，为null，表示不更新"""
	content:String
	"""电子公章文件路径，为null，表示不更新"""
	electronicSealPath:String
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
	@Author: XuCenHao
	@CreateTime: 2024-02-26  15:05
	@Description: 底部落款
"""
input TrainingInstitutionWebPortalFootContent @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionWebPortalFootContent") {
	"""主办单位"""
	organizers:String
	"""主管单位"""
	sponsor:String
	"""运营支持"""
	operationSupport:String
	"""备案号"""
	recordNumber:String
	"""许可证"""
	permit:String
	"""网安备案号"""
	networkSecurityRegistrationNumber:String
}
"""培训机构web门户修改请求
	<AUTHOR>
"""
input TrainingInstitutionWebPortalUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionWebPortalUpdateRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String!
	"""客户端"""
	clients:[Client]
	"""门户标题"""
	title:String
	"""门户logo"""
	logo:String
	"""浏览器图标"""
	icon:String
	"""移动二维码"""
	mobileQRCode:String
	"""移动二维码来源标识
		@see com.fjhb.domain.basicdata.api.servicer.consts.MobileQRCodeSign
	"""
	mobileQRCodeSign:Int
	"""客服电话图片"""
	csPhonePicture:String
	"""客服电话"""
	csPhone:String
	"""客服咨询时间"""
	csCallTime:String
	"""企业客服微信"""
	csWechat:String
	"""在线客服代码内容id"""
	csOnlineCodeId:String
	"""培训流程图片"""
	trainingFlowPicture:String
	"""底部内容id(底部落款id)"""
	footContentId:String
	"""底部内容(底部落款)"""
	footContent:String
	"""底部落款(新)"""
	trainingInstitutionWebPortalFootContent:TrainingInstitutionWebPortalFootContent
	"""友情链接类型
		@see com.fjhb.domain.basicdata.api.servicer.consts.FriendLinkTypes
		1-文本  2-图片
	"""
	friendLinkTypes:Int!
	"""友情链接集合"""
	friendLinks:[FriendLinkRequest]
	"""cnzz信息"""
	cnzz:String
	"""目录名"""
	dirName:String
	"""强制更新（前端传什么，后端就更新什么）"""
	compulsionUpdate:Boolean!
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
	@Author: XuCenHao
	@CreateTime: 2024-03-15  17:58
	@Description:
"""
input UnpublishEnterprisePortalRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.UnpublishEnterprisePortalRequest") {
	"""企业编号"""
	enterpriseId:String!
	"""门户类型
		<p>
		1-WEB
		2-移动端
	"""
	portalType:Int!
}
"""取消发布培训机构门户请求
	<AUTHOR>
"""
input UnpublishTrainingInstitutionPortalRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.UnpublishTrainingInstitutionPortalRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String!
	"""门户类型
		<p>
		1-WEB
		2-移动端
	"""
	portalType:Int!
}
input VerifyAuthCVendorPromotionTrainingRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.VerifyAuthCVendorPromotionTrainingRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String
	"""渠道商编号"""
	channelVendorId:String
	"""培训班编号"""
	trainingId:String
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
	@Author: XuCenHao
	@CreateTime: 2024-02-28  15:05
	@Description:
"""
input WebPortalUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.WebPortalUpdateRequest") {
	"""门户logo"""
	logo:String
	"""门户标题"""
	title:String
	"""移动二维码"""
	mobileQRCode:String
	"""底部落款(新)"""
	trainingInstitutionWebPortalFootContent:TrainingInstitutionWebPortalFootContent
	"""友情链接类型
		@see com.fjhb.domain.basicdata.api.servicer.consts.FriendLinkTypes
		1-文本  2-图片
	"""
	friendLinkTypes:Int!
	"""友情链接集合"""
	friendLinks:[FriendLinkRequest]
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
	@Author: XuCenHao
	@CreateTime: 2024-04-10  11:30
	@Description:
"""
input BuildInPlateCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.buildin.BuildInPlateCreateRequest") {
	"""key为服务商id，value为板块,
		如果是一级，必须传name和allowChildren
		如果是子级的话parentId必须传。必须传name，
		用法：如果要内置好多级，先内置一级，拿到一级的id，放到下一级的parentId中请求
	"""
	list:[BuildInPlateCreateRequestDto]
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.api.command.traininginstitution.buildin
	@Author: XuCenHao
	@CreateTime: 2024-04-10  19:46
	@Description:
"""
input BuildInPlateCreateRequestDto @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.buildin.BuildInPlateCreateRequestDto") {
	servicerId:String
}
"""服务商信息查询返回类"""
type ServicerInfoByQueryResponse @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.response.ServicerInfoByQueryResponse") {
	"""服务商id"""
	servicerId:String
	"""服务商名称"""
	servicerName:String
	"""服务商类别"""
	servicerType:Int
	"""服务凭证token"""
	token:String
}
"""服务方信息元数据
	<AUTHOR>
	@since 2021/7/27
"""
type ServicerTokenMetaResponse @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.response.ServicerTokenMetaResponse") {
	"""服务商编号"""
	servicerId:String
	"""服务商类型"""
	servicerType:Int!
	"""服务商状态"""
	status:Int!
	"""服务商所属单位编号"""
	unitId:String
}
"""服务凭证
	<AUTHOR>
	@since 2021/7/27
"""
type ServicerTokenResponse @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.response.ServicerTokenResponse") {
	"""服务凭证"""
	token:String
	"""服务凭证元数据"""
	tokenMeta:ServicerTokenMetaResponse
}
"""@BelongsProject: fjhb-microservice-servicer
	@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.response
	@Author: XuCenHao
	@CreateTime: 2024-03-07  16:31
	@Description:
"""
type GeneralMutationResponse @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.response.basic.GeneralMutationResponse") {
	"""@see GeneralMutationEnum"""
	code:Int!
	"""@see IBusinessCode"""
	businessCode:Int!
	errorMessage:String
	payload:String
}

scalar List
