import { Page } from '@hbfe/common'
import CourseLearningBackstage, {
  CourseSalesStatisticsInfoTotal,
  CourseSalesStatisticsRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import ChooseCourseStatisticDetail from '@api/service/management/statisticalReport/query/vo/ChooseCourseStatisticDetail'
import ExportDataExportBackstage from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import ResourceModule from '@api/service/management/resource/ResourceModule'

class TrainingUnitSalesStatistics {
  // * 培训单位销售总数统计
  courseSalesStatisticsInfoTotal = new CourseSalesStatisticsInfoTotal()

  /**
   * 查询培训单位销售统计列表
   * @param page
   * @param request
   */
  async queryTrainingUnitSalesStatisticsLait(page: Page, request: CourseSalesStatisticsRequest) {
    const response = await CourseLearningBackstage.pageCourseSalesStatistics({ page, request })
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('获取培训单位销售统计失败', response)
      return Promise.reject(response)
    }
    this.courseSalesStatisticsInfoTotal = response.data.courseSalesStatisticsInfoTotal
    let providerIds = response.data.data.currentPageData.map(res => res.coursewareSupplierId)
    providerIds = Array.from(new Set(providerIds))
    const providerInfo = await ResourceModule.courseFactory.queryCourse.batchQueryProvider(providerIds)
    let temp = ChooseCourseStatisticDetail.from(response.data.data.currentPageData)
    temp = temp.map(res => ChooseCourseStatisticDetail.addProviderInfo(res, providerInfo))
    page.totalPageSize = response.data.data.totalPageSize
    page.totalSize = response.data.data.totalSize
    return temp
  }

  /**
   * 导出培训单位销售统计列表
   * @param request
   */
  async exportTrainingUnitSalesStatisticsLait(request: CourseSalesStatisticsRequest) {
    const response = await ExportDataExportBackstage.exportCourseSalesStatistics(request)
    if (response.status.code !== 200 || !response.status.isSuccess()) {
      console.error('导出选课统计报错', response)
      return response
    }
    return true
  }
}

export default TrainingUnitSalesStatistics
