<route-meta>
{
"isMenu": true,
"title": "华医网组合开通统计",
"sort": 15,
"icon": "icon-ribaotongji"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="pageScheme,@BizDistributorSelect,@BizPortalSelect">
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->

        <hb-search-wrapper @reset="resetQueryParam" class="m-query is-border-bottom">
          <el-form-item label="分销推广">
            <biz-promotion-select v-model="isFx"></biz-promotion-select>
          </el-form-item>
          <el-form-item label="分销商" v-if="isOpenFxServer">
            <biz-distributor-select
              v-model="tradeReportRequest.distributorId"
              :name="distributorName"
            ></biz-distributor-select>
          </el-form-item>
          <el-form-item label="推广门户简称" v-if="isOpenFxServer">
            <biz-portal-select
              :disabled="tradeReportRequest.notDistributionPortal"
              v-model="portalId"
              :name="portalName"
            ></biz-portal-select>
          </el-form-item>
          <el-form-item label="报名时间">
            <double-date-picker
              :begin-create-time.sync="tradeReportRequest.tradeTime.begin"
              :end-create-time.sync="tradeReportRequest.tradeTime.end"
              beginTimePlaceholder="请选择报名成功时间"
              endTimePlaceholder="请选择报名成功时间"
              endDefaultTime="23:59:59"
            ></double-date-picker>
          </el-form-item>

          <!-- // 华医差异化 -->
          <el-form-item label="专题">
            <biz-special-select v-model="saleChannels" @input="changeSaleChannels"></biz-special-select>
          </el-form-item>
          <el-form-item label="专题名称">
            <biz-special-name v-model="tradeReportRequest.saleChannelName"></biz-special-name>
          </el-form-item>
          <el-form-item label="销售渠道">
            <biz-sale-channel-select v-model="tradeChannels"></biz-sale-channel-select>
          </el-form-item>
          <el-form-item v-if="isOpenFxServer">
            <el-checkbox
              label="查看非门户推广数据"
              name="type"
              v-model="tradeReportRequest.notDistributionPortal"
            ></el-checkbox>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="trainSchemePage.currentChange(1)">查询</el-button>
            <el-button v-if="$hasPermission('export')" desc="导出" actions="exportListData" @click="exportListData"
              >导出列表数据</el-button
            >
            <!-- <el-button>重置</el-button> -->
          </template>
        </hb-search-wrapper>

        <!--操作栏-->
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert f-clear">
            <div class="f-c6 f-fl">
              搜索结果合计：当前净开通
              <span class="f-fb f-co">{{
                queryTrainClassReportList.statisticsM.tradeCountSummaryInfo.netTradeSuccessCount
              }}</span>
              人次，成交总额
              <span class="f-fb f-co">¥ {{ queryTrainClassReportList.statisticsM.totalNetAmount }}</span>
            </div>
            <!-- <div class="f-fr f-csp f-flex f-align-center" @click="dialog5 = true">
                <i class="el-icon-info f-f16 f-mr5"></i>统计口径说明
              </div> -->
          </el-alert>
        </div>
        <!--表格-->
        <el-table
          v-loading="tableLoading"
          stripe
          :data="commodityOpenReportFormResponseVo"
          border
          show-summary
          :summary-method="getSummaries"
          :span-method="objectSpanMethod"
          max-height="500px"
          class="m-table is-statistical f-mt10"
          ref="schemeTable"
        >
          <el-table-column label="No." width="60" align="center" fixed="left">
            <template slot-scope="scope">
              {{ getIndex(scope.$index) }}
            </template>
          </el-table-column>
          <el-table-column label="年度" min-width="80" fixed="left" align="center">
            <template slot-scope="scope">{{
              scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.year.skuPropertyName || '-'
            }}</template>
          </el-table-column>
          <el-table-column label="培训方案" min-width="240" fixed="left">
            <template slot-scope="scope">
              <el-tag type="primary" effect="dark" size="mini">{{
                getSchemeType(scope.row.trainClassDetail.trainClassBaseInfo.schemeType)
              }}</el-tag
              >{{ scope.row.schemeName }}</template
            >
          </el-table-column>
          <el-table-column label="方案属性" align="center" min-width="200">
            <template slot-scope="scope">
              <p>
                行业:{{ scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.industry.skuPropertyName || '-' }}
              </p>
              <p>
                培训年度:{{ scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.year.skuPropertyName || '-' }}
              </p>
              <p v-if="scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.region">
                地区:{{ scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.region.skuPropertyName }}
              </p>
              <p v-if="accountType(scope.row.type)">科目类型:{{ accountType(scope.row.type) }}</p>
              <p v-if="scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.trainingMajor.skuPropertyName">
                培训专业:{{ scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.trainingMajor.skuPropertyName }}
              </p>
              <p v-if="scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.trainingObject.skuPropertyName">
                培训对象:{{ scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.trainingObject.skuPropertyName }}
              </p>
              <p v-if="scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.positionCategory.skuPropertyName">
                岗位类别:{{
                  scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.positionCategory.skuPropertyName
                }}
              </p>
              <p v-if="scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.jobLevel.skuPropertyName">
                技术等级:{{ scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.jobLevel.skuPropertyName || '-' }}
              </p>
              <!-- 学段 判断 -->
              <!-- 学科 判断 -->
              <p v-if="scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.learningPhase.skuPropertyName">
                学段：{{
                  scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.learningPhase.skuPropertyName || '-'
                }}
              </p>
              <p
                v-if="
                  scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.learningPhase.skuPropertyName &&
                  scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.discipline.skuPropertyName
                "
              >
                学科：{{ scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.discipline.skuPropertyName || '-' }}
              </p>
              <p
                v-if="
                  scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.practitionerCategory.skuPropertyName &&
                  scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.certificatesType.skuPropertyName
                "
              >
                执业类别：{{
                  scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.certificatesType.skuPropertyName || '-'
                }}/{{
                  scope.row.trainClassDetail.trainClassBaseInfo.skuProperty.practitionerCategory.skuPropertyName || '-'
                }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="合计" header-align="center">
            <el-table-column label="开通" prop="summaryInfo.tradeSuccessCount" min-width="90" align="center">
              <template slot-scope="scope">{{ scope.row.summaryInfo.tradeSuccessCount }}</template>
            </el-table-column>
            <el-table-column label="退班" prop="summaryInfo.returnCount" min-width="90" align="center">
              <template slot-scope="scope">{{ scope.row.summaryInfo.returnCount }}</template>
            </el-table-column>
            <el-table-column label="换入(换班)" prop="summaryInfo.exchangeInCount" min-width="90" align="center">
              <template slot-scope="scope">{{ scope.row.summaryInfo.exchangeInCount }}</template>
            </el-table-column>
            <el-table-column label="换出(换班)" prop="summaryInfo.exchangeOutCount" min-width="90" align="center">
              <template slot-scope="scope">{{ scope.row.summaryInfo.exchangeOutCount }}</template>
            </el-table-column>
            <el-table-column label="净开通" prop="summaryInfo.netTradeSuccessCount" min-width="90" align="center">
              <template slot-scope="scope">{{ scope.row.summaryInfo.netTradeSuccessCount }}</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="个人缴费" align="center">
            <el-table-column label="线上支付" align="center">
              <el-table-column label="开通" prop="online.tradeSuccessCount" min-width="90" align="center">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[0].purchaseChannel === 1 &&
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].paymentType === 1
                  "
                >
                  {{
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                      .tradeSuccessCount
                  }}
                </template>
              </el-table-column>
              <el-table-column label="退班" prop="online.returnCount" min-width="90" align="center">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[0].purchaseChannel === 1 &&
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                      .returnCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换入(换班)" prop="online.exchangeInCount" min-width="90" align="center">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[0].purchaseChannel === 1 &&
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                      .exchangeInCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换出(换班)" prop="online.exchangeOutCount" min-width="90" align="center">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[0].purchaseChannel === 1 &&
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                      .exchangeOutCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="净开通" min-width="90" prop="online.netTradeSuccessCount" align="center">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[0].purchaseChannel === 1 &&
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo
                      .netTradeSuccessCount
                  }}</template
                >
              </el-table-column>
            </el-table-column>
          </el-table-column>
          <el-table-column label="集体报名" header-align="center">
            <el-table-column label="线上支付" header-align="center">
              <el-table-column label="开通" prop="team.online.tradeSuccessCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                      .tradeSuccessCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="退班" prop="team.online.returnCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                      .returnCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换入(换班)" prop="team.online.exchangeInCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                      .exchangeInCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换出(换班)" prop="team.online.exchangeOutCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                      .exchangeOutCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="净开通" prop="team.online.netTradeSuccessCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].paymentType === 1
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1].statisticInfo
                      .netTradeSuccessCount
                  }}</template
                >
              </el-table-column>
            </el-table-column>
            <el-table-column label="线下支付" header-align="center">
              <el-table-column label="开通" prop="team.offline.tradeSuccessCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                      .tradeSuccessCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="退班" prop="team.offline.returnCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                      .returnCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换入(换班)" prop="team.offline.exchangeInCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                      .exchangeInCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换出(换班)" prop="team.offline.exchangeOutCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                      .exchangeOutCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="净开通" prop="team.offline.netTradeSuccessCount" min-width="90" align="right">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList.length > 0 &&
                    scope.row.purchaseChannelStatisticInfoList[1].purchaseChannel === 2 &&
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0].statisticInfo
                      .netTradeSuccessCount
                  }}</template
                >
              </el-table-column>
            </el-table-column>
          </el-table-column>
          <el-table-column label="导入开通" header-align="center">
            <el-table-column label="线下支付" header-align="center">
              <el-table-column label="开通" prop="export.tradeSuccessCount" min-width="90" align="center">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList[2].purchaseChannel === 3 &&
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                      .tradeSuccessCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="退班" prop="export.returnCount" min-width="90" align="center">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList[2].purchaseChannel === 3 &&
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                      .returnCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换入(换班)" prop="export.exchangeInCount" min-width="90" align="center">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList[2].purchaseChannel === 3 &&
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                      .exchangeInCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="换出(换班)" prop="export.exchangeOutCount" min-width="90" align="center">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList[2].purchaseChannel === 3 &&
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                      .exchangeOutCount
                  }}</template
                >
              </el-table-column>
              <el-table-column label="净开通" prop="export.netTradeSuccessCount" min-width="90" align="center">
                <template
                  slot-scope="scope"
                  v-if="
                    scope.row.purchaseChannelStatisticInfoList[2].purchaseChannel === 3 &&
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].paymentType === 2
                  "
                  >{{
                    scope.row.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo
                      .netTradeSuccessCount
                  }}</template
                >
              </el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination class="f-mt15 f-tr" :page="trainSchemePage" v-bind="trainSchemePage"> </hb-pagination>
        <el-drawer title="统计口径说明" :visible.sync="dialog5" size="900px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
            </el-alert>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">搜索条件说明</span>
              （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
            </p>
            <el-table stripe :data="tableData6" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">方案属性</div>
                  <div v-else-if="scope.$index === 1">方案类型</div>
                  <div v-else-if="scope.$index === 2">选择方案</div>
                  <div v-else-if="scope.$index === 3">开通时间</div>
                  <div v-else>分销推广</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">选择不同的行业，可以查询不同的培训属性值。属性值为空不展示</div>
                  <div v-else-if="scope.$index === 1">创建培训方案时定义的类型属性，如培训班等</div>
                  <div v-else-if="scope.$index === 2">选择全部已发布的培训方案</div>
                  <div v-else-if="scope.$index === 3">查看在某个开通时间内，培训方案开通数据</div>
                  <div v-else>默认显示所有订单的开通数据，支持选择查看分销订单或非分销订单的开通数据</div>
                </template>
              </el-table-column>
            </el-table>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">列表字段及详细说明</span>
              （列表下的数据显示受搜索条件的约束，统计单位：人次）
            </p>
            <el-table stripe :data="tableData13" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">方案名称</div>
                  <div v-else-if="scope.$index === 1">方案属性</div>
                  <div v-else-if="scope.$index === 2">合计</div>
                  <div v-else-if="scope.$index === 3">个人缴费</div>
                  <div v-else-if="scope.$index === 4">集体报名</div>
                  <div v-else-if="scope.$index === 5">导入开通</div>
                  <div v-else-if="scope.$index === 6">线上缴费</div>
                  <div v-else-if="scope.$index === 7">线下缴费</div>
                  <div v-else-if="scope.$index === 8">开通</div>
                  <div v-else-if="scope.$index === 9">退班</div>
                  <div v-else-if="scope.$index === 10">换入（换班）</div>
                  <div v-else-if="scope.$index === 11">换出（换班）</div>
                  <div v-else>净开通</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">培训方案名称，默认显示全部已发布的方案</div>
                  <div v-else-if="scope.$index === 1">培训方案的属性值展示</div>
                  <div v-else-if="scope.$index === 2">各项数据的合计数值</div>
                  <div v-else-if="scope.$index === 3">学员自主缴费的方式</div>
                  <div v-else-if="scope.$index === 4">学员参加培训的方式通过集体报名的方式</div>
                  <div v-else-if="scope.$index === 5">学员参加培训的方式是通过后台教务人员直接导入</div>
                  <div v-else-if="scope.$index === 6">通过网络在线的形式进行缴费</div>
                  <div v-else-if="scope.$index === 7">通过线下的形式进行缴费</div>
                  <div v-else-if="scope.$index === 8">
                    在开通日期段内，为学员成功开通培训班的人次。缴费成功订单状态为交易成功的培训班则开通数+1
                  </div>
                  <div v-else-if="scope.$index === 9">在开通日期段内，发起退班且退班成功则对应的培训班退班人次+1</div>
                  <div v-else-if="scope.$index === 10">在开通日期段内，发起换入班级且换入成功的人次+1</div>
                  <div v-else-if="scope.$index === 11">在开通日期段内，发起换出班级且换出成功的人次+1</div>
                  <div v-else>
                    净开通=开通+换入-换出-退班；如9.1查8.1-8.31开通数100，退班数2，换出5，换入5，净开通=100+5-2-5=98
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary" @click="dialog5 = false">确定</el-button>
          </div>
        </el-drawer>
        <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务查看-方案开通统计</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  // import {
  //   CommoditySkuRequest,
  //   CommoditySkuRequest12,
  //   CommoditySkuSortRequest,
  //   DateScopeRequest,
  //   DoubleScopeRequest,
  //   RegionSkuPropertyRequest,
  //   RegionSkuPropertySearchRequest,
  //   SchemeRequest1,
  //   SkuPropertyRequest,
  //   SubOrderStatisticDto,
  //   TradeReportRequest
  // } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { Component, Vue } from 'vue-property-decorator'
  import Page from '@hbfe/jxjy-admin-common/src/models/Page'
  // import { TradeReportRequest as TradeReportReq } from '@api/ms-gateway/ms-data-export-front-gateway-DataExportBackstage'
  import { Query, UiPage } from '@hbfe/common'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import { cloneDeep } from 'lodash'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'
  import { QueryTrainClassReportList } from '@api/service/management/statisticalReport/query/QueryTrainClassReportList'
  import QueryHuayiNetworkCombinations from '@api/service/diff/management/zztt/statistical-report/QueryHuayiNetworkCombinations'
  import { CommodityOpenReportFormResponseVo } from '@api/service/management/statisticalReport/query/vo/CommodityOpenReportFormResponseVo'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-common/src/models/sku'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import bizSaleChannelSelect from '@hbfe/jxjy-admin-components/src/biz-sale-channel-select.vue'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import bizSchemeType from '@hbfe/jxjy-admin-trade/src/diff/zztt/order/personal/components/scheme-type-diff.vue'
  import SaleChannelType from '@api/service/common/enums/trade/SaleChannelType'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import {
    CommoditySkuRequest,
    DateScopeRequest,
    DoubleScopeRequest,
    RegionSkuPropertyRequest,
    RegionSkuPropertySearchRequest,
    ZZTTSchemeRequest as SchemeRequest,
    SkuPropertyRequest,
    SubOrderStatisticDto,
    ZZTTTradeReportRequest as TradeReportRequest
  } from '@api/diff-gateway/zztt-trade-query-front-gateway-TradeQueryBackstage'
  import { ZZTTTradeReportRequest as TradeReportReq } from '@api/diff-gateway/zztt-data-export-gateway-backstage'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplicationDiff } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

  @Component({
    components: {
      DoubleDatePicker,
      bizSaleChannelSelect,
      bizSchemeType,
      BizPortalSelect,
      BizDistributorSelect
    }
  })
  export default class extends Vue {
    select = ''
    input = ''
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    page = new Page()
    form = {
      data1: ''
    }
    dialog5 = false
    tableData6 = new Array(5).fill(0)
    tableData13 = new Array(13).fill(0)
    //导出成功弹窗
    exportSuccessVisible = false
    openingstatistics = false
    // 查询参数 - 培训方案
    trainSchemeQueryParam: CommoditySkuRequest

    // 培训方案名称
    schemeName = ''
    // 分页参数 - 培训方案
    trainSchemePage: UiPage
    // 分页加载参数 - 培训方案
    trainSchemeQuery: Query = new Query()
    // 培训方案业务状态层入口
    schemeBusinessEntry: QueryTrainClassCommodityList = new QueryTrainClassCommodityList()
    /**
     * 培训方案类型
     */
    schemeTypeInfo: Array<string> = new Array<string>()
    // 培训方案列表
    trainSchemeList: Array<TrainClassCommodityVo> = new Array<TrainClassCommodityVo>()
    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''
    /**
     * 培训对象Id
     */
    trainingObjectId = ''
    /**
     * 推广门户id
     */
    portalId = ''
    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: false,
      // 培训类别
      trainingCategory: false,
      // 岗位类别
      positionCategory: false,
      // 培训对象
      trainingObject: false,
      // 技术等级
      technicalGrade: false,
      // 学段
      learningPhase: false,
      // 科目
      discipline: false
    }
    /**
     * 当前网校信息
     */
    envConfig = {
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 工勤行业Id
      workServiceId: '',
      // 职业卫生行业Id
      professionHealthIndustryId: '',
      // 教师行业id
      teacherIndustryId: ''
    }
    //门户简介
    portalName = ''
    //分销商名称
    distributorName = ''
    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = {
      /**
       * 年度
       */
      year: [],
      /**
       * 地区
       */
      region: [],
      /**
       * 行业
       */
      industry: '',
      /**
       * 科目类型
       */
      subjectType: '',
      /**
       * 培训类别
       */
      trainingCategory: '',
      /**
       * 培训专业 - 建设行业
       */
      constructionTrainingMajor: '',
      /**
       * 培训专业 - 人社行业
       */
      societyTrainingMajor: [] as string[],
      /**
       * 技术等级
       */
      technicalGrade: '',
      /**
       * 培训对象
       */
      trainingObject: '',
      /**
       * 岗位类别
       */
      positionCategory: '',
      /**
       * 学段
       */
      learningPhase: '',
      /**
       * 学科
       */
      discipline: ''
    } as SchemeSkuProperty
    /**
     * 表格loading
     */
    tableLoading = false
    // queryTrainClassReportList: QueryTrainClassReportList =
    //   StaticticalReportManagerModule.queryStaticticalReportFactory.getQueryTrainClassReportList()
    queryTrainClassReportList: QueryHuayiNetworkCombinations = new QueryHuayiNetworkCombinations()
    tradeReportRequest: TradeReportRequest = new TradeReportRequest()
    /**
     * 是否专题
     */
    isFx: number = null
    saleChannels: boolean = null
    tradeChannels: number = null
    exportQueryParam: TradeReportReq = new TradeReportReq()
    commodityOpenReportFormResponseVo: Array<CommodityOpenReportFormResponseVo> =
      new Array<CommodityOpenReportFormResponseVo>()
    ServiceRegion: RegionTreeVo[] = []

    // 合并二维数组
    spanList = new Array<number>()

    /**
     * 判断当前网校是否开启分销服务
     */
    get isOpenFxServer() {
      return CapabilityServiceConfig.fxCapabilityEnable
    }

    /**
     * 获取华医网特殊方案
     */
    get hywSchemeList(): string[] {
      const list: { zzttSchemeId: string }[] = JSON.parse(
        ConfigCenterModule.getFrontendApplicationDiff(frontendApplicationDiff.hywSchemeId)
      )
      return list.map((item) => item.zzttSchemeId)
    }

    constructor() {
      super()
      this.trainSchemePage = new UiPage(this.pageScheme, this.pageScheme)
    }

    async created() {
      this.initQueryParam()
      await this.pageScheme()
    }
    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      this.saleChannels = null
      this.isFx = null
      this.tradeChannels = null
      this.tradeReportRequest.saleChannels = new Array<number>()
      this.tradeReportRequest.saleChannel = null
      await this.initQueryParam()
      this.localSkuProperty = new SchemeSkuProperty()
      this.schemeName = ''
      this.portalId = null
      this.$set(this.tradeReportRequest, 'notDistributionPortal', false)
      await this.searchBase()
    }
    /**
     * 获取培训方案类型
     */
    getSchemeType(type: string) {
      return SchemeType.getSchemeType(type, true)
    }
    // 选择是否来源专题
    changeSaleChannels() {
      this.tradeReportRequest.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(this.saleChannels)
      //if (this.saleChannels === true) {
      // this.tradeReportRequest.saleChannels = [2]
      // } else if (this.saleChannels === false) {
      //   this.tradeReportRequest.saleChannels = [0, 1]
      // }
    }
    /**
     * 处理列表查询参数
     */
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.tradeReportRequest.commoditySku.saleTitle = this.schemeName || undefined
      this.configureTrainSchemeQueryParam()
    }
    /**
     * 配置查询参数
     */
    configureTrainSchemeQueryParam() {
      console.log(this.schemeTypeInfo, 'schemeTypeInfo')

      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.tradeReportRequest.commoditySku.scheme.schemeType = undefined
      }
      const schemeType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      // if (schemeType === 'chooseCourseLearning' || schemeType === 'autonomousCourseLearning') {
      this.tradeReportRequest.commoditySku.scheme.schemeType = schemeType
      // } else {
      //   this.tradeReportRequest.commoditySku.scheme.schemeType = schemeType
      // }
    }
    /**
     * 获取本地sku选项
     */
    getLocalSkuProperty() {
      const skuProperties = cloneDeep(this.tradeReportRequest.commoditySku.skuProperty)
      skuProperties.year = !this.localSkuProperty.year.length ? ([] as string[]) : this.localSkuProperty.year
      skuProperties.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
      const localRegion = cloneDeep(this.localSkuProperty.region)
      console.log(localRegion, 'localRegion')

      if (Array.isArray(localRegion) && localRegion.length) {
        const option = new RegionSkuPropertyRequest()
        // 与方案相关的 物理地区改为业务地区
        option.province = localRegion.length >= 1 ? localRegion[0] : undefined
        option.city = localRegion.length >= 2 ? localRegion[1] : undefined
        option.county = localRegion.length >= 3 ? localRegion[2] : undefined
        skuProperties.regionSkuPropertySearch.region.push(option)
        skuProperties.regionSkuPropertySearch.regionSearchType = 1
      } else {
        skuProperties.regionSkuPropertySearch.region = []
        skuProperties.regionSkuPropertySearch.regionSearchType = 2
      }
      skuProperties.industry = !this.localSkuProperty.industry ? ([] as string[]) : [this.localSkuProperty.industry]
      skuProperties.subjectType = !this.localSkuProperty.subjectType
        ? ([] as string[])
        : [this.localSkuProperty.subjectType]
      skuProperties.trainingCategory = !this.localSkuProperty.trainingCategory
        ? ([] as string[])
        : [this.localSkuProperty.trainingCategory]
      skuProperties.trainingProfessional = this.getTrainingProfessional()
      skuProperties.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      skuProperties.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      skuProperties.technicalGrade = !this.localSkuProperty.technicalGrade
        ? ([] as string[])
        : [this.localSkuProperty.technicalGrade]
      // 学科、学段转换
      skuProperties.learningPhase = !this.localSkuProperty.learningPhase
        ? ([] as string[])
        : [this.localSkuProperty.learningPhase]
      skuProperties.discipline = !this.localSkuProperty.discipline
        ? ([] as string[])
        : [this.localSkuProperty.discipline]
      this.tradeReportRequest.commoditySku.skuProperty = cloneDeep(skuProperties)
    }
    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      //console.log('envConfig', this.envConfig)
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人设行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }
    /**
     * 加载第一页
     */
    async searchBase() {
      this.trainSchemePage.pageNo = 1
      await this.pageScheme()
    }
    /**
     * 获取网校地区
     */
    async getServiceArea() {
      this.ServiceRegion = await QueryBusinessRegion.getServiceOrIndustry(1)
    }
    /**
     * 分页查询
     */
    async pageScheme() {
      this.trainSchemeQuery.loading = true
      this.tableLoading = true
      try {
        this.getPageQueryParams()
        if (this.portalId) {
          this.tradeReportRequest.portalId = this.portalId
        }
        if (this.tradeReportRequest.notDistributionPortal) {
          this.tradeReportRequest.portalId = ''
        }
        const tradeFlag = this.tradeChannels ?? null //销售渠道
        const isTopic = this.saleChannels ?? null //是否专题
        const isFx = this.isFx ?? null //是否分销推广
        console.log(tradeFlag, '销售渠道')
        console.log(isTopic, '是否专题')
        console.log(isFx, '是否分销推广')
        // 分销选了否 排初传1
        if (isFx === 0 && tradeFlag === null) {
          //
          this.tradeReportRequest.saleChannel = null
          this.tradeReportRequest.excludedSaleChannels = [1]
        }
        // 分销选了是 saleChannel传1
        if (isFx === 1 && tradeFlag === null) {
          this.tradeReportRequest.saleChannel = 1
          this.tradeReportRequest.excludedSaleChannels = null
          //
        }
        // 分销选了是 华医网也选了 saleChannel不穿 排初传[0123]
        if (isFx === 1 && tradeFlag != null) {
          this.tradeReportRequest.saleChannel = null
          this.tradeReportRequest.excludedSaleChannels = [0, 1, 2, 3]
        }
        // 分销选了否 华医网也选了  saleChannel穿3 排初传[1]
        if (isFx === 0 && tradeFlag != null) {
          this.tradeReportRequest.saleChannel = 3
          this.tradeReportRequest.excludedSaleChannels = [1]
        }
        // 单选华医网 saleChannel穿3
        if (isFx === null && tradeFlag != null) {
          this.tradeReportRequest.saleChannel = 3
          this.tradeReportRequest.excludedSaleChannels = null
        }
        if (isFx === null && tradeFlag == null) {
          this.tradeReportRequest.saleChannel = null
          this.tradeReportRequest.excludedSaleChannels = null
        }
        // const tradeFlag = this.tradeChannels != null ? this.tradeChannels : null
        // const isTop = this.saleChannels != null ? this.saleChannels : null
        // console.log(tradeFlag, 'tradeFlag')
        // console.log(isTop, 'isTop')
        // if (isTop) {
        //   // 是专题且选了华医网
        //   if (tradeFlag) {
        //     this.tradeReportRequest.saleChannels = [2, 3]
        //   } else {
        //     // 是专题没华医网
        //     this.tradeReportRequest.saleChannels = [2]
        //   }
        // }
        // if (!isTop) {
        //   // 不是专题且选了华医网
        //   if (tradeFlag) {
        //     this.tradeReportRequest.saleChannels = [3]
        //   } else {
        //     // 不是专题没华医网
        //     this.tradeReportRequest.saleChannels = [0, 1]
        //   }
        // }
        // // 专题为空且销售渠道不为空
        // if (isTop === null && tradeFlag) {
        //   this.tradeReportRequest.saleChannels = [this.tradeChannels]
        // }
        // if (isTop === null && tradeFlag === null) {
        //   this.tradeReportRequest.saleChannels = null
        // }
        this.commodityOpenReportFormResponseVo =
          await this.queryTrainClassReportList.pageCommodityOpenReportFormsInServicer(
            this.trainSchemePage,
            this.tradeReportRequest
          )
        console.log(this.commodityOpenReportFormResponseVo, 'this.commodityOpenReportFormResponseVo')
        this.tableLoading = false
        this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList =
          this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList
            .map((item) => {
              item.paymentTypeStatisticInfoList.sort(function (a: any, b: any) {
                return b.paymentType - a.paymentType
              })
              return item
            })
            .sort(function (a: any, b: any) {
              return a.purchaseChannel - b.purchaseChannel
            })
        // 数组去重
        this.commodityOpenReportFormResponseVo.map((item) => {
          item.purchaseChannelStatisticInfoList.map((subListItem) => {
            subListItem.paymentTypeStatisticInfoList.sort(function (a: any, b: any) {
              return b.paymentType - a.paymentType
            })
          })
          item.purchaseChannelStatisticInfoList = item.purchaseChannelStatisticInfoList
            .filter(function (item, index, e) {
              return e.findIndex((el) => el.purchaseChannel == item.purchaseChannel) === index
            })
            .sort(function (a: any, b: any) {
              return a.purchaseChannel - b.purchaseChannel
            })
        })
        console.log(this.commodityOpenReportFormResponseVo, 'this.commodityOpenReportFormResponseVo123')
      } catch (e) {
        // console.log('获取培训方案分页列表失败！', e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['schemeTable'] as any)?.doLayout()
        this.trainSchemeQuery.loading = false
        this.calculationSpan()
        this.exportQueryParam = Object.assign(new TradeReportReq(), this.tradeReportRequest)
      }
    }
    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.portalName = ''
      this.distributorName = ''
      this.tradeReportRequest = new TradeReportRequest()
      this.tradeReportRequest.tradeTime = new DateScopeRequest()
      this.tradeReportRequest.tradeTime.begin = ''
      this.tradeReportRequest.tradeTime.end = ''
      console.log(this.tradeReportRequest.tradeTime, 'this.tradeReportRequest.tradeTime')
      this.tradeReportRequest.commoditySku = new CommoditySkuRequest()
      this.tradeReportRequest.commoditySku.saleTitle = ''
      this.tradeReportRequest.commoditySku.scheme = new SchemeRequest()
      this.tradeReportRequest.commoditySku.scheme.schemeIdList = this.hywSchemeList
      this.tradeReportRequest.commoditySku.scheme.schemeType = ''
      this.tradeReportRequest.commoditySku.scheme.schemePeriodScope = new DoubleScopeRequest()
      this.tradeReportRequest.commoditySku.skuProperty = new SkuPropertyRequest()
      this.tradeReportRequest.commoditySku.skuProperty.year = new Array<string>()
      this.tradeReportRequest.commoditySku.skuProperty.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.tradeReportRequest.commoditySku.skuProperty.regionSkuPropertySearch.region =
        new Array<RegionSkuPropertyRequest>()
      this.tradeReportRequest.commoditySku.skuProperty.industry = new Array<string>()
      this.tradeReportRequest.commoditySku.skuProperty.subjectType = new Array<string>()
      this.tradeReportRequest.commoditySku.skuProperty.trainingCategory = new Array<string>()
      this.tradeReportRequest.commoditySku.skuProperty.trainingProfessional = new Array<string>()
      this.schemeTypeInfo = new Array<string>()
      this.queryTrainClassReportList.statisticsM.tradeCountSummaryInfo = new SubOrderStatisticDto()
    }
    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.technicalGrade = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      // 学段、学科
      this.localSkuProperty.discipline = ''
      this.localSkuProperty.learningPhase = ''
    }
    /**
     * 学段变化清空学科
     */
    updateStudyPeriod() {
      this.localSkuProperty.discipline = ''
    }
    /**
     * 组件联动：切换科目类型已选项
     */
    handleClearSubjectsTypeSelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
    }
    /**
     * 组件联动：切换培训类别已选项
     */
    handleClearTrainingTypeSelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
    }
    /**
     * 组件联动：切换培训专业已选项
     */
    handleClearTrainingProfessionalSelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
    }
    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )

      const configSubjectType = configList.findIndex((el) => (el.name = '科目类型'))
      const configTrainingCategory = configList.findIndex((el) => (el.name = '培训类别'))
      const configPositionCategory = configList.findIndex((el) => (el.code = 'POSITION_CATEGORY'))
      const configTrainingObject = configList.findIndex((el) => (el.code = 'TRAINNING_OBJECT'))
      const configTechnicalLevel = configList.findIndex((el) => (el.code = 'JOB_LEVEL'))
      // 修改
      const configTechnicalStudying = configList.findIndex((el) => (el.code = '学段'))
      const configTechnicalSubject = configList.findIndex((el) => (el.code = '学科'))
      this.skuVisible.subjectType = configSubjectType > -1 ? true : false
      this.skuVisible.trainingCategory = configTrainingCategory > -1 ? true : false
      this.skuVisible.positionCategory = configPositionCategory > -1 ? true : false
      this.skuVisible.trainingObject = configTrainingObject > -1 ? true : false
      this.skuVisible.technicalGrade = configTechnicalLevel > -1 ? true : false
      // 获取教师配置
      this.skuVisible.learningPhase = configTechnicalStudying > -1 ? true : false
      this.skuVisible.discipline = configTechnicalSubject > -1 ? true : false
    }
    // 卫生行业培训类别联动
    handleWSUpdateTrainingObject(value: string) {
      this.localSkuProperty.positionCategory = ''
      this.trainingObjectId = value
    }
    /**
     * 响应组件行业Id集合传参
     */
    handleIndustryInfos(values: any) {
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.professionHealthIndustryId = values.professionHealthIndustryId || ''
      // 获取教师行业id
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
    }
    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }
    // 卫生行业培训类别联动
    handleWSUpdateTrainingCategory(value: string) {
      console.log(value, 'val liandong')
      this.trainingCategoryId = value
    }
    openStatistics() {
      this.openingstatistics = true
    }
    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportSchemeOpenStatistical' }
      })
    }
    async exportListData() {
      if (this.portalId) {
        this.tradeReportRequest.portalId = this.portalId
      }
      if (this.tradeReportRequest.notDistributionPortal) {
        this.tradeReportRequest.portalId = ''
      }
      this.exportQueryParam = Object.assign(new TradeReportReq(), this.tradeReportRequest)
      try {
        const res = await this.queryTrainClassReportList.exportExcel(this.exportQueryParam)
        if (res.status.code == 200 && res.data) {
          //   this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        //console.log(e)
      } finally {
        //todo
      }
    }
    /**
     *  合计数据
     *  垃圾合计数据  不看也罢！！！ - -
     */
    getSummaries(param: any) {
      const { columns, data } = param
      const sums = [] as any[]
      columns.forEach((column: any, index: number) => {
        // 可以根据对象属性名判断是否合计
        // if (column.property === "amount3") {
        // 根据索引修改‘合计’文案用了getCommodityReportSummaryInServicer方法，返回值=statisticsM
        if (index === 0) {
          sums[index] = '合计'
          return
          // 可以根据索引判断是否合计
        }
        switch (column.property) {
          case 'summaryInfo.tradeSuccessCount':
            sums[index] = this.queryTrainClassReportList.statisticsM.tradeCountSummaryInfo.tradeSuccessCount
            break
          case 'summaryInfo.returnCount':
            sums[index] = this.queryTrainClassReportList.statisticsM.tradeCountSummaryInfo.returnCount
            break
          case 'summaryInfo.exchangeInCount':
            sums[index] = this.queryTrainClassReportList.statisticsM.tradeCountSummaryInfo.exchangeInCount
            break
          case 'summaryInfo.exchangeOutCount':
            sums[index] = this.queryTrainClassReportList.statisticsM.tradeCountSummaryInfo.exchangeOutCount
            break
          case 'summaryInfo.netTradeSuccessCount':
            sums[index] = this.queryTrainClassReportList.statisticsM.tradeCountSummaryInfo.netTradeSuccessCount
            break
          case 'online.tradeSuccessCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.tradeSuccessCount
            }

            break

          case 'online.returnCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.returnCount
            }

            break

          case 'online.exchangeInCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.exchangeInCount
            }

            break

          case 'online.exchangeOutCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.exchangeOutCount
            }

            break

          case 'online.netTradeSuccessCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[0].paymentTypeStatisticInfoList[0].statisticInfo.netTradeSuccessCount
            }

            break
          case 'team.online.tradeSuccessCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1]?.statisticInfo.tradeSuccessCount
            }

            break

          case 'team.online.returnCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1]?.statisticInfo.returnCount
            }

            break

          case 'team.online.exchangeInCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1]?.statisticInfo.exchangeInCount
            }

            break

          case 'team.online.exchangeOutCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1]?.statisticInfo.exchangeOutCount
            }

            break

          case 'team.online.netTradeSuccessCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[1]?.statisticInfo.netTradeSuccessCount
            }

            break
          case 'team.offline.tradeSuccessCount':
            if (
              this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList &&
              this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0
            ) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0]?.statisticInfo.tradeSuccessCount
            }

            break

          case 'team.offline.returnCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0]?.statisticInfo.returnCount
            }

            break

          case 'team.offline.exchangeInCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0]?.statisticInfo.exchangeInCount
            }

            break

          case 'team.offline.exchangeOutCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0]?.statisticInfo.exchangeOutCount
            }

            break

          case 'team.offline.netTradeSuccessCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[1].paymentTypeStatisticInfoList[0]?.statisticInfo.netTradeSuccessCount
            }

            break
          case 'export.tradeSuccessCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.tradeSuccessCount
            }

            break

          case 'export.returnCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.returnCount
            }

            break

          case 'export.exchangeInCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.exchangeInCount
            }

            break

          case 'export.exchangeOutCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.exchangeOutCount
            }

            break

          case 'export.netTradeSuccessCount':
            if (this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList.length > 0) {
              sums[index] =
                this.queryTrainClassReportList.statisticsM.purchaseChannelStatisticInfoList[2].paymentTypeStatisticInfoList[0].statisticInfo.netTradeSuccessCount
            }

            break
        }
      })
      return sums
    }

    calculationSpan() {
      this.spanList = []
      let pos = 0
      if (this.commodityOpenReportFormResponseVo) {
        for (let i = 0; i < this.commodityOpenReportFormResponseVo.length; i++) {
          console.log(this.commodityOpenReportFormResponseVo[i], '11111')
          if (i === 0) {
            this.spanList.push(1)
            pos = 0
          } else {
            const schemeName = this.commodityOpenReportFormResponseVo[i].schemeName
            const schemeNames = this.commodityOpenReportFormResponseVo[i - 1].schemeName
            if (schemeName === schemeNames) {
              this.spanList[pos] += 1
              this.spanList.push(0)
            } else {
              this.spanList.push(1)
              pos = i
            }
          }
        }
      }
      console.log(this.spanList, 'this.spanList')
    }

    /**
     * 合并坊方法
     * @param item
     */
    objectSpanMethod(item: { row: any; column: any; rowIndex: number; columnIndex: number }) {
      // 过滤掉下标
      // if (item.columnIndex == 0) {
      //   return { rowspan: 1, colspan: 1 }
      // }
      if (item.columnIndex == 0 || item.columnIndex === 1 || item.columnIndex === 2) {
        const _row = this.spanList[item.rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    }
    // 获取自定义索引
    getIndex(index: number) {
      let adjustedIndex = index
      for (let i = 0; i < index; i++) {
        if (this.spanList[i] > 1) {
          // 如果前一行被合并了，则调整当前索引
          adjustedIndex -= this.spanList[i] - 1
        }
      }
      return adjustedIndex + 1 // 加1是因为索引从1开始
    }

    accountType(type: number) {
      let str = ''
      if (type === 0) {
        str = '公需科目'
      } else if (type === 1) {
        str = '专业科目'
      }
      return str
    }
  }
</script>
<style scoped>
  ::v-deep .el-table__empty-block {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
    padding-right: 100%;
  }
</style>
