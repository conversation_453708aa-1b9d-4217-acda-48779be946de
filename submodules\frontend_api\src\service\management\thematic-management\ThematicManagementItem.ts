import BasicDataQueryBackstage, {
  TrainingChannelDetailResponse,
  TrainingChannelPageResponse
} from '@api/platform-gateway/platform-training-channel-back-gateway'
import Training, {
  DeleteScheme,
  DeleteSelectedCourse,
  SaveTrainingChannelOfflineCollectiveSignUpSettingsRequest,
  SaveTrainingChannelOnlineCollectiveSignUpSettingsRequest,
  SaveTrainingChannelSchemeRequest,
  SaveTrainingChannelSelectedCourseRequest,
  SchemeList,
  SelectedCourse,
  TrainingChannelSelectedCourse,
  UpdateTrainingChannelInfoRequest,
  UpdateTrainingChannelOfflineCollectiveSignUpSettingsRequest,
  UpdateTrainingChannelOnlineCollectiveSignUpSettingsRequest,
  UpdateTrainingChannelPortalInfoRequest
} from '@api/platform-gateway/platform-training-channel-v1'

import DataExportBackstage from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import { SubjectType } from './enum/SubjectType'
import BasicInfo from './model/BasicInfo'
import PortalInfo from './model/PortalInfo'
import OnlineCollectiveInfo from './model/OnlineCollectiveInfo'
import { ResponseStatus } from '@hbfe/common'
import OfflineCollectiveInfo from './model/OfflineCollectiveInfo'
import Env from '@api/service/common/utils/Env'

/**
 * 专题子模型
 */
export default class ThematicManagementItem {
  constructor(topicID?: string) {
    this.topicID = topicID || ''
  }
  /**
   * 专题ID
   */
  topicID = ''
  /**
   * 门户ID
   */
  portalID = ''
  /**
   * 排序
   */
  sort = 0
  /**
   * 专题状态
   */
  enable: boolean = undefined
  /**
   * 最新编辑时间
   */
  lastEditTime = ''
  /**
   * 基础信息
   */
  basicInfo = new BasicInfo()
  /**
   * 门户信息
   */
  portalInfo = new PortalInfo()
  /**
   * 已选中培训方案ID
   */
  selectedTrainingPlanID: { id: string; sort: number }[] = []
  /**
   * 已选中精品课程ID
   */
  selectedCourseID: { id: string; sort: number }[] = []
  /**
   * 线上集体报名信息
   */
  onlineCollectiveInfo = new OnlineCollectiveInfo()
  /**
   * 线下集体报名信息
   */
  offlineCollectiveInfo = new OfflineCollectiveInfo()
  /**
   * 已选方案数 -- 只列表使用
   */
  selectedTrainingPlanCount = 0
  /**
   * 日志
   */
  logs: string[] = []
  /**
   * 获取详情
   * @param id 专题ID
   */
  async getDetail(id: string) {
    const res = await BasicDataQueryBackstage.getTrainingChannelDetailById(id)
    return Object.assign(new ThematicManagementItem(), ThematicManagementItem.fromDetail(res.data))
  }
  static fromDetail(dto: TrainingChannelDetailResponse) {
    const vo = new ThematicManagementItem()
    if (dto) {
      vo.topicID = dto.id
      vo.enable = dto.enable
      vo.basicInfo.displayInSchool = dto.showOnNetSchool
      vo.basicInfo.domainType = dto.domainNameType
      vo.basicInfo.entryName = dto.entryName
      vo.basicInfo.onlineSchoolName = dto.netSchoolName
      vo.basicInfo.subjectDomain = dto.domainName
      vo.basicInfo.subjectName = dto.name
      vo.basicInfo.subjectType = dto.types
      vo.basicInfo.unitName = dto.unitName
      vo.selectedTrainingPlanCount = dto.configuredPlans
      vo.basicInfo.suiteArea = dto.regions?.map((item) => item.regionPath)[0]
      vo.basicInfo.suiteIndustry = dto.industrys && dto.industrys[0]
      vo.basicInfo.templateWeb = dto.pcTemplateNo
      vo.basicInfo.templateH5 = dto.h5TemplateNo
      vo.basicInfo.allowAccess = dto.allowAccess
      if (dto.topic) {
        vo.portalID = dto.topic.id
        vo.portalInfo.footerType = dto.topic.bottomShowType
        vo.portalInfo.footer = dto.topic.bottomShowContent
        vo.portalInfo.informationTime = dto.topic.seekTime
        vo.portalInfo.logoType = dto.topic.logoType
        if (dto.topic.logoType === 1) {
          vo.portalInfo.logo = dto.topic.logoName
        }
        if (dto.topic.logoType === 2) {
          vo.portalInfo.logo = dto.topic.logoPictureUrl
        }
        vo.portalInfo.phone = dto.topic.customerServicePhone
        vo.portalInfo.phoneImgUrl = dto.topic.customerServicePhonePictureUrl
        vo.portalInfo.phoneType = dto.topic.customerServicePhoneType
        vo.portalInfo.processType = dto.topic.trainingProcessType
        vo.portalInfo.process = dto.topic.trainingProcessAttachments?.[0]?.url ?? ''
        vo.portalInfo.customerServiceType = dto.topic.enterpriseWechatCustomerType
        vo.portalInfo.customerService = dto.topic.enterpriseWechatCustomerAttachments?.[0]?.url ?? ''
      }
      dto.topic?.photos?.forEach((item) => {
        if (item.type === 1) {
          vo.portalInfo.webCarouselSettings.push({
            id: item.id,
            url: item.pictureUrl,
            sort: item.sort,
            link: item.linkUrl,
            createdAt: item.createdTime
          })
        }
        if (item.type === 2) {
          vo.portalInfo.H5CarouselSettings.push({
            id: item.id,
            url: item.pictureUrl,
            sort: item.sort,
            link: item.linkUrl,
            createdAt: item.createdTime
          })
        }
      })
    }
    return vo
  }
  static fromList(dto: TrainingChannelPageResponse) {
    const vo = new ThematicManagementItem()
    vo.topicID = dto.id
    vo.sort = dto.sort
    vo.basicInfo.subjectName = dto.name
    vo.basicInfo.subjectDomain = dto.domainName
    vo.basicInfo.entryName = dto.entryName
    vo.basicInfo.subjectType = dto.types
    vo.basicInfo.allowAccess = dto.allowAccess
    if (vo.basicInfo.subjectType?.length) {
      if (vo.basicInfo.subjectType.includes(SubjectType.region) && dto?.regions?.length) {
        const region = dto.regions[0]
        if (region.provinceId) {
          vo.basicInfo.suiteArea = region.provinceName
        }
        if (region.cityId) {
          vo.basicInfo.suiteArea = vo.basicInfo.suiteArea + '/' + region.cityName
        }
        if (region.countyId) {
          vo.basicInfo.suiteArea = vo.basicInfo.suiteArea + '/' + region.countyName
        }
      }
      if (vo.basicInfo.subjectType.includes(SubjectType.industry)) {
        vo.basicInfo.suiteIndustry = dto.industrys[0].industryName
      }
      if (vo.basicInfo.subjectType.includes(SubjectType.unit)) {
        vo.basicInfo.unitName = dto.unitName
      }
    }
    vo.basicInfo.displayInSchool = dto.showOnNetSchool
    vo.selectedTrainingPlanCount = dto.configuredPlans
    vo.enable = dto.enable
    vo.lastEditTime = dto.updatedTime
    return vo
  }
  /**
   * 交易专题入口名称校验
   * 200002 专题入口名称已存在
   */
  async validTrainingChannelEntryNameUnique() {
    const res = await Training.validTrainingChannelEntryNameUnique({
      entryName: this.basicInfo.entryName,
      trainingChannelId: this.topicID ? this.topicID : undefined
    })
    return res.data
  }
  /**
   * 保存基础信息
   * code: 200001 域名已存在
   */
  async saveBasicInfo() {
    const res = await Training.saveTrainingChannelInfo(BasicInfo.to(this.basicInfo))
    this.topicID = res.data.id
    return res
  }
  /**
   * 更新基础信息
   */
  async updateBasicInfo() {
    // 有ID为更新，没ID为新建
    // Add code to save basic information here
    const request = new UpdateTrainingChannelInfoRequest()
    Object.assign(request, BasicInfo.to(this.basicInfo))
    request.id = this.topicID
    return Training.updateTrainingChannelInfo(request)
  }
  /**
   * 保存门户信息
   */
  async savePortalInfo() {
    // Add code to save basic information here
    const request = PortalInfo.to(this.portalInfo)
    request.id = this.topicID
    const res = await Training.saveTrainingChannelPortalInfo(request)
    this.portalID = res.data?.trainingChannelPortalId
    return res
  }
  /**
   * 更新门户信息
   */
  async updatePortalInfo() {
    // Add code to save basic information here
    const request = Object.assign(new UpdateTrainingChannelPortalInfoRequest(), PortalInfo.to(this.portalInfo))
    request.id = this.topicID
    request.trainingChannelPortalId = this.portalID
    return Training.updateTrainingChannelPortalInfo(request)
  }
  /**
   * 保存培训方案
   */
  async saveTrainingChannelScheme(
    addScheme: Array<SchemeList>,
    updateScheme: Array<SchemeList>,
    deleteScheme: Array<DeleteScheme>
  ) {
    const request = new SaveTrainingChannelSchemeRequest()
    request.id = this.topicID
    request.addScheme = addScheme
    request.updateScheme = updateScheme
    request.deleteScheme = deleteScheme
    return Training.saveTrainingChannelScheme(request)
  }
  /**
   * 修改精品课程（增，删，改）
   * courseCategoryId 默认 -1
   */
  async saveTrainingChannelCourse(
    params: {
      courseCategoryId?: string
      addCourse?: Array<SelectedCourse>
      updateCourse?: Array<SelectedCourse>
      deleteCourse?: Array<DeleteSelectedCourse>
    }[]
  ) {
    const request = new SaveTrainingChannelSelectedCourseRequest()
    request.id = this.topicID
    request.trainingChannelSelectedCourses = params.map((item) => {
      const trainingChannelSelectedCourse = new TrainingChannelSelectedCourse()
      trainingChannelSelectedCourse.selectedCourseCategoryId = item.courseCategoryId
      trainingChannelSelectedCourse.addSelectedCourse = item.addCourse
      trainingChannelSelectedCourse.updateSelectedCourse = item.updateCourse
      trainingChannelSelectedCourse.deleteSelectedCourse = item.deleteCourse
      return trainingChannelSelectedCourse
    })
    const res = await Training.saveTrainingChannelSelectedCourse(request)
    if (res.status.isSuccess()) {
      return new ResponseStatus(Number(res.data?.code), res.data?.message)
    }
    return res.status
  }

  /**
   * 查询线上集体报名配置
   */
  async queryOnlineCollectiveInfo() {
    const res = await BasicDataQueryBackstage.getOnlineCollectiveByTrainingChannelIdInSubject(this.topicID)
    if (res.status.isSuccess() && res.data) {
      Object.assign(this.onlineCollectiveInfo, OnlineCollectiveInfo.from(res.data))
    }
    this.onlineCollectiveInfo.signUpClassUrl = `${this.basicInfo.subjectDomain}${Env.proxyPortStr}/manage/view-train-class`
    return res.status
  }
  /**
   * 保存线上集体报名配置
   */
  async saveOnlineCollectiveInfo() {
    const params = OnlineCollectiveInfo.to(
      this.onlineCollectiveInfo,
      new SaveTrainingChannelOnlineCollectiveSignUpSettingsRequest()
    ) as SaveTrainingChannelOnlineCollectiveSignUpSettingsRequest
    params.trainingChannelId = this.topicID
    const res = await Training.saveTrainingChannelOnlineCollectiveSignUpSetting(params)
    if (res.status.isSuccess()) {
      return new ResponseStatus(Number(res.data?.code), res.data?.message)
    }
    return res.status
  }
  /**
   * 更新线上集体报名配置
   */
  async updateOnlineCollectiveInfo() {
    const params = OnlineCollectiveInfo.to(
      this.onlineCollectiveInfo,
      new UpdateTrainingChannelOnlineCollectiveSignUpSettingsRequest()
    ) as UpdateTrainingChannelOnlineCollectiveSignUpSettingsRequest
    params.id = this.onlineCollectiveInfo.id
    const res = await Training.updateTrainingChannelOnlineCollectiveSignUpSetting(params)
    if (res.status.isSuccess()) {
      return new ResponseStatus(Number(res.data?.code), res.data?.message)
    }
    return res.status
  }
  /**
   * 查询线下集体报名配置
   */
  async queryOfflineCollectiveInfo() {
    const res = await BasicDataQueryBackstage.getOfflineCollectiveByTrainingChannelIdInSubject(this.topicID)
    if (res.status.isSuccess() && res.data) {
      Object.assign(this.offlineCollectiveInfo, OfflineCollectiveInfo.from(res.data))
    }
    this.offlineCollectiveInfo.signUpClassUrl = `${this.basicInfo.subjectDomain}${Env.proxyPortStr}/collective-registry`
    return res.status
  }
  /**
   * 保存线下集体报名配置
   */
  async saveOfflineCollectiveInfo() {
    const params = OfflineCollectiveInfo.to(
      this.offlineCollectiveInfo,
      new SaveTrainingChannelOfflineCollectiveSignUpSettingsRequest()
    ) as SaveTrainingChannelOfflineCollectiveSignUpSettingsRequest
    params.trainingChannelId = this.topicID
    const res = await Training.saveTrainingChannelOfflineCollectiveSignUpSetting(params)
    if (res.status.isSuccess()) {
      return new ResponseStatus(Number(res.data?.code), res.data?.message)
    }
    return res.status
  }
  /**
   * 更新线下集体报名配置
   */
  async updateOfflineCollectiveInfo() {
    const params = OfflineCollectiveInfo.to(
      this.offlineCollectiveInfo,
      new UpdateTrainingChannelOfflineCollectiveSignUpSettingsRequest()
    ) as UpdateTrainingChannelOfflineCollectiveSignUpSettingsRequest
    params.id = this.offlineCollectiveInfo.id
    const res = await Training.updateTrainingChannelOfflineCollectiveSignUpSetting(params)
    if (res.status.isSuccess()) {
      return new ResponseStatus(Number(res.data?.code), res.data?.message)
    }
    return res.status
  }
  /**
   * 导出
   */
  async export() {
    return DataExportBackstage.exportTrainingChannelCommoditySkuInServicer({
      queryRequest: {
        trainingChannelIds: [this.topicID]
      }
    })
  }

  /**
   * 导出（专题管理员）
   */
  async exportInTrainingChannel() {
    return DataExportBackstage.exportTrainingChannelCommoditySkuInTrainingChannelAdmin({
      queryRequest: {
        trainingChannelIds: [this.topicID]
      }
    })
  }
  /**
   * 启用
   */
  async enableTopic() {
    const res = await Training.enableTrainingChannelInfo(this.topicID)
    if (res.data.code == '200') {
      this.enable = true
    }
    return res
  }
  /**
   * 停用
   */
  async disableTopic() {
    const res = await Training.disableTrainingChannelInfo(this.topicID)
    if (res.data.code == '200') {
      this.enable = false
    }
    return res
  }
  /**
   * 获取日志
   */
  getLogs() {
    //
  }
}
