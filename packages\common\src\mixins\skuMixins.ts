import { Component, Vue } from 'vue-property-decorator'
import CommonModule from '@api/service/common/common/CommonModule'
@Component
export default class SkuMixins extends Vue {
  /**
   * 培训对象列bie
   */
  get trainingTypeList() {
    return CommonModule.getTrainingTypeList
  }

  /**
   * 年度
   */
  get yearList() {
    return CommonModule.getYearList
  }

  /**
   * 获取培训对象
   */
  get traineesList() {
    return CommonModule.getTraineesList
  }

  /**
   * 获取岗位数据
   */
  get jobCategoryList() {
    return CommonModule.getJobCategoryList
  }

  /**
   * 获取单位数据
   */
  get unitCategoryList() {
    return CommonModule.getUnitCategoryList
  }

  // 加载sku属性
  async created() {
    // await this.init()
    try {
      const res = await CommonModule.init()
      if (!res.isSuccess()) {
        this.$message.error('加载失败！' + res.errors[0].message)
      }
    } catch (error) {
      console.error(error)
      this.$message.error('加载失败')
    }
  }
  /**
   * 通过id查找对象名
   */
  findTraineesNameByid(id: string) {
    const trainees = this.traineesList.find(el => {
      return el.id === id
    })
    return trainees?.optionName
  }

  /**
   * 通过id查找岗位名
   */
  findJobNameByid(id: string) {
    const trainees = this.jobCategoryList.find(el => {
      return el.id === id
    })
    return trainees?.optionName
  }

  /**
   * 通过id查找单位名
   */
  findUnitNameByid(id: string) {
    const trainees = this.unitCategoryList.find(el => {
      return el.id === id
    })
    console.log(this.unitCategoryList, id)
    return trainees?.optionName
  }
  /**
   * 通过id查类别名
   */
  findTypeNameByid(id: string) {
    const trainees = this.trainingTypeList.find(el => {
      return el.id === id
    })
    return trainees?.optionName
  }
}
