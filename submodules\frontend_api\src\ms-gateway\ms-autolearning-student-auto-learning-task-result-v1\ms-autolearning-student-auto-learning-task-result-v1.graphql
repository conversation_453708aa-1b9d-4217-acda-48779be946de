"""独立部署的微服务,K8S服务名:ms-autolearning-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询最近一次失败的子任务
		@param mainTaskId:
		@return {@link List<AutoLearningSubTask>}
		<AUTHOR> By Cb
		@since 2024/8/8 16:13
	"""
	findLastFailSubTaskByMainTaskIdList(mainTaskId:[String]!):[AutoLearningSubTask]
	"""学员自动学习任务结果分页查询请求处理
		@param request:
		@return {@link StudentAutoLearningTaskResultQueryPageResponse}
		<AUTHOR> By Cb
		@since 2024/5/15 20:31
	"""
	queryByPage(request:StudentAutoLearningTaskResultQueryPageRequest):StudentAutoLearningTaskResultPage @page(for:"StudentAutoLearningTaskResult")
	"""学员自动学习任务结果分页查询请求处理
		@param request:
		@return {@link StudentAutoLearningTaskResultQueryPageResponse}
		<AUTHOR> By Cb
		@since 2024/5/15 20:31
	"""
	queryNewestTaskResultByQualificationIds(qualificationIds:[String]):[StudentAutoLearningTaskResult]
	"""用于统计指定状态列表下自动学习任务结果数量
		@param request:
		@return {@link QueryStudentAutoLearningTaskResultResponse}
		<AUTHOR> By zcy
		@since 2025/4/1 9:50
	"""
	queryStudentAutoLearningTaskResult(request:QueryStudentAutoLearningTaskResultRequest):QueryStudentAutoLearningTaskResultResponse
	"""学员自动学习任务结果列表分页查询"""
	queryStudentAutoLearningTaskResultByPage(request:TaskResultByPageRequest):StudentAutoLearningTaskResultPage @page(for:"StudentAutoLearningTaskResult")
	"""学员正常自动学习任务结果查询请求处理
		@param qualificationId:
		@return {@link StudentAutoLearningTaskResult}
		<AUTHOR> By Cb
		@since 2024/7/24 10:49
	"""
	queryStudentNormalAutoLearningTaskResult(qualificationId:String!):StudentAutoLearningTaskResult
}
"""@Author: chenzeyu
	@CreateTime: 2025-03-21  10:21
	@Description: TODO
"""
input QueryStudentAutoLearningTaskResultRequest @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.request.QueryStudentAutoLearningTaskResultRequest") {
	"""查询开始时间 beginDate和 endDate都不传则查全部"""
	beginDate:DateTime
	"""查询结束时间"""
	endDate:DateTime
	"""任务结果
		@see StudentAutoLearningTaskResultResults
	"""
	taskResult:[Int]
}
"""学员自动学习任务结果分页查询请求
	<AUTHOR> By Cb
	@since 2024/05/15 17:28
"""
input StudentAutoLearningTaskResultQueryPageRequest @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.request.StudentAutoLearningTaskResultQueryPageRequest") {
	"""页码"""
	pageNo:Int
	"""每页数量"""
	pageSize:Int
	"""姓名"""
	name:String
	"""身份证号"""
	idCard:String
	"""手机号"""
	phone:String
	"""学习方案名称"""
	learningSchemeName:String
	"""参训资格ID集合"""
	qualificationIdList:[String]
	"""结果集合
		0 - 待执行
		1 - 编排失败
		2 - 执行中
		3 - 执行成功
		4 - 执行失败
		5 - 已取消
		6 - 终止
	"""
	resultList:[Int]
}
"""学员任务结果请求
	<AUTHOR>
	@date 2025/3/26 16:36
"""
input TaskResultByPageRequest @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.request.TaskResultByPageRequest") {
	"""页码"""
	pageNo:Int
	"""每页数量"""
	pageSize:Int
	"""主任务ID"""
	mainTaskIdList:[String]
	"""姓名"""
	name:String
	"""身份证号"""
	idCard:String
	"""手机号"""
	phone:String
	"""学习方案名称"""
	learningSchemeName:String
	"""结果集合
		0 - 待执行
		1 - 编排失败
		2 - 执行中
		3 - 执行成功
		4 - 执行失败
		5 - 已取消
		6 - 终止
	"""
	resultList:[Int]
}
type Range @type(value:"com.fjhb.domain.learningscheme.seedwork.Range") {
	key:String
	value:String
}
type BusinessDomainIdentity @type(value:"com.fjhb.micro.context.v1.BusinessDomainIdentity") {
	unitId:String
	servicerId:String
	applicationMemberType:Int!
	applicationMemberId:String
	applicationType:Int!
}
type DataRouterIdentity @type(value:"com.fjhb.micro.context.v1.DataRouterIdentity") {
	dataPlatformVersionId:String
	dataProjectId:String
}
type HttpIdentity @type(value:"com.fjhb.micro.context.v1.HttpIdentity") {
	ip:String
	domain:String
	requestUrl:String
}
type MicroContext @type(value:"com.fjhb.micro.context.v1.MicroContext") {
	sequenceNo:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	businessDomain:BusinessDomainIdentity
	servicerProvider:ServicerProvider
	userIdentity:UserIdentity
	dataRouterIdentity:DataRouterIdentity
	httpIdentity:HttpIdentity
}
type ServicerProvider @type(value:"com.fjhb.micro.context.v1.ServicerProvider") {
	unitId:String
	servicerType:Int!
	servicerId:String
}
type UserIdentity @type(value:"com.fjhb.micro.context.v1.UserIdentity") {
	sessionId:String
	accountId:String
	rootAccountId:String
	accountType:Int
	userId:String
}
"""@Author: chenzeyu
	@CreateTime: 2025-03-21  10:20
	@Description: TODO
"""
type QueryStudentAutoLearningTaskResultResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.QueryStudentAutoLearningTaskResultResponse") {
	code:String
	message:String
	count:Int
}
"""学员自动学习任务结果
	<AUTHOR> By Cb
	@since 2024/05/10 17:13
"""
type StudentAutoLearningTaskResult @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.studenautolearningtaskresult.model.StudentAutoLearningTaskResult") {
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""学员自动学习任务结果ID"""
	studentAutoLearningTaskResultId:String
	"""自动学习主任务ID"""
	mainTaskId:String
	"""当前自动学习主任务ID"""
	currentMainTaskId:String
	"""姓名"""
	name:String
	"""身份证号"""
	idCard:String
	"""手机号"""
	phone:String
	"""学习方案ID"""
	learningSchemeId:String
	"""学习方案名称"""
	learningSchemeName:String
	"""学号"""
	studentNo:String
	"""参训资格ID"""
	qualificationId:String
	"""结果
		@see StudentAutoLearningTaskResultResults
	"""
	result:Int
	"""状态码"""
	code:Int
	"""信息"""
	message:String
	"""完成时间"""
	completedTime:DateTime
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""用户ID"""
	userId:String
	"""来源ID"""
	sourceId:String
	"""来源类型"""
	sourceType:Int
	properties:[StudentAutoLearningTaskResultExtendProperty]
}
"""学员自动学习任务结果拓展属性
	<AUTHOR> By Cb
	@since 2025/03/26 14:13
"""
type StudentAutoLearningTaskResultExtendProperty @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.studenautolearningtaskresult.model.StudentAutoLearningTaskResultExtendProperty") {
	"""主键"""
	id:String
	"""学员自动学习任务结果ID"""
	studentAutoLearningTaskResultId:String
	"""键"""
	name:String
	"""值"""
	value:String
}
"""自动学习子任务
	<AUTHOR>
"""
type AutoLearningSubTask @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.subtask.model.AutoLearningSubTask") {
	"""子任务ID"""
	subTaskId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""微服务上下文"""
	microContext:MicroContext
	"""主任务ID"""
	mainTaskId:String
	"""子任务顺序"""
	seq:Int
	"""学习方案ID"""
	learningSchemeId:String
	"""学号"""
	studentNo:String
	"""参训资格ID"""
	qualificationId:String
	"""学习方式id"""
	learningId:String
	"""学习资源ID"""
	learningResourceId:String
	"""期望开始时间"""
	expectStartTime:DateTime
	"""期望结束时间"""
	expectEndTime:DateTime
	"""任务数据"""
	taskData:SubTaskData
	"""任务类型
		@see AutoLearningSubTaskTypes
	"""
	taskType:Int
	"""任务状态
		@see AutoLearningSubTaskStatus
	"""
	status:Int
	"""任务状态原因
		@see StatusReason
	"""
	statusReason:String
	"""重试次数"""
	retries:Int
	"""错误信息"""
	errorMsg:String
	"""创建时间"""
	createdTime:DateTime
	"""完成时间"""
	completedTime:DateTime
	"""用户ID"""
	userId:String
	"""所属的自动学习类型
		@see com.fjhb.ms.autolearning.v1.api.constant.AutoLearningSupportedLearningType
	"""
	belongedLearningType:Int
}
"""<AUTHOR>
	@since
"""
type ChooseCourseExecuteSettings implements SubTaskData @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.subtask.model.taskdata.ChooseCourseExecuteSettings") {
	"""选课列表"""
	chooseCourseList:[ChooseCourseParam]
	"""用户id"""
	userId:String
	"""学号"""
	studentNo:String
	"""选课数据范围"""
	ranges:[Range]
	"""选课时间"""
	chooseCourseDate:DateTime
	"""参训资格编号"""
	qualificationId:String
	"""推荐时间，选课时回传，用于校验，该时间后学员有无自主选课"""
	recommandDate:DateTime
	"""任务类型
		@see AutoLearningSubTaskTypes
	"""
	type:Int!
}
"""课程学习方式自动学习执行配置
	<AUTHOR>
"""
type CourseAutoLearningExecuteSetting implements SubTaskData @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.subtask.model.taskdata.CourseAutoLearningExecuteSetting") {
	"""学习进度(1% - 100%)"""
	learningProgress:Double
	"""本次学习进度"""
	currentLearningProgress:Double
	"""课程ID"""
	courseId:String
	"""学员ID"""
	studentCourseId:String
	"""课件ID"""
	coursewareId:String
	"""媒体资源ID"""
	mediaId:String
	"""学习开始时间"""
	studyStartTime:DateTime
	"""学习结束时间"""
	studyEndTime:DateTime
	"""任务类型
		@see AutoLearningSubTaskTypes
	"""
	type:Int!
}
"""课后练习自动学习执行配置
	<AUTHOR>
"""
type CourseQuizAutoLearningExecuteSetting implements SubTaskData @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.subtask.model.taskdata.CourseQuizAutoLearningExecuteSetting") {
	"""课程ID"""
	courseId:String
	"""学员课程ID"""
	studentCourseId:String
	"""课后测验ID"""
	courseQuizId:String
	"""获得分数"""
	score:Int
	"""作答开始时间"""
	answerStartTime:DateTime
	"""作答结束时间"""
	answerEndTime:DateTime
	"""任务类型
		@see AutoLearningSubTaskTypes
	"""
	type:Int!
}
"""考试学习方式自动学习执行配置
	<AUTHOR>
"""
type ExamAutoLearningExecuteSetting implements SubTaskData @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.subtask.model.taskdata.ExamAutoLearningExecuteSetting") {
	"""获得分数"""
	score:Int
	"""作答开始时间"""
	answerStartTime:DateTime
	"""作答结束时间"""
	answerEndTime:DateTime
	"""任务类型
		@see AutoLearningSubTaskTypes
	"""
	type:Int!
}
"""子任务数据
	<AUTHOR>
"""
interface SubTaskData @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.subtask.model.taskdata.SubTaskData") {
	"""任务类型
		@see AutoLearningSubTaskTypes
	"""
	type:Int!
}
"""<AUTHOR>
	@since
"""
type ChooseCourseParam @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.subtask.model.taskdata.dto.ChooseCourseParam") {
	"""学习大纲编号"""
	outlineId:String
	"""课程id"""
	courseId:String
	"""学员课程ID
		若不提供 则默认使用UUIDUtils.generate()生成
	"""
	studentCourseId:String
	"""课程类型
		1-必修，2-选修
	"""
	courseType:Int
	"""学时"""
	period:BigDecimal
}

scalar List
type StudentAutoLearningTaskResultPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentAutoLearningTaskResult]}
