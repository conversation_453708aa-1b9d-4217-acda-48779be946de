import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import Context from '@api/service/common/context/Context'

class QueryShowLoginAccount {
  /**
   * 是否显示登录账号读取阿波罗配置
   */
  get isShowLoginAccount(): boolean {
    return ConfigCenterModule.getFrontendApplication(frontendApplication.loginAccountServicerIds)?.includes(
      Context.servicerInfo.id
    )
  }
}
export default new QueryShowLoginAccount()
