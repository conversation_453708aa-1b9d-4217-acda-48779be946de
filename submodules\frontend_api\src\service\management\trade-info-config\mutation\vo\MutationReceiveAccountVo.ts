import UpdateAliPayReceiveAccountVo from './UpdateAliPayReceiveAccountVo'
import UpdateOfflineReceiveAccountVo from './UpdateOfflineReceiveAccountVo'
import UpdateWXPayReceiveAccountVo from './UpdateWXPayReceiveAccountVo'

class MutationReceiveAccountVo {
  // 唯一标识
  id: string
  //
  // 公共部分
  //
  /**
   * 支付方式 1-线上 2-线下
   */
  accountType = 1
  /**
   * 收款账号(开户号、商户号、支付宝账号)
   */
  accountNo = ''
  /**
   * 收款账号别名
   */
  accountName = ''
  /**
   * 纳税人识别号
   */
  taxPayerId = ''
  /**
   * 退款方式  1-线上 2-线下
   */
  refundWay = -1

  //
  //支付宝方式
  //
  /**
   * 支付账号类型id
   * 培训券，对接众智汇云培训券:TRAINING_VOUCHER
   * 支付宝:ALIPAY
   * 微信：WXPAY
   */
  paymentChannelId = ''
  /**
   * 支付宝密钥 -- 原型给的支付宝密钥不需要了
   */

  /**
   * 合作者身份ID
   */
  partner = ''
  /**
   * 支付宝应用私钥
   */
  privateKey = ''
  /**
   * 支付宝公钥
   */
  publicKey = ''
  /**
   * 支付宝应用id
   */
  aliPayAppId = ''

  /**
   * 微信方式
   */
  /**
   * API密钥
   */
  privateKeyPWD = ''
  /**
   * 公众账号ID
   */
  wxPayAppId = ''
  /**
   * 微信证书密钥
   */
  merchantKey = ''
  /**
   * 微信证书文件名称
   */
  privateKeyFileName = ''
  /**
   * 微信证书路径
   */
  privateKeyPath = ''

  //
  //线下方式
  //
  /**
   * 开户银行
   */
  depositBank = ''
  /**
   * 开户户名
   */
  merchantName = ''
  /**
   * 柜台号
   */
  counterNumber = ''

  /**
   * 兴业银行
   */
  /**
   * 应用id
   */
  xyPayAppId = ''
  /**
   * 公众号或小程序id
   */
  xyPaySubAppId = ''
  /**
   * 终端编号
   */
  terminalId = ''
  /**
   * SM2签名私钥
   */
  sm2key = ''
  /**
   * 响应公钥
   */
  resPublicKey = ''
  /**
   * 请求私钥
   */
  reqKey = ''

  /**
   * 建设银行
   */
  /**
   * 商户柜台代码
   */
  posId = ''
  /**
   * 分行代码
   */
  branchId = ''
  /**
   * 建行网银支付接口的公钥
   */
  jsPublicKey = ''
  /**
   * 建行的操作员账号不能为空
   */
  operator = ''
  /**
   * 建行操作员的登陆密码
   */
  password = ''
  /**
   * 是否使用防钓鱼,如果1表示使用防钓鱼接口,其他则不使用
   */
  phishing = 0
  /**
   * 小程序/公众号的 APPID 当前调起支付的小程序/公众号 APPID
   */
  jsSubAppid = ''
  /**
   * 文件证书路径
   */
  certFilePath = ''
  /**
   * 文件证书密码
   */
  certPassword = ''
  /**
   * 威富通支付
   */
  /**
   * 商户私钥
   */
  mchPrivateKey = ''
  /**
   * 平台公钥
   */
  platPublicKey = ''
  /**
   * 新大陆支付
   */
  /**
   * 支付商户号
   */
  payMerchantId = ''
  /**
   * 代理商号
   */
  proxyId = ''
  /**
   * 密钥
   */
  xdlPrivateKey = ''
  /**
   * 付款扫码引导语
   */
  qrScanPrompt = ''

  fromAliPayVo(updateAliPayReceiveAccount: UpdateAliPayReceiveAccountVo) {
    this.accountType = updateAliPayReceiveAccount.accountType
    this.paymentChannelId = updateAliPayReceiveAccount.paymentChannelId
    this.accountName = updateAliPayReceiveAccount.accountName
    this.refundWay = updateAliPayReceiveAccount.refundWay
    this.taxPayerId = updateAliPayReceiveAccount.taxPayerId
    this.partner = updateAliPayReceiveAccount.partner
    this.privateKey = updateAliPayReceiveAccount.privateKey
    this.publicKey = updateAliPayReceiveAccount.publicKey
    this.aliPayAppId = updateAliPayReceiveAccount.appId
  }

  fromOfflineVo(updateOfflineReceiveAccount: UpdateOfflineReceiveAccountVo) {
    this.accountType = updateOfflineReceiveAccount.accountType
    this.merchantName = updateOfflineReceiveAccount.merchantName
    this.refundWay = updateOfflineReceiveAccount.refundWay
    this.taxPayerId = updateOfflineReceiveAccount.taxPayerId
    this.depositBank = updateOfflineReceiveAccount.depositBank
    this.counterNumber = updateOfflineReceiveAccount.counterNumber
  }

  fromWXPayVo(updateWXPayReceiveAccount: UpdateWXPayReceiveAccountVo) {
    this.accountType = 1
    this.paymentChannelId = updateWXPayReceiveAccount.paymentChannelId
    this.accountName = updateWXPayReceiveAccount.accountName
    this.refundWay = updateWXPayReceiveAccount.refundWay
    this.taxPayerId = updateWXPayReceiveAccount.taxPayerId
    this.wxPayAppId = updateWXPayReceiveAccount.appId
    this.merchantKey = updateWXPayReceiveAccount.merchantKey
    this.privateKeyPWD = updateWXPayReceiveAccount.privateKeyPWD
    this.privateKeyFileName = updateWXPayReceiveAccount.privateKeyFileName
    this.privateKeyPath = updateWXPayReceiveAccount.privateKeyPath
  }
}

export default MutationReceiveAccountVo
