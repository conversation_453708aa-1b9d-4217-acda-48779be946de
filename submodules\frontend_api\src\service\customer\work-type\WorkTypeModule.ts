import { Action, getModule, Module, Mutation } from 'vuex-module-decorators'
import store from '@/store'
import { Role, RoleType, Secure, UnAuthorize } from '@api/Secure'
import Vue from 'vue'
import WorkType from '@api/service/common/models/work-type/WorkType'
import Response, { ResponseStatus } from '@api/Response'
import PlatformWorkTypeGateway, { WorkTypeResponse } from '@api/gateway/PlatformWorkType'
import AbstractWorkTypeModule from '@api/service/common/work-type/AbstractWorkTypeModule'
import PlatformCommodity, { WorkTypeCommodityOpenNumberQueryParamDTO } from '@api/gateway/PlatformCommodity'
import PlatformWorkTypeCategoryGateway, {
  WorkTypeCategoryResponse,
  WorkTypeRelationCategoryResponse
} from '@api/gateway/PlatformWorkTypeCategory'
import { WorkTypeCommodityOpenNumberWithName } from '@api/service/common/work-type/WorkTypeCommodityOpenNumberWithName'
import { WorkTypeCommodityOpenNumberWithChannelVendor } from '@api/service/customer/work-type/models/WorkTypeCommodityOpenNumberWithChannelVendor'

export class WorkTypeAndCategory {
  path: string
  name: string
}

export interface WorkTypeState {
  /**
   * 工种map，key为门户课程分类id，key为-1对应的是所有分类
   */
  workTypeMap: { [key: string]: Array<WorkType> }
  // 各个工种的报名数排序列表
  lazyWorkTypeOpenNumberList: Array<WorkTypeCommodityOpenNumberWithName>
  // 【渠道商】各个工种的报名数排序列表
  lazyWorkTypeChannelVendorOpenNumberList: Array<WorkTypeCommodityOpenNumberWithChannelVendor>
  // 工种类别路径解析结果
  workTypeAndCategoryList: Array<WorkTypeAndCategory>
}

@Module({ namespaced: true, dynamic: true, store, name: 'CustomerWorkTypeModule' })
class WorkTypeModule extends AbstractWorkTypeModule<WorkType> implements WorkTypeState {
  workTypeMap: { [key: string]: Array<WorkType> } = {}
  lazyWorkTypeOpenNumberList: WorkTypeCommodityOpenNumberWithName[] = new Array<WorkTypeCommodityOpenNumberWithName>()
  lazyWorkTypeChannelVendorOpenNumberList: WorkTypeCommodityOpenNumberWithChannelVendor[] = new Array<
    WorkTypeCommodityOpenNumberWithChannelVendor
  >()
  workTypeAndCategoryList: Array<WorkTypeAndCategory> = new Array<WorkTypeAndCategory>()

  @Action
  @Role([RoleType.user])
  @Secure({ PlatformWorkType: ['query.getWorkType'] })
  @UnAuthorize
  async getWorkType(id: string): Promise<Response<WorkType>> {
    const result = new Response<WorkType>()
    const old = this.cache[id]
    if (old) {
      result.data = old
      result.status.code = 200
      return result
    }
    const response = await PlatformWorkTypeGateway.getWorkType(id)
    result.status = response.status
    if (response.data) {
      const workType = new WorkType().parse(response.data)
      result.data = workType
      this.setWorkTypeToCache(workType)
    }
    return result
  }

  @Action
  @Role([RoleType.user])
  @UnAuthorize
  async getWorkTypeListByIds(ids: Array<string>): Promise<Response<Map<string, WorkType>>> {
    const resultResponse = new Response<Map<string, WorkType>>()
    const map = new Map<string, WorkType>()
    if (ids) {
      ids = ids.filter(id => {
        const workType = this.cache[id]
        if (workType) {
          map.set(id, workType)
          return false
        }
        return true
      })
      if (ids.length > 0) {
        const response = await PlatformWorkTypeGateway.getWorkTypeListByIds(ids)
        if (response.data) {
          response.data.forEach(e => {
            const workType = new WorkType().parse(e)
            map.set(workType.id, workType)
            this.setWorkTypeToCache(workType)
          })
        }
        resultResponse.status = response.status
      }
    }
    resultResponse.data = map
    return resultResponse
  }

  /**
   * 根据工种和工种类别路径获取完整中文名
   * @param param
   * @param param.path 如：['/123/456','789/000']
   * @param param.wordSplit 工种与类别之间的分隔符，如：  类别一  #wordSplit#  工种
   * @return 完整中文结果，如： 类别一/工种、类别二/工种、类别三/工种
   */
  @Action
  async getWorkTypeAndCategoryPath(param: { path: Array<string>; wordSplit: string }): Promise<Response<string>> {
    const response = new Response<string>()
    response.status = new ResponseStatus(500, '')
    response.data = ''
    // 入参信息： [[]、[]、[]]
    const paramArray = new Array<string[]>()
    // 拿出工种id
    const workTypeIds = param.path.map(p => {
      const arr = p.split('/')
      paramArray.push(arr)
      return arr[arr.length - 1]
    })
    // 根据工种id集合查工种
    const workTypesResponse = await PlatformWorkTypeGateway.getWorkTypeListByIds(workTypeIds)
    if (!workTypesResponse.status.isSuccess()) {
      return response
    }
    // 根据工种id集合查类别
    const categoryResponse = await PlatformWorkTypeCategoryGateway.getCategoryListByWorkTypeIds({
      workTypeIds,
      containsParent: true
    })
    if (!categoryResponse.status.isSuccess()) {
      return response
    }
    const resultList = new Array<WorkTypeAndCategory>()
    paramArray.forEach(a => {
      let result = ''
      let path = ''
      const workType = workTypesResponse.data.find(w => w.id === a[a.length - 1]) || new WorkTypeResponse()
      const types =
        categoryResponse.data.find(w => w.workTypeId === a[a.length - 1]) || new WorkTypeRelationCategoryResponse()
      if (a.length === 3) {
        // 即/类别/工种
        const type1 = types?.categoryList?.find(t => t.id === a[1]) || new WorkTypeCategoryResponse()
        result = type1.name + param.wordSplit + workType.name
        path = '/' + type1.id + '/' + workType.id
      }
      if (a.length === 4) {
        // 即/类别/类别/工种
        const type1 = types?.categoryList?.find(t => t.id === a[1]) || new WorkTypeCategoryResponse()
        const type2 = types?.categoryList?.find(t => t.id === a[2]) || new WorkTypeCategoryResponse()
        result = type1.name + param.wordSplit + type2.name + param.wordSplit + workType.name
        path = '/' + type1.id + '/' + type2.id + '/' + workType.id
      }
      resultList.push({ path: path, name: result })
    })
    this.SET_WORK_TYPE_CATEGORY_LIST(resultList)
    response.status = new ResponseStatus(200, '')
    return response
  }

  /**
   * 获取工种，门户课程分类id如果有就是获取指定门户课程分类下的工种，给-1就是获取所有工种
   */
  @Action
  @Secure({ PlatformWorkType: ['query.getAllWorkTypeList', 'query.getWorkTypeListByCategoryId'] })
  @UnAuthorize
  async getWorkTypeListByCategoryId(categoryId: string, name?: string): Promise<ResponseStatus> {
    const workTypes = this.workTypeMap[categoryId]
    if (!workTypes) {
      let response: Response<Array<WorkTypeResponse>>
      if (categoryId === '-1') {
        response = await PlatformWorkTypeGateway.getAllWorkTypeList({ name })
      } else {
        response = await PlatformWorkTypeGateway.getWorkTypeListByCategoryId({ categoryId, containsChildren: true })
      }
      if (response.data) {
        const list = response.data.map(e => new WorkType().parse(e))
        this.setOnList({ categoryId: categoryId, list })
      }
      return response.status
    }
    return new ResponseStatus(200)
  }

  @Action
  @UnAuthorize
  async getWorkTypeListByCategoryIds(categoryIdList: Array<string>) {
    try {
      const { status, data } = await PlatformWorkTypeGateway.getWorkTypeListByCategoryIds(categoryIdList)
      if (status.isSuccess()) {
        data.forEach(type => {
          this.setOnList({
            categoryId: type.categoryId,
            list: type.workTypeList.map(item => new WorkType().parse(item))
          })
        })
      }
      return status
    } catch (e) {
      // todo
    }
  }

  /**
   * 根据工种id集合查询每个工种报名数
   * @param param
   * @return response
   */
  @Action
  @UnAuthorize
  async listWorkTypeCommodityOpenNumber(param: WorkTypeCommodityOpenNumberQueryParamDTO) {
    const res = await PlatformCommodity.listWorkTypeCommodityOpenNumber(param)
    if (res.status.isSuccess()) {
      const list = new Array<WorkTypeCommodityOpenNumberWithName>()
      const ids: Array<string> = res.data.map(d => d.workTypeId)
      const workTypeResponse = await PlatformWorkTypeGateway.getWorkTypeListByIds(ids)
      if (workTypeResponse.status.isSuccess()) {
        res.data.forEach(d => {
          const workType = workTypeResponse.data.find(w => w.id === d.workTypeId)
          if (workType) {
            list.push({
              workTypeId: d.workTypeId,
              workTypeName: workType.name,
              openNumber: d.openNumber
            })
          }
        })
      }
      res.data = list
      this.SET_LAZY_WORK_TYPE_OPEN_NUMBER_LIST(list)
    }
    return res
  }

  /**
   * 根据工种id集合查询每个工种报名数【渠道商主页】
   * @param param
   * @return response
   */
  @Action
  @UnAuthorize
  async listWorkTypeCommodityChannelVendorOpenNumber(param: WorkTypeCommodityOpenNumberQueryParamDTO) {
    const res = await PlatformCommodity.listWorkTypeCommodityChannelVendorOpenNumber(param)
    if (res.status.isSuccess()) {
      const list = new Array<WorkTypeCommodityOpenNumberWithChannelVendor>()
      const ids: Array<string> = res.data.map(d => d.workTypeId)
      const workTypeResponse = await PlatformWorkTypeGateway.getWorkTypeListByIds(ids)
      if (workTypeResponse.status.isSuccess()) {
        res.data.forEach(d => {
          const workType = workTypeResponse.data.find(w => w.id === d.workTypeId)
          if (workType) {
            list.push({
              workTypeId: d.workTypeId,
              workTypeName: workType.name,
              openNumber: d.openNumber,
              channelVendorId: d.channelVendorId
            })
          }
        })
      }
      res.data = list
      this.SET_LAZY_WORK_TYPE_CHANNEL_VENDOR_OPEN_NUMBER_LIST(list)
    }
    return res
  }

  /**
   * 获取当前已开班的工种数(状态层不管理状态)
   * @param commodityState 指定一个商品状态，如果没指定则返回所有 UPED:上架|DOWNED下架
   * @return response
   */
  @Action
  @UnAuthorize
  async countWorkTypeHasCommodityNumber(commodityState?: string) {
    return await PlatformCommodity.countWorkTypeHasCommodityNumber(commodityState + '')
  }

  /**
   * 根据工种id获取所属的工种分类信息包括父分类
   * @param workTypeId
   * @return 返回完整的工种类别路径id集合
   */
  @Action
  @UnAuthorize
  async getCategoryListByWorkTypeId(workTypeId: string): Promise<Array<string>> {
    const res = await PlatformWorkTypeCategoryGateway.getCategoryListByWorkTypeId({
      workTypeId: workTypeId,
      containsParent: true
    })
    if (res.status.isSuccess()) {
      const resultList = new Array<string>()
      res.data.forEach(parent => {
        // 遍历根节点
        if (parent.parentId === '-1') {
          let result = '/' + parent.id
          // 找下有没有子节点
          const sub = res.data.find(d => d.parentId === parent.id) || undefined
          if (sub) {
            result += '/' + sub.id
          }
          result += '/' + workTypeId
          resultList.push(result)
        }
      })
      return resultList
    }
    return new Array<string>()
  }

  @Mutation
  private setOnList(payload: { categoryId: string; list: Array<WorkType> }) {
    Vue.set(this.workTypeMap, payload.categoryId, payload.list)
    payload.list.forEach(workType => this.setWorkTypeToCache(workType))
  }

  @Mutation
  private setWorkTypeToCache(workType: WorkType) {
    if (workType) {
      Vue.set(this.cache, workType.id, workType)
    }
  }

  @Mutation
  private SET_LAZY_WORK_TYPE_OPEN_NUMBER_LIST(lazyWorkTypeOpenNumberList: Array<WorkTypeCommodityOpenNumberWithName>) {
    this.lazyWorkTypeOpenNumberList = lazyWorkTypeOpenNumberList
  }

  @Mutation
  private SET_LAZY_WORK_TYPE_CHANNEL_VENDOR_OPEN_NUMBER_LIST(
    lazyWorkTypeOpenNumberList: Array<WorkTypeCommodityOpenNumberWithChannelVendor>
  ) {
    this.lazyWorkTypeChannelVendorOpenNumberList = lazyWorkTypeOpenNumberList
  }

  @Mutation
  private SET_WORK_TYPE_CATEGORY_LIST(workTypeAndCategoryList: Array<WorkTypeAndCategory>) {
    this.workTypeAndCategoryList = workTypeAndCategoryList
  }

  get getExistsWorkTypeById() {
    return (id: string): WorkType => {
      return this.cache[id]
    }
  }

  get getWorkTypeCategoryNameByPath() {
    return (path: string): string => {
      return this.workTypeAndCategoryList.find(w => w.path === path)?.name || ''
    }
  }
}

export default getModule(WorkTypeModule)
