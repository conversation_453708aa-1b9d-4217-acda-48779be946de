import CreateCourseVo from '@api/service/management/resource/course/mutation/vo/CreateCourse'
import MsCourseResourceV1, {
  CourseChapterRequest,
  CourseCreateRequest,
  CourseOutlineWithSubOutlineRequest
} from '@api/ms-gateway/ms-course-resource-v1'
import Chapter from '@api/service/management/resource/course/mutation/vo/Chapter'
import Courseware from '@api/service/management/resource/course/mutation/vo/Courseware'
import { ResponseStatus } from '@hbfe/common'

class CreateCourse extends CourseCreateRequest {
  /**
   * 课程名称
   */
  name: string

  /**
   * 保存
   */
  async save(): Promise<ResponseStatus> {
    const result = await MsCourseResourceV1.createCourse(this)
    return new ResponseStatus(result.status.code, result.status.getMessage())
  }

  /**
   * 转换器
   * @param createCourseVo
   */
  static from(createCourseVo: CreateCourseVo) {
    const dto = new CreateCourse()
    dto.name = createCourseVo.name
    dto.iconPath = createCourseVo.picture
    dto.categoryIds = createCourseVo.categoryId?.length
      ? [createCourseVo.categoryId[createCourseVo.categoryId.length - 1]]
      : []
    dto.courseOutlines = new Array<CourseOutlineWithSubOutlineRequest>()
    dto.aboutsContent = createCourseVo.description
    createCourseVo.chapters.forEach((chapter: Chapter) => {
      const courseSubOutline = new CourseOutlineWithSubOutlineRequest()
      courseSubOutline.name = chapter.name
      courseSubOutline.sort = chapter.sort
      courseSubOutline.courseChapters = new Array<CourseChapterRequest>()
      chapter.coursewares.forEach((courseware: Courseware) => {
        const chapterRequest = new CourseChapterRequest()
        chapterRequest.name = courseware.name
        chapterRequest.sort = courseware.sort
        chapterRequest.auditionStatus = courseware.trialType
        chapterRequest.coursewareId = courseware.id
        courseSubOutline.courseChapters.push(chapterRequest)
      })
      dto.courseOutlines.push(courseSubOutline)
    })
    dto.supplierId = createCourseVo.supplierId
    return dto
  }
}

export default CreateCourse
