/*
 * @Description: 资讯查询工厂
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-09 18:38:33
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 15:07:22
 */
import QueryNewsList from '@api/service/management/news/query/query-news-list/QueryNewsList'
import QueryNewsDetail from '@api/service/management/news/query/query-news-detail/QueryNewsDetail'
class QueryNewsFactory {
  /**
   * 资讯列表
   * @returns
   */
  getQueryNewsList() {
    return new QueryNewsList()
  }
  /**
   * 资讯详情
   * @param id
   * @returns
   */
  getQueryNewsDetail(id: string) {
    return new QueryNewsDetail(id)
  }
}

export default QueryNewsFactory
