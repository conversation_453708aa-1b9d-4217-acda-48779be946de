"""独立部署的微服务,K8S服务名:ms-batch-return-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""返回退货的原因id和原因描述的列Map,key为原因id,value为原因描述"""
	prepareReturn:Map
}
type Mutation {
	"""同意退货"""
	agreeReturnApply(request:BatchReturnOrderAgreeApplyRequest):BatchReturnOrderAgreeApplyResponse
	"""批量同意退货"""
	agreeReturnBatchApply(request:BatchReturnOrderAgreeBatchApplyRequest):BatchReturnOrderAgreeBatchApplyResponse
	"""确认退款，前端要求已退款给予300状态码"""
	confirmBatchRefund(request:BatchReturnOrderConfirmBatchRefundRequest):BatchReturnOrderConfirmBatchRefundResponse
	"""确认退款，前端要求已退款给予300状态码"""
	confirmRefund(request:BatchReturnOrderConfirmRefundRequest):BatchReturnOrderConfirmRefundResponse
	"""拒绝退货"""
	rejectReturnApply(request:BatchReturnOrderRejectApplyRequest):BatchReturnOrderRejectApplyResponse
	"""重新回收资源"""
	retryRecycleResource(request:BatchReturnOrderRetryRecycleRequest):Void
	"""继续退款"""
	retryRefund(request:BatchReturnOrderRetryRefundRequest):Void
	"""卖家取消退货申请"""
	sellerCancelReturnApply(request:BatchReturnOrderCancelApplyRequest):BatchReturnOrderCancelApplyResponse
}
"""批次退货单同意申请请求
	<AUTHOR>
"""
input BatchReturnOrderAgreeApplyRequest @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.request.BatchReturnOrderAgreeApplyRequest") {
	"""批次退货单号"""
	batchReturnOrderNo:String!
	"""审批意见"""
	approveComment:String
}
"""批次退货单批量同意申请请求
	<AUTHOR>
"""
input BatchReturnOrderAgreeBatchApplyRequest @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.request.BatchReturnOrderAgreeBatchApplyRequest") {
	"""批次退货单号"""
	batchReturnOrderNoList:[String]!
	"""审批意见"""
	approveComment:String
}
"""批次退货单申请取消请求
	<AUTHOR>
"""
input BatchReturnOrderCancelApplyRequest @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.request.BatchReturnOrderCancelApplyRequest") {
	batchReturnOrderNo:String!
	cancelReason:String
}
"""批次退货单批量确认退款请求
	<AUTHOR>
"""
input BatchReturnOrderConfirmBatchRefundRequest @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.request.BatchReturnOrderConfirmBatchRefundRequest") {
	batchReturnOrderNoList:[String]!
}
"""批次退货单确认退款请求
	<AUTHOR>
"""
input BatchReturnOrderConfirmRefundRequest @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.request.BatchReturnOrderConfirmRefundRequest") {
	batchReturnOrderNo:String!
}
"""批次退货单拒绝申请请求
	<AUTHOR>
"""
input BatchReturnOrderRejectApplyRequest @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.request.BatchReturnOrderRejectApplyRequest") {
	batchReturnOrderNo:String!
	"""审批意见"""
	approveComment:String
}
input BatchReturnOrderRetryRecycleRequest @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.request.BatchReturnOrderRetryRecycleRequest") {
	batchReturnOrderNo:String!
}
input BatchReturnOrderRetryRefundRequest @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.request.BatchReturnOrderRetryRefundRequest") {
	batchReturnOrderNo:String!
}
type BatchReturnOrderAgreeApplyResponse @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.response.BatchReturnOrderAgreeApplyResponse") {
	code:String
	message:String
	batchReturnOrderNo:String
}
type BatchReturnOrderAgreeBatchApplyResponse @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.response.BatchReturnOrderAgreeBatchApplyResponse") {
	batchReturnOrderAgreeApplyResponseList:[BatchReturnOrderAgreeApplyResponse]
}
type BatchReturnOrderCancelApplyResponse @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.response.BatchReturnOrderCancelApplyResponse") {
	code:String
	message:String
}
type BatchReturnOrderConfirmBatchRefundResponse @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.response.BatchReturnOrderConfirmBatchRefundResponse") {
	batchReturnOrderConfirmRefundResponseList:[BatchReturnOrderConfirmRefundResponse]
}
type BatchReturnOrderConfirmRefundResponse @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.response.BatchReturnOrderConfirmRefundResponse") {
	code:String
	message:String
	batchReturnOrderNo:String
}
type BatchReturnOrderRejectApplyResponse @type(value:"com.fjhb.ms.batchreturn.v1.kernel.gateway.graphql.response.BatchReturnOrderRejectApplyResponse") {
	code:String
	message:String
}

scalar List
