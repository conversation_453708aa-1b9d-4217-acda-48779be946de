<template>
  <div v-if="$hasPermission('logArrangement')" desc="查看编排日志" actions="querySearch">
    <el-drawer title="查看编排日志" :visible.sync="visible" size="1000px" custom-class="m-drawer m-table-auto">
      <div class="drawer-bd">
        <!--表格-->
        <el-table stripe :data="tableData" class="m-table f-mt10" v-loading="loading">
          <el-table-column type="index" label="NO." width="80"> </el-table-column>
          <el-table-column label="编排任务类型" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.taskType }}
            </template>
          </el-table-column>
          <el-table-column label="" min-width="100" align="center">
            <template slot="header">
              允许重叠
              <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                <div slot="content">
                  <p>
                    ①若为是：将按系统默认规则进行学习（一人一班每日可学习12选课学时，不限制学习时段），同一学员不同班级学习产生的学习记录允许存在重叠；
                  </p>
                  <p>②若为否：若选否，将按在线学习规则进行学习，同一学员所有班级学习产生的学习记录不允许存在重叠。</p>
                </div>
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              {{ scope.row.allowOverlap ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.createTime }}
            </template>
          </el-table-column>
          <el-table-column label="编排完成时间" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.arrangeTime }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <template v-if="$hasPermission('arrangementDetail')" desc="查看编排日志详情" actions="@DetailLogDrawer">
                <el-button type="text" @click="searchDetail(scope.row)">查看详情</el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button type="primary" @click="callBack">返 回</el-button>
      </div>
      <detail-log-drawer ref="detailRef" title="查看编排详情" />
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Vue, Component, Ref } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import ExecutionItem from '@api/service/management/intelligence-learning/model/ExecutionItem'
  import DetailLogDrawer from '@hbfe/jxjy-admin-intelligentLearning/src/learning-task-tracking/components/detail-log-drawer.vue'
  import ArrangeLogItem from '@api/service/management/intelligence-learning/model/ArrangeLogItem'
  import { LogTypeEnum } from '@api/service/management/intelligence-learning/enum/LogType'
  import { bind, debounce } from 'lodash-decorators'

  @Component({
    components: {
      DetailLogDrawer
    }
  })
  export default class extends Vue {
    @Ref('detailRef') detailRef: DetailLogDrawer
    /**
     * 显隐
     */
    visible = false

    /**
     * 表格数据
     */
    tableData = new Array<ArrangeLogItem>()

    /**
     * 加载状态
     */
    loading = false

    /**
     * 存储数据
     */
    executionItem = new ExecutionItem()

    /**
     * 打开弹框
     */
    async open(row: ExecutionItem) {
      this.tableData = new Array<ArrangeLogItem>()
      this.visible = true
      Object.assign(this.executionItem, row)
      await this.querySearch()
    }

    /**
     * 查询操作
     */
    async querySearch() {
      try {
        this.loading = true
        this.tableData = await this.executionItem.queryArrangeLogList()
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 返回
     */
    callBack() {
      this.visible = false
    }

    /**
     * 查看详情
     */
    @bind
    @debounce(200)
    async searchDetail(row: ArrangeLogItem) {
      await this.detailRef.open(row.logId, LogTypeEnum.arrangement)
    }
  }
</script>
