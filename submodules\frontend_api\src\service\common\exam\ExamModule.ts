// import axios, { Response } from 'axios'
import $http from '@packages/request'
import PlatformExamGateway, {
  AnswerExamPaperReportDTO,
  ExamRecordDTO,
  ExamRecordParamDTO,
  SingleQuestionAnswerParamDTO,
  SingleQuestionAnswerStatisticDTO
} from '@api/gateway/PlatformExam'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import Response, { ResponseStatus } from '../../../Response'
import ExamGateway, { FavoriteQuestionRequest } from '@api/gateway/btpx@GeneralExam-default'
import ConfigCenterModule from '../config-center/ConfigCenter'
import { Role, RoleType } from '../../../Secure'
import SubmitAnswerExamPaper from './interface/SubmitAnswerExamPaper'
import UserModule from '@api/service/customer/user/query-user/UserModule'
import LearningSchemeModule from '@api/service/customer/myscheme/LearningSchemeModule'
import AnswerExamPaper from '@api/service/common/models/exam/answer/AnswerExamPaper'

const markQuestion = function(question: any) {
  switch (question.questionType) {
    case 1:
      if (question.answered) {
        question.answersResultState = question.answersResult === question.correct ? 1 : 2
      } else {
        question.answersResultState = 0
      }
      break
    case 2:
      if (question.answered) {
        question.answersResultState = question.answersResult === question.correctAnswer ? 1 : 2
      } else {
        question.answersResultState = 0
      }
      break
    case 3:
      if (question.answered) {
        if (Array.isArray(question.answersResult) && question.answersResult.length > 0) {
          const more = question.answersResult.some(
            (result: any) => !question.correctAnswers.some((answer: any) => answer === result)
          )
          const less = question.correctAnswers.some(
            (answer: any) => !question.answersResult.some((result: any) => answer === result)
          )
          question.answersResultState = !more && !less ? 1 : 2
        } else {
          question.answersResultState = 2
        }
      } else {
        question.answersResultState = 0
      }
      break
    case 6:
      question.subQuestionBase.forEach((subQuestion: any) => {
        markQuestion(subQuestion)
      })
      break
  }
}

const urlPath = {
  loginIn: '/gateway/examination/loginin',
  enterExamRound: '/gateway/examAnswerPaper/enterExamRound',
  enterExamRoundAndReturnSimpleInfo: '/gateway/examAnswerPaper/simple/enterExamRound',
  getAnswerQuestion: '/gateway/examAnswerPaper/simple/',
  getAnswerQuestions: '/gateway/examAnswerPaper/simple/',
  submitQuestion: '/gateway/examAnswerPaper/submitQuestion',
  viewAnswerExamPaper: '/gateway/examAnswerPaper/viewAnswerExamPaper',
  getExamRoundSurplusTime: '/gateway/examAnswerPaper/getExamRoundSurplusTime',
  getRemainExamTimes: '/gateway/examAnswerPaper/',
  setMark: '/gateway/examAnswerPaper/setMark',
  setSubQuestionMark: '/gateway/examAnswerPaper/setSubQuestionMark',
  completeExam: '/gateway/examAnswerPaper/completeExam',
  /**
   * 试卷预览的地址
   */
  previewPath: '/gateway/examAnswerPaper/preViewByToken'
}

export class AnswerExamPaperQuestionPosition {
  questionItemIndex: number
  questionIndex: number
}

export class SubmitQuestion {
  // 大题下标
  index: number
  // 小题下标
  subIndex: number
  // 子题下标
  subQuestionIndex: number
  // 回答结果
  answersResult: any
  /**
   * 提交的试题类型
   * 1:判断题，2：单选题  3：多选题 4：填空题 5：简答题
   * 11：父子题 判断题 12：父子题 单选题 13：父子题 多选题 14：父子题 填空题 15：父子题简答题
   * */
  submitType: number
}

export class MarkQuestion {
  // 大题下标
  index: number
  // 小题下标
  subIndex: number
  // 子题下标
  subQuestionIndex: number
  // 是否标记
  mark: boolean
  /**
   * 提交的试题类型
   * 1:判断题，2：单选题  3：多选题 4：填空题 5：简答题
   * 11：父子题 判断题 12：父子题 单选题 13：父子题 多选题 14：父子题 填空题 15：父子题简答题
   * */
  submitType: number
}

export interface IState {
  schemeId: string
  issueId: string
  answerExamPaper: AnswerExamPaper
  questionAnswerStatistic: Array<SingleQuestionAnswerStatisticDTO>
  examReport: AnswerExamPaperReportDTO
  // 作答记录基础
  answerRecord: Array<ExamRecordDTO>
  // 剩余考试次数
  remainExamCount: number
}

@Module({
  namespaced: true,
  name: 'CommonExamModule',
  store,
  dynamic: true
})
class ExamModule extends VuexModule implements IState {
  schemeId = ''
  issueId = ''
  answerExamPaper = new AnswerExamPaper() //答卷
  questionAnswerStatistic = new Array<SingleQuestionAnswerStatisticDTO>() // 答题情况
  examReport = new AnswerExamPaperReportDTO() // 作答报告 -- 需要 loadExamReport()
  answerRecord = new Array<ExamRecordDTO>() //用户历史考试记录
  // 剩余考试次数
  remainExamCount = 0

  /**
   * 进入场次
   */
  @Role([RoleType.user])
  @Action
  async enterExamRound(params: {
    schemeId: string
    issueId: string
    examRoundId: string
    practice: boolean
    simpleInfo: boolean
  }) {
    const enterParams: any = params
    const urlExam = params.simpleInfo ? urlPath.enterExamRoundAndReturnSimpleInfo : urlPath.enterExamRound
    enterParams.mandatorySubmissionWhenTimesOverInAnytimeExamRound = !params.practice
    enterParams.relaxMinSubmitTimeLimit = params.practice
    const response: Response<any> = await $http.get(ConfigCenterModule.getIngressByName('ingress.exam') + urlExam, {
      params: params
    })
    if (!response.status.isSuccess() || response.data.status === false) {
      return new ResponseStatus(500, response.data?.info || '进入场次失败')
    }
    const answerExamPaper: AnswerExamPaper = response.data?.info || new AnswerExamPaper()
    answerExamPaper.answersId = response.data.info?.id
    answerExamPaper.id = response.data.info?.answerInfoId || response.data.info?.lastAnswerInfoId
    answerExamPaper.lastIndex = response.data.info?.lastAnswerInfoIndex
    answerExamPaper.autoSubmit = response.data.info?.mandatorySubmission
    answerExamPaper.submitTime = response.data.info?.completeTime
    answerExamPaper.autoMark = response.data.info?.auto
    answerExamPaper.marked = response.data.info?.markComplete
    this.setSchemeInfo(params)
    this.setAnswerExamPaper(answerExamPaper)
    if (!params.simpleInfo && answerExamPaper.questionItemDtos) {
      const questionIds = new Array<string>()
      answerExamPaper.questionItemDtos
        .flatMap(questionItems => questionItems.answerQuestionDtos)
        .forEach(question => {
          questionIds.push(question.id)
        })
      const response = await ExamGateway.validateFavorite({
        schemeId: this.schemeId,
        issueId: this.issueId,
        questionIds: questionIds
      })
      if (response.status.isSuccess()) {
        for (const questionItems of answerExamPaper.questionItemDtos) {
          if (questionItems.answerQuestionDtos) {
            for (const question of questionItems.answerQuestionDtos) {
              question.favorite = response.data.some(p => p.questionId == question.id && p.favorite)
            }
          }
        }
      } else {
        return response.status
      }
    }
    this.setAnswerExamPaper(answerExamPaper)
    return new ResponseStatus(200)
  }

  @Role([RoleType.user])
  @Action
  async loadQuestionInfo(params: AnswerExamPaperQuestionPosition) {
    // const questionItemIndex = this.answerExamPaper?.questionItemDtos?.findIndex(p => p.id === params.questionItemId) || -1
    const questionItemIndex = params.questionItemIndex
    const questionItemId = this.answerExamPaper?.questionItemDtos[questionItemIndex]?.id
    if (questionItemIndex === -1 || !questionItemId) {
      return new ResponseStatus(500, '试题项不存在')
    }
    const questionIndex = params.questionIndex
    // const questionIndex = this.answerExamPaper?.questionItemDtos?.find(p => p.id === params.questionItemId)
    //   ?.answerQuestionDtos.findIndex((p: any) => p.id === params.questionId) || -1
    const questionId = this.answerExamPaper?.questionItemDtos[questionItemIndex]?.answerQuestionDtos[questionIndex]?.id
    if (questionIndex === -1 || !questionId) {
      return new ResponseStatus(500, '试题不存在')
    } else if (
      this.answerExamPaper?.questionItemDtos[questionItemIndex]?.answerQuestionDtos[questionIndex].loaded === true
    ) {
      return new ResponseStatus(200, '试题已加载')
    }
    const loadParam = {
      answerPaperIndex: this.answerExamPaper.lastIndex,
      answerPaperId: this.answerExamPaper.id,
      questionItemIndex: questionItemIndex,
      questionItemId: questionItemId,
      questionIndex: questionIndex,
      questionId: questionId
    }
    const response: Response<any> = await $http.post(
      ConfigCenterModule.getIngressByName('ingress.exam') +
        urlPath.getAnswerQuestion +
        this.answerExamPaper.answersId +
        '/question',
      loadParam
    )
    if (!response.status.isSuccess() || response.data.status === false) {
      return new ResponseStatus(500, response.data?.info || '获取试题信息失败')
    }
    const question = response.data.info
    const param: FavoriteQuestionRequest = new FavoriteQuestionRequest()
    param.schemeId = this.schemeId
    param.issueId = this.issueId
    param.questionId = question.id
    const favoriteResponse = await ExamGateway.isFavoriteQuestion(param)
    if (favoriteResponse.status.isSuccess()) {
      question.favorite = favoriteResponse.data
    }
    this.setAnswerQuestionToAnswerPaper({
      questionItemIndex: questionItemIndex,
      questionIndex: questionIndex,
      answerQuestion: question
    })
    return new ResponseStatus(200)
  }
  @Role([RoleType.user])
  @Action
  async loadAnsweredQuestionInfos() {
    if (!this.answerExamPaper) {
      return new ResponseStatus(500, '请先入场考试')
    }

    // const questionItemIndex = params.questionItemIndex
    // const questionItemId = this.answerExamPaper?.questionItemDtos[questionItemIndex]?.id
    // if (questionItemIndex === -1 || !questionItemId) {
    //   return new ResponseStatus(500, '试题项不存在')
    // }
    // const questionIndex = params.questionIndex
    // // const questionIndex = this.answerExamPaper?.questionItemDtos?.find(p => p.id === params.questionItemId)
    // //   ?.answerQuestionDtos.findIndex((p: any) => p.id === params.questionId) || -1
    // const questionId = this.answerExamPaper?.questionItemDtos[questionItemIndex]?.answerQuestionDtos[questionIndex]?.id
    // if (questionIndex === -1 || !questionId) {
    //   return new ResponseStatus(500, '试题不存在')
    // }
    const loadParam = {
      answerPaperIndex: this.answerExamPaper.lastIndex,
      answerPaperId: this.answerExamPaper.id,
      questionPositions: new Array<any>()
    }

    this.answerExamPaper?.questionItemDtos.forEach((questionItem, questionItemIndex) => {
      questionItem.answerQuestionDtos?.forEach((answerQuestion: any, questionIndex: number) => {
        if (answerQuestion.answered && !answerQuestion.loaded) {
          const questionPosition = {
            questionItemIndex: questionItemIndex,
            questionItemId: questionItem.id,
            questionIndex: questionIndex,
            questionId: answerQuestion.id
          }
          loadParam.questionPositions.push(questionPosition)
        }
      })
    })

    if (loadParam.questionPositions.length == 0) {
      return new ResponseStatus(200)
    }

    const response: Response<any> = await $http.post(
      ConfigCenterModule.getIngressByName('ingress.exam') +
        urlPath.getAnswerQuestions +
        this.answerExamPaper.answersId +
        '/questions',
      loadParam
    )
    if (!response.status.isSuccess() || response.data.status === false) {
      return new ResponseStatus(500, response.data?.info || '获取试题信息失败')
    }
    const questions = response.data.info

    const questionIds = new Array<string>()
    questions.forEach((question: any) => {
      questionIds.push(question.id)
    })
    const response2 = await ExamGateway.validateFavorite({
      schemeId: this.schemeId,
      issueId: this.issueId,
      questionIds: questionIds
    })

    if (response2.status.isSuccess()) {
      for (const question of questions) {
        const param: FavoriteQuestionRequest = new FavoriteQuestionRequest()
        param.schemeId = this.schemeId
        param.issueId = this.issueId
        param.questionId = question.id
        question.favorite = response2.data.some(p => p.questionId == question.id && p.favorite)
        const questionPosition = loadParam.questionPositions.find(p => p.questionId === question.id)
        this.setAnswerQuestionToAnswerPaper({
          questionItemIndex: questionPosition.questionItemIndex,
          questionIndex: questionPosition.questionIndex,
          answerQuestion: question
        })
      }
    }

    this.setAnswerExamPaper(this.answerExamPaper)

    return new ResponseStatus(200)
  }

  /**
   * 进入预览试卷
   */
  @Action
  async enterPreviewExam(params: { token: string }) {
    const response: Response<any> = await $http.post(
      ConfigCenterModule.getIngressByName('ingress.exam') + urlPath.previewPath,
      params
    )
    if (!response.status.isSuccess() || response.data.status === false) {
      return new ResponseStatus(500, response.data?.info || '预览试卷失败')
    }
    const answerExamPaper: AnswerExamPaper = response.data?.info || new AnswerExamPaper()
    answerExamPaper.answersId = response.data.info?.id
    answerExamPaper.id = response.data.info?.answerInfoId || response.data.info?.lastAnswerInfoId
    answerExamPaper.autoSubmit = response.data.info?.mandatorySubmission
    answerExamPaper.submitTime = response.data.info?.completeTime
    answerExamPaper.autoMark = response.data.info?.auto
    answerExamPaper.marked = response.data.info?.markComplete
    this.setAnswerExamPaper(answerExamPaper)
    // if (answerExamPaper.questionItemDtos) {
    //   for (const questionItems of answerExamPaper.questionItemDtos) {
    //     if (questionItems.answerQuestionDtos) {
    //       for (const question of questionItems.answerQuestionDtos) {
    //         const param: PreExamFavoriteQuestionRequest = new PreExamFavoriteQuestionRequest()
    //         param.schemeId = this.schemeId
    //         param.issueId = this.issueId
    //         param.questionId = question.id
    //         const response = await PreExamGateway.isFavoriteQuestion(param)
    //         if (response.status.isSuccess()) {
    //           question.favorite = response.data
    //         }
    //       }
    //     }
    //   }
    // }
    this.setAnswerExamPaper(answerExamPaper)
    return new ResponseStatus(200)
  }

  /**
   * 查看答卷
   */
  @Role([RoleType.user])
  @Action
  async viewAnswerExamPaper(params: {
    schemeId: string
    issueId: string
    id: string
    historyAnswerExamPaperId: string
  }) {
    const response: Response<any> = await $http.get(
      ConfigCenterModule.getIngressByName('ingress.exam') + urlPath.viewAnswerExamPaper,
      {
        params: {
          examPaperId: params.id,
          historyAnswerExamPaperId: params.historyAnswerExamPaperId
        }
      }
    )
    if (!response.status.isSuccess() || response.data.status === false) {
      return new ResponseStatus(500, response.data?.info || '获取答卷失败')
    }
    const answerExamPaper: AnswerExamPaper = response.data?.info || new AnswerExamPaper()
    answerExamPaper.answersId = response.data.info?.id
    answerExamPaper.id = response.data.info?.answerInfoId || response.data.info?.lastAnswerInfoId
    answerExamPaper.autoSubmit = response.data.info?.mandatorySubmission
    answerExamPaper.submitTime = response.data.info?.completeTime
    answerExamPaper.autoMark = response.data.info?.auto
    answerExamPaper.marked = response.data.info?.markComplete
    this.setSchemeInfo(params)
    this.setAnswerExamPaper(answerExamPaper)
    if (answerExamPaper.questionItemDtos) {
      const questionIds = new Array<string>()
      answerExamPaper.questionItemDtos
        .flatMap(questionItems => questionItems.answerQuestionDtos)
        .forEach(question => {
          questionIds.push(question.id)
        })
      const response = await ExamGateway.validateFavorite({
        schemeId: this.schemeId,
        issueId: this.issueId,
        questionIds: questionIds
      })
      if (response.status.isSuccess()) {
        for (const questionItems of answerExamPaper.questionItemDtos) {
          if (questionItems.answerQuestionDtos) {
            for (const question of questionItems.answerQuestionDtos) {
              question.favorite = response.data.some(p => p.questionId == question.id && p.favorite)
            }
          }
        }
      }
    }
    this.setAnswerExamPaper(answerExamPaper)
    return new ResponseStatus(200)
  }

  /**
   * 标记试题
   * @param ctx
   * @param question
   */
  @Action
  async doChangeQuestionUnsure(param: MarkQuestion) {
    const mark: any = param
    mark.answerExamPaperId = this.answerExamPaper.answersId
    let axiosResponse: Response<any>
    if (param.subQuestionIndex <= 10) {
      axiosResponse = await $http.get(ConfigCenterModule.getIngressByName('ingress.exam') + urlPath.setMark, {
        params: mark
      })
    } else {
      axiosResponse = await $http.get(
        ConfigCenterModule.getIngressByName('ingress.exam') + urlPath.setSubQuestionMark,
        { params: mark }
      )
    }
    if (!axiosResponse.status.isSuccess() || axiosResponse.data.status === false) {
      return new ResponseStatus(500, axiosResponse.data?.info || '标记试题失败')
    }
    this.setMarkQuestion(mark)
    return new ResponseStatus(200)
  }

  /**
   * 回答试题
   * @param ctx
   * @param question
   */
  @Action
  async doAnswerQuestion(param: SubmitQuestion) {
    const submit: any = param
    submit.answerExamPaperId = this.answerExamPaper.answersId
    this.setAnswer(submit)

    return new ResponseStatus(200)
  }

  /**
   * 提交试题
   * @param ctx
   * @param question
   */
  @Action
  async doSubmitQuestion(param: SubmitQuestion) {
    const submit: any = param
    submit.answerExamPaperId = this.answerExamPaper.answersId
    const axiosResponse: Response<any> = await $http.post(
      ConfigCenterModule.getIngressByName('ingress.exam') + urlPath.submitQuestion,
      submit
    )
    if (!axiosResponse.status.isNetSuccess() || axiosResponse.data.status === false) {
      return new ResponseStatus(500, axiosResponse.data?.info || '提交试题失败')
    }
    this.setAnswer(submit)
    return new ResponseStatus(200)
  }

  /**
   * 提交试卷
   */
  @Action
  async doSubmitPaper(param: SubmitAnswerExamPaper) {
    param.replyDto.id = this.answerExamPaper.answersId
    const axiosResponse: Response<any> = await $http.post(
      ConfigCenterModule.getIngressByName('ingress.exam') + urlPath.completeExam,
      param
    )
    if (!axiosResponse.status.isSuccess()) {
      return new ResponseStatus(500, axiosResponse.data?.info || '交卷失败')
    }
    // 可重新加载方案
    LearningSchemeModule.SET_IS_LOAD_MY_SCHEME(false)
    this.ALERT_EXAM_RELOAD({
      schemeId: this.schemeId,
      issueId: this.issueId,
      answersId: this.answerExamPaper.answersId
    })
    const status: ResponseStatus = await this.viewAnswerExamPaper({
      schemeId: this.schemeId,
      issueId: this.issueId,
      id: this.answerExamPaper.answersId,
      historyAnswerExamPaperId: this.answerExamPaper.id
    })
    return status
  }

  /**
   * 获取场次剩余时间
   * @param ctx
   * @param examRoundId
   */
  @Action
  async getRestTime() {
    const response = await $http.get(
      ConfigCenterModule.getIngressByName('ingress.exam') + urlPath.getExamRoundSurplusTime,
      {
        params: {
          answerExamPaperId: this.answerExamPaper.answersId
        }
      }
    )
    this.setRestTime(response.data?.info || response.data)

    return new ResponseStatus(200)
  }

  /**
   * 获取剩余考试次数，-1的话等于不限次数
   */
  @Action
  async getRemainExamTimes() {
    const response = await $http.get(
      ConfigCenterModule.getIngressByName('ingress.exam') +
        urlPath.getRemainExamTimes +
        this.answerExamPaper.answersId +
        '/remainExamTimes'
    )
    this.setRemainExamTimes(response.data?.info)

    return new ResponseStatus(200)
  }

  /**
   * 添加收藏
   */
  @Role([RoleType.user])
  @Action
  async addFavorite(questionId: string) {
    const addFavorite: FavoriteQuestionRequest = new FavoriteQuestionRequest()
    addFavorite.schemeId = this.schemeId
    addFavorite.issueId = this.issueId
    addFavorite.questionId = questionId
    const response = await ExamGateway.addUserFavoriteQuestion(addFavorite)
    this.ALERT_EXAM_RELOAD({
      schemeId: this.schemeId,
      issueId: this.issueId,
      answersId: this.answerExamPaper.answersId
    })
    return response.status
  }

  /**
   * 移除收藏
   */
  @Role([RoleType.user])
  @Action
  async removeFavorite(questionId: string) {
    const addFavorite: FavoriteQuestionRequest = new FavoriteQuestionRequest()
    addFavorite.schemeId = this.schemeId
    addFavorite.issueId = this.issueId
    addFavorite.questionId = questionId
    const response = await ExamGateway.removeUserFavoriteQuestion(addFavorite)
    this.ALERT_EXAM_RELOAD({
      schemeId: this.schemeId,
      issueId: this.issueId,
      answersId: this.answerExamPaper.answersId
    })
    return response.status
  }

  /**
   * 获取答题解析
   */
  @Role([RoleType.user])
  @Action
  async loadQuestionAnswerStatistic(questionIds: Array<string>) {
    const paramDTO: SingleQuestionAnswerParamDTO = new SingleQuestionAnswerParamDTO()
    paramDTO.questionId = questionIds
    const response = await PlatformExamGateway.statisticUserSingleQuestionAnswerInfo(paramDTO)

    if (questionIds) {
      const result: Array<SingleQuestionAnswerStatisticDTO> = response?.data
      if (result) {
        const restQuestionIds = questionIds.filter(p => !result.some(r => r.questionId === p))
        const generateQuestions = restQuestionIds.map(id => {
          const result = new SingleQuestionAnswerStatisticDTO()
          result.questionId = id
          result.allAnsweredTimes = 0
          result.allCorrectRate = 0
          result.allCorrectTimes = 0
          result.allWrongTimes = 0
          result.allUnknownTimes = 0
          result.answeredTimes = 0
          result.correctRate = 0
          result.correctTimes = 0
          result.wrongTimes = 0
          result.unknownTimes = 0
          return result
        })
        result.push(...generateQuestions)
        this.setAnswerStatistic(result)
      }
    }

    return response.status
  }

  /**
   * 获取作答报告
   */
  @Role([RoleType.user])
  @Action
  async loadExamReport(payload: { id: string; schemeId: string; learningId: string }) {
    let response = await PlatformExamGateway.statisticAnswerExamPaperReport(payload.id)
    console.log(response)
    if (!response.status.isSuccess()) {
      return response.status
    }
    let times = 0
    while (response.data?.answerExamRecordId !== this.answerExamPaper.id && times < 3) {
      times++
      await new Promise(resolve => {
        setTimeout(resolve, 2000)
      })
      response = await PlatformExamGateway.statisticAnswerExamPaperReport(payload.id)
      console.log(response)
      if (!response.status.isSuccess()) {
        return response.status
      }
    }
    this.setExamReport(response?.data)

    const answerRecordParam: ExamRecordParamDTO = new ExamRecordParamDTO()
    answerRecordParam.schemeId = payload.schemeId
    answerRecordParam.learningId = payload.learningId
    answerRecordParam.statisticSize = 15
    answerRecordParam.userId = UserModule.userInfo.userId
    const answerRecordResponse = await PlatformExamGateway.listExamRecord(answerRecordParam)
    if (answerRecordResponse.status.isSuccess()) {
      this.setAnswerRecord(answerRecordResponse?.data)
      return answerRecordResponse.status
    }

    return answerRecordResponse.status
  }

  @Mutation
  setAnswerExamPaper(payload: AnswerExamPaper) {
    this.answerExamPaper = payload
    if (this.answerExamPaper.marked === false) {
      this.answerExamPaper.questionItemDtos.forEach(questionItem => {
        questionItem.answerQuestionDtos.forEach((question: any) => {
          markQuestion(question)
        })
      })
    }
  }

  @Mutation
  setSchemeInfo(payload: { schemeId: string; issueId: string }) {
    this.schemeId = payload.schemeId
    this.issueId = payload.issueId
  }

  @Mutation
  setRestTime(payload: number) {
    this.answerExamPaper.surplusTime = payload
  }
  @Mutation
  setRemainExamTimes(payload: number) {
    this.remainExamCount = payload
  }

  @Mutation
  setAnswer(payload: SubmitQuestion) {
    if (payload.submitType <= 10) {
      this.answerExamPaper.questionItemDtos[payload.index].answerQuestionDtos[payload.subIndex].answersResult =
        payload.answersResult
      this.answerExamPaper.questionItemDtos[payload.index].answerQuestionDtos[payload.subIndex].answered =
        payload.answersResult === false ||
        (Array.isArray(payload.answersResult) && payload.answersResult.length > 0) ||
        (!Array.isArray(payload.answersResult) && !!payload.answersResult)
      markQuestion(this.answerExamPaper.questionItemDtos[payload.index].answerQuestionDtos[payload.subIndex])
    } else {
      this.answerExamPaper.questionItemDtos[payload.index].answerQuestionDtos[payload.subIndex].subQuestionBase[
        payload.subQuestionIndex
      ].answersResult = payload.answersResult
      this.answerExamPaper.questionItemDtos[payload.index].answerQuestionDtos[payload.subIndex].subQuestionBase[
        payload.subQuestionIndex
      ].answered =
        payload.answersResult === false ||
        (Array.isArray(payload.answersResult) && payload.answersResult.length > 0) ||
        (!Array.isArray(payload.answersResult) && !!payload.answersResult)
      markQuestion(
        this.answerExamPaper.questionItemDtos[payload.index].answerQuestionDtos[payload.subIndex].subQuestionBase[
          payload.subQuestionIndex
        ]
      )
    }
  }

  @Mutation
  setMarkQuestion(payload: MarkQuestion) {
    if (payload.submitType <= 10) {
      this.answerExamPaper.questionItemDtos[payload.index].answerQuestionDtos[payload.subIndex].marked = payload.mark
    } else {
      this.answerExamPaper.questionItemDtos[payload.index].answerQuestionDtos[payload.subIndex].subQuestionBase[
        payload.subQuestionIndex
      ].marked = payload.mark
    }
  }

  @Mutation
  setAnswerStatistic(payload: Array<SingleQuestionAnswerStatisticDTO>) {
    this.questionAnswerStatistic = payload
  }

  @Mutation
  setExamReport(payload: AnswerExamPaperReportDTO) {
    this.examReport = payload
  }

  @Mutation
  setAnswerRecord(payload: Array<ExamRecordDTO>) {
    this.answerRecord = payload
  }

  @Mutation
  setAnswerQuestionToAnswerPaper(payload: { questionItemIndex: number; questionIndex: number; answerQuestion: any }) {
    const answerPaper = this.answerExamPaper
    answerPaper.questionItemDtos[payload.questionItemIndex].answerQuestionDtos[payload.questionIndex] =
      payload.answerQuestion
    if (answerPaper.questionItemDtos[payload.questionItemIndex].answerQuestionDtos[payload.questionIndex]) {
      answerPaper.questionItemDtos[payload.questionItemIndex].answerQuestionDtos[payload.questionIndex].loaded = true
    }
    this.answerExamPaper = answerPaper
  }

  @Mutation
  ALERT_EXAM_RELOAD(payload: any) {
    console.log('提醒需要重载' + JSON.stringify(payload))
  }
}

export default getModule(ExamModule)
