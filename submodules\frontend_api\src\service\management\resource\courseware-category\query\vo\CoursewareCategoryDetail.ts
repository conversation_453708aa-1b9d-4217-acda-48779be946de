import { CoursewareCategoryResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class CoursewareCategoryDetail {
  id: string = undefined
  name: string = undefined
  sort: number = undefined
  parentId: string = undefined
  createTime: string = undefined
  parentName: string = undefined

  children: Array<CoursewareCategoryDetail> = undefined
  hasChildren = true

  static from(courseCategoryResponse: CoursewareCategoryResponse) {
    const coursewareCategoryDetail = new CoursewareCategoryDetail()
    Object.assign(coursewareCategoryDetail, courseCategoryResponse)
    return coursewareCategoryDetail
  }
}

export default CoursewareCategoryDetail
