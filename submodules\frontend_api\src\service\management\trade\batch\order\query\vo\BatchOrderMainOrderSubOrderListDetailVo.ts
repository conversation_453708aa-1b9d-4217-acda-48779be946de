import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
import { BatchOrderSubOrderStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderSubOrderStatus'
import { ExchangeTrainClassStatusEnum } from '@api/service/management/train-class/query/enum/ExchangeTrainClassStatusType'
import { BatchOrderSubOrderAfterSaleStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderSubOrderAfterSaleStatus'
import ExchangeOrderStatus from '@api/service/management/train-class/query/vo/ExchangeOrderStatus'
import { ReturnOrderResponse, SubOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
/**
 * @description 【集体报名订单】批次单-主单-子单列表详情
 */
class BatchOrderMainOrderSubOrderListDetailVo extends SubOrderResponse {
  /**
   * 子单id
   */
  subOrderNo = ''

  /**
   * 培训方案类型 1：选课规则 2：自主选课
   */
  schemeType: SchemeTypeEnum = null

  /**
   * 培训方案名称
   */
  schemeName = ''

  /**
   * 班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuValueNameProperty: SkuPropertyResponseVo = new SkuPropertyResponseVo()

  /**
   * 学时
   */
  period: number = null

  /**
   * 数量
   */
  quantities: number = null

  /**
   * 实付金额
   */
  payAmount: number = null

  /**
   * 订单状态
   */
  subOrderStatus: BatchOrderSubOrderStatusEnum = null

  /**
   * 换班状态
   */
  exchangeStatus: ExchangeTrainClassStatusEnum = null

  /**
   * 【子单】售后状态
   */
  afterSale: BatchOrderSubOrderAfterSaleStatusEnum = null

  /**
   * 换货单状态列表，用于查看（换货）详情
   */
  exchangeOrderStatusList: ExchangeOrderStatus[]

  /**
   * 个人退款单编号
   */
  refundOrderNo = ''

  /**
   * 批次退款单号
   */
  batchRefundOrderNo = ''

  /**
   * 退款单详情
   */
  refundDetail: ReturnOrderResponse = new ReturnOrderResponse()

  /**
   * 期别名称
   */
  issueName = ''

  /**
   * 期别id
   */
  issueId = ''

  /**
   * 期数
   */
  issueNo = ''

  /**
   * 是否换期
   */
  isExchangeIssue = false
}

export default BatchOrderMainOrderSubOrderListDetailVo
