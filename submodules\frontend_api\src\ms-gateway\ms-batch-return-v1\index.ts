import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-batch-return-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-batch-return-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 批次退货单同意申请请求
<AUTHOR>
 */
export class BatchReturnOrderAgreeApplyRequest {
  /**
   * 批次退货单号
   */
  batchReturnOrderNo: string
  /**
   * 审批意见
   */
  approveComment?: string
}

/**
 * 批次退货单批量同意申请请求
<AUTHOR>
 */
export class BatchReturnOrderAgreeBatchApplyRequest {
  /**
   * 批次退货单号
   */
  batchReturnOrderNoList: Array<string>
  /**
   * 审批意见
   */
  approveComment?: string
}

/**
 * 批次退货单申请取消请求
<AUTHOR>
 */
export class BatchReturnOrderCancelApplyRequest {
  batchReturnOrderNo: string
  cancelReason?: string
}

/**
 * 批次退货单批量确认退款请求
<AUTHOR>
 */
export class BatchReturnOrderConfirmBatchRefundRequest {
  batchReturnOrderNoList: Array<string>
}

/**
 * 批次退货单确认退款请求
<AUTHOR>
 */
export class BatchReturnOrderConfirmRefundRequest {
  batchReturnOrderNo: string
}

/**
 * 批次退货单拒绝申请请求
<AUTHOR>
 */
export class BatchReturnOrderRejectApplyRequest {
  batchReturnOrderNo: string
  /**
   * 审批意见
   */
  approveComment?: string
}

export class BatchReturnOrderRetryRecycleRequest {
  batchReturnOrderNo: string
}

export class BatchReturnOrderRetryRefundRequest {
  batchReturnOrderNo: string
}

export class BatchReturnOrderAgreeApplyResponse {
  code: string
  message: string
  batchReturnOrderNo: string
}

export class BatchReturnOrderAgreeBatchApplyResponse {
  batchReturnOrderAgreeApplyResponseList: Array<BatchReturnOrderAgreeApplyResponse>
}

export class BatchReturnOrderCancelApplyResponse {
  code: string
  message: string
}

export class BatchReturnOrderConfirmBatchRefundResponse {
  batchReturnOrderConfirmRefundResponseList: Array<BatchReturnOrderConfirmRefundResponse>
}

export class BatchReturnOrderConfirmRefundResponse {
  code: string
  message: string
  batchReturnOrderNo: string
}

export class BatchReturnOrderRejectApplyResponse {
  code: string
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 返回退货的原因id和原因描述的列Map,key为原因id,value为原因描述
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async prepareReturn(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.prepareReturn,
    operation?: string
  ): Promise<Response<Map<string, string>>> {
    return commonRequestApi<Map<string, string>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 同意退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeReturnApply(
    request: BatchReturnOrderAgreeApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.agreeReturnApply,
    operation?: string
  ): Promise<Response<BatchReturnOrderAgreeApplyResponse>> {
    return commonRequestApi<BatchReturnOrderAgreeApplyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量同意退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeReturnBatchApply(
    request: BatchReturnOrderAgreeBatchApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.agreeReturnBatchApply,
    operation?: string
  ): Promise<Response<BatchReturnOrderAgreeBatchApplyResponse>> {
    return commonRequestApi<BatchReturnOrderAgreeBatchApplyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 确认退款，前端要求已退款给予300状态码
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async confirmBatchRefund(
    request: BatchReturnOrderConfirmBatchRefundRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.confirmBatchRefund,
    operation?: string
  ): Promise<Response<BatchReturnOrderConfirmBatchRefundResponse>> {
    return commonRequestApi<BatchReturnOrderConfirmBatchRefundResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 确认退款，前端要求已退款给予300状态码
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async confirmRefund(
    request: BatchReturnOrderConfirmRefundRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.confirmRefund,
    operation?: string
  ): Promise<Response<BatchReturnOrderConfirmRefundResponse>> {
    return commonRequestApi<BatchReturnOrderConfirmRefundResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 拒绝退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rejectReturnApply(
    request: BatchReturnOrderRejectApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.rejectReturnApply,
    operation?: string
  ): Promise<Response<BatchReturnOrderRejectApplyResponse>> {
    return commonRequestApi<BatchReturnOrderRejectApplyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重新回收资源
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async retryRecycleResource(
    request: BatchReturnOrderRetryRecycleRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.retryRecycleResource,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 继续退款
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async retryRefund(
    request: BatchReturnOrderRetryRefundRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.retryRefund,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 卖家取消退货申请
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sellerCancelReturnApply(
    request: BatchReturnOrderCancelApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sellerCancelReturnApply,
    operation?: string
  ): Promise<Response<BatchReturnOrderCancelApplyResponse>> {
    return commonRequestApi<BatchReturnOrderCancelApplyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
