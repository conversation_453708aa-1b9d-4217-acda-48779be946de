import { BillOrderInfoDTO } from '@api/gateway/PlatformCollectiveRegister'

class InvoiceInfo {
  /**
   * 发票类型：1普通发票，2增值税普通发票，3增值税专用发票
   */
  type: number
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型，0：无抬头类型，1：个人，2：企业
   */
  titleType: number
  /**
   * 统一社会信用代码
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账号
   */
  account: string
  /**
   * 是否电子票
   */
  electron: boolean
  /**
   * 发票状态,0：未开票，1：开具中，2：开票成功，3：开票失败
   */
  status = 0
  /**
   * 电子邮件
   */
  email: string
  /**
   * 发票代码
   */
  billCode: string
  /**
   * 发票验证码
   */
  billVeriCode: string
  /**
   * 用于接收通知的电话
   */
  noticePhone: string
  /**
   * 是否冻结
   */
  frozen: boolean
  /**
   * 开票金额
   */
  money: number
  /**
   * 发票号码
   */
  billNo: string
  /**
   * 发票id
   */
  id: string
  /**
   * 申请开票时间
   */
  createTime: string

  /**
   * 批次号
   */
  batchNo: string

  /**
   * 红票状态
未开票： 0
开票中： 1
开票成功： 2
开票失败： 3
   */
  redState: string

  static from(billInfo: BillOrderInfoDTO): InvoiceInfo {
    const invoiceInfo = new InvoiceInfo()
    if (!billInfo) {
      return invoiceInfo
    }
    invoiceInfo.type = Number.parseInt(billInfo.invoiceType)
    invoiceInfo.title = billInfo.title
    invoiceInfo.titleType = Number.parseInt(billInfo.titleType)
    invoiceInfo.taxpayerNo = billInfo.taxpayerNo
    invoiceInfo.address = billInfo.address
    invoiceInfo.phone = billInfo.phone
    invoiceInfo.bankName = billInfo.bankName
    invoiceInfo.account = billInfo.account
    invoiceInfo.electron = true
    invoiceInfo.status = Number.parseInt(billInfo.state)
    invoiceInfo.email = billInfo.email
    invoiceInfo.billCode = billInfo.billCode
    invoiceInfo.billVeriCode = billInfo.billVeriCode
    invoiceInfo.noticePhone = billInfo.noticePhone
    invoiceInfo.frozen = billInfo.frozen
    invoiceInfo.money = billInfo.money
    invoiceInfo.billNo = billInfo.billNo
    invoiceInfo.id = billInfo.id
    invoiceInfo.createTime = billInfo.createTime
    invoiceInfo.batchNo = billInfo.batchNo
    invoiceInfo.redState = billInfo.redState
    return invoiceInfo
  }
}

export default InvoiceInfo
