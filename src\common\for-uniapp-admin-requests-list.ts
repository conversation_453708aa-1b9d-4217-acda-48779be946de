import OpenStatisticModule from '@api/service/customer/statistic/open-statistic/OpenStatisticModule'

/**
 * 这个文件的场景描述
 * 安全对象在解析 admin 管理类型的角色链路，是从 frontend_web_admin 为入口，所有页面涉及的调用，都将会解析到 permission-tree.json 当中。
 * 但推广助手在uni-app项目中的请求，将不会被解析到里面，这时候如果使用供应商、渠道商登录的情况下，这边调用的接口会出现 403 的情况。
 * 临时解决方案，将 uni-app 涉及到 admin 角色调用的请求，在这边调用一次。解析器将会在属于全局的安全对象名单中生成列表
 */
class ForUniappAdminRequestsList {
  async getTotalOpenStatisticForChannelVendorWithoutContext() {
    await OpenStatisticModule.getCountByTrainingInstitutionWithoutContext('')
  }
}

export default ForUniappAdminRequestsList
