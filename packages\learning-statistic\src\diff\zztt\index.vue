<route-meta>
{
"isMenu": true,
"title": "学员学习明细",
"sort": 5,
"icon": "icon-mingxi"
}
</route-meta>
<template>
  <div>
    <ZzttLearningStatistic ref="learningStatisticRef">
      <template #sale-channel="{ localSkuProperty }">
        <el-form-item label="销售渠道" v-if="!isHaveZtRole">
          <biz-sale-channel-select v-model="localSkuProperty.tradeChannels"></biz-sale-channel-select>
        </el-form-item>
      </template>
    </ZzttLearningStatistic>
  </div>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-learningStatistic/src/models/index'
  import { StudentSchemeLearningRequestVo } from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
  import QueryStudentLearningListDiff from '@api/service/diff/management/zztt/statistical-report/query/QueryStudentLearningList'
  import LearningStatistic from '@hbfe/jxjy-admin-learningStatistic/src/index.vue'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import { StudentLearningStaticsVo } from '@api/service/diff/management/zztt/statistical-report/query/vo/StudentLearningStaticsVo'
  import bizSaleChannelSelect from '@hbfe/jxjy-admin-components//src/biz-sale-channel-select.vue'
  import OnlineClassTable from '@hbfe/jxjy-admin-learningStatistic/src/diff/zztt/__components__/online-class-table.vue'

  class NewSchemeSkuProperty extends SchemeSkuProperty {
    tradeChannels: number = null
  }
  @Component({
    components: {
      OnlineClassTable
    }
  })
  class ZzttLearningStatistic extends LearningStatistic {
    /**
     * 查询类
     */
    queryStudentLearningList = new QueryStudentLearningListDiff()
    /**
     * 查询参数
     */
    filter: StudentSchemeLearningRequestVo = new StudentSchemeLearningRequestVo()
    /**
     * 本地sku属性
     */
    localSkuProperty = new NewSchemeSkuProperty()
    /**
     * 表格数据
     */
    tableData: Array<StudentLearningStaticsVo> = new Array<StudentLearningStaticsVo>()

    /**
     * 查询列表——分销商
     */
    async pageStudentSchemeLearningInDistributor() {
      this.setFilter()
      return await this.queryStudentLearningList.pageStudentSchemeLearningInDistributor(this.page, this.filter)
    }

    /**
     * 学员学习统计列表--专题管理员
     */
    async listRegionLearningReportFormsInTrainingChannel() {
      this.setFilter()
      return await this.queryStudentLearningList.listRegionLearningReportFormsInTrainingChannel(this.page, this.filter)
    }

    /**
     * 学员学习统计列表
     */
    async listRegionLearningReportFormsInServicer() {
      this.setFilter()
      return await this.queryStudentLearningList.listRegionLearningReportFormsInServicer(this.page, this.filter)
    }
    /**
     * 设置过滤条件
     */
    setFilter() {
      this.filter.learningRegister.saleChannels = this.localSkuProperty.tradeChannels
        ? [this.localSkuProperty.tradeChannels]
        : undefined
    }
  }
  @Component({
    components: {
      ZzttLearningStatistic,
      bizSaleChannelSelect
    }
  })
  export default class extends Vue {
    @Ref('learningStatisticRef') learningStatisticRef: ZzttLearningStatistic

    /**
     * 判断当前用户是否专题管理员角色类型
     */
    get isHaveZtRole() {
      return QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    }
  }
</script>
