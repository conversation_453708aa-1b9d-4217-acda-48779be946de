<route-params content="/:schoolId"></route-params>
<route-meta>
{
"isMenu": true,
"title": "修改网校",
"hideMenu": true
}
</route-meta>
<script lang="ts">
  import UpdateSchool from '@hbfe/jxjy-admin-management/src/updateSchool.vue'
  import { ZXMGLY } from '@/models/RoleTypes'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  @RoleTypeDecorator({
    modifyQuery: [ZXMGLY],
    BasicInfo: [ZXMGLY],
    SchoolConfig: [ZXMGLY],
    TemplateConfig: [ZXMGLY],
    ManageAccount: [ZXMGLY],
    AddService: [ZXMGLY]
  })
  export default class extends UpdateSchool {}
</script>
