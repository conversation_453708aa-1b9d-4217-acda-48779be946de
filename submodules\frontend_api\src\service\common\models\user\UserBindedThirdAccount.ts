class UserBindedThirdAccount {
  id: string
  /**
   * 账户id
   */
  accountId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 互联方式
   @see com.fjhb6.basicdata.userdata.lang.UserMaterialConstant#REGISTER_TYPE_QQ
   @see com.fjhb6.basicdata.userdata.lang.UserMaterialConstant#REGISTER_TYPE_SINA_WEIBO
   */
  connectType: number
  /**
   * 用户唯一标识
   */
  openId: string
  /**
   * unionId
   */
  unionId: string
  /**
   * 访问令牌
   */
  accessToken: string
  /**
   * 剩余失效时间, 单位: 秒
   */
  expiresIn: number
  /**
   * 刷新的Token
   */
  refreshToken: string
  /**
   * 用户昵称
   */
  nickname: string
  /**
   * 用户50*50规格头像
   */
  figure_url_50: string
  /**
   * 用户100*100规格头像
   */
  figure_url_100: string
  /**
   * 用户的性别
   */
  gender: string
  /**
   * 所属地区
   */
  location: string
  /**
   * 授权时间
   */
  authorizeTime: string
  /**
   * 是否可用
   */
  available: boolean
}

export default UserBindedThirdAccount
