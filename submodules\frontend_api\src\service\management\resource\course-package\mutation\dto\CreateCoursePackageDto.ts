import CreateCoursePackageVo from '@api/service/management/resource/course-package/mutation/vo/CreateCoursePackageVo'
import MsCourseResourceV1, {
  CourseInPackageRequest,
  CoursePackageCreateRequest
} from '@api/ms-gateway/ms-course-resource-v1'
import { ResponseStatus } from '@hbfe/common'
import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'

class CreateCoursePackageDto extends CoursePackageCreateRequest {
  async save(): Promise<ResponseStatus> {
    const { status } = await MsCourseResourceV1.createCoursePackage(this)
    return new ResponseStatus(status.code, status.getMessage())
  }

  static from(vo: CreateCoursePackageVo): CreateCoursePackageDto {
    const dto = new CreateCoursePackageDto()
    dto.name = vo.name
    dto.displayName = vo.showName
    dto.courseInPackageList = new Array<CourseInPackageRequest>()
    vo.addedList.forEach((course: CourseInCoursePackage, index) => {
      const courseInRequest = new CourseInPackageRequest()
      courseInRequest.courseId = course.id
      courseInRequest.period = course.period
      courseInRequest.sort = index + 1
      dto.courseInPackageList.push(courseInRequest)
    })
    return dto
  }
}

export default CreateCoursePackageDto
