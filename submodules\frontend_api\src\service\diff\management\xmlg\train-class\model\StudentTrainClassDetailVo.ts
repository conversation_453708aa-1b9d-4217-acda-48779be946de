import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
import { StudentSchemeLearningResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import TrainContentBasicInfoVo from '@api/service/management/train-class/query/vo/TrainContentBasicInfoVo'
import TrainContentLearningInfoVo from '@api/service/management/train-class/query/vo/TrainContentLearningInfoVo'
import TrainContentExamInfoVo from '@api/service/management/train-class/query/vo/TrainContentExamInfoVo'
import MyTrainClassDetailClassVo from '@api/service/management/train-class/query/vo/MyTrainClassDetailClassVo'
import TrainClassModule from '@api/service/management/train-class/TrainClassManagerModule'
import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import AssessRequire from '@api/service/management/train-class/query/vo/AssessRequire'
import AssessResult from '@api/service/management/train-class/query/vo/AssessResult'
import studentCourseLearningQuery from '@api/platform-gateway/student-course-learning-query-back-gateway'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import studentCourseLearningQueryDiff from '@api/platform-gateway/platform-jxjypxtypt-student-learning-backstage'
import DateScope from '@api/service/common/models/DateScope'
import StudentPeriodLog from '@api/service/management/train-class/offlinePart/model/StudentPeriodLog'
import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
import StudentTrainClassDetailVoMain from '@api/service/management/train-class/query/vo/StudentTrainClassDetailVo'
import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'
/**
 * @description 学员培训方案列表详情
 */
class StudentTrainClassDetailVo extends StudentTrainClassDetailVoMain {
  /**
   * 培训班json配置
   */
  trainClassJsonConfig = ''

  /**
   * 培训班方案信息（未打平）
   */
  trainClassDetail: MyTrainClassDetailClassVo = new MyTrainClassDetailClassVo()

  /**
   * 参训资格ID
   */
  qualificationId = ''

  /**
   * 学号
   */
  studentNo = ''

  /**
   * 培训班状态（学员状态）1:有效 2:冻结 3:失效
   */
  schemeStatus: number = null

  /**
   * 基础信息
   */
  basicInfo: TrainContentBasicInfoVo = new TrainContentBasicInfoVo()

  /**
   * 是否配置课程
   */
  hasConfigCourse = false

  /**
   * 学习内容
   */
  course: TrainContentLearningInfoVo = new TrainContentLearningInfoVo()

  /**
   * 是否配置考试
   */
  hasConfigExam = false

  /**
   * 班级考试
   */
  exam: TrainContentExamInfoVo = new TrainContentExamInfoVo()

  /**
   * 考核要求
   */
  assessRequire: AssessRequire = new AssessRequire()

  /**
   * 考核结果
   */
  assessResult: AssessResult = new AssessResult()

  /**
   * 学习方式id
   */
  learningId = ''

  /**
   * 是否有模拟数据
   */
  haveSimulate: boolean = undefined
  /**
   * 是否只有考试模拟数据
   */
  onlyHaveExamSimulate: boolean = undefined
  /**
   * 方案模拟数据合格时间
   */
  simulatePassTime: string = undefined
  /**
   * 方案配置成果是否同步
   */
  needDataSync = false
  /**
   * 考试模拟成绩
   */
  simulateExamScore = ''
  /**
   * 考试模拟时间
   */
  simulateExamTime: DateScope = new DateScope()
  /**
   * 销售渠道
   */
  saleChannel: SaleChannelEnum = SaleChannelEnum.self
  /**
   * 专题名称
   */
  saleChannelName = ''

  /**
   * 第三方平台
   */
  thirdPartyPlatform = ''
  /**
   * 是否开启智能学习
   */
  isIntelligentLearning = false
  /**
   * 智能学习失败原因
   */
  failReason = ''
  /**
   * 智能学习状态 0待执行 1编排失败（报表异常数据）2执行中 3执行成功 4执行失败 5已取消
   */
  IntelligentLearningResult: number = null
  /**
   * 期别学习记录
   */
  periodStudy: StudentPeriodLog = new StudentPeriodLog()

  /**
   * 方案问卷考核要求
   */
  schemeAssessRequire: boolean = undefined
  /**
   * 用期别id获取期别配置
   */
  getIssueConfigById(issueId: string): IssueConfigDetail {
    const issueConfigList = this.trainClassDetail?.learningTypeModel?.issue?.issueConfigList
    if (!issueConfigList) {
      return new IssueConfigDetail()
    }
    const currentIssue = issueConfigList.find((item) => item.id === issueId)
    return currentIssue
  }

  static async from(response: StudentSchemeLearningResponse): Promise<StudentTrainClassDetailVo> {
    const detail = new StudentTrainClassDetailVo()
    Object.assign(detail, response)
    // 获取培训方案详情
    const queryUserTrainClassDetail = TrainClassModule.queryTrainClassFactory.getQueryUserTrainClassDetailClass()
    queryUserTrainClassDetail.qualificationId = response.qualificationId ?? null
    const trainClassQueryRes = await queryUserTrainClassDetail.queryTrainClassDetail()
    detail.trainClassDetail = trainClassQueryRes.isSuccess()
      ? queryUserTrainClassDetail.trainClassDetail
      : new MyTrainClassDetailClassVo()
    detail.trainClassJsonConfig = queryUserTrainClassDetail.jsonString ?? null
    StudentTrainClassDetailVo.convertFromTrainClassDetail(detail)
    StudentTrainClassDetailVo.fillAssessInfo(detail)
    return detail
  }

  /**
   * 重新推送
   */
  async rePush() {
    const res = await studentCourseLearningQueryDiff.rePushStudentTrainingResultToGatewayInServicerV2([this.studentNo])
    return res
  }

  /**
   * 数据打平
   */
  static convertFromTrainClassDetail(detail: StudentTrainClassDetailVo) {
    detail.schemeStatus = detail.learningRegister?.status
    /** 基础信息 */
    const trainClassBaseInfo = detail.trainClassDetail?.trainClassBaseInfo
    const courseConfigInfo = detail.trainClassDetail?.learningTypeModel?.courseLearning
    const examConfigInfo = detail.trainClassDetail?.learningTypeModel?.exam
    const userGetLearning = detail.trainClassDetail?.userGetLearning
    // 培训班id
    detail.basicInfo.schemeId = trainClassBaseInfo?.id
    // 培训班商品id
    detail.basicInfo.commoditySkuId = detail.trainClassDetail?.commoditySkuId
    // 培训方案名称
    detail.basicInfo.schemeName = trainClassBaseInfo?.name
    // sku属性值
    detail.basicInfo.skuValueNameProperty = trainClassBaseInfo?.skuProperty ?? new SkuPropertyResponseVo()
    // 培训方案类型
    const schemeType = trainClassBaseInfo?.schemeType
    detail.basicInfo.schemeType = SchemeTypeEnum[SchemeTypeEnum[schemeType]]
    // 开通方式
    detail.basicInfo.openType = detail.learningRegister?.registerType
    // 开通时间
    detail.basicInfo.openTime = detail.learningRegister?.registerTime
    // 考核成绩
    detail.basicInfo.assessScore = detail.trainClassDetail?.learningTypeModel?.exam?.examPassScore
    // 考核学时
    detail.basicInfo.assessPeriod = trainClassBaseInfo?.period
    // 考核结果
    detail.basicInfo.assessResult = userGetLearning?.trainingResult
    /** 考核情况 */
    detail.hasConfigCourse = courseConfigInfo?.isSelected
    // 整体要求学时
    detail.course.totalRequirePeriod = userGetLearning?.requirePeriod
    // 整体已完成学时
    detail.course.totalCompletePeriod = userGetLearning?.currentPeriod
    const totalUnCompletePeriod = CalculatorObj.subtract(
      detail.course.totalRequirePeriod,
      detail.course.totalCompletePeriod
    )
    // 整体待完成学时
    detail.course.totalUnCompletePeriod = totalUnCompletePeriod >= 0 ? totalUnCompletePeriod : 0
    // 选课要求学时
    detail.course.courseRequirePeriod = userGetLearning?.electiveRequirePeriod
    // 选课未选学时
    detail.course.courseUnCompletePeriod = userGetLearning?.needSelectedCoursePeriod
    const courseCompletePeriod = CalculatorObj.subtract(
      detail.course.courseRequirePeriod,
      detail.course.courseUnCompletePeriod
    )
    // 选课已完成学时
    detail.course.courseCompletePeriod = courseCompletePeriod >= 0 ? courseCompletePeriod : 0
    /** 班级考试 */
    detail.hasConfigExam = examConfigInfo.isSelected
    if (detail.hasConfigExam) {
      // 考试名称
      detail.exam.examName = examConfigInfo?.name
      // 考试总分
      detail.exam.examTotalScore = 100
      // 考试合格分
      detail.exam.examQualifiedScore = examConfigInfo?.examPassScore
      // 考试周期
      detail.exam.examTime.begin = examConfigInfo?.allowStartTime
      detail.exam.examTime.end = examConfigInfo?.allowEndTime
      // 考试时长
      detail.exam.examTimeLength = examConfigInfo?.timeLength / 60
      // 考试剩余次数
      detail.exam.examUnCompleteCount = userGetLearning?.surplusExamCount
      // 考试最高分
      detail.exam.examHighestScore = userGetLearning?.maxExamScore
    }
    // 【课程】学习方式id
    detail.learningId = detail.trainClassDetail?.learningTypeModel?.courseLearning?.learningTypeId
  }

  /**
   * 填充考核信息
   */
  static fillAssessInfo(detail: StudentTrainClassDetailVo) {
    const courseConfigInfo = detail.trainClassDetail?.learningTypeModel?.courseLearning
    const examConfigInfo = detail.trainClassDetail?.learningTypeModel?.exam
    const userGetLearning = detail.trainClassDetail?.userGetLearning
    const learningExperienceLearning = detail.studentLearning.learningExperienceLearning
    /** 心得考核情况 */
    if (
      learningExperienceLearning &&
      learningExperienceLearning.userAssessResult &&
      learningExperienceLearning.userAssessResult.length
    ) {
      const experienceConfig = JSON.parse(learningExperienceLearning.userAssessResult[0])
      detail.assessResult.passLearningExperienceCount = experienceConfig.lessParticipateLearningExperienceTopicCount
        ? experienceConfig.lessParticipateLearningExperienceTopicCount.current
        : 0
    }
    /** 获取考核要求 */
    detail.assessRequire.hasConfigCourse = detail.hasConfigCourse
    if (detail.assessRequire.hasConfigCourse) {
      if (detail.basicInfo.schemeType === TrainClassSchemeEnum.Choose_Course_Learning) {
        // 选课规则
        detail.assessRequire.totalPeriod = CalculatorObj.add(
          courseConfigInfo?.compulsoryRequirePeriod,
          courseConfigInfo?.electiveRequirePeriod
        )
      } else {
        // 自主选课
        detail.assessRequire.totalPeriod = courseConfigInfo?.requirePeriod
      }
      detail.assessRequire.hasConfigCourseQuiz = courseConfigInfo?.configCourseQuiz
      detail.assessRequire.courseQuizPagerStandard = courseConfigInfo?.courseQuizPagerStandard
      detail.assessRequire.courseQuizMinCourseSchedule = courseConfigInfo?.quizConfigModel?.minCourseSchedule
      detail.assessRequire.courseQuizPassScore = courseConfigInfo?.quizConfigModel?.passScore
      detail.assessRequire.enableAppraisal = courseConfigInfo?.enableAppraisal
      detail.assessRequire.enableCompulsoryAppraisal = courseConfigInfo?.enableCompulsoryAppraisal
    }
    detail.assessRequire.hasConfigExam = detail.hasConfigExam
    detail.assessRequire.examPassScore = examConfigInfo.examPassScore
    detail.assessRequire.isExamAssessed = examConfigInfo.isExamAssessed
    /** 获取考核结果 */
    detail.assessResult.acquirePeriod = detail.course.totalCompletePeriod
    // 计算班级学习进度
    if (
      userGetLearning?.requirePeriod > 0 &&
      userGetLearning.currentPeriod > 0 &&
      userGetLearning?.requirePeriod >= userGetLearning?.currentPeriod
    ) {
      detail.assessResult.courseLearningSchedule =
        CalculatorObj.divide(userGetLearning.currentPeriod, userGetLearning.requirePeriod) * 100
    }
    detail.assessResult.examScore = detail.exam.examHighestScore
    detail.assessResult.isQualified = detail.basicInfo.assessResult === 1 ? true : false
    detail.assessResult.qualifiedTime = detail.studentLearning?.trainingResultTime
    if ((detail.trainClassJsonConfig ?? '') !== '') {
      const configJson = JSON.parse(detail.trainClassJsonConfig) as Scheme
      if (configJson) {
        const { schemeAssessItem } = Scheme.parseTrainClassAssess(configJson)
        if (schemeAssessItem) {
          const certificateResults = schemeAssessItem.learningResults?.find((item: any) => {
            return item.type == 2
          })
          detail.assessResult.provideCert = certificateResults?.provideCert
          detail.assessResult.certificateTemplateId = certificateResults?.certificateTemplateId
        }
        detail.needDataSync = configJson.extendProperties.find((item: any) => {
          return item.name == 'needDataSync'
        })?.value as boolean
      }
    }
  }
}

export default StudentTrainClassDetailVo
