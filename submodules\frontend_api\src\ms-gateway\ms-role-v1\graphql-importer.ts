import getAllPermission from './queries/getAllPermission.graphql'
import getAllRoles from './queries/getAllRoles.graphql'
import getCurrentUserSecurityGroup from './queries/getCurrentUserSecurityGroup.graphql'
import getEnabledRoles from './queries/getEnabledRoles.graphql'
import getPermissionByRoleId from './queries/getPermissionByRoleId.graphql'
import getPermissionForEditRole from './queries/getPermissionForEditRole.graphql'
import getRoleById from './queries/getRoleById.graphql'
import isRoleExist from './queries/isRoleExist.graphql'
import pageRolesByQuery from './queries/pageRolesByQuery.graphql'
import addUserOwnRoles from './mutates/addUserOwnRoles.graphql'
import deleteRole from './mutates/deleteRole.graphql'
import getPermission from './mutates/getPermission.graphql'
import removeUserOwnRoles from './mutates/removeUserOwnRoles.graphql'
import saveRole from './mutates/saveRole.graphql'

export {
  getAllPermission,
  getAllRoles,
  getCurrentUserSecurityGroup,
  getEnabledRoles,
  getPermissionByRoleId,
  getPermissionForEditRole,
  getRoleById,
  isRoleExist,
  pageRolesByQuery,
  addUserOwnRoles,
  deleteRole,
  getPermission,
  removeUserOwnRoles,
  saveRole
}
