schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询当前网校下的训前规则配置模板
		@return
	"""
	getTrainingConfigTemplateInServicer:TrainingConfigTemplateResponse @optionalLogin
	"""分页获取方案配置
		@param page
		@param request
		@param needField
		@return
	"""
	pageSchemeConfigByRequestInServicer(page:Page,request:PlatformSchemeConfigRequest,needField:[String]):SchemeConfigResponsePage @page(for:"SchemeConfigResponse") @optionalLogin
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""获取我的班级请求参数"""
input PlatformSchemeConfigRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.PlatformSchemeConfigRequest") {
	"""方案id集合"""
	schemeIds:[String]
	"""是否包含已配置学习规则"""
	isIncludeHasStudyRule:Boolean
	"""培训形式"""
	trainType:String
	"""方案名称"""
	schemeName:String
	"""方案归属行业"""
	industryId:String
}
type SchemeConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.SchemeConfigResponse") {
	schemeId:String
	owner:OwnerResponse
	schemeConfig:String
	skuProperty:SchemeSkuPropertyResponse
	name:String
	hasAntiConfig:Boolean
	antiConfigId:String
	intro:String
}
type BatchOwnerResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.BatchOwnerResponse") {
	unitId:String
	userId:String
}
type OwnerResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.OwnerResponse") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerType:Int
	servicerId:String
	batchOwner:BatchOwnerResponse
}
type SchemeSkuPropertyResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeSkuPropertyResponse") {
	year:SchemeSkuPropertyValueResponse
	province:SchemeSkuPropertyValueResponse
	city:SchemeSkuPropertyValueResponse
	county:SchemeSkuPropertyValueResponse
	industry:SchemeSkuPropertyValueResponse
	subjectType:SchemeSkuPropertyValueResponse
	trainingCategory:SchemeSkuPropertyValueResponse
	trainingProfessional:SchemeSkuPropertyValueResponse
	technicalGrade:SchemeSkuPropertyValueResponse
	positionCategory:SchemeSkuPropertyValueResponse
	trainingObject:SchemeSkuPropertyValueResponse
	jobLevel:SchemeSkuPropertyValueResponse
	jobCategory:SchemeSkuPropertyValueResponse
	subject:SchemeSkuPropertyValueResponse
	grade:SchemeSkuPropertyValueResponse
	learningPhase:SchemeSkuPropertyValueResponse
	discipline:SchemeSkuPropertyValueResponse
	certificatesType:SchemeSkuPropertyValueResponse
	practitionerCategory:SchemeSkuPropertyValueResponse
	trainingInstitution:SchemeSkuPropertyValueResponse
	mainAdditionalItem:SchemeSkuPropertyValueResponse
	trainingWay:SchemeSkuPropertyValueResponse
}
type SchemeSkuPropertyValueResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeSkuPropertyValueResponse") {
	skuPropertyValueId:String
}
"""考勤配置模板"""
type TrainingConfigTemplateResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.TrainingConfigTemplateResponse") {
	"""考勤模板id"""
	attendanceTemplateId:String
	"""签到点信息"""
	attendanceSignIn:AttendanceSignResponse
	"""签退点信息"""
	attendanceSignOut:AttendanceSignResponse
	"""创建人id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""更新时间"""
	updateTime:DateTime
}
"""签到点信息"""
type AttendanceSignResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.nested.AttendanceSignResponse") {
	"""是否开启"""
	enable:Boolean!
	"""签到频率
		半天  1;
		每节课  2;
		@see SignFrequencyTypes
	"""
	frequency:Int!
	"""签到半径"""
	radius:Double!
	"""签到开始前
		单位:(秒)
	"""
	beforeSecond:Int!
	"""开始后
		单位:(秒)
	"""
	afterSecond:Int!
}

scalar List
type SchemeConfigResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SchemeConfigResponse]}
