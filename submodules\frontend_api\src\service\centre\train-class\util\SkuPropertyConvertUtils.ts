import {
  BusinessRegionResponse,
  BusinessYearResponse,
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import {
  SchemeSkuPropertyResponse,
  SkuPropertyResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import {
  CommoditySkuPropertyResponse,
  SkuPropertyListResponse,
  SkuPropertyResponse as SkuTradePropertyResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import RegionTreeVo from '@api/service/centre/train-class/query/vo/RegionTreeVo'
import SkuPropertyListVo from '@api/service/centre/train-class/query/vo/SkuPropertyListVo'
import SkuPropertyVo from '@api/service/centre/train-class/query/vo/SkuPropertyVo'
import SkuVo from '@api/service/centre/train-class/query/vo/SkuVo'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import {
  default as QueryBasicdataDictionaryFactory,
  default as QueryTechnologyLevel
} from '@api/service/common/basic-data-dictionary/QueryBasicdataDictionaryFactory'
import QueryGrade from '@api/service/common/basic-data-dictionary/query/QueryGrade'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import QueryPropertyDetail from '@api/service/common/basic-data-dictionary/query/QueryPropertyDetail'
import { TechnologyLevelVo } from '@api/service/common/basic-data-dictionary/query/QueryTechnologyLevel'
import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
import SubjectTypeVo from '@api/service/common/basic-data-dictionary/query/vo/SubjectTypeVo'
import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
import DataResolve from '@api/service/common/utils/DataResolve'
import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * @description 远端skuProperty转换为本地skuProperty
 */
class SkuPropertyConvertUtils {
  //处理年度
  static async calYear(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const skuVos: SkuVo[] = []
    const dataList: Array<BusinessYearResponse> = await factory.queryYear.getYearByIdList(SkuIds)
    dataList.forEach((tmpItem: BusinessYearResponse) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.id
      skuV.skuPropertyName = tmpItem.year
      skuVos.push(skuV)
    })
    return skuVos
  }

  static async requestBusinessRegionResponse(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const dataList: Array<BusinessRegionResponse> = await factory.queryPhysicalRegion.querRegionDetil(SkuIds)
    return dataList
  }

  //处理地区
  static async calBusinessRegion(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const skuVos: SkuVo[] = []
    const dataList: Array<BusinessRegionResponse> = await SkuPropertyConvertUtils.requestBusinessRegionResponse(
      SkuIds,
      factory
    )
    dataList.forEach((tmpItem: BusinessRegionResponse) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.id
      skuV.skuPropertyName = tmpItem.name
      skuVos.push(skuV)
    })
    return skuVos
  }

  //处理行业
  static async calIndustry(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const skuVos: SkuVo[] = []
    const dataList: Array<IndustryVo> = await factory.queryIndustry.getIndustryByIdList(SkuIds)
    dataList.forEach((tmpItem: IndustryVo) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.id
      skuV.skuPropertyName = tmpItem.name
      skuV.propertyId = tmpItem.propertyId
      skuVos.push(skuV)
    })
    return skuVos
  }
  // 处理行业等级
  static async calTechnicalGrade(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const skuVos: SkuVo[] = []
    await factory.queryTechnologyLevel.query()
    const dataList = factory.queryTechnologyLevel.data
    dataList.forEach((item) => {
      if (item.id === SkuIds[0]) {
        const skuV = new SkuVo()
        skuV.parentId = ''
        skuV.skuPropertyName = item.showName
        skuV.skuPropertyValueId = item.id
        skuVos.push(skuV)
      }
    })
    return skuVos
  }
  /**
   * 通用属性id获取属性名称
   * @param skuIds 属性id数组
   * @param industryId 行业id
   */
  static async calPropertyObject(skuIds: string[], industryId?: string) {
    const skuVos = new Array<SkuVo>()

    const trainingObjectList = await QueryPropertyDetail.getPropertyDetailByIds(industryId, skuIds)

    trainingObjectList?.length &&
      trainingObjectList.map((item) => {
        const sku = new SkuVo()

        sku.skuPropertyName = item.showName || item.name
        sku.parentId = item.parentId
        sku.skuPropertyValueId = item.propertyId
        skuVos.push(sku)
      })

    return skuVos
  }
  //处理科目类型
  static async calTrainingProperty(SkuIds: string[], factory: QueryBasicdataDictionaryFactory, industryId?: string) {
    const skuVos: SkuVo[] = []
    const dataList: Array<SubjectTypeVo> = await factory.querySubjectType.getSubjectTypeByIdList({
      industryId: industryId || '',
      subjectTypeIdList: SkuIds
    })
    dataList.forEach((tmpItem: SubjectTypeVo) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.propertyId
      skuV.skuPropertyName = tmpItem.showName || tmpItem.name
      skuVos.push(skuV)
    })
    return skuVos
  }
  //处理类别
  static async calTrainingCategory(SkuIds: string[], factory: QueryBasicdataDictionaryFactory, industryId?: string) {
    const skuVos: SkuVo[] = []
    const dataList: Array<TrainingCategoryVo> = await factory.queryTrainingCategory.getTrainingCategoryByIdList({
      industryId: industryId || '',
      trainingCategoryIdList: SkuIds
    })
    dataList.forEach((tmpItem: TrainingCategoryVo) => {
      const skuV = new SkuVo()
      skuV.parentId = tmpItem.parentId
      skuV.skuPropertyValueId = tmpItem.propertyId
      skuV.skuPropertyName = tmpItem.showName || tmpItem.name
      skuVos.push(skuV)
    })
    return skuVos
  }
  //处理专业
  static async calTrainingMajor(SkuIds: string[], factory: QueryBasicdataDictionaryFactory, industryId?: string) {
    const skuVos: SkuVo[] = []
    const dataList: Array<TrainingCategoryVo> = await factory.queryTrainingMajor.getTrainingMajorByIdList({
      industryId: industryId || '',
      trainingMajorIdList: SkuIds
    })
    dataList.forEach((tmpItem: TrainingCategoryVo) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.propertyId
      skuV.parentId = tmpItem.parentId
      skuV.skuPropertyName = tmpItem.showName || tmpItem.name
      skuVos.push(skuV)
    })
    return skuVos
  }
  //技术等级
  static async calTechnicalGradeLevel(SkuIds: string[], factory: QueryBasicdataDictionaryFactory, industryId?: string) {
    const skuVos: SkuVo[] = []
    const queryTechnologyLevel = new QueryTechnologyLevel()
    const dataList: Array<TechnologyLevelVo> = await queryTechnologyLevel.queryTechnologyLevel.query()
    dataList.forEach((tmpItem: TechnologyLevelVo) => {
      const skuV = new SkuVo()
      if (SkuIds.includes(tmpItem.id)) {
        skuV.skuPropertyValueId = tmpItem.id
        // skuV.parentId = tmpItem.parentId
        skuV.skuPropertyName = tmpItem.showName
        skuVos.push(skuV)
      }
    })
    return skuVos
  }
  // 处理学段
  static async calTrainingPhase(SkuIds: string[], industry: string) {
    await QueryIndustry.queryIndustry()
    const industryProperty = QueryIndustry.industryList.find((item) => item.id == industry)
    const dataList = await QueryGrade.queryGradeByIndustryV2(industryProperty.id, industryProperty.propertyId)
    const skuVos: SkuVo[] = []
    dataList.forEach((tmpItem: TrainingPropertyResponse) => {
      if (SkuIds.indexOf(tmpItem.propertyId) !== -1) {
        const skuV = new SkuVo()
        skuV.skuPropertyValueId = tmpItem.propertyId
        skuV.skuPropertyName = tmpItem.showName || tmpItem.name
        skuV.parentId = tmpItem.parentId
        skuVos.push(skuV)
      }
    })
    return skuVos
  }
  // 处理学科、学段
  static async calTrainingDiscipline(skuIds: string[], industryId?: string) {
    const skuVos = new Array<SkuVo>()

    const trainingObjectList = await QueryPropertyDetail.getPropertyDetailByIds(industryId, skuIds)

    trainingObjectList?.length &&
      trainingObjectList.map((item) => {
        const sku = new SkuVo()

        sku.skuPropertyName = item.showName || item.name
        sku.parentId = item.parentId
        sku.skuPropertyValueId = item.propertyId
        skuVos.push(sku)
      })

    return skuVos
  }
  static async convertSkuPropertyToValueName(
    source: CommoditySkuPropertyResponse | SchemeSkuPropertyResponse
  ): Promise<SkuPropertyVo> {
    const factory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
    const result = new SkuPropertyVo()
    if (!source) return result
    let skuIds = [] as string[]
    // 年度
    if (source.year) {
      skuIds = [source.year?.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calYear(skuIds, factory)
      result.year = skuVos[0] || new SkuVo()
    }
    // 地区
    skuIds = []
    if (source.province?.skuPropertyValueId) skuIds.push(source.province.skuPropertyValueId)
    if (source.city?.skuPropertyValueId) skuIds.push(source.city.skuPropertyValueId)
    if (source.county?.skuPropertyValueId) skuIds.push(source.county.skuPropertyValueId)
    if (DataResolve.isWeightyArr(skuIds)) {
      const skuVos = await SkuPropertyConvertUtils.calBusinessRegion(skuIds, factory)
      //   console.log('skuIds=', skuIds, 'skuVo=', skuVos)
      // skuname 优先省
      //   skuVos.reverse()
      let regionSkuId = '',
        regionSkuName = ''
      skuVos?.forEach((item) => {
        regionSkuId += !regionSkuId ? item.skuPropertyValueId : '/' + item.skuPropertyValueId
        regionSkuName += !regionSkuName ? item.skuPropertyName : '/' + item.skuPropertyName
      })
      const regionSku = new SkuVo()
      regionSku.skuPropertyName = regionSkuName
      regionSku.skuPropertyValueId = regionSkuId
      //   console.log('orderRegionSku', regionSku)
      result.region = regionSku || new SkuVo()
    }
    // 行业
    if (source.industry) {
      skuIds = [source.industry?.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calIndustry(skuIds, factory)
      result.industry = skuVos[0] || new SkuVo()
    }
    // 技术等级
    if (source.technicalGrade) {
      skuIds = [source.technicalGrade.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calTechnicalGradeLevel(skuIds, factory)
      result.technicalGrade = skuVos[0]
    }
    // 科目类型
    if (source.subjectType) {
      skuIds = [source.subjectType?.skuPropertyValueId]
      if (source.industry?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calTrainingProperty(
          skuIds,
          factory,
          source.industry.skuPropertyValueId
        )
        result.subjectType = skuVos[0] || new SkuVo()
      }
    }
    // 培训类别
    if (source.trainingCategory) {
      skuIds = [source.trainingCategory?.skuPropertyValueId]
      if (source.industry?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calTrainingCategory(
          skuIds,
          factory,
          source.industry.skuPropertyValueId
        )
        result.trainingCategory = skuVos[0] || new SkuVo()
      }
    }
    // 培训专业
    if (source.trainingProfessional) {
      skuIds = [source.trainingProfessional?.skuPropertyValueId]
      if (source.industry?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calTrainingMajor(
          skuIds,
          factory,
          source.industry.skuPropertyValueId
        )
        result.trainingMajor = skuVos[0] || new SkuVo()
      }
    }

    // 岗位类别
    if (source.positionCategory) {
      skuIds = [source.positionCategory?.skuPropertyValueId]
      if (source.positionCategory?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calPropertyObject(skuIds, source.industry.skuPropertyValueId)
        result.positionCategory = skuVos[0] || new SkuVo()
      }
    }

    // 培训对象
    if (source.trainingObject) {
      skuIds = [source.trainingObject?.skuPropertyValueId]
      if (source.trainingObject?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calPropertyObject(skuIds, source.industry.skuPropertyValueId)
        result.trainingObject = skuVos[0] || new SkuVo()
      }
    }

    // 培训对象
    if (source.jobLevel) {
      skuIds = [source.jobLevel?.skuPropertyValueId]
      if (source.jobLevel?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calPropertyObject(skuIds, source.industry.skuPropertyValueId)
        result.jobLevel = skuVos[0] || new SkuVo()
      }
    }

    // 学科、学段转换
    // console.log('转换的sku对象', result)
    if (source.learningPhase) {
      skuIds = [source.learningPhase?.skuPropertyValueId]
      if (source.learningPhase?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calTrainingDiscipline(skuIds, source.industry.skuPropertyValueId)
        result.learningPhase = skuVos[0] || new SkuVo()
      }
    }
    if (source.discipline) {
      skuIds = [source.discipline?.skuPropertyValueId]
      if (source.discipline?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calTrainingDiscipline(skuIds, source.industry.skuPropertyValueId)
        result.discipline = skuVos[0] || new SkuVo()
      }
    }
    if (source.certificatesType) {
      skuIds = [source.certificatesType?.skuPropertyValueId]
      if (source.certificatesType?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calTrainingDiscipline(skuIds, source.industry.skuPropertyValueId)
        result.certificatesType = skuVos[0] || new SkuVo()
      }
    }
    if (source.practitionerCategory) {
      skuIds = [source.practitionerCategory?.skuPropertyValueId]
      if (source.practitionerCategory?.skuPropertyValueId) {
        const skuVos = await SkuPropertyConvertUtils.calTrainingDiscipline(skuIds, source.industry.skuPropertyValueId)
        result.practitionerCategory = skuVos[0] || new SkuVo()
      }
    }
    console.log(result, 'resultresult2')
    return result
  }

  /**
   * 报名记录转换sku
   */
  static async convertToSignUpSkuPropertyVo(
    source: SkuPropertyResponse,
    currentIndustryId?: string
  ): Promise<SkuPropertyListVo> {
    const result = new SkuPropertyListVo()
    const factory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
    let skuIds: string[] = []
    // 年度
    if (DataResolve.isWeightyArr(source.year)) {
      skuIds = source.year
      result.year = await SkuPropertyConvertUtils.calYear(skuIds, factory)
    }
    // 地区
    skuIds = []
    if (DataResolve.isWeightyArr(source.province)) {
      skuIds = [...skuIds, ...source.province]
    }
    if (DataResolve.isWeightyArr(source.city)) {
      skuIds = [...skuIds, ...source.city]
    }
    if (DataResolve.isWeightyArr(source.county)) {
      skuIds = [...skuIds, ...source.county]
    }
    if (skuIds.length) {
      result.regionTree = await SkuPropertyConvertUtils.filterRegionFirstStep(skuIds, factory)
    }
    // 行业
    if (DataResolve.isWeightyArr(source.industry)) {
      skuIds = source.industry
      result.industry = await SkuPropertyConvertUtils.calIndustry(skuIds, factory)
    }
    // 科目类型
    if (DataResolve.isWeightyArr(source.subjectType)) {
      if (currentIndustryId) {
        skuIds = source.subjectType
        result.subjectType = await SkuPropertyConvertUtils.calTrainingProperty(skuIds, factory, currentIndustryId)
      }
    }
    // 培训类别
    if (DataResolve.isWeightyArr(source.trainingCategory)) {
      if (currentIndustryId) {
        skuIds = source.trainingCategory
        result.trainingCategory = await SkuPropertyConvertUtils.calTrainingCategory(skuIds, factory, currentIndustryId)
      }
    }

    // 培训专业
    if (DataResolve.isWeightyArr(source.trainingProfessional)) {
      if (currentIndustryId) {
        skuIds = source.trainingProfessional
        result.trainingMajor = await SkuPropertyConvertUtils.calTrainingMajor(skuIds, factory, currentIndustryId)
      }
    }

    // 岗位类别
    if (DataResolve.isWeightyArr(source.positionCategory)) {
      skuIds = source.positionCategory
      result.positionCategory = await SkuPropertyConvertUtils.calPropertyObject(skuIds, currentIndustryId)
    }

    // 培训对象
    if (DataResolve.isWeightyArr(source.trainingObject)) {
      skuIds = source.trainingObject
      result.trainingObject = await SkuPropertyConvertUtils.calPropertyObject(skuIds, currentIndustryId)
    }

    // 培训对象
    if (DataResolve.isWeightyArr(source.jobLevel)) {
      skuIds = source.jobLevel
      result.jobLevel = await SkuPropertyConvertUtils.calPropertyObject(skuIds, currentIndustryId)
    }
    // 技术等级
    // console.log(source.technicalGrade, 'source.technicalGrade source.technicalGrade')

    // if (DataResolve.isWeightyArr(source.technicalGrade)) {
    //   if (currentIndustryId) {
    //     skuIds = source.technicalGrade
    //     result.technicalGrade = await SkuPropertyConvertUtils.calTechnicalGradeLevel(skuIds, factory, currentIndustryId)
    //   }
    // }
    // 学段学科转换
    if (DataResolve.isWeightyArr(source.learningPhase)) {
      skuIds = source.learningPhase
      result.learningPhase = await SkuPropertyConvertUtils.calPropertyObject(skuIds, currentIndustryId)
    }
    if (DataResolve.isWeightyArr(source.discipline)) {
      skuIds = source.discipline
      result.discipline = await SkuPropertyConvertUtils.calPropertyObject(skuIds, currentIndustryId)
    }
    if (DataResolve.isWeightyArr(source.certificatesType)) {
      skuIds = source.certificatesType
      result.certificatesType = await SkuPropertyConvertUtils.calPropertyObject(skuIds, currentIndustryId)
    }
    if (DataResolve.isWeightyArr(source.discipline)) {
      skuIds = source.practitionerCategory
      result.practitionerCategory = await SkuPropertyConvertUtils.calPropertyObject(skuIds, currentIndustryId)
    }
    return result
  }

  static async convertToSkuPropertyVo(
    source: SkuPropertyListResponse,
    currentIndustryId?: string
  ): Promise<SkuPropertyListVo> {
    const result = new SkuPropertyListVo()
    const factory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
    let skuIds = []
    // 年度
    if (DataResolve.isWeightyArr(source.year)) {
      skuIds = source.year.map((item) => item.skuPropertyValueId)
      result.year = await SkuPropertyConvertUtils.calYear(skuIds, factory)
    }
    // 地区
    skuIds = []
    if (DataResolve.isWeightyArr(source.province)) {
      skuIds.push(...source.province.map((item) => item.skuPropertyValueId))
    }
    if (DataResolve.isWeightyArr(source.city)) {
      skuIds.push(...source.city.map((item) => item.skuPropertyValueId))
    }
    if (DataResolve.isWeightyArr(source.county)) {
      skuIds.push(...source.county.map((item) => item.skuPropertyValueId))
    }
    if (skuIds.length) {
      result.regionTree = await SkuPropertyConvertUtils.filterRegionFirstStep(skuIds, factory, source.region)
    }
    // 行业
    if (DataResolve.isWeightyArr(source.industry)) {
      skuIds = source.industry.map((item) => item.skuPropertyValueId)
      result.industry = await SkuPropertyConvertUtils.calIndustry(skuIds, factory)
    }
    // 科目类型
    if (DataResolve.isWeightyArr(source.subjectType)) {
      if (currentIndustryId) {
        skuIds = source.subjectType.map((item) => item.skuPropertyValueId)
        result.subjectType = await SkuPropertyConvertUtils.calTrainingProperty(skuIds, factory, currentIndustryId)
      }
    }
    // 培训类别
    if (DataResolve.isWeightyArr(source.trainingCategory)) {
      if (currentIndustryId) {
        skuIds = source.trainingCategory.map((item) => item.skuPropertyValueId)
        result.trainingCategory = await SkuPropertyConvertUtils.calTrainingCategory(skuIds, factory, currentIndustryId)
      }
    }

    // 培训专业
    if (DataResolve.isWeightyArr(source.trainingProfessional)) {
      if (currentIndustryId) {
        skuIds = source.trainingProfessional.map((item) => item.skuPropertyValueId)
        result.trainingMajor = await SkuPropertyConvertUtils.calTrainingMajor(skuIds, factory, currentIndustryId)
      }
    }

    // 岗位类别
    if (DataResolve.isWeightyArr(source.positionCategory)) {
      if (currentIndustryId) {
        skuIds = source.positionCategory.map((item) => item.skuPropertyValueId)
        result.positionCategory = await SkuPropertyConvertUtils.calPropertyObject(skuIds, currentIndustryId)
      }
    }

    // 培训对象
    if (DataResolve.isWeightyArr(source.trainingObject)) {
      if (currentIndustryId) {
        skuIds = source.trainingObject.map((item) => item.skuPropertyValueId)
        result.trainingObject = await SkuPropertyConvertUtils.calPropertyObject(skuIds, currentIndustryId)
      }
    }

    // 技术等级
    if (DataResolve.isWeightyArr(source.jobLevel)) {
      if (currentIndustryId) {
        skuIds = source.jobLevel.map((item) => item.skuPropertyValueId)
        result.jobLevel = await SkuPropertyConvertUtils.calPropertyObject(skuIds, currentIndustryId)
      }
    }
    // 集体 学科、学段

    if (DataResolve.isWeightyArr(source.learningPhase)) {
      if (currentIndustryId) {
        skuIds = source.learningPhase.map((item) => item.skuPropertyValueId)
        result.learningPhase = await SkuPropertyConvertUtils.calTrainingDiscipline(
          skuIds,
          source.industry[0].skuPropertyValueId
        )
      }
    }
    if (DataResolve.isWeightyArr(source.discipline)) {
      if (currentIndustryId) {
        skuIds = source.discipline.map((item) => item.skuPropertyValueId)
        result.discipline = await SkuPropertyConvertUtils.calTrainingDiscipline(
          skuIds,
          source.industry[0].skuPropertyValueId
        )
      }
    }
    if (DataResolve.isWeightyArr(source.certificatesType)) {
      if (currentIndustryId) {
        skuIds = source.certificatesType.map((item) => item.skuPropertyValueId)
        result.certificatesType = await SkuPropertyConvertUtils.calTrainingDiscipline(
          skuIds,
          source.industry[0].skuPropertyValueId
        )
      }
    }
    if (DataResolve.isWeightyArr(source.practitionerCategory)) {
      if (currentIndustryId) {
        skuIds = source.practitionerCategory.map((item) => item.skuPropertyValueId)
        result.practitionerCategory = await SkuPropertyConvertUtils.calTrainingDiscipline(
          skuIds,
          source.industry[0].skuPropertyValueId
        )
      }
    }
    if (DataResolve.isWeightyArr(source.trainingForm)) {
      result.trainingMode = source.trainingForm.map((item) => {
        const skuVo = new SkuVo()
        skuVo.skuPropertyValueId = item.skuPropertyValueId
        skuVo.skuPropertyName = TrainingMode.map.get(item.skuPropertyValueId as TrainingModeEnum)
        return skuVo
      })
    }
    console.log(result, 'resultresult')
    return result
  }

  static async filterRegionFirstStep(
    regionArr: string[],
    factory: QueryBasicdataDictionaryFactory,
    region?: SkuTradePropertyResponse[]
  ) {
    const regionList: BusinessRegionResponse[] = await factory.queryPhysicalRegion.querRegionDetil(regionArr, region)
    return SkuPropertyConvertUtils.filterRegionArr(regionList)
  }

  static filterRegionArr(regionList: BusinessRegionResponse[], parentId = '0') {
    const result = [] as RegionTreeVo[]
    regionList.forEach((item) => {
      if (item.parentId == parentId) {
        const region = new RegionTreeVo()
        region.enable = item.enable
        region.sort = item.sort
        region.name = item.name
        region.regionPath = item.regionPath
        region.parentId = item.parentId
        region.id = item.id
        region.child = SkuPropertyConvertUtils.filterRegionArr(regionList, item.id)
        result.push(region)
      }
    })
    if (DataResolve.isWeightyArr(result)) {
      return result
    }
    return undefined
  }
}

export default SkuPropertyConvertUtils
