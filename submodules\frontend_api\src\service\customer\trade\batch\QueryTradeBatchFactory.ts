import QueryMyOrderList from '@api/service/customer/trade/single/query/query-customer-user-order/query-customer-user-order-list/QueryMyOrderList'
import QueryOrderDetail from '@api/service/customer/trade/single/query/query-customer-user-order/query-order-detail/QueryOrderDetail'
import QueryCreateOrderConfig from '@api/service/customer/trade/single/query/query-create-order-config/QueryCreateOrderConfig'
/**
 * 交易查询工厂类
 */
class QueryTradeSingleFactory {
  get queryCreateOrderConfig() {
    return (schemeId: string) => {
      return new QueryCreateOrderConfig(schemeId)
    }
  }

  // 获取我的订单列表查询类实例 多例
  get queryMyOrderList() {
    return new QueryMyOrderList()
  }

  /**
   * 获取订单详情查询类
   */
  get queryOrderDetailMultiton() {
    return (orderNo: string) => {
      return new QueryOrderDetail(orderNo)
    }
  }
}
export default new QueryTradeSingleFactory()
