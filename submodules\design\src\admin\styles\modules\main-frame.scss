@import "../../../common/variables";

$text-color: #dce8ff; //菜单字体颜色
$footer-height: 70px; //底部高度
$side-width: 240px; //左侧栏宽度
$header-height: 100px; //头部高度
$tags-height: 40px; //标签导航高度
$color-header: #021c3f; // 头部背景颜色
$color-side: #021c3f; // 侧边栏背景颜色
$color-main: #f0f2f5; // 主要内容背景颜色
$color-footer: #f0f2f5; // 底部背景颜色

html {
  overflow: inherit;
}

body {
  background-color: #f0f2f5 !important;
  overflow: hidden;
}

.el-container {
  height: 100%;
  min-width: 1100px;
}

//侧边栏
.el-aside {
  background-color: $color-side;
  position: relative;
  overflow: inherit !important;
  height: 100%;
  display: flex;
  flex-direction: column;
  user-select: none;
  min-height: 630px;

  .aside-btn {
    color: $text-color;
    font-size: 18px;
    position: absolute;
    left: 250px;
    top: 41px;

    &:hover {
      text-decoration: none;
      color: #fff;
    }
  }

  &.side-collapsed {
    .op-btn {
      .el-button {
        width: 25px;
        overflow: hidden;
        padding: 7px 7px 6px;

        .el-icon-s-tools {
          margin-right: 5px;
        }
      }
    }
  }
}

//侧边栏菜单
.aside-nav {
  border-top: 1px solid #022e56;
  overflow: auto;
  flex: 1;

  &.el-menu {
    background-color: transparent;
    border-right: none;
  }

  .el-submenu__title,
  .el-menu-item {
    color: $text-color;

    i {
      color: currentColor;
    }

    &:hover {
      color: #fff;
      background-color: transparent;
    }

    &:focus {
      color: $text-color;
      background-color: transparent;
    }
  }

  .el-menu-item {
    &.is-active {
      color: lighten($base, 5%);
      background-color: #10243d;

      &::before {
        content: " ";
        background-color: lighten($base, 5%);
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 5px;
      }

      &:hover {
        background-color: #10243d;
      }
    }
  }

  .el-submenu {
    background-color: transparent;

    .el-menu {
      background-color: #031833;
      color: $text-color;
    }

    .el-menu-item {
      &:hover {
        color: lighten($base, 5%);
        background-color: transparent;
      }
    }
  }

  .iconfont,
  .hb-iconfont {
    vertical-align: middle;
    margin-right: 5px;
    width: 24px;
    text-align: center;
    font-size: 16px;
    display: inline-block;

    &.icon-trainprocess {
      font-size: 14px;
    }
  }

  //滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
    height: 1px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: #384a61;
  }

  &::-webkit-scrollbar-track {
    background: $color-side;
  }
}

//用户信息
.user-info {
  text-align: center;
  border-top: 1px solid #022e56;
  padding: 15px;
  box-sizing: border-box;
  user-select: none;

  .photo {
    width: 80px;
    height: 80px;
    border-radius: 80px;
    border: 2px solid $base;
  }

  .name {
    color: #fff;
    margin-top: 3px;
  }

  .op-btn {
    .el-button--primary {
      border: none;
      background-color: rgba(#fff, 0.2);
      color: #cdd9ec;
      padding: 6px 10px;

      &:hover {
        background-color: rgba(#fff, 0.26);
      }
    }
  }
}

//logo
.logo {
  height: $header-height;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0 10px;
  box-sizing: border-box;
  user-select: none;

  .logo-pic {
    width: 44px;
    height: 44px;
    vertical-align: middle;
  }

  .logo-txt {
    font-size: 28px;
    color: #fff;
    margin-left: 10px;
    line-height: 1.2;
    font-weight: bold;
  }

  .sub {
    width: 100%;
    max-height: 40px;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-align: center;
    color: #fff;
    margin-top: 5px;
    opacity: 0.7;
    line-height: 1.3;
  }
}

//头部
.el-header {
  background-color: $color-header;
  padding: 0 !important;
  user-select: none;

  .header-nav {
    margin-left: 40px;
  }
}

//头部导航
.header-nav {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: flex-start;
  color: $text-color;
  position: relative;

  &.f-flex-sub {
    flex: 1;
  }

  .iconfont {
    font-size: 18px;
    margin-right: 6px;
    vertical-align: -1px;
  }

  .nav-item {
    padding: 5px 20px;
    margin-right: 8px;
    cursor: pointer;
    position: relative;
    z-index: 2;

    &:hover {
      color: #fff;
    }

    &.current {
      color: #d35171;

      &:hover {
        color: #d35171;
      }
    }
  }

  .nav-item-1 {
    border-left: 1px solid darken($color-header, 10%);
    margin-right: 0;
    min-width: inherit;
  }

  .current-bg {
    min-width: 96px;
    height: 36px;
    background-color: #fff;
    border-radius: 100px;
    position: absolute;
    top: 32px;
    z-index: 1;
  }
}

//标签模块
.tags {
  background-color: #f8f9fd;
  border-bottom: 1px solid #e4e8f2;
  box-sizing: border-box;
  display: flex;
  user-select: none;

  .prev,
  .next,
  .more {
    width: 25px;
    text-align: center;
    line-height: 40px;
    background-color: #fff;
    border-right: 1px solid #e4e8f2;
    cursor: pointer;
    color: #666;

    &:hover {
      color: $base;
    }
  }

  .more {
    width: 20px;
    line-height: 39px;
    display: inline-block;
  }

  .next {
    border-left: 1px solid #e4e8f2;
  }

  .tags-bd {
    flex: 1;
    overflow: hidden;

    .el-tag {
      border: none;
      border-right: 1px solid #e4e8f2;
      background-color: transparent;
      height: 40px;
      line-height: 40px;
      padding: 0 15px;
      color: #999;
      font-size: 13px;
      cursor: pointer;

      &:hover {
        .el-icon {
          color: #999;
        }
      }

      &::before {
        content: "";
        width: 8px;
        height: 8px;
        display: inline-block;
        border-radius: 100%;
        background-color: #d6d6d6;
        margin-right: 8px;
        vertical-align: 1px;
      }

      .el-tag__close {
        color: #999;

        &:hover {
          color: #fff;
        }
      }

      &.current,
      &:hover {
        color: $base;

        &::before {
          background-color: $base;
        }
      }
    }
  }
}

.el-main {
  background-color: $color-main;
  padding: 0;
  height: 100%;

  .el-main {
    overflow: inherit;
  }
}

.el-footer {
  background-color: $color-footer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;

  .footer-bd {
    text-align: center;
    line-height: 1.3;

    a {
      color: currentColor;
    }
  }
}

//公司信息
.m-company-info {
  color: rgba($text-color, 0.5);
  text-align: center;
  padding: 10px;
  font-size: 12px;
  border-top: 1px solid rgba($text-color, 0.1);
  max-height: 100px;
  overflow: auto;

  .a-txt {
    color: rgba($text-color, 0.5);

    &:hover {
      text-decoration: underline;
    }
  }

  //滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
    height: 1px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: #384a61;
  }

  &::-webkit-scrollbar-track {
    background: $color-side;
  }
}

//滚动条样式
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background: transparent !important;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #bbb;
}

::-webkit-scrollbar-track {
  background: $color-main;
}
