import CreateTakePlaceVo from '@api/service/management/online-school-config/distribution-channels-config/mutation/vo/CreateTakePlaceVo'
import { ResponseStatus } from '@hbfe/common'
import MsOfflineinvoiceV1 from '@api/ms-gateway/ms-offlineinvoice-v1'

/**
 * @description 创建自取点
 */
class MutationCreateTakePlace {
  vo = new CreateTakePlaceVo()

  async doCreate(): Promise<ResponseStatus> {
    const { status } = await MsOfflineinvoiceV1.createChannel(this.vo.toJSON())
    return status
  }
}

export default MutationCreateTakePlace
