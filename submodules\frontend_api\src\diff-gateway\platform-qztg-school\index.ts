import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-qztg-school'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class DeliveryAddress {
  consignee: string
  phone: string
  region: string
  address: string
}

export class TakePoint {
  pickupLocation: string
  pickupTime: string
  remark?: string
}

/**
 * 发票信息
<AUTHOR>
@since 2021/3/23
 */
export class InvoiceInfoRequest {
  /**
   * 发票抬头
   */
  title?: string
  /**
   * 发票抬头类型
<pre>
1-个人
2-企业
</pre>
   */
  titleType?: number
  /**
   * 发票类型
<pre>
1-电子发票
2-纸质发票
</pre>
   */
  invoiceType?: number
  /**
   * 发票种类
<pre>
1-普通发票
2-增值税普通发票
3-增值税专用发票
</pre>
   */
  invoiceCategory?: number
  /**
   * 购买方纳税人识别号
   */
  taxpayerNo?: string
  /**
   * 地址
   */
  address?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 开户行
   */
  bankName?: string
  /**
   * 账户
   */
  account?: string
  /**
   * 发票票面备注
   */
  remark?: string
  /**
   * 开票方式
1 - 线上开票
2 - 线下开票
   */
  invoiceMethod?: number
  /**
   * 联系电子邮箱
   */
  email?: string
  /**
   * 联系电话
   */
  contactPhone?: string
  /**
   * 营业执照
   */
  businessLicensePath?: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath?: string
  /**
   * 配送方式
0/1/2,无/自取/快递
@see OfflineShippingMethods
   */
  shippingMethod?: number
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddress
  /**
   * 自取点信息
   */
  takePoint?: TakePoint
  /**
   * 发票信息校验策略
@see InvoiceVerifyStrategy
   */
  invoiceVerifyStrategy: number
}

/**
 * @Description: 泉州提高创建订单
@Author: chenDB
@Date: 2025/3/18
 */
export class QZTGCreateOrderRequest {
  /**
   * 买家编号
   */
  buyerId: string
  /**
   * 主方案
   */
  mainCommodity: Commodity
  /**
   * 被合并的方案
   */
  mergedCommodities: Array<Commodity>
  /**
   * 购买渠道类型
1-用户自主购买
2-集体缴费
3-管理员导入
   */
  purchaseChannelType: number
  /**
   * 终端类型
<p>
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  terminalCode: string
  /**
   * 渠道商编号
   */
  channelVendorId?: string
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceInfoRequest
  /**
   * 参训单位id
   */
  participatingUnitId?: string
  /**
   * 销售渠道Id（自营渠道为空，专题渠道时必填）
   */
  saleChannelId?: string
  /**
   * 销售渠道类型
0-自营渠道
2-专题渠道
   */
  saleChannel: number
  /**
   * 购买来源类型，1-门户，2-专题
@see com.fjhb.ms.order.v1.api.consts.PurchaseSourceType
   */
  purchaseSourceType?: number
}

/**
 * 商品描述
 */
export class Commodity {
  /**
   * 商品sku编号
   */
  skuId?: string
  /**
   * 商品数量
   */
  quantity?: number
  /**
   * 是否已报名
   */
  enrolled: boolean
  /**
   * 是否是被合并方案商品（默认false，如果是true则用来跳过商品验证）
   */
  mergedCommodity: boolean
  /**
   * 面授班时有值
   */
  issueInfo?: IssueInfo
}

export class IssueInfo {
  /**
   * 期别id
   */
  issueId?: string
  /**
   * 住宿类型
住宿类型 0-无需住宿 1-单人住宿 2-合住
   */
  accommodationType?: number
}

/**
 * 泉州提高修改商品合并关系请求
 */
export class QZTGUpdateMergeSkuRelationRequest {
  /**
   * 当前方案（商品sku）id
   */
  skuId: string
  /**
   * 删除商品id列表
   */
  removeSkuIds?: Array<string>
  /**
   * 新增商品id列表
   */
  addSkuIds?: Array<string>
}

/**
 * 创建订单结果
<AUTHOR> create 2021/1/29 17:27
 */
export class CreateOrderResultResponse {
  /**
   * 是否创建成功
   */
  success: boolean
  /**
   * 订单号，仅当{@link #success}为{@code true}时有值
   */
  orderNo: string
  /**
   * 子订单号
   */
  subOrderNoList: Array<string>
  /**
   * 订单创建时间，仅当{@link #success}为{@code true}时有值
   */
  createTime: string
  /**
   * 下单结果信息
   */
  message: string
  /**
   * - 200成功
- 4002 用户未登录
- 4003 主方案或者合并方案选择错误
- 5001 订单创建异常
   */
  code: number
  /**
   * 商品验证结果信息
   */
  resultList: Array<VerifyResultResponse>
}

/**
 * 校验结果返回
<AUTHOR> create 2021/2/3 10:53
 */
export class VerifyResultResponse {
  /**
   * 校验结果
   */
  message: string
  /**
   * 校验code
   */
  code: string
  /**
   * 订单内的商品skuId
   */
  skuId: string
  /**
   * 目前是(ms-learningscheme_reservingScheme)返回的子订单
   */
  subOrderNo: string
  /**
   * @see StudentSourceTypes
   */
  sourceType: string
  sourceId: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建订单
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOrder(
    request: QZTGCreateOrderRequest,
    query: DocumentNode = GraphqlImporter.createOrder,
    operation?: string
  ): Promise<Response<CreateOrderResultResponse>> {
    return commonRequestApi<CreateOrderResultResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改商品合并关系
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateMergeCommodityRelation(
    request: QZTGUpdateMergeSkuRelationRequest,
    query: DocumentNode = GraphqlImporter.updateMergeCommodityRelation,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
