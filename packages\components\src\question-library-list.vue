<template>
  <PopTree
    :placeholder="placeholder"
    :default-value="defaultNode"
    :close-after-select="true"
    v-model="selectNode"
    :load-data="load"
    size="small"
    :multiple="multiple"
    :expandOnClickNode="expandOnClickNode"
    ref="popTreeRef"
    :clearable="true"
    v-if="update"
    :props="defaultProps"
  >
  </PopTree>
</template>
<script lang="ts">
  import { Component, Emit, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import PopTree from '@hbfe-vue-components/pop-tree'
  import QuestionLibraryUI from '@api/service/common/models/exam/library/QuestionLibraryUI'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { Query, UiPage } from '@hbfe/common'
  import LibraryResponseVo from '@api/service/management/resource/question-library/query/vo/LibraryResponseVo'

  class NewQuestionLibraryListResponse extends LibraryResponseVo {
    children? = new Array<NewQuestionLibraryListResponse>()
    leaf = false
  }
  @Component({
    components: {
      PopTree
    }
  })
  export default class QuestionLibraryList extends Vue {
    @Ref('popTreeRef')
    popTreeRef: any // 组件内数据
    visible = false
    categoryName = ''
    currentCategoryId = ''
    dataList = new Array<QuestionLibraryUI>()
    defaultProps = {
      children: 'children',
      label: 'name',
      value: 'id',
      isLeaf: 'leaf'
    }
    questionLibraryFactory = ResourceModule.mutationQuestionLibraryFactory
    libraryFactory = ResourceModule.queryQuestionLibraryFactory
    page: UiPage
    query: Query = new Query()
    libraryResponseVo: LibraryResponseVo = new LibraryResponseVo()
    selectNode = ''
    update = true
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    @Prop() value: string | Array<string>
    @Prop({
      default: false
    })
    multiple: boolean

    @Prop({
      default: false
    })
    expandOnClickNode: boolean

    @Prop({
      // type: QuestionLibraryUI,
      required: false,
      default: function () {
        // return new QuestionLibraryUI()
        return new Array<NewQuestionLibraryListResponse>()
      }
    })
    defaultNode: object | Array<any>
    @Watch('defaultNode', {
      deep: true,
      immediate: true
    })
    defaultNodeChange(val: any) {
      if (val) {
        console.log(val, 'val')
      }
    }
    @Prop({
      default: '请选择题库'
    })
    placeholder: string

    @Prop({
      default: ''
    })
    unitId: string | Array<string> //机构id

    // @Watch('unitId', { deep: true })
    // unitIdChange() {
    //   this.update = false
    //   this.selectNode = new Array<string>()

    //   setTimeout(() => {
    //     this.update = true
    //   }, 10)
    // }
    @Prop({
      default: false
    })
    remoteData: boolean
    @Watch('remoteData', {
      deep: true,
      immediate: true
    })
    remoteDataChange(val: any) {
      if (val) {
        this.update = false
        this.selectNode = ''

        setTimeout(() => {
          this.update = true
        }, 10)
      }
    }
    @Watch('value', { deep: true, immediate: true })
    valueChange(val: any) {
      const item = this.popTreeRef?.$refs?.tree?.getCurrentNode()
      if (item && item.questionCount > 0 && item.id !== '-1' && item.isLeaf) {
        if (val) {
          this.$message.error('该题库下已有试题')
        }
        this.clearChoose()
      } else {
        this.selectNode = val
      }
      if (!this.value) {
        this.clearChoose()
      }
    }

    @Emit('input')
    @Watch('selectNode')
    selectNodeChange(val: string) {
      if (!val) {
        val = ''
      }
      return val
    }

    handleNodeClick(data: any) {
      this.categoryName = data.name
      this.currentCategoryId = data.id
      this.selectNodeChange(this.currentCategoryId)
      this.visible = false
    }

    clearChoose() {
      this.categoryName = ''
      this.currentCategoryId = ''
      this.selectNodeChange(this.currentCategoryId)
      this.visible = false
      this.popTreeRef?.clear()
    }
    createRootCategory() {
      const result = new NewQuestionLibraryListResponse()
      result.parentId = '-1'
      result.id = '-1'
      result.name = '题库分类'
      return result
    }
    async load(node: any, resolve: any) {
      const categoryUIList = new Array<NewQuestionLibraryListResponse>()

      if (node.data.id == undefined) {
        const rootCategory = this.createRootCategory()
        categoryUIList.push(rootCategory)
        resolve(categoryUIList)
        return
      }

      const parentId = node.data.id || '-1'
      this.page.pageSize = 200
      const res = await this.libraryFactory.queryQuestionLibraryMultiton.queryQuestionBankLibraryItem(
        this.page,
        parentId
      )
      res.data?.forEach((p) => {
        const category = new NewQuestionLibraryListResponse()
        category.parentId = p.parentId
        category.name = p.name
        category.id = p.id
        category.questionCount = p.questionCount
        categoryUIList.push(category)
      })
      console.log(categoryUIList, 'categoryUIList')
      resolve(categoryUIList)
    }
    // async requestRootPaperClassificationList(node: any, resolve: (arr: any) => {}) {
    //   const categoryUIList = new Array<NewQuestionLibraryListResponse>()
    //   const parentId = node.data.id || '-1'

    //   const res = await this.libraryFactory.queryQuestionLibraryMultiton.queryQuestionBankLibraryItem(
    //     this.page,
    //     parentId
    //   )
    //   res.data.forEach(p => {
    //     const category = new NewQuestionLibraryListResponse()
    //     category.parentId = p.parentId
    //     category.name = p.name
    //     category.id = p.id
    //     categoryUIList.push(category)
    //     categoryUIList
    //   })

    //   resolve(categoryUIList)
    // }
    // async requestSubPaperClassificationList(node: any, resolve: (arr: any) => {}) {
    //   const categoryUIList = new Array<NewQuestionLibraryListResponse>()
    //   const parentId = node.data.id || '-1'
    //   const res = await this.libraryFactory.queryQuestionLibraryMultiton.queryQuestionBankLibraryItem(
    //     this.page,
    //     parentId
    //   )
    //   res.data.forEach(p => {
    //     const category = new NewQuestionLibraryListResponse()
    //     category.parentId = p.parentId
    //     category.name = p.name
    //     category.id = p.id
    //     categoryUIList.push(category)
    //   })
    //   console.log(categoryUIList, 'categoryUIList')
    //   resolve(categoryUIList)
    // }
    async doQueryPage() {
      const res = await this.libraryFactory.queryQuestionLibraryMultiton.queryQuestionBankLibrary(
        this.page,
        this.libraryResponseVo
      )
      console.log(res, '节点数据')
    }
  }
</script>
