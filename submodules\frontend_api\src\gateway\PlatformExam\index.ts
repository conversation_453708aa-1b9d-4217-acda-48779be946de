import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/PlatformExam'

// 枚举
export enum WrongQuestionType {
  ALL = 'ALL',
  NEVER_CORRECT_IN_CORRECTION = 'NEVER_CORRECT_IN_CORRECTION',
  CORRECT_IN_CORRECTION = 'CORRECT_IN_CORRECTION'
}
export enum PaperConfigType {
  FIXED = 'FIXED',
  GROUP = 'GROUP',
  RANDOM = 'RANDOM'
}
export enum AnswerStatusEnum {
  ENTER = 'ENTER',
  MARKED_FINISH = 'MARKED_FINISH',
  DELETE = 'DELETE'
}
export enum ExamPaperApplyType {
  EXAM = 'EXAM',
  PRACTICE = 'PRACTICE'
}
export enum PracticeMode {
  OUTLINE = 'OUTLINE',
  RANDOM = 'RANDOM',
  QUESTION_TYPE = 'QUESTION_TYPE'
}
export enum PracticeType {
  REAL = 'REAL',
  SIMULATION = 'SIMULATION',
  PRACTICE = 'PRACTICE',
  RANDOM = 'RANDOM',
  DAILY = 'DAILY',
  FAVORITE = 'FAVORITE',
  ERROR_PRONE = 'ERROR_PRONE',
  CORRECTION = 'CORRECTION',
  EXAM = 'EXAM'
}

// 类

/**
 * Author:FangKunSen
Time:2021-03-01,10:20
 */
export class QuestionLibraryQueryRequestDTO {
  /**
   * 题库名
   */
  name?: string
  /**
   * 单位id
   */
  unitId?: string
  /**
   * 包含的题库ID集合
   */
  libraryIds?: Array<string>
  /**
   * 是否可用 -1/0/1 不查/可用/不可用
   */
  enable?: number
}

/**
 * @author: eleven
@date: 2020/4/13
 */
export class AnswerPracticeParamDTO {
  /**
   * 统计的试题类型
   */
  statisticPracticeTypeList: Array<PracticeType>
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 答题数量统计
@author: eleven
@date: 2020/3/23
 */
export class AnswerQuestionCountParamDTO {
  /**
   * 统计的试题类型
   */
  statisticPracticeTypeList: Array<PracticeType>
  /**
   * 统计的期数id
   */
  issueId?: string
  /**
   * 用户id，学员端忽略该参数
   */
  userId?: string
  /**
   * 答卷完成时间 -起
   */
  completeTimeStart?: string
  /**
   * 答卷完成时间 -止
   */
  completeTimeEnd?: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * *用户答题统计入参
@author: eleven
@date: 2020/3/4
 */
export class AnswerQuestionStatisticParamDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 试题类型
@see PracticeType#getValue()
   */
  practiceType?: PracticeType
  /**
   * 考纲id # 标签id
   */
  tagIdList?: Array<string>
}

/**
 * 做题记录分页查询参数
@author: eleven
@date: 2020/3/14
 */
export class AnswerRecordParamDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 查询的用户id
学员端忽略该参数
   */
  userId?: string
  /**
   * 查询的试题类型
   */
  practiceTypeList?: Array<PracticeType>
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 查询的答题记录卷状态
如果没传，查询所有
   */
  answerStatusEnum?: AnswerStatusEnum
}

/**
 * 每日一练查询参数
@author: eleven
@date: 2020/3/9
 */
export class DailyPracticeParamDTO {
  /**
   * 获取近N日的数据
目前前端应该传 7
   */
  recentDay: number
  /**
   * 统计每日一练参与人次的参数
入参为 listDailyPractice返回的日期
date 2020-03-20
   */
  date?: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 首页统计数据统计参数
@author: eleven
@date: 2020/4/18
 */
export class DashboardParam {
  /**
   * 作答提交时间起 >&#x3D;
   */
  submitAnswerTimeBegin?: string
  /**
   * 作答提交时间止 <&#x3D;
   */
  submitAnswerTimeEnd?: string
  /**
   * 单位
   */
  unitId?: string
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 年度
   */
  year?: number
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId?: string
}

/**
 * @author: eleven
@date: 2020/4/18
 */
export class DashboardRankParam {
  /**
   * 统计返回多少条的记录
   */
  size: number
  /**
   * 作答提交时间起 >&#x3D;
   */
  submitAnswerTimeBegin?: string
  /**
   * 作答提交时间止 <&#x3D;
   */
  submitAnswerTimeEnd?: string
  /**
   * 单位
   */
  unitId?: string
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 年度
   */
  year?: number
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId?: string
}

/**
 * 易错题统计
@author: eleven
@date: 2020/3/4
 */
export class ErrorProneStatisticParamDTO {
  /**
   * 标签id集合
   */
  tagIdList?: Array<string>
}

/**
 * 考试记录查询参数
@author: eleven
@date: 2020/3/4
 */
export class ExamRecordParamDTO {
  /**
   * 答题记录卷id
   */
  answerExamRecordIdList?: Array<string>
  /**
   * 用户id - 运营域参数
   */
  userId?: string
  /**
   * 查询的学习方案
   */
  schemeId?: string
  /**
   * 学习方式id
   */
  learningId?: string
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 统计返回多少的长度
目前都是前端都传15
-1：查询全部
   */
  statisticSize: number
  /**
   * 答卷完成时间 -起
   */
  completeTimeStart?: string
  /**
   * 答卷完成时间 -止
   */
  completeTimeEnd?: string
  /**
   * 模拟考卷应用类型
@see ExamPaperApplyType#getValue()
   */
  examPaperApplyType?: ExamPaperApplyType
}

/**
 * <AUTHOR>
@date 2020/7/9
@description
 */
export class LibraryQuestionStatisticRequest {
  /**
   * 题库id集合
   */
  libraryIdList?: Array<string>
  /**
   * 题目
   */
  topic?: string
  /**
   * 指定试题类型
1：判断 2：单选 3：多选 4：填空 5：问答 6：父子
   */
  questionTypes?: Array<number>
  /**
   * 是否可用 -1/0/1 不查/可用/不可用
   */
  enable: number
}

/**
 * 练习趋势统计
@author: eleven
@date: 2020/3/5
 */
export class PracticeTrendStatisticParamDTO {
  /**
   * 统计的练习类型
评估报告-试题练习
这里入参应该包括 REAL、SIMULATION、PRACTICE、DAILY、ERROR_PRONE
收藏题原型没备注，需要？待和产品确认
评估报告-错题 最近15天的错题重答数量
   */
  statisticPracticeTypeList: Array<PracticeType>
  /**
   * 统计返回多少的长度
目前都是前端都传15
   */
  statisticSize: number
  /**
   * 答卷完成时间 -起
   */
  completeTimeStart?: string
  /**
   * 答卷完成时间 -止
   */
  completeTimeEnd?: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * @author: eleven
@date: 2020/3/4
 */
export class QuestionStatisticParamDTO {
  /**
   * 试题类型
@see PracticeType#getValue()
支持是其中的 真题练习、模拟题、练习题三种
   */
  questionType?: PracticeType
  /**
   * 考纲id # 标签id
   */
  tagIdList?: Array<string>
  /**
   * 是否可用
-1:不查  0:可用  1:不可用
   */
  enable: number
}

/**
 * 单题试题答题统计
@author: eleven
@date: 2020/3/4
 */
export class SingleQuestionAnswerParamDTO {
  /**
   * 试题id
   */
  questionId?: Array<string>
}

/**
 * 用户模考正确率统计
@author: eleven
@date: 2020/3/5
 */
export class UserExamQuestionCorrectRateStatisticParamDTO {
  /**
   * 答卷完成时间 -起
   */
  completeTimeStart?: string
  /**
   * 答卷完成时间 -止
   */
  completeTimeEnd?: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 用户模考数据统计
@author: eleven
@date: 2020/3/5
 */
export class UserExamStatisticParamDTO {
  /**
   * 答卷完成时间 -起
   */
  completeTimeStart?: string
  /**
   * 答卷完成时间 -止
   */
  completeTimeEnd?: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 收藏题统计参数
@author: eleven
@date: 2020/3/5
 */
export class UserFavoriteQuestionStatisticParamDTO {
  /**
   * 学习方案id
   */
  schemeId?: string
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 统计的标签id集合
   */
  tagIdList?: Array<string>
  /**
   * 收藏记录创建时间 起 >
   */
  createTimeStart?: string
  /**
   * 收藏记录创建时间 止 <&#x3D;
   */
  createTimeEnd?: string
  /**
   * 有效时间截至 ，有效时间值得是在时间内创建且未删除的记录
   */
  validTimeEnd?: string
}

/**
 * 用户最后一次答题记录参数
@author: eleven
@date: 2020/3/5
 */
export class UserLastPracticeRecordParamDTO {
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 查询的答题记录卷状态
如果没传，查询所有
   */
  answerStatusEnum?: AnswerStatusEnum
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * 练习统计查询参数  - 评估报告-试题练习
@author: eleven
@date: 2020/3/5
 */
export class UserPracticeStatisticParamDTO {
  /**
   * 统计的期数id
   */
  issueId?: string
  /**
   * 统计的练习类型
评估报告-试题练习
   */
  statisticPracticeTypeList?: Array<PracticeType>
  /**
   * 答卷完成时间 -起
   */
  completeTimeStart?: string
  /**
   * 答卷完成时间 -止
   */
  completeTimeEnd?: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
}

/**
 * @author: eleven
@date: 2020/3/5
 */
export class UserWrongQuestionStatisticParamDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 错题类型
   */
  type: WrongQuestionType
  /**
   * 答对次数{@link #type} &#x3D; {@link WrongQuestionType#CORRECT_IN_CORRECTION} 有在错题重练中答对
大于
   */
  correctCountBegin?: number
  /**
   * 首次错题时间 起 >
   */
  firstTimeStart?: string
  /**
   * 首次错题时间 止 <&#x3D;
   */
  firstTimeEnd?: string
  /**
   * 考纲id # 标签id
   */
  tagIdList?: Array<string>
}

/**
 * 统计用户错题相关的合计数据
<AUTHOR> create 2020/3/14 11:14
 */
export class UserWrongQuestionSummaryParamDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 作答截至起始 >
   */
  answerTimeStart?: string
  /**
   * 作答截至时间 <&#x3D;
   */
  answerTimeEnd?: string
  /**
   * 错题类型
   */
  type?: WrongQuestionType
  /**
   * 答对次数{@link #type} &#x3D; {@link WrongQuestionType#CORRECT_IN_CORRECTION} 有在错题重练中答对
大于
   */
  correctCountBegin?: number
  /**
   * 考纲id # 标签id
   */
  tagIdList?: Array<string>
}

/**
 * 作答试题统计参数
<AUTHOR> create 2020/3/25 10:12
 */
export class AnswerQuestionStatisticPerDayParamDTO {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId?: string
  /**
   * 统计的标签id集合
   */
  tagIdList?: Array<string>
  /**
   * 作答提交时间起 <&#x3D;
   */
  submitAnswerTimeBegin: string
  /**
   * 作答提交时间止 <&#x3D;
   */
  submitAnswerTimeEnd: string
}

/**
 * @author: eleven
@date: 2020/5/7
 */
export class QuestionImportRequest {
  /**
   * 导入文件路径
   */
  filePath?: string
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 答题记录卷在对应场次的考试报告
@author: eleven
@date: 2020/3/4
 */
export class AnswerExamPaperReportDTO {
  /**
   * 答题记录卷id
   */
  answerExamRecordId: string
  /**
   * 考试成绩
   */
  examScore: number
  /**
   * 本场考试平均分
   */
  avgScore: number
  /**
   * 本次考试在同场次的排名
分数相同的，则并列排名同分数第一名
   */
  examRank: number
  /**
   * 累计参考人次
   */
  totalAnswerPaperCount: number
  /**
   * 统计考试成绩小于当前分数的人次，用户计算击败考生的比例
   */
  lessCurrentScoreAnswerPaperCount: number
  /**
   * 击败考生比例 &#x3D; lessCurrentScoreAnswerPaperCount /totalAnswerPaperCount
   */
  examRankRatio: number
  /**
   * 答卷耗时时间
   */
  totalUseTime: number
  /**
   * 已答题数
   */
  answeredCount: number
  /**
   * 答对题数
   */
  correctCount: number
  /**
   * 答错题数
   */
  wrongCount: number
  /**
   * 试卷总题数
   */
  totalQuestionCount: number
}

/**
 * 练习答卷
@author: eleven
@date: 2020/4/13
 */
export class AnswerPracticeDTO {
  /**
   * 标签id
   */
  tagId: string
  /**
   * 练习次数
   */
  count: number
}

/**
 * 用户答题统计-考纲维度
@author: eleven
@date: 2020/3/4
 */
export class AnswerQuestionStatisticDTO {
  /**
   * 标签id集合
   */
  tagIds: Array<string>
  /**
   * 已答试题数
   */
  answeredCount: number
  /**
   * 已答题次
   */
  answeredTimes: number
  /**
   * 答对题次
   */
  correctTimes: number
  /**
   * 正确率
   */
  correctRate: number
}

/**
 * 用户答题统计- 题类维度
@author: eleven
@date: 2020/3/4
 */
export class AnswerQuestionStatisticSummaryDTO {
  /**
   * 做答总题次
   */
  answeredCount: number
  /**
   * 答对题次
   */
  correctCount: number
  /**
   * 答错题次
   */
  wrongCount: number
  /**
   * 正确率
   */
  correctRate: number
}

/**
 * 做题记录对象
@author: eleven
@date: 2020/3/14
 */
export class AnswerRecordDTO {
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方式id
   */
  learningId: string
  /**
   * 答题记录卷id
   */
  answerExamRecordId: string
  /**
   * 答卷id
   */
  answerPaperId: string
  /**
   * 引用的试卷id
   */
  examPaperId: string
  /**
   * 场次id
   */
  examRoundId: string
  /**
   * 练习模式
   */
  practiceMode: PracticeMode
  /**
   * 抽题所在的标签
当practiceMode &#x3D; PracticeMode.OUTLINE 有效
   */
  tagId: string
  /**
   * 抽题题型
当practiceMode &#x3D;  PracticeMode.QUESTION_TYPE 有效
0/1/2/3/4/5/6/7 未知/判断/单选/多选/填空/简答/父子/混合
@see com.fjhb.platform.component.exam.commons.api.consts.question.QuestionType
   */
  questionType: number
  /**
   * 抽题数
   */
  questionCount: number
  /**
   * 答题数
   */
  answerCount: number
  /**
   * 正确数
   */
  correctCount: number
  /**
   * 答卷完成时间
   */
  answerCompleteTime: string
}

/**
 * 每日一练数据对象
@author: eleven
@date: 2020/3/4
 */
export class DailyPracticeDTO {
  /**
   * 每日一练日期
格式：2020-03-04
   */
  date: string
  /**
   * 星期几
MONDAY
TUESDAY
WEDNESDAY
THURSDAY
FRIDAY
SATURDAY
SUNDAY
@see java.time.DayOfWeek
   */
  week: string
  /**
   * 是否已经有答题记录
   */
  hasAnswerRecord: boolean
  /**
   * 当前登录用练习次数
   */
  count: number
}

/**
 * 易错题统计
@author: eleven
@date: 2020/3/4
 */
export class ErrorProneStatisticDTO {
  /**
   * 分组key,tag集合  - 按标签分组统计，该字段才生效
   */
  tags: Array<string>
  /**
   * 试题题型 - 按题型分组统计，该字段才生效
0/1/2/3/4/5/6/7 未知/判断/单选/多选/填空/简答/父子/混合
@see com.fjhb.platform.component.exam.commons.api.consts.question.QuestionType
   */
  questionType: string
  /**
   * 剩余未答的易错题总数
   */
  remainCount: number
}

/**
 * 考试学习方式的场次试卷信息
@author: eleven
@date: 2020/3/4
 */
export class ExamLearningDTO {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 考试名称
   */
  name: string
  /**
   * 试卷ID
   */
  examPaperId: string
  /**
   * 考试时长，单位分钟
   */
  examTimeLength: number
  /**
   * 考试次数 0表示不限制次数
   */
  examCount: number
  /**
   * 试卷组卷类型
   */
  paperConfigType: PaperConfigType
}

/**
 * 试卷分类
@author: eleven
@date: 2020/4/3
 */
export class ExamPaperClassifyDTO {
  /**
   * 分类id
   */
  id: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 父试卷分类id
   */
  parentId: string
  /**
   * 描述
   */
  description: string
}

/**
 * 用户历史考试记录
@author: eleven
@date: 2020/3/4
 */
export class ExamRecordDTO {
  /**
   * 答题记录卷id
   */
  answerExamRecordId: string
  /**
   * 答卷id
   */
  answerPaperId: string
  /**
   * 引用的试卷id
   */
  examPaperId: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方式id
   */
  learningId: string
  /**
   * 场次id
   */
  examRoundId: string
  /**
   * 考试成绩
   */
  examScore: number
  /**
   * 考试考试时间（用户开始考试的时间）
   */
  answerPaperStartTime: string
  /**
   * 答卷完成时间
   */
  answerPaperCompleteTime: string
  /**
   * 答卷耗时时间
   */
  totalUseTime: number
}

/**
 * 平台级考试场次信息
Author:FangKunSen
Time:2020-04-21,09:38
 */
export class ExamRoundBaseDTO {
  /**
   * 场次id
   */
  id: string
  /**
   * 场次名称
   */
  name: string
  /**
   * 开考时间
   */
  beginTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 考试时长
   */
  examTimeLength: number
  /**
   * 考试次数 0表示不限制次数
   */
  examCount: number
}

/**
 * <AUTHOR>
@date 2020/7/9
@description
 */
export class LibraryQuestionCountResponse {
  /**
   * 题库id
   */
  libraryId: string
  /**
   * 试题数量
   */
  questionCount: number
}

/**
 * 平台考试统计数据
@author: eleven
@date: 2020/3/14
 */
export class PlatformExamStatisticDTO {
  /**
   * 累计考试次数
   */
  totalExamCount: number
  /**
   * 累计练习刷题次数
   */
  totalPracticeCount: number
}

/**
 * 练习正确率趋势统计
@author: eleven
@date: 2020/3/5
 */
export class PracticeCorrectRateTrendDTO {
  /**
   * 答题记录卷id
   */
  answerExamRecordId: string
  /**
   * 答题时间
   */
  answerPaperTime: string
  /**
   * 总题数
   */
  totalCount: number
  /**
   * 已做答题数
   */
  answeredCount: number
  /**
   * 答对题数
   */
  correctCount: number
  /**
   * 答错题数
   */
  wrongCount: number
  /**
   * 正确率
   */
  correctRate: number
}

/**
 * 练习题量趋势统计
@author: eleven
@date: 2020/3/5
 */
export class PracticeCountTrendDTO {
  /**
   * 答题时间
   */
  answerPaperDate: string
  /**
   * 答题量
   */
  answerCount: number
}

/**
 * 练习频率统计
@author: eleven
@date: 2020/3/5
 */
export class PracticeFrequencyStatisticDTO {
  /**
   * 累计练习天数
   */
  practiceDayCount: number
  /**
   * 近N日增加的练习天数
   */
  recentAddPracticeDayCount: number
}

/**
 * 首页数据统计 - 试题答题次数统计
@author: eleven
@date: 2020/4/18
 */
export class QuestionAnswerCountDTO {
  /**
   * 答题日期
   */
  date: string
  /**
   * 真题答题数
   */
  real: number
  /**
   * 练习答题数
   */
  practice: number
  /**
   * 模拟题答题数
   */
  simulation: number
}

/**
 * 试题类型统计
@author: eleven
@date: 2020/4/20
 */
export class QuestionCategoryCountDTO {
  /**
   * 真题数量
   */
  real: number
  /**
   * 练习题数量
   */
  practice: number
  /**
   * 模拟题数量
   */
  simulation: number
  /**
   * 合计数量
   */
  total: number
}

/**
 * 试题正确率统计
@author: eleven
@date: 2020/4/18
 */
export class QuestionCorrectRateDTO {
  /**
   * 真题答题情况
   */
  real: QuestionStatisticBaseDTO
  /**
   * 练习题答题情况
   */
  practice: QuestionStatisticBaseDTO
  /**
   * 模拟题答题情况
   */
  simulation: QuestionStatisticBaseDTO
  /**
   * 合计答题情况
   */
  total: QuestionStatisticBaseDTO
}

export class QuestionStatisticBaseDTO {
  /**
   * 已做答题数
   */
  answeredCount: number
  /**
   * 答对题数
   */
  correctCount: number
  /**
   * 对题率
   */
  correctRate: number
}

/**
 * @author: eleven
@date: 2020/3/4
 */
export class QuestionStatisticDTO {
  /**
   * 标签id - 按标签分组统计，该字段才生效
   */
  tags: Array<string>
  /**
   * 试题类型 - 按题型分组统计，该字段才生效
   */
  questionType: number
  /**
   * 题类  - 按题类分组统计，该字段才生效
支持的试题类型：真题练习、模拟题、练习题
   */
  questionCategory: string
  /**
   * 试题数量
   */
  count: number
}

/**
 * 用户单题试题的答题情况
@author: eleven
@date: 2020/3/4
 */
export class SingleQuestionAnswerStatisticDTO {
  /**
   * 试题id
   */
  questionId: string
  /**
   * 已答题次数
   */
  answeredTimes: number
  /**
   * 答对次数
   */
  correctTimes: number
  /**
   * 答错次数
   */
  wrongTimes: number
  /**
   * 未答次数,未作答
   */
  unknownTimes: number
  /**
   * 正确率
   */
  correctRate: number
  /**
   * 全站已答题次数
   */
  allAnsweredTimes: number
  /**
   * 全站答对次数
   */
  allCorrectTimes: number
  /**
   * 全站答错次数
   */
  allWrongTimes: number
  /**
   * 全站未答次数,未作答
   */
  allUnknownTimes: number
  /**
   * 全站正确率
   */
  allCorrectRate: number
}

/**
 * 试题类型答题统计
@author: eleven
@date: 2020/3/23
 */
export class UserAnswerQuestionCountDTO {
  /**
   * 真题答题数
   */
  real: number
  /**
   * 练习答题数
   */
  practice: number
  /**
   * 模拟题答题数
   */
  simulation: number
}

/**
 * 用户模考答题正确率统计
@author: eleven
@date: 2020/3/5
 */
export class UserExamQuestionCorrectRateStatisticDTO {
  /**
   * 单选
   */
  single: UserQuestionCorrectStatistic
  /**
   * 多选
   */
  multiple: UserQuestionCorrectStatistic
  /**
   * 判断
   */
  judgement: UserQuestionCorrectStatistic
  /**
   * 案例题（综合题）
   */
  comprehensive: UserQuestionCorrectStatistic
}

/**
 * 用户模考据统计
@author: eleven
@date: 2020/3/5
 */
export class UserExamStatisticDTO {
  /**
   * 考试次数
   */
  examCount: number
  /**
   * 平均分
   */
  aveScore: number
  /**
   * 最高分
   */
  maxScore: number
  /**
   * 平均分网校排名
   */
  examRank: number
  /**
   * 做题总量
   */
  totalAnswerCount: number
  /**
   * 答对题量
   */
  correctAnswerCount: number
  /**
   * 正确率
   */
  correctRate: number
  /**
   * 平均用时
   */
  avgUseTime: number
  /**
   * 最短用时
   */
  minUseTime: number
}

/**
 * @author: eleven
@date: 2020/3/5
 */
export class UserFavoriteQuestionStatisticDTO {
  /**
   * 标签id - 按标签分组统计，该字段才生效
   */
  tags: Array<string>
  /**
   * 试题题型 - 按题型分组统计，该字段才生效
0/1/2/3/4/5/6/7 未知/判断/单选/多选/填空/简答/父子/混合
@see com.fjhb.platform.component.exam.commons.api.consts.question.QuestionType
   */
  questionType: string
  /**
   * 题类  - 按题类分组统计，该字段才生效
支持的试题类型：真题练习、模拟题、练习题
   */
  questionCategory: string
  /**
   * 试题数量
   */
  questionCount: number
}

/**
 * 用户最后一次的练习答题记录数据
@author: eleven
@date: 2020/3/5
 */
export class UserLastPracticeRecordDTO {
  /**
   * 练习类型
   */
  practiceType: PracticeType
  /**
   * 抽题题型
当practiceMode &#x3D;  PracticeMode.QUESTION_TYPE 有效
@see com.fjhb.platform.component.exam.commons.api.consts.question.QuestionType
   */
  questionType: number
  /**
   * 练习模式
   */
  practiceMode: PracticeMode
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方式id
   */
  learningId: string
  /**
   * 答题记录卷id
   */
  answerExamRecordId: string
  /**
   * 答卷id
   */
  answerPaperId: string
  /**
   * 引用的试卷
   */
  examPaperId: string
  /**
   * 标签id
   */
  tagId: string
  /**
   * 已答题数（答题记录卷的已作答题数）
   */
  hasAnswerCount: number
  /**
   * 总题数（答题记录卷的总题数）
   */
  totalCount: number
  /**
   * 答题进度 - 完成度
已答题数/总题数
   */
  answerSchedule: number
  /**
   * 作答完成时间 -可能为空
当当前答题记录卷还在作答中的话，则该字段为空，对应取更新时间updateTime
   */
  answerPaperCompleteTime: string
  /**
   * 数据更新时间
   */
  updateTime: string
}

/**
 * 练习相关统计 -
@author: eleven
@date: 2020/3/5
 */
export class UserPracticeStatisticDTO {
  /**
   * 累计做题总量
   */
  totalAnswerCount: number
  /**
   * 正确题次
   */
  totalCorrectCount: number
  /**
   * 正确率
   */
  correctRate: number
}

/**
 * 用户答题正确率统计
@author: eleven
@date: 2020/4/18
 */
export class UserQuestionAnswerCorrectRateDTO {
  /**
   * 用户名
   */
  userName: string
  /**
   * 用戶id
   */
  userId: string
  /**
   * 累计答题次数
   */
  answerCount: number
  /**
   * 答对题数
   */
  correctCount: number
  /**
   * 对题率
   */
  correctRate: number
}

/**
 * 用户答题次数统计
@author: eleven
@date: 2020/4/18
 */
export class UserQuestionAnswerCountDTO {
  /**
   * 用户名称
   */
  userName: string
  /**
   * 用戶id
   */
  userId: string
  /**
   * 累计答题次数
   */
  answerCount: number
}

/**
 * 统计用户对题率 - 因为通过题数获取对题率，所以连带答题情况一起返回
@author: eleven
@date: 2020/3/8
 */
export class UserQuestionCorrectStatistic {
  /**
   * 已做答题数
   */
  answeredCount: number
  /**
   * 答对题数
   */
  correctCount: number
  /**
   * 答错题数
   */
  wrongCount: number
  /**
   * 对题率
   */
  correctRate: number
}

/**
 * 用户错题统计数据
@author: eleven
@date: 2020/3/5
 */
export class UserWrongQuestionStatisticDTO {
  /**
   * 标签id集合 - 按标签分组统计，该字段才生效
   */
  tagIds: Array<string>
  /**
   * 试题题型 - 按题型分组统计，该字段才生效
   */
  questionType: string
  /**
   * 题类  - 按题类分组统计，该字段才生效
支持的试题类型：真题练习、模拟题、练习题
   */
  questionCategory: string
  /**
   * 试题数量
   */
  questionCount: number
}

/**
 * 我的错题汇总统计
做题记录 - 错题重答
错题相关统计的是题数，而非题次
@author: eleven
@date: 2020/3/5
 */
export class UserWrongQuestionStatisticSummaryDTO {
  /**
   * 错题总数
   */
  wrongQuestionCount: number
  /**
   * 作答试题总次数
   */
  questionAnsweredTimes: number
  /**
   * 错题总次数
   */
  wrongQuestionTimes: number
  /**
   * 错题率 错题总次数/累计做题总次数
   */
  wrongRate: number
}

/**
 * 用户答题统计-每天
@author: linj
@date: 2020/3/25
 */
export class AnswerQuestionStatisticPerDayDTO {
  /**
   * 日期 yyyy-MM-dd
   */
  day: string
  /**
   * 已答试题数
   */
  answeredCount: number
  /**
   * 已答题次
   */
  answeredTimes: number
  /**
   * 答对题次
   */
  correctTimes: number
  /**
   * 答错题次
   */
  errorTimes: number
  /**
   * 正确率
   */
  correctRate: number
  /**
   * 错误率
   */
  errorRate: number
}

export class ExamObjectDto {
  objectId: string
  type: string
}

export class ExamLibraryDto {
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  organizationId: string
  name: string
  id: string
  share: boolean
  examObjects: Array<ExamObjectDto>
  description: string
  parentId: string
  enabled: boolean
  createTime: string
  createUserId: string
  lastChangeTime: string
  sourceType: number
  sourceId: string
  rootId: string
  token: string
}

export class ExamLibraryDtoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ExamLibraryDto>
}

export class AnswerRecordDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<AnswerRecordDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 统计每日一练参与人次 - 来源中间表(已实现)
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async countDailyPracticePartInUser(
    paramDTO: DailyPracticeParamDTO,
    query: DocumentNode = GraphqlImporter.countDailyPracticePartInUser,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计易错题总数
   * @return
   * @param tagIdList
   * @param query 查询 graphql 语法文档
   * @param tagIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async countTotalErrorProne(
    tagIdList: Array<string>,
    query: DocumentNode = GraphqlImporter.countTotalErrorProne,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(SERVER_URL, {
      query: query,
      variables: { tagIdList },
      operation: operation
    })
  }

  /**   * 获取指定考试学习方式下面的考试参考人次
   * 练习模式的也记录参考人次
   * @param learningId
   * @return
   * @param query 查询 graphql 语法文档
   * @param learningId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async countTotalUserEnterExam(
    learningId: string,
    query: DocumentNode = GraphqlImporter.countTotalUserEnterExam,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(SERVER_URL, {
      query: query,
      variables: { learningId },
      operation: operation
    })
  }

  /**   * 统计用户已答过的易错题数量 - 易错题-累计消灭提数 -  - 考试服务
   * @param userId
   * @return
   * @param query 查询 graphql 语法文档
   * @param userId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async countUserHasAnswerErrorProneQuestion(
    userId: string,
    query: DocumentNode = GraphqlImporter.countUserHasAnswerErrorProneQuestion,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(SERVER_URL, {
      query: query,
      variables: { userId },
      operation: operation
    })
  }

  /**   * 导出试题答题记录明细
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportUserAnswerRecord(
    query: DocumentNode = GraphqlImporter.exportUserAnswerRecord,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 获取内置题库1
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDefaultQuestionLibraryId(
    query: DocumentNode = GraphqlImporter.getDefaultQuestionLibraryId,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 获取场次基本信息
   * @param schemeId
   * @param learningId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getExamRoundBaseInfo(
    params: { schemeId: string; learningId: string },
    query: DocumentNode = GraphqlImporter.getExamRoundBaseInfo,
    operation?: string
  ): Promise<Response<ExamRoundBaseDTO>> {
    return commonRequestApi<ExamRoundBaseDTO>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 统计练习次数最多的练习
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getMaxPracticeCountAnswerPaper(
    param: AnswerPracticeParamDTO,
    query: DocumentNode = GraphqlImporter.getMaxPracticeCountAnswerPaper,
    operation?: string
  ): Promise<Response<AnswerPracticeDTO>> {
    return commonRequestApi<AnswerPracticeDTO>(SERVER_URL, {
      query: query,
      variables: { param },
      operation: operation
    })
  }

  /**   * 用户最后一次答题记录 - 来源中间表
   * @return  UserLastPracticeRecordDTO || null:不存在答题记录
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserLastPracticeRecord(
    paramDTO: UserLastPracticeRecordParamDTO,
    query: DocumentNode = GraphqlImporter.getUserLastPracticeRecord,
    operation?: string
  ): Promise<Response<UserLastPracticeRecordDTO>> {
    return commonRequestApi<UserLastPracticeRecordDTO>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 获取当前登录用户的每日一练 - 来源中间表(已实现)
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listDailyPractice(
    paramDTO: DailyPracticeParamDTO,
    query: DocumentNode = GraphqlImporter.listDailyPractice,
    operation?: string
  ): Promise<Response<Array<DailyPracticeDTO>>> {
    return commonRequestApi<Array<DailyPracticeDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 获取指定学习方案下面的考试学习方式 -目前只有一个考试学习方式
   * @param schemeId 学习方案id
   * @return
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExamLearning(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.listExamLearning,
    operation?: string
  ): Promise<Response<ExamLearningDTO>> {
    return commonRequestApi<ExamLearningDTO>(SERVER_URL, {
      query: query,
      variables: { schemeId },
      operation: operation
    })
  }

  /**   * 获取题库数据
   * @param idList
   * @return
   * @param query 查询 graphql 语法文档
   * @param idList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExamLibrary(
    idList: Array<string>,
    query: DocumentNode = GraphqlImporter.listExamLibrary,
    operation?: string
  ): Promise<Response<Array<ExamLibraryDto>>> {
    return commonRequestApi<Array<ExamLibraryDto>>(SERVER_URL, {
      query: query,
      variables: { idList },
      operation: operation
    })
  }

  /**   * 获取试卷分类详情
   * @param idList
   * @return
   * @param query 查询 graphql 语法文档
   * @param idList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExamPaperClassify(
    idList: Array<string>,
    query: DocumentNode = GraphqlImporter.listExamPaperClassify,
    operation?: string
  ): Promise<Response<Array<ExamPaperClassifyDTO>>> {
    return commonRequestApi<Array<ExamPaperClassifyDTO>>(SERVER_URL, {
      query: query,
      variables: { idList },
      operation: operation
    })
  }

  /**   * 查询历史考试记录
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExamRecord(
    paramDTO: ExamRecordParamDTO,
    query: DocumentNode = GraphqlImporter.listExamRecord,
    operation?: string
  ): Promise<Response<Array<ExamRecordDTO>>> {
    return commonRequestApi<Array<ExamRecordDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 获取制定条件的场次信息
   * @return
   * @param query 查询 graphql 语法文档
   * @param examRoundList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExamRound(
    examRoundList: Array<string>,
    query: DocumentNode = GraphqlImporter.listExamRound,
    operation?: string
  ): Promise<Response<Array<ExamRoundBaseDTO>>> {
    return commonRequestApi<Array<ExamRoundBaseDTO>>(SERVER_URL, {
      query: query,
      variables: { examRoundList },
      operation: operation
    })
  }

  /**   * 获取用户答题次数排行榜-Dashboard接口
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserQuestionAnswerCountRank(
    param: DashboardRankParam,
    query: DocumentNode = GraphqlImporter.listUserQuestionAnswerCountRank,
    operation?: string
  ): Promise<Response<Array<UserQuestionAnswerCountDTO>>> {
    return commonRequestApi<Array<UserQuestionAnswerCountDTO>>(SERVER_URL, {
      query: query,
      variables: { param },
      operation: operation
    })
  }

  /**   * 获取用户答题正确率排行榜-Dashboard接口
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserQuestionCorrectRateRank(
    param: DashboardRankParam,
    query: DocumentNode = GraphqlImporter.listUserQuestionCorrectRateRank,
    operation?: string
  ): Promise<Response<Array<UserQuestionAnswerCorrectRateDTO>>> {
    return commonRequestApi<Array<UserQuestionAnswerCorrectRateDTO>>(SERVER_URL, {
      query: query,
      variables: { param },
      operation: operation
    })
  }

  /**   * 分页获取题库
   * @param page
   * @param requestDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageExamLibrary(
    params: { page?: Page; requestDTO?: QuestionLibraryQueryRequestDTO },
    query: DocumentNode = GraphqlImporter.pageExamLibrary,
    operation?: string
  ): Promise<Response<ExamLibraryDtoPage>> {
    return commonRequestApi<ExamLibraryDtoPage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取用户做题记录
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserAnswerRecord(
    params: { page?: Page; paramDTO?: AnswerRecordParamDTO },
    query: DocumentNode = GraphqlImporter.pageUserAnswerRecord,
    operation?: string
  ): Promise<Response<AnswerRecordDTOPage>> {
    return commonRequestApi<AnswerRecordDTOPage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 统计指定答题记录卷在同场次中的考试报告
   * @param answerExamRecordId
   * @return 如果考试报告尚未生成，则返回null
   * @param query 查询 graphql 语法文档
   * @param answerExamRecordId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticAnswerExamPaperReport(
    answerExamRecordId: string,
    query: DocumentNode = GraphqlImporter.statisticAnswerExamPaperReport,
    operation?: string
  ): Promise<Response<AnswerExamPaperReportDTO>> {
    return commonRequestApi<AnswerExamPaperReportDTO>(SERVER_URL, {
      query: query,
      variables: { answerExamRecordId },
      operation: operation
    })
  }

  /**   * 统计试题数量
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticLibraryQuestionCount(
    request: LibraryQuestionStatisticRequest,
    query: DocumentNode = GraphqlImporter.statisticLibraryQuestionCount,
    operation?: string
  ): Promise<Response<Array<LibraryQuestionCountResponse>>> {
    return commonRequestApi<Array<LibraryQuestionCountResponse>>(SERVER_URL, {
      query: query,
      variables: { request },
      operation: operation
    })
  }

  /**   * 统计平台的考试相关数据总览
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticPlatformExamInfo(
    query: DocumentNode = GraphqlImporter.statisticPlatformExamInfo,
    operation?: string
  ): Promise<Response<PlatformExamStatisticDTO>> {
    return commonRequestApi<PlatformExamStatisticDTO>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 统计试题答题次数 - 按日期分组统计 -Dashboard接口
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticQuestionAnswerCountGroupDate(
    param: DashboardParam,
    query: DocumentNode = GraphqlImporter.statisticQuestionAnswerCountGroupDate,
    operation?: string
  ): Promise<Response<Array<QuestionAnswerCountDTO>>> {
    return commonRequestApi<Array<QuestionAnswerCountDTO>>(SERVER_URL, {
      query: query,
      variables: { param },
      operation: operation
    })
  }

  /**   * 统计试题正确率 - 按题类分组统计-Dashboard接口
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticQuestionCorrectRate(
    param: DashboardParam,
    query: DocumentNode = GraphqlImporter.statisticQuestionCorrectRate,
    operation?: string
  ): Promise<Response<QuestionCorrectRateDTO>> {
    return commonRequestApi<QuestionCorrectRateDTO>(SERVER_URL, {
      query: query,
      variables: { param },
      operation: operation
    })
  }

  /**   * 统计试题数量
   * 支持的试题类型：真题练习、模拟题、练习题
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticQuestionGroupByQuestionCategory(
    paramDTO: QuestionStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticQuestionGroupByQuestionCategory,
    operation?: string
  ): Promise<Response<QuestionCategoryCountDTO>> {
    return commonRequestApi<QuestionCategoryCountDTO>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计试题数量
   * 支持的试题类型：真题练习、模拟题、练习题
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticQuestionGroupByQuestionType(
    paramDTO: QuestionStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticQuestionGroupByQuestionType,
    operation?: string
  ): Promise<Response<Array<QuestionStatisticDTO>>> {
    return commonRequestApi<Array<QuestionStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计试题数量
   * 支持的试题类型：真题练习、模拟题、练习题
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticQuestionGroupByTagIds(
    paramDTO: QuestionStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticQuestionGroupByTagIds,
    operation?: string
  ): Promise<Response<Array<QuestionStatisticDTO>>> {
    return commonRequestApi<Array<QuestionStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计指定试题的答题情况  -考试结果解析页  -- 考试服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticSingleQuestionAnswerInfo(
    paramDTO: SingleQuestionAnswerParamDTO,
    query: DocumentNode = GraphqlImporter.statisticSingleQuestionAnswerInfo,
    operation?: string
  ): Promise<Response<Array<SingleQuestionAnswerStatisticDTO>>> {
    return commonRequestApi<Array<SingleQuestionAnswerStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户试题类型的答题重量
   * @param paramDTO
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserAnswerPerDay(
    paramDTO: AnswerQuestionStatisticPerDayParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserAnswerPerDay,
    operation?: string
  ): Promise<Response<Array<AnswerQuestionStatisticPerDayDTO>>> {
    return commonRequestApi<Array<AnswerQuestionStatisticPerDayDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户指定试题类型指定考纲下的答题情况  -考纲维度- 考试服务
   * 支持的试题类型：真题练习、模拟题、练习题
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserAnswerQuestionByTag(
    paramDTO: AnswerQuestionStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserAnswerQuestionByTag,
    operation?: string
  ): Promise<Response<Array<AnswerQuestionStatisticDTO>>> {
    return commonRequestApi<Array<AnswerQuestionStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户试题类型的答题重量
   * @param paramDTO
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserAnswerQuestionCountByQuestionCategory(
    paramDTO: AnswerQuestionCountParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserAnswerQuestionCountByQuestionCategory,
    operation?: string
  ): Promise<Response<UserAnswerQuestionCountDTO>> {
    return commonRequestApi<UserAnswerQuestionCountDTO>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户指定试题类型下的答题汇总情况  -题类下的汇总统计- 来源中间表（已实现）
   * 支持的试题类型：真题练习、模拟题、练习题
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserAnswerQuestionSummaryInfo(
    paramDTO: AnswerQuestionStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserAnswerQuestionSummaryInfo,
    operation?: string
  ): Promise<Response<AnswerQuestionStatisticSummaryDTO>> {
    return commonRequestApi<AnswerQuestionStatisticSummaryDTO>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户的模考数据  - 来源中间表(部分实现，剩余排名为实现)
   * 1、对于评估报告首行汇总数据考虑单独开一个口？
   * 2、首页的模拟试卷统计也使用该接口？
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserExamInfo(
    paramDTO: UserExamStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserExamInfo,
    operation?: string
  ): Promise<Response<UserExamStatisticDTO>> {
    return commonRequestApi<UserExamStatisticDTO>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 用户模拟考试题型正确率统计- 来源中间表(已实现)
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserExamQuestionCorrectRateByQuestionType(
    paramDTO: UserExamQuestionCorrectRateStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserExamQuestionCorrectRateByQuestionType,
    operation?: string
  ): Promise<Response<UserExamQuestionCorrectRateStatisticDTO>> {
    return commonRequestApi<UserExamQuestionCorrectRateStatisticDTO>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户收藏题数量 - 总数 - 考试服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserFavoriteQuestionCount(
    paramDTO: UserFavoriteQuestionStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserFavoriteQuestionCount,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户收藏题数量 - 题类分组- 考试服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserFavoriteQuestionGroupByQuestionCategory(
    paramDTO: UserFavoriteQuestionStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserFavoriteQuestionGroupByQuestionCategory,
    operation?: string
  ): Promise<Response<Array<UserFavoriteQuestionStatisticDTO>>> {
    return commonRequestApi<Array<UserFavoriteQuestionStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户收藏题数量 - 题型分组- 考试服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserFavoriteQuestionGroupByQuestionType(
    paramDTO: UserFavoriteQuestionStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserFavoriteQuestionGroupByQuestionType,
    operation?: string
  ): Promise<Response<Array<UserFavoriteQuestionStatisticDTO>>> {
    return commonRequestApi<Array<UserFavoriteQuestionStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户收藏题数量 - 标签分组 - 考试服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserFavoriteQuestionGroupByTag(
    paramDTO: UserFavoriteQuestionStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserFavoriteQuestionGroupByTag,
    operation?: string
  ): Promise<Response<Array<UserFavoriteQuestionStatisticDTO>>> {
    return commonRequestApi<Array<UserFavoriteQuestionStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 用户练习统计 评估报告-试题练习、首页统计  - 中间表(已实现)
   * 计算现在的数据并且比对7天前的数据
   * 入参 statisticPracticeTypeList 应该包括 REAL、SIMULATION、PRACTICE、DAILY、ERROR_PRONE
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserPractice(
    paramDTO: UserPracticeStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserPractice,
    operation?: string
  ): Promise<Response<UserPracticeStatisticDTO>> {
    return commonRequestApi<UserPracticeStatisticDTO>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 练习正确率趋势统计- 来源中间表(已实现)
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserPracticeCorrectRateTrend(
    paramDTO: PracticeTrendStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserPracticeCorrectRateTrend,
    operation?: string
  ): Promise<Response<Array<PracticeCorrectRateTrendDTO>>> {
    return commonRequestApi<Array<PracticeCorrectRateTrendDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 练习题量趋势统计- 来源中间表(已实现)
   * 评估报告-练习-展示最近15天的练习量
   * 评估报告-错题重答-展示最近15天的练习量
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserPracticeCountTrend(
    paramDTO: PracticeTrendStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserPracticeCountTrend,
    operation?: string
  ): Promise<Response<Array<PracticeCountTrendDTO>>> {
    return commonRequestApi<Array<PracticeCountTrendDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户练习频率（天数） - 来源中间表(已实现)
   * 学员中心首页-每日一练累计练习的天数，入参 statisticPracticeTypeList 应该 DAILY
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserPracticeFrequency(
    paramDTO: UserPracticeStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserPracticeFrequency,
    operation?: string
  ): Promise<Response<PracticeFrequencyStatisticDTO>> {
    return commonRequestApi<PracticeFrequencyStatisticDTO>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户剩余的易错题数量 --未做对(在易错题场景下)- - 按试题题型维度 - 考试服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserRemainErrorProneGroupByQuestionType(
    paramDTO: ErrorProneStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserRemainErrorProneGroupByQuestionType,
    operation?: string
  ): Promise<Response<Array<ErrorProneStatisticDTO>>> {
    return commonRequestApi<Array<ErrorProneStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户剩余的易错题数量  --未做对(在易错题场景下)- - 考纲维度  -- 考试服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserRemainErrorProneGroupByTag(
    paramDTO: ErrorProneStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserRemainErrorProneGroupByTag,
    operation?: string
  ): Promise<Response<Array<ErrorProneStatisticDTO>>> {
    return commonRequestApi<Array<ErrorProneStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 用户单题试题答题统计  --考试结果解析页  -- 考试服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserSingleQuestionAnswerInfo(
    paramDTO: SingleQuestionAnswerParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserSingleQuestionAnswerInfo,
    operation?: string
  ): Promise<Response<Array<SingleQuestionAnswerStatisticDTO>>> {
    return commonRequestApi<Array<SingleQuestionAnswerStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户错题数量，按题类分组统计 考试服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserWrongQuestionGroupByQuestionCategory(
    paramDTO: UserWrongQuestionStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserWrongQuestionGroupByQuestionCategory,
    operation?: string
  ): Promise<Response<Array<UserWrongQuestionStatisticDTO>>> {
    return commonRequestApi<Array<UserWrongQuestionStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户错题数量，按题型分组统计,  错题重答、错题评估报告 考试服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserWrongQuestionGroupByQuestionType(
    paramDTO: UserWrongQuestionStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserWrongQuestionGroupByQuestionType,
    operation?: string
  ): Promise<Response<Array<UserWrongQuestionStatisticDTO>>> {
    return commonRequestApi<Array<UserWrongQuestionStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户错题数量，按标签分组统计 考试服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserWrongQuestionGroupByTag(
    paramDTO: UserWrongQuestionStatisticParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserWrongQuestionGroupByTag,
    operation?: string
  ): Promise<Response<Array<UserWrongQuestionStatisticDTO>>> {
    return commonRequestApi<Array<UserWrongQuestionStatisticDTO>>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 统计用户错题相关的合计数据 考试服务
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserWrongQuestionSummary(
    paramDTO: UserWrongQuestionSummaryParamDTO,
    query: DocumentNode = GraphqlImporter.statisticUserWrongQuestionSummary,
    operation?: string
  ): Promise<Response<UserWrongQuestionStatisticSummaryDTO>> {
    return commonRequestApi<UserWrongQuestionStatisticSummaryDTO>(SERVER_URL, {
      query: query,
      variables: { paramDTO },
      operation: operation
    })
  }

  /**   * 导入案例题
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importComprehensiveQuestion(
    request: QuestionImportRequest,
    mutate: DocumentNode = GraphqlImporter.importComprehensiveQuestion,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }
}

export default new DataGateway()
