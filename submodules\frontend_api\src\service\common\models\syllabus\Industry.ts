import Major from './Major'

/**
 * 行业信息
 */
class Industry {
  /**
   * 行业编号，对应标签编号
   */
  id: string
  /**
   * 行业名称，对应标签名称
   */
  name: string
  /**
   * 当前节点关系编号
   * @description 由于同一个标签可能存在在同一个关系中的不同节点位置,
   * 只有节点关系编号才能定位一个行业的位置
   *
   */
  relationId: string
  /**
   * 同一级序号
   */
  sequence: number
  /**
   * 专业列表
   */
  majorList: Array<Major> = []

  /**
   * 按章节的序号排序子章节，并返回子章节
   */
  sorted(): Array<Major> {
    if (this.majorList.length > 0) {
      return this.majorList.sort((first, second) => first.sequence - second.sequence)
    }
    return this.majorList
  }
}

export default Industry
