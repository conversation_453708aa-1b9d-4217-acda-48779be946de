import { RegionRequest } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import {
  StudentSchemeLearningRequestVo,
  SyncResultEnmu
} from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
export default // sku绑定模型
class SchemeSkuProperty {
  year: string
  region: string[]
  industry: string
  subjectType: string
  trainingCategory: string
  societyTrainingMajor: string[]
  // 学段
  learningPhase: string
  // 学科
  discipline: string
  constructionTrainingMajor: string
  trainingObject: string
  positionCategory: string
  trainingResultPeriodBegin: number
  trainingResultPeriodEnd: number
  requirePeriodBegin: number
  requirePeriodEnd: number
  acquiredPeriodBegin: number
  acquiredPeriodEnd: number
  technicalGrade: string
  syncResult: any
  trainingResultTimeBegin: string
  trainingResultTimeEnd: string
  registerTimeEnd: string
  registerTimeBegin: string
  companyName: string
  schemeName: string
  regionList: Array<string>
  schemeTypeInfo: Array<string>
  trainingResultList: Array<number>
  name: ''
  idCard: ''
  phone: ''
  loginAccount: ''
}
