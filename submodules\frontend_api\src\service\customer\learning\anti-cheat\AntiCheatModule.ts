import { EventEmitter } from 'events'
import AntiResponseHeaderModel from '@api/service/customer/learning/anti-cheat/models/AntiResponseHeaderModel'
import { AntiResultCodeEnum } from '@api/service/customer/learning/anti-cheat/enums/AntiResultCodeEnum'
import { AntiCheatEventEnum } from '@api/service/customer/learning/anti-cheat/enums/AntiCheatEventEnum'
/**
 * @description 监管中控层
 */
class AntiCheatModule extends EventEmitter {
  triggerEnterLearningAnti(param: AntiResponseHeaderModel) {
    this.emit(AntiCheatEventEnum.ENTER_LEARNING_ANTI, param)
  }

  triggerLearningTimingAnti(param: AntiResponseHeaderModel) {
    this.emit(AntiCheatEventEnum.LEARNING_ANTI, param)
  }

  /**
   * 销毁
   */
  destroy() {
    this.removeAllListeners()
    this.eventNames().forEach((key: any) => {
      this.removeListener(key, null as any)
    })
  }
}

export default new AntiCheatModule()
