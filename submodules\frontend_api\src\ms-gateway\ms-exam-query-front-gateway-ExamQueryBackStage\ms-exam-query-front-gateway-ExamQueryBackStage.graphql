"""独立部署的微服务,K8S服务名:ms-exam-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取答卷详情
		@param id 答卷id
		@return
	"""
	getAnswerPaperRecordInServicer(id:String):AnswerPaperResponse @optionalLogin
	"""获取当前服务商下简答题详情
		@param questionId 试题ID
		@return
	"""
	getAskQuestionInServicer(questionId:String):AskQuestionResponse @optionalLogin
	"""获取当前服务商下父子题详情
		@param questionId 试题ID
		@return
	"""
	getFatherQuestionInServicer(questionId:String):FatherQuestionResponse @optionalLogin
	"""获取当前服务商下填空题详情
		@param questionId 试题ID
		@return
	"""
	getFillQuestionInServicer(questionId:String):FillQuestionResponse @optionalLogin
	"""获取当前服务商下题库详情
		@param libraryId 题库ID
		@return
	"""
	getLibraryInServicer(libraryId:String):LibraryResponse @optionalLogin
	"""获取当前服务商下多选题详情
		@param questionId 试题ID
		@return
	"""
	getMultipleQuestionInServicer(questionId:String):MultipleQuestionResponse @optionalLogin
	"""获取当前服务商下判断题详情
		@param questionId 试题ID
		@return
	"""
	getOpinionQuestionInServicer(questionId:String):OpinionQuestionResponse @optionalLogin
	"""获取当前服务商下出卷配置分类详情
		@param categoryId 出卷配置分类ID
		@return
	"""
	getPaperPublishConfigureCategoryInServicer(categoryId:String):PaperPublishConfigureCategoryResponse @optionalLogin
	"""获取当前服务商下智能卷出卷配置详情
		@param configureId 出卷配置ID
		@return
	"""
	getPaperPublishConfigureInServicer(configureId:String):PaperPublishConfigureResponse @optionalLogin
	"""获取网校下指定课程的试题数量
		@param courseIdList 课程id集合
		@param enable       试题状态，传入查询指定状态试题数量
		@return
	"""
	getQuestionCountByRelateCourseInServicer(courseIdList:[String],enable:Boolean):[CourseQuestionCountResponse] @optionalLogin
	"""获取网校下指定题库的试题数量
		@param libraryIdList 题库id集合
		@param enable        试题状态，传入查询指定状态试题数量
		@param questionType  试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
		@return
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	getQuestionCountInServicer(libraryIdList:[String],enable:Boolean,questionType:Int):[LibraryQuestionCountResponse] @optionalLogin
	"""获取子项目下试题数量
		@param servicerId 网校ID，传入查询指定网校
		@param enable     试题状态，传入查询指定状态试题数量
		@return
	"""
	getQuestionCountInSubProject(servicerId:String,enable:Boolean):Long! @optionalLogin
	"""获取当前服务商下单选题详情
		@param questionId 试题ID
		@return
	"""
	getRadioQuestionInServicer(questionId:String):RadioQuestionResponse @optionalLogin
	"""获取子项目下学员课后测验分页列表
		@param qualificationId   学员ID
		@param answerPaperStatus 作答状态
		@param answerPaperSort 排序类型
		@param sort answerPaperSort排序方式 正序 逆序
		@return
	"""
	pageCourseQuizRecordInSubProject(page:Page,courseId:String,qualificationId:String,answerPaperStatus:Int,answerPaperSort:AnswerPaperSort,sort:SortTypeEnum):CourseQuizAnswerPaperResponsePage @page(for:"CourseQuizAnswerPaperResponse") @optionalLogin
	"""获取子项目下学员考试记录分页列表
		@param qualificationId   学员ID
		@param answerPaperStatus 作答状态
		@param answerPaperSort 排序类型
		@param sort answerPaperSort排序方式 正序 逆序
		@return
	"""
	pageExaminationRecordInSubProject(page:Page,qualificationId:String,answerPaperStatus:Int,answerPaperSort:AnswerPaperSort,sort:SortTypeEnum):ExaminationAnswerPaperResponsePage @page(for:"ExaminationAnswerPaperResponse") @optionalLogin
	"""获取当前服务商下题库分页
		@param page    分页对象
		@param request 查询参数对象
		@return
	"""
	pageLibraryInServicer(page:Page,request:LibraryRequest):LibraryResponsePage @page(for:"LibraryResponse") @optionalLogin
	"""获取当前服务商下出卷配置分类分页
		@param request 出卷配置分类查询
		@return
	"""
	pagePaperPublishConfigureCategoryInServicer(page:Page,request:PaperPublishConfigureCategoryRequest):PaperPublishConfigureCategoryResponsePage @page(for:"PaperPublishConfigureCategoryResponse") @optionalLogin
	"""获取当前服务商下出卷配置分页
		@param page    分页对象
		@param request 查询参数对象
		@return
	"""
	pagePaperPublishConfigureInServicer(page:Page,request:PaperPublishConfigureRequest):PaperPublishConfigureResponsePage @page(for:"PaperPublishConfigureResponse") @optionalLogin
	"""获取子项目下学员班级练习分页列表
		@param qualificationId   学员ID
		@param answerPaperStatus 作答状态
		@param answerPaperSort 排序类型
		@param sort answerPaperSort排序方式 正序 逆序
		@return
	"""
	pagePracticeRecordInSubProject(page:Page,qualificationId:String,answerPaperStatus:Int,answerPaperSort:AnswerPaperSort,sort:SortTypeEnum):PracticeAnswerPaperResponsePage @page(for:"PracticeAnswerPaperResponse") @optionalLogin
	"""获取当前服务商下试题分页
		@param page    分页对象
		@param request 查询参数对象
		@return
	"""
	pageQuestionInServicer(page:Page,request:QuestionRequest):BaseQuestionResponsePage @page(for:"BaseQuestionResponse") @optionalLogin
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""功能描述：时间范围查询条件
	@Author： wtl
	@Date： 2022/1/25 15:30
"""
input DateScopeRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.DateScopeRequest") {
	"""开始时间
		查询大于等于开始时间的结果
	"""
	beginTime:DateTime
	"""结束时间
		查询小于等于结束时间的结果
	"""
	endTime:DateTime
}
"""题库查询条件"""
input LibraryRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.LibraryRequest") {
	"""题库ID集合"""
	libraryIdList:[String]
	"""排除的题库ID集合"""
	excludeLibraryIdList:[String]
	"""题库名称"""
	libraryName:String
	"""父题库ID"""
	parentLibraryId:String
	"""是否可用"""
	enabled:Boolean
}
"""出卷配置分类主题模型"""
input PaperPublishConfigureCategoryRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.PaperPublishConfigureCategoryRequest") {
	"""出卷配置分类ID"""
	categoryIdList:[String]
	"""分类名称"""
	name:String
	"""父级分类id"""
	parentId:String
}
"""出卷配置主题模型"""
input PaperPublishConfigureRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.PaperPublishConfigureRequest") {
	"""出卷配置ID"""
	configureIdList:[String]
	"""出卷配置分类ID"""
	categoryIdList:[String]
	"""出卷配置名称"""
	name:String
	"""创建时间开始"""
	createTimeBegin:DateTime
	"""创建时间结束"""
	createTimeEnd:DateTime
	"""出卷模式"""
	paperPublishPatterns:Int
	"""是否启用 1 启用 2禁用"""
	status:Int
	"""是否是草稿 1是  2不是"""
	isDraft:Int
}
"""试题查询条件"""
input QuestionRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.QuestionRequest") {
	"""参训资格id"""
	qualificationId:String
	"""答卷ID"""
	answerPaperId:String
	"""试题ID集合"""
	questionIdList:[String]
	"""题库ID集合"""
	libraryIdList:[String]
	"""关联课程ID集合"""
	relateCourseIds:[String]
	"""关联工种ID集合"""
	relateTagCodes:[String]
	"""试题题目"""
	topic:String
	"""试题类型（1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7：量表题）
		@see QuestionTypeEnum
	"""
	questionType:Int
	"""创建时间范围"""
	createTimeScope:DateScopeRequest
	"""是否启用"""
	isEnabled:Boolean
	"""课程供应商id"""
	courseSupplierId:String
	"""查询范围（不传默认普通试题，不包含问卷试题）
		0-普通试题
		1-全部（包含普通试题和问卷试题）
		2-问卷试题
	"""
	queryScope:Int
}
"""@Description 答卷排序类型
	<AUTHOR>
	@Date 14:59 2022/3/25
"""
enum AnswerPaperSort @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.enums.AnswerPaperSort") {
	"""创建时间"""
	CREATE_TIME
	"""提交时间"""
	HANDED_TIME
}
"""<AUTHOR> create 2021/6/29 14:23"""
enum FillAnswerType @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.enums.FillAnswerType") {
	disarray
	sequence
	sequenceRelate
}
"""试题类型"""
enum QuestionTypeEnum @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.enums.QuestionTypeEnum") {
	"""单选题"""
	RADIO
	"""多选题"""
	MULTIPLE
	"""填空题"""
	FILL
	"""判断题"""
	OPINION
	"""简答题"""
	ASK
	"""父子题"""
	FATHER
	"""量表题"""
	SCALE
}
"""排序类型"""
enum SortTypeEnum @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.enums.SortTypeEnum") {
	"""升序"""
	ASC
	"""降序"""
	DESC
}
"""@Description （跟底层返回的一致）答卷信息
	<AUTHOR>
	@Date 15:19 2022/2/24
"""
type AnswerPaperResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.AnswerPaperResponse") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位id"""
	unitId:String
	"""所属服务商id"""
	servicerId:String
	"""id"""
	id:String
	"""答卷状态 发布中 1  取消发布 2  已发布 3"""
	status:Int!
	"""试卷作答状态 未开始作答 -1  作答中0 交卷中 1 已交卷 2"""
	answerStatus:Int!
	"""试卷阅卷状态  未交卷-1 阅卷中 0 阅卷完成 1"""
	markStatus:Int!
	"""用户答卷评定结果常量 未评定 -1  无评定结果 0 合格 1 不合格 2"""
	evaluateResult:Int!
	"""场景类型"""
	sceneType:Int!
	"""场景id"""
	sceneId:String
	qualificationId:String
	userId:String
	"""答题数"""
	answerCount:Int!
	"""开始作答时间"""
	beginAnswerTime:DateTime
	"""创建时间"""
	createdTime:DateTime
	"""发布时间"""
	publishedTime:DateTime
	"""交卷时间"""
	handingTime:DateTime
	"""交卷完成时间"""
	handedTime:DateTime
	"""阅卷开始时间"""
	markingTime:DateTime
	"""阅卷完成时间"""
	markedTime:DateTime
	"""发布时间"""
	pusblishedTime:DateTime
	"""考试时长"""
	timeLength:Int!
	"""阅卷人ID"""
	markUserId:String
	"""得分，-1表示不是为分数评定方式"""
	score:Double!
	"""正确题数，-1表示试题不进行评定"""
	correctCount:Int!
	"""错误题数，-1表示试题不进行评定"""
	incorrectCount:Int!
	studentNo:String
	name:String
	description:String
	"""总分"""
	totalScore:Double!
	cancelReason:String
	createUserId:String
	systemHanded:Boolean!
	groups:[QuestionGroup]
	"""试题"""
	questions:[Question]
	"""答卷时长"""
	answerTimeLength:Int!
}
"""简答题主题模型"""
type AskQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.AskQuestionResponse") {
	"""试题标签，教师评价题专用"""
	code:[String]
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""试题ID"""
	questionId:String
	"""父题库信息"""
	libraryInfo:LibraryResponse
	"""试题题目"""
	topic:String
	"""试题解析"""
	dissects:String
	"""是否为子题"""
	isChildQuestion:Boolean
	"""父题ID（如果为子题）"""
	parentQuestionId:String
	"""创建人Id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""试题来源类型（1：创建 2：导入）
		@see QuestionSourceTypes
	"""
	sourceType:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""试题是否可用"""
	isEnabled:Boolean
	"""试题难度（1：低难度 2：中等难度 3：高难度）
		@see QuestionDifficulty
	"""
	questionDifficulty:Int
	"""关联课程ID集合"""
	relateCourseIds:[String]
	"""关联工种ID集合"""
	relateTagCodes:[String]
	"""关联课程
		relateCourseIds 拓展
	"""
	relateCourse:[RelateCourse]
}
"""试题主题模型"""
interface BaseQuestionResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.BaseQuestionResponse") {
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""试题ID"""
	questionId:String
	"""父题库信息"""
	libraryInfo:LibraryResponse
	"""试题题目"""
	topic:String
	"""试题解析"""
	dissects:String
	"""是否为子题"""
	isChildQuestion:Boolean
	"""父题ID（如果为子题）"""
	parentQuestionId:String
	"""创建人Id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""试题来源类型（1：创建 2：导入）
		@see QuestionSourceTypes
	"""
	sourceType:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""试题是否可用"""
	isEnabled:Boolean
	"""试题难度（1：低难度 2：中等难度 3：高难度）
		@see QuestionDifficulty
	"""
	questionDifficulty:Int
	"""关联课程ID集合"""
	relateCourseIds:[String]
	"""关联工种ID集合"""
	relateTagCodes:[String]
	"""关联课程
		relateCourseIds 拓展
	"""
	relateCourse:[RelateCourse]
}
"""课程试题数量"""
type CourseQuestionCountResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.CourseQuestionCountResponse") {
	"""课程ID"""
	courseId:String
	"""试题数量"""
	questionCount:Int!
}
"""考试答卷主题模型"""
type CourseQuizAnswerPaperResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.CourseQuizAnswerPaperResponse") {
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""答卷ID"""
	answerPaperId:String
	"""用户ID"""
	userId:String
	"""答卷基础信息"""
	answerPaperBasicInfo:CourseQuizAnswerPaperBasicInfo
	"""答卷状态信息"""
	answerPaperStateInfo:CourseQuizAnswerPaperStateInfo
	"""答卷时间信息"""
	answerPaperTimeInfo:CourseQuizAnswerPaperTimeInfo
	"""答卷答题信息"""
	answerPaperAnswerInfo:CourseQuizAnswerPaperAnswerInfo
	"""答卷评定信息"""
	answerPaperMarkInfo:CourseQuizAnswerPaperMarkInfo
}
"""考试答卷主题模型"""
type ExaminationAnswerPaperResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.ExaminationAnswerPaperResponse") {
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""答卷ID"""
	answerPaperId:String
	"""用户ID"""
	userId:String
	"""答卷基础信息"""
	answerPaperBasicInfo:ExaminationAnswerPaperBasicInfo
	"""答卷状态信息"""
	answerPaperStateInfo:ExaminationAnswerPaperStateInfo
	"""答卷时间信息"""
	answerPaperTimeInfo:ExaminationAnswerPaperTimeInfo
	"""答卷答题信息"""
	answerPaperAnswerInfo:ExaminationAnswerPaperAnswerInfo
	"""答卷评定信息"""
	answerPaperMarkInfo:ExaminationAnswerPaperMarkInfo
}
"""父子题主题模型"""
type FatherQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.FatherQuestionResponse") {
	"""子题集合"""
	childQuestions:[ChildItem]
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""试题ID"""
	questionId:String
	"""父题库信息"""
	libraryInfo:LibraryResponse
	"""试题题目"""
	topic:String
	"""试题解析"""
	dissects:String
	"""是否为子题"""
	isChildQuestion:Boolean
	"""父题ID（如果为子题）"""
	parentQuestionId:String
	"""创建人Id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""试题来源类型（1：创建 2：导入）
		@see QuestionSourceTypes
	"""
	sourceType:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""试题是否可用"""
	isEnabled:Boolean
	"""试题难度（1：低难度 2：中等难度 3：高难度）
		@see QuestionDifficulty
	"""
	questionDifficulty:Int
	"""关联课程ID集合"""
	relateCourseIds:[String]
	"""关联工种ID集合"""
	relateTagCodes:[String]
	"""关联课程
		relateCourseIds 拓展
	"""
	relateCourse:[RelateCourse]
}
"""填空题主题模型"""
type FillQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.FillQuestionResponse") {
	"""填空数"""
	fillCount:Int!
	"""正确答案"""
	fillCorrectAnswer:FillAnswer
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""试题ID"""
	questionId:String
	"""父题库信息"""
	libraryInfo:LibraryResponse
	"""试题题目"""
	topic:String
	"""试题解析"""
	dissects:String
	"""是否为子题"""
	isChildQuestion:Boolean
	"""父题ID（如果为子题）"""
	parentQuestionId:String
	"""创建人Id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""试题来源类型（1：创建 2：导入）
		@see QuestionSourceTypes
	"""
	sourceType:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""试题是否可用"""
	isEnabled:Boolean
	"""试题难度（1：低难度 2：中等难度 3：高难度）
		@see QuestionDifficulty
	"""
	questionDifficulty:Int
	"""关联课程ID集合"""
	relateCourseIds:[String]
	"""关联工种ID集合"""
	relateTagCodes:[String]
	"""关联课程
		relateCourseIds 拓展
	"""
	relateCourse:[RelateCourse]
}
"""题库试题数量"""
type LibraryQuestionCountResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.LibraryQuestionCountResponse") {
	"""题库ID"""
	libraryId:String
	"""试题数量"""
	questionCount:Int!
}
"""题库主题模型"""
type LibraryResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.LibraryResponse") {
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""题库ID"""
	libraryId:String
	"""题库名称"""
	libraryName:String
	"""父题库信息"""
	parentLibraryInfo:LibraryResponse
	"""题库描述"""
	description:String
	"""是否可用"""
	enabled:Boolean
	"""创建人Id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""题库来源类型（1：创建 2：导入）
		@see LibrarySourceTypes
	"""
	sourceType:Int
}
"""多选题主题模型"""
type MultipleQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.MultipleQuestionResponse") {
	"""可选答案列表"""
	multipleAnswerOptions:[ChooseAnswerOption]
	"""正确答案ID集合"""
	correctAnswerIds:[String]
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""试题ID"""
	questionId:String
	"""父题库信息"""
	libraryInfo:LibraryResponse
	"""试题题目"""
	topic:String
	"""试题解析"""
	dissects:String
	"""是否为子题"""
	isChildQuestion:Boolean
	"""父题ID（如果为子题）"""
	parentQuestionId:String
	"""创建人Id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""试题来源类型（1：创建 2：导入）
		@see QuestionSourceTypes
	"""
	sourceType:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""试题是否可用"""
	isEnabled:Boolean
	"""试题难度（1：低难度 2：中等难度 3：高难度）
		@see QuestionDifficulty
	"""
	questionDifficulty:Int
	"""关联课程ID集合"""
	relateCourseIds:[String]
	"""关联工种ID集合"""
	relateTagCodes:[String]
	"""关联课程
		relateCourseIds 拓展
	"""
	relateCourse:[RelateCourse]
}
"""判断题主题模型"""
type OpinionQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.OpinionQuestionResponse") {
	"""正确答案"""
	opinionCorrectAnswer:Boolean
	"""正确文本"""
	correctAnswerText:String
	"""不正确文本"""
	incorrectAnswerText:String
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""试题ID"""
	questionId:String
	"""父题库信息"""
	libraryInfo:LibraryResponse
	"""试题题目"""
	topic:String
	"""试题解析"""
	dissects:String
	"""是否为子题"""
	isChildQuestion:Boolean
	"""父题ID（如果为子题）"""
	parentQuestionId:String
	"""创建人Id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""试题来源类型（1：创建 2：导入）
		@see QuestionSourceTypes
	"""
	sourceType:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""试题是否可用"""
	isEnabled:Boolean
	"""试题难度（1：低难度 2：中等难度 3：高难度）
		@see QuestionDifficulty
	"""
	questionDifficulty:Int
	"""关联课程ID集合"""
	relateCourseIds:[String]
	"""关联工种ID集合"""
	relateTagCodes:[String]
	"""关联课程
		relateCourseIds 拓展
	"""
	relateCourse:[RelateCourse]
}
"""出卷配置分类主题模型"""
type PaperPublishConfigureCategoryResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.PaperPublishConfigureCategoryResponse") {
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""出卷配置分类ID"""
	id:String
	"""分类名称"""
	name:String
	"""父级分类id"""
	parentCategory:PaperPublishConfigureCategoryResponse
	"""排序"""
	sort:Int!
	"""创建人id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
}
"""出卷配置主题模型"""
type PaperPublishConfigureResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.PaperPublishConfigureResponse") {
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""出卷配置ID"""
	id:String
	"""出卷配置名称"""
	name:String
	"""数据归属信息"""
	paperPublishConfigureCategory:PaperPublishConfigureCategoryResponse
	"""创建人Id"""
	createUserId:String
	"""创建时间"""
	createdTime:DateTime
	"""出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2"""
	paperPublishPatterns:Int
	"""出卷模式"""
	publishPattern:PublishPattern
	"""是否启用 1 启用 2禁用"""
	status:Int
	"""适用范围 用于筛选自定义的分类"""
	usageScope:Int
	"""是否是草稿 1是  2不是"""
	isDraft:Int
}
"""课后测验答卷主题模型"""
type PracticeAnswerPaperResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.PracticeAnswerPaperResponse") {
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""答卷ID"""
	answerPaperId:String
	"""用户ID"""
	userId:String
	"""课程ID"""
	courseId:String
	"""答卷基础信息"""
	answerPaperBasicInfo:PracticeAnswerPaperBasicInfo
	"""答卷状态信息"""
	answerPaperStateInfo:PracticeAnswerPaperStateInfo
	"""答卷时间信息"""
	answerPaperTimeInfo:PracticeAnswerPaperTimeInfo
	"""答卷答题信息"""
	answerPaperAnswerInfo:PracticeAnswerPaperAnswerInfo
	"""答卷评定信息"""
	answerPaperMarkInfo:PracticeAnswerPaperMarkInfo
}
"""单选题主题模型"""
type RadioQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.RadioQuestionResponse") {
	"""可选答案列表"""
	radioAnswerOptions:[ChooseAnswerOption]
	"""正确答案ID"""
	correctAnswerId:String
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""试题ID"""
	questionId:String
	"""父题库信息"""
	libraryInfo:LibraryResponse
	"""试题题目"""
	topic:String
	"""试题解析"""
	dissects:String
	"""是否为子题"""
	isChildQuestion:Boolean
	"""父题ID（如果为子题）"""
	parentQuestionId:String
	"""创建人Id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""试题来源类型（1：创建 2：导入）
		@see QuestionSourceTypes
	"""
	sourceType:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""试题是否可用"""
	isEnabled:Boolean
	"""试题难度（1：低难度 2：中等难度 3：高难度）
		@see QuestionDifficulty
	"""
	questionDifficulty:Int
	"""关联课程ID集合"""
	relateCourseIds:[String]
	"""关联工种ID集合"""
	relateTagCodes:[String]
	"""关联课程
		relateCourseIds 拓展
	"""
	relateCourse:[RelateCourse]
}
"""量表题主题模型"""
type ScaleQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.ScaleQuestionResponse") {
	"""量表类型"""
	scaleType:Int!
	"""程度-始"""
	startDegree:String
	"""程度-止"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""试题ID"""
	questionId:String
	"""父题库信息"""
	libraryInfo:LibraryResponse
	"""试题题目"""
	topic:String
	"""试题解析"""
	dissects:String
	"""是否为子题"""
	isChildQuestion:Boolean
	"""父题ID（如果为子题）"""
	parentQuestionId:String
	"""创建人Id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""试题来源类型（1：创建 2：导入）
		@see QuestionSourceTypes
	"""
	sourceType:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""试题是否可用"""
	isEnabled:Boolean
	"""试题难度（1：低难度 2：中等难度 3：高难度）
		@see QuestionDifficulty
	"""
	questionDifficulty:Int
	"""关联课程ID集合"""
	relateCourseIds:[String]
	"""关联工种ID集合"""
	relateTagCodes:[String]
	"""关联课程
		relateCourseIds 拓展
	"""
	relateCourse:[RelateCourse]
}
"""数据归属模型"""
type DataBelongModel @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.common.DataBelongModel") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位id"""
	unitId:String
	"""所属服务商id"""
	servicerId:String
}
"""智能卷出卷模式"""
type AutomaticPublishPattern implements PublishPattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.AutomaticPublishPattern") {
	"""建议作答时长"""
	suggestionTimeLength:Int!
	"""抽题规则"""
	questionExtractRule:QuestionExtractRule
	"""评定方式"""
	evaluatePattern:EvaluatePattern
	"""出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2"""
	type:Int
}
"""课后测验答题信息"""
type CourseQuizAnswerPaperAnswerInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.CourseQuizAnswerPaperAnswerInfo") {
	"""作答时长"""
	answerTimeLength:Int!
	"""答题总数"""
	answerCount:Int!
	"""是否系统强制交卷"""
	systemHanded:Boolean!
	"""考试时长，单位：秒，-1表示没有时长限制"""
	examTimeLength:Int!
}
"""课后测验基础信息"""
type CourseQuizAnswerPaperBasicInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.CourseQuizAnswerPaperBasicInfo") {
	"""课后测验ID"""
	sceneId:String
	"""课程ID"""
	courseId:String
	"""试卷名称"""
	name:String
	"""试卷描述"""
	description:String
	"""试题总数"""
	questionCount:Int!
	"""试卷总分 -1表示不以分数方式进行评定"""
	totalScore:BigDecimal
	"""及格分"""
	qualifiedScore:BigDecimal
}
"""课后测验评定信息"""
type CourseQuizAnswerPaperMarkInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.CourseQuizAnswerPaperMarkInfo") {
	"""阅卷人ID"""
	markUserId:String
	"""得分，-1表示不是为分数评定方式"""
	score:Double!
	"""正确题数，-1表示试题不进行评定"""
	correctCount:Int!
	"""错误题数，-1表示试题不进行评定"""
	incorrectCount:Int!
}
"""课后测验状态信息"""
type CourseQuizAnswerPaperStateInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.CourseQuizAnswerPaperStateInfo") {
	"""答卷状态"""
	answerPaperStatus:Int!
	"""答卷作答状态"""
	answerPaperAnswerStatus:Int!
	"""答卷阅卷状态"""
	answerPaperMarkStatus:Int!
	"""答卷评定结果"""
	answerPaperEvaluateResults:Int!
}
"""课后测验时间信息"""
type CourseQuizAnswerPaperTimeInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.CourseQuizAnswerPaperTimeInfo") {
	"""创建时间"""
	createdTime:DateTime
	"""发布时间"""
	publishedTime:DateTime
	"""开始作答时间"""
	answeringTime:DateTime
	"""交卷时间"""
	handingTime:DateTime
	"""交卷完成时间"""
	handedTime:DateTime
	"""阅卷开始时间"""
	markingTime:DateTime
	"""阅卷完成时间"""
	markedTime:DateTime
}
"""答卷答题信息"""
type ExaminationAnswerPaperAnswerInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.ExaminationAnswerPaperAnswerInfo") {
	"""作答时长"""
	answerTimeLength:Int!
	"""答题总数"""
	answerCount:Int!
}
"""答卷基础信息"""
type ExaminationAnswerPaperBasicInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.ExaminationAnswerPaperBasicInfo") {
	"""考试ID"""
	sceneId:String
	"""试卷名称"""
	name:String
	"""试卷描述"""
	description:String
	"""试题总数"""
	questionCount:Int!
	"""试卷总分 -1表示不以分数方式进行评定"""
	totalScore:BigDecimal
	"""及格分"""
	qualifiedScore:BigDecimal
	"""考试场次名称"""
	examSessionName:String
}
"""答卷评定信息"""
type ExaminationAnswerPaperMarkInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.ExaminationAnswerPaperMarkInfo") {
	"""阅卷人ID"""
	markUserId:String
	"""得分，-1表示不是为分数评定方式"""
	score:Double!
	"""正确题数，-1表示试题不进行评定"""
	correctCount:Int!
	"""错误题数，-1表示试题不进行评定"""
	incorrectCount:Int!
}
"""答卷状态信息"""
type ExaminationAnswerPaperStateInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.ExaminationAnswerPaperStateInfo") {
	"""答卷状态"""
	answerPaperStatus:Int!
	"""答卷作答状态"""
	answerPaperAnswerStatus:Int!
	"""答卷阅卷状态"""
	answerPaperMarkStatus:Int!
	"""答卷评定结果"""
	answerPaperEvaluateResults:Int!
}
"""答卷时间信息"""
type ExaminationAnswerPaperTimeInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.ExaminationAnswerPaperTimeInfo") {
	"""创建时间"""
	createdTime:DateTime
	"""发布时间"""
	publishedTime:DateTime
	"""开始作答时间"""
	answeringTime:DateTime
	"""交卷时间"""
	handingTime:DateTime
	"""交卷完成时间"""
	handedTime:DateTime
	"""阅卷开始时间"""
	markingTime:DateTime
	"""阅卷完成时间"""
	markedTime:DateTime
}
"""固定卷出卷模式"""
type FixedPaper implements PublishPattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.FixedPaper") {
	"""试卷id"""
	id:String
	"""试卷名称"""
	name:String
	"""试卷描述"""
	description:String
	"""作答时长"""
	timeLength:Int!
	"""试卷总分"""
	totalScore:Double!
	"""大题集合"""
	groups:[QuestionGroup]
	"""试题集合"""
	questions:[PaperQuestion]
	"""出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2"""
	type:Int
}
"""AB出卷模式"""
type MultipleFixedPaperPublishPattern implements PublishPattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.MultipleFixedPaperPublishPattern") {
	"""固定卷集合"""
	fixedPapers:[FixedPaper]
	"""出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2"""
	type:Int
}
"""<AUTHOR> create 2021/6/3 17:35"""
type PaperQuestion @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.PaperQuestion") {
	"""试题ID"""
	questionId:String
	"""分数，-1表示不为分数评定方式为"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""是否必答"""
	answerRequired:Boolean!
}
"""练习答题信息"""
type PracticeAnswerPaperAnswerInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.PracticeAnswerPaperAnswerInfo") {
	"""作答时长"""
	answerTimeLength:Int!
	"""答题总数"""
	answerCount:Int!
}
"""练习基础信息"""
type PracticeAnswerPaperBasicInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.PracticeAnswerPaperBasicInfo") {
	"""练习ID"""
	sceneId:String
	"""试卷名称"""
	name:String
	"""试卷描述"""
	description:String
	"""试题总数"""
	questionCount:Int!
}
"""练习评定信息"""
type PracticeAnswerPaperMarkInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.PracticeAnswerPaperMarkInfo") {
	"""阅卷人ID"""
	markUserId:String
	"""得分，-1表示不是为分数评定方式"""
	score:Double!
	"""正确题数，-1表示试题不进行评定"""
	correctCount:Int!
	"""错误题数，-1表示试题不进行评定"""
	incorrectCount:Int!
}
"""练习状态信息"""
type PracticeAnswerPaperStateInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.PracticeAnswerPaperStateInfo") {
	"""答卷状态"""
	answerPaperStatus:Int!
	"""答卷作答状态"""
	answerPaperAnswerStatus:Int!
	"""答卷阅卷状态"""
	answerPaperMarkStatus:Int!
	"""答卷评定结果"""
	answerPaperEvaluateResults:Int!
}
"""练习时间信息"""
type PracticeAnswerPaperTimeInfo @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.PracticeAnswerPaperTimeInfo") {
	"""创建时间"""
	createdTime:DateTime
	"""发布时间"""
	publishedTime:DateTime
	"""开始作答时间"""
	answeringTime:DateTime
	"""交卷时间"""
	handingTime:DateTime
	"""交卷完成时间"""
	handedTime:DateTime
}
"""@Description 初级模式
	<AUTHOR>
	@Date 9:14 2022/3/1
"""
interface PublishPattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.PublishPattern") {
	"""出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2"""
	type:Int
}
"""试卷大题信息
	<AUTHOR>
"""
type QuestionGroup @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.QuestionGroup") {
	sequence:Int!
	questionType:Int!
	groupName:String
	eachQuestionScore:Double!
}
"""正确率评定方式
	<AUTHOR>
"""
type CorrectRateEvaluatePattern implements EvaluatePattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.evaluate.CorrectRateEvaluatePattern") {
	"""要求答题总数"""
	answerQuestionCount:Int!
	"""合格正确率"""
	qualifiedCorrectRate:Double!
	"""评定方式类型"""
	type:Int
}
"""评定方式基类
	<AUTHOR>
"""
interface EvaluatePattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.evaluate.EvaluatePattern") {
	"""评定方式类型"""
	type:Int
}
"""无评定方式
	<AUTHOR>
"""
type NoneEvaluatePattern implements EvaluatePattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.evaluate.NoneEvaluatePattern") {
	"""评定方式类型"""
	type:Int
}
"""@Description试题指定分值设置
	<AUTHOR>
	@Date 15:15 2022/3/3
"""
type QuestionMapScoreSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.evaluate.QuestionMapScoreSetting") {
	"""试题id"""
	questionId:String
	"""分数"""
	score:Double!
}
"""试题分数设置信息
	<AUTHOR>
"""
type QuestionScoreSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.evaluate.QuestionScoreSetting") {
	"""大题序号"""
	sequence:Int
	"""试题类型"""
	questionType:Int
	"""每题平均分"""
	eachQuestionScore:Double
	"""具体试题分数"""
	questionScores:[QuestionMapScoreSetting]
}
"""分数评定方式
	<AUTHOR>
"""
type ScoreEvaluatePattern implements EvaluatePattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.evaluate.ScoreEvaluatePattern") {
	"""总分"""
	totalScore:Double
	"""合格分数"""
	qualifiedScore:Double
	"""每道试题分数"""
	questionScores:[QuestionScoreSetting]
	"""多选题漏选得分模式"""
	multipleMissScorePattern:Int
	"""评定方式类型"""
	type:Int
}
"""试题抽题规则
	<AUTHOR>
"""
type QuestionExtractRule @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.QuestionExtractRule") {
	"""试题总数"""
	questionCount:Int!
	"""出题范围"""
	questionScopes:[QuestionScopeSetting]
	"""出题描述"""
	questionExtracts:[QuestionExtractSetting]
}
"""试题抽题设置信息
	<AUTHOR>
"""
type QuestionExtractSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.QuestionExtractSetting") {
	"""试题类型"""
	questionType:Int!
	"""大题序号"""
	sequence:Int
	"""大题名称"""
	groupName:String
	"""试题数"""
	questionCount:Int!
	"""出题范围"""
	questionScopes:[QuestionScopeSetting]
}
"""出题范围设置基类
	<AUTHOR>
"""
interface QuestionScopeSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.QuestionScopeSetting") {
	"""出题类型"""
	type:Int
}
"""题库出题配置
	<AUTHOR> create 2021/8/19 11:18
"""
type LibraryFixedQuestionScopeSetting implements QuestionScopeSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.scopes.LibraryFixedQuestionScopeSetting") {
	"""题库对应试题数设置对象"""
	libraryMapQuestionNumSettings:[LibraryMapQuestionNumSetting]
	"""出题类型"""
	type:Int
}
"""@Description 题库对应试题数设置对象
	<AUTHOR>
	@Date 14:20 2022/3/3
"""
type LibraryMapQuestionNumSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.scopes.LibraryMapQuestionNumSetting") {
	"""题库id"""
	libraryId:String
	"""试题数量"""
	questionNum:Int
}
"""题库出题配置
	<AUTHOR> create 2021/8/19 11:18
"""
type LibraryQuestionScopeSetting implements QuestionScopeSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.scopes.LibraryQuestionScopeSetting") {
	"""题库id集合"""
	libraryIds:[String]
	"""出题类型"""
	type:Int
}
"""用户课程抽题维度
	<AUTHOR> create 2021/11/26 13:55
"""
type UserCourseScopeSetting implements QuestionScopeSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.scopes.UserCourseScopeSetting") {
	"""课程来源
		@see UserCourseSources
	"""
	userCourseSource:Int!
	"""要求的组卷信息key.
		当{@link #userCourseSource} = 用户课程题库时需要指定
		@see ExtractionMessageKeys
	"""
	requireKeys:[String]
	"""出题类型"""
	type:Int
}
"""@Description
	<AUTHOR>
	@Date 17:13 2022/3/9
"""
type Answer @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.Answer") {
	key:Int
	"""答案"""
	answer:String
}
"""@Description 问答题
	<AUTHOR>
	@Date 15:48 2022/2/28
"""
type AskQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.AskQuestion") {
	"""答案"""
	askAnswer:String
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""父子题子题项"""
type ChildItem @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.ChildItem") {
	"""子题序号"""
	no:Int!
	"""子题ID"""
	questionId:String
}
"""选择题答案选项"""
type ChooseAnswerOption @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.ChooseAnswerOption") {
	"""答案ID"""
	id:String
	"""答案内容"""
	content:String
	"""是否填空题"""
	enableFillContent:Boolean!
	"""是否必填"""
	mustFillContent:Boolean!
}
"""散乱无序填空题答案实体
	<pre>
	每空有多种匹配答案，空格答案不存在顺序关系： 比如：
	试题题目：请写出中国四大银行__________、__________、__________、__________。
	每空备选答案：
	1/中国建设银行 建设银行 建行
	2/中国银行 中行
	3/中国工商银行 工商银行 工行
	4/中国农业银行 农业银行 农行
	学员答题答案：农行、工行、中行、建行；评卷为正确并得分；
	</pre>
	<AUTHOR>
"""
type DisarrayFillAnswer implements FillAnswer @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.DisarrayFillAnswer") {
	"""正确答案集合"""
	disarrayCorrectAnswers:[[String]]
	"""填空题答案类型"""
	type:FillAnswerType
}
"""@Description 父子题
	<AUTHOR>
	@Date 15:48 2022/2/28
"""
type FatherQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.FatherQuestion") {
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""填空题答案基类"""
interface FillAnswer @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.FillAnswer") {
	"""填空题答案类型"""
	type:FillAnswerType
}
"""功能描述：填空
	@Author： wtl
	@Date： 2022/2/17 17:40
"""
type FillCorrectAnswers @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.FillCorrectAnswers") {
	"""空格位置"""
	blankNo:Int!
	"""答案备选项"""
	answers:[String]
}
"""@Description 填空题
	<AUTHOR>
	@Date 15:48 2022/2/28
"""
type FillQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.FillQuestion") {
	"""答案"""
	fillAnswer:[Answer]
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""@Description 多选题
	<AUTHOR>
	@Date 15:48 2022/2/28
"""
type MultipleQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.MultipleQuestion") {
	"""答案"""
	multipleAnswer:[String]
	"""填空的内容"""
	fillContents:[MultipleQuestionFillContent]
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
type MultipleQuestionFillContent @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.MultipleQuestionFillContent") {
	"""选项id"""
	id:String
	"""填空内容"""
	fillContent:String
}
"""@Description 判断题
	<AUTHOR>
	@Date 15:48 2022/2/28
"""
type OpinionQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.OpinionQuestion") {
	"""答案"""
	opinionAnswer:Boolean
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""@Description 试题
	<AUTHOR>
	@Date 16:07 2022/2/28
"""
interface Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.Question") {
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""@Description 单选题和底层一样
	<AUTHOR>
	@Date 15:40 2022/2/28
"""
type RadioQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.RadioQuestion") {
	"""答案"""
	radioAnswer:String
	"""需要填空的内容"""
	fillContent:String
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""试题关联课程"""
type RelateCourse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.RelateCourse") {
	"""课程id"""
	courseId:String
	"""课程供应商id"""
	courseSupplierId:String
}
"""@Description 量表题
	<AUTHOR>
	@Date 15:48 2022/2/28
"""
type ScaleQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.ScaleQuestion") {
	"""答案"""
	answer:Int!
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""按序填空题答案实体
	<pre>
	每空有多种匹配答案且空格答案存在顺序关系： 比如：
	试题题目：中国的政治中心是__________；中国的经济中心是__________。
	试题答案： 1/北京北京市、2/上海上海市
	学员答题答案：北京市、上海；评卷为正确并得分；
	</pre>
	<AUTHOR>
"""
type SequenceFillAnswer implements FillAnswer @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.SequenceFillAnswer") {
	"""每个填空数答案"""
	sequenceCorrectAnswers:[FillCorrectAnswers]
	"""填空题答案类型"""
	type:FillAnswerType
}
"""按序关联填空题答案实体
	<pre>
	每空答案精确匹配： 适用于前后空格的答案是有关联的。比如：
	试题题目：请写出中国四大名著之一是__________；作者是__________。
	试题答案： 1/红楼梦曹雪芹、2/西游记吴承恩
	学员答题答案：红楼梦、曹雪芹；评卷为正确并得分；
	红楼梦、吴承恩；评卷则给第一个空得分；
	</pre>
	<AUTHOR>
"""
type SequenceRelateFillAnswer implements FillAnswer @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.SequenceRelateFillAnswer") {
	"""正确答案集合"""
	sequenceRelateCorrectAnswers:[SequenceFillAnswer]
	"""填空题答案类型"""
	type:FillAnswerType
}

scalar List
type CourseQuizAnswerPaperResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseQuizAnswerPaperResponse]}
type ExaminationAnswerPaperResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ExaminationAnswerPaperResponse]}
type LibraryResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [LibraryResponse]}
type PaperPublishConfigureCategoryResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [PaperPublishConfigureCategoryResponse]}
type PaperPublishConfigureResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [PaperPublishConfigureResponse]}
type PracticeAnswerPaperResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [PracticeAnswerPaperResponse]}
type BaseQuestionResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BaseQuestionResponse]}
