schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询用户一场考试下所有作答记录人脸识别结果
		@param queryInfo 查询信息
	"""
	findAllUserExamAnswerFSResults(queryInfo:ExamAnswerFSResultQueryRequest):[ExamAnswerFSResultResponse]
	"""分页查询用户指定学习方案的所有课程人脸识别防作弊记录
		@param page 分页信息
		@param queryInfo 查询信息
		@return
	"""
	findAllUserSchemeCourseFSRecordPage(page:Page,queryInfo:SchemeCourseFSRecordsQueryRequest):CourseFSInfoResponsePage @page(for:"CourseFSInfoResponse")
	"""分页查询用户指定学习方案的所有课件人脸识别防作弊记录
		@param page 分页信息
		@param queryInfo 查询信息
		@return
	"""
	findAllUserSchemeCoursewareFSRecordPage(page:Page,queryInfo:SchemeCoursewareFSRecordsQueryRequest):CoursewareFSInfoResponsePage @page(for:"CoursewareFSInfoResponse")
	"""分页查询用户指定学习方案的所有课件人脸识别防作弊记录，学习过程的相同拍摄点只返回最新的一次拍摄记录
		@param page 分页信息
		@param queryInfo 查询信息
	"""
	findAllUserSchemeCoursewareFSRecordPageByLasted(page:Page,queryInfo:SchemeCoursewareFSRecordsQueryRequest):CoursewareFSInfoResponsePage @page(for:"CoursewareFSInfoResponse")
	"""分页查询用户指定学习方案的所有考试人脸识别防作弊记录
		@param page 分页信息
		@param queryInfo 查询信息
	"""
	findAllUserSchemeExamFSRecordPage(page:Page,queryInfo:SchemeExamFSRecordQueryRequest):ExamFSInfoResponsePage @page(for:"ExamFSInfoResponse")
	"""查询子项目下的人脸识别采集配置
		@return 采集配置
	"""
	findDatumConfig:DatumConfigResponse
	"""查询平台下的人脸识别采集配置
		@return 采集配置
	"""
	findDatumConfigByPlatform:PlatformDatumConfigResponse
	"""查询当前用户指定考试的申诉次数信息
		@param queryInfo 查询信息
	"""
	findExamAppealCountInfoByCurrentUser(queryInfo:CurrentUserExamAppealCountInfoQueryRequest):ExamUserAppealCountInfoResponse
	"""查询当前用户考试申诉
		@param queryInfo 查询信息
	"""
	findExamAppealListByCurrentUser(queryInfo:CurrentUserExamAppealQueryRequest):[ExamUserAppealResponse]
	"""<pre>
		按条件查询平台中已启用的考试防作弊配置，
		如果指定条件不存在，则进行向上查询，查询步骤为：试卷配置->学习方案->单位->组织机构->子项目
		</pre>
		@param configQuery 查询条件
		@return 防作弊配置编号，如果为null则表示没有找到配置
	"""
	findExamScenesConfig(configQuery:ExamScenesConfigQueryRequest):ExamScenesConfigResponse
	"""查询考试场景配置，如果提供的配置编号不是考试场景，则抛出异常
		@param configId 考试场景配置编号
		@return 配置信息
	"""
	findExamScenesConfigInfo(configId:String):ExamScenesConfigResponse
	"""获取用户考试申诉
		@param id 申诉ID
	"""
	findExamUserAppealById(id:String):ExamUserAppealResponse
	"""分页查询用户考试申诉
		@param pageIndex 当前页码
		@param pageSize 分页大小
		@param queryInfo 查询信息
	"""
	findExamUserAppealPageList(pageIndex:Int!,pageSize:Int!,queryInfo:ExamUserAppealPageQueryRequest):ExamUserAppealResponsePage @page(for:"ExamUserAppealResponse")
	"""<pre>
		按条件查询平台中已启用的课程学习防作弊配置，
		如果指定条件不存在，则进行向上查询，查询步骤为：课程->学习方案->单位->组织机构->子项目
		</pre>
		@param configQuery 查询条件
		@return 防作弊配置编号，如果为null则表示没有找到配置
	"""
	findLearningScenesConfig(configQuery:LearningScenesConfigQueryRequest):LearningScenesConfigResponse
	"""查询课程学习场景配置，如果提供的配置编号不是课程学习场景，则抛出异常
		@param configId 课程学习场景配置编号
		@return 配置信息
	"""
	findLearningScenesConfigInfo(configId:String):LearningScenesConfigResponse
	"""<pre>
		按条件查询平台中已启用的登录防作弊配置，
		如果指定条件不存在，则进行向上查询，查询步骤为：单位->组织机构->子项目
		</pre>
		@param configQuery 查询条件
		@return 防作弊配置编号
	"""
	findLoginScenesConfig(configQuery:LoginScenesConfigQueryRequest):LoginScenesConfigResponse
	"""查询登录场景配置，如果提供的配置编号不是登录场景，则抛出异常
		@param configId 登录场景配置编号
		@return 配置信息
	"""
	findLoginScenesConfigInfo(configId:String):LoginScenesConfigResponse
	"""查询平台级的防作弊配置
		@return 配置信息
	"""
	findPlatformLearningScenesConfigInfo:LearningScenesConfigResponse
	"""创建和更新单位级别人脸识别监管配置
		@param unitId 单位编号
		@return 人脸识别监管配置
	"""
	findSupervisionConfigByByUnit(unitId:String):FaceSupervisionSettingResponse
	"""获取平台级别人脸识别监管配置
		@return 人脸识别监管配置
	"""
	findSupervisionConfigByPlatform:FaceSupervisionSettingResponse
	"""获取子项目级别人脸识别监管配置
		@return 人脸识别监管配置
	"""
	findSupervisionConfigBySubProject:FaceSupervisionSettingResponse
	"""查询用户基准照信息
		@param userId 用户编号
		@return 基准照信息
	"""
	findUserDatum(userId:String):UserDatumResponse
	"""查询用户指定考试答卷的人脸识别防作弊记录
		@param queryInfo 查询信息
	"""
	findUserExamAnswerPagerFSRecords(queryInfo:ExamAnswerPagerFSRecordQueryRequest):[ExamFSRecordResponse]
	"""查询用户指定考试答卷的人脸识别防作弊记录，考试过程的相同拍摄点只返回最新的一次拍摄记录，进入考试和退出考试全部返回
		@param queryInfo 查询信息
	"""
	findUserExamAnswerPagerFSRecordsByLasted(queryInfo:ExamAnswerPagerFSRecordQueryRequest):[ExamFSRecordResponse]
	"""分页查询用户登录人脸识别防作弊记录
		@param page 分页信息
		@param queryInfo 查询信息
	"""
	findUserLoginFSRecordPage(page:Page,queryInfo:LoginFSRecordQueryRequest):LoginFSRecordResponsePage @page(for:"LoginFSRecordResponse")
	"""查询某用户基准照的变更记录
		@param userId  用户编号
		@return 变更记录列表
	"""
	listUserDatumPhotoChangeRecord(userId:String):[UserDatumPhotoChangeRecordResponse]
}
type Mutation {
	"""添加考试场景防作弊配置
		@param config 配置信息
		@return 配置信息
	"""
	addExamScenesConfig(config:ExamScenesConfigAddRequest):ExamScenesConfigResponse
	"""添加课程场景防作弊配置
		@param config 配置信息
		@return 配置信息
	"""
	addLearningScenesConfig(config:LearningScenesConfigAddRequest):LearningScenesConfigResponse
	"""添加登录场景防作弊配置
		@param config 配置信息
		@return 配置信息
	"""
	addLoginScenesConfig(config:LoginScenesConfigAddRequest):LoginScenesConfigResponse
	"""添加平台级课程场景防作弊配置
		@param config 配置信息
		@return 配置信息
	"""
	addPlatformLearningScenesConfig(config:PlatformLearningScenesConfigAddRequest):LearningScenesConfigResponse
	"""同意用户考试申诉
		@param agreeInfo 同意信息
	"""
	agreeAppeal(agreeInfo:AgreeExamUserAppealRequest):ExamUserAppealResponse
	"""用户新增基准照，原有的基准照不变，新增一张基准照
		@param photoPath 基准照照片路径
	"""
	appendPhoto(photoPath:String):Void
	"""申请课件防作弊随机拍摄点信息
		@param applyInfo 申请信息
		@return 课件防作弊随机拍摄点
	"""
	applyCoursewareRandomFacePoints(applyInfo:ApplyCoursewareRandomPointRequest):CoursewareRandomPointResponse
	"""对比照片
		@param photo1 第一张照片Base64字符串
		@param photo2 第二张照片Base64字符串
		@return 对比结果
	"""
	comparePhoto(photo1:String,photo2:String):SimilarResultResponse
	"""尝试使用新的照片与基准照对比
		@param newPhotoBase64 新的照片Base64字符串
		@return 对比结果
	"""
	comparePhotoFromDatum(newPhotoBase64:String):SimilarResult
	"""创建基准照采集信息
		@param request 信息
	"""
	createDatumConfig(request:PlatformDatumConfigAddRequest):Void
	"""创建用户考试申诉
		@param createInfo 创建信息
	"""
	createExamAppeal(createInfo:ExamUserAppealCreateRequest):ExamUserAppealResponse
	"""创建和更新平台级别人脸识别监管配置
		@param setting 人脸识别监管配置
	"""
	createOrUpdateSupervisionConfigByPlatform(setting:FaceSupervisionSettingRequest):Void
	"""创建和更新子项目级别人脸识别监管配置
		@param setting 人脸识别监管配置
	"""
	createOrUpdateSupervisionConfigBySubProject(setting:FaceSupervisionSettingRequest):Void
	"""创建和更新单位级别人脸识别监管配置
		@param unitId  单位编号
		@param setting 人脸识别监管配置
	"""
	createOrUpdateSupervisionConfigByUnit(unitId:String,setting:FaceSupervisionSettingRequest):Void
	"""创建用户基准照
		@param photoPaths       基准照相对路径，不包含mfs
		@param allowUpdateCount 允许更新基准照次数
	"""
	createUserDatum(photoPaths:[String],allowUpdateCount:Int!):Void
	"""检测提供的照片是否有人脸
		@param photoBase64 照片的Base64字符串
		@return 是否有人脸，true/false，有/没有
	"""
	detectPhotoFace(photoBase64:String):Boolean!
	"""拒绝用户考试申诉
		@param rejectInfo 拒绝信息
	"""
	rejectAppeal(rejectInfo:RejectExamUserAppealRequest):ExamUserAppealResponse
	"""重置允许更新次数
		@param userId           用户编号
		@param allowUpdateCount 允许更新次数
	"""
	resetAllowUpdateCount(userId:String,allowUpdateCount:Int!):Void
	"""重置更新次数
		@param userId      用户编号
		@param updateCount 更新次数
	"""
	resetUpdateCount(userId:String,updateCount:Int!):Void
	"""更新人脸识别采集配置
		@param configId 配置编号
		@param update   更新信息
	"""
	updateDatumConfig(configId:String,update:DatumConfigUpdateRequest):Void
	"""更新人脸识别配置启禁用状态
		@param configId 配置编号
		@param enable   true/false,启用/禁用
	"""
	updateDatumConfigState(configId:String,enable:Boolean!):Void
	"""更新考试场景防作弊配置
		@param configUpdate 更新配置信息
	"""
	updateExamScenesConfig(configUpdate:ExamScenesConfigUpdateRequest):Void
	"""更新课程场景防作弊配置
		@param configUpdate 更新配置信息
	"""
	updateLearningScenesConfig(configUpdate:LearningScenesConfigUpdateRequest):Void
	"""更新登录场景防作弊配置
		@param configUpdate 更新配置信息
	"""
	updateLoginScenesConfig(configUpdate:LoginScenesConfigUpdateRequest):Void
	"""更新用户基准照，采用覆盖的方式
		@param photoPaths 用户基准照列表
	"""
	updatePhotoPaths(photoPaths:[String]):Void
	"""更新平台级课程场景防作弊配置
		@param configUpdate 更新配置信息
	"""
	updatePlatformLearningScenesConfig(configUpdate:PlatformLearningScenesConfigUpdateRequest):Void
	"""检测提供照片是否为活体
		@param photoBase64 照片的Base64字符串
		@return 是否活体，true/false，是/否
	"""
	verifyPhotoLiveness(photoBase64:String):Boolean!
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input FaceRecognitionShapeModel @type(value:"com.fjhb.platform.core.v1.anti.kernel.assist.anticonfig.model.FaceRecognitionShapeModel") {
	similarity:Double!
	protocolText:String
	promptText:String
	id:String
	verification:Int!
	verificationTimes:Int!
	shape:ShapeModeType
	createTime:DateTime
}
"""同意用户的考试申诉信息"""
input AgreeExamUserAppealRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.AgreeExamUserAppealRequest") {
	"""申诉ID"""
	id:String!
	"""同意的理由"""
	reason:String
}
"""防作弊形式
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
input AntiModeSettingRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.AntiModeSettingRequest") {
	"""验证方式，0/1，成功为止/验证次数"""
	verification:Int!
	"""验证次数，当verification=1时有效"""
	verificationTimes:Int!
	"""匹配相似度"""
	similarity:Double!
	"""协议文本"""
	protocolText:String
	"""提示信息文本"""
	promptText:String
	"""创建时间"""
	createTime:DateTime
}
"""申请课件防作弊随机拍摄点信息
	<AUTHOR>
"""
input ApplyCoursewareRandomPointRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.ApplyCoursewareRandomPointRequest") {
	"""防作弊配置编号"""
	configId:String
	"""防作弊方式编号"""
	modeId:String
	"""所属平台编号"""
	platformId:String
	"""所属平台版本编号"""
	platformVersionId:String
	"""所属项目编号"""
	projectId:String
	"""所属子项目编号"""
	subProjectId:String
	"""所属组织机构编号"""
	organizationId:String
	"""所属单位编号"""
	unitId:String
	"""特征标记列表，于判断是否需要反作弊请求接口的markers保存一致"""
	markers:[MarkerRequest]
	"""用户编号"""
	userId:String
}
"""用户考试申诉次数查询信息"""
input CurrentUserExamAppealCountInfoQueryRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.CurrentUserExamAppealCountInfoQueryRequest") {
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""考试场次ID"""
	examRoundId:String
}
"""用户考试申诉查询信息"""
input CurrentUserExamAppealQueryRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.CurrentUserExamAppealQueryRequest") {
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""考试场次ID"""
	examRoundId:String
}
"""基准库采集配置
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
input DatumConfigSettingRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.DatumConfigSettingRequest") {
	"""采集照片数量，默认：1"""
	collectCount:Int!
	"""更新次数"""
	updateCount:Int!
	"""采集协议文本"""
	protocolText:String
	"""是否启用"""
	enable:Boolean!
}
"""基准照采集配置更新
	<AUTHOR>
	@date 2020/6/8
	@since 1.0.0
"""
input DatumConfigUpdateRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.DatumConfigUpdateRequest") {
	"""采集照片数量，默认：1"""
	collectCount:Int
	"""更新次数"""
	updateCount:Int
	"""采集协议文本"""
	protocolText:String
}
"""考试作答记录人脸识别结果查询"""
input ExamAnswerFSResultQueryRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.ExamAnswerFSResultQueryRequest") {
	"""用户ID"""
	userId:String
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""考试场次ID"""
	examRoundId:String
}
"""用户考试答卷防作弊人脸识别记录查询"""
input ExamAnswerPagerFSRecordQueryRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.ExamAnswerPagerFSRecordQueryRequest") {
	"""用户ID"""
	userId:String!
	"""考试作答ID"""
	examAnswerId:String!
}
"""考试过程监管行为
	<AUTHOR>
	@date 2020/5/25
	@since 1.0.0
"""
input ExamProcessBehaviorRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.ExamProcessBehaviorRequest") {
	"""维度，2-时间"""
	dimensions:Int!
	"""跟踪方式，0/1，精确点/范围点"""
	traceMode:Int!
	"""跟踪记录点"""
	recordPoints:[String]
}
"""考试场景防作弊场景配置
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
input ExamScenesConfigAddRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.ExamScenesConfigAddRequest") {
	"""所属平台编号"""
	platformId:String
	"""所属平台版本编号"""
	platformVersionId:String
	"""所属项目编号"""
	projectId:String
	"""所属子项目编号"""
	subProjectId:String
	"""所属组织机构编号"""
	organizationId:String
	"""所属单位编号"""
	unitId:String
	"""应用范围，0/1/2/3，子项目/单位/组织机构/具体资源"""
	useRange:Int!
	"""<pre>
		学习方案编号，如果需要应用到学习方案，则useRange=3且填写具体学习方案编号
		</pre>
	"""
	schemeId:String
	"""<pre>
		考试场次编号，如果需要应用到考试场次，则useRange=3且则填写具体的考试场次编号
		</pre>
	"""
	examRoundId:String
	"""是否启用"""
	enable:Boolean!
	"""当前只支持人脸识别模式"""
	shapeModel:FaceRecognitionShapeRequest
	"""考试前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeExamBehavior:Int!
	"""考试中监管行为"""
	processBehavior:ExamProcessBehaviorRequest
	"""考试后监管行为，-1/0/1|不生效/每次生效/生效一次"""
	afterExamBehavior:Int!
}
"""考试防作弊配置查询
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
input ExamScenesConfigQueryRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.ExamScenesConfigQueryRequest") {
	"""应用组织机构编号"""
	organizationId:String
	"""应用单位编号"""
	unitId:String
	"""学习方案编号"""
	schemeId:String
	"""考试场次编号"""
	examRoundId:String
}
"""考试场景防作弊场景配置
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
input ExamScenesConfigUpdateRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.ExamScenesConfigUpdateRequest") {
	"""配置编号"""
	configId:String
	"""应用组织机构编号,useRange=2时有效"""
	organizationId:String
	"""应用单位编号,useRange=1时有效"""
	unitId:String
	"""学习方案编号,useRange=3时有效"""
	schemeId:String
	"""考试场次编号，useRange=3时有效"""
	examRoundId:String
	"""应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
		null表示不更新
	"""
	useRange:Int
	"""是否启用,
		null表示不修改
	"""
	enable:Boolean
	"""是否更新监管形式"""
	updateShapeModel:Boolean!
	"""监管形式，更新时需要全部更新"""
	shapeModel:FaceRecognitionShapeRequest
	"""是否更新考试前监管行为"""
	updateBeforeExamBehavior:Boolean!
	"""考试前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeExamBehavior:Int!
	"""是否更新考试中监管行为"""
	updateProcessExamBehavior:Boolean!
	"""考试中监管行为，如果updateProcessExamBehavior=true,且processBehavior=null表示移除考试中监管行为"""
	processBehavior:ExamProcessBehaviorRequest
	"""是否更新考试后监管行为"""
	updateAfterExamBehavior:Boolean!
	"""考试后监管行为，-1/0/1|不生效/每次生效/生效一次"""
	afterExamBehavior:Int!
}
"""用户考试申诉创建信息"""
input ExamUserAppealCreateRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.ExamUserAppealCreateRequest") {
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""考试场次ID"""
	examRoundId:String
	"""考试作答记录ID"""
	examAnswerId:String
	"""申诉原因"""
	reason:String
}
"""用户申诉分页查询"""
input ExamUserAppealPageQueryRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.ExamUserAppealPageQueryRequest") {
	"""学习方案ID"""
	schemeId:String
	"""用户ID集合"""
	userIds:[String]
	"""申诉创建时间起始查询"""
	startCreateTime:DateTime
	"""申诉创建时间截止查询"""
	endCreateTime:DateTime
	"""审批结果
		@see com.fjhb.platform.core.v1.anti.api.constants.UserAppealApprovedResultConst
	"""
	approvedResultList:[Int]
}
"""考试监管配置
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
input ExaminationConfigSettingRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.ExaminationConfigSettingRequest") {
	"""是否启用"""
	enable:Boolean!
	"""考试前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeExamBehavior:Int!
	"""考试中监管行为"""
	processBehavior:ProcessBehaviorSettingRequest
	"""考试后监管行为，-1/0/1|不生效/每次生效/生效一次"""
	afterExamBehavior:Int!
	"""监管形式"""
	shapeMode:AntiModeSettingRequest
	"""每场考试可申诉次数"""
	appealCount:Int!
}
"""人脸识别模式
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
input FaceRecognitionShapeRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.FaceRecognitionShapeRequest") {
	"""编号"""
	id:String
	"""验证方式，0/1，成功为止/验证次数"""
	verification:Int!
	"""验证次数，当verification=1时有效"""
	verificationTimes:Int!
	"""匹配相似度"""
	similarity:Double!
	"""协议文本"""
	protocolText:String
	"""提示信息文本"""
	promptText:String
	"""创建时间"""
	createTime:DateTime
}
"""人脸识别监管配置
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
input FaceSupervisionSettingRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.FaceSupervisionSettingRequest") {
	"""基准照采集配置"""
	datumConfigSetting:DatumConfigSettingRequest
	"""登录监管配置"""
	loginConfigSetting:LoginConfigSettingRequest
	"""学习监管配置"""
	learningConfigSetting:LearningConfigSettingRequest
	"""考试监管配置"""
	examinationConfigSetting:ExaminationConfigSettingRequest
}
"""学习监管配置
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
input LearningConfigSettingRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.LearningConfigSettingRequest") {
	"""是否启用"""
	enable:Boolean!
	"""学习前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeLearningBehavior:Int!
	"""学习中监管行为"""
	processBehavior:ProcessBehaviorSettingRequest
	"""学习后监管行为，-1/0/1|不生效/每次生效/生效一次"""
	afterLearningBehavior:Int!
	"""监管形式"""
	shapeMode:AntiModeSettingRequest
	"""如果最终验证未匹配，则本次学习资源是否有效"""
	effectiveIfNoMatch:Boolean
}
"""课程学习过程监管行为
	<AUTHOR>
	@date 2020/5/25
	@since 1.0.0
"""
input LearningProcessBehaviorRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.LearningProcessBehaviorRequest") {
	"""维度，2/3，时间/进度"""
	dimensions:Int!
	"""跟踪方式，0/1，精确点/范围点/随机点"""
	traceMode:Int!
	"""跟踪记录点"""
	recordPoints:[String]
}
"""学习场景防作弊场景配置
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
input LearningScenesConfigAddRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.LearningScenesConfigAddRequest") {
	"""所属平台编号"""
	platformId:String
	"""所属平台版本编号"""
	platformVersionId:String
	"""所属项目编号"""
	projectId:String
	"""所属子项目编号"""
	subProjectId:String
	"""所属组织机构编号"""
	organizationId:String
	"""所属单位编号"""
	unitId:String
	"""应用范围，0/1/2/3，子项目/单位/组织机构/具体资源"""
	useRange:Int!
	"""<pre>
		学习方案编号，如果需要应用到学习方案，则useRange=3且填写具体学习方案编号
		</pre>
	"""
	schemeId:String
	"""<pre>
		课程编号，如果需要应用到课程，则useRange=3且则填写具体的课程编号
		</pre>
	"""
	courseId:String
	"""是否启用"""
	enable:Boolean!
	"""当前只支持人脸识别模式"""
	shapeModel:FaceRecognitionShapeRequest
	"""学习前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeLearningBehavior:Int!
	"""学习中监管行为"""
	processLearningBehavior:LearningProcessBehaviorRequest
	"""学习后监管行为，-1/0/1|不生效/每次生效/生效一次"""
	afterLearningBehavior:Int!
	"""如果最终验证未匹配，则本次学习资源是否有效"""
	effectiveIfNoMatch:Boolean
}
"""课程学习防作弊配置查询
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
input LearningScenesConfigQueryRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.LearningScenesConfigQueryRequest") {
	"""应用组织机构编号"""
	organizationId:String
	"""应用单位编号"""
	unitId:String
	"""学习方案编号"""
	schemeId:String
	"""课程编号"""
	courseId:String
}
"""学习场景防作弊场景配置
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
input LearningScenesConfigUpdateRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.LearningScenesConfigUpdateRequest") {
	"""配置编号"""
	configId:String
	"""应用组织机构编号,useRange=2时有效"""
	organizationId:String
	"""应用单位编号,useRange=1时有效"""
	unitId:String
	"""学习方案编号,useRange=3时有效"""
	schemeId:String
	"""课程编号，useRange=3时有效"""
	courseId:String
	"""应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
		null表示不更新
	"""
	useRange:Int
	"""是否启用,
		null表示不修改
	"""
	enable:Boolean
	"""是否更新监管形式"""
	updateShapeModel:Boolean!
	"""监管形式，更新时需要全部更新"""
	shapeModel:FaceRecognitionShapeRequest
	"""是否更新学习前监管行为"""
	updateBeforeLearningBehavior:Boolean!
	"""学习前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeLearningBehavior:Int!
	"""是否更新学习中监管行为"""
	updateProcessLearningBehavior:Boolean!
	"""学习中监管行为，如果updateProcessLearningBehavior=true,且processLearningBehavior=null表示移除学习中监管行为"""
	processLearningBehavior:LearningProcessBehaviorRequest
	"""是否更新学习后监管行为"""
	updateAfterLearningBehavior:Boolean!
	"""学习后监管行为，-1/0/1|不生效/每次生效/生效一次"""
	afterLearningBehavior:Int!
	"""如果最终验证未匹配，则本次学习资源是否有效"""
	effectiveIfNoMatch:Boolean
}
"""登录配置
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
input LoginConfigSettingRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.LoginConfigSettingRequest") {
	"""是否启用"""
	enable:Boolean!
	"""登录前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeLoginBehavior:Int!
	"""监管形式"""
	shapeMode:AntiModeSettingRequest
}
"""用户登录防作弊人脸识别记录查询"""
input LoginFSRecordQueryRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.LoginFSRecordQueryRequest") {
	"""用户ID"""
	userId:String
}
"""登录场景防作弊场景配置
	<AUTHOR>
	@date 2020/5/28
	@since 1.0.0
"""
input LoginScenesConfigAddRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.LoginScenesConfigAddRequest") {
	"""所属平台编号"""
	platformId:String
	"""所属平台版本编号"""
	platformVersionId:String
	"""所属项目编号"""
	projectId:String
	"""所属子项目编号"""
	subProjectId:String
	"""所属组织机构编号"""
	organizationId:String
	"""所属单位编号"""
	unitId:String
	"""应用范围，0/1/2/3，子项目/单位/组织机构/具体资源"""
	useRange:Int!
	"""是否启用"""
	enable:Boolean!
	"""当前只支持人脸识别模式"""
	shapeModel:FaceRecognitionShapeModel
	"""登录前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeLoginBehavior:Int!
}
"""登录防作弊配置查询
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
input LoginScenesConfigQueryRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.LoginScenesConfigQueryRequest") {
	"""应用组织机构编号"""
	organizationId:String
	"""应用单位编号"""
	unitId:String
}
"""登录场景防作弊场景配置
	<AUTHOR>
	@date 2020/5/28
	@since 1.0.0
"""
input LoginScenesConfigUpdateRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.LoginScenesConfigUpdateRequest") {
	"""配置编号"""
	configId:String
	"""应用组织机构编号,useRange=2时有效"""
	organizationId:String
	"""应用单位编号,useRange=1时有效"""
	unitId:String
	"""应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
		null表示不更新
	"""
	useRange:Int
	"""是否启用,
		null表示不修改
	"""
	enable:Boolean
	"""是否更新监管形式"""
	updateShapeModel:Boolean!
	"""监管形式，更新时需要全部更新"""
	shapeModel:FaceRecognitionShapeModel
	"""是否更新学习前监管行为"""
	updateBeforeLoginBehavior:Boolean!
	"""学习前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeLoginBehavior:Int!
}
"""特征标记
	<AUTHOR>
"""
input MarkerRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.MarkerRequest") {
	key:String
	value:String
}
"""基准照采集配置添加
	<AUTHOR>
	@date 2020/6/8
	@since 1.0.0
"""
input PlatformDatumConfigAddRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.PlatformDatumConfigAddRequest") {
	"""采集照片数量，默认：1"""
	collectCount:Int!
	"""更新次数"""
	updateCount:Int!
	"""采集协议文本"""
	protocolText:String
	"""是否启用"""
	enable:Boolean!
}
"""平台级别学习场景防作弊场景配置
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
input PlatformLearningScenesConfigAddRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.PlatformLearningScenesConfigAddRequest") {
	"""是否启用"""
	enable:Boolean!
	"""当前只支持人脸识别模式"""
	shapeModel:FaceRecognitionShapeRequest
	"""学习前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeLearningBehavior:Int!
	"""学习中监管行为"""
	processLearningBehavior:LearningProcessBehaviorRequest
	"""学习后监管行为，-1/0/1|不生效/每次生效/生效一次"""
	afterLearningBehavior:Int!
	"""如果最终验证未匹配，则本次学习资源是否有效"""
	effectiveIfNoMatch:Boolean
}
"""学习场景防作弊场景配置
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
input PlatformLearningScenesConfigUpdateRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.PlatformLearningScenesConfigUpdateRequest") {
	"""配置编号"""
	configId:String
	"""是否启用,
		null表示不修改
	"""
	enable:Boolean
	"""是否更新监管形式"""
	updateShapeModel:Boolean!
	"""监管形式，更新时需要全部更新"""
	shapeModel:FaceRecognitionShapeRequest
	"""是否更新学习前监管行为"""
	updateBeforeLearningBehavior:Boolean!
	"""学习前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeLearningBehavior:Int!
	"""是否更新学习中监管行为"""
	updateProcessLearningBehavior:Boolean!
	"""学习中监管行为，如果updateProcessLearningBehavior=true,且processLearningBehavior=null表示移除学习中监管行为"""
	processLearningBehavior:LearningProcessBehaviorRequest
	"""是否更新学习后监管行为"""
	updateAfterLearningBehavior:Boolean!
	"""学习后监管行为，-1/0/1|不生效/每次生效/生效一次"""
	afterLearningBehavior:Int!
	"""如果最终验证未匹配，则本次学习资源是否有效"""
	effectiveIfNoMatch:Boolean
}
"""过程监管行为配置
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
input ProcessBehaviorSettingRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.ProcessBehaviorSettingRequest") {
	"""维度，2/3，时间/进度"""
	dimensions:Int!
	"""跟踪方式，0/1，精确点/范围点"""
	traceMode:Int!
	"""跟踪记录点"""
	recordPoints:[String]
}
"""拒绝用户的考试申诉信息"""
input RejectExamUserAppealRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.RejectExamUserAppealRequest") {
	"""申诉ID"""
	id:String!
	"""拒绝的理由"""
	reason:String
}
"""用户指定学习方案下课程人脸识别记录查询信息"""
input SchemeCourseFSRecordsQueryRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.SchemeCourseFSRecordsQueryRequest") {
	"""用户ID"""
	userId:String
	"""学习方案ID"""
	schemeId:String
}
"""用户指定学习方案下课件人脸识别记录查询信息"""
input SchemeCoursewareFSRecordsQueryRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.SchemeCoursewareFSRecordsQueryRequest") {
	"""用户ID"""
	userId:String
	"""学习方案ID"""
	schemeId:String
}
"""用户指定学习方案下考试人脸识别记录查询信息"""
input SchemeExamFSRecordQueryRequest @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.SchemeExamFSRecordQueryRequest") {
	"""用户ID"""
	userId:String
	"""学习方案ID"""
	schemeId:String
	"""跟踪维度列表，-1/0/1/2/3/4，全部/进入场景/退出场景/时间/进度/具体项目"""
	dimensionsList:[Int]
}
type SimilarResult @type(value:"com.fjhb.platform.core.v1.anti.kernel.appservice.result.SimilarResult") {
	similar:Double!
	result:Boolean!
	errorInfo:String
}
enum ShapeModeType @type(value:"com.fjhb.platform.core.v1.anti.kernel.assist.anticonfig.ShapeModeType") {
	KnowledgePoint
	RandomQuestions
	FaceRecognition
}
type FaceRecognitionShapeModel1 @type(value:"com.fjhb.platform.core.v1.anti.kernel.assist.anticonfig.model.FaceRecognitionShapeModel") {
	similarity:Double!
	protocolText:String
	promptText:String
	id:String
	verification:Int!
	verificationTimes:Int!
	shape:ShapeModeType
	createTime:DateTime
}
"""考试过程监管行为
	<AUTHOR>
	@date 2020/5/25
	@since 1.0.0
"""
type ExamProcessBehaviorRequest1 @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.ExamProcessBehaviorRequest") {
	"""维度，2-时间"""
	dimensions:Int!
	"""跟踪方式，0/1，精确点/范围点"""
	traceMode:Int!
	"""跟踪记录点"""
	recordPoints:[String]
}
"""人脸识别模式
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
type FaceRecognitionShapeRequest1 @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.FaceRecognitionShapeRequest") {
	"""编号"""
	id:String
	"""验证方式，0/1，成功为止/验证次数"""
	verification:Int!
	"""验证次数，当verification=1时有效"""
	verificationTimes:Int!
	"""匹配相似度"""
	similarity:Double!
	"""协议文本"""
	protocolText:String
	"""提示信息文本"""
	promptText:String
	"""创建时间"""
	createTime:DateTime
}
"""课程学习过程监管行为
	<AUTHOR>
	@date 2020/5/25
	@since 1.0.0
"""
type LearningProcessBehaviorRequest1 @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.request.LearningProcessBehaviorRequest") {
	"""维度，2/3，时间/进度"""
	dimensions:Int!
	"""跟踪方式，0/1，精确点/范围点/随机点"""
	traceMode:Int!
	"""跟踪记录点"""
	recordPoints:[String]
}
"""防作弊形式
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
type AntiModeSettingResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.AntiModeSettingResponse") {
	"""验证方式，0/1，成功为止/验证次数"""
	verification:Int!
	"""验证次数，当verification=1时有效"""
	verificationTimes:Int!
	"""匹配相似度"""
	similarity:Double!
	"""协议文本"""
	protocolText:String
	"""提示信息文本"""
	promptText:String
	"""创建时间"""
	createTime:DateTime
}
"""用户课程防作弊人脸识别记录信息"""
type CourseFSInfoResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.CourseFSInfoResponse") {
	"""用户ID"""
	userId:String
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""课程包ID"""
	packageId:String
	"""课程ID"""
	courseId:String
	"""课程的人脸识别记录"""
	records:[CourseFSRecordResponse]
}
"""课程防作弊人脸识别记录信息"""
type CourseFSRecordResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.CourseFSRecordResponse") {
	"""记录ID"""
	id:String
	"""用户ID"""
	userId:String
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""课程包ID"""
	packageId:String
	"""课程ID"""
	courseId:String
	"""跟踪维度，0/1/2/3/4，进入场景/退出场景/时间/进度/具体项目"""
	dimensions:Int!
	"""跟踪记录点，当dimensions=0/1时，当前无值
		当dimensions=2时，单位：秒
		当dimensions=3时，单位：百分比
		当dimensions=4时，具体编号
	"""
	recordPoint:String
	"""基准照片路径"""
	standardAnswer:String
	"""单次拍照路径"""
	currentAnswer:String
	"""达标对比值，匹配相似度"""
	standardValue:String
	"""当前对比值，当前相似度"""
	currentValue:String
	"""记录点对应的时间点，单位：秒，如：学习对应时长，考试对应当前考试时间"""
	timeLength:Long!
	"""记录点对应的进度百分比，如：学习对应进度，考试对应的考试进度"""
	schedule:Double!
	"""验证结果"""
	result:Boolean!
	"""创建时间"""
	createTime:DateTime
	"""更新时间"""
	updateTime:DateTime
}
"""用户课件防作弊人脸识别记录信息"""
type CoursewareFSInfoResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.CoursewareFSInfoResponse") {
	"""用户ID"""
	userId:String
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""课程包ID"""
	packageId:String
	"""课程ID"""
	courseId:String
	"""课件ID"""
	coursewareId:String
	"""课件的人脸识别记录"""
	records:[CoursewareFSRecordResponse]
}
"""课件防作弊人脸识别记录信息"""
type CoursewareFSRecordResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.CoursewareFSRecordResponse") {
	"""记录ID"""
	id:String
	"""用户ID"""
	userId:String
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""课程包ID"""
	packageId:String
	"""课程ID"""
	courseId:String
	"""课件ID"""
	coursewareId:String
	"""跟踪维度，0/1/2/3/4，进入场景/退出场景/时间/进度/具体项目"""
	dimensions:Int!
	"""跟踪记录点，当dimensions=0/1时，当前无值
		当dimensions=2时，单位：秒
		当dimensions=3时，单位：百分比
		当dimensions=4时，具体编号
	"""
	recordPoint:String
	"""基准照片路径"""
	standardAnswer:String
	"""单次拍照路径"""
	currentAnswer:String
	"""达标对比值，匹配相似度"""
	standardValue:String
	"""当前对比值，当前相似度"""
	currentValue:String
	"""记录点对应的时间点，单位：秒，如：学习对应时长，考试对应当前考试时间"""
	timeLength:Long!
	"""记录点对应的进度百分比，如：学习对应进度，考试对应的考试进度"""
	schedule:Double!
	"""验证结果"""
	result:Boolean!
	"""创建时间"""
	createTime:DateTime
	"""更新时间"""
	updateTime:DateTime
}
"""课件防作弊随机拍摄点信息"""
type CoursewareRandomPointResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.CoursewareRandomPointResponse") {
	"""拍摄点，单位百分比进度"""
	recordPoints:[Int]
}
"""采集基准照配置
	<AUTHOR>
	@date 2020/6/8
	@since 1.0.0
"""
type DatumConfigResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.DatumConfigResponse") {
	"""基准照片采集配置编号"""
	id:String
	"""所属平台编号"""
	platformId:String
	"""所属平台版本编号"""
	platformVersionId:String
	"""所属项目编号"""
	projectId:String
	"""所属子项目编号"""
	subProjectId:String
	"""所属组织机构编号"""
	organizationId:String
	"""所属单位编号"""
	unitId:String
	"""采集照片数量，默认：1"""
	collectCount:Int!
	"""更新次数"""
	updateCount:Int!
	"""采集协议文本"""
	protocolText:String
	"""创建人"""
	createUserId:String
	"""最后更新人"""
	updateUserId:String
	"""最后更新时间"""
	updateTime:DateTime
	"""创建时间"""
	createTime:DateTime
	"""是否启用"""
	enable:Boolean!
}
"""基准库采集配置
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
type DatumConfigSettingResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.DatumConfigSettingResponse") {
	"""采集照片数量，默认：1"""
	collectCount:Int!
	"""更新次数"""
	updateCount:Int!
	"""采集协议文本"""
	protocolText:String
	"""是否启用"""
	enable:Boolean!
	"""配置id"""
	id:String
}
"""考试作答记录人脸识别结果"""
type ExamAnswerFSResultResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.ExamAnswerFSResultResponse") {
	"""用户ID"""
	userId:String
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""考试场次ID"""
	examRoundId:String
	"""考试作答记录ID"""
	examAnswerId:String
	"""是否通过"""
	passed:Boolean!
	"""人脸拍摄总点数"""
	totalPointCount:Int!
	"""通过的人脸拍摄总点数"""
	passedPointCount:Int!
}
"""用户考试防作弊人脸识别记录信息"""
type ExamFSInfoResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.ExamFSInfoResponse") {
	"""用户ID"""
	userId:String
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""考试场次ID"""
	examRoundId:String
	"""考试场次的人脸识别记录"""
	records:[ExamFSRecordResponse]
	"""用户答卷ID"""
	examAnswerId:String
}
"""考试防作弊人脸识别记录信息"""
type ExamFSRecordResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.ExamFSRecordResponse") {
	"""记录ID"""
	id:String
	"""用户ID"""
	userId:String
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""考试场次ID"""
	examRoundId:String
	"""用户答卷ID"""
	examAnswerId:String
	"""跟踪维度，0/1/2/3/4，进入场景/退出场景/时间/进度/具体项目"""
	dimensions:Int!
	"""跟踪记录点，当dimensions=0/1时，当前无值
		当dimensions=2时，单位：秒
		当dimensions=3时，单位：百分比
		当dimensions=4时，具体编号
	"""
	recordPoint:String
	"""基准照片路径"""
	standardAnswer:String
	"""单次拍照路径"""
	currentAnswer:String
	"""达标对比值，匹配相似度"""
	standardValue:String
	"""当前对比值，当前相似度"""
	currentValue:String
	"""记录点对应的时间点，单位：秒，如：学习对应时长，考试对应当前考试时间"""
	timeLength:Long!
	"""记录点对应的进度百分比，如：学习对应进度，考试对应的考试进度"""
	schedule:Double!
	"""验证结果"""
	result:Boolean!
	"""创建时间"""
	createTime:DateTime
	"""更新时间"""
	updateTime:DateTime
}
"""考试场景防作弊配置
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
type ExamScenesConfigResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.ExamScenesConfigResponse") {
	"""防作弊配置编号"""
	id:String
	"""所属平台编号"""
	platformId:String
	"""所属平台版本编号"""
	platformVersionId:String
	"""所属项目编号"""
	projectId:String
	"""所属子项目编号"""
	subProjectId:String
	"""所属组织机构编号"""
	organizationId:String
	"""所属单位编号"""
	unitId:String
	"""<pre>
		考试方案编号，如果需要应用到考试方案，则useRange=3且填写具体考试方案编号
		</pre>
	"""
	schemeId:String
	"""<pre>
		考试场次编号，如果需要应用到考试场次，则useRange=3且则填写具体的考试场次编号
		</pre>
	"""
	examRoundId:String
	"""应用场景,0/1/2,考试/考试/登录"""
	usageScenarios:Int!
	"""应用范围，0/1/2/3，子项目/单位/组织机构/具体资源"""
	useRange:Int!
	"""是否启用"""
	enable:Boolean!
	"""当前只支持人脸识别模式"""
	shapeModel:FaceRecognitionShapeRequest1
	"""考试前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeExamBehavior:Int!
	"""考试中监管行为"""
	processExamBehavior:ExamProcessBehaviorRequest1
	"""考试后监管行为，-1/0/1|不生效/每次生效/生效一次"""
	afterExamBehavior:Int!
	"""配置时间"""
	createTime:DateTime
	"""创建人编号"""
	createUserId:String
	"""最后更新人"""
	updateUserId:String
	"""最后更新时间"""
	updateTime:DateTime
}
"""用户考试申诉次数信息"""
type ExamUserAppealCountInfoResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.ExamUserAppealCountInfoResponse") {
	"""允许申诉次数"""
	allowAppealCount:Int!
	"""已申诉次数"""
	appealedCount:Int!
}
"""用户考试申诉信息"""
type ExamUserAppealResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.ExamUserAppealResponse") {
	"""申诉ID"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""用户ID"""
	userId:String
	"""学习方案ID"""
	schemeId:String
	"""学习方式ID"""
	learningId:String
	"""考试场次ID"""
	examRoundId:String
	"""考试作答记录ID"""
	examAnswerId:String
	"""申诉原因"""
	reason:String
	"""申诉审批结果
		@see UserAppealApprovedResultConst
	"""
	approvedResult:Int!
	"""申诉审批结果备注"""
	approvedRemark:String
	"""审批时间"""
	approvedTime:DateTime
	"""审批人ID"""
	approvedUserId:String
	"""申诉创建时间"""
	createTime:DateTime
	"""创建人ID"""
	createUserId:String
}
"""考试监管配置
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
type ExaminationConfigSettingResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.ExaminationConfigSettingResponse") {
	"""是否启用"""
	enable:Boolean!
	"""考试前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeExamBehavior:Int!
	"""考试中监管行为"""
	processBehavior:ProcessBehaviorSettingResponse
	"""考试后监管行为，-1/0/1|不生效/每次生效/生效一次"""
	afterExamBehavior:Int!
	"""监管形式"""
	shapeMode:AntiModeSettingResponse
	"""每场考试可申诉次数"""
	appealCount:Int!
}
"""人脸识别监管配置
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
type FaceSupervisionSettingResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.FaceSupervisionSettingResponse") {
	"""基准照采集配置"""
	datumConfigSetting:DatumConfigSettingResponse
	"""登录监管配置"""
	loginConfigSetting:LoginConfigSettingResponse
	"""学习监管配置"""
	learningConfigSetting:LearningConfigSettingResponse
	"""考试监管配置"""
	examinationConfigSetting:ExaminationConfigSettingResponse
}
"""学习监管配置
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
type LearningConfigSettingResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.LearningConfigSettingResponse") {
	"""是否启用"""
	enable:Boolean!
	"""学习前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeLearningBehavior:Int!
	"""学习中监管行为"""
	processBehavior:ProcessBehaviorSettingResponse
	"""学习后监管行为，-1/0/1|不生效/每次生效/生效一次"""
	afterLearningBehavior:Int!
	"""监管形式"""
	shapeMode:AntiModeSettingResponse
	"""如果最终验证未匹配，则本次学习资源是否有效"""
	effectiveIfNoMatch:Boolean
}
"""课程学习场景防作弊配置
	<AUTHOR>
	@date 2020/5/27
	@since 1.0.0
"""
type LearningScenesConfigResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.LearningScenesConfigResponse") {
	"""防作弊配置编号"""
	id:String
	"""所属平台编号"""
	platformId:String
	"""所属平台版本编号"""
	platformVersionId:String
	"""所属项目编号"""
	projectId:String
	"""所属子项目编号"""
	subProjectId:String
	"""所属组织机构编号"""
	organizationId:String
	"""所属单位编号"""
	unitId:String
	"""<pre>
		学习方案编号，如果需要应用到学习方案，则useRange=3且填写具体学习方案编号
		</pre>
	"""
	schemeId:String
	"""<pre>
		课程编号，如果需要应用到课程，则useRange=3且则填写具体的课程编号
		</pre>
	"""
	courseId:String
	"""应用场景,0/1/2,学习/考试/登录"""
	usageScenarios:Int!
	"""应用范围，0/1/2/3/4，子项目/单位/组织机构/具体资源/平台"""
	useRange:Int!
	"""是否启用"""
	enable:Boolean!
	"""当前只支持人脸识别模式"""
	shapeModel:FaceRecognitionShapeRequest1
	"""学习前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeLearningBehavior:Int!
	"""学习中监管行为"""
	processLearningBehavior:LearningProcessBehaviorRequest1
	"""学习后监管行为，-1/0/1|不生效/每次生效/生效一次"""
	afterLearningBehavior:Int!
	"""如果最终验证未匹配，则本次学习资源是否有效"""
	effectiveIfNoMatch:Boolean
	"""配置时间"""
	createTime:DateTime
	"""创建人编号"""
	createUserId:String
	"""最后更新人"""
	updateUserId:String
	"""最后更新时间"""
	updateTime:DateTime
}
"""登录配置
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
type LoginConfigSettingResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.LoginConfigSettingResponse") {
	"""是否启用"""
	enable:Boolean!
	"""登录前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeLoginBehavior:Int!
	"""监管形式"""
	shapeMode:AntiModeSettingResponse
}
"""用户登录防作弊记录"""
type LoginFSRecordResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.LoginFSRecordResponse") {
	"""记录ID"""
	id:String
	"""用户ID"""
	userId:String
	"""跟踪维度，0/1/2/3/4，进入场景/退出场景/时间/进度/具体项目"""
	dimensions:Int!
	"""跟踪记录点，当dimensions=0/1时，当前无值
		当dimensions=2时，单位：秒
		当dimensions=3时，单位：百分比
		当dimensions=4时，具体编号
	"""
	recordPoint:String
	"""基准照片路径"""
	standardAnswer:String
	"""单次拍照路径"""
	currentAnswer:String
	"""达标对比值，匹配相似度"""
	standardValue:String
	"""当前对比值，当前相似度"""
	currentValue:String
	"""记录点对应的时间点，单位：秒，如：学习对应时长，考试对应当前考试时间"""
	timeLength:Long!
	"""记录点对应的进度百分比，如：学习对应进度，考试对应的考试进度"""
	schedule:Double!
	"""验证结果"""
	result:Boolean!
	"""创建时间"""
	createTime:DateTime
}
"""登录场景防作弊配置
	<AUTHOR>
	@date 2020/5/28
	@since 1.0.0
"""
type LoginScenesConfigResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.LoginScenesConfigResponse") {
	"""防作弊配置编号"""
	id:String
	"""所属平台编号"""
	platformId:String
	"""所属平台版本编号"""
	platformVersionId:String
	"""所属项目编号"""
	projectId:String
	"""所属子项目编号"""
	subProjectId:String
	"""所属组织机构编号"""
	organizationId:String
	"""所属单位编号"""
	unitId:String
	"""应用场景,0/1/2,学习/考试/登录"""
	usageScenarios:Int!
	"""应用范围，0/1/2/3，子项目/单位/组织机构/具体资源"""
	useRange:Int!
	"""是否启用"""
	enable:Boolean!
	"""当前只支持人脸识别模式"""
	shapeModel:FaceRecognitionShapeModel1
	"""学习前监管行为，-1/0/1|不生效/每次生效/生效一次"""
	beforeLoginBehavior:Int!
	"""配置时间"""
	createTime:DateTime
	"""创建人编号"""
	createUserId:String
	"""最后更新人"""
	updateUserId:String
	"""最后更新时间"""
	updateTime:DateTime
}
"""采集基准照配置
	<AUTHOR>
	@date 2020/6/8
	@since 1.0.0
"""
type PlatformDatumConfigResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.PlatformDatumConfigResponse") {
	"""基准照片采集配置编号"""
	id:String
	"""采集照片数量，默认：1"""
	collectCount:Int!
	"""更新次数"""
	updateCount:Int!
	"""采集协议文本"""
	protocolText:String
	"""创建人"""
	createUserId:String
	"""最后更新人"""
	updateUserId:String
	"""最后更新时间"""
	updateTime:DateTime
	"""创建时间"""
	createTime:DateTime
	"""是否启用"""
	enable:Boolean!
}
"""过程监管行为配置
	<AUTHOR>
	@date 2020/7/23
	@since 1.0.0
"""
type ProcessBehaviorSettingResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.ProcessBehaviorSettingResponse") {
	"""维度，2/3，时间/进度"""
	dimensions:Int!
	"""跟踪方式，0/1，精确点/范围点/随机点"""
	traceMode:Int!
	"""跟踪记录点"""
	recordPoints:[String]
}
"""对比结果信息
	<AUTHOR>
	@since 2020/06/05
"""
type SimilarResultResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.SimilarResultResponse") {
	"""相似度"""
	similar:Double!
	"""对比结果，true/false,成功/失败"""
	result:Boolean!
	"""失败信息"""
	errorInfo:String
}
"""eleven"""
type UserDatumPhotoChangeRecordResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.UserDatumPhotoChangeRecordResponse") {
	"""基准照id"""
	id:String
	"""原始基准照信息"""
	original:UserDatumResponse
	"""更新后基准照信息"""
	target:UserDatumResponse
	"""操作人编号"""
	operatorId:String
	"""更新时间"""
	updateTime:DateTime
}
"""用户基准照信息
	<AUTHOR>
	@date 2020/5/25
	@since 1.0.0
"""
type UserDatumResponse @type(value:"com.fjhb.platform.core.v1.anti.kernel.gateway.graphql.response.UserDatumResponse") {
	"""用户编号"""
	userId:String
	"""基准照相对路径，不包含msf"""
	photoPaths:[String]
	"""允许更新次数"""
	allowUpdateCount:Int!
	"""已更新次数"""
	updateCount:Int!
	"""最后更新时间"""
	lastUpdateTime:DateTime
}

scalar List
type CourseFSInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseFSInfoResponse]}
type CoursewareFSInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CoursewareFSInfoResponse]}
type ExamFSInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ExamFSInfoResponse]}
type ExamUserAppealResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ExamUserAppealResponse]}
type LoginFSRecordResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [LoginFSRecordResponse]}
