<template>
  <el-main>
    <!-- <div class="f-p15" style="padding-bottom:0"> -->
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
      <p>温馨提示：</p>
      <p>
        1.导入开通，将由系统以证件号（登录帐号）为依据，对未存在于平台中的学员自动生成帐号，对于已存在于平台内的学员,可选择是否变更密码以及除证件号外的其他的信息。
      </p>
      <p>
        2.填写的信息，请按下载的模板填写要求，严格填写。导入表格一次最多支持1000条记录，若超过1000条则不能正常导入。
      </p>
      <p>
        3.批量创建失败的原因。请查阅“导入任务查阅”并下载失败数据！你可以下载未导入成功的信息表，修改正确后再试。你仅需再次上传出错的记录即可。
      </p>
      <p>
        4.如网校开启工作单位配置天眼查/企查查，通过第三方没有查询到对应的单位无法录入系统，如需跳过查询环节，请在对应的单位字段前添加*，例如：*单位名称。
      </p>
    </el-alert>
    <!-- </div> -->
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-row type="flex" justify="center">
          <el-col :sm="14" :lg="10">
            <el-steps direction="vertical" :active="4" class="m-vertical-steps">
              <el-step title="下载批量创建学员模版，填写要求信息">
                <div slot="description">
                  <template v-if="$hasPermission('download')" desc="下载模板" actions="@downloadTemplate">
                    <el-button
                      type="primary"
                      size="small"
                      plain
                      class="f-mt5"
                      icon="el-icon-download"
                      @click="downloadTemplate"
                    >
                      下载模板
                    </el-button>
                  </template>
                </div>
              </el-step>
              <el-step title="上传填写好的创建学员表格">
                <div slot="description">
                  <template v-if="$hasPermission('uploadComponent')" desc="选择文件" actions="@MinUploadFile">
                    <min-upload-file v-model="hbFileUploadResponse" :file-type="1" :is-protected="true">
                      <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                        选择文件
                      </el-button>
                    </min-upload-file>
                  </template>
                </div>
              </el-step>
              <el-step>
                <!-- <div slot="title">
                  默认密码：
                  <el-radio-group v-model="radio" class="default-psw">
                    <el-radio :label="1">000000</el-radio>
                    <el-radio :label="2">abc123</el-radio>
                    <el-radio :label="4" v-if="isShowCardIdPassWord">身份证后六位</el-radio>
                    <el-radio :label="3">
                      <span class="f-mr10">自定义密码</span>
                      <el-input v-model="password" v-if="radio === 3" clearable placeholder="请输入自定义密码" />
                    </el-radio>
                  </el-radio-group>
                </div> -->
                <div slot="title">
                  设置默认密码：
                  <div class="f-flex">
                    <span class="m-radio-group-title">默认密码：</span>
                    <el-radio-group v-model="radio" class="default-psw">
                      <el-radio :label="1">000000</el-radio>
                      <el-radio :label="2">abc123</el-radio>
                      <el-radio :label="3">证件号后六位</el-radio>
                      <el-radio :label="4">
                        <span class="f-mr10">自定义密码</span>
                        <!--选中后出现输入框-->
                        <el-input v-model="password" v-if="radio === 4" clearable placeholder="请输入自定义密码" />
                      </el-radio>
                    </el-radio-group>
                  </div>
                  <div class="f-flex f-mt20">
                    <span class="m-radio-group-title">密码生效范围：</span>
                    <el-radio-group v-model="passwordEffectiveRange" class="default-psw">
                      <el-radio :label="5">仅新用户</el-radio>
                      <el-radio :label="6">全部用户（含已注册）</el-radio>
                    </el-radio-group>
                  </div>
                </div>
              </el-step>
              <el-step>
                <div slot="title">
                  已注册学员更新基础信息：
                  <el-radio-group v-model="updateBasicInformation" class="default-psw">
                    <el-radio :label="7">是</el-radio>
                    <el-radio :label="8">否</el-radio>
                  </el-radio-group>
                </div>
              </el-step>
            </el-steps>
          </el-col>
        </el-row>
      </el-card>
    </div>
    <div class="m-btn-bar f-tc">
      <template v-if="$hasPermission('upload')" desc="上传文件" actions="importStudent">
        <el-button type="primary" @click="importStudent" v-loading="loading">导入</el-button>
      </template>
    </div>
    <el-dialog title="提示" :visible.sync="importSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导入成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导入任务查看-导入学员</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="importSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goImportDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import AsyncTaskModule from '@api/service/management/async-task/AsyncTaskModule'
  import MinUploadFile from '@hbfe/jxjy-admin-platform/src/function/components/min-upload-file.vue'
  import { bind, debounce } from 'lodash-decorators'
  import { isAxServicerId } from '@hbfe/jxjy-admin-common/src/util/differentiateServicerId'
  import axjxx from '@api/service/common/diffSchool/axjxx/index'
  @Component({
    components: { MinUploadFile }
  })
  export default class extends Vue {
    radio = 0
    passwordEffectiveRange = 100
    updateBasicInformation = 200
    //文件上传之后的回调参数
    hbFileUploadResponse = new HBFileUploadResponse()
    // 导入学员实例
    mutationImportUserVo = AsyncTaskModule.mutationAsyncTaskFactory.mutationImportUser
    // 自定义密码
    password = ''
    // 导入弹窗显隐
    importSuccessVisible = false
    loading = false
    isShowCardIdPassWord = axjxx.isShowCardIdPassWord

    //下载模板
    @bind
    @debounce(1000)
    downloadTemplate() {
      const link = document.createElement('a')
      const resolver = isAxServicerId()
        ? this.$router.resolve({
            name:
              '/mfs/ms-file/public/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/axjxx/导入学员模板.xlsx'
          })
        : this.$router.resolve({
            name:
              '/mfs/ms-file/public/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/导入学员模板.xlsx'
          })

      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.')
      const typeName = urlArr.pop()
      link.setAttribute('download', '导入学员模板')
      document.body.appendChild(link)
      link.click()
      link.remove()
    }

    //导入学员
    @bind
    @debounce(1000)
    async importStudent() {
      if (this.hbFileUploadResponse.url) {
        // 导入前校验-默认密码
        if (this.radio === 0) {
          return this.$confirm('请选择默认密码。', '系统提醒', {
            confirmButtonText: '我知道了',
            showCancelButton: false,
            showClose: false,
            center: true
          }).then(() => {
            this.loading = false
          })
        }
        // 选择默认密码后
        if (this.radio === 1) {
          this.mutationImportUserVo.importParams.password = '000000'
          this.mutationImportUserVo.importParams.defaultPasswordType = 1
        } else if (this.radio === 2) {
          this.mutationImportUserVo.importParams.password = 'abc123'
          this.mutationImportUserVo.importParams.defaultPasswordType = 1
        } else if (this.radio === 4) {
          if (this.password) {
            this.mutationImportUserVo.importParams.password = this.password
            this.mutationImportUserVo.importParams.defaultPasswordType = 1
          } else {
            return this.$message.warning('请填写自定义密码！')
          }
        } else {
          this.mutationImportUserVo.importParams.password = ''
          this.mutationImportUserVo.importParams.defaultPasswordType = 2
        }
        // 导入前校验-密码生效范围
        if (this.passwordEffectiveRange === 100) {
          return this.$confirm('请选择密码生效范围。', '系统提醒', {
            confirmButtonText: '我知道了',
            showCancelButton: false,
            showClose: false,
            center: true
          }).then(() => {
            this.loading = false
          })
        }

        // 导入前校验-密码生效范围
        if (this.updateBasicInformation === 200) {
          return this.$confirm('请选择是否更新已注册学员基础信息。', '系统提醒', {
            confirmButtonText: '我知道了',
            showCancelButton: false,
            showClose: false,
            center: true
          }).then(() => {
            this.loading = false
          })
        }
        // 选择密码生效范围后
        if (this.passwordEffectiveRange === 5) {
          this.mutationImportUserVo.importParams.passwordEffectiveRange = 1
        } else {
          this.mutationImportUserVo.importParams.passwordEffectiveRange = 2
        }

        if (this.updateBasicInformation === 7) {
          this.mutationImportUserVo.importParams.onlyCreate = false
        } else {
          this.mutationImportUserVo.importParams.onlyCreate = true
        }

        this.loading = true
        this.mutationImportUserVo.importParams.filePath = this.hbFileUploadResponse.url
        this.mutationImportUserVo.importParams.fileName = this.hbFileUploadResponse.fileName

        const res = await this.mutationImportUserVo.importStudent()
        if (res.status?.isSuccess()) {
          this.$message.success('操作成功！')
          this.hbFileUploadResponse.fileName = ''
          this.hbFileUploadResponse.url = ''
          this.radio = 0
          this.passwordEffectiveRange = 100
          this.updateBasicInformation = 200
          this.importSuccessVisible = true
        } else {
          this.$message.warning('操作失败！')
        }
      } else {
        this.$message.warning('请先上传文件')
      }
      this.loading = false
    }

    // 前往下载
    @bind
    @debounce(1000)
    goImportDownloadPage() {
      this.importSuccessVisible = false
      this.$router.push({ path: '/training/task/importtask', query: { type: '导入学员' } })
    }
  }
</script>
