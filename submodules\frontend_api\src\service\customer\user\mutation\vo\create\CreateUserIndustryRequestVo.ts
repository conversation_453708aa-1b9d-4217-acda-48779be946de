import { CreateUserIndustryRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import CreateCertificateInfoVo from './CreateCertificateInfoVo'
export class SectionAndSubjects {
  section: number
  subjects: number
}

/**
 * 行业信息
 */

class CreateUserIndustryRequestVo extends CreateUserIndustryRequest {
  /**
   * 所属行业
   */
  industryId = ''
  /**
   * 一级专业类别
   */
  firstProfessionalCategory: string = undefined
  /**
   * 二级专业类别
   */
  secondProfessionalCategory?: string = ''
  /**
   * 职称等级
   */
  professionalQualification: string = undefined
  //以上不选建设时是人设信息

  /**
   * 证书信息
   * 建设行业没有证书信息
   */
  certificateInfos: Array<CreateCertificateInfoVo> = []

  /**
   * 人员类别（职业卫生行业）
   */
  personnelCategory: string = undefined
  /**
   * 岗位类别（职业卫生行业）
   */
  positionCategory: string = undefined
  /**
   * 技术等级（工勤行业）
   */
  professionalLevel: string = undefined
  /**
   * 工种（工勤行业）
   */
  jobCategoryId: string = undefined
  /**
   * 教师行业 学段、学科信息 code
   */
  sectionAndSubjects: Array<SectionAndSubjects> = undefined
  /**
   * 学段（教师行业）
   */
  learningPhase: string = undefined
  /**
   * 学科（教师行业）
   */
  disciplin: string = undefined
  /**
   * 证书类型
   */
  certificatesType: string = undefined
  /**
   * 执业类别
   */
  practitionerCategory: string = undefined
}

export default CreateUserIndustryRequestVo
