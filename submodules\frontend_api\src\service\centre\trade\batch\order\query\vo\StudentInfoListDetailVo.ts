import RegionInfoVo from '@api/service/centre/trade/batch/order/query/vo/RegionInfoVo'
import SchemeInfoVo from '@api/service/centre/trade/batch/order/query/vo/SchemeInfoVo'
import { SignUpStatusEnum } from '@api/service/centre/trade/batch/order/enum/SignUpStatusList'

/**
 * @description
 */
class StudentInfoListDetailVo {
  /**
   * 唯一标识
   */
  id = ''

  /**
   * 学员id
   */
  studentId = ''

  /**
   * 学员姓名
   */
  studentName = ''

  /**
   * 学员证件号
   */
  studentAccount = ''

  /**
   * 学员地区
   */
  studentRegionInfo: RegionInfoVo

  /**
   * 培训班信息
   */
  schemeInfo: SchemeInfoVo = new SchemeInfoVo()

  /**
   * 学时
   */
  period = 0

  /**
   * 价格
   */
  price = 0

  /**
   * 报名状态 1：未完成 2：已完成 3：已失效
   */
  signUpStatus: SignUpStatusEnum

  /**
   * 报名失败理由（仅当报名状态非已完成）
   */
  signUpFailReason = ''
}

export default StudentInfoListDetailVo
