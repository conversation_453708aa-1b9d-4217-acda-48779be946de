<template>
  <div>
    <el-form-item label="试题题目：" required>
      <div class="rich-text">
        <!-- <el-input type="textarea" :rows="6" v-model="createQuestion.described" placeholder="请输入试题题目" /> -->
        <hb-tinymce-editor v-model="createQuestion.described" :propId="basicKey" token=""></hb-tinymce-editor>
      </div>
    </el-form-item>
    <el-form-item label="量表级数：" required>
      <el-select placeholder="请选择" v-model="createQuestion.levelNum">
        <el-option v-for="item in levelOptions" :key="item" :label="item" :value="item"> </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="量表类型：" required>
      <el-select placeholder="请选择" v-model="createQuestion.type" @change="changeDeepTip">
        <el-option v-for="item in GaugeType.list()" :key="item.code" :label="item.desc" :value="item.code"> </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="程度：" required v-if="createQuestion.type === GaugeTypeEnum.customer">
      <el-input class="input-num" v-model="createQuestion.minDeepTip"></el-input> -
      <el-input class="input-num" v-model="createQuestion.maxDeepTip"></el-input>
    </el-form-item>
    <el-form-item label="起始数值：" required>
      <el-input-number :min="1" :precision="0" v-model="createQuestion.startLevel" @input="changeTime($event)" />
    </el-form-item>
  </div>
</template>
<script lang="ts">
  import GaugeType, { GaugeTypeEnum } from '@api/service/common/question-naire/enums/GaugeType'
  import GaugeQuestion from '@api/service/common/question-naire/GaugeQuestion'
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import HbTinyMceEditor from '@hbfe/jxjy-admin-components/src/tinymce-editor/index.vue'
  @Component({
    components: {
      HbTinyMceEditor
    }
  })
  export default class extends Vue {
    @Prop({ type: Object })
    createQuestion: GaugeQuestion //量表入参
    GaugeTypeEnum = GaugeTypeEnum
    GaugeType = new GaugeType() //类型枚举emitChange
    levelOptions = Array.from({ length: 9 }, (_, index) => index + 2) // 生成2~30的选项数组
    basicKey = new Date().getTime().toString() //key 解决数据不同步问题
    changeTime() {
      this.$forceUpdate()
    }
    /**
     * 自定义
     */
    changeDeepTip() {
      this.createQuestion.minDeepTip = ''
      this.createQuestion.maxDeepTip = ''
    }
    /**
     * 基本表单校验
     */
    validForm() {
      if (!this.createQuestion.described) {
        this.$message.warning('请填写试题题目，不可为空。')
        return false
      }
      if (!this.createQuestion.levelNum) {
        this.$message.warning('请填写量表级数，不可为空。')
        return false
      }
      if (this.createQuestion.type === (GaugeTypeEnum.customer as GaugeTypeEnum)) {
        if (!this.createQuestion.minDeepTip || !this.createQuestion.maxDeepTip) {
          this.$message.warning('请填写程度，不可为空。')
          return false
        }
        return true
      } else {
        if (!(this.createQuestion.type as GaugeTypeEnum)) {
          this.$message.warning('请填写量表类型，不可为空。')
          return false
        }
      }
      return true
    }
  }
</script>
