import {
  UserRequest,
  CollectiveQueryRequest,
  AccountRequest,
  EnterprisePersonSortFieldEnum,
  SortTypeEnum,
  AuthenticationRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

/**
 * @description
 */
class CollectiveManagerQueryIdVo {
  /**
   * 用户名称（模糊）
   */
  userName?: string

  /**
   * 证件号
   */
  idCard?: string = ''

  /**
   * 手机号
   */
  phone?: string = ''

  /**
   * 账号
   */
  identity?: string = ''

  to(): CollectiveQueryRequest {
    const to = new CollectiveQueryRequest()
    to.user = new UserRequest()
    to.user.userName = this.userName ?? undefined
    to.user.idCard = this.idCard ?? undefined
    to.user.phone = this.phone ?? undefined
    to.account = new AccountRequest()
    to.account.accountTypeList = [3]
    to.authentication = new AuthenticationRequest()
    to.authentication.identity = this.identity ?? undefined
    to.sortList = [{ sortField: EnterprisePersonSortFieldEnum.accountType, sortType: SortTypeEnum.DESC }]
    return to
  }
}

export default CollectiveManagerQueryIdVo
