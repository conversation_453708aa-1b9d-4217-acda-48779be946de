<template>
  <el-main v-if="$hasPermission('query')" desc="分销入口" query actions="">
    <div class="f-p15">
      <div class="m-info-list">
        <div class="item" @click="toMarkerting">
          <div class="text">
            <div class="tit">分销管理</div>
            <div class="con">
              {{ context }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Vue, Component } from 'vue-property-decorator'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'

  @Component({})
  export default class extends Vue {
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)

    async created() {
      await CapabilityServiceConfig.checkFxCapability()
    }

    /**
     * 是否展示
     */
    get isShow() {
      return CapabilityServiceConfig.fxCapabilityEnable
    }

    get title() {
      return '分销管理'
    }

    get context() {
      return this.isFxlogin
        ? '请进入分销管理中心管理被授权的分销商品。'
        : '将培训方案授权给分销商进行销售，帮助网校提高销售效率和销售范围~'
    }

    toMarkerting() {
      // 获取菜单
      this.$router.push('/fx')
    }
  }
</script>
