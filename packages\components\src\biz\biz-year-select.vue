<route-meta>
{
"title": "年度选择器"
}
</route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    :multiple="multiple"
    filterable
    clearable
  >
    <el-option v-for="item in placeChannelOptions" :label="item.year" :value="item.id" :key="item.id"></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import QueryYear from '@api/service/common/basic-data-dictionary/query/QueryYear'
  import YearVo from '@api/service/common/basic-data-dictionary/query/vo/YearVo'
  @Component
  export default class extends Vue {
    selected: string | string[] = null
    placeChannelOptions: Array<YearVo> = new Array<YearVo>()
    @Prop()
    value: string | string[]

    @Prop({
      type: String,
      default: '请选择年度'
    })
    placeholder: string

    @Prop({
      type: <PERSON>olean,
      default: false
    })
    multiple: boolean
    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string | string[]) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }
    async created() {
      const res = await QueryYear.queryYearList()
      if (res.isSuccess()) {
        this.placeChannelOptions = QueryYear.yearList.sort((a, b) => Number(b.year) - Number(a.year))
      }
    }
  }
</script>
