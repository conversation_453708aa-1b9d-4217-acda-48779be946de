"""独立部署的微服务,K8S服务名:ms-payment-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询批次订单的付款单状态
		@param batchOrderNo 批次单号
		@return 付款单状态信息
	"""
	prepareBatchOrderRepay(batchOrderNo:String):BatchOrderPrepareRepayResponse
	"""查询订单的付款单状态
		@param orderNo 订单号
		@return 付款单状态信息
	"""
	prepareRepay(orderNo:String):PrepareRepayResponse
	"""查询流水状态
		@param flowNo 流水号
		@return 支付流水结果
	"""
	queryPayFlowStatus(flowNo:String):TimeoutPayFlowResponse
	"""根据流水号查询对应收款账户扫码引导信息，当返回的信息为空时，则表示没有配置引导信息
		@param flowNo 流水号
		@return 对应收款账户扫码引导信息
	"""
	queryQrScanPromptByPayFlowNo(flowNo:String):String
}
type Mutation {
	"""管理员确认付款单已线下付款
		@param paymentOrderNo 付款单号
	"""
	confirmPaymentOrderOfflinePaid(paymentOrderNo:String):Void
	"""获取批次准备支付信息
		@param batchNo 批次订单号
		@return 准备支付信息结果
	"""
	getBatchPreParePayResult(batchNo:String):BatchPreparePayInfoResponse @optionalLogin
	"""获取准备支付信息
		@param orderNo 订单号
		@return 准备支付信息结果
	"""
	getPreParePayResult(orderNo:String):PreparePayInfoResponse @optionalLogin
	"""超时支付流水
		@param flowNo 流水号
		@return 超时结果
	"""
	timeoutPayFlow(flowNo:String):TimeoutPayFlowResponse @optionalLogin
	"""更新线下支付凭证
		@param request 凭证信息
	"""
	updateOfflinePaymentVoucher(request:OfflinePaymentOrderUpdateVoucherRequest):Void
}
"""订单线下付款更新凭证命令
	<AUTHOR>
	@since 2021/1/29
"""
input OfflinePaymentOrderUpdateVoucherRequest @type(value:"com.fjhb.ms.payment.v1.kernel.gateway.graphql.request.OfflinePaymentOrderUpdateVoucherRequest") {
	"""付款单号"""
	paymentOrderNo:String!
	"""付款凭证列表"""
	paymentVouchers:[PaymentVoucherRequest]!
}
"""
	<AUTHOR>
	@since 2021/2/1
"""
input PaymentVoucherRequest @type(value:"com.fjhb.ms.payment.v1.kernel.gateway.graphql.request.PaymentVoucherRequest") {
	"""付款凭证文件路径，通常是图片的路径"""
	path:String
	"""凭证上传时间"""
	uploadTime:DateTime
}
type BatchOrderPrepareRepayResponse @type(value:"com.fjhb.ms.payment.v1.kernel.gateway.graphql.response.BatchOrderPrepareRepayResponse") {
	"""批次单号"""
	batchOrderNo:String
	"""支付状态
		0-未支付
		1-支付中
		2-已支付
	"""
	paymentStatus:Int!
}
"""准备支付结果"""
type BatchPreparePayInfoResponse @type(value:"com.fjhb.ms.payment.v1.kernel.gateway.graphql.response.BatchPreparePayInfoResponse") {
	"""批次单号"""
	batchNo:String
	"""支付状态
		0-未支付
		1-支付中
		2-已支付
	"""
	paymentStatus:Int!
	"""是否超时"""
	timeOut:Boolean!
	"""支付流水号"""
	flowNo:String
	"""支付回调地址"""
	pageUrl:String
	"""支付金额"""
	payMoney:String
	"""商户名称"""
	merchantName:String
	"""项目id"""
	projectId:String
	"""平台版本id"""
	platformVersionId:String
	"""超时时间"""
	timeOutTime:Long!
	"""第三方支付返回参数
		用于生成二维码。
	"""
	thirdReturnData:String
}
"""准备支付结果"""
type PreparePayInfoResponse @type(value:"com.fjhb.ms.payment.v1.kernel.gateway.graphql.response.PreparePayInfoResponse") {
	"""订单号"""
	orderNo:String
	"""支付状态
		0-未支付
		1-支付中
		2-已支付
	"""
	paymentStatus:Int!
	"""是否超时"""
	timeOut:Boolean!
	"""支付流水号"""
	flowNo:String
	"""支付回调地址"""
	pageUrl:String
	"""支付金额"""
	payMoney:String
	"""商户名称"""
	merchantName:String
	"""项目id"""
	projectId:String
	"""平台版本id"""
	platformVersionId:String
	"""超时时间"""
	timeOutTime:Long!
	"""第三方支付返回参数
		用于生成二维码。
	"""
	thirdReturnData:String
}
type PrepareRepayResponse @type(value:"com.fjhb.ms.payment.v1.kernel.gateway.graphql.response.PrepareRepayResponse") {
	"""订单号"""
	orderNo:String
	"""支付状态
		0-未支付
		1-支付中
		2-已支付
	"""
	paymentStatus:Int!
}
"""支付流水超时结果"""
type TimeoutPayFlowResponse @type(value:"com.fjhb.ms.payment.v1.kernel.gateway.graphql.response.TimeoutPayFlowResponse") {
	"""流水号"""
	flowNo:String
	"""是否超时"""
	timeOut:Boolean!
	"""流水支付结果
		1-支付处理中 2-支付成功 3-支付失败 4-支付超时 5-支付关闭
	"""
	payStatus:Int!
	"""超时时间"""
	timeOutTime:Long!
}

scalar List
