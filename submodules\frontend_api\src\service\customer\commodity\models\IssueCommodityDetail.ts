import {
  ExamLearningResponse,
  IssueClassLSAchieveSettingResponse,
  QuestionPracticeLearningDto
} from '@api/gateway/PlatformLearningScheme'
import { InterestCourse } from '@api/service/common/models/learning/InterestCourse'
import { CourseLearning } from '@api/service/common/learning-scheme/CourseLearning'

export class IssueCommodityDetail {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 方案名称
   */
  name: string
  /**
   * 图片
   */
  picture: string
  /**
   * 课程学习方式
   */
  courseLearning: CourseLearning
  /**
   *考试学习方式
   */
  examLearning: ExamLearningResponse
  /**
   *试题练习
   */
  questionPracticeLearning: QuestionPracticeLearningDto
  /**
   * 培训班成果设置
   */
  achieveSetting: IssueClassLSAchieveSettingResponse
  /**
   * 兴趣课配置
   */
  interestCourse: InterestCourse
  /**
   *创建人
   */
  createUserId: string
  /**
   *创建时间
   */
  createTime: string
}
