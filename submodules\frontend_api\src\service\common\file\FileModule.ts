/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-04-16 10:00:00
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-06-26 20:20:38
 * @Description:
 */
import store from '@/store'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import axios from 'axios'
import { Action, Module, Mutation, VuexModule, getModule } from 'vuex-module-decorators'
@Module({
  name: 'CommonFileModule',
  store,
  namespaced: true,
  dynamic: true
})
class FileModule extends VuexModule {
  // 1. 图片、pdf、excel资源查看
  // 2. 时效1分钟 如何刷新

  /**
   * 资源访问token
   */
  resourceAccessToken = ''
  /**
   * 最新获取token时间戳
   */
  lastTimeStamp = 0

  /**
   * isValid
   */
  isValid = false

  /**
   * 获取查看受保护资源授权token
   */
  @Action
  async applyResourceAccessToken() {
    const isValid = Date.now() - this.lastTimeStamp - 60000 < 0
    if (!isValid) {
      const baseUrl = `${ConfigCenterModule.getFrontendApplication(
        frontendApplication.apiendpoint
      )}/web/ms-file-v1/token/getTempAccessToken`
      try {
        const config = {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json;charset=UTF-8',
            'App-Authentication': `Basic ${process.env.VUE_APP_KEY}`
          }
        }
        if (localStorage.getItem('customer.Access-Token')) {
          config.headers['Authorization'] = `Mship ${localStorage.getItem('customer.Access-Token')}`
        }

        await axios.get(baseUrl, config).then(data => {
          if (data?.data?.code === 200) {
            this.SET_RESOURCE_ACCESS_TOKEN(data.data.data)
            this.SET_LAST_TIME_STAMP(Date.now())
            console.log(data.data.data)
          }
        })
      } catch (error) {
        console.log(error)
      }
    }
  }
  /**
   * 设置资源访问授权token
   */
  @Mutation
  SET_RESOURCE_ACCESS_TOKEN(url: string) {
    this.resourceAccessToken = url
  }

  /**
   * 设置最新获取授权token的时间戳
   */
  @Mutation
  SET_LAST_TIME_STAMP(timeStamp: number) {
    this.lastTimeStamp = timeStamp
  }
}
export default getModule(FileModule)
