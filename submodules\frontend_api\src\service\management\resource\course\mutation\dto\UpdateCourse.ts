import {
  CourseChapterRequest,
  CourseOutlineWithSubOutlineRequest,
  CourseUpdateRequest
} from '@api/ms-gateway/ms-course-resource-v1'
import MsCourseResourceV1 from '@api/ms-gateway/ms-course-resource-v1'
import UpdateCourseVo from '@api/service/management/resource/course/mutation/vo/UpdateCourse'
import { ResponseStatus } from '@hbfe/common'
import { CourseOutline } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import Chapter from '@api/service/management/resource/course/mutation/vo/Chapter'
import Courseware from '@api/service/management/resource/course/mutation/vo/Courseware'
import course from '@api/service/common/models/course/Course'

class UpdateCourse extends CourseUpdateRequest {
  id: string

  async save(): Promise<ResponseStatus> {
    const result = await MsCourseResourceV1.updateCourse(this)
    return new ResponseStatus(result.status.code, result.status.getMessage())
  }

  /**
   * 转换器
   * @param updateCourseVo
   */
  static from(updateCourseVo: UpdateCourseVo) {
    const dto = new UpdateCourse()
    dto.id = updateCourseVo.id
    dto.name = updateCourseVo.name
    dto.iconPath = updateCourseVo.picture
    dto.categoryIds = Array.isArray(updateCourseVo.categoryId) ? updateCourseVo.categoryId : [updateCourseVo.categoryId]
    dto.courseOutlines = new Array<CourseOutlineWithSubOutlineRequest>()
    dto.aboutsContent = updateCourseVo.description
    updateCourseVo.chapters.forEach((chapter: Chapter, index: number) => {
      const courseSubOutline = new CourseOutlineWithSubOutlineRequest()
      courseSubOutline.name = chapter.name
      courseSubOutline.sort = index
      courseSubOutline.courseChapters = new Array<CourseChapterRequest>()
      chapter.coursewares.forEach((courseware: Courseware, index: number) => {
        const chapterRequest = new CourseChapterRequest()
        chapterRequest.name = courseware.name
        chapterRequest.sort = index
        chapterRequest.auditionStatus = courseware.trialType
        chapterRequest.coursewareId = courseware.id
        courseSubOutline.courseChapters.push(chapterRequest)
      })
      dto.courseOutlines.push(courseSubOutline)
    })
    // dto.courseOutlines = updateCourseVo.outlines
    //   .filter((outline: CourseOutline) => outline.parentId === '-1')
    //   .map((outline: CourseOutline) => {
    //     const outlineRequest = new CourseOutlineWithSubOutlineRequest()
    //     outlineRequest.id = outline.id
    //     outlineRequest.name = outline.name
    //     outlineRequest.sort = outline.sort
    //     outlineRequest.courseChapters = new Array<CourseChapterRequest>()
    //     outlineRequest.subCourseOutlines = new Array<CourseOutlineWithSubOutlineRequest>()
    //     return outlineRequest
    //   })
    //
    // console.log(updateCourseVo.outlines, dto.courseOutlines)
    // dto.courseOutlines.forEach((outline: CourseOutlineWithSubOutlineRequest) => {
    //   outline.subCourseOutlines = updateCourseVo.outlines.filter(
    //     (voOutline: CourseOutline) => voOutline.parentId === outline.id
    //   )
    //   if (outline.subCourseOutlines.length) {
    //     outline.subCourseOutlines.forEach((subOutline: CourseOutlineWithSubOutlineRequest) => {
    //       subOutline.courseChapters = new Array<CourseChapterRequest>()
    //       updateCourseVo.chapters.forEach((chapter: Chapter) => {
    //         const listCourseware = chapter.coursewares.filter(
    //           (courseware: Courseware) => courseware.courseOutlineId === subOutline.id
    //         )
    //         listCourseware.forEach((courseware: Courseware, index: number) => {
    //           const chapterRequest = new CourseChapterRequest()
    //           chapterRequest.id = courseware.courseChapterId
    //           chapterRequest.name = courseware.name
    //           chapterRequest.sort = index
    //           chapterRequest.auditionStatus = courseware.trialType
    //           chapterRequest.coursewareId = courseware.id
    //           subOutline.courseChapters.push(chapterRequest)
    //         })
    //       })
    //     })
    //   } else {
    //     updateCourseVo.chapters.forEach((chapter: Chapter) => {
    //       const listCourseware = chapter.coursewares.filter(
    //         (courseware: Courseware) => courseware.courseOutlineId === outline.id
    //       )
    //       listCourseware.forEach((courseware: Courseware, index: number) => {
    //         const chapterRequest = new CourseChapterRequest()
    //         chapterRequest.id = courseware.courseChapterId
    //         chapterRequest.name = courseware.name
    //         chapterRequest.sort = index
    //         chapterRequest.auditionStatus = courseware.trialType
    //         chapterRequest.coursewareId = courseware.id
    //         outline.courseChapters.push(chapterRequest)
    //       })
    //     })
    //   }
    // })
    console.log(dto)
    return dto
  }
}

export default UpdateCourse
