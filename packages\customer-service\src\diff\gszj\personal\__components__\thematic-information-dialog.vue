<template>
  <div>
    <!-- 编辑专题信息抽屉 -->
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      direction="rtl"
      size="560px"
      custom-class="m-drawer"
      :before-close="handleCancel"
    >
      <div class="drawer-bd">
        <el-form ref="drawerFormRef" :model="drawerForm" label-width="auto" class="m-form f-mt20">
          <el-form-item label="工作单位性质：" prop="natureWorkUnit">
            <el-select v-model="drawerForm.natureWorkUnit" clearable placeholder="请选择工作单位性质">
              <el-option
                v-for="item in workUnitCategoriesTypes"
                :label="item.name"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="在编情况：" prop="currentSituation" v-if="isCurrentSituation">
            <el-select v-model="drawerForm.currentSituation" clearable placeholder="请选择在编情况">
              <el-option
                v-for="item in empowermentStatusTypes"
                :label="item.name"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否在专技岗位工作：" prop="isOnTechnicalPost">
            <el-select v-model="drawerForm.isOnTechnicalPost" clearable placeholder="请选择是否在专技岗位工作">
              <el-option
                v-for="item in workInSpecialPostList"
                :label="item.desc"
                :value="item.code"
                :key="item.code"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="职称系列：" prop="titleSeries">
            <el-select placeholder="请选择职称系列" v-model="drawerForm.titleSeries" clearable>
              <el-option
                v-for="item in professionalSeriesList"
                :label="item.name"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="职称专业：" prop="professionalTitle" v-if="isTitleSeries">
            <el-input v-model="drawerForm.professionalTitle" clearable placeholder="请输入职称专业" />
          </el-form-item>
          <el-form-item label="现有职称等级：" prop="currentTitleLevel" v-if="isTitleSeries">
            <el-select placeholder="请选择现有职称等级" v-model="drawerForm.currentTitleLevel" clearable>
              <el-option
                v-for="item in professionalGradeList"
                :label="item.name"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="现有职称资格名称：" prop="currentTitleQualification" v-if="isTitleSeries">
            <el-input v-model="drawerForm.currentTitleQualification" clearable placeholder="请输入现有职称资格名称" />
          </el-form-item>
          <el-form-item label="现有职称有效范围：" prop="currentTitleValidRange" v-if="isTitleSeries">
            <el-select placeholder="请选择现有职称有效范围" v-model="drawerForm.currentTitleValidRange" clearable>
              <el-option
                v-for="item in professionalValidityTypes"
                :label="item.name"
                :value="item.id"
                :key="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="最高学历：" prop="highestEducation">
            <el-input v-model="drawerForm.highestEducation" clearable placeholder="请输入最高学历" />
          </el-form-item>
          <el-form-item class="m-btn-bar">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSave" :loading="loading">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Watch, Prop } from 'vue-property-decorator'
  import { ElForm } from 'element-ui/types/form'
  import UserDetailVoDiff from '@api/service/diff/management/gszj/user/student/model/UserDetailVo'
  import TopicInformationVo from '@api/service/diff/management/gszj/user/student/model/TopicInformationVo'
  import MutationUpdateStudentInfo from '@api/service/diff/management/gszj/user/student/MutationUpdateStudentInfo'
  import UpdateUserDetailVo from '@api/service/diff/management/gszj/user/student/model/UpdateUserDetailVo'
  import WorkInSpecialPostTypes from '@api/service/diff/common/gszj/basic-data-dictionary/query/enum/WorkInSpecialPostTypes'
  import DictionaryTypeTypeVo from '@api/service/diff/common/gszj/basic-data-dictionary/query/vo/DictionaryTypeTypeVo'
  import QueryDictionaryType from '@api/service/diff/common/gszj/basic-data-dictionary/query/QueryDictionaryType'
  import { DictionaryTypeEnum } from '@api/service/diff/common/gszj/basic-data-dictionary/query/enum/DictionaryType'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import { bind, debounce } from 'lodash-decorators'

  @Component
  export default class extends Vue {
    @Ref('drawerFormRef')
    drawerFormRef: ElForm

    drawer = false

    drawerTitle = '编辑专题信息-金昌市'

    drawerForm = new TopicInformationVo()

    natureWorkUnitList: Array<TrainingCategoryVo> = new Array<TrainingCategoryVo>()

    // 编辑专题信息 必填校验
    drawerFormRules = {
      natureWorkUnit: [{ required: true, message: '请选择工作单位性质', trigger: 'change' }],
      currentSituation: [{ required: true, message: '请选择在编情况', trigger: '在编情况' }],
      isOnTechnicalPost: [{ required: true, message: '请选择是否在专技岗位工作', trigger: 'change' }],
      titleSeries: [{ required: true, message: '请选择职称系列', trigger: 'change' }],
      professionalTitle: [{ required: true, message: '请输入职称专业', trigger: ['blur', 'change'] }],
      currentTitleLevel: [{ required: true, message: '请选择现有职称等级', trigger: 'change' }],
      currentTitleQualification: [{ required: true, message: '请输入现有职称资格名称', trigger: ['blur', 'change'] }],
      currentTitleValidRange: [{ required: true, message: '请选择现有职称有效范围', trigger: 'change' }],
      highestEducation: [{ required: true, message: '请输入最高学历', trigger: ['blur', 'change'] }]
    }

    // 修改入参
    studentParams = new UserDetailVoDiff()

    // 学员修改实例
    studentUpdateObj = new MutationUpdateStudentInfo()

    loading = false

    /**
     * 工作单位性质数组
     */
    workUnitCategoriesTypes: Array<DictionaryTypeTypeVo> = []

    /**
     * 在编情况数组
     */
    empowermentStatusTypes: Array<DictionaryTypeTypeVo> = []

    /**
     * 专技岗位工作数组
     */
    workInSpecialPostList = WorkInSpecialPostTypes.list()

    /**
     * 职称系列数组
     */
    professionalSeriesList: Array<DictionaryTypeTypeVo> = []

    /**
     * 甘肃专技职称等级数组
     */
    professionalGradeList: Array<DictionaryTypeTypeVo> = []

    /**
     * 现有职称范围数组
     */
    professionalValidityTypes: Array<DictionaryTypeTypeVo> = []

    get isCurrentSituation() {
      const validValues = [
        'jxjyGszjUnitNatureDict0000000002',
        'jxjyGszjUnitNatureDict0000000005',
        'jxjyGszjUnitNatureDict0000000006'
      ]
      const natureWorkUnit = this.drawerForm.natureWorkUnit
      return validValues.includes(natureWorkUnit)
    }

    get isTitleSeries() {
      const titleSeries = this.drawerForm.titleSeries
      return titleSeries && titleSeries != 'jxjyGszjTitleSeries0000000000032'
    }

    async created() {
      try {
        const [
          empowermentStatusRes,
          professionalValidityRes,
          workUnitCategoriesRes,
          professionalSeriesRes,
          professionalGradeRes
        ] = await Promise.all([
          QueryDictionaryType.queryDictionaryTypeList(DictionaryTypeEnum.GSZJ_STAFFING_STATUS),
          QueryDictionaryType.queryDictionaryTypeList(DictionaryTypeEnum.GSZJ_TITLE_EFFECTIVE_RANGE),
          QueryDictionaryType.queryDictionaryTypeList(DictionaryTypeEnum.GSZJ_UNIT_NATURE),
          QueryDictionaryType.queryDictionaryTypeList(DictionaryTypeEnum.GSZJ_TITLE_SERIES),
          QueryDictionaryType.queryDictionaryTypeList(DictionaryTypeEnum.GSZJ_TITLE_GRADE)
        ])
        // 赋值数据
        this.workUnitCategoriesTypes = workUnitCategoriesRes || [] //工作单位性质数组
        this.empowermentStatusTypes = empowermentStatusRes || [] //在编情况数组
        this.professionalSeriesList = professionalSeriesRes || [] //在编情况数组
        this.professionalGradeList = professionalGradeRes || [] //甘肃专技职称等级数组
        this.professionalValidityTypes = professionalValidityRes || [] //现有职称范围数组
      } catch (error) {
        console.error('获取字典类型失败:', error)
      }
    }

    // 根据父组件传入的值给表单赋值
    transformData(studentParams: UserDetailVoDiff, studentUpdateObj: MutationUpdateStudentInfo) {
      this.studentParams = studentParams
      this.studentUpdateObj = studentUpdateObj
      if (studentParams) {
        this.drawerForm = Object.assign(new TopicInformationVo(), studentParams.topicInformationVo)
      }
      console.log('studentParams', studentParams)
      console.log('studentUpdateObj', studentUpdateObj)
    }

    // 取消
    handleCancel() {
      this.drawer = false
      this.loading = false
      this.drawerForm = new TopicInformationVo()
    }

    //保存
    @bind
    @debounce(200)
    async handleSave() {
      this.drawerFormRef.validate(async (val: boolean) => {
        if (val) {
          try {
            this.loading = true
            console.log('保存表单数据', this.drawerForm)
            const updateStudentParams = this.studentUpdateObj?.updateStudentParams
            const updateUserDetailVo = Object.assign(new UpdateUserDetailVo(), updateStudentParams)
            updateUserDetailVo.topicInformationVo = this.drawerForm
            updateUserDetailVo.topicInformationVo.currentSituation = this.isCurrentSituation
              ? this.drawerForm.currentSituation
              : ''
            updateUserDetailVo.topicInformationVo.professionalTitle = this.isTitleSeries
              ? this.drawerForm.professionalTitle
              : ''
            updateUserDetailVo.topicInformationVo.currentTitleLevel = this.isTitleSeries
              ? this.drawerForm.currentTitleLevel
              : ''
            updateUserDetailVo.topicInformationVo.currentTitleQualification = this.isTitleSeries
              ? this.drawerForm.currentTitleQualification
              : ''
            updateUserDetailVo.topicInformationVo.currentTitleValidRange = this.isTitleSeries
              ? this.drawerForm.currentTitleValidRange
              : ''
            console.log(updateUserDetailVo, 'updateUserDetailVo')
            const res = await this.studentUpdateObj.doUpdateStudentInfoDiff(updateUserDetailVo)
            console.log(res, 'res')
            if (!res?.status.isSuccess()) {
              this.loading = false
              return this.$message.error('修改请求失败！')
            } else {
              if (res?.data?.code == 200) {
                this.$message.success('修改请求成功！')
                this.$emit('updateUserInfo')
                this.handleCancel()
              }
              if (res?.data?.code == 100001) {
                this.$message.warning('手机号码重复！')
              }
              if (res?.data?.code == 100002) {
                this.$message.warning('身份证号码重复！')
              }
              this.loading = false
            }
          } catch (e) {
            console.log(e)
          } finally {
            this.loading = false
          }
        }
      })
    }
  }
</script>
