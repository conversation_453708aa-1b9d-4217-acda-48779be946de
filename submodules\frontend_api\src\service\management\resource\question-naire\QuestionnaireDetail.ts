import Question from '@api/service/common/question-naire/Question'
import QuestionnaireItem from '@api/service/management/resource/question-naire/models/QuestionnaireItem'
import MsExamextraction, {
  EvaluatePatternRequest,
  FixedPaperRequest,
  PaperPublishConfigureCreateRequest,
  PaperPublishConfigureUpdateRequest,
  PaperQuestionDTO
} from '@api/ms-gateway/ms-examextraction-v1'
import { QuestionTypeEnum } from '@api/service/common/enums/question-naire/QuestionType'
import MsQuestionnaireQueryBackStage, {
  FixedPaper,
  QuestionnairePaperPublishConfigureResponse,
  QuestionnaireTemplateDetailRequest
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'
import PlatformExamQueryBackStage, {
  BaseQuestionResponse,
  MultipleQuestionResponse,
  RadioQuestionResponse,
  ScaleQuestionResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
// import PlatQuestionnaire, {
//   CopyQuestionnaireQuestion,
//   CopyQuestionnaireRequest,
//   CreateQuestionRequest
// } from '@api/platform-gateway/platform-jxjy-examquestion-v1'
import MsQuestionnaire, {
  CopyQuestionnaireQuestion,
  CopyQuestionnaireRequest,
  CreateQuestionRequest
} from '@api/ms-gateway/ms-examextraction-v1'
import { TeacherQuestionTypeEnum } from '@api/service/common/question-naire/enums/TeacherQuestionType'
import { TeacherEvaluationQuestionTypeEnum } from '@api/service/common/question-naire/enums/TeacherEvaluationQuestionType'
import { QuestionSourceTypeEnum } from '@api/service/common/question-naire/enums/QuestionSourceType'
import { cloneDeep } from 'lodash'
/**
 * 问卷详情
 * 答题记录是否需要缓存
 */
export default class QuestionnaireDetail extends QuestionnaireItem {
  /**
   * 说明
   */
  questionnaireInfo = ''
  /**
   * 题目列表
   */
  questionList: Question[] = new Array<Question>()

  /**
   * 查询详情
   */
  async queryDetail(isPreview = false) {
    const request = new QuestionnaireTemplateDetailRequest()
    request.templateId = this.id
    const res = await MsQuestionnaireQueryBackStage.getQuestionnaireTemplateDetailInServicer(request)
    if (res.status.isSuccess()) {
      this.parameterChange(res.data)
      const questionIdList = this.questionList.map((item) => item.id)
      if (!questionIdList.length) {
        this.questionList = []
        return
      }
      const currentPageData = new Array<BaseQuestionResponse>()
      // 获取试题 防止分页口超过200条
      const fetchQuestionList = async (ids: string[]) => {
        const questionRes = await PlatformExamQueryBackStage.pageQuestionInServicer({
          page: {
            pageNo: 1,
            pageSize: ids.length
          },
          request: {
            queryScope: 2,
            questionIdList: ids
          }
        })
        if (questionRes.status.isSuccess() && questionRes.data.currentPageData?.length) {
          currentPageData.push(...questionRes.data.currentPageData)
        }
      }
      for (let i = 0; i < questionIdList.length; i += 200) {
        const chunk = questionIdList.slice(i, i + 200)
        await fetchQuestionList(chunk)
      }
      if (currentPageData?.length) {
        const newArray = currentPageData.map((item) => {
          // 没写问答题response
          const temp = Question.fromQuestion(
            item as RadioQuestionResponse | MultipleQuestionResponse | ScaleQuestionResponse
          )
          return temp
        })
        const map = new Map(newArray.map((item) => [item.id, item]))
        // 需要按照试卷获取的id排序
        let mergedArray = this.questionList.map((item1) => {
          const temp = map.get(item1.id)
          if (temp) {
            temp.isMustAnswered = item1.isMustAnswered
          }
          return temp
        })

        if (isPreview) {
          mergedArray = mergedArray.reduce((acc, item) => {
            if (item.isTeacherEvaluate && isPreview) {
              const newItem1 = cloneDeep(item)
              newItem1.onlineOrOffline = QuestionSourceTypeEnum.online_teacher
              const newItem2 = cloneDeep(item)
              newItem2.onlineOrOffline = QuestionSourceTypeEnum.offline_teacher
              acc.push(newItem1)
              acc.push(newItem2)
            } else {
              acc.push(item)
            }
            return acc
          }, [] as typeof newArray)
        }
        // 解决部分题目查不到时全部题目出不来
        this.questionList = mergedArray.filter((item) => item != undefined)
      }
    }

    return res.status
  }
  /**
   * 保存问卷更改
   * @param isDraft 1草稿 2发布
   */
  async save(isDraft: number) {
    const request = new PaperPublishConfigureUpdateRequest()
    this.to(request)
    request.id = this.id
    request.isDraft = isDraft
    return await MsExamextraction.updatePaperPublishConfigure(request)
  }
  /**
   * 创建为草稿
   */
  async createDraft() {
    const request = new PaperPublishConfigureCreateRequest()
    this.to(request)
    request.isDraft = 1
    return await MsExamextraction.createPaperPublishConfigure(request)
  }
  /**
   * 发布问卷
   */
  async publish() {
    const request = new PaperPublishConfigureCreateRequest()
    this.to(request)
    request.isDraft = 2
    return await MsExamextraction.createPaperPublishConfigure(request)
  }

  /**
   * 复制问卷列表的问卷第二步 复制提交接口
   * @param isDraft 1草稿 2发布
   */
  async newCopy(isDraft: number) {
    const request = new CopyQuestionnaireRequest()
    this.to(request)
    const map = new Map<string, CreateQuestionRequest>()
    this.questionList.map((item) => {
      const request2 = item.to()
      map.set(item.id, request2)
    })
    request.publishPattern.questions.forEach((item) => {
      item.copyQuestionContent = map.get(item.questionId)
    })
    request.isDraft = isDraft
    const res = await MsQuestionnaire.copyQuestionnaire(request)
    return res
  }
  /**
   * 参转
   */
  parameterChange(dto: QuestionnairePaperPublishConfigureResponse) {
    this.id = dto.id
    this.questionnaireInfo = (dto.publishPattern as FixedPaper).description
    this.type = dto.type
    this.questionList = (dto.publishPattern as FixedPaper).questions.map((item) => {
      const temp = new Question()
      temp.id = item.questionId
      temp.isMustAnswered = item.answerRequired
      return temp
    })
    this.name = dto.name
    this.createTime = dto.createdTime
    this.status = dto.isDraft
    this.schemeUseStatus = dto.isReferenced
  }
  to(request: PaperPublishConfigureCreateRequest | PaperPublishConfigureUpdateRequest | CopyQuestionnaireRequest) {
    // 旧考试的试卷名称绑定的也是出卷配置名称
    request.name = this.name
    request.isDraft = 2
    request.questionnaireType = this.type
    request.paperPublishConfigureCategoryId = '0'
    request.usageScope = 3
    const pattern = new FixedPaperRequest()
    pattern.name = this.name
    pattern.type = 1
    pattern.description = this.questionnaireInfo
    pattern.evaluatePattern = new EvaluatePatternRequest()
    pattern.evaluatePattern.type = 2
    pattern.timeLength = 3600
    pattern.totalScore = 0.0
    pattern.questions = this.questionList.map((item) => {
      const temp = new PaperQuestionDTO()
      temp.questionId = item.id
      switch (item.type) {
        case QuestionTypeEnum.single:
          if (item.isTeacherEvaluate) {
            ;(temp as CopyQuestionnaireQuestion).teacherEvaluateCode =
              TeacherEvaluationQuestionTypeEnum.TEACHER_EVALUATION_QUESTION
          }
          temp.questionType = 1
          break
        case QuestionTypeEnum.multiple:
          if (item.isTeacherEvaluate) {
            ;(temp as CopyQuestionnaireQuestion).teacherEvaluateCode =
              TeacherEvaluationQuestionTypeEnum.TEACHER_EVALUATION_QUESTION
          }
          temp.questionType = 2
          break
        case QuestionTypeEnum.answer:
          temp.questionType = 5
          break
        case QuestionTypeEnum.gauge:
          temp.questionType = 7
          break
        default:
          break
      }
      temp.answerRequired = item.isMustAnswered
      temp.score = -1
      temp.groupSequence = -1
      return temp
    })
    pattern.groups = []
    request.publishPattern = pattern
  }
}
