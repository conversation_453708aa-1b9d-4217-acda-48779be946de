<template>
  <el-drawer title="查看问卷" :visible.sync="showDrawer" size="900px" custom-class="m-drawer">
    <div class="drawer-bd">
      <!--表格-->
      <el-table stripe :data="questionnaireList" max-height="500px" class="m-table f-mt10">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="问卷名称" min-width="200">
          <template v-slot="{ row }">{{ row.questionnaireName }}</template>
        </el-table-column>
        <el-table-column label="问卷开放时间" min-width="200" align="center">
          <template v-slot="{ row }">
            <template v-if="isAssignedDate(row)">
              <p><el-tag type="info" size="mini">起始</el-tag>{{ row.openDateRange.startDate }}</p>
              <p><el-tag type="info" size="mini">结束</el-tag>{{ row.openDateRange.startDate }}</p>
            </template>
            <template v-else-if="isLongTerm">长期有效</template>
            <template v-else-if="hasTrainClassLearningTime">
              <p><el-tag type="info" size="mini">起始</el-tag>{{ trainClassBaseInfo.trainingBeginDate }}</p>
              <p><el-tag type="info" size="mini">结束</el-tag>{{ trainClassBaseInfo.trainingEndDate }}</p>
            </template>
            <template v-else>—</template>
          </template>
        </el-table-column>
        <el-table-column label="问卷状态" min-width="100" align="center" fixed="right">
          <template v-slot="{ row }">{{ statusDesc(row) }} </template>
        </el-table-column>
        <el-table-column label="应用范围" min-width="120" align="center" fixed="right">
          <template v-slot="{ row }">{{ appliedRangeName(row.appliedRangeType) }}</template>
        </el-table-column>
        <el-table-column label="前置条件" min-width="120" align="center" fixed="right">
          <template v-slot="{ row }">{{ preconditionType(row.preconditionType) }}</template>
        </el-table-column>
      </el-table>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Vue } from 'vue-property-decorator'
  import { QuestionnaireOpenDateTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireOpenDateType'
  import SchemeBaseInfo from '@api/service/common/scheme/model/SchemeBaseInfo'
  import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
  import QuestionnaireAppliedRangeType, {
    QuestionnaireAppliedRangeTypeEnum
  } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'
  import QuestionnairePreconditionType, {
    QuestionnairePreconditionTypeEnum
  } from '@api/service/common/scheme/enum/QuestionnairePreconditionType'
  import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
  import { QuestionnaireStatusEnum } from '@api/service/common/scheme/enum/QuestionnaireStatus'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  @Component
  export default class extends Vue {
    @PropSync('questionnaireAssess', { type: Boolean, default: false }) showDrawer: boolean
    @Prop({ type: Object, default: () => new SchemeBaseInfo() }) trainClassBaseInfo: SchemeBaseInfo
    @Prop({ type: Array, default: () => new Array<QuestionnaireConfigDetail>() })
    questionnaireList: QuestionnaireConfigDetail[]

    /**
     * 开发时间是否指定时间
     */
    get isAssignedDate() {
      return (row: QuestionnaireConfigDetail) => {
        return row.openDateType === QuestionnaireOpenDateTypeEnum.assign
      }
    }
    /**
     * 当前培训班是否长期有效
     */
    get isLongTerm() {
      return (
        this.trainClassBaseInfo.trainingBeginDate === CreateSchemeUtils.defaultBeginDate &&
        this.trainClassBaseInfo.trainingEndDate === CreateSchemeUtils.defaultEndDate
      )
    }
    /**
     * 当前培训班学习时间
     */
    get hasTrainClassLearningTime() {
      return this.trainClassBaseInfo.trainingBeginDate && this.trainClassBaseInfo.trainingEndDate
    }

    /**
     * 范围名称
     * @returns {(item: QuestionnaireAppliedRangeTypeEnum) => string}
     */
    get appliedRangeName() {
      return (item: QuestionnaireAppliedRangeTypeEnum) => {
        return QuestionnaireAppliedRangeType.map.get(item)
      }
    }
    /**
     * 获取前置条件类型
     */
    get preconditionType() {
      return (item: QuestionnairePreconditionTypeEnum) => {
        return QuestionnairePreconditionType.map.get(item)
      }
    }
    get statusDesc() {
      return (row: QuestionnaireConfigDetail) => {
        if (row.operationType !== OperationTypeEnum.update) return '—'
        else if (row.status == QuestionnaireStatusEnum.enabled) return '启用'
        else return '停用'
      }
    }
  }
</script>
