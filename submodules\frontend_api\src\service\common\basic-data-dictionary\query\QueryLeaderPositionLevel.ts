import Backstage, { BusinessDataDictionaryRequest } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import LeaderPositionLevelVo from '@api/service/common/basic-data-dictionary/query/vo/LeaderPositionLevelVo'
import { ResponseStatus } from '@hbfe/common'

class QueryLeaderPositionLevel {
  /**
   * 职称等级列表
   */
  leaderPositionLevelList = Array<LeaderPositionLevelVo>()

  /**
   * 查询职称等级列表
   * @return leaderPositionLevelList
   */
  async queryLeaderPositionLevel() {
    const param = new BusinessDataDictionaryRequest()
    param.businessDataDictionaryType = 'TECHNICAL_GRADE'
    if (this.leaderPositionLevelList.length) {
      return new ResponseStatus(200, '状态层已有缓存')
    }
    const res = await Backstage.listBusinessDataDictionaryInSubProject(param)
    if (res.status.isSuccess()) {
      this.leaderPositionLevelList = res.data.map(item => LeaderPositionLevelVo.from(item))
    }
    return res.status
  }

  /**
   * 获取职称等级的详情列表
   */
  getLeaderPositionLevelByIdList(leaderPositionLevelIdList: Array<string>) {
    const leaderPositionLevelList = new Array<LeaderPositionLevelVo>()
    leaderPositionLevelIdList?.forEach(id => {
      this.leaderPositionLevelList?.forEach(leaderPositionLevel => {
        if (id === leaderPositionLevel.id) {
          leaderPositionLevelList.push(leaderPositionLevel)
        }
      })
    })
    return leaderPositionLevelList
  }
}

export default new QueryLeaderPositionLevel()
