<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">修改手机号</el-button>
        <el-drawer
          title="修改手机号"
          :visible.sync="dialog1"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="手机号：" required>
                    <el-input v-model="form.name" clearable placeholder="请输入11位手机号" />
                  </el-form-item>
                  <el-form-item label="图形验证码：" required>
                    <div class="f-flex">
                      <el-input v-model="form.name" clearable placeholder="请输入图形验证码" class="f-flex-sub" />
                      <div class="code">
                        <img src="./assets/images/code.jpg" title="看不清，点击刷新" />
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="短信校验码：" required>
                    <div class="f-flex">
                      <el-input v-model="form.name" clearable placeholder="请输入短信校验码" class="f-flex-sub" />
                      <div class="code">
                        <el-button type="primary" plain>获取短信验证码</el-button>
                        <!--<el-button type="info" plain disabled>重新获取（60s）</el-button>-->
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确认提交</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">请选择导出方式</el-button>
        <el-drawer
          title="请选择导出方式"
          :visible.sync="dialog2"
          :direction="direction"
          size="520px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20 f-ml20">
              <el-form-item label="导出方式：">
                <el-radio-group v-model="radio">
                  <el-radio :label="3" border class="f-mr10">导出列表详细数据</el-radio>
                  <el-radio :label="6" border>导出学员学习日志</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item class="m-btn-bar f-mt30">
                <el-button>取消</el-button>
                <el-button type="primary">确定</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <el-button @click="dialog3 = true" type="primary" class="f-mr20">导出弹窗-成功</el-button>
        <el-dialog title="提示" :visible.sync="dialog3" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb f-f16">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务查看，选择学员学习日志！</p>
            </div>
          </div>
          <div slot="footer">
            <el-button>暂 不</el-button>
            <el-button type="primary">前往下载</el-button>
          </div>
        </el-dialog>
        <p class="f-mt20">统计相关弹窗 详见 3106_统计报表_弹窗.vue</p>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
