import { DockingTycAndQccResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import { UnitSearchServiceTypeEnum } from '@api/service/customer/online-school-config/online-school-partal-config/query/enum/UnitSearchServiceType'

export default class UnitConfigVo {
  /**
   * 是否启用查询配置
   */
  enableSearch = false

  /**
   * 是否启用企查查
   */
  hasQcc = false

  /**
   * 企查查账号信息
   */
  qccAccount: string = undefined

  /**
   * 企查查密钥
   */
  qccKey: string = undefined

  /**
   * 是否启用天眼
   */
  hasTy = false

  /**
   * 天眼查账号信息
   */
  tyAccount: string = undefined

  static from(dto: DockingTycAndQccResponse) {
    const vo = new UnitConfigVo()
    vo.enableSearch = dto?.enabled
    dto?.dockingInfoList?.length &&
      dto.dockingInfoList.map(item => {
        switch (item.serviceType) {
          case UnitSearchServiceTypeEnum.qccSearch:
            vo.hasQcc = true
            vo.qccAccount = item.dockingAccount
            vo.qccKey = item.secret
            break
          case UnitSearchServiceTypeEnum.tySearch:
            vo.hasTy = true
            vo.tyAccount = item.dockingAccount
            break
        }
      })
    return vo
  }
}
