<route-meta>
  {
  "isMenu": true,
  "title": "个人报名发票",
  "sort": 1
  }
</route-meta>
<script lang="ts">
  import { Component } from 'vue-property-decorator'
  import Invoice from '@hbfe/jxjy-admin-trade/src/invoice/personal/index.vue'
  import SpecialInvoice from '@hbfe/jxjy-admin-trade/src/diff/qztg/invoice/personal/components/special-invoice.vue'
  import ElectronicSpecialInvoice from '@hbfe/jxjy-admin-trade/src/diff/qztg/invoice/personal/components/electronic-special-invoice.vue'

  @Component({
    components: {
      SpecialInvoice,
      ElectronicSpecialInvoice
    }
  })
  export default class extends Invoice {}
</script>
