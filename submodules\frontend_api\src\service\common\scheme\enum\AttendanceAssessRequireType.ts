import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 考勤考核要求类型枚举
 * same_as_scheme 同班级考勤要求
 * custom 自定义
 */
export enum AttendanceAssessRequireTypeEnum {
  same_as_scheme = 'same_as_scheme',
  custom = 'custom'
}

/**
 * @description 考勤考核要求类型
 */
class AttendanceAssessRequireType extends AbstractEnum<AttendanceAssessRequireTypeEnum> {
  static enum = AttendanceAssessRequireTypeEnum

  constructor(status?: AttendanceAssessRequireTypeEnum) {
    super()
    this.current = status
    this.map.set(AttendanceAssessRequireTypeEnum.same_as_scheme, '同班级考勤要求')
    this.map.set(AttendanceAssessRequireTypeEnum.custom, '自定义')
  }
}

export default new AttendanceAssessRequireType()
