<template>
  <div>
    <template v-if="$hasPermission('portal')" desc="门户信息配置" actions="created">
      <el-card shadow="never" class="m-card f-mb15">
        <div class="f-p10">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
              <el-form :model="webPortalInfo" :rules="rules" ref="portalForm" label-width="140px" class="m-form">
                <el-form-item label="平台名称：" prop="title">
                  <el-input
                    v-model="webPortalInfo.title"
                    clearable
                    placeholder="请输入平台名称，学员中心和管理平台登录页面同步显示"
                  />
                </el-form-item>
                <el-form-item label="门户logo：" prop="logo" ref="logoPic">
                  <cropper-img-upload
                    :dialogStyleOpation="{
                      width: '950px',
                      height: '150px'
                    }"
                    :ratioArr="[`${TemplateModuleObj.logoSize[0]}:${TemplateModuleObj.logoSize[1]}`]"
                    :initWidth="TemplateModuleObj.logoSize[0]"
                    title="门户logo"
                    button-text="上传平台名称及logo"
                    :is-long-pic="true"
                    reminder-text="只支持JPG、PNG、GIF"
                    v-model="webPortalInfo.logo"
                    :has-preview="false"
                    :mode="`${TemplateModuleObj.logoSize[0]}px ${TemplateModuleObj.logoSize[1]}px`"
                  >
                    <template slot="tip">
                      <div slot="tip" class="el-upload__tip">
                        <i class="el-icon-warning"></i>
                        <span class="txt">
                          上传的平台名称及LOGO图片，适用于门户顶部。请先设计好后上传，尺寸：宽度
                          {{ TemplateModuleObj.logoSize[0] }}px ， 高度{{ TemplateModuleObj.logoSize[1] }}px。
                          <i class="f-link" @click="logoDialog = true">示例图片</i>
                        </span>
                        <!--示例图片弹窗-->
                        <el-dialog :visible.sync="logoDialog" width="1100px" class="m-dialog-pic">
                          <img src="@design/admin/assets/images/demo-web-logo.png" alt="" />
                        </el-dialog>
                      </div>
                    </template>
                  </cropper-img-upload>
                </el-form-item>
                <el-form-item label="浏览器图标：" prop="icon" ref="iconPic" :error="uploadIconInfo">
                  <upload-images
                    v-model="icon"
                    :limit="1"
                    imgType=".ico"
                    content-text="上传浏览器图标"
                    :iconSizeList="TemplateModuleObj.iconSize"
                  ></upload-images>
                </el-form-item>
                <el-form-item label="客服电话图片：">
                  <cropper-img-upload
                    :dialogStyleOpation="{
                      width: '350px',
                      height: '100px'
                    }"
                    :ratioArr="[`${TemplateModuleObj.customerPhoneSize[0]}:${TemplateModuleObj.customerPhoneSize[1]}`]"
                    :initWidth="TemplateModuleObj.customerPhoneSize[0]"
                    title="客服电话"
                    button-text="上传客服电话图片"
                    :is-long-pic="true"
                    reminder-text="只支持JPG、PNG、GIF"
                    v-model="webPortalInfo.CSPhonePicture"
                    :has-preview="false"
                    :mode="`${TemplateModuleObj.customerPhoneSize[0]}px ${TemplateModuleObj.customerPhoneSize[1]}px`"
                  >
                    <template slot="tip">
                      <div slot="tip" class="el-upload__tip">
                        <i class="el-icon-warning"></i>
                        <span class="txt">
                          上传客服电话图片，请先设计好后再上传，尺寸：宽度{{ TemplateModuleObj.customerPhoneSize[0] }}px
                          ，高度 {{ TemplateModuleObj.customerPhoneSize[1] }}px。
                          <i class="f-link" @click="customerPhoneDialog = true">示例图片</i>
                        </span>
                        <!--示例图片弹窗-->
                        <el-dialog :visible.sync="customerPhoneDialog" width="1100px" class="m-dialog-pic">
                          <img src="@design/admin/assets/images/demo-tel-head.png" alt="" />
                        </el-dialog>
                      </div>
                    </template>
                  </cropper-img-upload>
                </el-form-item>
                <el-form-item label="客服电话：">
                  <el-input
                    v-model="webPortalInfo.CSPhone"
                    clearable
                    placeholder="请输入客服电话，同步展示在web端和h5"
                    class="form-l"
                  />
                  <div class="el-upload__tip">
                    <i class="el-icon-warning"></i>
                    <span class="txt">
                      输入网校门户右侧悬停区域显示的客服电话，如需展示多个客服电话，请用顿号“、”分隔。例如96882301、96882302
                    </span>
                  </div>
                </el-form-item>
                <el-form-item label="咨询时间：">
                  <el-input
                    v-model="webPortalInfo.CSCallTime"
                    clearable
                    class="form-l"
                    placeholder="周一至周五 (09:00 - 12:00 14:00 - 17:30)"
                  />
                </el-form-item>
                <el-form-item label="企业微信客服：">
                  <cropper-img-upload
                    :key="1"
                    :dialogStyleOpation="{
                      width: '300px',
                      height: '300px'
                    }"
                    :ratioArr="[
                      `${TemplateModuleObj.wechatCustomerServiceSize[0]}:${TemplateModuleObj.wechatCustomerServiceSize[1]}`
                    ]"
                    :initWidth="TemplateModuleObj.wechatCustomerServiceSize[0]"
                    title="企业微信客服二维码："
                    button-text="上传企业微信客服图片"
                    reminder-text="只支持JPG、PNG、GIF"
                    v-model="webPortalInfo.enterPriseWxCustomer"
                    :has-preview="false"
                  >
                    <template slot="tip">
                      <div slot="tip" class="el-upload__tip">
                        <i class="el-icon-warning"></i>
                        <span class="txt"
                          >上传企业微信客服图片，尺寸：宽度{{ TemplateModuleObj.wechatCustomerServiceSize[0] }}px ，
                          高度{{ TemplateModuleObj.wechatCustomerServiceSize[1] }}px。</span
                        >
                      </div>
                    </template>
                  </cropper-img-upload>
                </el-form-item>
                <el-form-item label="移动学习二维码：">
                  <el-radio v-model="webPortalInfo.mobileQRCodeSign" :label="1" border class="f-mr10"
                    >系统默认</el-radio
                  >
                  <el-radio v-model="webPortalInfo.mobileQRCodeSign" :label="2" border class="f-mr10">自定义</el-radio>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>网校门户移动端推广二维码展示配置说明：</p>
                      <p>
                        若需要使用系统生成的平台H5页面访问网校，请选择“系统默认”类型；若需要使用微信公众号访问网校，请选择“自定义”类型。
                      </p>
                    </div>
                  </el-tooltip>
                  <div class="m-upload-item" v-if="webPortalInfo.mobileQRCodeSign === 1">
                    <div class="img-file">
                      <img
                        class="el-upload-list__item-thumbnail"
                        :src="systemQrCode"
                        alt=""
                        style="width: 146px; height: 146px"
                      />
                    </div>
                  </div>
                  <template v-if="webPortalInfo.mobileQRCodeSign === 2">
                    <cropper-img-upload
                      :key="2"
                      :dialogStyleOpation="{
                        width: '300px',
                        height: '300px'
                      }"
                      :ratioArr="[`${TemplateModuleObj.H5QRCodeSize[0]}:${TemplateModuleObj.H5QRCodeSize[1]}`]"
                      :initWidth="TemplateModuleObj.H5QRCodeSize[0]"
                      title="移动学习二维码"
                      button-text="上传移动学习图片"
                      reminder-text="只支持JPG、PNG、GIF"
                      v-model="webPortalInfo.mobileQRCode"
                      :has-preview="false"
                      :mode="`${TemplateModuleObj.H5QRCodeSize[0]}px ${TemplateModuleObj.H5QRCodeSize[1]}px`"
                    >
                      <template slot="tip">
                        <div slot="tip" class="el-upload__tip">
                          <i class="el-icon-warning"></i>
                          <span class="txt"
                            >上传移动学习二维码图片，尺寸：宽度{{ TemplateModuleObj.H5QRCodeSize[0] }}px ， 高度{{
                              TemplateModuleObj.H5QRCodeSize[1]
                            }}px。</span
                          >
                        </div>
                      </template>
                    </cropper-img-upload>
                  </template>
                </el-form-item>
                <el-form-item label="培训流程：">
                  <cropper-img-upload
                    :dialogStyleOpation="{
                      width: '1300px',
                      height: '200px'
                    }"
                    :ratioArr="[
                      `${TemplateModuleObj.trainingProcessSize[0]}:${TemplateModuleObj.trainingProcessSize[1]}`
                    ]"
                    :initWidth="TemplateModuleObj.trainingProcessSize[0]"
                    title="图片设置"
                    button-text="上传培训流程图片"
                    :is-long-pic="true"
                    reminder-text="只支持JPG、PNG、GIF"
                    v-model="webPortalInfo.trainingFlowPicture"
                    :has-preview="false"
                    :mode="`${TemplateModuleObj.trainingProcessSize[0]}px ${TemplateModuleObj.trainingProcessSize[1]}px`"
                  >
                    <template slot="tip">
                      <div slot="tip" class="el-upload__tip">
                        <i class="el-icon-warning"></i>
                        <span class="txt">
                          上传培训流程图片，尺寸：宽度{{ TemplateModuleObj.trainingProcessSize[0] }}px ， 高度{{
                            TemplateModuleObj.trainingProcessSize[1]
                          }}px。
                          <i class="f-link" @click="trainingStepsDialog = true">示例图片</i>
                        </span>
                        <!--示例图片弹窗-->
                        <el-dialog :visible.sync="trainingStepsDialog" width="1100px" class="m-dialog-pic">
                          <img src="@design/admin/assets/images/demo-pic-process.jpg" alt="" />
                        </el-dialog>
                      </div>
                    </template>
                  </cropper-img-upload>
                </el-form-item>
                <el-form-item label="在线客服代码：">
                  <el-input v-model="webPortalInfo.CSOnlineCodeId" type="textarea" placeholder="请输入内容" :rows="6" />
                </el-form-item>
                <el-form-item label="底部落款：">
                  <div class="rich-text">
                    <hb-tinymce-editor v-model="webPortalInfo.footContent" v-if="show"></hb-tinymce-editor>
                  </div>
                </el-form-item>
                <el-form-item label="友情链接显示方式：">
                  <el-radio-group v-model="webPortalInfo.friendLinkType" @change="changeLinkType">
                    <el-radio @click.native.prevent="clickItem(1)" :label="1">文本</el-radio>
                    <el-radio @click.native.prevent="clickItem(2)" :label="2">图片</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="友情链接：" v-show="webPortalInfo.friendLinkType == 1">
                  <div class="item f-mb10" v-for="(item, index) in webPortalInfo.friendLinkList" :key="index">
                    <el-input
                      v-model="item.title"
                      clearable
                      placeholder="请输入友情链接文字信息，最多15字"
                      class="form-m"
                      maxlength="15"
                      show-word-limit
                    />
                    <span class="f-pl20">链接地址：</span>
                    <el-input v-model="item.link" clearable placeholder="请输入链接地址" class="form-s" />
                    <el-button type="danger" class="f-ml10" plain @click="deleteFriendLink(index)">删除</el-button>
                  </div>
                  <el-button type="primary" @click="addFriendLink" icon="el-icon-plus" plain>添加</el-button>
                </el-form-item>
                <el-form-item label="友情链接：" v-show="webPortalInfo.friendLinkType == 2">
                  <div v-for="(item, index) in webPortalInfo.friendLinkList" :key="index">
                    <cropper-img-upload
                      :dialogStyleOpation="{
                        width: '500px',
                        height: '150px'
                      }"
                      :ratioArr="[`${TemplateModuleObj.linksSize[0]}:${TemplateModuleObj.linksSize[1]}`]"
                      :initWidth="TemplateModuleObj.linksSize[0]"
                      :initHeight="TemplateModuleObj.linksSize[1]"
                      title="图片设置"
                      button-text="上传图片"
                      :is-long-pic="true"
                      :is-upload-item="true"
                      reminder-text="只支持JPG、PNG、GIF"
                      v-model="item.picture"
                      :has-preview="false"
                    >
                      <template slot="other">
                        <div class="other">
                          <p>链接地址</p>
                          <div class="f-flex">
                            <el-input v-model="item.link" clearable class="f-flex-sub" placeholder="请输入链接地址" />
                            <el-button type="danger" class="f-ml10" plain @click="deleteFriendLink(index)"
                              >删除</el-button
                            >
                          </div>
                        </div>
                      </template>
                    </cropper-img-upload>
                    <div class="el-upload__tip">
                      <i class="el-icon-warning"></i>
                      <span class="txt">
                        底部链接图片，尺寸：{{ TemplateModuleObj.linksSize[0] }}px *
                        {{ TemplateModuleObj.linksSize[1] }}px。
                      </span>
                    </div>
                  </div>
                  <el-button type="primary" @click="addFriendLink" icon="el-icon-plus" class="f-mt10" plain
                    >添加</el-button
                  >
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <template v-if="$hasPermission('editPortal')" desc="门户信息配置（编辑）" actions="validForm">
        <div class="m-btn-bar f-tc is-sticky-1">
          <el-button @click="doCancel">取消</el-button>
          <el-button type="primary" @click="validForm" :loading="isLoading">保存</el-button>
        </div>
      </template>
      <el-dialog title="提示" :visible.sync="saveSuccessVisible" width="450px" class="m-dialog">
        <div class="dialog-alert">
          <i class="icon el-icon-warning warning"></i>
          <span class="txt">当前门户已完成配置，开启网校对外访问后可通过网校域名进入。是否开启网校对外访问？</span>
        </div>
        <div slot="footer">
          <el-button @click="saveSuccessVisible = false">暂不开启</el-button>
          <el-button type="primary" @click="openSchoolVisit">确 定</el-button>
        </div>
      </el-dialog>
      <give-up-dialog :give-up-model="uiConfig.giveUpModel" @callBack="resetData"></give-up-dialog>
    </template>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Watch, Prop } from 'vue-property-decorator'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import CropperImgUpload from '@hbfe/jxjy-admin-platform/src/function/components/cropper-img-upload.vue'
  import UploadImages from '@hbfe/jxjy-admin-platform/src/function/components/upload-images.vue'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import LinkVo from '@api/service/common/online-school-config/vo/LinkVo'
  import PortalVo from '@api/service/common/online-school-config/vo/PortalVo'
  import TemplateModule from '@api/service/common/template-school/TemplateModule'
  import TemplateItem from '@api/service/common/template-school/TemplateItem'
  import QRCode from 'qrcode'

  export class UploadImageFile {
    name: string
    url: string
  }

  @Component({
    components: { CropperImgUpload, GiveUpDialog, UploadImages }
  })
  export default class extends Vue {
    @Ref('portalForm') portalForm: any
    @Prop({
      required: true,
      default: () => {
        return new TemplateItem()
      }
    })
    TemplateModuleObj: TemplateItem
    isLoading = false
    //门户示例图片
    logoDialog = false
    //客服电话示例图片
    customerPhoneDialog = false
    //培训流程示例图片
    trainingStepsDialog = false

    //web网校访问开关
    webAccess = false

    webPortalInfo: PortalVo = OnlineSchoolConfigModule.queryPortal.webPortalInfo

    url = ''
    image = ''

    // 富文本有时候显示不出来
    show = false

    //保存成功弹窗
    saveSuccessVisible = false
    /**
     * 系统二维码
     */
    systemQrCode = window.location.origin + '/h5?source=scan'

    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }

    //文本类友情链接暂存
    txtLinkList: Array<LinkVo> = new Array<LinkVo>()
    //图片类友情链接暂存
    pictureLinkList: Array<LinkVo> = new Array<LinkVo>()

    icon: Array<UploadImageFile> = new Array<UploadImageFile>()

    // 上传浏览器图标手动校验信息
    uploadIconInfo = ''

    rules = {
      title: [{ required: true, message: '请填写平台名称', trigger: ['change', 'blur'] }],
      logo: [{ required: true, message: '请上传门户logo', trigger: 'blur' }],
      icon: [{ required: true, message: '请上传浏览器图标', trigger: 'blur' }]
    }

    @Watch('webPortalInfo.logo', {
      immediate: true,
      deep: true
    })
    changeLogoUrl(val: string) {
      const el: any = this.$refs['logoPic']
      if (el) {
        if (val) {
          //有图片时清除校验
          el.clearValidate()
        } else {
          el.validate()
        }
      }
    }

    @Watch('icon', {
      immediate: true,
      deep: true
    })
    changeIconUrl(val: any) {
      const el: any = this.$refs['iconPic']
      if (el) {
        if (val?.length) {
          //有图片时清除校验
          this.webPortalInfo.icon = val[0].url
          el.clearValidate()
        } else {
          this.icon = undefined
          this.webPortalInfo.icon = null
          el.validate()
        }
      }
    }

    //验证表单
    validForm() {
      this.portalForm.validate((valid: boolean) => {
        if (valid) {
          let curIndex = -1
          if (this.webPortalInfo?.friendLinkType == 1) {
            this.webPortalInfo.friendLinkList.forEach((item, index) => {
              if (item.title) {
                curIndex = index
              }
            })
            if (curIndex != -1 && this.webPortalInfo?.friendLinkList.slice(0, curIndex).some((link) => !link.title)) {
              this.$message.warning('请填写友情链接')
              return
            }
          } else if (this.webPortalInfo?.friendLinkType == 2) {
            this.webPortalInfo.friendLinkList.forEach((item, index) => {
              if (item.picture) {
                curIndex = index
              }
            })
            if (curIndex != -1 && this.webPortalInfo?.friendLinkList.slice(0, curIndex).some((link) => !link.picture)) {
              this.$message.warning('请上传底部链接图片')
              return
            }
          }
          if (curIndex == -1) {
            this.webPortalInfo.friendLinkList = new Array<LinkVo>()
          } else {
            this.webPortalInfo.friendLinkList = Object.assign(
              new Array<LinkVo>(),
              this.webPortalInfo.friendLinkList.slice(0, curIndex + 1)
            )
          }
          // 浏览器图标必传拦截
          this.uploadIconInfo = this.icon.length && this.icon[0].url ? '' : '请上传浏览器图标'
          if (this.uploadIconInfo) return
          this.doSave()
        }
      })
    }

    //保存
    async doSave() {
      this.isLoading = true
      try {
        this.webPortalInfo.compulsionUpdate = true
        if (this.webPortalInfo.mobileQRCodeSign === 2 && !this.webPortalInfo.mobileQRCode) {
          this.isLoading = false
          return this.$message.error('请上传移动学习二维码')
        }
        const res = await OnlineSchoolConfigModule.mutationPortal.doSave()
        if (res.isSuccess()) {
          this.$message.success('保存门户信息配置成功')
          this.isLoading = false
          if (this.webPortalInfo.friendLinkType == 1) {
            this.pictureLinkList = new Array<LinkVo>()
          } else if (this.webPortalInfo.friendLinkType == 2) {
            this.txtLinkList = new Array<LinkVo>()
          }
          //await this.getPortalConfig()
        } else {
          this.$message.error('保存门户信息配置失败')
          this.isLoading = false
        }
        this.getAccessConfig()
        if (!this.webAccess) {
          this.saveSuccessVisible = true
        }
      } catch (e) {
        console.log(e)
        this.$message.error('保存门户信息配置失败')
        this.isLoading = false
      }
    }

    //重置数据
    async resetData() {
      await this.getPortalConfig()
    }

    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
    }

    //添加友情链接
    addFriendLink() {
      this.webPortalInfo.friendLinkList.push(new LinkVo())
    }

    //删除友情链接
    deleteFriendLink(index: number) {
      this.webPortalInfo.friendLinkList.splice(index, 1)
    }

    //切换链接方式
    changeLinkType() {
      console.log(this.webPortalInfo.friendLinkType, '当前类型')

      if (this.webPortalInfo.friendLinkType == 1) {
        this.pictureLinkList = this.webPortalInfo.friendLinkList
        this.webPortalInfo.friendLinkList = this.txtLinkList
      } else if (this.webPortalInfo.friendLinkType == 2) {
        this.txtLinkList = this.webPortalInfo.friendLinkList
        this.webPortalInfo.friendLinkList = this.pictureLinkList
      }
    }

    // 切换radio,重复点击清空当前选中
    clickItem(e: number) {
      e === this.webPortalInfo.friendLinkType
        ? (this.webPortalInfo.friendLinkType = 0)
        : (this.webPortalInfo.friendLinkType = e)
    }

    //开启网校对外访问
    async openSchoolVisit() {
      await this.editAccessConfig(1, true)
      this.$emit('callBack')
      this.saveSuccessVisible = false
    }
    //获取门户信息配置
    async getPortalConfig() {
      const res = await OnlineSchoolConfigModule.queryPortal.queryDetail()
      if (res.isSuccess()) {
        console.log(this.webPortalInfo)
        if (this.webPortalInfo.icon) {
          this.icon = new Array<UploadImageFile>()
          this.icon.push(new UploadImageFile())
          this.icon[0].url = this.webPortalInfo.icon
          this.icon[0].name = this.webPortalInfo.icon
        }
        if (this.webPortalInfo?.friendLinkList?.length == 0) {
          this.webPortalInfo.friendLinkList.push(new LinkVo())
        }
        this.nullToString()
      }
    }

    async editAccessConfig(type: number, isAccess: boolean) {
      const res = await OnlineSchoolConfigModule.mutationPortal.handleAccess(type, isAccess)
      if (res.isSuccess()) {
        this.$message.success('修改成功')
      } else {
        this.$message.error('修改失败')
      }
    }

    getAccessConfig() {
      /*const res = await OnlineSchoolConfigModule.queryPortal.queryDetail()
      if (res.isSuccess()) {*/
      this.webAccess = OnlineSchoolConfigModule.queryPortal.webAccess
      //}
    }

    nullToString() {
      this.webPortalInfo.logo = this.webPortalInfo.logo ? this.webPortalInfo.logo : ''
      this.webPortalInfo.CSPhonePicture = this.webPortalInfo.CSPhonePicture ? this.webPortalInfo.CSPhonePicture : ''
      this.webPortalInfo.mobileQRCode = this.webPortalInfo.mobileQRCode ? this.webPortalInfo.mobileQRCode : ''
      this.webPortalInfo.trainingFlowPicture = this.webPortalInfo.trainingFlowPicture
        ? this.webPortalInfo.trainingFlowPicture
        : ''
    }

    async activated() {
      this.show = false
      setTimeout(() => {
        this.show = true
      }, 300)
    }

    async created() {
      await this.getPortalConfig()
      this.systemQrCode = await QRCode.toDataURL(this.systemQrCode)
      if (this.webPortalInfo.icon) {
        this.icon = new Array<UploadImageFile>()
        this.icon.push(new UploadImageFile())
        this.icon[0].url = this.webPortalInfo.icon
        this.icon[0].name = this.webPortalInfo.icon
      }
      if (this.webPortalInfo?.friendLinkList?.length == 0) {
        this.webPortalInfo.friendLinkList.push(new LinkVo())
      }
      this.nullToString()
    }
  }
</script>
