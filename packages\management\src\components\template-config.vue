<template>
  <div>
    <el-tabs v-model="activeName" type="card" class="m-tab-card">
      <el-tab-pane v-if="schoolDetail.schoolBase.haveRSIndustry" label="人社行业" name="first">
        <template-industry
          :schoolDetail="schoolDetail"
          industryType="rs"
          @getList="getList"
          :pcCheckedList="pcCheckedList"
          :h5CheckedList="h5CheckedList"
        ></template-industry>
      </el-tab-pane>
      <el-tab-pane v-if="schoolDetail.schoolBase.haveJSIndustry" label="建设行业" name="second">
        <template-industry
          :schoolDetail="schoolDetail"
          industryType="js"
          @getList="getList"
          :pcCheckedList="pcCheckedList"
          :h5CheckedList="h5CheckedList"
        ></template-industry>
      </el-tab-pane>
      <el-tab-pane v-if="schoolDetail.schoolBase.haveWSIndustry" label="职业卫生行业" name="third">
        <template-industry
          :schoolDetail="schoolDetail"
          industryType="ws"
          @getList="getList"
          :pcCheckedList="pcCheckedList"
          :h5CheckedList="h5CheckedList"
        ></template-industry>
      </el-tab-pane>
      <el-tab-pane v-if="schoolDetail.schoolBase.haveGQIndustry" label="工勤行业" name="fourth">
        <template-industry
          :schoolDetail="schoolDetail"
          industryType="gq"
          @getList="getList"
          :pcCheckedList="pcCheckedList"
          :h5CheckedList="h5CheckedList"
        ></template-industry>
      </el-tab-pane>
      <el-tab-pane v-if="schoolDetail.schoolBase.haveLSIndustry" label="教师行业" name="fifth">
        <template-industry
          :schoolDetail="schoolDetail"
          industryType="ls"
          @getList="getList"
          :pcCheckedList="pcCheckedList"
          :h5CheckedList="h5CheckedList"
        ></template-industry>
      </el-tab-pane>
      <el-tab-pane v-if="schoolDetail.schoolBase.haveYSIndustry" label="药师行业" name="sixth">
        <template-industry
          :schoolDetail="schoolDetail"
          industryType="ys"
          @getList="getList"
          :pcCheckedList="pcCheckedList"
          :h5CheckedList="h5CheckedList"
        ></template-industry>
      </el-tab-pane>
    </el-tabs>
    <div class="m-btn-bar f-tc is-sticky-1" style="z-index: 1001">
      <el-button @click="handleReJump">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </div>
    <el-dialog title="系统提醒" :visible.sync="dialog2" width="400px" class="m-dialog">
      <div>
        当前网校<span class="f-cb">【{{ schoolDetail.schoolBase.schoolName }}】</span
        >变更了网校模板，变更模板后对应布局及已配置内容将会清空，是否确认变更模板？<br />
        如若确认更换，请与网校做好沟通，并告知网校门户前端暂停开放。
      </div>
      <div slot="footer">
        <el-button @click="dialog2 = false">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import OnlineSchoolModule from '@api/service/training-institution/online-school/OnlineSchoolModule'
  import OnlineSchoolModel from '@api/service/training-institution/online-school/models/OnlineSchoolModel'
  import TemplateModule from '@api/service/common/template-school/TemplateModule'
  import templateIndustry from '@hbfe/jxjy-admin-management/src/components/template-industry-pane.vue'
  import { debounce, bind } from 'lodash-decorators'
  @Component({
    components: {
      templateIndustry
    }
  })
  export default class extends Vue {
    @Prop({
      required: true,
      default: () => {
        return new OnlineSchoolModel()
      }
    })
    // 网校信息
    schoolDetail: OnlineSchoolModel
    TemplateConfigObj = TemplateModule
    onlineSchoolObj: OnlineSchoolModule = new OnlineSchoolModule()
    activeName = 'first'
    pcCheckedList: Array<string> = []
    h5CheckedList: Array<string> = []
    dialog2 = false
    @Watch('schoolDetail', {
      immediate: true,
      deep: true
    })
    watchValue(val?: any) {
      this.pcCheckedList = [this.schoolDetail.schoolConfig.webPortalTemplateId]
      this.h5CheckedList = [this.schoolDetail.schoolConfig.H5PortalTemplateId]
      if (this.schoolDetail.schoolBase.haveRSIndustry) {
        this.activeName = 'first'
      } else if (this.schoolDetail.schoolBase.haveJSIndustry) {
        this.activeName = 'second'
      } else if (this.schoolDetail.schoolBase.haveWSIndustry) {
        this.activeName = 'third'
      } else if (this.schoolDetail.schoolBase.haveGQIndustry) {
        this.activeName = 'fourth'
      } else if (this.schoolDetail.schoolBase.haveLSIndustry) {
        this.activeName = 'fifth'
      } else if (this.schoolDetail.schoolBase.haveYSIndustry) {
        this.activeName = 'sixth'
      }
    }
    // created() {
    // }
    //弹窗
    handleSubmit() {
      if (this.schoolDetail.schoolConfig.provideH5Service && !this.h5CheckedList.length) {
        this.$alert('H5端请至少选择一个模板', '提示')
        return
      }
      if (this.schoolDetail.schoolConfig.provideWebService && !this.pcCheckedList.length) {
        this.$alert('PC端请至少选择一个模板', '提示')
        return
      }
      this.dialog2 = true
    }
    //保存模板修改
    @bind
    @debounce(200)
    async handleSave() {
      this.onlineSchoolObj.onlineSchool = new OnlineSchoolModel()
      this.onlineSchoolObj.onlineSchool.id = this.schoolDetail.id
      this.onlineSchoolObj.onlineSchool.schoolConfig.webPortalTemplateId = this.pcCheckedList[0]
      // 暂时H5没有模板
      this.onlineSchoolObj.onlineSchool.schoolConfig.H5PortalTemplateId = this.h5CheckedList[0] || 'TestH5TemplateId-1'

      const result = await this.onlineSchoolObj.updateSchoolTemplate()
      if (result.status.code == 200) {
        this.$message.success('保存成功！')
        this.$emit('update', this.onlineSchoolObj.onlineSchool)
      }
      this.dialog2 = false
    }
    handleReJump() {
      this.dialog2 = false
      this.$router.push('/school-management/management')
    }
    getList(pclist: Array<string>, h5list: Array<string>) {
      this.pcCheckedList = pclist
      this.h5CheckedList = h5list
    }
  }
</script>
<style lang="scss" scoped>
  .el-collapse-item__header {
    font-size: 16px;
    font-weight: bold;
    padding-left: 20px;
    padding-right: 10px;
  }
  //模板配置-模版图片列表
  .m-demo-pic {
    li {
      &:nth-child(3n) {
        margin-right: 0;
      }
    }
    .demo-pic-info {
      height: 124px;
      color: #666;
      font-size: 14px;
      // p {
      //   line-height: 18px;
      //   margin-bottom: 8px;

      //   .t {
      //     display: inline-block;
      //     font-weight: bold;
      //   }

      //   &:last-child {
      //     margin-bottom: 0;
      //   }
      // }
      // }
    }
    .phone-text {
      height: 20px;
    }
    .template-config--pic-color {
      display: flex;
      align-items: center;
      .t {
        font-size: 14px;
        color: #666;
        font-weight: bold;
        width: 70px;
      }
      .color-template-con {
        display: flex;
        width: 200px;
        justify-content: space-evenly;
        .color-tem {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          cursor: pointer;
          // margin: 0 20px;
        }
      }
    }
  }
</style>
