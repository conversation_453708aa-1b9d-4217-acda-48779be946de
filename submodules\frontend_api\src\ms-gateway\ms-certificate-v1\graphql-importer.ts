import determineCertificateTemplateIsNeedPhoto from './queries/determineCertificateTemplateIsNeedPhoto.graphql'
import findCertificateTemplate from './queries/findCertificateTemplate.graphql'
import findImportPrintTaskExecuteResponsePage from './queries/findImportPrintTaskExecuteResponsePage.graphql'
import findSnapShotByCertificateId from './queries/findSnapShotByCertificateId.graphql'
import findTaskExecuteResponsePage from './queries/findTaskExecuteResponsePage.graphql'
import findUserIdentificationPhotoByUserId from './queries/findUserIdentificationPhotoByUserId.graphql'
import queryExportTaskResponsePage from './queries/queryExportTaskResponsePage.graphql'
import verifyPhotoIsUpload from './queries/verifyPhotoIsUpload.graphql'
import anewConsumeCertificateEvent from './mutates/anewConsumeCertificateEvent.graphql'
import batchPrintCertificates from './mutates/batchPrintCertificates.graphql'
import certificateNumberRecordUpdateCrypto from './mutates/certificateNumberRecordUpdateCrypto.graphql'
import certificateSnapshotUpdateCrypto from './mutates/certificateSnapshotUpdateCrypto.graphql'
import certificateUpdateCrypto from './mutates/certificateUpdateCrypto.graphql'
import configureElectronicSeal from './mutates/configureElectronicSeal.graphql'
import createCertificateDimensionalCode from './mutates/createCertificateDimensionalCode.graphql'
import createCertificateElectronicSeal from './mutates/createCertificateElectronicSeal.graphql'
import createCertificateTemplate from './mutates/createCertificateTemplate.graphql'
import getCertificateSnapShot from './mutates/getCertificateSnapShot.graphql'
import printCertificate from './mutates/printCertificate.graphql'
import printCertificateTemplate from './mutates/printCertificateTemplate.graphql'
import rebuildCertificateNo from './mutates/rebuildCertificateNo.graphql'
import scanQrCodeGenerateCertificate from './mutates/scanQrCodeGenerateCertificate.graphql'
import uploadPhoto from './mutates/uploadPhoto.graphql'

export {
  determineCertificateTemplateIsNeedPhoto,
  findCertificateTemplate,
  findImportPrintTaskExecuteResponsePage,
  findSnapShotByCertificateId,
  findTaskExecuteResponsePage,
  findUserIdentificationPhotoByUserId,
  queryExportTaskResponsePage,
  verifyPhotoIsUpload,
  anewConsumeCertificateEvent,
  batchPrintCertificates,
  certificateNumberRecordUpdateCrypto,
  certificateSnapshotUpdateCrypto,
  certificateUpdateCrypto,
  configureElectronicSeal,
  createCertificateDimensionalCode,
  createCertificateElectronicSeal,
  createCertificateTemplate,
  getCertificateSnapShot,
  printCertificate,
  printCertificateTemplate,
  rebuildCertificateNo,
  scanQrCodeGenerateCertificate,
  uploadPhoto
}
