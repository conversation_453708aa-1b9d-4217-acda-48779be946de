/**
 * 期数方案信息
 */
import { CommodityInfoDTO } from '@api/gateway/PlatformTrade'
import moment from 'moment'
import { Constants } from '@api/service/common/models/common/Constants'

export class IssueLsInfo {
  /**
   * 期别学习开始时间
   */
  startTime: Date
  /**
   * 期别学习结束时间
   */
  endTime: Date
  /**
   * 学习方案ID（该项目中学习方案代表培训班）
   */
  schemeId: string
  /**
   * 学习方案名称（该项目中学习方案代表培训班）
   */
  schemeName: string
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训对象
   */
  traineesId: string
  /**
   * 岗位类别
   */
  jobCategoryId: string
  /**
   * 单位类别
   */
  unitCategoryId: string
  /**
   * 学时
   * @param dto
   */
  period: number
  /**
   * 商品skuid
   */
  commoditySkuId: string
  /**
   * 期数ID  (前端需要取这个id做期别id)
   */
  issueId: string

  static from(dto: CommodityInfoDTO): IssueLsInfo {
    const info = new IssueLsInfo()
    // info.startTime = moment(dto.startTime, Constants.DATE_PATTERN).toDate()
    // info.endTime = moment(dto.endTime, Constants.DATE_PATTERN).toDate()
    // info.schemeId = dto.schemeId
    // info.schemeName = dto.schemeName
    // info.year = dto.year
    // info.trainingTypeId = dto.trainingTypeId
    // info.traineesId = dto.workTypeId
    // // info.jobCategoryId = dto.jobCategoryId
    // // info.unitCategoryId = dto.unitCategoryId
    // info.period = dto.period
    // info.issueId = dto.issueId
    // info.commoditySkuId = dto.commoditySkuId
    return info
  }
}
