"""独立部署的微服务,K8S服务名:ms-order-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	applyEnterSaleChannel(request:ApplyEnterSaleChannelRequest):ApplyEnterSaleChannelResponse
	calculateOrderPrice(request:CalculateOrderPriceRequest):CalculateOrderPriceResponse
}
"""<AUTHOR>
	@date 2024/8/16 10:38
"""
input ApplyEnterSaleChannelRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.marketing.ApplyEnterSaleChannelRequest") {
	"""渠道类型，由枚举类ChannelType定义。
		<p>
		自营渠道 0
		分销渠道 1
		专题渠道 2
		华医网 3
		推广门户渠道 4
		@see ChannelType
	"""
	channelType:Int
	"""渠道类型ID，具体含义取决于渠道类型：
		- 自营：网店ID；
		- 专题：专题ID；
		- 分销：分销商ID；
		- 推广门户：推广门户唯一标识
	"""
	channelTypeId:String
}
"""<AUTHOR>
	@date 2024/8/16 10:38
"""
input CalculateOrderPriceRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.marketing.CalculateOrderPriceRequest") {
	"""销售渠道购买凭证token,目前为销售渠道id"""
	saleChannelPurchaseToken:String
	"""商品id"""
	commodityRequestList:[CommodityRequest]
}
input CommodityRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.marketing.CommodityRequest") {
	"""商品数量"""
	quantity:Int!
	"""商品skuId"""
	commoditySkuId:String
	"""商品使用的价格策略类型 1-定价策略 2-优惠策略"""
	policyType:Int
	"""策略id
		若使用定价策略则为定价策略id,若使用优惠策略则为优惠策略id
	"""
	policyId:String
}
"""<AUTHOR>
	@date 2024/8/16 10:38
"""
type ApplyEnterSaleChannelResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.marketing.ApplyEnterSaleChannelResponse") {
	"""销售渠道购买凭证,目前为销售渠道id"""
	saleChannelPurchaseToken:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""<AUTHOR>
	@date 2024/8/16 10:38
"""
type CalculateOrderPriceResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.marketing.CalculateOrderPriceResponse") {
	"""子订单价格信息"""
	subOrderPriceInfoList:[SubOrderPriceInfo]
	"""总价格"""
	totalAmount:BigDecimal
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
type SubOrderPriceInfo @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.marketing.CalculateOrderPriceResponse$SubOrderPriceInfo") {
	"""子订单订单价格"""
	amount:BigDecimal
	"""商品skuId"""
	commoditySkuId:String
}

scalar List
