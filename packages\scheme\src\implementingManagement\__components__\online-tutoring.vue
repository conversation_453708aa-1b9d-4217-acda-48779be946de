<template>
  <div v-if="$hasPermission('trainingResultListQuery')" desc="网授培训成果查询" actions="created">
    <el-card shadow="never" class="m-card">
      <!--表格-->
      <el-table ref="table" stripe :data="tableData" v-loading="uiLoading.tableLoading" class="m-table">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column
          label="方案名称"
          width="200"
          fixed="left"
          show-overflow-tooltip
          prop="trainClassDetail.commodityBasicData.saleTitle"
        >
        </el-table-column>
        <el-table-column label="方案属性" min-width="240">
          <template #default="{ row }">
            <div v-html="classSkuList(row)"></div>
          </template>
        </el-table-column>
        <el-table-column label="已报名人数" min-width="110" align="center">
          <template #default="{ row }">
            {{ row.learningStatistic.netRegisterCount || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="合格人数" min-width="110" align="center">
          <template #default="{ row }">
            {{ row.learningStatistic.qualifiedCount || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="报名起止时间" min-width="220">
          <template #default="{ row }">
            <p>
              <el-tag type="info" size="mini">起始</el-tag>
              {{ row.trainClassDetail.registerBeginDate || '--' }}
            </p>
            <p>
              <el-tag type="info" size="mini">结束</el-tag>
              {{ row.trainClassDetail.registerEndDate || '--' }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="学习起止时间" min-width="220">
          <template #default="{ row }">
            <template v-if="row.trainClassDetail.trainingBeginDate !== '1900-01-01 00:00:00'">
              <p>
                <el-tag type="info" size="mini">起始</el-tag>
                {{ row.trainClassDetail.trainingBeginDate || '--' }}
              </p>
              <p>
                <el-tag type="info" size="mini">结束</el-tag>
                {{ row.trainClassDetail.trainingEndDate || '--' }}
              </p>
            </template>
            <template v-else> 长期有效</template>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="$hasPermission('viewOnlineLearningStatistic')"
              desc="查看网授学员学习明细权限"
              actions="@/unit-share/network-school/statistic/learning-statistic"
              type="text"
              @click="viewOnlineLearningStatistic(row)"
              >查看学员学习明细
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Prop } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
  import SkuVo from '@api/service/management/train-class/query/vo/SkuVo'
  import { ElTable } from 'element-ui/types/table'
  import { QuerySchemeLearningList } from '@api/service/management/statisticalReport/query/QuerySchemeLearningList'
  import { SchemeLearningStatisticsResponseVo } from '@api/service/management/statisticalReport/query/vo/SchemeLearningStatisticsResponseVo'
  import { CommoditySkuRequestVo } from '@api/service/management/statisticalReport/query/vo/CommoditySkuRequestVo'
  import {
    DateScopeRequest,
    RegionSkuPropertyRequest,
    RegionSkuPropertySearchRequest,
    SchemeRequest,
    SkuPropertyRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

  @Component
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.pageSearch, this.pageSearch)
    }

    /**
     * 培训形式
     */
    @Prop({
      type: String,
      default: ''
    })
    trainingMode: string

    @Prop({
      type: String,
      required: true
    })
    schemeId: string

    /**
     * 方案学习统计
     */
    querySchemeLearningList = new QuerySchemeLearningList()
    /**
     * 筛选条件
     */
    commoditySkuRequestVo = new CommoditySkuRequestVo()

    page: UiPage

    tableData: SchemeLearningStatisticsResponseVo[] = []
    /**
     * ui loading效果
     */
    uiLoading = {
      tableLoading: false
    }

    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.commoditySkuRequestVo = new CommoditySkuRequestVo()

      this.commoditySkuRequestVo.promoteThePortalAlias = ''
      this.commoditySkuRequestVo.portalId = ''
      this.commoditySkuRequestVo.distributorId = ''
      this.commoditySkuRequestVo.distributorName = ''

      this.commoditySkuRequestVo.registerTime = new DateScopeRequest()
      this.commoditySkuRequestVo.registerTime.begin = ''
      this.commoditySkuRequestVo.registerTime.end = ''
      this.commoditySkuRequestVo.schemeRequest = new SchemeRequest()
      this.commoditySkuRequestVo.skuPropertyRequest = new SkuPropertyRequest()
      this.commoditySkuRequestVo.skuPropertyRequest.year = new Array<string>()
      this.commoditySkuRequestVo.skuPropertyRequest.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.commoditySkuRequestVo.skuPropertyRequest.regionSkuPropertySearch.region =
        new Array<RegionSkuPropertyRequest>()
      this.commoditySkuRequestVo.skuPropertyRequest.industry = new Array<string>()
      this.commoditySkuRequestVo.skuPropertyRequest.subjectType = new Array<string>()
      this.commoditySkuRequestVo.skuPropertyRequest.trainingCategory = new Array<string>()
      this.commoditySkuRequestVo.skuPropertyRequest.trainingProfessional = new Array<string>()
    }

    /**
     * 方案sku展示
     */
    classSkuList(row: SchemeLearningStatisticsResponseVo) {
      const map = {
        industry: '行业',
        trainingMajor: '培训专业',
        trainingCategory: '培训类别',
        subjectType: '科目类型',
        region: '地区',
        year: '年份',
        positionCategory: '岗位类别'
      }
      const keys: Array<keyof SkuPropertyResponseVo> = [
        'year',
        'region',
        'industry',
        'trainingCategory',
        'trainingMajor',
        'subjectType',
        'positionCategory'
      ]
      return keys
        .map((key) => {
          const sku = row.trainClassDetail.skuValueNameProperty[key] as SkuVo
          const skuPropertyName = sku?.skuPropertyValueId && sku?.skuPropertyName ? sku.skuPropertyName : ''
          return { skuPropertyName, key }
        })
        .filter((item) => item.skuPropertyName)
        .map(({ skuPropertyName, key }) => `<p>${map[key]}：${skuPropertyName}</p>`)
        .join('')
    }

    /**
     * 分页查询
     */
    async pageSearch() {
      this.tableData = await this.querySchemeLearningList.listSchemeLearningReportFormsInServicer(
        this.page,
        this.commoditySkuRequestVo
      )
      ;(this.$refs['table'] as ElTable).doLayout()
    }

    async doQuery() {
      this.uiLoading.tableLoading = true
      this.page.pageNo = 1
      this.commoditySkuRequestVo.schemeRequest.schemeIdList = [this.schemeId]
      await this.pageSearch()
      this.uiLoading.tableLoading = false
    }

    async reset() {
      await this.doQuery()
    }

    viewOnlineLearningStatistic(row: SchemeLearningStatisticsResponseVo) {
      this.$router.push(
        `/statistic/learning-statistic?schemeId=${this.schemeId}&schemeName=${row.trainClassDetail.commodityBasicData.saleTitle}&trainingMode=${this.trainingMode}`
      )
    }

    created() {
      this.initQueryParam()
      this.doQuery()
    }
  }
</script>
