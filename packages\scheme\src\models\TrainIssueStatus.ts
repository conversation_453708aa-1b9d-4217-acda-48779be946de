import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum TrainIssueStatusEnum {
  /**
   * 未开始
   */
  notStarted,
  /**
   * 进行中
   */
  ongoing,
  /**
   * 已结束
   */
  ended
}
/**
 * @description 考勤考核要求类型
 */
class TrainIssueStatus extends AbstractEnum<TrainIssueStatusEnum> {
  static enum = TrainIssueStatusEnum

  constructor(status?: TrainIssueStatusEnum) {
    super()
    this.current = status
    this.map.set(TrainIssueStatusEnum.notStarted, '未开始')
    this.map.set(TrainIssueStatusEnum.ongoing, '进行中')
    this.map.set(TrainIssueStatusEnum.ended, '已结束')
  }

  getTagType(status: TrainIssueStatusEnum) {
    switch (status) {
      case TrainIssueStatusEnum.notStarted:
        return 'warning'
      case TrainIssueStatusEnum.ongoing:
        return 'success'
      case TrainIssueStatusEnum.ended:
        return 'info'
    }
  }
}
export default TrainIssueStatus
