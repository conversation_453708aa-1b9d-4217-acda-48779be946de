import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 开始时间类型枚举
 * open_now 立即开启
 * assign 指定开启时间
 */
export enum BeginTimeTypeEnum {
  open_now = 'open_now',
  assign = 'assign'
}

/**
 * @description 开始时间类型
 */
class BeginTimeType extends AbstractEnum<BeginTimeTypeEnum> {
  static enum = BeginTimeTypeEnum

  constructor(status?: BeginTimeTypeEnum) {
    super()
    this.current = status
    this.map.set(BeginTimeTypeEnum.open_now, '立即开启')
    this.map.set(BeginTimeTypeEnum.assign, '指定开启时间')
  }
}

export default new BeginTimeType()
