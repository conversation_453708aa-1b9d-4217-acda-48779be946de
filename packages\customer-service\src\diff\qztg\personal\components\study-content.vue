<template>
  <div class="f-p15" v-if="$hasPermission('studyDontent')" desc="学习内容" actions="selectIdChange,handleCurrentRow">
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div class="f-plr20 f-pt20">
        <el-row :gutter="16" class="m-query">
          <el-form :inline="true" label-width="80px">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="年度">
                <year-select v-model="querySchemeParams.year"></year-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训方案">
                <el-input v-model="querySchemeParams.schemeName" clearable placeholder="请输入培训方案名称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item>
                <template v-if="$hasPermission('studyQuery')" desc="查询" actions="clickSearch">
                  <el-button type="primary" @click="clickSearch">查询</el-button>
                </template>
                <el-button @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </div>
    </el-card>
    <el-row :gutter="15" class="is-height">
      <el-col :md="8">
        <el-card shadow="never" class="m-card is-header f-mb15">
          <div class="f-plr20">
            <div class="m-tit">
              <span class="tit-txt">已报方案</span>
            </div>
          </div>
          <div class="f-plr10 f-pb20" style="max-height: 800px">
            <div v-loading="uiStatus.query.loadSchemePage">
              <el-table
                stripe
                :data="schemeTableData"
                ref="SchemeTableRef"
                highlight-current-row
                @current-change="handleCurrentRow"
                class="m-table is-body m-plan-list"
                max-height="700px"
              >
                <el-table-column>
                  <template slot-scope="scope">
                    <div class="f-flex f-align-start">
                      <div class="tag" v-if="scope.row.schemeStatus === 1"><i class="txt">有效</i></div>
                      <div class="tag is-gray" v-if="scope.row.schemeStatus === 2"><i class="txt">失效</i></div>
                      <div class="f-flex-sub">
                        <p>{{ scope.row.basicInfo.schemeName }}</p>
                        <p class="f-f13 f-c9">
                          【{{ schemeTag(scope.row) }}】
                          <span class="f-ml20">{{
                            getSchemeProperty(scope.row.basicInfo.skuValueNameProperty, true)
                          }}</span>
                        </p>
                        <template v-if="scope.row.saleChannel != SaleChannelEnum.self">
                          <el-tag type="warning" v-if="scope.row.saleChannel == SaleChannelEnum.distribution">
                            分销
                          </el-tag>
                          <el-tooltip
                            class="item"
                            effect="dark"
                            placement="right"
                            v-if="scope.row.saleChannel == SaleChannelEnum.topic"
                          >
                            <el-tag type="primary">专题</el-tag>
                            <div slot="content">{{ scope.row.saleChannelName }}</div>
                          </el-tooltip>
                        </template>
                        <el-tag type="danger" v-if="scope.row.thirdPartyPlatform">{{
                          scope.row.thirdPartyPlatform
                        }}</el-tag>
                      </div>
                      <template
                        v-if="$hasPermission('showIntelligentLearningTag')"
                        desc="是否展示智能学习标识"
                        actions=""
                      >
                        <el-tooltip
                          class="item"
                          effect="dark"
                          placement="right"
                          v-if="scope.row.isIntelligentLearning && scope.row.IntelligentLearningResult === 0"
                        >
                          <!--处理中 添加 is-warning，成功 添加 is-success，失败 添加 is-error-->
                          <span class="label">智能<br />学习</span>
                          <div slot="content">待处理</div>
                        </el-tooltip>
                      </template>
                      <template
                        v-if="$hasPermission('showIntelligentLearningTag')"
                        desc="是否展示智能学习标识"
                        actions=""
                      >
                        <el-tooltip
                          class="item"
                          effect="dark"
                          placement="right"
                          v-if="scope.row.isIntelligentLearning && scope.row.IntelligentLearningResult === 2"
                        >
                          <!--处理中 添加 is-warning，成功 添加 is-success，失败 添加 is-error-->
                          <span class="label is-warning">智能<br />学习</span>
                          <div slot="content">处理中</div>
                        </el-tooltip>
                      </template>
                      <template
                        v-if="$hasPermission('showIntelligentLearningTag')"
                        desc="是否展示智能学习标识"
                        actions=""
                      >
                        <el-tooltip
                          class="item"
                          effect="dark"
                          placement="right"
                          v-if="scope.row.isIntelligentLearning && scope.row.IntelligentLearningResult === 3"
                        >
                          <!--处理中 添加 is-warning，成功 添加 is-success，失败 添加 is-error-->
                          <span class="label is-success">智能<br />学习</span>
                          <div slot="content">处理成功</div>
                        </el-tooltip>
                      </template>
                      <template
                        v-if="$hasPermission('showIntelligentLearningTag')"
                        desc="是否展示智能学习标识"
                        actions=""
                      >
                        <el-tooltip
                          class="item"
                          effect="dark"
                          placement="right"
                          v-if="
                            (scope.row.isIntelligentLearning && scope.row.IntelligentLearningResult === 4) ||
                            scope.row.IntelligentLearningResult === 1
                          "
                        >
                          <!--处理中 添加 is-warning，成功 添加 is-success，失败 添加 is-error-->
                          <span class="label is-error">智能<br />学习</span>
                          <div slot="content">处理失败，{{ scope.row.failReason }}。</div>
                        </el-tooltip>
                      </template>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <!--分页-->
            <hb-pagination :page="trainClassPage" v-bind="trainClassPage" class="f-mt15 f-tr"></hb-pagination>
          </div>
        </el-card>
      </el-col>
      <el-col :md="16">
        <el-card shadow="never" class="m-card is-header f-mb15">
          <div slot="header" class="">
            <span class="tit-txt">培训内容</span>
          </div>
          <div class="f-p20">
            <el-tabs
              v-model="activeTabPane"
              type="card"
              class="no-margin"
              v-loading="uiStatus.query.loadBaseInfoDetail"
            >
              <el-tab-pane label="基本信息" name="first" :lazy="true">
                <el-row class="no-gutter">
                  <el-form label-width="120px" class="m-text-form f-ml30 f-mt30">
                    <el-col :span="12">
                      <el-form-item label="培训方案：">{{ baseInfoDetail.basicInfo.schemeName || '-' }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="方案类型：">{{ schemeName }} </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="年度：">{{
                        baseInfoDetail.basicInfo.skuValueNameProperty.year.skuPropertyName || '-'
                      }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="培训属性：">{{
                        getSchemeProperty(baseInfoDetail.basicInfo.skuValueNameProperty)
                      }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="开通方式：">{{
                        getTrainClassOpenTypeNameById(baseInfoDetail.basicInfo.openType)
                      }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="开通时间：">{{
                        formatTime(baseInfoDetail.basicInfo.openTime)
                      }}</el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="isMixedClass">
                      <el-form-item label="面授期别/编号：">
                        {{ echoIssueInfo }}
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="isMixedClass">
                      <el-form-item label="考勤要求：">
                        <span v-if="issueInfo.isOpenAttendance"
                          >{{ isNumber(baseInfoDetail.periodStudy.requireAttendanceRate) }}%</span
                        >
                        <span v-else>-</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" v-if="!isMixedClass">
                      <el-form-item label="考核成绩：">
                        <span>{{ isNumber(baseInfoDetail.basicInfo.assessScore) }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="考核学时：">{{ baseInfoDetail.basicInfo.assessPeriod || '-' }}</el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="考核结果：" class="is-form">
                        <template v-if="showBtn">
                          <span class="f-cg f-mr10" v-if="baseInfoDetail.basicInfo.assessResult === 1">已合格</span>
                          <span class="f-cr f-mr10" v-if="baseInfoDetail.basicInfo.assessResult === 0">未合格</span>
                          <span class="f-cr f-mr10" v-if="baseInfoDetail.basicInfo.assessResult === -1">待考核</span>
                          <template v-if="$hasPermission('qualified')" desc="一键合格" actions="confirmOneKeyQualified">
                            <el-button
                              type="primary"
                              size="small"
                              @click="oneClickPass()"
                              v-if="baseInfoDetail.basicInfo.assessResult !== 1"
                              :disabled="
                                baseInfoDetail.schemeStatus === 2 ||
                                baseInfoDetail.trainClassDetail.learningTypeModel.learningExperience.isExamine
                              "
                              >一键合格</el-button
                            >
                          </template>
                        </template>
                        <template v-else>-</template>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
              </el-tab-pane>
              <el-tab-pane label="学习情况" name="second" :lazy="true">
                <template v-if="$hasPermission('learningSituation')" desc="学习情况" actions="@LearningSituation">
                  <learning-situation
                    :baseInfoDetail="baseInfoDetail"
                    :userId="userId"
                    :qualificationId="qualificationId"
                    :hasConfigCourseQuiz="hasConfigCourseQuiz"
                    :studentNo="studentNo"
                    ref="learningSituationRef"
                    @updateBaseInfoDetail="queryBaseInfoDetailByQualificationId"
                  ></learning-situation>
                </template>
              </el-tab-pane>
              <el-tab-pane label="班级考试" v-if="baseInfoDetail.hasConfigExam" name="third">
                <div class="f-ptb25 f-plr5">
                  <div class="f-f16 f-fb">{{ baseInfoDetail.exam.examName }}</div>
                  <div class="f-flex f-mt20">
                    <div class="m-score f-mr30">
                      <!--合格 .f-cg，不合格 .f-cr-->
                      <p>
                        <span class="num" :class="{ 'f-cg': isExamScoreQualifed, 'f-cr': !isExamScoreQualifed }">{{
                          baseInfoDetail.exam.examHighestScore
                        }}</span>
                        分
                      </p>
                      <p class="f-mt5 f-c9">考试最高分</p>
                    </div>
                    <el-row class="no-gutter f-flex-sub">
                      <el-form :inline="true" label-width="120px" class="m-text-form f-mt15">
                        <el-col :span="12">
                          <el-form-item label="总分 / 及格分："
                            >{{ baseInfoDetail.exam.examTotalScore }} /
                            {{ baseInfoDetail.exam.examQualifiedScore }}分</el-form-item
                          >
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="考试时长："
                            >{{ 60 * baseInfoDetail.exam.examTimeLength }}分钟</el-form-item
                          >
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="考试周期："
                            ><span>{{ showExamCycle(baseInfoDetail.exam.examTime) }}</span>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="已考 / 剩余："
                            >{{ examRecordCount }} /
                            {{ getRestExamCount(baseInfoDetail.exam.examUnCompleteCount) }}</el-form-item
                          >
                        </el-col>
                      </el-form>
                    </el-row>
                  </div>
                </div>
                <el-table
                  stripe
                  :data="classExamRecordTableData"
                  max-height="500px"
                  class="m-table"
                  v-loading="uiStatus.query.loadExamRecordsPage"
                >
                  <el-table-column type="index" label="No." width="60" align="center">
                    <template slot-scope="scope">
                      <span
                        :data-index="scope.$index + 1"
                        v-observe-visibility="visibilityClassExamRecordTableConfig"
                        >{{ scope.$index + 1 }}</span
                      >
                    </template>
                  </el-table-column>
                  <el-table-column label="答卷时间" min-width="180">
                    <template slot-scope="scope">
                      <span>
                        <el-tooltip
                          class="item"
                          effect="dark"
                          placement="top"
                          popper-class="m-tooltip"
                          :content="
                            '同步第三方数据：' +
                            itemScheme.simulateExamTime.begin +
                            '/' +
                            itemScheme.simulateExamScore +
                            '分'
                          "
                          v-if="
                            scope.$index === 0 && itemScheme.haveSimulate && !itemScheme.assessRequire.isExamAssessed
                          "
                        >
                          <i class="el-icon-info f-co f-mr5"></i>
                        </el-tooltip>
                      </span>
                      <span>{{ scope.row.examTime.begin }} 至 {{ scope.row.examTime.end }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="成绩(分)" min-width="200" align="center">
                    <template slot-scope="scope">
                      <div>{{ scope.row.examScore }}</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="100" align="center" fixed="right">
                    <template
                      slot-scope="scope"
                      v-if="$hasPermission('deleteExamRecord')"
                      desc="删除考试记录"
                      actions="deleteExamRecord"
                    >
                      <el-button type="text" size="mini" @click="deleteExamRecord(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-tab-pane
                v-if="baseInfoDetail.trainClassDetail.learningTypeModel.learningExperience.isSelected"
                label="学习心得"
                name="fourth"
              >
                <el-row class="no-gutter">
                  <el-form ref="form" label-width="auto" class="m-text-form f-ml30 f-mt30">
                    <el-col :span="12">
                      <el-form-item label="考核要求：">
                        ①各项学习心得要求以具体配置为准
                        <br />
                        <div v-if="baseInfoDetail.trainClassDetail.learningTypeModel.learningExperience.isExamine">
                          ②学习心得纳入考核，至少参加
                          <i class="f-cr">{{
                            baseInfoDetail.trainClassDetail.learningTypeModel.learningExperience.joinCount
                          }}</i>
                          个心得，且每项心得均为通过。
                        </div>
                        <div v-else>②学习心得不纳入考核</div>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
                <div class="m-attribute">
                  <el-collapse v-model="activeNames" accordion @change="getRecordList">
                    <el-collapse-item v-for="(item, index) in experienceList" :key="item.id" :name="index + 1">
                      <template slot="title">
                        <span :title="item.theme" class="experience-record-list">
                          <el-tag v-if="item.isRequired" type="primary" effect="dark" size="mini">必选</el-tag>
                          <i class="f-fb f-ml5">{{ item.theme }}</i></span
                        >
                        <span class="f-mlr40">参加时间：{{ isLongTime(item) }}</span>
                        <span>审核方式：{{ item.checkType }} </span>
                        <span class="f-ml40">剩余提交次数：{{ submitCount(item) }}次</span>
                      </template>
                      <div class="f-plr20">
                        <el-row :gutter="20">
                          <el-table stripe :data="item.recordList" max-height="500px" class="m-table f-mt15">
                            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                            <el-table-column label="提交时间" min-width="140">
                              <template slot-scope="{ row }">{{ row.submitTime }}</template>
                            </el-table-column>
                            <el-table-column label="审核状态" min-width="100" align="center">
                              <template slot-scope="{ row }">
                                <span>{{ row.checkStatus.isChecked }}</span>
                              </template>
                            </el-table-column>
                            <el-table-column label="审核时间" min-width="140">
                              <template slot-scope="{ row }">{{ row.checkTime || '--' }}</template>
                            </el-table-column>
                            <el-table-column label="审核结果" min-width="100" align="center">
                              <template slot-scope="{ row }">
                                {{ isPass(row) }}
                              </template>
                            </el-table-column>
                            <el-table-column label="操作" width="80" align="center" fixed="right">
                              <template
                                v-if="$hasPermission('deleteExperience')"
                                desc="删除学习心得"
                                actions="handleDeleteExperience"
                                slot-scope="{ row }"
                              >
                                <el-button
                                  v-if="!isAuto(item)"
                                  type="text"
                                  size="mini"
                                  @click="handleDeleteExperience(row)"
                                  >删除</el-button
                                >
                                <span v-else>{{ '--' }}</span>
                              </template>
                            </el-table-column>
                          </el-table>
                        </el-row>
                      </div>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </el-tab-pane>
              <el-tab-pane
                label="调研问卷"
                name="fifth"
                v-if="
                  baseInfoDetail.periodStudy.questionnaireTotalNum > 0 ||
                  baseInfoDetail.periodStudy.schemeQuestionnaireTotalNum
                "
              >
                <template v-if="$hasPermission('surveyQuestionnaire')" desc="调研问卷" actions="@SurveyQuestionnaire">
                  <survey-questionnaire
                    :studentNo="studentNo"
                    ref="surveyQuestionnaireRef"
                    :baseInfoDetail="baseInfoDetail"
                  ></survey-questionnaire>
                </template>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!--基本信息 考核结果的一键合格抽屉 -->
          <el-drawer
            title="一键合格"
            :visible.sync="uiStatus.isShow.oneKeyQualifiedDrawer"
            :close-on-press-escape="false"
            :wrapper-closable="false"
            size="800px"
            custom-class="m-drawer"
          >
            <div class="drawer-bd">
              <el-form
                ref="OneKeyQualifyDrawerFormRef"
                :model="oneKeyDrawerForm"
                :rules="oneKeyDrawerRules"
                label-width="150px"
                class="m-form f-mt20"
              >
                <el-form-item label="一键合格类型：" required>
                  <el-radio-group v-model="radioValue" @change="radioValueChange">
                    <el-radio :label="2">
                      按系统当前操作成功时间
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                        <i class="el-icon-info m-tooltip-icon f-c9"></i>
                        <div slot="content">
                          按系统当前操作成功时间：快速完成培训，所有学习过程的时间都按照系统当前的操作时间
                        </div>
                      </el-tooltip>
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="考试合格分：" prop="examPoint" v-if="hasExam">
                  <el-input v-model="oneKeyDrawerForm.examPoint" class="input-num" placeholder="请输入" />
                  <span class="f-ml5">分</span>
                  <span class="f-cb">（及格 / 满分：{{ examScore }} / 100分）</span>
                </el-form-item>
                <el-form-item label="课程测验合格分：" prop="testPoint" v-if="hasQuizConfig">
                  <el-input v-model="oneKeyDrawerForm.testPoint" class="input-num" placeholder="请输入" />
                  <span class="f-ml5">分</span>
                  <span class="f-cb">（及格 / 满分：{{ passingScore }} / 100分）</span>
                </el-form-item>
                <el-form-item>
                  <span class="f-co">注：请设置合格成绩介于合格分和满分之间。</span>
                </el-form-item>
                <el-form-item v-if="needAnti">
                  <span class="f-co">一键合格的班级，无学习监拍日志，请谨慎操作！</span>
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button @click="cancelOneKeyQualified">取消</el-button>
                  <el-button type="primary" @click="confirmOneKeyQualified">确认</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-drawer>
        </el-card>
      </el-col>
    </el-row>
    <!--  面网授班一键合格禁用提示  -->
    <el-dialog
      title="提示"
      :visible.sync="disableDialog"
      :lock-scroll="true"
      :append-to-body="true"
      width="450px"
      class="m-dialog"
    >
      <span>
        <span v-if="isMixed">面网授班</span>
        <span v-if="isOffline">面授班</span>
        暂不支持一键合格！</span
      >
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="disableDialog = false">我知道了</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Prop, Watch } from 'vue-property-decorator'
  import ExaminationDetailDrawer from '@hbfe/jxjy-admin-customerService/src/personal/components/components/study-content-components/examination-detail-drawer.vue'
  import OnekeyStudyDrawer from '@hbfe/jxjy-admin-customerService/src/personal/components/components/study-content-components/onekey-study-drawer.vue'
  import YearSelect from '@hbfe/jxjy-admin-customerService/src/personal/components/components/year-select.vue'
  // import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
  import { Page, UiPage } from '@hbfe/common'
  import QueryStudentTrainClassListVo from '@api/service/management/train-class/query/vo/QueryStudentTrainClassListVo'
  import StudentTrainClassDetailVo from '@api/service/management/train-class/query/vo/StudentTrainClassDetailVo'
  import TrainClassSchemeType, {
    TrainClassSchemeEnum
  } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
  import { ElTable } from 'element-ui/types/table'
  // import ResourceModule from '@api/service/management/resource/ResourceModule'
  import StudentCourseVo from '@api/service/management/resource/course/query/vo/StudentCourseVo'
  import ExamRecordVo from '@api/service/management/train-class/query/vo/ExamRecordVo'
  import { debounce, bind } from 'lodash-decorators'
  import QueryCourseQuizRecordListVo from '@api/service/management/train-class/query/vo/QueryCourseQuizRecordListVo'
  import CourseQuizRecordDetailVo from '@api/service/management/train-class/query/vo/CourseQuizRecordDetailVo'
  import SchemeLearningInfoVo from '@api/service/management/train-class/query/vo/SchemeLearningInfoVo'
  import { ElForm } from 'element-ui/types/form'
  import DeleteExamRecordVo from '@api/service/management/train-class/mutation/vo/DeleteExamRecordVo'
  import QueryStudentTrainClass from '@api/service/management/train-class/query/QueryStudentTrainClass'
  import QueryStudentCourse from '@api/service/management/resource/course/query/QueryStudentCourse'
  import MutationDeleteExamRecord from '@api/service/management/train-class/mutation/MutationDeleteExamRecord'
  import MutationQualifiedCourse from '@api/service/management/train-class/mutation/MutationQualifiedCourse'
  import MutationQualifiedCourseware from '@api/service/management/train-class/mutation/MutationQualifiedCourseware'
  import MutationQualifiedTrainClass from '@api/service/management/train-class/mutation/MutationQualifiedTrainClass'
  import MutationDeleteCourse from '@api/service/management/train-class/mutation/MutationDeleteCourse'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
  import ExperienceRecordItem from '@api/service/management/train-class/query/vo/ExperienceRecordItem'

  import DateScope from '@api/service/common/models/DateScope'
  import AntiCheatConfig from '@hbfe-biz/biz-anticheat/dist/config/AntiCheatConfig'
  import ExperienceItem from '@api/service/management/train-class/query/vo/ExperienceItem'
  import Constant from '@api/service/common/models/constant'
  import { CheckTypeEnum } from '@api/service/management/train-class/query/enum/CheckTypeEnum'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import LearningSituation from '@hbfe/jxjy-admin-customerService/src/personal/components/components/study-content-components/learning-situation.vue'
  import SurveyQuestionnaire from '@hbfe/jxjy-admin-customerService/src/personal/components/components/study-content-components/survey-questionnaire.vue'
  import {
    StudentSchemeLearningSortField,
    SortPolicy
  } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
  import StudentTrainClassDetailDiffVo from '@api/service/diff/management/qztg/train-class/model/StudentTrainClassDetailVo'
  import QueryStudentTrainClassDiff from '@api/service/diff/management/qztg/train-class/QueryStudentTrainClass'
  import TrainClassOpenType from '@api/service/common/enums/train-class/TrainClassOpenType'
  import BaseConfig from '@hbfe-biz/biz-anticheat/dist/config/BaseConfig'
  import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  enum QualifiedTimeEnum {
    // 指定合格时间
    SPECIAL_TIME = 1,
    // 系统时间
    SYSTEM_TIME = 2
  }
  @Component({
    components: {
      ExaminationDetailDrawer,
      OnekeyStudyDrawer,
      YearSelect,
      LearningSituation,
      SurveyQuestionnaire
    }
  })
  export default class extends Vue {
    @Ref('OneKeyQualifyDrawerFormRef')
    OneKeyQualifyDrawerFormRef: ElForm

    @Ref('OnekeyStudyDrawerRef')
    OnekeyStudyDrawerRef: OnekeyStudyDrawer
    @Ref('ExaminationDetailDrawerRef')
    ExaminationDetailDrawerRef: ExaminationDetailDrawer
    @Ref('SchemeTableRef')
    SchemeTableRef: ElTable
    @Ref('coursePeriodTableRef')
    coursePeriodTableRef: ElTable
    @Ref('learningSituationRef')
    learningSituationRef: LearningSituation
    @Ref('surveyQuestionnaireRef')
    surveyQuestionnaireRef: SurveyQuestionnaire
    // 监管基础配置信息
    @Prop({ type: Object, default: () => new BaseConfig() })
    baseConfig: BaseConfig
    get hasQuizConfig() {
      return !!this.baseInfoDetail?.trainClassDetail?.learningTypeModel?.courseLearning?.configCourseQuiz
    }
    get passingScore() {
      return (
        Number(this.baseInfoDetail?.trainClassDetail?.learningTypeModel?.courseLearning?.quizConfigModel?.passScore) ||
        60
      )
    }
    get hasExam() {
      return this.baseInfoDetail?.trainClassDetail?.learningTypeModel?.exam?.isSelected
    }
    get examScore() {
      return Number(this.baseInfoDetail?.trainClassDetail?.learningTypeModel?.exam?.examPassScore) || 60
    }
    /**
     * @description 获取期别信息
     * */
    get issueInfo() {
      if (!this.baseInfoDetail?.periodStudy?.periodId) return new IssueConfigDetail()
      return (
        this.baseInfoDetail?.getIssueConfigById(this.baseInfoDetail.periodStudy.periodId) ?? new IssueConfigDetail()
      )
    }

    /**
     * @description 回显期别信息
     * */
    get echoIssueInfo() {
      if (!this.issueInfo.issueName && !this.issueInfo.issueNo) return '-'
      return `${this.issueInfo.issueName}/${this.issueInfo.issueNo}`
    }

    /**
     * @description 是否网面授班
     * */
    get isMixedClass() {
      return (
        this.baseInfoDetail?.scheme?.skuProperty?.trainingWay?.skuPropertyValueId == TrainingModeEnum.mixed ||
        this.baseInfoDetail?.scheme?.skuProperty?.trainingWay?.skuPropertyValueId == TrainingModeEnum.offline
      )
    }
    get isMixed() {
      return this.baseInfoDetail?.scheme?.skuProperty?.trainingWay?.skuPropertyValueId == TrainingModeEnum.mixed
    }
    get isOffline() {
      return this.baseInfoDetail?.scheme?.skuProperty?.trainingWay?.skuPropertyValueId == TrainingModeEnum.offline
    }
    Constant = Constant
    // 监管规则
    antiCheatConfig = new AntiCheatConfig()
    // 是否需要监管
    needAnti = false
    disableDialog = false
    // 查询培训班列表实例
    queryStudentTrainClassObj = new QueryStudentTrainClass()

    // 查询培训班列表差异化实例
    queryStudentTrainClassDiffObj = new QueryStudentTrainClassDiff()
    trainClassPage: UiPage
    // 查询课程分类实例
    queryStudentCourseObj = new QueryStudentCourse()
    coursePage: Page
    coursePageTotalSize = 0
    // 考试记录删除实例
    deleteExamRecordObj = new MutationDeleteExamRecord()
    // 请求入参
    deleteExamRecordParams: DeleteExamRecordVo = this.deleteExamRecordObj.deleteExamRecordParams

    // 一键合格实例
    oneKeyQualifiedTrainClassObj = new MutationQualifiedTrainClass()
    // 一键合格入参
    oneKeyQualifiedParams = this.oneKeyQualifiedTrainClassObj.qualifiedParams

    // 一键学习实例 【课程】
    qualifiedCourseObj = new MutationQualifiedCourse()
    qualifiedCourseParams = this.qualifiedCourseObj.qualifiedParams

    // 一键学习实例 【课件】
    qualifiedCoursewareObj = new MutationQualifiedCourseware()
    qualifiedCoursewareParams = this.qualifiedCoursewareObj.qualifiedParams
    tableLoading = false
    // 删除课程实例
    deleteCourseObj = new MutationDeleteCourse()
    deleteCourseParams = this.deleteCourseObj.deleteParams

    // 一键合格 【课程】
    courseParams = {
      courseTestPoint: null as number,
      hasConfigQuiz: false,
      courseId: '',
      studentCourseId: ''
    }

    uiStatus = {
      query: {
        loadSchemePage: false,
        loadBaseInfoDetail: false,
        loadCoursePeriodPage: false,
        loadExamRecordsPage: false,
        loadCourseExamTest: false,
        searchLoading: false,
        resetLoading: false
      },
      isShow: {
        courseExamDrawer: false,
        oneKeyQualifiedDrawer: false,
        oneKeyStudyDrawer: false
      },
      disabled: {
        qualifiedTime: true
      }
    }

    /**
     * @description 培训形式枚举
     * */
    TrainingModeEnum = TrainingModeEnum
    querySchemeParams = new QueryStudentTrainClassListVo()
    schemeTableData = new Array<StudentTrainClassDetailDiffVo>()
    baseInfoDetail = new StudentTrainClassDetailVo()
    coursePeriodTableData = new Array<SchemeLearningInfoVo>()
    classExamRecordTableData = new Array<ExamRecordVo>()
    courseQuizRecordTableData = new Array<CourseQuizRecordDetailVo>()
    schemeOldVal = new StudentTrainClassDetailDiffVo()
    // 参训资格id
    qualificationId = ''
    // 学号
    studentNo = ''
    examRecordPage = new Page(1, 10)

    // 课程测验分页
    courseQuizPage = new Page(1, 10)
    // 用于查询课程详情列表的课程id
    courseQuizId = ''

    // 默认选中（基本信息）栏目
    activeTabPane = 'first'
    /**
     * 一键学习按钮loading
     */
    oneKeyStudyLoading = false

    // 展示按钮
    showBtn = false
    activeNames = 0
    experienceList: Array<ExperienceItem> = new Array<ExperienceItem>()
    QueryMyLearningExperienceObj: QueryStudentTrainClass = new QueryStudentTrainClass()
    ExperienceRecordItemObj: ExperienceRecordItem = new ExperienceRecordItem()
    /**
     * 专题类型枚举
     */
    SaleChannelEnum = SaleChannelEnum
    /**
     * 当前选择方案
     */
    itemScheme = new StudentTrainClassDetailVo()
    get courseUnCompletePeriod() {
      return this.baseInfoDetail.course.courseUnCompletePeriod > 0
        ? this.baseInfoDetail.course.courseUnCompletePeriod
        : 0
    }
    isPass(row: ExperienceRecordItem) {
      if (row.checkStatus.isChecked === '已审核') {
        return row.checkStatus.checkResult + '（' + row.score + '）'
      } else {
        return '-'
      }
    }
    isLongTime(row: ExperienceItem) {
      if (row.joinStartTime == Constant.START_TIME_DEFAULT && row.joinEndTime == Constant.END_TIME_DEFAULT) {
        return '长期有效'
      } else {
        return row.joinStartTime + '至' + row.joinEndTime
      }
    }
    get isNumber() {
      return (val: any) => {
        const echoVal = !isNaN(val) ? val : '-'
        return echoVal
      }
    }
    isAuto(row: ExperienceItem) {
      if (row.checkType.current == CheckTypeEnum.auto) {
        return true
      } else {
        return false
      }
    }
    submitCount(row: ExperienceItem) {
      if (this.isAuto(row)) {
        return '-- '
      } else {
        return row.remainderCount
      }
    }
    // 一键合格
    oneClickPass() {
      if (this.isMixedClass) {
        this.disableDialog = true
        return
      }
      if (!this.baseInfoDetail.hasConfigCourse && !this.baseInfoDetail.hasConfigExam) {
        this.$confirm('当前方案配置的学习内容，一键合格无法提供对应数据', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'warning'
        })
        return
      }
      this.uiStatus.isShow.oneKeyQualifiedDrawer = true
    }
    // 考试合格分校验规则
    checkExamPoint(rule: any, value: any, callback: any) {
      if (!value) {
        return callback(new Error('考试合格分不能为空'))
      } else if (value < this.examScore || value > 100) {
        callback(new Error('请设置合格成绩介于合格分和满分之间'))
      } else {
        callback()
      }
    }
    // 课程测验合格分校验规则
    checkTestPoint(rule: any, value: any, callback: any) {
      if (!value) {
        return callback(new Error('课程测验合格分不能为空'))
      } else if (value < this.passingScore || value > 100) {
        callback(new Error('请设置合格成绩介于合格分和满分之间'))
      } else {
        callback()
      }
    }

    getTrainingEndDate() {
      return this.baseInfoDetail.trainClassDetail.trainClassBaseInfo.trainingEndDate
    }

    checkQualifiedTime = (rule: any, value: any, callback: any, type: number) => {
      const trainingEndDate = new Date(this.getTrainingEndDate()).getTime()
      // 指定合格时间时添加校验
      if (type === QualifiedTimeEnum.SPECIAL_TIME) {
        if (!value) {
          return callback(new Error('指定合格时间不能为空！'))
        } else {
          const inputDate = new Date(value).getTime()
          if (trainingEndDate < inputDate) {
            return callback(new Error('合格时间，不能晚于培训结束时间'))
          } else {
            callback()
          }
        }
      } else {
        callback()
      }
    }

    oneKeyDrawerRules = {
      examPoint: [{ required: true, validator: this.checkExamPoint, trigger: 'blur' }],
      testPoint: [{ required: true, validator: this.checkTestPoint, trigger: 'blur' }]
    }
    // 合格时间配置 默认选中系统时间
    radioValue = 2

    oneKeyDrawerForm = {
      qualifiedTime: '',
      examPoint: null as number,
      testPoint: null as number
    }
    // 是否配置课程测验
    hasConfigCourseQuiz = false

    // 学员id 由主文件ref传入
    userId = ''

    async selectIdChange(userId: string) {
      if (userId) {
        this.baseInfoDetail = new StudentTrainClassDetailVo()
        this.showBtn = false
        this.querySchemeParams.userId = userId
        await this.clickSearch()
      } else {
        this.schemeTableData = new Array<StudentTrainClassDetailDiffVo>()
        this.baseInfoDetail = new StudentTrainClassDetailVo()
        this.classExamRecordTableData = new Array<ExamRecordVo>()
        this.$nextTick(() => {
          this.learningSituationRef?.clearList()
          this.surveyQuestionnaireRef?.clearList()
        })

        this.coursePeriodTableData = new Array<SchemeLearningInfoVo>()
        this.coursePage.totalSize = 0
        this.querySchemeParams.userId = ''
        this.trainClassPage.totalSize = 0
      }
    }

    /**
     * @description 已报方案分页更新后，默认选中第一项
     * */
    handleLoadCurrentChange() {
      // 分页加载完成
      if (this.schemeTableData?.length) {
        this.qualificationId = this.schemeTableData[0]?.qualificationId || ''
        this.studentNo = this.schemeTableData[0]?.studentNo || ''
        this.$nextTick(() => {
          // 触发 table current-change 事件 ：handleCurrentRow
          this.SchemeTableRef.setCurrentRow(this.schemeTableData[0])
        })
      } else {
        this.schemeTableData = new Array<StudentTrainClassDetailDiffVo>()
        this.baseInfoDetail = new StudentTrainClassDetailVo()
        this.$nextTick(() => {
          this.learningSituationRef?.clearList()
          this.surveyQuestionnaireRef?.clearList()
        })
        this.qualificationId = ''
        this.studentNo = ''
      }
    }

    constructor() {
      super()
      this.trainClassPage = new UiPage(this.doQuery, this.doQuery)
      this.coursePage = new Page(1, 20)
    }

    // 获取已考次数
    get examRecordCount() {
      return this.examRecordPage.totalSize
    }

    // 分数是否合格
    get isExamScoreQualifed() {
      const res = this.baseInfoDetail?.exam?.examHighestScore >= this.baseInfoDetail?.exam?.examQualifiedScore
      return res
    }

    // 获取课程总数
    get coursePeriodCount() {
      return this.coursePage.totalSize
    }

    // 课程分类
    classifyInfo(classify: string) {
      return classify.split('>').join(' / ')
    }
    // 删除学习心得
    @bind
    @debounce(150)
    async handleDeleteExperience(row: ExperienceRecordItem) {
      this.$confirm('确认删除本条提交记录？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(async () => {
        this.ExperienceRecordItemObj.studentLearningExperienceId = row.studentLearningExperienceId
        this.ExperienceRecordItemObj.learningExperienceTopicType = row.learningExperienceTopicType
        const res = await this.ExperienceRecordItemObj.delete()
        if (res.code == 200) {
          this.$message.success('删除成功！')
          await this.queryUserExperience()
          this.activeNames = 0
        } else {
          this.$message.error('删除失败！')
        }
      })
    }
    // 查询学员班级考试记录列表
    queryExamRecordsList() {
      if (!this.qualificationId) {
        this.$message.error('参训资格id不能为空！')
        return
      }
      try {
        this.uiStatus.query.loadExamRecordsPage = true
        this.queryStudentTrainClassObj
          .queryStudentExamRecordList(this.examRecordPage, this.qualificationId)
          .then((res) => {
            this.classExamRecordTableData = res
          })
        // console.log('班级考试列表', this.classExamRecordTableData)
      } catch (e) {
        console.log(e)
        this.$message.error('获取学员考试记录列表请求失败！')
      } finally {
        this.uiStatus.query.loadExamRecordsPage = false
      }
    }
    // 获取审核日志
    async getRecordList(index: number) {
      if (!index) {
        return
      }
      await this.experienceList[index - 1].queryRecord()
      console.log(this.experienceList[index - 1], 'red')
    }
    // 前往学习日志
    toStudyLog() {
      const userId = this.userId
      const schemeId = this.baseInfoDetail.scheme.schemeId
      const qualificationId = this.qualificationId
      const schemeType = this.baseInfoDetail.basicInfo.schemeType
      const studentNo = this.studentNo
      this.$router.push({
        path: '/statistic/statistics-report/study-log',
        query: { userId, schemeId, qualificationId, schemeType: String(schemeType), studentNo }
      })
    }

    // 滚动查询学员考试记录列表
    async visibilityClassExamRecordTableConfig(isVisible: boolean, entry: any) {
      // console.log('滚动加载学员考试记录列表')
      if (isVisible) {
        if (entry.target.dataset.index >= this.examRecordPage.totalSize) {
          //   最大值时不请求
          return
        }
        if (parseInt(entry.target.dataset.index) == this.classExamRecordTableData?.length) {
          this.examRecordPage.pageNo++
          const list = await this.queryStudentTrainClassObj.queryStudentExamRecordList(
            this.examRecordPage,
            this.qualificationId
          )
          this.classExamRecordTableData = this.classExamRecordTableData.concat(list)
        }
      }
    }

    /* 滚动查询课程学时列表 */
    async visibilityCoursePeriodTableConfig(isVisible: boolean, entry: any) {
      // console.log('滑动加载课程学时列表')
      if (isVisible) {
        if (entry.target.dataset.index >= this.coursePage.totalSize) {
          //   最大值时不请求
          return
        }
        if (parseInt(entry.target.dataset.index) == this.coursePeriodTableData?.length) {
          this.coursePage.pageNo++
          // TODO
          const list = await this.queryStudentTrainClassObj.queryStudentLearningInfoList(
            this.coursePage,
            this.studentNo,
            this.baseInfoDetail.scheme.schemeId,
            this.baseInfoDetail.scheme.schemeType
          )
          this.coursePeriodTableData = this.coursePeriodTableData.concat(list)
          this.$nextTick(() => {
            this.coursePeriodTableRef.doLayout()
          })
        }
      }
    }
    async handleCurrentRow(val: StudentTrainClassDetailVo) {
      this.activeTabPane = 'first'
      this.itemScheme = val
      if (val) {
        const schemeId = val.scheme.schemeId
        // 判断该方案是否需要监管
        await this.antiCheatConfig.queryDetailBySchemeId(schemeId)
        if (
          this.antiCheatConfig.antiCheatConfig.studyConfig.enterVerifyMode == 1 ||
          this.antiCheatConfig.antiCheatConfig.studyConfig.processVerify
        ) {
          this.needAnti = true
        } else {
          this.needAnti = false
        }
        this.qualificationId = val?.qualificationId
        this.studentNo = val?.studentNo
        await this.queryBaseInfoDetailByQualificationId()
        // 学习情况tab数据重载
        this.$nextTick(() => {
          this.learningSituationRef?.init()
          this.surveyQuestionnaireRef?.init()
        })
        this.queryExamRecordsList()
        if (this.baseInfoDetail.trainClassDetail.learningTypeModel.learningExperience.isSelected) {
          this.activeNames = 0
          this.queryUserExperience()
        }
      }
    }
    // 获取心得记录
    async queryUserExperience() {
      this.experienceList = await this.QueryMyLearningExperienceObj.queryStudentExperienceList(this.studentNo)
      console.log(this.experienceList, 'this.experienceList')
    }
    // 获取用户基本信息
    async queryBaseInfoDetailByQualificationId() {
      this.showBtn = false
      if (!this.qualificationId) {
        this.$message.error('参训资格id不能为空！')
        return
      }
      try {
        this.uiStatus.query.loadBaseInfoDetail = true
        await this.queryStudentTrainClassObj.queryStudentTrainClassDetail(this.qualificationId).then((res) => {
          this.baseInfoDetail = res
          this.showBtn = true //展示按钮
          this.hasConfigCourseQuiz = this.baseInfoDetail?.assessRequire?.hasConfigCourseQuiz
        })
        // console.log(this.baseInfoDetail)
      } catch (error) {
        console.log(error, 'error')
        this.$message.error('获取基本信息失败！')
      } finally {
        this.uiStatus.query.loadBaseInfoDetail = false
      }
    }

    async clickSearch() {
      this.trainClassPage.pageNo = 1
      await this.doQuery()
    }

    showExamCycle(timeRange: DateScope) {
      const dateScope = new DateScope()
      // 允许考试开始时间 , date 同培训班的学习起止时填班级的学习开始，类型为string
      //   const allowStartTime = this.baseInfoDetail.trainClassDetail.learningTypeModel.exam.allowStartTime || '-'
      const allowStartTime = this.baseInfoDetail.trainClassDetail.trainClassBaseInfo.trainingBeginDate || '-'
      //  允许考试结束时间， date 同培训班的学习起止时填班级的学习结束，类型为string
      //   const allowEndTime = this.baseInfoDetail.trainClassDetail.learningTypeModel.exam.allowStartTime || '-'
      const allowEndTime = this.baseInfoDetail.trainClassDetail.trainClassBaseInfo.trainingEndDate || '-'

      if (timeRange.begin || timeRange.end) {
        dateScope.begin = timeRange.begin ? timeRange.begin.split(' ')[0] : '-'
        dateScope.end = timeRange.end ? timeRange.end.split(' ')[0] : '-'
      } else if (allowEndTime != '-' || allowStartTime != '-') {
        dateScope.begin = allowStartTime ? allowStartTime.split(' ')[0] : '-'
        dateScope.end = allowEndTime ? allowEndTime.split(' ')[0] : '-'
      }

      if (dateScope.begin || dateScope.end) {
        const startYear = new Date(dateScope.begin).getFullYear() <= 1900
        const endYear = new Date(dateScope.end).getFullYear() >= 2100
        if (startYear || endYear) return '长期有效'
        return `${dateScope.begin} 至 ${dateScope.end}`
      } else {
        return '-'
      }
    }

    // 查询已报方案列表
    async doQuery() {
      if (!this.querySchemeParams.userId) return
      try {
        this.uiStatus.query.searchLoading = true
        this.uiStatus.query.loadSchemePage = true
        const sort = [
          { field: StudentSchemeLearningSortField.SCHEME_YEAR, policy: SortPolicy.DESC },
          { field: StudentSchemeLearningSortField.REGISTER_TIME, policy: SortPolicy.DESC }
        ]
        this.schemeTableData = await this.queryStudentTrainClassDiffObj.queryStudentTrainClassList(
          this.trainClassPage,
          this.querySchemeParams,
          sort
        )
        console.log(this.schemeTableData, 'schemeTableData')

        await this.queryStudentTrainClassDiffObj.supplementStudentChannelInfo(this.schemeTableData)
        if (this.schemeTableData.length > 0) this.schemeOldVal = this.schemeTableData[0]
        this.handleLoadCurrentChange()
      } catch (e) {
        this.$message.error(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['SchemeTableRef'] as any)?.doLayout()
        this.uiStatus.query.searchLoading = false
        this.uiStatus.query.loadSchemePage = false
      }
    }

    // 这里重置的时候学员ID得保留
    async resetQuery() {
      try {
        this.uiStatus.query.resetLoading = true
        this.querySchemeParams.year = undefined
        this.querySchemeParams.schemeName = undefined
        await this.clickSearch()
      } finally {
        this.uiStatus.query.resetLoading = false
      }
    }

    // 确认一键合格
    confirmOneKeyQualified() {
      // 增加时间校验
      if (this.uiStatus.disabled.qualifiedTime == true) {
        // 按系统当前操作成功时间
        const trainingEndDate = new Date(this.getTrainingEndDate()).getTime()
        const systemDate = new Date().getTime()
        if (trainingEndDate < systemDate) {
          this.$message.warning('合格时间，不能晚于培训结束时间')
          return
        }
      }
      // 合格时间，不能晚于培训结束时间
      this.OneKeyQualifyDrawerFormRef.validate(async (val: boolean) => {
        if (val) {
          try {
            this.oneKeyQualifiedParams.qualifiedTime = this.oneKeyDrawerForm.qualifiedTime
            this.oneKeyQualifiedParams.examScore = this.oneKeyDrawerForm.examPoint
            this.oneKeyQualifiedParams.courseQuizScore = this.oneKeyDrawerForm.testPoint
            console.log(this.oneKeyDrawerForm.testPoint, 'this.oneKeyDrawerForm.testPoint')
            this.oneKeyQualifiedParams.qualificationId = this.qualificationId
            this.oneKeyQualifiedParams.schemeType = this.baseInfoDetail?.basicInfo?.schemeType
            console.log(this.oneKeyQualifiedParams)
            const loading = this.$loading({
              background: 'rgba(0, 0, 0, 0.8)'
            })
            console.log(this.oneKeyQualifiedParams.schemeType, 'this.oneKeyQualifiedParams.schemeType')
            const res = await this.oneKeyQualifiedTrainClassObj.doQualifiedTrainClass()
            if (res.isSuccess()) {
              this.$message.success('一键合格成功')
              await this.queryBaseInfoDetailByQualificationId()
            } else if (res.code == 10001) {
              this.$message.error('没有剩余的考试次数，无法操作。')
            } else if (res.code == 2008) {
              this.$confirm('系统正在推送课程，请稍后重试。', '提示', {
                confirmButtonText: '确认',
                showCancelButton: false
              }).then(async () => {
                return true
              })
            } else if (res.code == 50002) {
              this.$message.error('合格时间，不能早于培训开始时间')
            } else {
              this.$message.error('一键合格失败')
            }
            loading.close()
          } catch (e) {
            console.log(e)
            this.$message.error('一键合格请求失败！')
          } finally {
            this.oneKeyDrawerForm.qualifiedTime = undefined
            this.oneKeyDrawerForm.examPoint = undefined
            this.oneKeyDrawerForm.testPoint = undefined
            this.radioValue = 2
            this.uiStatus.disabled.qualifiedTime = true
            this.uiStatus.isShow.oneKeyQualifiedDrawer = false
          }
        }
      })
    }

    // 一键合格时间选择
    radioValueChange(val: QualifiedTimeEnum) {
      this.radioValue = val
      if (val === QualifiedTimeEnum.SYSTEM_TIME) {
        // 从自定义时间切换回系统时间时，重新赋值
        this.oneKeyDrawerForm.qualifiedTime = ''
        this.uiStatus.disabled.qualifiedTime = true
      } else if (val === QualifiedTimeEnum.SPECIAL_TIME) {
        this.uiStatus.disabled.qualifiedTime = false
      }
    }
    // 取消一键合格
    cancelOneKeyQualified() {
      this.oneKeyDrawerForm.qualifiedTime = undefined
      this.oneKeyDrawerForm.examPoint = undefined
      this.oneKeyDrawerForm.testPoint = undefined
      this.radioValue = 2
      this.uiStatus.disabled.qualifiedTime = true
      this.uiStatus.isShow.oneKeyQualifiedDrawer = false
    }

    // 删除考试记录
    @bind
    @debounce(150)
    deleteExamRecord(item: ExamRecordVo) {
      this.$confirm('确定要删除？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(async () => {
        this.deleteExamRecordParams.studentExamId = `${this.studentNo}_${item.studentExamId}`
        this.deleteExamRecordParams.answerPaperIds = [item.answerPaperId]
        const res = await this.deleteExamRecordObj.doDeleteExamRecord()
        if (!res?.status?.isSuccess()) {
          this.$message.error('删除失败！')
        } else {
          await this.queryExamRecordsList()
          this.$message.success('删除成功！')
        }
      })
    }

    formatTimeToHours(time: number) {
      if (!time) return '-'
      const res = this.$moment.utc(time * 1000).format('HH:mm:ss')
      return res
    }

    getQuizStatusName(val: number) {
      // 0：未评定 1：未合格 2：合格
      const obj = {
        0: '未评定',
        1: '未合格',
        2: '合格'
      }
      return obj[val] || '-'
    }

    formatTime(val: string) {
      if (!val) return '-'
      const res = this.$moment(new Date(val)).format('YYYY.MM.DD HH:mm:ss')
      return res
    }

    getRestExamCount(val: number) {
      if (val === -1) {
        return '无限次'
      }
      return isNaN(val) ? '-' : val
    }

    getPercentageProgress(total: number, complete: number, expand = 100) {
      if (total === 0) return 0
      if (total && complete) {
        const res = ((((complete * expand) / total) * expand) / expand).toFixed()
        const resToNumber = Number(res)
        return resToNumber > 100 ? 100 : resToNumber
      } else {
        return 0
      }
    }

    getSchemeTypeNameById(val: number) {
      return SchemeType.getSchemeType(val)
    }

    getSchemeTrainingType(val: TrainingModeEnum) {
      const res = TrainingMode.map.get(val) || '-'
      return res
    }
    /**
     * @description 获取培训班名称
     * */
    get schemeName() {
      // const schemeType = '培训班'
      // const trainingWay = this.getSchemeTrainingType(
      //   this.baseInfoDetail.basicInfo.skuValueNameProperty.trainingMode.skuPropertyValueId
      // )
      // const selectClassWay = this.getSchemeTypeNameById(this.baseInfoDetail.basicInfo.schemeType)
      // const nameArr = [schemeType]
      // if (trainingWay !== '-') nameArr.push(trainingWay)
      // if (selectClassWay !== '-') nameArr.push(selectClassWay)
      // return nameArr.join('-')’
      return SchemeType.getNewSchemeType(this.baseInfoDetail.basicInfo, true)
    }
    /**
     * @description 获取培训班类型
     * */
    get schemeTag() {
      return (row: StudentTrainClassDetailVo) => {
        // const schemeType = '培训班'
        // const trainingWay = this.getSchemeTrainingType(
        //   row.basicInfo.skuValueNameProperty.trainingMode.skuPropertyValueId
        // )
        // const selectClassWay = this.getSchemeTypeNameById(row.basicInfo.schemeType)
        // const nameArr = [trainingWay]
        // if (trainingWay !== '-') nameArr.push(schemeType)
        // if (selectClassWay !== '-') nameArr.push(selectClassWay)
        // return `${nameArr.join('-')}`
        return SchemeType.getNewSchemeType(row.basicInfo, true)
      }
    }

    getSchemeProperty(val: SkuPropertyResponseVo, border?: boolean) {
      const arrList = Array<string>()
      if (val.year && val.year.skuPropertyName) arrList.push(val.year.skuPropertyName)
      if (val.industry && val.industry.skuPropertyName) arrList.push(val.industry.skuPropertyName)
      if (val.technicalGrade && val.technicalGrade.skuPropertyName) arrList.push(val.technicalGrade.skuPropertyName)
      if (val.subjectType && val.subjectType.skuPropertyName) arrList.push(val.subjectType.skuPropertyName)
      if (val.trainingCategory && val.trainingCategory.skuPropertyName)
        arrList.push(val.trainingCategory.skuPropertyName)
      if (val.trainingMajor && val.trainingMajor.skuPropertyName) arrList.push(val.trainingMajor.skuPropertyName)
      if (val.trainingObject && val.trainingObject.skuPropertyName) arrList.push(val.trainingObject.skuPropertyName)
      if (val.positionCategory && val.positionCategory.skuPropertyName)
        arrList.push(val.positionCategory.skuPropertyName)
      if (val.jobLevel && val.jobLevel.skuPropertyName) arrList.push(val.jobLevel.skuPropertyName)
      if (val.learningPhase && val.learningPhase.skuPropertyName) arrList.push(val.learningPhase.skuPropertyName)
      if (val.discipline && val.discipline.skuPropertyName) arrList.push(val.discipline.skuPropertyName)

      if (!arrList.length) return '-'
      if (border) {
        return '【' + arrList.join(' / ') + '】'
      } else {
        return arrList.join(' / ')
      }
    }

    getTrainClassOpenTypeNameById(val: number) {
      if (val === 5) return '个人报名'
      const res = TrainClassOpenType.map.get(val) || '-'
      return res
    }
  }
</script>
<style scoped lang="scss">
  .myList {
    cursor: not-allowed !important;
    pointer-events: none !important;
  }
  ::v-deep .el-table:before {
    content: none;
  }
  .experience-record-list {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  ::v-deep .el-pagination {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
</style>
