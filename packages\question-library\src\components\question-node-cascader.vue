<template>
  <div v-loading="!show">
    <el-cascader
      v-model="select"
      :props="props"
      style="width: 100%"
      :options="categoryCache"
      :popper-class="'hb-cascader-exam'"
      :show-all-levels="false"
      placeholder="请选择题库"
      v-bind="$attrs"
      v-if="show && categoryCache.length > 0"
      @change="handleCascaderChange"
    >
    </el-cascader>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { UiPage, Query } from '@hbfe/common'
  import LibraryResponseVo from '@api/service/management/resource/question-library/query/vo/LibraryResponseVo'
  import { isEmpty } from '@hbfe/jxjy-admin-common/src/util/util'
  import LibraryRequestVo from '@api/service/management/resource/question-library/query/vo/LibraryRequestVo'
  class NewLibraryResponseVo extends LibraryResponseVo {
    children? = new Array<any>()
    leaf = false
  }
  @Component
  export default class extends Vue {
    page: UiPage
    query: Query = new Query()
    libraryResponseVo = new LibraryResponseVo()
    LibraryRequestVo: LibraryRequestVo = new LibraryRequestVo()

    @Prop({
      type: Boolean,
      default: false
    })
    multiple: boolean // 是否多选

    // 双向绑定
    @Prop({
      type: String,
      required: true,
      default: ''
    })
    value: string

    //判断是否更新
    @Prop({
      type: Boolean,
      default: false
    })
    isUpdate: boolean

    show = true // 组件显示 回显用

    questionDetail = ''

    // 存储所有题库数据
    categoryCache = new Array<LibraryResponseVo>()
    // 查询题库实例
    questionLibrary = ResourceModule.queryQuestionLibraryFactory.queryQuestionLibraryMultiton

    //级联组件所需要传参
    props = {
      multiple: false,
      value: 'id',
      label: 'name',
      checkStrictly: true,
      emitPath: false
    }

    //获取数据
    get select(): string | Array<string> {
      return this.value
    }
    set select(val: Array<string> | string) {
      let select = ''
      if (typeof val === 'object') {
        select = (val[val.length - 1] || '') as string
        this.$emit('input', select)
      } else {
        select = val as string
      }
      this.$emit('input', select)
    }

    constructor() {
      super()
      this.page = new UiPage()
    }

    async created() {
      // 查询
      await this.lazyLoad()
    }

    async lazyLoad() {
      this.show = false
      this.page.pageSize = 200
      this.LibraryRequestVo.parentLibraryId = ''
      const res = await this.questionLibrary.queryQuestionBankLibraryNode(this.page, this.LibraryRequestVo)
      if (!res?.status?.isSuccess()) {
        console.error('获取题库信息失败！')
        return
      }
      // 全部题库数据
      const arr = cloneDeep(res?.data)

      // 假数据  根节点 parentId 为 undefined
      arr?.map((item) => {
        if (item.parentId == undefined) {
          item.parentId = '-1'
        }
      })

      // 添加一个根节点 “题库分类”
      const root: any = {
        id: '-1',
        name: '题库分类',
        parentId: '-1',
        children: [] as any
      }

      // 将原来的数据作为根节点的子节点
      root.children = this.recursion('-1', arr)

      // 设置 categoryCache 为带有根节点的数据
      this.categoryCache = [root]
      console.group(
        '%c%s',
        'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#800080, #C71585)',
        'this.categoryCache调试输出'
      )
      console.log(this.categoryCache)
      console.count('this.categoryCache输出次数')
      console.groupEnd()

      this.show = true
    }
    // 递归
    recursion(id: string, arr: Array<LibraryResponseVo>) {
      const parentList = new Array<NewLibraryResponseVo>()
      const childrenList = new Array<LibraryResponseVo>()

      arr?.forEach((item: LibraryResponseVo) => {
        if (item.parentId === id) {
          const temp = new NewLibraryResponseVo()
          Object.assign(temp, item)
          temp.children = new Array<any>()
          temp.leaf = false
          parentList.push(temp)
        } else {
          childrenList.push(item)
        }
      })

      parentList?.forEach((sub: NewLibraryResponseVo) => {
        const res = this.recursion(sub.id, childrenList)
        if (isEmpty(res)) {
          sub.leaf = true
          delete sub.children
        } else {
          sub.children = res
        }
      })

      return parentList
    }
    // 查找父节点
    findParentIdById(data: any[], id: string): string | null {
      // 遍历每一个对象，进行递归查找
      for (const item of data) {
        // 如果当前项的 id 匹配，返回它的 parentId
        if (item.id === id) {
          return item.parentId || null
        }

        // 如果有子节点，递归地查找子节点
        if (item.children && item.children.length > 0) {
          const result = this.findParentIdById(item.children, id)
          if (result) {
            return result
          }
        }
      }

      // 如果找不到，返回 null
      return null
    }
    async handleCascaderChange(val: any) {
      const selectedValue = val // 叶子节点ID
      // 查找父节点
      const parentNode = await this.findParentIdById(this.categoryCache, selectedValue)

      const result = {
        parentNodeId: parentNode ? parentNode : null,
        nodeId: selectedValue ? selectedValue : null
      }

      console.log('Selected Value:', result)
      this.$emit('handleCascaderChange', result)
    }
  }
</script>
<style>
  /*css方法：使用css将其他的选项框给干掉。*/
  /*以下样式将单选框隐藏 除了第三个单选框不隐藏*/
</style>
