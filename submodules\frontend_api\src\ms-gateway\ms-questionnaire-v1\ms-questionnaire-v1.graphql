"""独立部署的微服务,K8S服务名:ms-questionnaire-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""申请问卷调查"""
	applyQuestionnaire(studentToken:String):String
	applyQuestionnaireVerify(studentToken:String):ApplyQuestionnaireVerifyResponse
}
"""@Author: chen<PERSON>yu
	@CreateTime: 2024-12-06  14:12
	@Description: TODO
"""
type ApplyQuestionnaireVerifyResponse @type(value:"com.fjhb.ms.questionnaire.v1.kernel.gateway.graphql.response.ApplyQuestionnaireVerifyResponse") {
	"""30001 问卷已停用,无需作答
		30002 问卷未开始
		30003 问卷已结束
	"""
	code:String
	message:String
}

scalar List
