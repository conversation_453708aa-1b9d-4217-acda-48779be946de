/**
 * 学员学习课程场景
 */
import { StudentCourseLearningTokenResponse } from '@api/ms-gateway/ms-autonomouscourselearningscene-v1'
import ApplyCourseLearningTokenFactory from '@api/service/common/token/ApplyCourseLearningTokenFactory'
import ApplyStudentLearningTokenDiff from '@api/service/diff/common/zztt/token/ApplyStudentLearningToken'
import ApplyCourseLearningPlayToken from '@api/service/customer/learning/course/token-provider/ApplyCourseLearningPlayToken'
import LearningCourseGateError from '@api/service/customer/learning/scene/errors/LearningCourseGateError'
import StudentLearningCourseTypeEnum from '@api/service/customer/learning/scene/gates/enum/StudentLearningCourseTypeEnum'
import StudentLearningSceneProof from '@api/service/customer/learning/scene/proofs/StudentLearningSceneProof'
import CoursePlayTicket from '@api/service/customer/learning/scene/tickets/CoursePlayTicket'
import { Response } from '@hbfe/common'

class StudentLearningCourseSceneGateDiff {
  private readonly proof: StudentLearningSceneProof
  schemeType: StudentLearningCourseTypeEnum
  applyCourseLearningTokenFactory: ApplyCourseLearningTokenFactory
  /**
   * 申请课程学习token返回值
   */
  response = new Response<StudentCourseLearningTokenResponse>()

  constructor(proof: StudentLearningSceneProof, schemeType: StudentLearningCourseTypeEnum) {
    this.proof = proof
    this.schemeType = schemeType
    this.applyCourseLearningTokenFactory = new ApplyCourseLearningTokenFactory(schemeType, proof)
  }

  /**
   * 申请进入场景门票
   * 进入场景预演：
   * 1. 申请学员 token
   * 2.【学员 token】申请课程学习 token
   * 3.【课程学习 token】申请课程播放 token
   */
  async applyEnterTicket(): Promise<CoursePlayTicket> {
    const applyStudentLearningToken = new ApplyStudentLearningTokenDiff(
      this.proof.qualificationId,
      this.proof.learningId
    )
    const res = await applyStudentLearningToken.apply()
    //华医网订单退款中
    if (res.code == 80000) {
      const info = new CoursePlayTicket(null)
      info.code = res.code
      info.message = res.message as string
      return info
    }
    //华医网订单退款完成
    if (res.code == 80001) {
      const info = new CoursePlayTicket(null)
      info.code = res.code
      info.message = res.message as string
      return info
    }
    // 申请课程学习 token
    let courseLearningToken: string | LearningCourseGateError
    try {
      courseLearningToken = await this.applyCourseLearningTokenFactory.getStudentCourseLearningToken(
        applyStudentLearningToken.token
      )
    } catch (e) {
      this.response = this.applyCourseLearningTokenFactory.response
      if (e instanceof LearningCourseGateError) {
        const info = new CoursePlayTicket(null)
        info.code = (e as LearningCourseGateError)?.code
        info.message = (e as LearningCourseGateError)?.message
        return info
      }
    }

    // 申请课程播放 token
    const applyCoursePlayToken = new ApplyCourseLearningPlayToken(courseLearningToken as string)
    await applyCoursePlayToken.apply()
    // 申请失败不进行下一步
    return new CoursePlayTicket(applyCoursePlayToken.token)
  }

  /**
   * 拒绝进入场景
   */
  rejectEnter() {
    // 拒绝原因
  }
}

export default StudentLearningCourseSceneGateDiff
