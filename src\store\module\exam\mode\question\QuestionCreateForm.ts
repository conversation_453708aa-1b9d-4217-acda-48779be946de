/**
 *
 * 试题创建的表单
 * @author: eleven
 * @date: 2020/4/11
 */
import { PreExamQuestionCreateRequest, TagDTO1 } from '@api/gateway/PreExam-default'

export class QuestionCreateForm extends PreExamQuestionCreateRequest {
  constructor() {
    super()
    this.applyTypes = new Array<number>()
    this.applyTypes.push(1)
    this.applyTypes.push(2)
    this.applyTypes.push(3)
    this.mode = 1
    this.difficulty = 1
    this.enabled = true
    this.chapters = new Array<TagDTO1>()
    this.errorProne = false
  }
}
