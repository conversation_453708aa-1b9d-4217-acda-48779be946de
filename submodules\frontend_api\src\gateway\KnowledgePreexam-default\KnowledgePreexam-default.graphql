schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取某个专业关系节点下一级章节子节点
		@param majorRelationId 专业关系子节点
		@return 章节列表
	"""
	findAllChildChapterByMajor(majorRelationId:String):[ChapterResponse]
	"""获取指定多个专业关系下章节节点及子节点
		@param majorRelationIdList 专业关系节点列表
		@return 专业关系节点对应章节列表
	"""
	findAllChildChapterByMajorList(majorRelationIdList:[String]):[MajorMapChapterListResponse]
	"""获取所有行业及专业
		@return 行业专业关系树
	"""
	findAllIndustryRelationList:[IndustryResponse]
}
type Mutation {
	"""添加章节
		@param majorRelationId  专业关系编号
		@param parentRelationId 上级章节节点编号
		@param chapterName      章节名称
	"""
	createChapter(majorRelationId:String,parentRelationId:String,chapterName:String):Void
	"""删除章节
		@param majorRelationId 专业关系编号
		@param relationId      节点编号
	"""
	deleteChapter(majorRelationId:String,relationId:String):Void
	"""导入考纲
		@param path 模板文件路径
		@return 是否创建导入任务成功
	"""
	importBatchSyllabus(path:String):Boolean!
	"""更新章节信息
		@param originalMajorRelationId   原始专业关系编号
		@param originalChapterRelationId 原始章节关系编号
		@param targetParentRelationId    更新的节点编号
		@param chapterName               章节名称
	"""
	updateChapter(originalMajorRelationId:String,originalChapterRelationId:String,targetParentRelationId:String,chapterName:String):Void
}
"""章节响应信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type ChapterResponse @type(value:"com.fjhb.platform.core.v1.knowledge.kernel.gateway.graphql.response.ChapterResponse") {
	"""当前关系节点的编号"""
	relationId:String
	"""父节点编号"""
	parentRelationId:String
	"""同一级排序"""
	sort:Long
	"""编号"""
	id:String
	"""类型{@link SyllabusStructureConst}"""
	type:String
	"""名称"""
	name:String
	"""代码"""
	code:String
	"""是否可用"""
	enabled:Boolean
}
"""行业信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type IndustryResponse @type(value:"com.fjhb.platform.core.v1.knowledge.kernel.gateway.graphql.response.IndustryResponse") {
	"""所属关系节点编号"""
	relationId:String
	"""专业列表"""
	majorModelList:[MajorResponse]
	"""编号"""
	id:String
	"""类型{@link SyllabusStructureConst}"""
	type:String
	"""名称"""
	name:String
	"""代码"""
	code:String
	"""是否可用"""
	enabled:Boolean
}
"""专业映射章节列表信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type MajorMapChapterListResponse @type(value:"com.fjhb.platform.core.v1.knowledge.kernel.gateway.graphql.response.MajorMapChapterListResponse") {
	"""专业关系编号"""
	majorRelationId:String
	"""专业关系下所有章节列表"""
	chapterList:[ChapterResponse]
}
"""专业信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type MajorResponse @type(value:"com.fjhb.platform.core.v1.knowledge.kernel.gateway.graphql.response.MajorResponse") {
	"""当前节点关系编号"""
	relationId:String
	"""同一级排序"""
	sort:Long
	"""编号"""
	id:String
	"""类型{@link SyllabusStructureConst}"""
	type:String
	"""名称"""
	name:String
	"""代码"""
	code:String
	"""是否可用"""
	enabled:Boolean
}

scalar List
