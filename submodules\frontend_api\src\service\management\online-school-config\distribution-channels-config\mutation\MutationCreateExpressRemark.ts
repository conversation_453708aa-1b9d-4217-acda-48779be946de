import { ResponseStatus } from '@hbfe/common'
import CreateExpressDetailVo from './vo/CreateExpressDetailVo'
import MsOfflineinvoiceV1 from '@api/ms-gateway/ms-offlineinvoice-v1'

/**
 * @description 更新快递备注
 */
class MutationCreateExpressRemark {
  createExpressDetailVo = new CreateExpressDetailVo()

  constructor(remark: string) {
    this.createExpressDetailVo.remark = remark
  }

  async doCreate(): Promise<ResponseStatus> {
    const { status } = await MsOfflineinvoiceV1.createChannel(this.createExpressDetailVo)
    return status
  }
}

export default MutationCreateExpressRemark
