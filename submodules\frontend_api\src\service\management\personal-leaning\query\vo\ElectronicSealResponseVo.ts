import { ElectronicSealResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { GenerateTypesEnum } from '@api/service/common/enums/personal-leaning/GenerateTypes'

/* 
  电子章详情
*/
class ElectronicSealResponseVo extends ElectronicSealResponse {
  /**
   * 电子章id
   */
  id = ''
  /**
   * 生成形式
   * 【默认 图片】
   */
  generateType: number = GenerateTypesEnum.IMAGE
  /**
   * 电子章url
   */
  url = ''
  /**
   * 电子章落款
   */
  sign = ''
  /**
   * 创建时间
   */
  createdTime = ''
  /**
   * 创建人id
   */
  createUserId = ''
  /**
   * 更新时间
   */
  updateTime = ''

  from(item: ElectronicSealResponse) {
    this.id = item.id
    this.generateType = item.generateType
    this.url = item.url
    this.sign = item.sign
    this.createdTime = item.createdTime
    this.createUserId = item.createUserId
    this.updateTime = item.updateTime
  }
}

export default ElectronicSealResponseVo
