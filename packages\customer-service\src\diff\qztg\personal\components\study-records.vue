<template>
  <QztgStudyRecords ref="studyRecordsRef">
    <template #training-program="{ scope }">
      <el-tag type="warning" class="f-mr10" v-if="getTrainClassOpenTypeNameById(scope.row.basicInfo.openType)">{{
        getTrainClassOpenTypeNameById(scope.row.basicInfo.openType)
      }}</el-tag>
      <!-- TODO 是否显示专题 -->
      <el-tag type="success" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题 </el-tag>
      <!-- 是否分销推广 -->
      <el-tag type="warning" class="f-mr10" v-if="scope.row.saleChannel == SaleChannelEnum.distribution"
        >分销推广</el-tag
      >
      <el-tag type="danger" v-if="scope.row.thirdPartyPlatform">{{ scope.row.thirdPartyPlatform }}</el-tag>
    </template>
  </QztgStudyRecords>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import StudyRecords from '@hbfe/jxjy-admin-customerService/src/personal/components/study-records.vue'
  import QueryStudentTrainClass from '@api/service/diff/management/qztg/train-class/QueryStudentTrainClass'
  import StudentTrainClassDetailVo from '@api/service/diff/management/qztg/train-class/model/StudentTrainClassDetailVo'
  import { SaleChannelEnum } from '@api/service/diff/management/qztg/trade/enums/SaleChannelType'
  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication, ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import GSZJStudentTrainClassDetailVo from '@api/service/diff/management/gszj/train-class/StudentTrainClassDetailVo'

  class QztgStudyRecords extends StudyRecords {
    // 查询实例培训班列表
    queryStudentTrainClassObj = new QueryStudentTrainClass()
    trainClassTableData = new Array<StudentTrainClassDetailVo>()
    queryTrainClassTableData = new StudentTrainClassDetailVo()
  }
  @Component({
    components: {
      QztgStudyRecords
    }
  })
  export default class extends Vue {
    @Ref('studyRecordsRef') studyRecordsRef: QztgStudyRecords

    SaleChannelEnum = SaleChannelEnum

    mounted() {
      this.studyRecordsRef.handleRePush = this.handleRePush
    }

    async userIdChange(val: string) {
      return this.studyRecordsRef.userIdChange(val)
    }

    //重新推送
    async handleRePush(row: StudentTrainClassDetailVo) {
      console.log('row+++++++3', row)

      // this.queryTrainClassTableData.studentNo = row.studentNo
      const noAllowPushSchemeIdsArr = (
        CommonConfigCenter.getFrontendApplication(frontendApplication.noAllowPushSchemeIds) || ''
      ).split(',')
      if (noAllowPushSchemeIdsArr.includes(row.trainClassDetail.trainClassBaseInfo.id)) {
        this.$message.error('存在不在推送范围内的方案！')
        return
      }
      // let res
      // if (isAhServicerId()) {
      const obj = new GSZJStudentTrainClassDetailVo()
      const res = await obj.rePush([row.studentNo])
      // } else {
      // res = await row.rePush()
      // }
      if (res.status.code == 200 && res.data == '重推成功') {
        this.$message.success('重推成功！')
      } else {
        this.$message.error('重推失败！')
      }
    }

    getTrainClassOpenTypeNameById(val: number) {
      switch (val) {
        case 1:
        case 5:
          return '个人报名'
        case 2:
          return '集体报名'
        case 3:
          return '导入开通'
        default:
          return ''
      }
    }
  }
</script>
