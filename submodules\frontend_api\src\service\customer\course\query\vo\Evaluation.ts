import { StudentCourseAppraised } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

class Evaluation {
  id: string
  score: number
  createTime: string
  hadEvaluated: boolean

  static from(appraise: StudentCourseAppraised) {
    const evaluation = new Evaluation()
    if (!appraise) {
      return evaluation
    }
    evaluation.id = appraise.studentCourseAppraisalId
    evaluation.hadEvaluated = appraise.appraisalCourse
    evaluation.createTime = appraise.appraisedTime
    return evaluation
  }
}

export default Evaluation
