import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 评定方式类型
 */
export enum EvaluatePatternTypes {
  'AccuracyEvaluate' = 1,
  'NoneEvaluate' = 2,
  'ScoreEvaluate' = 3
}

class ExamEvaluatePatternTypes extends AbstractEnum<EvaluatePatternTypes> {
  static enum = EvaluatePatternTypes

  constructor(status?: EvaluatePatternTypes) {
    super()
    this.current = status
    this.map.set(EvaluatePatternTypes.AccuracyEvaluate, '正确率评定方式')
    this.map.set(EvaluatePatternTypes.NoneEvaluate, '无评定方式')
    this.map.set(EvaluatePatternTypes.ScoreEvaluate, '分值评定方式')
  }
}

export default new ExamEvaluatePatternTypes()
