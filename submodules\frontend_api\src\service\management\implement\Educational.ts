import EducationalManage from '@api/service/management/implement/EducationalManage'
import QuestionnaireInfoManage from '@api/service/management/implement/QuestionnaireInfoManage'
import PeriodImplementBase from '@api/service/management/implement/models/PeriodImplementBase'

/**
 * 教务管理聚合模型
 */
export default class Educational {
  /**
   * 期别id
   */
  private periodId: string = undefined

  /**
   * 方案id
   */
  private schemeId: string = undefined

  /**
   * 期别
   */
  period: PeriodImplementBase = new PeriodImplementBase()

  /**
   * 教务管理
   */
  educationalManage: EducationalManage = new EducationalManage(this.schemeId, this.periodId)

  /**
   * 问卷管理
   */
  questionnaireInfoManage: QuestionnaireInfoManage = new QuestionnaireInfoManage(this.schemeId, this.periodId)

  /**
   * @param schemeId 方案id
   * @param periodId 期别id
   */
  constructor(schemeId: string, periodId: string) {
    this.periodId = periodId
    this.schemeId = schemeId
    this.period = new PeriodImplementBase(periodId)
    this.educationalManage = new EducationalManage(schemeId, periodId)
    this.questionnaireInfoManage = new QuestionnaireInfoManage(schemeId, periodId)
  }
}
