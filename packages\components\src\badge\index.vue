<template>
  <div class="badge-status">
    <span class="badge-status-dot" :class="`badge-status-${status}`"></span>
    <span class="badge-status-text">{{ text }}</span>
  </div>
</template>

<script>
  export default {
    props: {
      text: {
        type: [String, Object],
        default: ''
      },
      status: {
        type: String,
        default: 'default'
      }
    }
  }
</script>

<style scoped lang="scss">
  $name: badge-status;

  @keyframes antStatusProcessing {
    0% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    100% {
      transform: scale(2.4);
      opacity: 0;
    }
  }

  .#{$name} {
    position: relative;

    .#{$name}-default {
      background-color: #e6ebf1;
    }

    .#{$name}-error {
      background-color: #ed4014;
    }

    .#{$name}-success {
      background-color: #19be6b;
    }

    .#{$name}-warning {
      background-color: #faad14;
    }

    .#{$name}-processing {
      position: relative;
      background-color: #2d8cf0;

      &::after {
        position: absolute;
        top: -1px;
        left: -1px;
        width: 100%;
        height: 100%;
        border: 1px solid #2d8cf0;
        border-radius: 50%;
        animation: antStatusProcessing 1.2s infinite ease-in-out;
        content: '';
      }
    }

    .#{$name}-dot {
      width: 6px;
      height: 6px;
      display: inline-block;
      border-radius: 50%;
      vertical-align: middle;
      position: relative;
      top: -1px;
    }

    .#{$name}-text {
      display: inline-block;
      color: #515a6e;
      font-size: 12px;
      margin-left: 6px;
    }
  }
</style>
