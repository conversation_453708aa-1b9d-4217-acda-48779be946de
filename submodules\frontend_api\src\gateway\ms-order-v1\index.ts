import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/gql/ms-order-v1'

// 是否微服务
const isMicroService = true

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-order-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: 'ms-order-v1'
}

// 枚举

// 类

/**
 * 索取发票
<AUTHOR>
@since 2021/3/25
 */
export class ApplyOrderInvoiceRequest {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceInfoRequest
}

/**
 * 买家申请取消订单
<AUTHOR> create 2021/1/27 19:32
 */
export class BuyerApplyCancelOrderRequest {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 取消原因编号
   */
  reasonId?: string
  /**
   * 取消原因描述
   */
  reason?: string
}

/**
 * 请求创建订单
<AUTHOR>
@since 2021/1/22
 */
export class CreateOrderRequest {
  /**
   * 买家编号
   */
  buyerId: string
  /**
   * 商品列表
   */
  commodities: Array<Commodity>
  /**
   * 购买渠道类型
1-用户自主购买
2-集体缴费
3-管理员导入
   */
  purchaseChannelType: number
  /**
   * 终端类型
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  terminalCode: string
  /**
   * 渠道商编号
   */
  channelVendorId?: string
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceInfoRequest
}

/**
 * 商品描述
 */
export class Commodity {
  /**
   * 商品sku编号
   */
  skuId?: string
  /**
   * 商品数量
   */
  quantity?: number
}

/**
 * 发票信息
<AUTHOR>
@since 2021/3/23
 */
export class InvoiceInfoRequest {
  /**
   * 发票抬头
   */
  title?: string
  /**
   * 发票抬头类型
<pre>
1-个人
2-企业
</pre>
   */
  titleType?: number
  /**
   * 购买方纳税人识别号
   */
  taxpayerNo?: string
  /**
   * 地址
   */
  address?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 开户行
   */
  bankName?: string
  /**
   * 账户
   */
  account?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 发票票面备注
   */
  remark?: string
}

/**
 * graphql 使用的key、value 返回对象
<AUTHOR> create 2021/2/24 20:07
 */
export class KeyValueDataRequest {
  /**
   * 字段的key
   */
  key?: string
  /**
   * 字段value
   */
  value?: string
}

/**
 * 线上支付参数
<AUTHOR> create 2021/1/28 19:39
 */
export class OrderOfflinePaymentRequest {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 付款人
   */
  payer?: string
  /**
   * 备注
选填
   */
  remark?: string
  /**
   * 线下付款凭证文件路径集合
   */
  paths?: Array<string>
}

/**
 * 线上支付参数
<AUTHOR> create 2021/1/28 19:39
 */
export class OrderOnlinePaymentRequest {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 支付渠道编号
培训券，对接众智汇云培训券:TRAINING_VOUCHER
支付宝:ALIPAY
微信：WXPAY
   */
  paymentChannelId: string
  /**
   * 购买渠道类型
1-用户自主购买
2-集体缴费
3-管理员导入
   */
  purchaseChannelType: number
  /**
   * 支付终端
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  purchaseChannelTerminal: string
  /**
   * 支付描述
   */
  description?: string
  /**
   * 支付的附加属性，由支付渠道决定
当支付渠道为培训券（TRAINING4_VOUCHER）时，
{
&quot;couponCode&quot;:&quot;培训券编码&quot;,
&quot;educationCode&quot;:&quot;机构编码（企业统一社会信用代码）&quot;,
&quot;workType&quot;: &quot;工种名称&quot;,
&quot;learningSchemeId&quot;: &quot;培训班id&quot;
}
   */
  paymentProperties?: Array<KeyValueDataRequest>
}

/**
 * 卖家取消订单
<AUTHOR> create 2021/1/27 20:08
 */
export class SellerCancelOrderRequest {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 取消原因ID
   */
  reasonId?: string
  /**
   * 取消原因说明
选填
   */
  reason?: string
}

/**
 * 批次发票
<AUTHOR> create 2021/4/30 9:36
 */
export class BatchInvoiceRequest {
  /**
   * 发票种类，默认2
<pre>
1-普通发票
2-增值税普通发票
3-增值税专用发票
</pre>
   */
  invoiceCategory: number
  /**
   * 发票种类
1-电子发票
2-纸质发票
   */
  invoiceType: number
  /**
   * 发票抬头
   */
  title?: string
  /**
   * 发票抬头类型
<pre>
1-个人
2-企业
</pre>
   */
  titleType?: number
  /**
   * 购买方纳税人识别号
   */
  taxpayerNo?: string
  /**
   * 地址
   */
  address?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 开户行
   */
  bankName?: string
  /**
   * 账户
   */
  account?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 发票票面备注
   */
  remark?: string
}

/**
 * 批次单申请发票
<AUTHOR> create 2021/4/30 9:12
 */
export class BatchOrderApplyInvoiceRequest {
  /**
   * 批次单编号
   */
  batchOrderNo?: string
  /**
   * 申请批次发票信息
   */
  invoiceInfo?: BatchInvoiceRequest
}

/**
 * 批次单取消
<AUTHOR> create 2021/4/22 16:11
 */
export class BatchOrderCancelRequest {
  /**
   * 批次单号
   */
  batchOrderNo?: string
  /**
   * 取消时间
   */
  cancelledTime?: string
  /**
   * 取消原因编号
   */
  reasonId?: string
  /**
   * 取消原因描述
   */
  reason?: string
}

/**
 * 批次单线下支付请求参数
<AUTHOR> create 2021/4/23 11:11
 */
export class BatchOrderOfflinePaymentRequest {
  /**
   * 批次单号
   */
  batchOrderNo: string
  /**
   * 付款人
   */
  payer?: string
  /**
   * 备注
选填
   */
  remark?: string
  /**
   * 线下付款凭证文件路径集合
   */
  paths?: Array<string>
}

/**
 * 批次单线上支付请求参数
<AUTHOR> create 2021/4/23 11:10
 */
export class BatchOrderOnlinePaymentRequest {
  /**
   * 批次单号
   */
  batchOrderNo?: string
  /**
   * 支付渠道ID
   */
  paymentChannelId?: string
  /**
   * 终端类型
   */
  terminalCode?: string
  /**
   * 支付成功后跳转地址
   */
  pageUrl?: string
  /**
   * 支付描述
   */
  description?: string
  /**
   * 支付的附加属性，由支付渠道决定
当支付渠道为培训券（TRAINING_VOUCHER）时，
{
&quot;couponCode&quot;:&quot;培训券编码&quot;,
&quot;educationCode&quot;:&quot;机构编码（企业统一社会信用代码）&quot;,
&quot;workType&quot;: &quot;工种名称&quot;,
&quot;learningSchemeId&quot;: &quot;培训班id&quot;
}
   */
  paymentProperties?: Array<KeyValueDataRequest>
}

/**
 * 请求线上支付结果
<AUTHOR> create 2021/2/4 15:16
 */
export class ApplyOnlinePaymentResultResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 去支付的地址
   */
  payUrl: string
  /**
   * 请求支付结果
   */
  result: boolean
  /**
   * 失败错误代码，仅当result&#x3D;false时有值
培训券支付渠道返回错误代码：
261-培训券已使用
262-培训券应用条件不符合
263-培训券已过期
264-培训券已作废
265-已兑换过【xxx】的线上培训课程
266-当前券仅用于兑换【xxx】工种的课程
267-今年内已参加过该工种培训
269-培训券余额不足
   */
  code: string
  /**
   * 错误信息
   */
  message: string
  /**
   * 支付网关支付模式
0-同步
1-重定向
   */
  payMode: number
  /**
   * 交易号（付款流水号）
   */
  payFlowNo: string
}

/**
 * 取消订单结果
<AUTHOR> create 2021/1/29 17:27
 */
export class CancelOrderResultResponse {
  /**
   * 是否取消成功
   */
  success: boolean
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 取消结果信息
   */
  message: string
  /**
   * 商品验证结果信息
   */
  resultList: Array<VerifyResultResponse>
}

/**
 * 创建订单结果
<AUTHOR> create 2021/1/29 17:27
 */
export class CreateOrderResultResponse {
  /**
   * 是否创建成功
   */
  success: boolean
  /**
   * 订单号，仅当{@link #success}为{@code true}时有值
   */
  orderNo: string
  /**
   * 订单创建时间，仅当{@link #success}为{@code true}时有值
   */
  createTime: string
  /**
   * 下单结果信息
   */
  message: string
  /**
   * 商品验证结果信息
   */
  resultList: Array<VerifyResultResponse>
}

/**
 * 校验结果返回
<AUTHOR> create 2021/2/3 10:53
 */
export class VerifyResultResponse {
  /**
   * 校验结果
   */
  message: string
  /**
   * 校验code
   */
  code: string
  /**
   * 订单内的商品skuId
   */
  skuId: string
}

/**
 * 请求线上支付结果
<AUTHOR> create 2021/2/4 15:16
 */
export class BatchApplyOnlinePaymentResponse {
  /**
   * 批次单号
   */
  batchOrderNo: string
  /**
   * 付款地址
   */
  payUrl: string
  /**
   * 支付网关支付模式
<pre>
0-同步
1-重定向
</pre>
   */
  payMode: number
  /**
   * 请求支付结果
   */
  result: boolean
  /**
   * 错误信息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 买家申请取消订单
   * @param applyCancelOrder 申请取消订单信息
   * @return 取消订单结果
   * @param mutate 查询 graphql 语法文档
   * @param applyCancelOrder 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCancelOrder(
    applyCancelOrder: BuyerApplyCancelOrderRequest,
    mutate: DocumentNode = GraphqlImporter.applyCancelOrder,
    operation?: string
  ): Promise<Response<CancelOrderResultResponse>> {
    return commonRequestApi<CancelOrderResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyCancelOrder },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 索取订单发票
   * @param orderInvoiceRequest 发票信息
   * @param mutate 查询 graphql 语法文档
   * @param orderInvoiceRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyInvoice(
    orderInvoiceRequest: ApplyOrderInvoiceRequest,
    mutate: DocumentNode = GraphqlImporter.applyInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { orderInvoiceRequest },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批次单申请发票
   * @param applyInvoiceParam 申请发票信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInvoiceParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchApplyInvoice(
    applyInvoiceParam: BatchOrderApplyInvoiceRequest,
    mutate: DocumentNode = GraphqlImporter.batchApplyInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInvoiceParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 取消批次
   * @param cancelParam 取消批次参数
   * @param mutate 查询 graphql 语法文档
   * @param cancelParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchOrderCancel(
    cancelParam: BatchOrderCancelRequest,
    mutate: DocumentNode = GraphqlImporter.batchOrderCancel,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { cancelParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 提交批次
   * @param mutate 查询 graphql 语法文档
   * @param batchOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async commitBatchOrder(
    batchOrderNo: string,
    mutate: DocumentNode = GraphqlImporter.commitBatchOrder,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { batchOrderNo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建订单
   * @param createOrderInfo 创建参数
   * @return 订单创建序列号
   * @param mutate 查询 graphql 语法文档
   * @param createOrderInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOrder(
    createOrderInfo: CreateOrderRequest,
    mutate: DocumentNode = GraphqlImporter.createOrder,
    operation?: string
  ): Promise<Response<CreateOrderResultResponse>> {
    return commonRequestApi<CreateOrderResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createOrderInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请线下支付
   * <p>
   * TODO 线下付款暂未对接
   * @param offlinePaymentRequest 线下支付信息
   * @param mutate 查询 graphql 语法文档
   * @param offlinePaymentRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async offlineApplyPayOrder(
    offlinePaymentRequest: OrderOfflinePaymentRequest,
    mutate: DocumentNode = GraphqlImporter.offlineApplyPayOrder,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { offlinePaymentRequest },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批次线下支付
   * @param mutate 查询 graphql 语法文档
   * @param batchOrderOfflinePayParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async offlinePayBatchOrder(
    batchOrderOfflinePayParam: BatchOrderOfflinePaymentRequest,
    mutate: DocumentNode = GraphqlImporter.offlinePayBatchOrder,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { batchOrderOfflinePayParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批次线上支付
   * @param mutate 查询 graphql 语法文档
   * @param batchOrderOnlinePayParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async onlinePayBatchOrder(
    batchOrderOnlinePayParam: BatchOrderOnlinePaymentRequest,
    mutate: DocumentNode = GraphqlImporter.onlinePayBatchOrder,
    operation?: string
  ): Promise<Response<BatchApplyOnlinePaymentResponse>> {
    return commonRequestApi<BatchApplyOnlinePaymentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { batchOrderOnlinePayParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 线上支付
   * @param onlinePaymentRequest 线上支付信息
   * @return 线上支付结果
   * @param mutate 查询 graphql 语法文档
   * @param onlinePaymentRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async onlinePayOrder(
    onlinePaymentRequest: OrderOnlinePaymentRequest,
    mutate: DocumentNode = GraphqlImporter.onlinePayOrder,
    operation?: string
  ): Promise<Response<ApplyOnlinePaymentResultResponse>> {
    return commonRequestApi<ApplyOnlinePaymentResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { onlinePaymentRequest },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 卖家取消订单
   * @param sellerCancelOrder 卖家申请取消信息
   * @return 取消订单结果
   * @param mutate 查询 graphql 语法文档
   * @param sellerCancelOrder 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sellerCancelOrder(
    sellerCancelOrder: SellerCancelOrderRequest,
    mutate: DocumentNode = GraphqlImporter.sellerCancelOrder,
    operation?: string
  ): Promise<Response<CancelOrderResultResponse>> {
    return commonRequestApi<CancelOrderResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { sellerCancelOrder },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
