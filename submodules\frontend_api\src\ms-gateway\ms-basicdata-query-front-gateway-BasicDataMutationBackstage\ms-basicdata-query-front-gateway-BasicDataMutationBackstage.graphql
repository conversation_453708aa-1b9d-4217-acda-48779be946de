"""独立部署的微服务,K8S服务名:ms-basicdata-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""新增行业培训属性"""
	addIndustryProperty(request:AddIndustryPropertyRequest):IndustryPropertyMutationResponse
	"""更新行业培训属性"""
	updateIndustryProperty(request:UpdateIndustryPropertyRequest):IndustryPropertyMutationResponse
}
"""新增字典请求体"""
input AddIndustryPropertyRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.AddIndustryPropertyRequest") {
	"""业务数据字典类型|对应枚举BusinessDataDictionaryTypeEnum
		@see BusinessDataDictionaryTypeEnum
	"""
	type:String
	"""行业ID"""
	industryId:String
	"""字典名称"""
	name:String
	"""字典编码"""
	code:Int
	"""字典编码字典扩展，用于解决code适用度不够<br/>
		默认用于存放字典国标\通用代码的字段，开放编辑的字段则存放管理员自定义的代码
	"""
	codeExt:String
}
"""新增字典请求体"""
input UpdateIndustryPropertyRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.UpdateIndustryPropertyRequest") {
	"""字典ID"""
	id:String
	"""字典名称"""
	name:String
	"""字典编码"""
	code:Int
	"""字典编码字典扩展，用于解决code适用度不够<br/>
		默认用于存放字典国标\通用代码的字段，开放编辑的字段则存放管理员自定义的代码
	"""
	codeExt:String
}
"""字典变更响应"""
type IndustryPropertyMutationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.IndustryPropertyMutationResponse") {
	"""错误码<br/>
		<li>200：成功</li>
		<li>500：非以下类别的响应</li>
		<li>50001：工种或培训类别名称已存在</li>
		<li>50002：工种code已存在</li>
		@see IndustryPropertyMutationErrCodeConstants
	"""
	code:Int
	"""错误信息"""
	message:String
}

scalar List
