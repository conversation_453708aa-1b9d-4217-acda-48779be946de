import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/jxjy-data-export-gateway-backstage'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'jxjy-data-export-gateway-backstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortTypeEnum1 {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum TrainingChannelEnum {
  publishedTime = 'publishedTime'
}
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum StudentSortFieldEnum {
  createdTime = 'createdTime'
}
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum BatchOrderSortField {
  BATCH_ORDER_UN_CONFIRMED_TIME = 'BATCH_ORDER_UN_CONFIRMED_TIME',
  BATCH_ORDER_COMMIT_TIME = 'BATCH_ORDER_COMMIT_TIME'
}
export enum BatchReturnOrderSortField {
  CREATED_TIME = 'CREATED_TIME'
}
export enum CommoditySkuSortField {
  ON_SHELVE_TIME = 'ON_SHELVE_TIME',
  LAST_EDIT_TIME = 'LAST_EDIT_TIME',
  ISSUE_TRAINING_BEGIN_TIME = 'ISSUE_TRAINING_BEGIN_TIME',
  COMMODITY_CREATED_TIME = 'COMMODITY_CREATED_TIME',
  SALE_TOTAL_NUMBER = 'SALE_TOTAL_NUMBER',
  SKU_PROPERTY_YEAR = 'SKU_PROPERTY_YEAR',
  TRAINING_CHANNEL = 'TRAINING_CHANNEL'
}
export enum OrderSortField {
  ORDER_NORMAL_TIME = 'ORDER_NORMAL_TIME',
  ORDER_COMPLETED_TIME = 'ORDER_COMPLETED_TIME'
}
export enum ReturnOrderSortField {
  APPLIED_TIME = 'APPLIED_TIME'
}

// 类

export class TrainingChannelSortKParam {
  sortField?: TrainingChannelEnum
  sortType?: SortTypeEnum1
}

export class DateScopeRequest1 {
  beginTime?: string
  endTime?: string
}

export class ObsFileMetaData {
  bizType?: string
  owner?: string
  sign?: string
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

export class CommoditySkuRequest1 {
  commoditySkuIdList?: Array<string>
  saleTitle?: string
  issueInfo?: IssueInfo
  skuProperty?: SkuPropertyRequest1
  externalTrainingPlatform?: Array<string>
  trainingInstitution?: Array<string>
}

export class RegionSkuPropertyRequest1 {
  province?: string
  city?: string
  county?: string
}

export class RegionSkuPropertySearchRequest1 {
  regionSearchType?: number
  region?: Array<RegionSkuPropertyRequest1>
}

export class SkuPropertyRequest1 {
  year?: Array<string>
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest1
  industry?: Array<string>
  subjectType?: Array<string>
  trainingCategory?: Array<string>
  trainingProfessional?: Array<string>
  technicalGrade?: Array<string>
  trainingObject?: Array<string>
  positionCategory?: Array<string>
  jobLevel?: Array<string>
  jobCategory?: Array<string>
  grade?: Array<string>
  subject?: Array<string>
  learningPhase?: Array<string>
  discipline?: Array<string>
  trainingChannelIds?: Array<string>
  certificatesType?: Array<string>
  practitionerCategory?: Array<string>
  qualificationCategory?: Array<string>
  trainingForm?: Array<string>
}

export class IssueInfo {
  issueId?: string
  issueName?: string
  issueNum?: string
  trainStartTime?: string
  trainEndTime?: string
  sourceType?: string
  sourceId?: string
}

export class OrderInfoRequest {
  orderNoList?: Array<string>
  batchOrderNoList?: Array<string>
  buyerIdList?: Array<string>
  receiveAccountIdList?: Array<string>
  flowNoList?: Array<string>
  channelTypesList?: Array<number>
  terminalCodeList?: Array<string>
  saleChannel?: number
  saleChannels?: Array<number>
  saleChannelName?: string
  saleChannelIds?: Array<string>
  policyTrainingSchemeIdList?: Array<string>
  declarationUnitCodeList?: Array<string>
}

export class SubOrderInfoRequest {
  subOrderNoList?: Array<string>
  orderInfo?: OrderInfoRequest
  discountType?: number
  useDiscount?: boolean
}

/**
 * @Description 范围查询条件
<AUTHOR>
@Date 8:51 2022/5/23
 */
export class BigDecimalScopeExcelRequest {
  /**
   * result >&#x3D; begin
   */
  begin?: number
  /**
   * result <&#x3D; end
   */
  end?: number
}

/**
 * 范围查询条件
<AUTHOR>
@version 1.0
@date 2022/5/7 15:34
 */
export class DateScopeExcelRequest {
  /**
   * result >&#x3D; begin
   */
  begin?: string
  /**
   * result <&#x3D; end
   */
  end?: string
}

/**
 * 商品查询条件
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuExcelKParam {
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品Sku名称
   */
  saleTitle?: string
  /**
   * 商品sku属性查询
   */
  skuProperty?: SkuPropertyExcelKParam
}

/**
 * 地区查询参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertyExcelKParam {
  /**
   * 地区: 省
   */
  province?: string
  /**
   * 地区: 市
   */
  city?: string
  /**
   * 地区: 区县
   */
  county?: string
}

/**
 * 地区查询请求参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertySearchExcelKParam {
  /**
   * 地区匹配方式
<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
@see RegionSearchType
   */
  regionSearchType?: number
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyExcelKParam>
}

/**
 * 商品sku属性查询参数
<AUTHOR>
@date 2022/01/25
 */
export class SkuPropertyExcelKParam {
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchExcelKParam
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType?: Array<string>
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory?: Array<string>
  /**
   * 工勤行业-工种
   */
  jobCategory?: Array<string>
  /**
   * 卫生行业-培训对象
   */
  trainingObject?: Array<string>
}

/**
 * 子订单状态变更模型的订单查询参数
<AUTHOR>
@date 2022/05/09
 */
export class OrderExcelKParam {
  /**
   * 买家所在地区路径
   */
  buyerAreaPath?: Array<RegionExcelKParam>
}

/**
 * 地区查询参数
<AUTHOR>
@date 2022/05/09
 */
export class RegionExcelKParam {
  /**
   * 省code
   */
  province?: string
  /**
   * 市code
   */
  city?: string
  /**
   * 县code
   */
  county?: string
  /**
   * 路径 省-市-县
   */
  path?: string
}

/**
 * 发票关联订单查询参数
<AUTHOR>
@date 2022/3/18
 */
export class InvoiceAssociationInfoRequest {
  /**
   * 关联订单类型
0:订单号
1:批次单号
@see AssociationTypes
   */
  associationType?: number
  /**
   * 订单号 | 批次单号
   */
  associationIdList?: Array<string>
  /**
   * 买家信息
   */
  buyerIdList?: Array<string>
  /**
   * 企业id
   */
  unitBuyerUnitIdList?: Array<string>
  /**
   * 收款账号
   */
  receiveAccountIdList?: Array<string>
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 异步任务组名返回对象
 */
export class JobGroupRequest {
  /**
   * 任务组key
   */
  group?: string
  /**
   * 任务组名（模糊查询）
   */
  groupName?: string
}

/**
 * 功能描述：学员查询条件
@Author： wtl
@Date： 2022年1月26日 10:10:33
 */
export class StudentQueryRequest {
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: StudentUserRequest
  /**
   * 用户认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 排序
   */
  sortList?: Array<StudentSortRequest>
}

export class TrainingChannelRequest {
  /**
   * 专题ID集合
   */
  ids?: Array<string>
  /**
   * 专题名称
   */
  name?: string
  /**
   * 专题入口名称
   */
  entryName?: string
  /**
   * 专题类型：
1-地区  2-行业  3-班级
   */
  types?: Array<number>
  /**
   * 行业id
   */
  industryId?: string
  /**
   * 地区路径
   */
  regionPath?: string
  /**
   * 启用状态（0：停用 1：启用）
   */
  enable?: boolean
  /**
   * 是否显示在网校
   */
  showOnNetSchool?: boolean
  /**
   * 排序
   */
  sort?: number
  /**
   * 编辑时间范围
   */
  createdDateScope?: DateScopeRequest1
  /**
   * 排序
   */
  sortList?: Array<TrainingChannelSortKParam>
  /**
   * 专题管理员用户id
   */
  userIdList?: Array<string>
  /**
   * 单位名称
   */
  unitName?: string
}

export class AccountRequest {
  /**
   * 账户状态 1：正常，2：冻结，3：注销
@see AccountStatus
   */
  statusList?: Array<number>
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 创建人用户id
   */
  createdUserId?: string
}

/**
 * 功能描述：账户认证查询条件
@Author： wtl
@Date： 2022年1月26日 09:30:12
 */
export class AuthenticationRequest {
  /**
   * 帐号
   */
  identity?: string
}

/**
 * 功能描述：学员集体缴费信息
@Author： wtl
@Date： 2022年4月21日 08:58:49
 */
export class CollectiveRequest {
  /**
   * 集体缴费管理员用户id集合
   */
  collectiveUserIdList?: Array<string>
}

/**
 * 功能描述 : 学员排序参数
@date : 2022/4/1 17:15
 */
export class StudentSortRequest {
  /**
   * 学员排序字段
   */
  studentSortField?: StudentSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

export class StudentUserRequest {
  /**
   * 工作单位名称（模糊）
   */
  companyName?: string
  /**
   * 用户所属地区路径集合（模糊，右like）
   */
  regionPathList?: Array<string>
  /**
   * 集体缴费信息
   */
  collective?: CollectiveRequest
  /**
   * 单位所属地区路径集合（模糊，右like）
   */
  companyRegionPathList?: Array<string>
  /**
   * 单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  companyRegionPathListMatchType?: number
  /**
   * 是否工勤人员  (0:非工勤人员  1:工勤人员)
   */
  isWorker?: string
  /**
   * 是否退休   (0:非退休人员 1:退休人员)
   */
  isRetire?: string
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
}

/**
 * @Description
<AUTHOR>
@Date 16:18 2022/5/20
 */
export class OwnerKParam {
  /**
   * 所属平台ID
   */
  platformId?: string
  /**
   * 所属平台版本ID
   */
  platformVersionId?: string
  /**
   * 所属项目ID
   */
  projectId?: string
  /**
   * 所属子项目ID
   */
  subProjectId?: string
  /**
   * 企业账号id
   */
  rootAccountId?: string
  /**
   * 所属服务商类型
@see com.fjhb.domain.basicdata.api.servicer.consts.ServicerTypes
   */
  servicerType?: number
  /**
   * 所属服务商ID
   */
  servicerId?: string
  /**
   * 平台租户id
   */
  tenantId?: string
  /**
   * 平台租户id集合
   */
  tenantIdList?: Array<string>
}

/**
 * 地区查询参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertyRequest {
  /**
   * 地区: 省
   */
  province?: string
  /**
   * 地区: 市
   */
  city?: string
  /**
   * 地区: 区县
   */
  county?: string
}

/**
 * 地区查询请求参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertySearchRequest {
  /**
   * 地区匹配方式
<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
@see RegionSearchType
   */
  regionSearchType?: number
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest>
}

/**
 * 商品sku属性查询参数
<AUTHOR>
@date 2022/01/25
 */
export class SkuPropertyRequest {
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType?: Array<string>
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory?: Array<string>
  /**
   * 工勤行业-工种
   */
  jobCategory?: Array<string>
  /**
   * 卫生行业-培训对象
   */
  trainingObject?: Array<string>
  /**
   * 培训形式
   */
  trainingForm?: Array<string>
}

/**
 * 线下发票查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceExportRequest {
  /**
   * 发票ID集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基本信息
   */
  basicData?: OfflineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfo?: InvoiceAssociationInfoRequest
  /**
   * 发票配送信息
   */
  invoiceDeliveryInfo?: OfflineInvoiceDeliveryInfoRequest
  /**
   * 所属单位id集合
   */
  unitIds?: Array<string>
  /**
   * 收款账号id
   */
  receiveAccountId?: Array<string>
  /**
   * 期别名称
   */
  issueId?: Array<string>
  jobName?: string
  metaData?: ObsFileMetaData
}

/**
 * 配送地址信息
<AUTHOR>
@date 2022/05/07
 */
export class DeliveryAddressRequest {
  /**
   * 收件人
   */
  consignee?: string
}

/**
 * 配送状态变更时间查询参数
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryStatusChangeTimeRequest {
  /**
   * 未就绪
   */
  unReady?: DateScopeExcelRequest
  /**
   * 已就绪
   */
  ready?: DateScopeExcelRequest
  /**
   * 已配送
   */
  shipped?: DateScopeExcelRequest
  /**
   * 已自取
   */
  taken?: DateScopeExcelRequest
}

/**
 * 快递信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class ExpressRequest {
  /**
   * 快递单号
   */
  expressNo?: string
}

/**
 * 发票开票状态变更时间记录查询参数
<AUTHOR>
@date 2022/04/06
 */
export class InvoiceBillStatusChangTimeRequest {
  /**
   * 发票申请开票时间
   */
  unBillDateScope?: DateScopeExcelRequest
  /**
   * 发票开票时间
   */
  successDateScope?: DateScopeExcelRequest
}

/**
 * 线下发票基本信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceBasicDataRequest {
  /**
   * 发票类型
1:电子发票 2:纸质发票
@see InvoiceTypes
   */
  invoiceTypeList?: Array<number>
  /**
   * 发票种类
1:普通发票 2:增值税普通发票 3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategory?: Array<number>
  /**
   * 发票状态
1:正常
2:作废
@see InvoiceStatus
   */
  invoiceStatus?: number
  /**
   * 发票状态变更时间记录
   */
  invoiceStatusChangeTime?: InvoiceStatusChangeTimeRequest
  /**
   * 发票开票状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  billStatusList?: Array<number>
  /**
   * 发票开票状态变更时间记录
   */
  billStatusChangTime?: InvoiceBillStatusChangTimeRequest
  /**
   * 发票是否冻结
   */
  freeze?: boolean
  /**
   * 发票号集合
   */
  invoiceNoList?: Array<string>
  /**
   * 商品id集合
   */
  commoditySkuIdList?: Array<string>
}

/**
 * 线下发票配送信息
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceDeliveryInfoRequest {
  /**
   * 配送状态
0:未就绪 1：已就绪 2：已自取 3：已配送
@see OfflineDeliveryStatus
   */
  deliveryStatusList?: Array<number>
  /**
   * 配送状态变更时间记录
0:未就绪 1：已就绪 2：已自取 3：已配送
key值 {@link OfflineDeliveryStatus}
   */
  deliveryStatusChangeTime?: DeliveryStatusChangeTimeRequest
  /**
   * 配送方式
0:无 1：自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethodList?: Array<number>
  /**
   * 快递信息
   */
  express?: ExpressRequest
  /**
   * 自取信息
   */
  takeResult?: TakeResultRequest
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddressRequest
}

/**
 * 取件信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class TakeResultRequest {
  /**
   * 领取人
   */
  takePerson?: string
}

/**
 * 发票查询参数
<AUTHOR>
@date 2022/03/23
 */
export class OnlineInvoiceRequest {
  /**
   * 发票id集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基础信息查询参数
   */
  basicData?: OnlineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfoList?: Array<InvoiceAssociationInfoRequest>
  /**
   * 蓝票票据查询参数
   */
  blueInvoiceItem?: OnlineInvoiceItemRequest
  /**
   * 红票票据查询参数
   */
  redInvoiceItem?: OnlineInvoiceItemRequest
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 所属单位id集合
   */
  unitIds?: Array<string>
  /**
   * 收款账号id
   */
  receiveAccountId?: Array<string>
  /**
   * 期别名称
   */
  issueId?: Array<string>
  jobName?: string
  metaData?: ObsFileMetaData
}

/**
 * 发票开具状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class BillStatusChangeTimeRequest {
  /**
   * 未开具
   */
  unBill?: DateScopeExcelRequest
  /**
   * 开票中
   */
  billing?: DateScopeExcelRequest
  /**
   * 开票成功
   */
  success?: DateScopeExcelRequest
  /**
   * 开票失败
   */
  failure?: DateScopeExcelRequest
}

/**
 * 发票状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class InvoiceStatusChangeTimeRequest {
  /**
   * 正常
   */
  normal?: DateScopeExcelRequest
  /**
   * 作废
   */
  invalid?: DateScopeExcelRequest
}

/**
 * 发票基础信息查询参数
<AUTHOR>
@date 2022/03/23
 */
export class OnlineInvoiceBasicDataRequest {
  /**
   * 发票类型
1:电子发票 2:纸质发票
@see InvoiceTypes
   */
  invoiceType?: number
  /**
   * 发票种类
1:普通发票 2:增值税普通发票 3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategoryList?: Array<number>
  /**
   * 发票状态变更时间
@see InvoiceStatus
   */
  invoiceStatusChangeTime?: InvoiceStatusChangeTimeRequest
  /**
   * 发票状态
1：正常 2：作废
@see InvoiceStatus
   */
  invoiceStatusList?: Array<number>
  /**
   * 蓝票票据开具状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  blueInvoiceItemBillStatusList?: Array<number>
  /**
   * 红票票据开具状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  redInvoiceItemBillStatusList?: Array<number>
  /**
   * 发票是否已冲红
   */
  flushed?: boolean
  /**
   * 发票是否已生成红票票据
   */
  redInvoiceItemExist?: boolean
  /**
   * 商品id集合
   */
  commoditySkuIdList?: Array<string>
  /**
   * 发票是否冻结
   */
  freeze?: boolean
}

/**
 * 发票票据
<AUTHOR>
@date 2022/03/18
 */
export class OnlineInvoiceItemRequest {
  /**
   * 票据开具状态变更时间
   */
  billStatusChangeTime?: BillStatusChangeTimeRequest
  /**
   * 发票号码
   */
  billNoList?: Array<string>
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class BatchOrderRequest {
  /**
   * 批次单号集合
   */
  batchOrderNoList?: Array<string>
  /**
   * 批次单基本信息查询参数
   */
  basicData?: BatchOrderBasicDataRequest
  /**
   * 批次单支付信息查询参数
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 批次单创建人查询参数
   */
  creatorIdList?: Array<string>
  /**
   * 是否已经申请发票
   */
  isInvoiceApplied?: boolean
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 批次单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class BatchOrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: BatchOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 批次单基本信息查询参数
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderBasicDataRequest {
  /**
   * 批次单状态
0: 未确认，批次单初始状态
1: 正常
2: 交易完成
3: 交易关闭
4: 提交处理中 提交处理完成后，变更为NORMAl
5: 取消处理中
@see BatchOrderStatus
   */
  batchOrderStatusList?: Array<number>
  /**
   * 批次单状态变更时间
   */
  batchOrderStatusChangeTime?: BatchOrderStatusChangeTimeRequest
  /**
   * 批次单支付状态
<p>
0：未支付
1：支付中
2：已支付
@see BatchOrderPaymentStatus
   */
  batchOrderPaymentStatusList?: Array<number>
  /**
   * 批次单发货状态
0: 未发货
1: 发货中
2: 已发货
@see BatchOrderDeliveryStatus
   */
  batchOrderDeliveryStatusList?: Array<number>
  /**
   * 批次单价格范围
<p> 查询非0元批次单 begin填0.01
   */
  batchOrderAmountScope?: BigDecimalScopeExcelRequest
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 批次单状态变更时间查询参数
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderStatusChangeTimeRequest {
  /**
   * 未确认
   */
  unConfirmed?: DateScopeExcelRequest
  /**
   * 正常
   */
  normal?: DateScopeExcelRequest
  /**
   * 交易成功
   */
  completed?: DateScopeExcelRequest
  /**
   * 已关闭
   */
  closed?: DateScopeExcelRequest
  /**
   * 提交中
   */
  committing?: DateScopeExcelRequest
  /**
   * 取消处理中
   */
  canceling?: DateScopeExcelRequest
}

/**
 * 批次退货单查询参数
<AUTHOR>
@date 2022/04/19
 */
export class BatchReturnOrderRequest {
  /**
   * 批次退货单号集合
   */
  batchReturnOrderList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: BatchReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: BatchReturnOrderApprovalInfoRequest
  /**
   * 批次退货单关联批次单
   */
  batchOrderInfo?: BatchOrderInfoRequest
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 批次退货单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class BatchReturnOrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: BatchReturnOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 批次退货单关联批次单查询参数
<AUTHOR>
@date 2022/04/19
 */
export class BatchOrderInfoRequest {
  /**
   * 批次单号集合
   */
  batchOrderNoList?: Array<string>
  /**
   * 批次单创建人id集合
   */
  creatorIdList?: Array<string>
  /**
   * 收款账号ID集合
   */
  receiveAccountIdList?: Array<string>
  /**
   * 交易流水号集合
   */
  flowNoList?: Array<string>
  /**
   * 付款类型
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
   */
  paymentOrderTypeList?: Array<number>
}

/**
 * 批次退货单关闭信息
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnCloseReasonRequest {
  /**
   * 批次退货单关闭类型（1：卖家取消 2：卖家拒绝退货 3：买家取消 4：确认失败取消）
@see BatchReturnCloseTypes
   */
  closeTypeList?: Array<number>
}

/**
 * 批次退货单审批信息查询参数
<AUTHOR>
@date 2022/03/18
 */
export class BatchReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime?: DateScopeExcelRequest
}

/**
 * 批次退货单基本信息查询参数
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnOrderBasicDataRequest {
  /**
   * 批次退货单状态
0: 已创建
1: 已确认
2: 取消申请中
3: 退货处理中
4: 退货失败
5: 正在申请退款
6: 已申请退款
7: 退款处理中
8: 退款失败
9: 退货完成
10: 退款完成
11: 退货退款完成
12: 已关闭
@see BatchReturnOrderStatus
   */
  batchReturnOrderStatus?: Array<number>
  /**
   * 批次退货单关闭信息
   */
  batchReturnCloseReason?: BatchReturnCloseReasonRequest
  /**
   * 批次退货单状态变更时间
   */
  batchReturnStatusChangeTime?: BatchReturnOrderStatusChangeTimeRequest
  /**
   * 退款金额范围
<br> 查询非0元  begin填0.01
   */
  refundAmountScope?: BigDecimalScopeExcelRequest
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 批次退货单状态变更时间查询参数
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied?: DateScopeExcelRequest
  /**
   * 批次退货完成时间
<p> 这个参数包含了退货退款完成（批次退货单类型为退货退款）、仅退货完成（批次退货单类型为仅退货）、仅退款完成（批次退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted?: DateScopeExcelRequest
}

/**
 * 商品查询条件
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuRequest {
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品名称（精确匹配）
   */
  saleTitleList?: Array<string>
  /**
   * 商品名称（模糊查询）
   */
  saleTitleMatchLike?: string
  /**
   * 要从查询结果中剔除的商品ID集合
   */
  notShowCommoditySkuIdList?: Array<string>
  /**
   * 商品售价
   */
  price?: number
  /**
   * 商品上下架信息
   */
  onShelveRequest?: OnShelveRequest
  /**
   * 培训方案信息
   */
  schemeRequest?: SchemeRequest
  /**
   * 商品sku属性查询
   */
  skuPropertyRequest?: SkuPropertyRequest
  /**
   * 是否展示资源不可用的商品
   */
  isDisabledResourceShow?: boolean
  /**
   * 是否展示所有资源
（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
   */
  isShowAll?: boolean
  /**
   * 专题id
   */
  trainingChannelIds?: Array<string>
  /**
   * 是否存在专题
   */
  existTrainingChannel?: boolean
  /**
   * 管理系统平台
   */
  externalTrainingPlatform?: Array<string>
}

/**
 * 商品排序参数
<AUTHOR>
@date 2022/01/27
 */
export class CommoditySkuSortRequest {
  /**
   * 用来排序的字段
   */
  sortField?: CommoditySkuSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 商品上下架相关查询参数
<AUTHOR>
@date 2022/01/25
 */
export class OnShelveRequest {
  /**
   * 商品上下架状态
<br> 0:已下架 1：已上架
   */
  onShelveStatus?: number
}

/**
 * 培训方案相关查询参数
<AUTHOR>
@date 2022/01/25
 */
export class SchemeRequest {
  /**
   * 培训方案ID
   */
  schemeIdList?: Array<string>
  /**
   * 培训方案类型
<br> chooseCourseLearning:选课规则 autonomousCourseLearning:自主学习
   */
  schemeType?: string
  /**
   * 培训方案名称(模糊查询)
   */
  schemeName?: string
  /**
   * 培训开始时间
   */
  trainingBeginDate?: DateScopeRequest
  /**
   * 培训结束时间
   */
  trainingEndDate?: DateScopeRequest
}

/**
 * <AUTHOR>
 */
export class ChooseCourseStatisticsRequest {
  /**
   * 培训班级上的年度
   */
  year?: string
  /**
   * 课件供应商ID
   */
  supplierId?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 课程Id
   */
  courseId?: Array<string>
  /**
   * 选课时间开始
   */
  chooseCourseDateStart?: string
  /**
   * 选课时间结束
   */
  chooseCourseDateEnd?: string
}

/**
 * <AUTHOR>
 */
export class CourseSalesStatisticsRequest {
  /**
   * 课件供应商ID
   */
  supplierId?: Array<string>
  /**
   * 选课时间开始
   */
  chooseCourseDateStart?: string
  /**
   * 选课时间结束
   */
  chooseCourseDateEnd?: string
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class OrderRequest {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 子订单退货状态
0: 未退货
1: 退货申请中
2: 退货中
3: 退货成功
4: 退款中
5: 退款成功
@see SubOrderReturnStatus
   */
  subOrderReturnStatus?: Array<number>
  /**
   * 订单基本信息查询参数
   */
  orderBasicData?: OrderBasicDataRequest
  /**
   * 订单支付信息查询
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 买家查询参数
   */
  buyerIdList?: Array<string>
  /**
   * 发货商品信息
   */
  deliveryCommodity?: CommoditySkuRequest1
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 管理系统平台
   */
  externalTrainingPlatform?: Array<string>
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: OrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 订单基本信息查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderBasicDataRequest {
  /**
   * 订单类型
1:常规订单 2:批次关联订单
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType?: number
  /**
   * 批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 订单状态
<br> 1:正常 2：交易完成 3：交易关闭
@see OrderStatus
   */
  orderStatusList?: Array<number>
  /**
   * 订单支付状态
<br> 0:未支付 1：支付中 2：已支付
@see com.fjhb.domain.trade.api.order.consts.OrderPaymentStatus
   */
  orderPaymentStatusList?: Array<number>
  /**
   * 订单发货状态
<br> 0:未发货 1：发货中 2：已发货
@see com.fjhb.domain.trade.api.order.consts.OrderDeliveryStatus
   */
  orderDeliveryStatusList?: Array<number>
  /**
   * 订单状态变更时间
   */
  orderStatusChangeTime?: OrderStatusChangeTimeRequest
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
  /**
   * 订单价格范围
<br> 查询非0元订单 begin填0.01
   */
  orderAmountScope?: BigDecimalScopeExcelRequest
}

/**
 * 订单支付信息相关查询参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderPayInfoRequest {
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 付款类型
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
   */
  paymentOrderTypeList?: Array<number>
}

/**
 * 订单状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderStatusChangeTimeRequest {
  /**
   * 订单处于正常状态时间范围(创建时间范围)
   */
  normalDateScope?: DateScopeExcelRequest
  /**
   * 订单创建时间范围
   */
  completedDatesScope?: DateScopeExcelRequest
}

/**
 * 退货单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderRequest {
  /**
   * 退货单号
   */
  returnOrderNoList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: ReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: ReturnOrderApprovalInfoRequest
  /**
   * 退货商品id集合
   */
  returnCommoditySkuIdList?: Array<string>
  /**
   * 退款商品id集合
   */
  refundCommoditySkuIdList?: Array<string>
  /**
   * 退货单关联子订单查询参数
   */
  subOrderInfo?: SubOrderInfoRequest
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 退货商品
   */
  returnCommodity?: CommoditySkuRequest1
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class ReturnSortRequest {
  /**
   * 需要排序的字段
   */
  field?: ReturnOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 退货单关闭信息
<AUTHOR>
@date 2022年4月11日 11:33:35
 */
export class ReturnCloseReasonRequest {
  /**
   * 退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
@see ReturnOrderCloseTypes
   */
  closeTypeList?: Array<number>
}

/**
 * 退货单审批信息查询参数
<AUTHOR>
@date 2022/03/18
 */
export class ReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime?: DateScopeExcelRequest
}

/**
 * 退货单基本信息查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderBasicDataRequest {
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
   */
  returnOrderStatus?: Array<number>
  /**
   * 退货单类型
1-仅退货
2-仅退款
3-退货并退款
4-部分退货
5-部分退款
6-部分退货并部分退款
7-部分退货并全额退款
8-全部退货并部分退款
   */
  returnOrderTypes?: Array<number>
  /**
   * 退货单申请来源类型
SUB_ORDER
BATCH_RETURN_ORDER
@see ReturnOrderApplySourceTypes
   */
  applySourceType?: string
  /**
   * 来源ID集合
   */
  applySourceIdList?: Array<string>
  /**
   * 退货单关闭信息
   */
  returnCloseReason?: ReturnCloseReasonRequest
  /**
   * 退货单状态变更时间
   */
  returnStatusChangeTime?: ReturnOrderStatusChangeTimeRequest
  /**
   * 退款金额范围
<br> 查询非0元  begin填0.01
   */
  refundAmountScope?: BigDecimalScopeExcelRequest
}

/**
 * 退货单状态变更时间查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied?: DateScopeExcelRequest
  /**
   * 退货单完成时间
<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted?: DateScopeExcelRequest
}

/**
 * 交易变更记录查询参数
<AUTHOR>
@date 2022/05/09
 */
export class TradeRecordExcelKParam {
  /**
   * 交易时间
   */
  tradeTime?: DateScopeExcelRequest
  /**
   * 订单查询参数
   */
  order?: OrderExcelKParam
  /**
   * 商品查询参数
   */
  commoditySku?: CommoditySkuExcelKParam
  /**
   * 归属信息
   */
  owner?: OwnerKParam
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 排除的销售渠道
0-自营 1-分销 2-专题 3-华医网
   */
  excludedSaleChannels?: Array<number>
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 门户标识
   */
  portalIdentifier?: string
  /**
   * 查看非推广门户数据 | true 为勾选效果
   */
  notDistributionPortal?: boolean
}

/**
 * 商品开通统计报表查询参数
<AUTHOR>
@date 2022/05/11
 */
export class TradeReportRequest {
  /**
   * 交易时间范围
   */
  tradeTime?: DateScopeExcelRequest
  /**
   * 买家所在地区路径
   */
  buyerAreaPath?: Array<string>
  /**
   * 商品查询条件
   */
  commoditySku?: CommoditySkuRequest12
  /**
   * 是否包含集体缴费信息
   */
  containsCollective: boolean
  /**
   * 买家所在地区是否需要包含全部下级地区数据
   */
  isAllCotained?: boolean
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 排除的销售渠道
0-自营 1-分销 2-专题 3-华医网
   */
  excludedSaleChannels?: Array<number>
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 查看非推广门户数据 | true 为勾选效果
   */
  notDistributionPortal?: boolean
  jobName?: string
  metaData?: ObsFileMetaData
}

/**
 * 商品查询参数
<AUTHOR>
@date 2022/05/11
 */
export class CommoditySkuRequest12 {
  /**
   * 商品Sku名称
   */
  saleTitle?: string
  /**
   * 商品sku属性查询
   */
  skuProperty?: SkuPropertyRequest
  /**
   * 学习方案查询参数
   */
  scheme?: SchemeRequest1
  /**
   * 排除的商品List
   */
  excludeCommodityIdlist?: Array<string>
}

/**
 * 方案查询参数
<AUTHOR>
@date 2022/05/11
 */
export class SchemeRequest1 {
  /**
   * 方案类型
@see SchemeType
   */
  schemeType?: string
  /**
   * 方案学时
   */
  schemePeriodScope?: DoubleScopeRequest
}

/**
 * 范围查询条件
<AUTHOR>
@version 1.0
@date 2022/5/7 15:34
 */
export class DateScopeExcelRequest1 {
  /**
   * result >&#x3D; begin
   */
  begin: string
  /**
   * result <&#x3D; end
   */
  end: string
}

/**
 * 商品查询条件
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuExcelKParam1 {
  /**
   * 商品id
   */
  commoditySkuIdList: Array<string>
  /**
   * 商品Sku名称
   */
  saleTitle: string
  /**
   * 商品sku属性查询
   */
  skuProperty: SkuPropertyExcelKParam1
}

/**
 * 地区查询参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertyExcelKParam1 {
  /**
   * 地区: 省
   */
  province: string
  /**
   * 地区: 市
   */
  city: string
  /**
   * 地区: 区县
   */
  county: string
}

/**
 * 地区查询请求参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertySearchExcelKParam1 {
  /**
   * 地区匹配方式
<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
@see RegionSearchType
   */
  regionSearchType: number
  /**
   * 地区
   */
  region: Array<RegionSkuPropertyExcelKParam1>
}

/**
 * 商品sku属性查询参数
<AUTHOR>
@date 2022/01/25
 */
export class SkuPropertyExcelKParam1 {
  /**
   * 年度
   */
  year: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch: RegionSkuPropertySearchExcelKParam1
  /**
   * 行业
   */
  industry: Array<string>
  /**
   * 科目类型
   */
  subjectType: Array<string>
  /**
   * 培训类别
   */
  trainingCategory: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional: Array<string>
  /**
   * 学段
   */
  learningPhase: Array<string>
  /**
   * 学科
   */
  discipline: Array<string>
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType: Array<string>
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory: Array<string>
  /**
   * 工勤行业-工种
   */
  jobCategory: Array<string>
  /**
   * 卫生行业-培训对象
   */
  trainingObject: Array<string>
}

/**
 * 子订单状态变更模型的订单查询参数
<AUTHOR>
@date 2022/05/09
 */
export class OrderExcelKParam1 {
  /**
   * 买家所在地区路径
   */
  buyerAreaPath: Array<RegionExcelKParam1>
}

/**
 * 地区查询参数
<AUTHOR>
@date 2022/05/09
 */
export class RegionExcelKParam1 {
  /**
   * 省code
   */
  province: string
  /**
   * 市code
   */
  city: string
  /**
   * 县code
   */
  county: string
  /**
   * 路径 省-市-县
   */
  path: string
}

/**
 * @Description
<AUTHOR>
@Date 16:18 2022/5/20
 */
export class OwnerKParam1 {
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 企业账号id
   */
  rootAccountId: string
  /**
   * 所属服务商类型
@see com.fjhb.domain.basicdata.api.servicer.consts.ServicerTypes
   */
  servicerType: number
  /**
   * 所属服务商ID
   */
  servicerId: string
  /**
   * 平台租户id
   */
  tenantId: string
  /**
   * 平台租户id集合
   */
  tenantIdList: Array<string>
}

/**
 * 交易变更记录查询参数
<AUTHOR>
@date 2022/05/09
 */
export class TradeRecordExcelKParam1 {
  /**
   * 交易时间
   */
  tradeTime: DateScopeExcelRequest1
  /**
   * 订单查询参数
   */
  order: OrderExcelKParam1
  /**
   * 商品查询参数
   */
  commoditySku: CommoditySkuExcelKParam1
  /**
   * 归属信息
   */
  owner: OwnerKParam1
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels: Array<number>
  /**
   * 排除的销售渠道
0-自营 1-分销 2-专题 3-华医网
   */
  excludedSaleChannels: Array<number>
  /**
   * 专题id
   */
  saleChannelIds: Array<string>
  /**
   * 分销商id
   */
  distributorId: string
  /**
   * 门户标识
   */
  portalIdentifier: string
  /**
   * 查看非推广门户数据 | true 为勾选效果
   */
  notDistributionPortal: boolean
}

/**
 * 异步任务组名返回对象
 */
export class JobGroupResponse {
  /**
   * 异步任务组key
   */
  group: string
  /**
   * 异步任务组名
   */
  groupName: string
  /**
   * 排序大小
   */
  order: number
  /**
   * 所在域
   */
  domain: Array<string>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出批次单明细
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchOrderDetailInServicer(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchOrderDetailInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 导出批次单明细
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchOrderDetailInTrainingChannel(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchOrderDetailInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次单
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchOrderInServicer(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchOrderInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 导出批次单
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchOrderInTrainingChannel(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchOrderInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次对账
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReconciliationInServicer(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReconciliationInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 导出批次对账
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReconciliationInTrainingChannel(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReconciliationInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次退货单明细
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReturnOrderDetailExcelInServicer(
    params: { request?: BatchReturnOrderRequest; sort?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReturnOrderDetailExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 导出批次退货单明细
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReturnOrderDetailExcelInTrainingChannel(
    params: { request?: BatchReturnOrderRequest; sort?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReturnOrderDetailExcelInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReturnOrderExcelInServicer(
    params: { request?: BatchReturnOrderRequest; sort?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReturnOrderExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 导出批次退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReturnOrderExcelInTrainingChannel(
    params: { request?: BatchReturnOrderRequest; sort?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReturnOrderExcelInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次退货报名对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReturnReconciliationExcelInServicer(
    params: { request?: BatchReturnOrderRequest; sort?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReturnReconciliationExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 导出批次退货报名对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReturnReconciliationExcelInTrainingChannel(
    params: { request?: BatchReturnOrderRequest; sort?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReturnReconciliationExcelInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出中心财务数据表
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCentralFinancialDataInServicer(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCentralFinancialDataInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出学员选课统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportChooseCourseStatistic(
    request: ChooseCourseStatisticsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportChooseCourseStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出商品开通统计列表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommodityOpenReportFormsInServicer(
    request: TradeReportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommodityOpenReportFormsInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 商品导出
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommoditySkuInServicer(
    params: { queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommoditySkuInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出方案配置专题信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommoditySkuWithTrainingChannelInServicer(
    params: { queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommoditySkuWithTrainingChannelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出培训单位销售统计报表   课件供应商维度
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCourseSalesStatistics(
    request: CourseSalesStatisticsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCourseSalesStatistics,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 发票配送导出
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportInvoiceDeliveryInServicer(
    request: OfflineInvoiceExportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportInvoiceDeliveryInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 发票配送导出
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportInvoiceDeliveryInTrainingChannel(
    request: OfflineInvoiceExportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportInvoiceDeliveryInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 商品期别明细列表导出
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportIssueCommoditySkuInServicer(
    params: { queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportIssueCommoditySkuInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 线下发票导出-继续教育
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOfflineInvoiceInServicerForJxjy(
    request: OfflineInvoiceExportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOfflineInvoiceInServicerForJxjy,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 线下发票导出-继续教育
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOfflineInvoiceInTrainingChannelForJxjy(
    request: OfflineInvoiceExportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOfflineInvoiceInTrainingChannelForJxjy,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 线上发票导出-继续教育
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOnlineInvoiceInServicerForJxjy(
    request: OnlineInvoiceRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOnlineInvoiceInServicerForJxjy,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 线上发票导出-继续教育
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOnlineInvoiceInTrainingChannelForJxjy(
    request: OnlineInvoiceRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOnlineInvoiceInTrainingChannelForJxjy,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人订单
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOrderExcelInServicer(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOrderExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 导出个人订单
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOrderExcelInTrainingChannel(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOrderExcelInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人对账
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReconciliationExcelInServicer(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReconciliationExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 导出个人对账
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReconciliationExcelInTrainingChannel(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReconciliationExcelInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出地区开通统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportRegionOpenReportFormsInServier(
    request: TradeReportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionOpenReportFormsInServier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnOrderExcelInServicer(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReturnOrderExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 导出个人退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnOrderExcelInTrainingChannel(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReturnOrderExcelInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人报名退货对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnReconciliationExcelInServicer(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReturnReconciliationExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 导出个人报名退货对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnReconciliationExcelInTrainingChannel(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReturnReconciliationExcelInTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：学员导出
   * @return : void
   * @Author： wtl
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentExcelInServicer(
    request: StudentQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：项目级-学员导出
   * @return : void
   * @Author： wtl
   * @Date： 2022年11月17日 15:25:00
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentExcelInSubProject(
    request: StudentQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentExcelInSubProject,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出专题列表 - 网校超管
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTrainingChannel(
    request: TrainingChannelRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出专题的方案
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTrainingChannelCommoditySkuInServicer(
    params: { queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportTrainingChannelCommoditySkuInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出专题明细-专题管理员
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTrainingChannelCommoditySkuInTrainingChannelAdmin(
    params: { queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportTrainingChannelCommoditySkuInTrainingChannelAdmin,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出专题列表 -专题管理员
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTrainingChannelInTrainingChannelAdmin(
    request: TrainingChannelRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportTrainingChannelInTrainingChannelAdmin,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页查询导出任务组
   * @param jobGroupRequest : 任务查询条件
   * @return : void
   * @Author： sjm
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param jobGroupRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExportTaskGroupInfoInServicer(
    jobGroupRequest: JobGroupRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listExportTaskGroupInfoInServicer,
    operation?: string
  ): Promise<Response<Array<JobGroupResponse>>> {
    return commonRequestApi<Array<JobGroupResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { jobGroupRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页查询导出任务组  （专题管理员）
   * @param jobGroupRequest : 任务查询条件
   * @return : void
   * @Author： sjm
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param jobGroupRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExportTaskGroupInfoInTrainingChannel(
    jobGroupRequest: JobGroupRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listExportTaskGroupInfoInTrainingChannel,
    operation?: string
  ): Promise<Response<Array<JobGroupResponse>>> {
    return commonRequestApi<Array<JobGroupResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { jobGroupRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async mapTradeRecordParam(
    request: TradeReportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.mapTradeRecordParam,
    operation?: string
  ): Promise<Response<TradeRecordExcelKParam1>> {
    return commonRequestApi<TradeRecordExcelKParam1>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async mapTrainingChannelParam(
    params: { request?: TradeReportRequest; param?: TradeRecordExcelKParam },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.mapTrainingChannelParam,
    operation?: string
  ): Promise<Response<TradeRecordExcelKParam1>> {
    return commonRequestApi<TradeRecordExcelKParam1>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
