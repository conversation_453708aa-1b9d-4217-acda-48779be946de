import ChooseProviderItem from '@api/service/management/resource/course-sort-config/model/ChooseProviderItem'
import CourseLearningBackstage, {
  BaseResponse,
  EffectiveScopeEnum,
  SortCourseNumRequest,
  SortCourseNumResponse,
  UpdateCourseSortRuleConfigRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import QueryServiceProvider, {
  ProviderAdminQueryRequest
} from '@api/service/management/authority/service-provider/query/QueryServiceProvider'
import { Page, Response } from '@hbfe/common'
import CoursewareSupplierListDetail from '@api/service/management/authority/service-provider/query/vo/CoursewareSupplierListDetail'
import UseRangeEnum from '@api/service/management/resource/course-sort-config/enums/UseRangeEnum'

/**
 * 选课排序配置基础信息类
 */
class CourseConfigBaseInfo {
  /**
   * 规则名称
   */
  ruleName: string = undefined

  /**
   * 是否乱序
   */
  unSort = false

  /**
   * 排序更新周期
   */
  updatePeriod: string = undefined

  /**
   * 使用范围
   */
  useRange: string | number = undefined
}

/**
 * 选课排序配置详情类
 */
export default class CourseSortChooseModule {
  /**
   * 规则id
   */
  private ruleId: string = undefined

  /**
   * 基础信息
   */
  baseInfo: CourseConfigBaseInfo = new CourseConfigBaseInfo()

  /**
   * 可选课件供应商筛选名称
   */
  waitSelectFilterProviderName = ''

  /**
   * 待选课件供应商
   */
  waitSelectServiceProviderList: Array<ChooseProviderItem> = new Array<ChooseProviderItem>()

  /**
   * 已选课件供应商
   */
  selectedServiceProviderList: Array<ChooseProviderItem> = new Array<ChooseProviderItem>()

  /**
   * 实例化
   * @param ruleId 排序规则id
   */
  constructor(ruleId: string) {
    this.ruleId = ruleId
  }

  /**
   * 初始化 - 加载详情信息 - 查询可选课件供应商
   */
  async init() {
    if (this.ruleId) {
      await this.queryChooseServiceProvider()
    }
  }

  /**
   * 查询可选课件供应商列表
   * @param page
   */
  async queryWaitSelectServiceProvider(page: Page): Promise<Array<ChooseProviderItem>> {
    const rq = new ProviderAdminQueryRequest()
    rq.user.userName = this.waitSelectFilterProviderName

    const queryServiceProvider = new QueryServiceProvider()
    const res = await queryServiceProvider.queryCoursewareSupplierList(page, rq)

    const result =
      (res?.length &&
        res.map((item: CoursewareSupplierListDetail) =>
          ChooseProviderItem.unSelectFrom(item, this.selectedServiceProviderList)
        )) ||
      []

    this.waitSelectServiceProviderList = result

    return this.waitSelectServiceProviderList
  }

  /**
   * 选择课件供应商
   * @param providerItem 课件供应商项
   */
  selectServiceProvider(providerItem: ChooseProviderItem) {
    // 重复勾选
    if (providerItem.select) {
      return
    }
    providerItem.select = true
    this.selectedServiceProviderList.push(providerItem)
  }

  /**
   * 取消选择课件供应商
   * @param providerItem 课件供应商项
   */
  deselectServiceProvider(providerItem: ChooseProviderItem) {
    // 重复取消
    if (!providerItem.select) {
      return
    }
    providerItem.courseConfigNum = 0
    const waitSelectItem = this.waitSelectServiceProviderList.find(
      item => item.serviceProviderId === providerItem.serviceProviderId
    )
    if (waitSelectItem) {
      waitSelectItem.select = false
    }
    const selectItemIndex = this.selectedServiceProviderList.findIndex(
      item => item.serviceProviderId === providerItem.serviceProviderId
    )
    if (selectItemIndex != -1) {
      this.selectedServiceProviderList.splice(selectItemIndex, 1)
    }
  }

  /**
   * 保存选择
   */
  async saveChange(): Promise<Response<BaseResponse>> {
    const rq = new UpdateCourseSortRuleConfigRequest()

    rq.configId = this.ruleId
    rq.updateSortCourseNum = true
    rq.sortCourseNum = this.selectedServiceProviderList.map((item: ChooseProviderItem) => {
      const dto = new SortCourseNumRequest()
      dto.coursewareSupplierId = item.serviceProviderId
      dto.num = item.courseConfigNum
      return dto
    })

    const res = await CourseLearningBackstage.updateCourseSortRuleConfigInServicer(rq)

    return res
  }

  /**
   * 查询排序配置详情信息
   */
  private async queryChooseServiceProvider(): Promise<Array<ChooseProviderItem>> {
    /** 查询基础信息 **/
    const res = await CourseLearningBackstage.getCourseSortRuleConfigDetailInServicer(this.ruleId)

    this.baseInfo.ruleName = res?.data?.name
    this.baseInfo.updatePeriod = res?.data?.cronDescribe
    this.baseInfo.unSort = res?.data?.isShuffle
    this.baseInfo.useRange = new UseRangeEnum(res?.data?.effectiveScope).toString()

    const chooseProviderItems = (res?.data?.sortCourseNum?.length && res.data.sortCourseNum) || []

    /** 查询课件供应商详情 **/
    const providerIds = chooseProviderItems
      .filter(it => it.coursewareSupplierId)
      .map((item: SortCourseNumResponse) => item.coursewareSupplierId)
    const queryServiceProvider = new QueryServiceProvider()
    const queryProviderPage = new Page()
    const queryProviderRq = new ProviderAdminQueryRequest()

    queryProviderPage.pageNo = 1
    queryProviderPage.pageSize = providerIds.length || 1
    queryProviderRq.businessOwnerInfo.servicerIdList = providerIds

    const queryProviderRes = await queryServiceProvider.queryCoursewareSupplierList(queryProviderPage, queryProviderRq)

    this.selectedServiceProviderList = chooseProviderItems.map((item: SortCourseNumResponse) =>
      ChooseProviderItem.selectFrom(
        item,
        queryProviderRes?.length && queryProviderRes.find(it => it.coursewareId === item.coursewareSupplierId)
      )
    )

    /** 若待选列表已初始化则对待选列表进行勾选 **/
    this.waitSelectServiceProviderList.map((item: ChooseProviderItem) => {
      if (this.selectedServiceProviderList.find(it => it.serviceProviderId === item.serviceProviderId)) {
        item.select = true
      }
    })

    return this.selectedServiceProviderList
  }
}
