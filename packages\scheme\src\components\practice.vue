<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div class="f-plr20">
      <div class="f-pt5 f-mtb20">
        <!--有修改的添加 <i class="is-tag"></i>-->
        <!--<i class="is-tag"></i>-->
        <span class="f-mr10">请选择练习来源：</span>
        <el-radio-group
          v-model="practiceLearningInfo.type"
          :disabled="!enableCourseQuiz"
          @change="handlePracticeLearningTypeChange"
        >
          <el-radio :label="1">题库</el-radio>
          <el-radio :label="2" :disabled="!hasConfigCourseLearning">按照学员课程ID出题</el-radio>
          <el-radio :label="3" :disabled="!hasConfigExam">同考试</el-radio>
        </el-radio-group>
      </div>
      <el-button
        type="primary"
        icon="el-icon-plus"
        class="f-mb15"
        v-show="practiceLearningInfo.type === 1"
        @click="popCourseQuiz"
      >
        添加题库
      </el-button>
      <el-table
        stripe
        v-show="practiceLearningInfo.type === 1"
        :data="selectedQuestionLibraryList"
        max-height="500px"
        class="m-table"
      >
        <!--<el-table-column width="30" align="center" fixed="left">
          <template slot-scope="scope">
            &lt;!&ndash;有新增或者修改的添加 <i class="is-tag"></i>&ndash;&gt;
            <div v-if="scope.$index === 0"><i class="is-tag"></i></div>
            <div v-else></div>
          </template>
        </el-table-column>-->
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="题库名称" min-width="240" fixed="left">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column label="已启用的试题数量" min-width="190" align="center">
          <template slot-scope="scope">{{ scope.row.enabledQuestionCount }}</template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template slot-scope="scope">
            <el-popconfirm
              title="确定移除该题库吗？"
              icon="el-icon-info"
              icon-color="red"
              confirm-button-text="确定移除"
              cancel-button-text="取消"
              @confirm="removeQuestionLibrary(scope.row)"
            >
              <el-button type="text" size="mini" slot="reference">移除</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <el-form ref="form" label-width="100px" class="m-text-form is-column f-mt30">
        <el-form-item label="前置条件：">无</el-form-item>
        <el-form-item label="培训要求：">无</el-form-item>
      </el-form>
    </div>
    <!--选择题库-->
    <el-drawer
      title="选择题库"
      :visible.sync="uiConfig.dialog.pagePracticeDialogVisible"
      size="1000px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <el-row :gutter="16" class="m-query f-mt10">
          <el-form :inline="true">
            <el-col :span="10">
              <el-form-item label="题库名称">
                <el-input v-model="libraryName" clearable placeholder="请输入题库名称" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-button type="primary" @click="searchQuestionLibrary">查询</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table
          stripe
          :data="questionLibraryList"
          v-loading="query.loading"
          max-height="500px"
          class="m-table f-mt10"
        >
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="题库名称" min-width="300">
            <template slot-scope="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column label="已启用的试题数量" width="180" align="center">
            <template slot-scope="scope">{{ scope.row.enabledQuestionCount }}</template>
          </el-table-column>
          <el-table-column label="创建人" min-width="120">
            <template slot-scope="scope">{{ scope.row.createUserName }}</template>
          </el-table-column>
          <el-table-column label="创建时间" min-width="180">
            <template slot-scope="scope">{{ scope.row.createTime }}</template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <el-checkbox
                v-model="scope.row.isSelected"
                label="选择"
                @change="
                  (value) => {
                    return handleCheckStatusChange(value, scope.row)
                  }
                "
              ></el-checkbox>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button @click="cancelSelectQuestionLibrary">取消</el-button>
        <el-button type="primary" @click="confirmSelectQuestionLibrary">确定</el-button>
      </div>
    </el-drawer>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, Prop, PropSync } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import PracticeLearningType from '@api/service/management/train-class/mutation/vo/PracticeLearningType'
  import { cloneDeep } from 'lodash'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import LibraryResponseVo from '@api/service/management/resource/question-library/query/vo/LibraryResponseVo'
  import QueryQuestionLibrary from '@api/service/management/resource/question-library/query/QueryQuestionLibrary'
  import LibraryRequestVo from '@api/service/management/resource/question-library/query/vo/LibraryRequestVo'
  import ExamLearningType from '@api/service/management/train-class/mutation/vo/ExamLearningType'
  import CourseLearningLearningType from '@api/service/management/train-class/mutation/vo/CourseLearningLearningType'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import CreateSchemeUIModule from '@/store/modules-ui/scheme/CreateSchemeUIModule'

  @Component
  export default class extends Vue {
    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number

    /**
     * 练习配置 - 双向绑定
     */
    @PropSync('practiceLearn', { type: PracticeLearningType }) practiceLearningInfo!: PracticeLearningType

    /**
     * 是否勾选“班级考试”
     */
    @Prop({
      required: true,
      type: ExamLearningType
    })
    examLearningInfo: ExamLearningType

    /**
     * 课程学习信息
     */
    @Prop({
      required: true,
      type: CourseLearningLearningType
    })
    courseLearningInfo: CourseLearningLearningType

    /**
     * ui相关的变量控制
     */
    uiConfig = {
      // 对话框是否展示
      dialog: {
        // 选择题库
        pagePracticeDialogVisible: false
      }
    }

    // 课程包名称
    courseCategoryName = ''

    // 查询参数 - 题库
    pageQueryParam: LibraryRequestVo = new LibraryResponseVo()
    // 分页相关 - 题库
    page: UiPage
    // 查询相关 - 题库
    query: Query = new Query()
    // 分页列表 - 题库
    questionLibraryList: Array<LibraryResponseVo> = new Array<LibraryResponseVo>()
    // 已选题库列表
    selectedQuestionLibraryList: Array<LibraryResponseVo> = new Array<LibraryResponseVo>()
    // 暂存题库id集合（已选中待提交）
    temporaryQuestionLibraryIds: Array<string> = new Array<string>()
    // 查询题库 - 业务状态层
    questionLibraryBusinessRemote: QueryQuestionLibrary
    // 题库名称 - 添加题库
    libraryName = ''

    /**
     * 是否勾选班级练习
     */
    get enableCourseQuiz() {
      return this.practiceLearningInfo.isSelected
    }

    /**
     * 是否配置班级考试
     */
    get hasConfigExam() {
      return this.examLearningInfo.isSelected && this.examLearningInfo.paperPublishConfigureId ? true : false
    }

    /**
     * 培训方案类型，1-选课规则，2-自主选课，默认：0
     */
    get schemeType() {
      return CreateSchemeUIModule.schemeType
    }

    /**
     * 是否配置课程
     */
    get hasConfigureCourse() {
      if (this.schemeType === 1) {
        // 选课模式
        const hasCourseNode = CreateSchemeUtils.treeFind<Classification>(
          this.courseLearningInfo.classification.childOutlines,
          (node: Classification) => {
            return node.coursePackageId && node.coursePackageId !== ''
          },
          'childOutlines'
        )
        return hasCourseNode ? true : false
      } else {
        // 自主选课
        const classification = this.courseLearningInfo.classification
        if (CreateSchemeUtils.isWeightyArray(classification.childOutlines)) {
          // 有分类
          const hasCourseNode = CreateSchemeUtils.treeFind<Classification>(
            this.courseLearningInfo.classification.childOutlines,
            (node: Classification) => {
              return node.coursePackageId && node.coursePackageId !== ''
            },
            'childOutlines'
          )
          return hasCourseNode ? true : false
        } else {
          // 无分类
          return classification.coursePackageId ? true : false
        }
      }
    }

    /**
     *
     */
    get hasConfigCourseLearning() {
      return this.courseLearningInfo.isSelected && this.hasConfigureCourse ? true : false
    }

    constructor() {
      super()
      this.questionLibraryBusinessRemote = ResourceModule.queryQuestionLibraryFactory.queryQuestionLibraryMultiton
      this.page = new UiPage(this.pageQuestionLibrary, this.pageQuestionLibrary)
    }

    /**
     * 页面数据初始化
     */
    async created() {
      if (this.routerMode !== 1) {
        if (this.practiceLearningInfo.libraryIds && this.practiceLearningInfo.libraryIds.length) {
          await this.getSelectedQuestionLibraryList()
        }
      }
    }

    /**
     * 题库分页查询
     */
    async searchQuestionLibrary() {
      this.page.pageNo = 1
      await this.pageQuestionLibrary()
    }

    /**
     * 题库分页查询
     */
    async pageQuestionLibrary() {
      this.query.loading = true
      try {
        this.pageQueryParam.libraryName = this.libraryName || undefined
        // 过滤题库id集合
        this.pageQueryParam.excludeLibraryIdList = this.getExcludeLibraryIdList()
        this.pageQueryParam.parentLibraryId = undefined
        // 取值
        const response = await this.questionLibraryBusinessRemote.queryQuestionBankLibrary(
          this.page,
          this.pageQueryParam
        )
        if (!response.status.isSuccess()) {
          this.$message.error((response.status?.message as string) || '获取题库列表失败！')
        }
        // 赋值
        this.questionLibraryList = response.data || ([] as LibraryResponseVo[])
        // 遍历设置是否已选
        this.questionLibraryList?.forEach((el: LibraryResponseVo) => {
          el.isSelected = false
          const index = this.temporaryQuestionLibraryIds.findIndex((item) => {
            return item === el.libraryId
          })
          if (index > -1) {
            el.isSelected = true
          }
        })
        console.log('questionLibraryList', this.questionLibraryList)
      } catch (e) {
        console.log('获取题库列表失败 - ', e)
      } finally {
        this.query.loading = false
      }
    }

    /**
     * 获取已选题库id集合
     */
    getExcludeLibraryIdList(): string[] | undefined {
      const libraryIds = this.practiceLearningInfo.libraryIds
      return CreateSchemeUtils.isWeightyArray(libraryIds) ? libraryIds : undefined
    }

    /**
     * 获取已选课程列表
     */
    async getSelectedQuestionLibraryList() {
      const questionLibraryList: Array<LibraryResponseVo> = await Promise.all(
        this.practiceLearningInfo.libraryIds.map(async (item) => {
          const response = await this.questionLibraryBusinessRemote.queryQuestionBankLibraryDetail(item)
          const question = response?.data || new LibraryResponseVo()
          return question
        })
      )
      this.selectedQuestionLibraryList = questionLibraryList || ([] as LibraryResponseVo[])
    }

    /**
     * 切换练习来源响应事件
     */
    handlePracticeLearningTypeChange(value: number) {
      if (value === 2 || value === 3) {
        this.practiceLearningInfo.libraryIds = new Array<string>()
        this.selectedQuestionLibraryList = new Array<LibraryResponseVo>()
      }
      if (value === 3) {
        this.practiceLearningInfo.paperPublishConfigureId = this.examLearningInfo.paperPublishConfigureId
      }
      if (value === 1 || value === 2) {
        this.practiceLearningInfo.paperPublishConfigureId = ''
      }
    }

    /**
     * 添加题库
     */
    async popCourseQuiz() {
      const canOperate = this.canOperate()
      if (!canOperate) return
      this.temporaryQuestionLibraryIds = new Array<string>()
      this.temporaryQuestionLibraryIds = cloneDeep(this.practiceLearningInfo.libraryIds)
      this.pageQueryParam = new LibraryRequestVo()
      this.libraryName = ''
      await this.searchQuestionLibrary()
      this.uiConfig.dialog.pagePracticeDialogVisible = true
    }

    /**
     * 取消 - 选择题库
     */
    cancelSelectQuestionLibrary() {
      this.uiConfig.dialog.pagePracticeDialogVisible = false
    }

    /**
     * 确定 - 选择题库
     */
    async confirmSelectQuestionLibrary() {
      this.practiceLearningInfo.libraryIds = new Array<string>()
      this.practiceLearningInfo.libraryIds = cloneDeep(this.temporaryQuestionLibraryIds)
      await this.getSelectedQuestionLibraryList()
      this.uiConfig.dialog.pagePracticeDialogVisible = false
    }

    /**
     * 校验是否允许操作
     */
    canOperate() {
      let result = true
      if (!this.enableCourseQuiz) {
        result = false
        this.$message.error('请先勾选“练习”的学习内容，再配置题库！')
      }
      return result
    }

    /**
     * 选中状态变化响应事件
     */
    handleCheckStatusChange(value: boolean, node: LibraryResponseVo) {
      const toggleLibraryId = node.id
      const index = this.temporaryQuestionLibraryIds.findIndex((el) => {
        return el === toggleLibraryId
      })
      if (value) {
        if (index === -1) {
          this.temporaryQuestionLibraryIds.push(toggleLibraryId)
        }
      } else {
        if (index > -1) {
          this.temporaryQuestionLibraryIds.splice(index, 1)
        }
      }
    }

    /**
     * 移除已选题库
     */
    removeQuestionLibrary(item: LibraryResponseVo) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      const delLibraryId = item.id
      let delIndex = this.practiceLearningInfo.libraryIds.findIndex((el) => {
        return el === delLibraryId
      })
      if (delIndex > -1) {
        this.practiceLearningInfo.libraryIds.splice(delIndex, 1)
      }
      delIndex = this.selectedQuestionLibraryList.findIndex((el: LibraryResponseVo) => {
        return el.id === delLibraryId
      })
      if (delIndex > -1) {
        this.selectedQuestionLibraryList.splice(delIndex, 1)
      }
    }

    /**
     * 重置练习配置
     */
    resetPractice() {
      this.selectedQuestionLibraryList = [] as LibraryResponseVo[]
      this.temporaryQuestionLibraryIds = [] as string[]
    }
  }
</script>
