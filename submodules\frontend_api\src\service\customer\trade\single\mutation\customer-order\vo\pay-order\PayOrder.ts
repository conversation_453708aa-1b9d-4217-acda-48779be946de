import orderGateway from '@api/gateway/ms-order-v1'
import PayOrderParams from './PayOrderParams'
import commonError from '@api/service/common/helpers/decorator/error/common'
// import { TerminalEnum } from '@api/service/customer/trade/enums/Terminal'
export default class CreateOrder {
  // //  支付订单入参
  // payOrderParams: PayOrderParams
  // constructor(purchaseChannelTerminal?: TerminalEnum, orderNo?: string) {
  //   this.payOrderParams = new PayOrderParams(purchaseChannelTerminal, orderNo)
  // }
  // /**
  //  * 申请在线支付
  //  */
  // @commonError
  // async doPayOrder() {
  //   return await orderGateway.onlinePayOrder(this.payOrderParams.to())
  // }
}
