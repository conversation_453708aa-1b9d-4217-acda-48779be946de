<template>
  <el-autocomplete
    v-model="selectValue"
    :disabled="disabled"
    :fetch-suggestions="querySearchAsync"
    placeholder="请输入并选择地址"
    @select="handleSelect"
    @input="handleChange"
    :debounce="500"
    value-key="title"
    :trigger-on-focus="false"
    @clear="clearSelected"
    @blur="blurChange"
    class="autocomplete"
    clearable
  ></el-autocomplete>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import QueryRegionInfo from '@api/service/common/tx-map/QueryRegionInfo'
  import axios from 'axios'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'

  @Component
  export default class MapAutocomplete extends Vue {
    // v-model绑定的值
    @Prop({ type: String, default: '' }) value!: string

    /**
     * 是否禁用
     */
    @Prop({ type: <PERSON><PERSON>an, default: false }) disabled!: boolean

    /**
     * 搜索框当前值
     */
    selectValue = ''
    queryRegionInfo = new QueryRegionInfo()

    /**
     * 选中地址的名称
     */
    selectedName = ''

    /**
     * 腾讯地图 API Key
     */
    apiKey = ''

    /**
     * value赋值
     */
    @Watch('value', { immediate: true })
    valueChange(val: string) {
      if (!val) {
        this.selectValue = ''
        this.selectedName = ''
      } else {
        this.selectValue = val
      }
    }

    created() {
      this.apiKey = CommonConfigCenter.getFrontendApplication(frontendApplication.txMapKey)
    }

    /**
     * 清空选项
     */
    clearSelected() {
      this.selectValue = ''
      this.selectedName = ''
      this.$emit('input', '')
    }

    /**
     * 失去焦点时恢复选中值
     */
    blurChange() {
      this.selectValue = this.selectedName ? this.selectedName : this.selectValue
    }

    /**
     * 远程搜索 - 腾讯地图 API
     */
    async querySearchAsync(queryString: string, callback: any) {
      if (!queryString.length) {
        callback([])
        return
      }
      try {
        const url = `/ws/place/v1/suggestion/?key=${encodeURIComponent(this.apiKey)}&keyword=${encodeURIComponent(
          queryString
        )}`
        const response: any = await this.queryRegionInfo.queryCurrentPlaceJsonp(url)
        const suggestions = response.map((item: any) => ({
          title: item.title,
          address: item.address + item.title,
          value: item.id,
          location: {
            lat: item.location.lat,
            lng: item.location.lng
          }
        }))
        callback(suggestions)
      } catch (error) {
        console.error('腾讯地图 API 请求失败', error)
        callback([])
      }
    }

    /**
     * 选中项
     */
    handleSelect(item: any) {
      this.selectedName = item.title

      this.$emit('input', item.title)
      this.$emit('getLocation', item)
    }

    /**
     * 输入时清空选中项
     */
    handleChange(value: string) {
      if (!value) {
        this.selectedName = ''
        this.$emit('input', '')
      }
      const nowLocation = JSON.parse(localStorage.getItem('nowLocation'))
      const item = {
        address: '',
        location: {
          lat: nowLocation.lat,
          lng: nowLocation.lng
        },
        zoom: 13
      }
      this.$emit('getLocation', item)
    }
  }
</script>

<style scoped>
  /* 自定义样式 */
  .autocomplete {
    width: 100%;
  }
</style>
