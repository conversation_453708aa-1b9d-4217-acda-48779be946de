import msReturn, {
  ReturnOrderAgreeBatchApplyResponse,
  ReturnOrderConfirmBatchRefundResponse
} from '@api/ms-gateway/ms-return-order-v1'
import { Response, ResponseStatus } from '@hbfe/common'
import { CalReturnOrderUtil } from '@api/service/management/trade/single/order/utils/CalReturnOrderUtil'
import fjzjTradeGateway from '@api/diff-gateway/platform-jxjypxtypt-fjzj-trade'
import MutationReturnOrderMain from '@api/service/management/trade/single/order/mutation/MutationReturnOrder'
export default class MutationReturnOrder extends MutationReturnOrderMain {
  //订单号
  orderNo = ''
  //退货单号
  returnOrderNo = ''
  //备注
  note = ''
  /**
   * 退货单号列表
   */
  returnOrderNoList: Array<string> = []
  /**
   * 是否自动同意退款
   */
  autoAgreeReturn = false

  /*
   *  同意退货审批
   * */
  async agreeReturnApplyDiff() {
    const res = await fjzjTradeGateway.agreeOrderReturn({
      orderNo: this.orderNo,
      returnOrderNo: this.returnOrderNo,
      approveComment: this.note,
      autoAgreeReturn: false
    })
    return this.filterRes(res).status
  }

  /*
   *  取消退货审批
   * */
  async cancelReturnApplyDiff() {
    const res = await fjzjTradeGateway.cancelOrderReturn({
      orderNo: this.orderNo,
      returnOrderNo: this.returnOrderNo,
      cancelReason: this.note
    })
    return this.filterRes(res).status
  }
  /*
   *  拒绝退货
   * */
  async rejectReturnApplyDiff() {
    const res = await fjzjTradeGateway.refuseOrderReturn({
      orderNo: this.orderNo,
      returnOrderNo: this.returnOrderNo,
      approveComment: this.note
    })
    return this.filterRes(res).status
  }
  
  filterRes(res: Response<any>) {
    return CalReturnOrderUtil.filterRes(res)
  }
}
