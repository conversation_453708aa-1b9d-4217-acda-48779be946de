import { Response } from '@hbfe/common'
import MsBasicDataQueryBackstageGateway, {
  StudentIndustryResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import QueryCategoryInfo from '@api/service/common/basic-data-dictionary/query/QueryCategoryInfo'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
import QueryDictionaryAcrossType from '@api/service/common/basic-data-dictionary/query/QueryDictionaryAcrossType'
import UserDetailVo from '@api/service/diff/management/qztg/user/student/model/UserDetailVo'

/**
 * 查询用户详情
 */

class QueryStudentDetail {
  // 学员用户id
  private studentUserId = ''
  private trainingCategoryObj = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryTrainingCategory
  private trainingMajorObj = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryTrainingMajor

  constructor(id: string) {
    this.studentUserId = id
  }

  /**
   * 查询用户详情
   */
  async queryDetail(): Promise<Response<UserDetailVo>> {
    const res = await MsBasicDataQueryBackstageGateway.getStudentInfoInSubProject(this.studentUserId)

    const response = new Response<UserDetailVo>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = new UserDetailVo()
    let nameMap = new Map<string, string>()
    if (res.data?.userInfo?.userIndustryList?.length) {
      // 人设的一级专业id后端后续不再返回，故需要我们根据二级id查父亲并填充
      const jsIndustry = res.data.userInfo.userIndustryList.find((it) => it.industryId === IndustryIdEnum.RS)
      if (jsIndustry && jsIndustry.secondProfessionalCategory) {
        const findParentRes = await QueryDictionaryAcrossType.queryCategoryInfoByIds(
          jsIndustry.secondProfessionalCategory
        )
        if (findParentRes?.length) {
          jsIndustry.firstProfessionalCategory = findParentRes[0].masterId
        }
      }
      nameMap = await this.queryTrainingCategoryAndMajorNameById(res.data?.userInfo?.userIndustryList)
    }
    response.data.from(res.data, nameMap)
    response.status = res.status
    return response
  }

  /*
   根据培训类别，专业id查名称
 */
  private async queryTrainingCategoryAndMajorNameById(
    data: Array<StudentIndustryResponse>
  ): Promise<Map<string, string>> {
    const nameMap = new Map<string, string>()
    const industryPropertyIds: Array<string> = []
    data.forEach((item) => {
      Object.keys(item).forEach((key) => {
        if (!['industryId', 'userIndustryId'].includes(key) && typeof item[key] == 'string' && item[key]) {
          industryPropertyIds.push(item[key])
        }
      })
      if (item?.industryId === IndustryIdEnum.JS) {
        item.userCertificateList?.forEach((ite) => {
          ite.certificateCategory && industryPropertyIds.push(ite.certificateCategory)
          ite.registerProfessional && industryPropertyIds.push(ite.registerProfessional)
        })
      }
    })
    const industryPropertyInfos = await QueryCategoryInfo.queryCategoryInfoByIds(industryPropertyIds)
    industryPropertyInfos?.forEach((item) => {
      nameMap.set(item.id, item.name)
    })
    const lsIndustry = data.find((item) => item.industryId === IndustryIdEnum.LS)
    if (lsIndustry && lsIndustry.sectionAndSubjects?.length) {
      await Promise.all(
        lsIndustry.sectionAndSubjects.map(async (item) => {
          const sectionRes = await QueryCategoryInfo.queryCategoryInfoByCode(
            IndustryPropertyCodeEnum.LEARNING_PHASE,
            item.section
          )
          const subjectsRes = await QueryCategoryInfo.queryCategoryInfoByCode(
            IndustryPropertyCodeEnum.DISCIPLINE,
            item.subjects
          )
          nameMap.set('section' + item.section, sectionRes?.name)
          nameMap.set('subject' + item.subjects, subjectsRes?.name)
        })
      )
    }
    return nameMap
  }
}
export default QueryStudentDetail
