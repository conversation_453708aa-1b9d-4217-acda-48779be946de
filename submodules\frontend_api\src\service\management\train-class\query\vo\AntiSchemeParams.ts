import { SchemeConfigRequest } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

class AntiSchemeParams {
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 方案id集合
   */
  schemeIds?: Array<string>
  /**
   * 是否包含失效大纲id   用于班级修改大纲时  显示失效的大纲信息
   */
  isIncludeUnableOutlineId?: boolean
  /**
   * 是否包含已配置监管方案
   */
  isIncludeHasAntiScheme?: boolean
  /**
   * 培训形式
   */
  trainType?: string
  /**
   * 方案名称
   */
  schemeName?: string

  static to(vo: AntiSchemeParams) {
    const dto = new SchemeConfigRequest()
    dto.schemeId = vo?.schemeId
    dto.schemeIds = vo?.schemeIds
    dto.isIncludeUnableOutlineId = vo?.isIncludeUnableOutlineId
    dto.isIncludeHasAntiScheme = vo?.isIncludeHasAntiScheme
    dto.trainType = vo?.trainType
    dto.schemeName = vo?.schemeName
    return dto
  }
}

export default AntiSchemeParams
