<template>
  <div>
    <TrainingRequire v-bind="$attrs" v-on="$listeners" ref="trainingRequireDiffRef">
      <template #hasLearningResult="{ routerMode, recalculating, isIntelligenceLearning, commodityInfo }">
        <span v-show="false">{{ defaultSchemeTypeDiff !== 'train_cooperate' }}</span>
        <el-form-item
          label="培训成果："
          v-if="
            !(routerMode === 3 && (recalculating || isIntelligenceLearning)) &&
            defaultSchemeTypeDiff !== 'train_cooperate'
          "
        >
          <el-radio-group
            v-model="commodityInfo.trainClassBaseInfo.hasLearningResult"
            @change="handleHasLearningResult"
          >
            <el-radio :label="true">提供培训证明，达到培训要求后可打印</el-radio>
            <el-radio :label="false">不提供培训证明</el-radio>
          </el-radio-group>
        </el-form-item>
      </template>
      <template
        #certificateTemplate="{ routerMode, recalculating, isIntelligenceLearning, commodityInfo, learningResultName }"
      >
        <span v-show="false">{{ defaultSchemeTypeDiff !== 'train_cooperate' }}</span>
        <el-form-item label="培训证明模板：" v-if="defaultSchemeTypeDiff !== 'train_cooperate'">
          <!--选择后 隐藏-->
          <template v-if="!(routerMode === 3 && (recalculating || isIntelligenceLearning))">
            <el-button
              size="small"
              type="primary"
              class="mr-10"
              plain
              v-show="!commodityInfo.trainClassBaseInfo.learningResultId"
              :disabled="!commodityInfo.trainClassBaseInfo.hasLearningResult"
              @click="chooseCertificateTemplate"
            >
              选择模板
            </el-button>
            <!--选择后 出现-->
            <p v-show="commodityInfo.trainClassBaseInfo.learningResultId">
              {{ learningResultName }}
              <el-button type="text" class="f-ml20" @click="chooseCertificateTemplate">替换模板</el-button>
            </p>
          </template>
          <el-checkbox
            class="f-mt5 f-show"
            v-model="closePrintTemplate"
            @change="handleClosePrintTemplate"
            :disabled="!commodityInfo.trainClassBaseInfo.hasLearningResult"
          >
            不开放学员和集体报名人员打印，仅管理员可打印
          </el-checkbox>
        </el-form-item>
      </template>
    </TrainingRequire>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import TrainingRequire from '@hbfe/jxjy-admin-scheme/src/components/training-require.vue'
  import { ProxyRef } from '@api/service/common/utils/ProxyRefMethods'

  @Component({
    components: { TrainingRequire }
  })
  @ProxyRef('trainingRequireDiffRef', true)
  export default class extends Vue {
    /**
     * 培训要求
     */
    @Ref('trainingRequireDiffRef') trainingRequireDiffRef: TrainingRequire
    /**
     * 培训班类型默认选中“培训班”
     */
    @Prop({
      type: String,
      default: 'train_class'
    })
    defaultSchemeTypeDiff!: 'train_class' | 'train_cooperate'

    // 不开放证明打印
    closePrintTemplate = false

    /**
     * 通过选中的值，设置是否提供培训证明
     */
    @Watch('defaultSchemeTypeDiff')
    defaultSchemeTypeDiffChange() {
      if (this.trainingRequireDiffRef.routerMode === 1 || this.trainingRequireDiffRef.routerMode === 2) {
        this.trainingRequireDiffRef.commodityInfo.trainClassBaseInfo.hasLearningResult =
          this.defaultSchemeTypeDiff !== 'train_cooperate'
      }
    }

    mounted() {
      this.trainingRequireDiffRef.closePrintTemplate = this.closePrintTemplate
    }

    /**
     * 选择培训证明模板
     */
    chooseCertificateTemplate() {
      this.trainingRequireDiffRef.chooseCertificateTemplate()
    }

    /**
     * 响应UI设置是否提供证明
     */
    handleHasLearningResult(val: boolean) {
      this.trainingRequireDiffRef.handleHasLearningResult(val)
    }

    /**
     * 响应UI设置是否关闭打印
     */
    handleClosePrintTemplate(val: boolean) {
      this.trainingRequireDiffRef.handleClosePrintTemplate(val)
    }
  }
</script>

<style scoped>
  .mr-10 {
    margin-right: 10px;
  }

  /*去除input为number类型时的上下加减箭头 */
  /* google、safari */
  ::v-deep input::-webkit-outer-spin-button,
  ::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0;
  }

  /* 火狐 */
  ::v-deep input[type='number'] {
    -moz-appearance: textfield;
  }
</style>
