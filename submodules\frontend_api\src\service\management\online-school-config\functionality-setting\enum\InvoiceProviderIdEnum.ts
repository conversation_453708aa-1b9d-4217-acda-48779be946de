import AbstractEnum from '@api/service/common/enums/AbstractEnum'

enum InvoiceProviderIdEnum {
  V1 = 5,
  V2 = 7
}

class InvoiceProviderIdEnumClass extends AbstractEnum<InvoiceProviderIdEnum> {
  enum = InvoiceProviderIdEnum

  constructor(status?: InvoiceProviderIdEnum) {
    super()
    this.current = status
    this.map.set(InvoiceProviderIdEnum.V1, '授权码')
    this.map.set(InvoiceProviderIdEnum.V1, 'App')
  }
}

export default new InvoiceProviderIdEnumClass()
