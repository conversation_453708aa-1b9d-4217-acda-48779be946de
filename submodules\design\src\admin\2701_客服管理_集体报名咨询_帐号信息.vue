<template>
  <el-main>
    <el-card shadow="never" class="m-card is-bg is-overflow-hidden">
      <div class="f-plr20 f-pt20">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="姓名">
                <el-input v-model="input" clearable placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="管理员帐号">
                <el-input v-model="input" clearable placeholder="请输入管理员帐号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="4" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--人员信息-->
        <el-collapse v-model="activeNames5" @change="handleChange" class="m-collapse no-border">
          <el-collapse-item name="1">
            <div slot="title" class="m-tit">
              <span class="tit-txt">人员信息</span>
            </div>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="240" highlight-current-row class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="姓名" min-width="160" fixed="left">
                <template>张依依</template>
              </el-table-column>
              <el-table-column label="帐号" min-width="200">
                <template>354875965412365896</template>
              </el-table-column>
              <el-table-column label="注册时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-border-top is-sticky">
      <el-tab-pane label="帐号信息" name="first">
        <div class="f-p15">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">基础信息</span>
            </div>
            <div class="f-plr45 f-pt20 f-pb10">
              <el-row :gutter="16">
                <el-form :inline="true" label-width="auto" class="m-text-form is-edit">
                  <el-col :sm="12" :md="8">
                    <el-form-item label="管理员姓名：">
                      林依依
                      <el-tooltip class="item" effect="dark" placement="top">
                        <span class="el-icon-edit-outline f-c9 edit-icon"></span>
                        <div slot="content">编辑</div>
                      </el-tooltip>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <!--点击编辑时 添加 is-editing-->
                    <el-form-item label="登录帐号：" class="is-editing">
                      356247854125896547
                      <el-tooltip class="item" effect="dark" placement="top">
                        <span class="el-icon-edit-outline f-c9 edit-icon"></span>
                        <div slot="content">编辑</div>
                      </el-tooltip>
                      <div class="edit-box">
                        <el-input v-model="input" clearable placeholder="请输入登录帐号" class="f-flex-sub" />
                        <div class="op">
                          <el-tooltip class="item" effect="dark" placement="top">
                            <span class="el-icon-circle-check f-cb edit-icon"></span>
                            <div slot="content">保存</div>
                          </el-tooltip>
                          <el-tooltip class="item" effect="dark" placement="top">
                            <span class="el-icon-circle-close f-c9 edit-icon"></span>
                            <div slot="content">取消</div>
                          </el-tooltip>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="注册时间：">2012-11-05 14:23:12</el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8">
                    <el-form-item label="注册来源：">主网站</el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </el-card>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">密码相关</span>
            </div>
            <div class="f-plr30 f-pt15 f-pb20">
              <el-button type="primary" size="small" class="f-ml10">重置管理员密码</el-button>
              <span class="f-ml10 f-co">注：密码默认重置gl000000</span>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="批次信息" name="second">
        <div class="f-p15">详见 2702_客服管理_集体报名咨询_批次信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="发票信息" name="third">
        <div class="f-p15">详见 2703_客服管理_集体报名咨询_发票信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="退款信息" name="fourth">
        <div class="f-p15">详见 2704_客服管理_集体报名咨询_退款信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="学员名单" name="five">
        <div class="f-p15">详见 2705_客服管理_集体报名咨询_学员名单.vue</div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames5: ['1'],
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
