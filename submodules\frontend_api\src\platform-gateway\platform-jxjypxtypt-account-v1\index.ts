import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjypxtypt-account-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-account-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * @author: xucenhao
@time: 2024-09-14
@description:
 */
export class ChangeUnitAuthorizeRequest {
  /**
   * 网校id
   */
  onlineSchoolId?: string
}

/**
 * 切换企业管理员信息响应信息
<AUTHOR>
 */
export class ChangeUnitAuthorizeResponse {
  changeTokenInfos: Array<ChangeEnterpriseAdminUnitInfo>
  token: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

export class ChangeEnterpriseAdminUnitInfo {
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 统一社会信用代码
   */
  unitCode: string
  /**
   * 单位状态 1正常,2冻结
@see com.fjhb.domain.basicdata.api.unit.consts.UnitStatus
   */
  unitStatus: number
  /**
   * 管理员与单位关系状态 1为正常可用  0为冻结不可用
@see com.fjhb.domain.basicdata.api.unit.consts.PersonUnitRelationshipStatus
   */
  personUnitRelationStatus: number
  /**
   * 账户状态
   */
  accountStatus: number
  /**
   * 账户类型
   */
  accountType: number
  /**
   * 应用方类型
   */
  applicationMemberType: number
  /**
   * 应用方id
   */
  applicationMemberId: string
  /**
   * 创建时间
   */
  createTime: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询切换管理员关联单位信息列表
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changeAuthorizationUnitInfoList(
    request: ChangeUnitAuthorizeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.changeAuthorizationUnitInfoList,
    operation?: string
  ): Promise<Response<ChangeUnitAuthorizeResponse>> {
    return commonRequestApi<ChangeUnitAuthorizeResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
