<template>
  <el-card shadow="never" class="m-card f-mb15 is-bg">
    <div class="f-p20">
      <!--精品课程包展示方式选择-->
      <span class="f-mr10">请选择精品课程包展示方式：</span>
      <el-radio-group v-model="hasClassify">
        <el-radio :label="false">
          无分类
          <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
            <i class="el-icon-info m-tooltip-icon f-c9"></i>
            <div slot="content">
              支持不按照分类添加课程，若无精品课程配置，专题门户将不展示精品课程模块。
            </div>
          </el-tooltip>
        </el-radio>
        <!-- <el-radio :label="true">
          有分类
          <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
            <i class="el-icon-info m-tooltip-icon f-c9"></i>
            <div slot="content">
              支持按不同的课程分类添加课程，每一个分类最多添加7门。若无精品课程配置，门户将默认展示最新发布的7门课程。
            </div>
          </el-tooltip>
        </el-radio> -->
      </el-radio-group>
      <el-divider class="m-divider"></el-divider>
      <!-- 有分类 -->
      <template v-if="$hasPermission('hasClassify')" desc="精品课程无分类" actions="@NoClassified">
        <classified v-if="hasClassify"></classified>
      </template>
      <!--无分类-->
      <template v-if="$hasPermission('noClassified')" desc="精品课程无分类" actions="@NoClassified">
        <no-classified v-if="!hasClassify"></no-classified>
      </template>
    </div>
  </el-card>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import Classified from '@hbfe/jxjy-admin-specialTopics/src/manage/components/components/premium-course-components/classified.vue'
  import NoClassified from '@hbfe/jxjy-admin-specialTopics/src/manage/components/components/premium-course-components/no-classified.vue'
  @Component({
    components: { Classified, NoClassified }
  })
  export default class extends Vue {
    // ========================= 变量 =========================

    /**
     * @description 展示方式 true 有分类 false 无分类
     * @mark 只有无分类,有分类为兼容处理
     * */
    hasClassify = false
  }
</script>
