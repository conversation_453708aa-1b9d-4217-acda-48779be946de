<template>
  <study-content ref="studyContent">
    <template #one-click-qualified>
      <span></span>
    </template>
  </study-content>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'

  import StudyContent from '@hbfe/jxjy-admin-customerService/src/personal/components/study-content.vue'

  @Component({ components: { StudyContent } })
  export default class extends Vue {
    @Ref('studyContent') studyContent: StudyContent
    async selectIdChange(val: string) {
      return await this.studyContent.selectIdChange(val)
    }
  }
</script>
<style scoped lang="scss">
  .myList {
    cursor: not-allowed !important;
    pointer-events: none !important;
  }

  ::v-deep .el-table:before {
    content: none;
  }

  .experience-record-list {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  ::v-deep .el-pagination {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  ::v-deep .el-pagination__jump {
    margin-left: auto;
  }
</style>
