<template>
  <el-drawer title="期别报名信息" :visible.sync="showDrawer" size="900px" custom-class="m-drawer" id="topTitle">
    <div class="drawer-bd" v-loading="termLoading">
      <div class="m-side-positioner">
        <div
          class="item"
          :class="{ 'z-cur': activeAnchor == index }"
          v-for="(item, index) in issueList"
          :key="item.id"
          @click="scrollToSection(index)"
        >
          <div class="dot"></div>
          <div class="tit">期别{{ item.issueNo }}</div>
        </div>
      </div>
      <div style="padding-right: 160px;" class="m-add-period">
        <template v-for="(item, index) in issueList">
          <div :id="index.toString()" class="m-tit is-border-bottom bg-gray" :key="item.id">
            <span class="tit-txt">{{ item.issueName }}（{{ item.issueNo }}）</span>
          </div>
          <el-form :key="`${index}-form`" ref="form" label-width="180px" class="m-form f-mt10">
            <el-form-item label="课程学时："> {{ item.periods || item.issueCourserPeriodTotal }}学时 </el-form-item>
            <el-form-item label="展示在门户：">
              {{ item.isShowInPortal ? '展示门户' : '不展示门户' }}
            </el-form-item>
            <el-form-item label="开放学员报名时间：">
              <span v-if="!item.isEnableStudentEnroll">不开放学员报名</span>
              <span v-else>{{ item.registerBeginTime || '-' }} 至 {{ item.registerEndTime || '无关闭时间' }}</span>
            </el-form-item>
            <el-form-item label="培训时段：">
              {{ item.trainingDateRange.startDate.split(' ')[0] }} 至 {{ item.trainingDateRange.endDate.split(' ')[0] }}
            </el-form-item>
          </el-form>
        </template>
      </div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="showDrawer = false">取消</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    @Ref('scrollableDiv') scrollableDiv: HTMLElement
    @PropSync('signUpIssueDrawer', { type: Boolean, default: false }) showDrawer: boolean
    // 期别数组
    @Prop({ type: Array, default: () => new Array<IssueConfigDetail>() }) issueList: Array<IssueConfigDetail>
    // 弹窗loading
    @Prop({ type: Boolean, default: false }) termLoading: boolean
    /**
     * 当前激活锚点
     */
    activeAnchor = 0
    // 绑定计时器
    timeout: any

    /**
     * 滑动开关
     */
    isScrollFlag = false

    scrollToSection(index: number): void {
      clearTimeout(this.timeout)
      // 开启滚动拦截开关
      this.isScrollFlag = true
      this.activeAnchor = index
      const element = document.getElementById(String(index))
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
      // 关闭滚动拦截开关
      this.timeout = setTimeout(() => {
        this.isScrollFlag = false
      }, 1500)
    }

    activated() {
      const drawer = document.getElementById('topTitle')
      if (drawer) {
        // 滚动条监听事件
        drawer.addEventListener('scroll', this.onScroll, true)
        // 鼠标滚动监听事件
        drawer.addEventListener('mousewheel', this.changeIsScrollFlagAsFalse, true)
      }
    }
    destroyed() {
      const drawer = document.getElementById('topTitle')
      if (drawer) {
        drawer.removeEventListener('scroll', this.onScroll, true)
        drawer.removeEventListener('scroll', this.changeIsScrollFlagAsFalse, true)
      }
    }
    changeIsScrollFlagAsFalse() {
      this.isScrollFlag = false
    }
    // 滚动监听器
    onScroll() {
      if (this.isScrollFlag) return
      // 获取所有锚点元素
      const navContents = document.querySelectorAll('.m-add-period .m-tit')
      // 所有锚点元素的 offsetTop
      const offsetTopArr: any = []
      navContents.forEach((item: any) => {
        offsetTopArr.push(item.offsetTop)
      })
      // 获取当前文档流的 scrollTop
      const scrollTop = document.querySelectorAll('.el-drawer__body')[0].scrollTop + 80
      console.log(scrollTop, 'scrollTop')

      // 定义当前点亮的导航下标
      let navIndex = 0
      for (let n = 0; n < offsetTopArr.length; n++) {
        // 如果 scrollTop 大于等于第 n 个元素的 offsetTop 则说明 n-1 的内容已经完全不可见
        // 那么此时导航索引就应该是 n 了
        if (scrollTop >= offsetTopArr[n]) {
          navIndex = n
        }
      }
      // 把下标赋值给 vue 的 data
      this.activeAnchor = Number(navContents[navIndex].id)
    }
  }
</script>
