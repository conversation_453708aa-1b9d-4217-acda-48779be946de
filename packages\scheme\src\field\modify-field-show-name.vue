<template>
  <!--修改属性值-->
  <el-drawer
    title="修改属性值"
    :visible.sync="show"
    size="600px"
    custom-class="m-drawer"
    :wrapper-closable="false"
    :close-on-press-escape="false"
  >
    <div class="drawer-bd">
      <el-form ref="form" :model="editInfo" label-width="auto" class="m-form f-mt20">
        <el-form-item label="默认名字：">{{ editInfo.name }}</el-form-item>
        <el-form-item label="对外展示名称：">
          <el-input v-model="editInfo.showName" clearable placeholder="请输入对外展示名称" />
        </el-form-item>
        <el-form-item class="m-btn-bar">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="confirmEdit">确认</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, PropSync, Prop } from 'vue-property-decorator'
  import { bind, debounce } from 'lodash-decorators'
  import FieldEditModel from '@hbfe/jxjy-admin-scheme/src/field/models/FieldEditModel'
  import UpdateTrainingPropertyVo from '@api/service/common/basic-data-dictionary/mutation/vo/UpdateTrainingPropertyVo'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  @Component
  export default class extends Vue {
    // 是否展示抽屉
    @PropSync('visible', { type: Boolean }) show!: boolean

    /**
     * 编辑属性信息
     */
    @PropSync('modifyInfo', { type: FieldEditModel }) editInfo!: FieldEditModel

    /**
     * 取消编辑
     */
    cancelEdit() {
      this.show = false
    }

    /**
     * 确认编辑
     */
    @bind
    @debounce(200)
    async confirmEdit() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        const params = new UpdateTrainingPropertyVo()
        params.industryId = this.editInfo.industryId
        params.showName = this.editInfo.showName
        params.trainingPropertyId = this.editInfo.trainingPropertyId
        console.log(params, this.editInfo)
        const response =
          await BasicDataDictionaryModule.mutationBasicdataDictionaryFactory.mutationTrainingProperty.doUpdateShowName(
            params
          )
        if (response.isSuccess()) {
          this.$message.success('操作成功')
          this.$emit('reloadData')
          this.show = false
        } else {
          this.$message.error('操作失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }
  }
</script>
