import CourseTeacher from '@api/service/common/models/course/CourseTeacher'
import CourseWareUpdate from '@api/service/common/models/course/course-ware/CourseWareUpdate'
import CourseChapterUpdate from '@api/service/common/models/course/update/CourseChapterUpdate'
import { TagDTO } from '@api/gateway/btpx@Course-default'

/**
 * 修改课程
 * <AUTHOR> update 2021/7/15  TODO
 */
class CourseUpdate {
  /**
   * 课程id
   */
  id = ''
  /**
   * 课程名称
   */
  name = ''
  /**
   * 封面图片路径
   */
  iconPath = ''
  /**
   * 课程教师信息
   */
  teacherList: Array<CourseTeacher> = new Array<CourseTeacher>()
  /**
   * 课程总章节数
   */
  totalLecture = 0
  /**
   * 已更新章节数
   */
  alreadyUpdateLecture = 0
  /**
   * 总时长
   */
  timeLength = 0
  /**
   * 课程简介
   */
  abouts = ''

  /**
   * 是否启用
   */
  enabled: boolean

  /**
   * 分类id
   */
  categoryIds?: Array<string>
  // /**
  //  * 课程分类
  //  */
  // category: CourseCategory = new CourseCategory()
  /**
   * 课程章节
   */
  courseOutline: Array<CourseChapterUpdate> = new Array<CourseChapterUpdate>()

  /**
   * 课件
   */
  courseWares: Array<CourseWareUpdate>

  /**
   * 教师id集合
   */
  teacherIds: Array<string>
  /**
   * 工种
   */
  workTypes?: Array<TagDTO>
  /**
   * 开放使用的平台
   */
  openUsedPlatforms?: Array<string>
  /**
   * 课件供应商id
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称
   */
  coursewareSupplierName: string
}

export default CourseUpdate
