import LearningRuleItem from './LearningRuleItem'
import { Page } from '@hbfe/common'
import QueryManager from '@api/service/management/user/query/manager/QueryManager'
import CourseLearningBackstage, {
  StudentLearningRuleResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import QueryPropertyDetail, {
  BatchQueryPropertyRequest
} from '@api/service/common/basic-data-dictionary/query/QueryPropertyDetail'
import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import QueryYear from '@api/service/common/basic-data-dictionary/query/QueryYear'

export default class LearningRuleList {
  /**
   * 学习规则列表
   */
  learningRuleList: Array<LearningRuleItem> = []
  /**
   * 查询列表
   */
  async queryLearningRuleList(page: Page) {
    this.learningRuleList = []
    const res = await CourseLearningBackstage.pageStudentLearningRule(page)
    this.learningRuleList = res.data?.currentPageData?.map((item: any) => LearningRuleItem.from(item)) || []
    console.log(this.learningRuleList, 'this.learningRuleList')

    page.totalSize = res.data?.totalSize || 0
    page.totalPageSize = res.data?.totalPageSize || 0
    return res.status
  }
  /**
   * 查询操作人
   */
  async queryOperationUser() {
    const createUserIds: string[] = this.learningRuleList.map((item) => item.createUserId)
    if (createUserIds.length) {
      const queryManager = new QueryManager()
      const res = await queryManager.batchQueryUserInfo([...new Set(createUserIds)])
      this.learningRuleList.forEach((item) => {
        item.operator = res.get(item.createUserId)?.userName || ''
      })
    }
  }
}
