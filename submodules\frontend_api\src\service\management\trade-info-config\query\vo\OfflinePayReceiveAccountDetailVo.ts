import {
  OfflineEncryptionKeyDataResponse,
  ReceiveAccountConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import ReceiveAccountDetailVo from './ReceiveAccountDetailVo'

class OfflinePayReceiveAccountDetailVo extends ReceiveAccountDetailVo {
  /**
   * 开户银行
   */
  depositBank = ''
  /**
   * 开户户名
   */
  merchantName = ''
  /**
   * 柜台号
   */
  counterNumber = ''

  from(res: ReceiveAccountConfigResponse) {
    this.accountName = res.name
    this.accountNo = res.accountNo
    this.accountType = res.accountType
    this.refundWay = res.returnType
    this.taxPayerId = res.taxPayerId
    if (res.encryptionKeyData.encryptionKeyType === 'offlinePay') {
      const temp = res.encryptionKeyData as OfflineEncryptionKeyDataResponse
      this.depositBank = temp.depositBank
      this.counterNumber = temp.counterNumber
      this.merchantName = temp.merchantName
    }
  }
}
export default OfflinePayReceiveAccountDetailVo
