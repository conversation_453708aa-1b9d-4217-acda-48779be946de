import MsServicerSeriesV1, { ExcellentCoursesSaveRequest } from '@api/ms-gateway/ms-servicer-series-v1'
import ExcellentCourseCategoryConfig from '@api/service/management/online-school-config/excellent-course/query/vo/ExcellentCourseCategoryConfig'
import ExcellentCourseConfigDetail from '@api/service/management/online-school-config/excellent-course/query/vo/ExcellentCourseConfigDetail'
import QueryExcellentCourse from '@api/service/common/online-school-config/excellent-course/QueryExcellentCourse'
import { bind, debounce } from 'lodash-decorators'
import CourseCategoryListDetail from '@api/service/management/resource/course-category/query/vo/CourseCategoryListDetail'

class ExcellentCourseConfig extends QueryExcellentCourse {
  onLoading = false

  async doSaveConfig() {
    this.onLoading = true
    const { status } = await MsServicerSeriesV1.saveExcellentCoursesConfig(this.toJSON())
    if (status.isSuccess()) {
      this.onLoading = false
    }
    this.onLoading = false
    return status
  }

  async doRemove(category: ExcellentCourseCategoryConfig) {
    const index = this.categories.findIndex((preCategory: ExcellentCourseCategoryConfig) => {
      return preCategory.id === category.id
    })
    this.categories.splice(index, 1)
    await this.doSaveConfig()
  }

  addCourse(courseList: Array<ExcellentCourseConfigDetail>) {
    this.courses.push(...courseList)
  }

  async addCategory(courseCategoryListDetail: CourseCategoryListDetail) {
    const category = new ExcellentCourseCategoryConfig()
    category.name = courseCategoryListDetail.name
    category.id = courseCategoryListDetail.id
    this.categories.push(category)
    await this.doSaveConfig()
  }

  includeCategory(categoryId: string) {
    return this.categories.find(category => category.id === categoryId)
  }

  async removeCourse(category: ExcellentCourseCategoryConfig, course: ExcellentCourseConfigDetail) {
    if (category) {
      const findOutCategory = this.categories.find(cate => category.id === cate.id)
      const index = findOutCategory.courses.findIndex((cour: ExcellentCourseConfigDetail) => {
        return cour.id === course.id
      })
      findOutCategory.courses.splice(index, 1)
    } else {
      const index = this.courses.findIndex((cour: ExcellentCourseConfigDetail) => {
        return cour.id === course.id
      })
      this.courses.splice(index, 1)
    }
    await this.doSaveConfig()
  }

  /**
   * 变更分类的位置
   * @param oldIndex
   * @param newIndex
   */
  changeCategoryPosition(oldIndex: number, newIndex: number) {
    if (oldIndex === newIndex) return
    const oldIndexItem = this.categories[oldIndex]
    this.categories.splice(oldIndex, 1)
    this.categories.splice(newIndex, 0, oldIndexItem)
    this.sortCategories()
  }

  /**
   * 变更课程的位置
   * @param oldIndex
   * @param newIndex
   */
  changeCoursePosition(oldIndex: number, newIndex: number) {
    if (oldIndex === newIndex) return
    const oldIndexItem = this.courses[oldIndex]
    this.courses.splice(oldIndex, 1)
    this.courses.splice(newIndex, 0, oldIndexItem)
    this.sortCourses()
  }

  /**
   * 重新排序课程列表，设置 sort 的值
   */
  sortCourses() {
    this.courses.forEach((course: ExcellentCourseConfigDetail, index: number) => {
      course.sort = index
    })
  }

  /**
   * 重新排序课程列表，设置 sort 的值
   */
  sortCategories() {
    this.categories.forEach((category: ExcellentCourseCategoryConfig, index: number) => {
      category.sort = index
    })
  }
  toJSON(): ExcellentCoursesSaveRequest {
    return {
      usedCategory: this.hasCategory,
      categories: this.categories.filter(category => !category.isRemoved),
      courses: this.courses
    }
  }

  get list() {
    return this.hasCategory
  }
}

export default ExcellentCourseConfig
