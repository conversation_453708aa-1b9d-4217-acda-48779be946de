/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-08-23 15:59:13
 * @LastEditors: chenweinian chenweinian
 * @LastEditTime: 2024-12-04 16:31:45
 * @Description:
 */
import { domTo<PERSON><PERSON> } from '@hbfe/jxjy-admin-common/src/DomToPic'
import PermissionMixin from '@hbfe/jxjy-admin-common/src/mixins/PermissionMixin'
import ServiceTokenMixin from '@hbfe/jxjy-admin-common/src/mixins/ServiceTokenMixin'
import '@/common/plugins/dev-keep-alive'
import util from '@/common/util/index'
import error from '@hbfe/jxjy-admin-components/src/errors/500/index.vue'
import RootModule from '@/store/RootModule'
import SecurityModule from '@/store/SecurityModule'
import Authentication from '@api/service/common/authentication/Authentication'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import QueryIndustryPers from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryIndustry'
import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
import {
  default as CommonConfigCenter,
  default as ConfigCenterModule
} from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication, ingress, frontend } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import systemContext from '@api/service/common/context/Context'
import SchoolServiceIdStrategy from '@api/service/common/diffSchool/SchoolServiceIdStrategy'
import '@design/common/theme.scss'
import '@design/common/webstyle.scss'
import BusinessClientTypeEnum from '@hbfe-biz/biz-authentication/src/enums/BusinessClientTypeEnum'
import FxConfig from '@hbfe/fx-manage/src/config'
import axios from 'axios'
import ElementUI, { Message, Notification } from 'element-ui'
import moment from 'moment'
import progress from 'nprogress'
import 'nprogress/nprogress.css'
import Vue from 'vue'
import VueClipboard2 from 'vue-clipboard2'
import { ObserveVisibility } from 'vue-observe-visibility'
import { VNode } from 'vue/types/umd'
import App from './App.vue'
import filters from '@/common/filters/index'
import './common/interceptors'
import ReportRequestTimeout from './common/util/ReportRequestTimeout'
import ComponentLoader from '@hbfe/jxjy-admin-components/src/components-loader'
import HbfeUiRegister from '@hbfe/jxjy-admin-components/src/hbfe-ui-register'
import router from './router/index'
import $store from './store'
import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
import FileModule from '@api/service/common/file/FileModule'
import CryptUtil from '@api/service/common/crypt/CryptUtil'
import { repeatConfig } from '@/common/interceptors'
import RefererUrls from '@api/service/common/authentication/modules/RefererUrls'
import WebfunnyReport from '@api/service/common/webfunny/WefunnyReport'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
import showIntelligentLearningTaskFeedback from '@hbfe/jxjy-admin-components/src/intelligent-learning-task-feedback/register'

const numeral = require('numeral')
moment.locale('zh-cn')

Vue.use(ComponentLoader)
HbfeUiRegister.registerComponents()
Vue.use(filters)
////////////////////////////////////////////////////////////////////////////////////
//////////////   原型绑定
// 时间处理工具
Vue.prototype.$moment = moment
// dom节点转换为图片下载方法
Vue.prototype.$domToPic = domToPic
// 数字处理工具
Vue.prototype.$numeral = numeral
// ajax 请求库， 使在每个vue组件中能使用 this.$http 请求
Vue.prototype.$http = axios
/* eslint-disable @typescript-eslint/no-unused-vars */
Vue.prototype.$util = util

Vue.prototype.$showIntelligentLearningTaskFeedback = showIntelligentLearningTaskFeedback

////////////////////////////////////////////////////////////////////////////////////
Vue.prototype.onCopy = function () {
  Message.success('复制成功！')
}

const prefix = 'admin'

// 使用 element-ui 作为ui组件
Vue.use(ElementUI)
/** 重写element-ui的form相关组件中方法，解决label宽度计算错误的问题 **/
// form组件
const formHandler = ElementUI.Form as any
;(ElementUI.Form as any).methods.getLabelWidthIndex = function (width: number) {
  const potentialLabelWidthArr = formHandler.data().potentialLabelWidthArr
  const index = potentialLabelWidthArr.indexOf(width)
  // // it's impossible
  // if (index === -1) {
  //   throw new Error('[ElementForm]unpected width ', width);
  // }
  return index
}
// 自定义指令 ----
Vue.directive('observe-visibility', ObserveVisibility)
Vue.directive('tableScroll', {
  bind(el, binding, vNode: VNode) {
    let oldTime = new Date().getTime()
    const selectWrap = el.querySelector('.el-table__body-wrapper')
    selectWrap.addEventListener('scroll', function () {
      const sign = 0
      const scrollDistance = this.scrollHeight - this.scrollTop - this.clientHeight - 1
      if (scrollDistance < sign) {
        const newDate = new Date().getTime()
        if (newDate - oldTime > 1000) {
          oldTime = newDate
          binding.value(vNode.key)
        }
      }
    })
  }
})
// 自定义指令 ----
Vue.use(VueClipboard2)
// 全局混入
Vue.mixin(PermissionMixin)

class System {
  async init() {
    try {
      FxConfig.vueAppKey = process.env.VUE_APP_KEY
      const { href } = window.location
      if (href.indexOf('?') !== -1) {
        const hashList = href.split('?')
        if (hashList.length > 1) {
          if (hashList[1].indexOf('envType') !== -1) {
            if (hashList[1].indexOf('&') !== -1) {
              const params = hashList[1].split('&')
              const findParam = params.find((item) => item.indexOf('envType') !== -1)
              if (findParam) {
                const paramObj = findParam.split('=')
                if (paramObj.length > 1) {
                  const ev = paramObj[1]
                  if (ev) {
                    localStorage.setItem('ENV', ev)
                  } else {
                    localStorage.removeItem('ENV')
                  }
                }
              }
            } else {
              const paramObj = hashList[1].split('=')
              if (paramObj.length > 1) {
                const ev = paramObj[1]
                if (ev) {
                  localStorage.setItem('ENV', ev)
                } else {
                  localStorage.removeItem('ENV')
                }
              }
            }
          }
        }
      }
      progress.start()
      try {
        WebfunnyReport.beforeInit()
      } catch (error) {
        console.log(error)
      }
      // 平台口还未替换 TODO:
      try {
        await CommonConfigCenter.queryApplicationConfig()
      } catch (error) {
        console.error(error)
      }
      const serviceTokenInstance = ServiceTokenMixin.shareInstance()
      serviceTokenInstance.init({
        prefix
      })
      // 加载系统的一些 host 配置。
      RefererUrls.authUrl = CommonConfigCenter.getIngress(ingress.auth)
      RefererUrls.ssoUrl = CommonConfigCenter.getFrontendApplication(frontendApplication.ssoDomain)
      // monitor.setConfig(ConfigCenterModule.getApplicationByName(Monitor.apolloKey))
      // Monitor.setConfig(CommonConfigCenter.getFrontendApplication(frontendApplication.adminMonitor))
      // Monitor.launch()
      const $authentication = new Authentication({
        authUrl: RefererUrls.authUrl,
        ssoUrl: RefererUrls.ssoUrl,
        request: axios,
        appKey: process.env.VUE_APP_KEY,
        prefix: BusinessClientTypeEnum.admin,
        thirdAuthUrl: RefererUrls.authUrl,
        service: location.hostname
      })
      $authentication.isSeparateLogin = true
      Vue.prototype.$authentication = $authentication
      RootModule.SET_BROWSER_TITLE()
      const accessToken = $authentication.getAccessToken()
      await systemContext.buildContextByAdmin()
      // 查询是否开启分销增值服务
      if (systemContext?.businessEnvironment?.serviceToken?.tokenMeta?.servicerId) {
        await CapabilityServiceConfig.checkFxCapability()
        // 分销组件传入网校服务商ID
        FxConfig.servicerId = systemContext.businessEnvironment?.serviceToken?.tokenMeta?.servicerId
        FxConfig.distributionType = CapabilityServiceConfig.distributionType
      }
      SchoolServiceIdStrategy.buildSchoolMap()
      let remoteList = []
      try {
        if (accessToken) {
          await serviceTokenInstance.firstLoad()
          // 检测是否需要调用该接口
          if (localStorage.getItem('isNeedAccessTokenValue')) {
            await $authentication.getAccessTokenValue()
          }
          // 加载用户菜单
          remoteList = await RootModule.getMenuList()
          if (CapabilityServiceConfig.distributionType || CapabilityServiceConfig.distributionType === 0) {
            await RootModule.getFxMenuList()
          }
          await RootModule.generateSecurityList()
          // 初始化获取行业信息缓存
          await QueryIndustry.queryIndustry()

          await QueryIndustryPers.queryIndustry()
          // 安全对象鉴权使用 初始化角色信息
          await SecurityModule.roleInit()
          // 用户跳转至营销中心，不存在营销中心权限时，强制跳转回首页。(切勿调整菜单名称)
          const res = remoteList.find((item) => item.code == '/marketing-center')
          if (!res && window.location.hash.includes('fx/distribution/promotion-gateway/check')) {
            location.hash = '/welcome'
            location.reload()
          }
        }
      } catch (e) {
        console.log('切换失败')
      }

      let conditionResolver: any
      let conditionPromise: any

      // 初始化控制执行的Promise
      const initConditionPromise = () => {
        conditionPromise = new Promise((resolve) => {
          conditionResolver = resolve
        })
      }

      let interval: any
      let isLocked = false

      const errorHandle = async (response: any) => {
        const authResponse = (await $authentication.ssoRefreshToken()) as any
        if (authResponse.code != 200 || !authResponse.data?.access_token) {
          serviceTokenInstance.clearService()
          $authentication.removeToken()
          localStorage.removeItem('currentUnitId')
          localStorage.removeItem('lastFeedBackTime')
          window.location.replace('#/login')
          if (QueryManagerDetail.hasCategory(CategoryEnums.ztgly)) {
            window.location.replace('#/specialSubjectLogin')
          } else {
            window.location.replace('#/login')
          }
          location.reload()
          conditionResolver(response)
          return response
        }
        isLocked = false
        conditionResolver(response)
        return response
      }
      axios.interceptors.response.use(
        async (response: any) => {
          if (response?.response?.status === 401 && !isLocked) {
            isLocked = true
            initConditionPromise()
            await errorHandle(response)
            const api = axios.create()
            repeatConfig.headers['Authorization'] = `Mship ${$authentication.getAccessToken()}`
            console.log(repeatConfig, 'repeatConfig')
            response = await api(repeatConfig)
            // await serviceTokenInstance.firstLoad()
            await conditionPromise
            return response
          } else if (isLocked) {
            // 如果已经上锁，则等待解锁
            await new Promise((resolve) => {
              console.log('检测是否锁定3')
              if (interval) {
                interval = setInterval(() => {
                  console.log('检测是否锁定1')
                  if (!isLocked) {
                    clearInterval(interval)
                    interval = null
                    return resolve(response)
                  }
                }, 500)
              }
            })
          }
          return response
        },
        async (error) => {
          let { response } = error
          // 判断请求放回的是否是401
          if (response.status === 401 && !isLocked) {
            isLocked = true
            initConditionPromise()
            await errorHandle(response)
            const api = axios.create()
            repeatConfig.headers['Authorization'] = `Mship ${$authentication.getAccessToken()}`
            console.log(repeatConfig, 'repeatConfig')
            response = await api(repeatConfig)
            // await serviceTokenInstance.firstLoad()
            await conditionPromise
            return response
          } else if (isLocked) {
            await new Promise((resolve) => {
              if (!interval) {
                interval = setInterval(() => {
                  console.log('检测是否锁定2')
                  if (!isLocked) {
                    clearInterval(interval)
                    return resolve(response)
                  }
                }, 500)
              }
            })
          } else if (response.status === 403) {
            Notification.error({
              title: '请求没权限',
              message: `${response.config.url}`,
              type: 'error'
            })
          }
          return response
        }
      )

      // 服务商id
      const serviceId = CommonConfigCenter.getFrontendApplication(frontendApplication.trainingInstitutionServicerId)
      const trainingInstitutionDomain = CommonConfigCenter.getFrontendApplication(
        frontendApplication.trainingInstitutionDomain
      )

      // 传参
      FxConfig.ingress = ingress
      FxConfig.frontendApplication = frontendApplication
      FxConfig.configCenterModule = ConfigCenterModule
      FxConfig.moment = moment
      FxConfig.appKey = process.env.VUE_APP_KEY
      FxConfig.ingressAddress = ConfigCenterModule.getIngress(frontendApplication.mfsSubprojectAddress)
      FxConfig.ingressResource = ConfigCenterModule.getIngress(ingress.resource)
      FxConfig.ingressApiendpoint = ConfigCenterModule.getIngress(ingress.apiendpoint)
      FxConfig.region = await QueryBusinessRegion.getCountrywideRegion()
      FxConfig.imgUrlWithToken = util.imgUrlWithToken
      FxConfig.pageLeaveTips = RootModule.SET_CLOSE_PAGE_TIP
      FxConfig.removePageLeaveTips = RootModule.REMOVE_CLOSE_PAGE_TIP
      FxConfig.closePageTip = RootModule.closePageTip
      FxConfig.resourceAccessToken = FileModule.applyResourceAccessToken
      FxConfig.encrypt = CryptUtil.encrypt
      FxConfig.decrypt = CryptUtil.decrypt

      //   await FileModule.applyResourceAccessToken()
      // 判断当前是否为运营域 如果是 登录页定向到运营域下
      // diff-  粗略适配差异化运营域访问，待优化
      const isOperation = systemContext.servicerInfo.id === serviceId
      if (!systemContext.servicerInfo.id) {
        if (
          window.location.hostname != trainingInstitutionDomain &&
          window.location.hostname != `diff-${trainingInstitutionDomain}`
        ) {
          if (window.location.protocol == 'https:') {
            window.location.href = window.location.origin
          }
        }
      }
      RootModule.SET_OPERATION_ENVIRONMENT(isOperation)
      if (isOperation) {
        import('@design/trainingInstitution/styles/webstyle.scss')
      } else {
        import('@design/admin/styles/webstyle.scss')
      }
      // 根据用户中
      /* eslint-disable no-new */
      new Vue({
        router: await router(remoteList),
        store: $store,
        el: '#app',
        components: { App },
        template: '<App/>'
      })
    } catch (e) {
      console.log(e)
      new Vue({
        el: '#app',
        components: { error },
        template: '<error/>'
      })
    }
  }
}
;(async () => {
  const system = new System()
  await system.init()
})()
