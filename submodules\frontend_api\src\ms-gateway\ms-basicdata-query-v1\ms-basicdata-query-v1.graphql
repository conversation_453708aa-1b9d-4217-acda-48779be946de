"""独立部署的微服务,K8S服务名:ms-basicdata-query-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取ui主题颜色列表
		@return 主题颜色列表
	"""
	findUiThemeColorList:[UiThemeColorResponse]
	getRegion(request:GetRegionRequest):GetRegionResponse @optionalLogin
	getRegionNew(request:GetRegionNewRequest):GetRegionNewResponse @optionalLogin
	"""当前服务商合约是否到期
		【注意】调用前需要调用applyForServiceByDomainName获取服务商上下文信息
		@return 服务商合约是否已到期
	"""
	isOnlineSchoolContractExpired:Boolean! @optionalLogin
}
"""查询服务地区/行业地区请求信息
	@author: zhengp 2022/5/31 10:23
"""
input GetRegionNewRequest @type(value:"com.fjhb.ms.basicdataquery.v1.kernel.gateway.graphql.request.GetRegionNewRequest") {
	"""0 行业地区 1服务地区"""
	queryType:Int!
}
"""查询服务地区/行业地区请求信息
	@author: zhengp 2022/5/31 10:23
"""
input GetRegionRequest @type(value:"com.fjhb.ms.basicdataquery.v1.kernel.gateway.graphql.request.GetRegionRequest") {
	"""0 行业地区 1服务地区"""
	queryType:Int!
	"""查询层级 [1,2,3]"""
	level:Int!
	"""父级id"""
	parentId:String
}
"""业务地区信息
	@author: zhengp 2022/5/31 10:29
"""
type BusinessRegionInfoDto @type(value:"com.fjhb.ms.basicdataquery.v1.kernel.gateway.graphql.dto.BusinessRegionInfoDto") {
	"""地区编号"""
	id:String
	"""上级地区编号
		为-1时，表示当前为根地区
	"""
	parentId:String
	"""地区路径"""
	regionPath:String
	"""地区名称"""
	name:String
	"""排序"""
	sort:Int!
	"""是否启用"""
	enable:Boolean!
}
"""<AUTHOR> 2022/11/1 17:34"""
type RegionDto @type(value:"com.fjhb.ms.basicdataquery.v1.kernel.gateway.graphql.dto.RegionDto") {
	"""编号"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""是否是全国 为TRUE时 region为空"""
	isAllRegion:Boolean
	"""地区 末级地区集合 字符数组"""
	region:String
	"""创建时间"""
	createTime:DateTime
}
"""查询服务地区/行业地区响应信息
	@author: zhengp 2022/5/31 10:27
"""
type GetRegionNewResponse @type(value:"com.fjhb.ms.basicdataquery.v1.kernel.gateway.graphql.response.GetRegionNewResponse") {
	regionDto:RegionDto
}
"""查询服务地区/行业地区响应信息
	@author: zhengp 2022/5/31 10:27
"""
type GetRegionResponse @type(value:"com.fjhb.ms.basicdataquery.v1.kernel.gateway.graphql.response.GetRegionResponse") {
	"""业务地区信息"""
	businessRegionInfoDtoList:[BusinessRegionInfoDto]
}
"""Ui主题颜色
	<AUTHOR>
"""
type UiThemeColorResponse @type(value:"com.fjhb.ms.basicdataquery.v1.kernel.gateway.graphql.response.UiThemeColorResponse") {
	"""颜色id"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""帐户ID"""
	servicerId:String
	"""颜色值"""
	colorRef:String
	"""创建时间"""
	createdTime:DateTime
}

scalar List
