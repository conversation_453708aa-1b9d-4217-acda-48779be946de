<template>
  <div>
    <div class="u-tab-tips-new" v-if="!(createMode === 3 && (recalculating || isIntelligenceLearning))">
      <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
        <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
        <div slot="content">请先勾选学习内容模块，再配置对应内容。</div>
      </el-tooltip>
    </div>
    <el-tabs @tab-click="changeTab()" v-model="activeName" type="card" class="m-tab-card is-sticky">
      <el-tab-pane
        name="course-learn"
        v-if="!(createMode === 3 && (recalculating || isIntelligenceLearning)) && isPureOnlineClass"
      >
        <div slot="label">
          <el-checkbox
            class="f-mr5"
            v-model="learningTypeSelect('courseLearning').value"
            @change="
              (value) => {
                return handleLearningTypeChange('courseLearning', value)
              }
            "
          ></el-checkbox>
          <i class="f-pl5">线上课程</i>
        </div>
        <course-learn
          ref="courseLearnRef"
          :trainSchemeDetail.sync="commodityBasicInfo"
          :courseLearning.sync="learningTypeModel.courseLearning"
          :isTowElectiveListShow="isTowElectiveListShow"
          :learningTypeModelCopy="learningTypeModelCopy"
          :router-mode="routerMode"
          :learningExperience="learningTypeModel.learningExperience"
        ></course-learn>
      </el-tab-pane>
      <el-tab-pane
        name="exam"
        v-if="
          !(createMode === 3 && (recalculating || isIntelligenceLearning) && !learningTypeModel.exam.isSelected) &&
          isPureOnlineClass
        "
      >
        <div slot="label">
          <el-checkbox
            class="f-mr5"
            v-model="learningTypeSelect('exam').value"
            @change="
              (value) => {
                return handleLearningTypeChange('exam', value)
              }
            "
            :disabled="createMode === 3 && (recalculating || isIntelligenceLearning)"
          ></el-checkbox>
          <i class="f-pl5">班级考试</i>
        </div>
        <exam
          ref="examRef"
          :router-mode="routerMode"
          :examLearn.sync="learningTypeModel.exam"
          :course-learning.sync="learningTypeModel.courseLearning"
          :recalculating="recalculating"
          :isIntelligenceLearning="isIntelligenceLearning"
        >
        </exam>
      </el-tab-pane>
      <el-tab-pane
        name="practice"
        v-if="!(createMode === 3 && (recalculating || isIntelligenceLearning)) && isPureOnlineClass"
      >
        <div slot="label">
          <el-checkbox
            class="f-mr5"
            v-model="learningTypeSelect('practiceLearning').value"
            @change="
              (value) => {
                return handleLearningTypeChange('practiceLearning', value)
              }
            "
          ></el-checkbox>
          <i class="f-pl5">班级练习</i>
        </div>
        <practice
          ref="practiceLearningRef"
          :router-mode="routerMode"
          :practiceLearn.sync="learningTypeModel.practiceLearning"
          :exam-learning-info="learningTypeModel.exam"
          :course-learning-info="learningTypeModel.courseLearning"
        ></practice>
      </el-tab-pane>
      <el-tab-pane
        name="interest-course"
        v-if="!(createMode === 3 && (recalculating || isIntelligenceLearning)) && isPureOnlineClass"
      >
        <div slot="label">
          <el-checkbox
            class="f-mr5"
            v-model="learningTypeSelect('interestCourse').value"
            @change="
              (value) => {
                return handleLearningTypeChange('interestCourse', value)
              }
            "
          ></el-checkbox>
          <i class="f-pl5">兴趣课程</i>
        </div>
        <el-card shadow="never" class="m-card is-header f-mb15">
          <div class="f-plr20">
            <!-- 创建/复制 -->
            <template v-if="isCreateMode">
              <edit-choose-course-outline
                ref="editInterestChooseCourseRef"
                :outlineTree.sync="learningTypeModel.interestCourse.classification"
                :init-mode="2"
                :enable-edit="learningTypeModel.interestCourse.isSelected"
              />
            </template>
            <!-- 修改 -->
            <template v-if="isModifyMode">
              <modify-choose-course-outline
                ref="modifyInterestChooseCourseRef"
                :trainSchemeDetail.sync="commodityBasicInfo"
                :outlineTree.sync="learningTypeModel.interestCourse.classification"
                :init-mode="2"
                :enable-edit="learningTypeModel.interestCourse.isSelected"
              />
            </template>
          </div>
        </el-card>
      </el-tab-pane>
      <el-tab-pane
        name="study-notes"
        v-if="!(createMode === 3 && (recalculating || isIntelligenceLearning)) && isPureOnlineClass"
      >
        <div slot="label">
          <el-checkbox
            class="f-mr5"
            v-model="learningTypeSelect('learningExperience').value"
            @change="
              (value) => {
                return handleLearningTypeChange('learningExperience', value)
              }
            "
          ></el-checkbox>
          <i class="f-pl5">学习心得</i>
        </div>
        <study-notes
          ref="studyNotesRef"
          :trainSchemeDetail.sync="commodityBasicInfo"
          :learningFeelInfo="learningTypeModel.learningExperience"
          :classification="learningTypeModel.courseLearning.classification"
          :course-learning="learningTypeModel.courseLearning"
          :router-mode="routerMode"
        ></study-notes>
      </el-tab-pane>
      <el-tab-pane
        name="training-period"
        v-if="
          [TrainingModeEnum.mixed, TrainingModeEnum.offline].includes(
            commodityBasicInfo.skuProperty.trainingMode.skuPropertyValueId
          )
        "
      >
        <div slot="label">
          <el-checkbox
            class="f-mr5"
            v-model="learningTypeSelect('issue').value"
            disabled
            @change="
              (value) => {
                return handleLearningTypeChange('issue', value)
              }
            "
          ></el-checkbox>
          <i class="f-pl5">培训期别</i>
        </div>
        <training-period
          ref="trainingPeriodRef"
          :issue="learningTypeModel.issue"
          :routerMode="routerMode"
          :trainClassBaseInfo="commodityBasicInfo"
          :learningTypeModelCopy="learningTypeModelCopy"
          :recalculating="recalculating"
          :isIntelligenceLearning="isIntelligenceLearning"
          @removeIssue="removeIssue"
        ></training-period>
      </el-tab-pane>
      <el-tab-pane name="questionnaire">
        <div slot="label">
          <el-checkbox
            class="f-mr5"
            :disabled="recalculating || isIntelligenceLearning"
            v-model="learningTypeSelect('questionnaire').value"
            @change="
              (value) => {
                return handleLearningTypeChange('questionnaire', value)
              }
            "
          ></el-checkbox>
          <i class="f-pl5">调研问卷</i>
        </div>
        <questionnaire
          ref="questionnaireRef"
          :questionnaire="learningTypeModel.questionnaire"
          :issue="learningTypeModel.issue"
          :trainClassBaseInfo="commodityBasicInfo"
          :routerMode="routerMode"
          :recalculating="recalculating"
          :isIntelligenceLearning="isIntelligenceLearning"
        ></questionnaire>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Ref, PropSync, Watch } from 'vue-property-decorator'
  import CourseLearn from '@hbfe/jxjy-admin-scheme/src/components/course-learn.vue'
  import Exam from '@hbfe/jxjy-admin-scheme/src/components/exam.vue'
  import Practice from '@hbfe/jxjy-admin-scheme/src/components/practice.vue'
  import EditChooseCourseOutline from '@hbfe/jxjy-admin-scheme/src/components/functional-components/edit-choose-course-outline.vue'
  import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import CreateSchemeUIModule from '@/store/modules-ui/scheme/CreateSchemeUIModule'
  import CourseLearningLearningType from '@api/service/management/train-class/mutation/vo/CourseLearningLearningType'
  import ExamLearningType from '@api/service/management/train-class/mutation/vo/ExamLearningType'
  import PracticeLearningType from '@api/service/management/train-class/mutation/vo/PracticeLearningType'
  import InterestCourseType from '@api/service/management/train-class/mutation/vo/InterestCourseType'
  import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  import ModifyChooseCourseOutline from '@hbfe/jxjy-admin-scheme/src/components/functional-components/modify-choose-course-outline.vue'
  import StudyNotes from '@hbfe/jxjy-admin-scheme/src/components/study-notes.vue'
  import { cloneDeep } from 'lodash'
  import { JoinTimeTypeEnum } from '@api/service/management/train-class/mutation/Enum/JoinTimeTypeEnum'
  import LearningExperience from '@api/service/management/train-class/mutation/vo/LearningExperience'
  import TrainingPeriod from '@hbfe/jxjy-admin-scheme/src/components/training-period.vue'
  import Questionnaire from '@hbfe/jxjy-admin-scheme/src/components/questionnaire.vue'
  import QuestionnaireAssess from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/questionnaire-assess.vue'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import IssueLearningType from '@api/service/common/scheme/model/IssueLearningType'
  import QuestionnaireLearningType from '@api/service/common/scheme/model/QuestionnaireLearningType'
  import { BeginTimeTypeEnum } from '@api/service/common/scheme/enum/BeginTimeType'
  import { QuestionnaireOpenDateTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireOpenDateType'
  import PlaceholderConstant from '@api/service/common/scheme/model/schemeDto/common/PlaceholderConstant'
  import MutationCreateTrainClassCommodity from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'
  import { QuestionnaireAppliedRangeTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'
  import { IssueTrainingDateTypeEnum } from '@api/service/common/scheme/enum/IssueTrainingDateType'
  import { QuestionnairePreconditionTypeEnum } from '@api/service/common/scheme/enum/QuestionnairePreconditionType'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

  class UILearningTypeSelectStatus {
    // 属性名
    key: string
    // 选中状态
    value: boolean
    // 选中提示语
    selectTip: string
    // 取消选中提示语
    cancelSelectTip: string
  }

  @Component({
    components: {
      QuestionnaireAssess,
      Questionnaire,
      TrainingPeriod,
      ModifyChooseCourseOutline,
      CourseLearn,
      Exam,
      Practice,
      EditChooseCourseOutline,
      StudyNotes
    }
  })
  export default class extends Vue {
    // 练习
    @Ref('practiceLearningRef') practiceLearningRef: Practice

    // 课程学习
    @Ref('courseLearnRef') courseLearnRef: CourseLearn

    // 考试
    @Ref('examRef') examRef: Exam

    // 兴趣课-创建
    @Ref('editInterestChooseCourseRef') editInterestChooseCourseRef: EditChooseCourseOutline

    // 兴趣课-修改
    @Ref('modifyInterestChooseCourseRef') modifyInterestChooseCourseRef: ModifyChooseCourseOutline

    //学习心得
    @Ref('studyNotesRef') studyNotesRef: StudyNotes

    /**
     * 培训期别
     */
    @Ref('trainingPeriodRef') trainingPeriodRef: TrainingPeriod

    /**
     * 调研问卷
     */
    @Ref('questionnaireRef') questionnaireRef: Questionnaire

    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number
    /**
     * 是否重算中
     */
    @Prop({ type: Boolean, default: false })
    recalculating: boolean
    /**
     * 是否智能学习中
     */
    @Prop({ type: Boolean, default: false })
    isIntelligenceLearning: boolean

    // 创建模式列表（课程包下课程读取最新的课程包）
    get isCreateMode() {
      return CreateSchemeUtils.createModeList.includes(this.routerMode)
    }

    // 修改模式列表（课程包下课程读取大纲下的课程）
    get isModifyMode() {
      return CreateSchemeUtils.modifyModeList.includes(this.routerMode)
    }

    /**
     * 学习内容 - 双向绑定
     */
    @PropSync('learningType', { type: LearningType }) learningTypeModel!: LearningType

    /**
     * 基础信息配置 - 双向绑定
     */
    @PropSync('basicInfo', { type: TrainClassBaseModel }) commodityBasicInfo!: TrainClassBaseModel
    /**
     * 培训班类型默认选中“培训班”
     */
    @PropSync('defaultSchemeType', {
      type: String,
      default: 'train_class'
    })
    classType!: 'train_class' | 'train_cooperate'
    /**
     * 商品信息 - 双向绑定
     */
    @Prop({ type: Object, default: () => new MutationCreateTrainClassCommodity() })
    commodityInfo: MutationCreateTrainClassCommodity
    // 活页
    activeName = 'course-learn'
    /**
     * 初始数据
     */
    @Prop({
      type: LearningType,
      default: () => new LearningType()
    })
    learningTypeModelCopy: LearningType

    skipTime = false

    /**
     * 【UI】学习内容选中状态
     */
    uiLearningTypeSelect: UILearningTypeSelectStatus[] = []
    TrainingModeEnum = TrainingModeEnum
    /**
     * 培训方案类型，1-选课规则，2-自主选课
     */
    get schemeType() {
      return CreateSchemeUIModule.schemeType
    }

    /**
     * 创建模式，1-创建，2-复制，3-编辑，默认：0
     */
    get createMode() {
      return CreateSchemeUIModule.createMode
    }
    /**
     * 培训方案修改选修二级分类是否展示
     */
    isTowElectiveListShow = false

    /**
     * 是否纯网授班
     */
    get isPureOnlineClass() {
      return this.commodityBasicInfo.skuProperty.trainingMode.skuPropertyValueId !== TrainingModeEnum.offline
    }
    /**
     * 查找所有叶子节点 - 课程大纲树通用
     */
    outlineTreeFindAllLeaves(tree: Array<Classification>) {
      return CreateSchemeUtils.treeFindAllLeaves<Classification>(tree, 'childOutlines')
    }
    @Watch('learningTypeModel.exam.isSelected')
    isSelectedChange(val: any) {
      if (!val) {
        this.learningTypeSelect('exam').value = val
      }
    }
    @Watch('learningTypeModel.courseLearning.chooseCourseRule.secondElectiveMaxPeriod', {
      immediate: true,
      deep: true
    })
    courseLearningChange(val: any) {
      // TODO 修改先拦截待调整
      if (
        this.createMode === 3 &&
        !this.learningTypeModelCopy.courseLearning.chooseCourseRule?.secondElectiveMaxPeriod?.length
      )
        return
      if (val && val.length) {
        this.isTowElectiveListShow = true
      }
    }

    // 当tab改变到学习心得时，刷新学习心得列表
    changeTab() {
      if (this.activeName == 'study-notes') {
        this.studyNotesRef?.experienceRef?.experiencePage?.currentChange(1)
      }
    }
    /**
     * 表单校验 - 学习内容配置
     */
    validateForm() {
      let finalValid = true
      try {
        if (
          !this.learningTypeModel.courseLearning.isSelected &&
          !this.learningTypeModel.exam.isSelected &&
          !this.learningTypeModel.practiceLearning.isSelected &&
          !this.learningTypeModel.interestCourse.isSelected &&
          !this.learningTypeModel.questionnaire.isSelected &&
          this.classType === 'train_class' &&
          this.commodityBasicInfo.skuProperty.trainingMode.skuPropertyValueId !== TrainingModeEnum.offline
        ) {
          this.$message.error(
            `当前方案为${
              this.commodityBasicInfo.skuProperty.trainingMode.skuPropertyValueId === TrainingModeEnum.mixed ? '面' : ''
            }网授班，需至少选择一项学习方式`
          )
          throw new Error('中断函数执行')
        } else if (
          this.commodityBasicInfo.skuProperty.trainingMode.skuPropertyValueId === TrainingModeEnum.mixed &&
          (!this.learningTypeModel.issue.isSelected || !this.learningTypeModel.issue.issueConfigList.length)
        ) {
          this.$message.error('请配置培训期别')
          throw new Error('中断函数执行')
        }
        if (
          this.learningTypeModel.questionnaire.isSelected &&
          !this.learningTypeModel.questionnaire.questionnaireConfigList.length
        ) {
          this.$message.error('请配置问卷信息')
          throw new Error('中断函数执行')
        } else if (
          this.learningTypeModel.questionnaire.isSelected &&
          this.learningTypeModel.questionnaire.questionnaireConfigList.length
        ) {
          const nameList = this.learningTypeModel.questionnaire.questionnaireConfigList.map(
            (item) => item.questionnaireName
          )
          const arrName = Array.from(new Set(nameList))
          if (arrName.length != this.learningTypeModel.questionnaire.questionnaireConfigList.length) {
            this.$message.error('问卷名称不能重复')
            throw new Error('中断函数执行')
          }
          const findNotCourseId = this.learningTypeModel.issue.issueConfigList
            .filter((item) => !item.issueCourseList.length)
            ?.map((ite) => ite.id)
          const findNotCourse = this.learningTypeModel.issue.issueConfigList.filter((item) => {
            return !item.issueCourseList.length
          })
          const questionPreIssue = this.learningTypeModel.questionnaire.questionnaireConfigList.filter((item) => {
            return (
              item.appliedRangeType === QuestionnaireAppliedRangeTypeEnum.per_issue &&
              item.preconditionType == QuestionnairePreconditionTypeEnum.pass_issue_assess
            )
          })
          const questionIsAssignIssue = this.learningTypeModel.questionnaire.questionnaireConfigList.filter(
            (item) =>
              item.appliedRangeType === QuestionnaireAppliedRangeTypeEnum.assign_issue &&
              item.preconditionType == QuestionnairePreconditionTypeEnum.pass_issue_assess &&
              findNotCourseId.includes(item.curIssueId)
          )
          console.log(findNotCourseId, questionIsAssignIssue, questionPreIssue, 'xxxxx')
          if (
            questionIsAssignIssue.length ||
            (findNotCourse &&
              findNotCourse.length &&
              findNotCourse.length == this.learningTypeModel.issue.issueConfigList.length &&
              questionPreIssue &&
              questionPreIssue.length)
          ) {
            // const questionnairePreName = questionPreIssue.map((item) => item.questionnaireName).join('、')
            // const questionnaireName =  questionIsAssignIssue.map((item) => item.questionnaireName).join('、')
            const questionName = [...questionPreIssue, ...questionIsAssignIssue]
              .map((item) => item.questionnaireName)
              .join('、')
            this.$message.error(
              `【${questionName}】应用于期别且配置完成课程前置条件，但期别未配置课表，请检查并调整配置。`
            )
            throw new Error('中断函数执行')
          }

          // if (
          //   findNotCourse &&
          //   findNotCourse.length &&
          //   findNotCourse.length == this.learningTypeModel.issue.issueConfigList.length &&
          //   questionPreIssue &&
          //   questionPreIssue.length
          // ) {
          //   const questionnaireName = questionPreIssue.map((item) => item.questionnaireName).join('、')
          //   this.$message.error(
          //     `【${questionnaireName}】应用于期别且配置完成课程前置条件，但期别未配置课表，请检查并调整配置。`
          //   )
          //   throw new Error('中断函数执行')
          // }
        }

        const examLearningInfo = this.learningTypeModel.exam
        // 课程学习
        const courseLearningInfo = this.learningTypeModel.courseLearning
        const practiceLearning = this.learningTypeModel.practiceLearning
        // 是否配置课程
        let hasConfigureCourse: Classification
        // 选课规则
        if (this.schemeType === 1) {
          hasConfigureCourse = CreateSchemeUtils.treeFind<Classification>(
            courseLearningInfo.classification.childOutlines,
            (node: Classification) => {
              return node.coursePackageId && node.coursePackageId !== ''
            },
            'childOutlines'
          )
        }
        // 自主选课
        if (this.schemeType === 2) {
          if (courseLearningInfo.classification?.childOutlines?.length) {
            hasConfigureCourse = CreateSchemeUtils.treeFind<Classification>(
              courseLearningInfo.classification.childOutlines,
              (node: Classification) => {
                return node.coursePackageId && node.coursePackageId !== ''
              },
              'childOutlines'
            )
          } else {
            hasConfigureCourse = courseLearningInfo.classification.coursePackageId
              ? courseLearningInfo.classification
              : undefined
          }
        }
        const hasConfigCourseLearning = !!(courseLearningInfo.isSelected && hasConfigureCourse)
        const hasConfigExam = !!(examLearningInfo.isSelected && examLearningInfo.paperPublishConfigureId)
        const interestLearningInfo = this.learningTypeModel.interestCourse
        const learningExperienceInfo = this.learningTypeModel.learningExperience
        /** 课程学习相关校验 */
        if (courseLearningInfo.isSelected && this.courseLearnRef) {
          const valid = this.courseLearnRef.validateForm()
          if (!valid) {
            throw new Error('中断函数执行')
          }
        }
        if (examLearningInfo.isSelected) {
          if (!examLearningInfo.paperPublishConfigureId) {
            this.$message.error('请配置班级考试')
            throw new Error('中断函数执行')
          }
          if (examLearningInfo.preCondition && !courseLearningInfo.isSelected) {
            this.$message.error('当前班级考试配置课程学习考核前置条件，请先配置课程信息！')
            throw new Error('中断函数执行')
          }
        }
        if (practiceLearning.isSelected) {
          if (!practiceLearning.type) {
            this.$message.error('请配置班级练习')
            throw new Error('中断函数执行')
          }
          if (practiceLearning.type === 1 && (!practiceLearning.libraryIds || !practiceLearning.libraryIds.length)) {
            this.$message.error('已勾选“班级练习”且练习来源为“题库”，请配置题库')
            throw new Error('中断函数执行')
          }
          if (practiceLearning.type === 2 && !hasConfigCourseLearning) {
            this.$message.error('已勾选“班级练习”且练习来源为“按照学员课程ID出题”，请配置课程学习')
            throw new Error('中断函数执行')
          }
          if (practiceLearning.type === 3 && !hasConfigExam) {
            this.$message.error('已勾选“班级练习”且练习来源为“同考试”，请配置班级考试')
            throw new Error('中断函数执行')
          }
        }
        if (interestLearningInfo.isSelected) {
          const interestCourseTreeWithoutCourseLeaves =
            this.outlineTreeFindAllLeaves(interestLearningInfo.classification.childOutlines).filter(
              (el: Classification) => {
                return el.coursePackageId == ''
              }
            ) || ([] as Classification[])
          if (interestCourseTreeWithoutCourseLeaves.length) {
            this.$message.error('请配置兴趣课程')
            throw new Error('中断函数执行')
          }
        }
        if (learningExperienceInfo.isSelected) {
          if (learningExperienceInfo.isExamine == null) {
            // this.$message.error('已勾选“学习心得”，请配置心得是否纳入考核')
            this.$message.error('请配置学习心得')
            throw new Error('中断函数执行')
          }
          if (learningExperienceInfo.isExamine == true && learningExperienceInfo.joinCount == 0) {
            this.$message.error('已勾选“学习心得”且心得纳入考核，参加的心得数不得为0')
            throw new Error('中断函数执行')
          }
          if (this.convertTimeFormat()) {
            this.$message.error('检测到学习心得的开始时间未选择在培训方案时间内，请调整。')
            throw new Error('中断函数执行')
          }
          if (learningExperienceInfo.experienceList.length < learningExperienceInfo.joinCount) {
            this.$message.error('检测到学习心得个数小于参加的心得数，请调整。')
            throw new Error('中断函数执行')
          }
          if (learningExperienceInfo.experienceName.length > 6) {
            this.$message.error('检测到学习心得对外展示名称超过6个字符，请调整。')
            throw new Error('中断函数执行')
          }
        }
        if (this.learningTypeModel.issue.isSelected) {
          if (
            !this.learningTypeModel.issue.issueConfigList.every((item) => {
              if (item.issueTrainingDateType === IssueTrainingDateTypeEnum.by_issue_courses) {
                return item.issueCourseList.length
              } else {
                return true
              }
            })
          ) {
            this.$message.error('检测到存在期别未配置课程')
            throw new Error('中断函数执行')
          }
          if (
            this.learningTypeModel.issue.issueConfigList
              .filter((item) => item.issueTrainingDateType == IssueTrainingDateTypeEnum.by_issue_courses)
              .map((item) => item.issueCourseList)
              .flat(1)
              .some((item) => !item.teachingDate || !item.courseBeginTime || !item.courseEndTime)
          ) {
            this.$message.error('检测到存在复制期别课程的授课时间未配置')
            throw new Error('中断函数执行')
          }
          const date = this.$moment(new Date(new Date().getTime() + 60 * 1000)).format('YYYY-MM-DD HH:mm:ss')
          this.learningTypeModel.issue.issueConfigList.forEach((item) => {
            if (item.isEnableStudentEnroll && item.registerBeginTimeType == BeginTimeTypeEnum.open_now) {
              item.registerBeginTime = date
              item.registerBeginTimeType = BeginTimeTypeEnum.assign
            }
          })
          if (
            this.commodityInfo.onShelvesPlanTime &&
            this.learningTypeModel.issue.issueConfigList
              .filter((item) => item.isEnableStudentEnroll)
              ?.some(
                (item) =>
                  !item.registerBeginTime ||
                  new Date(item.registerBeginTime) < new Date(this.commodityInfo.onShelvesPlanTime)
              )
          ) {
            this.$message.error('培训期别报名时段不在方案上下架期间，请调整!')
            throw new Error('中断函数执行')
          }
          if (
            this.commodityInfo.offShelvesPlanTime &&
            this.learningTypeModel.issue.issueConfigList
              .filter((item) => item.isEnableStudentEnroll)
              ?.some(
                (item) =>
                  !item.registerEndTime ||
                  new Date(item.registerEndTime) > new Date(this.commodityInfo.offShelvesPlanTime)
              ) &&
            !this.skipTime
          ) {
            this.$message.error('培训期别报名时段不在方案上下架期间，请调整')
            throw new Error('中断函数执行')
          }
          if (
            (this.commodityBasicInfo.trainingBeginDate !== CreateSchemeUtils.defaultBeginDate ||
              this.commodityBasicInfo.trainingEndDate !== CreateSchemeUtils.defaultEndDate) &&
            this.learningTypeModel.issue.issueConfigList?.some(
              (item) =>
                new Date(item.trainingDateRange.startDate) < new Date(this.commodityBasicInfo.trainingBeginDate) ||
                new Date(item.trainingDateRange.endDate) > new Date(this.commodityBasicInfo.trainingEndDate)
            ) &&
            !this.skipTime
          ) {
            this.$message.error('培训期别培训时段超出方案学习起止时间，请调整！')
            throw new Error('中断函数执行')
          }
          if (
            (this.commodityBasicInfo.trainingBeginDate !== CreateSchemeUtils.defaultBeginDate ||
              this.commodityBasicInfo.trainingEndDate !== CreateSchemeUtils.defaultEndDate) &&
            this.learningTypeModel.questionnaire.questionnaireConfigList?.some(
              (item) =>
                item.openDateType === QuestionnaireOpenDateTypeEnum.assign &&
                (new Date(item.openDateRange.startDate) < new Date(this.commodityBasicInfo.trainingBeginDate) ||
                  new Date(item.openDateRange.endDate) > new Date(this.commodityBasicInfo.trainingEndDate))
            )
          ) {
            this.$message.error('问卷开放时间超出方案学习起止时间，请调整！')
            throw new Error('中断函数执行')
          }
          if (!this.learningTypeModel.issue.issueConfigList.every((item) => item.issueNo)) {
            this.$message.error('请填写期别编号')
            throw new Error('中断函数执行')
          }
          if (!this.learningTypeModel.issue.issueConfigList.every((item) => item.trainingPointId)) {
            this.$message.error('请选择培训点')
            throw new Error('中断函数执行')
          }
        }
        if (this.learningTypeModel.questionnaire.isSelected) {
          const schemeTrainingBeginTime =
              this.commodityBasicInfo.trainingBeginDate || PlaceholderConstant.defaultBeginTime,
            schemeTrainingEndTime = this.commodityBasicInfo.trainingEndDate || PlaceholderConstant.defaultEndTime
          this.learningTypeModel.questionnaire.questionnaireConfigList.forEach((item) => {
            if (item.openDateType === QuestionnaireOpenDateTypeEnum.same_as_scheme_training_time) {
              item.openDateRange.dateRange = [schemeTrainingBeginTime, schemeTrainingEndTime]
            } else {
              if (
                new Date(item.openDateRange.startDate) < new Date(schemeTrainingBeginTime) ||
                new Date(item.openDateRange.endDate) > new Date(schemeTrainingEndTime)
              ) {
                this.$message.error(`【${item.questionnaireName}】开放时间需在培训方案学习时间范围内，请重新选择。`)
                throw new Error('中断函数执行')
              }
            }
          })
        }
      } catch (e) {
        console.log(e, '错误信息')
        finalValid = false
      }
      return finalValid
    }

    /**
     * 学习内容选择
     */
    get learningTypeSelect() {
      return (type: string) => {
        return this.uiLearningTypeSelect.find((item) => item.key === type)
      }
    }

    /**
     * 是否存在线上课程问卷
     */
    get hasOnlineQuestionnaire() {
      return this.learningTypeModel.questionnaire.questionnaireConfigList.some(
        (item) => item.appliedRangeType === QuestionnaireAppliedRangeTypeEnum.online_course
      )
    }

    /**
     * 校验所有学习心得开始时间是否在培训方案时间内
     */
    convertTimeFormat() {
      // 转换时间为时间戳
      const trainingBeginDate = this.convertToTimestamp(this.commodityBasicInfo.trainingBeginDate)
      const trainingEndDate = this.convertToTimestamp(this.commodityBasicInfo.trainingEndDate)
      return this.learningTypeModel.learningExperience.experienceList
        .map((item) => {
          const experienceBeginDate = this.convertToTimestamp(item.joinTime[0])
          const experienceEndDate = this.convertToTimestamp(item.joinTime[1])
          // 培训方案开始时间 < 心得开始时间 < 心得结束时间 < 培训方案结束时间
          if (trainingBeginDate && trainingEndDate && item.joinTimeType.equal(JoinTimeTypeEnum.designate_time)) {
            if (
              experienceBeginDate > trainingBeginDate &&
              experienceBeginDate < trainingEndDate &&
              experienceEndDate < trainingEndDate &&
              experienceEndDate > trainingBeginDate
            ) {
              return true
            } else {
              return false
            }
          } else {
            return true
          }
        })
        .some((item) => item === false)
    }

    /**
     * 转换时间格式
     */
    convertToTimestamp(timeString: string) {
      const date = new Date(timeString)
      const timestamp = date.getTime()
      return timestamp
    }

    /**
     * 页面初始化
     */
    async created() {
      this.skipTime = ConfigCenterModule.getFrontendApplication(frontendApplication.skipTimeVerification) == 'true'

      this.uiLearningTypeSelect = [] as UILearningTypeSelectStatus[]
      // 获取培训类型
      for (const argument of Object.keys(this.learningTypeModel)) {
        const option = new UILearningTypeSelectStatus()
        option.key = argument
        option.value = this.learningTypeModel[argument]?.isSelected
        // 课程学习
        if (argument === 'courseLearning') {
          option.selectTip = '勾选课程学习，将同步影响方案未合格的学员，确认要勾选课程学习？'
          option.cancelSelectTip =
            '取消勾选课程学习，已经合格的学员不影响，未合格的学员的课程列表将移除所有课程。确认要取消勾选？'
        }
        // 班级考试
        if (argument === 'exam') {
          option.selectTip = '勾选班级考试，将同步影响方案未合格的学员，确认要勾选班级考试？'
          option.cancelSelectTip =
            '取消勾选班级考试，已经合格的学员不影响，未合格的学员的无法进入考试。确认要取消勾选？'
        }
        // 班级练习 || 兴趣课程
        if (['practiceLearning', 'interestCourse', 'questionnaire'].includes(argument)) {
          option.selectTip = '调整后将同步影响所有已报名的学员，确认操作？'
          option.cancelSelectTip = option.selectTip
        }
        if (argument === 'learningExperience') {
          option.selectTip = '勾选学习心得，将同步影响方案未合格的学员，确认要勾选课程学习？'
          option.cancelSelectTip =
            '取消勾选学习心得，已合格的学员不受影响，未合格的学员将无法参加并提交学习心得，确认要取消勾选？'
        }
        this.uiLearningTypeSelect.push(option)
      }
      if (
        this.createMode === 3 &&
        this.learningTypeModel.exam.isSelected &&
        (this.recalculating || this.isIntelligenceLearning)
      ) {
        this.activeName = 'exam'
      }
      if (this.commodityBasicInfo.skuProperty.trainingMode.skuPropertyValueId == TrainingModeEnum.offline) {
        this.activeName = 'training-period'
      }
    }

    @Watch('uiLearningTypeSelect', {
      deep: true,
      immediate: false
    })
    uiLearningTypeSelectChange(val: any) {
      console.log('uiLearningTypeSelect', val)
    }

    @Watch('learningTypeModel.courseLearning.isSelected', {
      immediate: true,
      deep: true
    })
    valueChange(val: any) {
      console.log('learningTypeModel.courseLearning.isSelected', val)
    }

    /**
     * 响应勾选切换事件
     */
    handleLearningTypeChange(type: string, newVal: boolean) {
      this.$nextTick(() => {
        const lastVal = !newVal
        if (lastVal && type == 'courseLearning' && this.hasOnlineQuestionnaire) {
          const target = this.learningTypeSelect(type)
          this.$confirm(
            '当前方案调研问卷应用于线上课程，取消勾选线上课程将同步移除调研问卷，是否确定继续操作？',
            '提示',
            {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning',
              callback: (action) => {
                target.value = action === 'confirm' ? newVal : lastVal
                if (!target.value) this.questionnaireRef.removeOnlineCourse()
                this.learningTypeModel[type].isSelected = target.value
                this.updateScheme(type)
              }
            }
          )
        } else if (this.createMode === 3) {
          if (this.classType === 'train_class') {
            const target = this.learningTypeSelect(type)
            const tip = newVal ? target.selectTip : target.cancelSelectTip
            this.$confirm(tip, '提示', {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning',
              callback: (action) => {
                target.value = action === 'confirm' ? newVal : lastVal
                this.learningTypeModel[type].isSelected = target.value
                this.updateScheme(type)
              }
            })
          }
        } else {
          this.learningTypeModel[type].isSelected = newVal
          this.updateScheme(type)
        }
        if (type == 'courseLearning' && !newVal) {
          this.learningTypeModel.learningExperience.removeAllOutlineCourse()
        }
      })
    }

    /**
     * 更新培训方案
     */
    updateScheme(type?: string) {
      const enableCourseLearning = this.learningTypeModel.courseLearning.isSelected
      const enableExamLearning = this.learningTypeModel.exam.isSelected
      const enablePracticeLearning = this.learningTypeModel.practiceLearning.isSelected
      const enableInterestCourseLearning = this.learningTypeModel.interestCourse.isSelected
      const learningExperienceLearning = this.learningTypeModel.learningExperience.isSelected
      const issue = this.learningTypeModel.issue.isSelected
      const questionnaire = this.learningTypeModel.questionnaire.isSelected
      if (!issue) {
        this.learningTypeModel.issue = new IssueLearningType()
      }
      if (!questionnaire) {
        this.learningTypeModel.questionnaire = new QuestionnaireLearningType()
        this.questionnaireRef?.resetQuestionnaire()
      }
      // 未勾选课程学习
      console.log(enableExamLearning, '631')

      if (!enableCourseLearning) {
        this.learningTypeModel.courseLearning = new CourseLearningLearningType()
        this.courseLearnRef?.resetCourseLearning()
      } else if (
        type === 'courseLearning' &&
        this.createMode === 3 &&
        this.learningTypeModelCopy.courseLearning.configId
      ) {
        this.learningTypeModel.courseLearning = cloneDeep(this.learningTypeModelCopy.courseLearning)
        this.$nextTick(() => {
          this.courseLearnRef.getCourseQuizList()
          this.courseLearnRef?.editChooseCourseOutlineRef?.refresh()
          this.courseLearnRef?.editAutoChooseOutlineRef?.refresh()
          this.courseLearnRef?.modifyAutoChooseOutlineRef?.refresh()
          this.courseLearnRef?.modifyChooseCourseOutlineRef?.refresh()
        })
      }
      // 未勾选班级考试
      if (!enableExamLearning) {
        this.learningTypeModel.exam = new ExamLearningType()
        this.examRef?.resetExam()
      } else if (type === 'exam' && this.createMode === 3 && this.learningTypeModelCopy.exam.configId) {
        this.learningTypeModel.exam = cloneDeep(this.learningTypeModelCopy.exam)
        this.$nextTick(() => {
          this.examRef?.initExamList()
        })
      }
      // 未勾选班级练习
      if (!enablePracticeLearning) {
        this.learningTypeModel.practiceLearning = new PracticeLearningType()
        this.practiceLearningRef?.resetPractice()
      } else if (
        type === 'practiceLearning' &&
        this.createMode === 3 &&
        this.learningTypeModelCopy.practiceLearning.configId
      ) {
        this.learningTypeModel.practiceLearning = cloneDeep(this.learningTypeModelCopy.practiceLearning)
        this.$nextTick(() => {
          this.practiceLearningRef?.getSelectedQuestionLibraryList()
        })
      }
      // 未勾选兴趣课
      if (!enableInterestCourseLearning) {
        this.learningTypeModel.interestCourse = new InterestCourseType()
        if (this.isCreateMode) {
          this.editInterestChooseCourseRef?.resetClassification()
        }
        if (this.isModifyMode) {
          this.modifyInterestChooseCourseRef?.resetClassification()
        }
      } else if (
        type === 'interestCourse' &&
        this.createMode === 3 &&
        this.learningTypeModelCopy.interestCourse.configId
      ) {
        this.learningTypeModel.interestCourse = cloneDeep(this.learningTypeModelCopy.interestCourse)
        if (this.isCreateMode) {
          this.$nextTick(() => {
            this.editInterestChooseCourseRef?.refresh()
          })
        }
        if (this.isModifyMode) {
          this.$nextTick(() => {
            this.modifyInterestChooseCourseRef?.refresh()
          })
        }
      }
      // 未勾选学习心得
      if (!learningExperienceLearning) {
        this.learningTypeModel.learningExperience = new LearningExperience()
        this.$nextTick(() => {
          this.studyNotesRef?.getSelectedLearningExperienceList()
        })
      } else if (
        type === 'learningExperience' &&
        this.createMode === 3 &&
        this.learningTypeModelCopy.learningExperience.configId
      ) {
        this.learningTypeModel.learningExperience = cloneDeep(this.learningTypeModelCopy.learningExperience)
        this.$nextTick(() => {
          this.studyNotesRef?.getSelectedLearningExperienceList()
        })
      }
    }

    /**
     * 移除期别需同步删除对应问卷
     */
    removeIssue(id: string) {
      if (
        this.learningTypeModel.questionnaire.isSelected &&
        this.learningTypeModel.questionnaire.questionnaireConfigList.length
      ) {
        this.learningTypeModel.questionnaire.questionnaireConfigList =
          this.learningTypeModel.questionnaire.questionnaireConfigList.filter((item) => item.curIssueId !== id)
        this.questionnaireRef?.page.currentChange(1)
      }
    }
  }
</script>
