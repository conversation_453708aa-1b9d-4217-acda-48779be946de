import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { ResponseStatus } from '@api/Response'

import servicerGateway, {
  ServicerContractStatusEnums,
  ServicerDto,
  ServicerTypeEnums
} from '@api/gateway/PlatformServicer'
import msServicerGateway, { CVendorForTInstitutionRequest, ServicerTokenResponse } from '@api/gateway/ms-servicer-v1'
import { UnAuthorize } from '@api/Secure'

export interface IServicerState {
  /**
   * 服务商信息列表
   */
  servicerList: Array<ServicerDto>
  /**
   * 服务商信息列表
   */
  servicerByTypeList: Array<ServicerDto>

  /**
   * 培训机构与渠道商签约关系
   */
  contractStatus: number

  /**
   * 服务商授权凭证
   */
  servicerToken: ServicerTokenResponse
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerServicerModule', store })
class ServicerModule extends VuexModule implements IServicerState {
  servicerList = new Array<ServicerDto>()
  servicerByTypeList = new Array<ServicerDto>()
  contractStatus = 1
  /**
   * 服务商授权凭证
   */
  servicerToken: ServicerTokenResponse = new ServicerTokenResponse()

  /**   * 通过服务商类型查询关联的服务商信息列表
   * @param inputIdList 服务商Id列表
   * @param inPutType   传入服务商Id列表对应的服务商类型
   * @param outPutType  需查询关联的服务商类型
   */
  @Action
  async findRelationServiceListByType(params: {
    inputIdList: Array<string>
    inPutType: ServicerTypeEnums
    outPutType: ServicerTypeEnums
    contractStatus?: ServicerContractStatusEnums
  }): Promise<ResponseStatus> {
    if (params.contractStatus == undefined || params.contractStatus == null) {
      params.contractStatus = ServicerContractStatusEnums.NORMAL
    }
    const response = await servicerGateway.findRelationServiceListByType(params)
    if (response.status.isSuccess()) {
      this.SET_SERVICER_LIST(response.data)
    }
    return response.status
  }

  /**   通过服务商类型所有服务商信息列表
   * @param servicerType  服务商类型，不传默认培训机构
   */
  @Action
  async findServicerListByType(servicerType?: ServicerTypeEnums): Promise<ResponseStatus> {
    if (servicerType == undefined || servicerType == null) {
      servicerType = ServicerTypeEnums.TRAINING_INSTITUTION
    }
    const response = await servicerGateway.findServicerListByType(servicerType)
    if (response.status.isSuccess()) {
      this.SET_SERVICER_BY_TYPE_LIST(response.data)
    }
    return response.status
  }

  /**
   * 查询培训机构与渠道商的签约关系
   */
  @Action
  @UnAuthorize
  async getTInstitutionAndCVendorContract(param: { trainingInstitutionId: string; channelVendorId: string }) {
    const requestParam = new CVendorForTInstitutionRequest()
    requestParam.trainingInstitutionId = param.trainingInstitutionId
    requestParam.channelVendorId = param.channelVendorId
    const response = await msServicerGateway.getContractStatus(requestParam)
    if (response.status.isSuccess()) {
      this.SET_CONTRACT_STATUS(response.data)
    }
    return response.status
  }

  /**
   * 服务商申请服务凭证（获取服务商对应服务提供商信息）
   */
  @Action
  @UnAuthorize
  async applyForService(servicerId: string) {
    const { status, data } = await msServicerGateway.applyForService(servicerId)
    if (status.isSuccess()) {
      this.SET_SERVICER_TOKEN(data)
    }
    return status
  }

  @Mutation
  private SET_SERVICER_LIST(list: Array<ServicerDto>) {
    this.servicerList = list
  }

  @Mutation
  private SET_SERVICER_BY_TYPE_LIST(list: Array<ServicerDto>) {
    this.servicerByTypeList = list
  }

  @Mutation
  private SET_CONTRACT_STATUS(status: number) {
    this.contractStatus = status
  }

  @Mutation
  private SET_SERVICER_TOKEN(data: ServicerTokenResponse) {
    this.servicerToken = data
  }

  /**
   * 服务商状态是否启用
   */
  get isServicerEnable() {
    return this.servicerToken?.tokenMeta?.status === 1
  }

  /**
   * 培训机构与渠道商签约关系是否正常
   */
  get isTInstitutionAndCVendorContract() {
    return this.contractStatus === 1
  }
}

export default getModule(ServicerModule)
