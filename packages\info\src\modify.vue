<route-params content="/:id"></route-params>
<route-meta>
{
"title": "修改资讯"
}
</route-meta>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/basic-data/info')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/basic-data/info' }">资讯管理</el-breadcrumb-item>
      <el-breadcrumb-item>修改资讯</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="formRef" :rules="rules" :model="form" label-width="auto" class="m-form">
                <el-form-item label="资讯类别：" required prop="categoryType">
                  <information-classification
                    ref="classificationRef"
                    v-model="form.categoryType"
                    :checkStrictly="false"
                    @getNewsCode="getNewsCode"
                  ></information-classification>
                  <el-button type="text" class="f-ml15" @click="goToColumnSetup">新建分类</el-button>
                </el-form-item>
                <el-form-item label="资讯标题：" required prop="title">
                  <el-input v-model="form.title" clearable placeholder="请输入资讯标题" />
                </el-form-item>
                <el-form-item label="摘要信息：">
                  <el-input type="textarea" v-model="form.abstract" :rows="6" placeholder="请输入摘要信息" />
                </el-form-item>
                <el-form-item label="封面图片：">
                  <cover-image-upload
                    :dialogStyleOpation="{
                      width: '500px',
                      height: '300px'
                    }"
                    :ratioArr="['400:225']"
                    :initWidth="400"
                    v-model="form.bgImage"
                    mode="400px 225px"
                  ></cover-image-upload>
                </el-form-item>
                <el-form-item label="资讯内容：" required prop="content">
                  <div class="rich-text">
                    <hb-tinymce-editor v-if="active" v-model="form.content" ref="tinymceEditorRef"></hb-tinymce-editor>
                  </div>
                </el-form-item>
                <el-form-item label="资讯来源：">
                  <el-input v-model="form.source" clearable placeholder="请输入资讯来源" />
                </el-form-item>
                <!-- <el-form-item label="发布地区：" prop="areaCodeList">
                  <biz-region-cascader
                    :check-strictly="true"
                    placeholder="请选择地区"
                    v-model="form.areaCodeList"
                  ></biz-region-cascader>
                </el-form-item> -->
                <el-form-item label="发布时间：">
                  <el-date-picker
                    v-model="form.time"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="datetime"
                    placeholder="选择日期时间"
                    class="form-s"
                  />
                </el-form-item>
                <el-form-item label="弹窗公告：" v-if="isShowPopup">
                  <el-radio-group v-model="form.isPopup">
                    <el-radio :label="true" :disabled="isShowFalse">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="弹窗时间：" v-if="form.isPopup">
                  <double-date-picker
                    :begin-create-time.sync="form.popupBeginTime"
                    :end-create-time.sync="form.popupEndTime"
                    :isLimitEndTimeSecond="isLimitEndTimeSecond"
                  ></double-date-picker>
                </el-form-item>
                <el-form-item label="是否置顶：">
                  <el-radio-group v-model="form.top">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="goBack">取消</el-button>
        <el-button @click="saveDraft" :loading="saveDraftLoading">保存草稿</el-button>
        <el-button type="primary" @click="publish" :loading="publishLoading">发布</el-button>
      </div>
    </div>
    <el-dialog title="操作提示" :visible.sync="dialogVisible" width="20%">
      <span>发布地区为具体地市，无法设置为弹窗公告。</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">我知道了</el-button>
      </span>
    </el-dialog>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import AddCertificationImages from '@hbfe/jxjy-admin-components/src/upload-images.vue'
  import NewsModule from '@api/service/management/news/NewsModule'
  import CoverImageUpload from '@hbfe/jxjy-admin-components/src/cover-image-upload.vue'
  import InformationClassification from '@hbfe/jxjy-admin-components/src/information-classification.vue'
  import CreateDraftNewsVo from '@api/service/management/news/query/query-news-detail/vo/NewsDetail'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/news-date-picker.vue'
  import { NewsCategoryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import MutationNewsChangeStatus from '@api/service/management/news/mutation/mutation-news-change-status/MutationNewsChangeStatus'
  import MutationNewsFactory from '@api/service/management/news/mutation/MutationNewsFactory'
  import bizRegionCascader from '@hbfe/jxjy-admin-components/src/biz/biz-region-cascader.vue'
  import { debounce, bind } from 'lodash-decorators'
  class Category extends NewsCategoryResponse {
    children?: Array<Category>
    fatherId?: string
  }

  @Component({
    components: {
      AddCertificationImages,
      InformationClassification,
      CoverImageUpload,
      DoubleDatePicker,
      bizRegionCascader
    }
  })
  export default class extends Vue {
    @Ref('formRef') formRef: any
    /**
     * 资讯分类组件ref
     */
    @Ref('classificationRef')
    classificationRef: InformationClassification
    queryNewsObj = NewsModule.queryNewsFactory
    form = new CreateDraftNewsVo()
    // 显示富文本框
    active = false
    categoryId = ''
    // 是否显示弹窗选项
    isShowPopup = true
    // 资讯一级分类
    newsClassification = new Array<Category>()
    // 不允许弹窗单选项显隐
    isShowFalse = false
    // 已发布的地区
    areaCodeList: Array<string> = []
    // 已发布是否可以弹窗
    isPopup = true
    dialogVisible = false
    informationClassificationOptions = new Array<Category>()
    notificationId = '' // 通知栏目id
    /**
     * 发布按钮loading
     */
    publishLoading = false
    /**
     * 保存草稿按钮loading
     */
    saveDraftLoading = false
    /**
     * 默认结束时分秒
     */
    isLimitEndTimeSecond = '23:59:59'

    rules = {
      messageType: [{ required: true, message: '请输入资讯类别', trigger: ['blur', 'change'] }],
      title: [{ required: true, message: '请输入资讯标题', trigger: 'blur' }],
      content: [{ required: true, message: '请输入资讯内容', trigger: 'blur' }],
      areaCodeList: [{ required: true, message: '请输入发布地区', trigger: 'blur' }]
    }

    @Watch('form.categoryType', { deep: true, immediate: true })
    formWatchIsPopup(val: Array<string>) {
      if (val && val[0] == this.notificationId) {
        this.form.isPopup = false
        this.isShowPopup = false
      }
    }

    async init() {
      this.active = false
      setTimeout(() => {
        this.active = true
      }, 100)
    }

    async created() {
      await this.init()
      this.informationClassificationOptions = await NewsModule.queryNewsFactory
        .getQueryNewsList()
        .queryNewsRootCategory()
      this.notificationId = this.informationClassificationOptions.filter((item) => {
        return item.code == 'MESSAGE'
      })[0].newsCategoryId
      await this.classificationRef.getInformationClassificationStair()
      this.classificationRef.echo()
      this.form = await NewsModule.queryNewsFactory.getQueryNewsDetail(this.$route.params.id).queryNewsDetail()
      let secondCategoryType: Category
      this.classificationRef.informationClassificationOptions.map((item) => {
        const secondCategoryTypeChild = item.children?.find((ite) => ite.newsCategoryId === this.form.categoryType[0])
        if (secondCategoryTypeChild) secondCategoryType = secondCategoryTypeChild
      })
      if (secondCategoryType) {
        this.form.categoryType = [secondCategoryType.fatherId, this.form.categoryType[0]]
      }
      await this.getNewsClassification()
      this.areaCodeList = this.form.areaCodeList
      this.isPopup = this.form.isPopup
    }

    // 去栏目管理
    goToColumnSetup() {
      this.$router.push('/basic-data/platform/basic-info?&column=' + 'column')
    }

    // 返回上一页
    goBack() {
      this.$router.push('/basic-data/info')
    }

    // 保存草稿
    @bind
    @debounce(200)
    saveDraft() {
      const mutationNewsObj = new MutationNewsFactory()
      const upDataNewsObj = new MutationNewsChangeStatus(this.$route.params.id)
      this.formRef.validate(async (boolean: boolean, value: object) => {
        if (boolean) {
          this.saveDraftLoading = true
          if (this.form.isPopup) {
            if (!this.form.popupBeginTime || !this.form.popupEndTime) {
              this.saveDraftLoading = false
              return this.$message.warning('请选择弹窗时间！')
            }
            if (new Date(this.form.popupBeginTime).getTime() >= new Date(this.form.popupEndTime).getTime()) {
              this.saveDraftLoading = false
              return this.$message.warning('请调整正确的弹窗时间区间！')
            }
          } else {
            this.form.popupBeginTime = undefined
            this.form.popupEndTime = undefined
          }
          this.form.verifyPopUps = false
          try {
            const res = await mutationNewsObj.mutationNewsUpdate().doUpdateSpecialNews(this.form)
            if (res.isSuccess()) {
              const result = await upDataNewsObj.doDraftNews()
              if (result.isSuccess()) {
                this.$message.success('修改成功')
                this.goBack()
                console.log('hahahahha')
              } else {
                this.$message.warning(result.getMessage())
                this.saveDraftLoading = false
              }
            } else {
              this.$message.warning(res.getMessage())
              this.saveDraftLoading = false
            }
          } catch (e) {
            console.log(e)
            this.saveDraftLoading = false
          }
        }
      })
    }

    //发布
    @bind
    @debounce(200)
    publish() {
      const mutationNewsObj = new MutationNewsFactory()
      this.formRef.validate(async (boolean: boolean, value: object) => {
        if (boolean) {
          this.publishLoading = true
          if (this.form.isPopup) {
            if (!this.form.popupBeginTime || !this.form.popupEndTime) {
              this.publishLoading = false
              return this.$message.warning('请选择弹窗时间！')
            }
            if (new Date(this.form.popupBeginTime).getTime() >= new Date(this.form.popupEndTime).getTime()) {
              this.publishLoading = false
              return this.$message.warning('请调整正确的弹窗时间区间！')
            }
          } else {
            this.form.popupBeginTime = undefined
            this.form.popupEndTime = undefined
          }
          if (!this.form.time) {
            const date = this.$moment(new Date(new Date().getTime() + 60 * 1000)).format('YYYY-MM-DD HH:mm:ss')
            this.form.time = date
          }
          this.form.content = this.form.content.replace(/"..\/mfs/g, '"/mfs')
          this.form.verifyPopUps = true
          try {
            const res = await mutationNewsObj.mutationNewsUpdate().doUpdateNews(this.form)
            if (res.isSuccess()) {
              const res = await mutationNewsObj.mutationNewsChangeStatus(this.$route.params.id).doPublishNews()
              if (res.isSuccess()) {
                this.$message.success('修改成功')
                this.goBack()
              } else {
                this.$message.warning('修改失败')
                this.publishLoading = false
              }
            } else {
              this.$message.warning('修改失败')
              this.publishLoading = false
            }
          } catch (e) {
            console.log(e)
            this.publishLoading = false
          }
        }
      })
    }

    // 获取资讯分类code
    async getNewsCode(informationClassificationOptions: Array<Category>, id: string) {
      for (let i = 0; i < informationClassificationOptions.length; i++) {
        if (
          informationClassificationOptions[i].newsCategoryId === id &&
          informationClassificationOptions[i].code === 'MESSAGE'
        ) {
          this.form.isPopup = false
          return (this.isShowPopup = false)
        } else {
          this.isShowPopup = true
        }
      }
    }

    // 获取资讯分类
    async getNewsClassification() {
      this.newsClassification = await NewsModule.queryNewsFactory.getQueryNewsList().queryNewsRootCategory()
      for (let i = 0; i < this.newsClassification.length; i++) {
        if (
          this.newsClassification[i].newsCategoryId === this.form.categoryType[this.form.categoryType.length - 1] &&
          this.newsClassification[i].code === 'MESSAGE'
        ) {
          this.form.isPopup = false
          return (this.isShowPopup = false)
        } else {
          this.isShowPopup = true
        }
      }
    }
    deactivated() {
      ;(this.$refs['tinymceEditorRef'] as any).activated = false
    }
  }
</script>
