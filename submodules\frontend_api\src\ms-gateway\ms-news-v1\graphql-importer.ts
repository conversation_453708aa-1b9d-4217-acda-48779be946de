import findChildNewsCategoryList from './queries/findChildNewsCategoryList.graphql'
import findNewsDetail from './queries/findNewsDetail.graphql'
import findNewsDetailWithPreviousAndNext from './queries/findNewsDetailWithPreviousAndNext.graphql'
import findPopUpsList from './queries/findPopUpsList.graphql'
import findReviewTopNews from './queries/findReviewTopNews.graphql'
import findRootNewsCategoryList from './queries/findRootNewsCategoryList.graphql'
import findSimpleNewsByPublishPageList from './queries/findSimpleNewsByPublishPageList.graphql'
import findSimpleNewsPageList from './queries/findSimpleNewsPageList.graphql'
import findTopNewsCategoryList from './queries/findTopNewsCategoryList.graphql'
import browseNews from './mutates/browseNews.graphql'
import createNews from './mutates/createNews.graphql'
import createNewsCategory from './mutates/createNewsCategory.graphql'
import createSpecialSubjectNews from './mutates/createSpecialSubjectNews.graphql'
import deleteNews from './mutates/deleteNews.graphql'
import deleteNewsCategory from './mutates/deleteNewsCategory.graphql'
import disableNewsCategory from './mutates/disableNewsCategory.graphql'
import enableNewsCategory from './mutates/enableNewsCategory.graphql'
import publishNews from './mutates/publishNews.graphql'
import pulishNewsByZhjyPlatform from './mutates/pulishNewsByZhjyPlatform.graphql'
import unPublishNews from './mutates/unPublishNews.graphql'
import updateNews from './mutates/updateNews.graphql'
import updateNewsCategory from './mutates/updateNewsCategory.graphql'
import updateSpecialSubjectNews from './mutates/updateSpecialSubjectNews.graphql'

export {
  findChildNewsCategoryList,
  findNewsDetail,
  findNewsDetailWithPreviousAndNext,
  findPopUpsList,
  findReviewTopNews,
  findRootNewsCategoryList,
  findSimpleNewsByPublishPageList,
  findSimpleNewsPageList,
  findTopNewsCategoryList,
  browseNews,
  createNews,
  createNewsCategory,
  createSpecialSubjectNews,
  deleteNews,
  deleteNewsCategory,
  disableNewsCategory,
  enableNewsCategory,
  publishNews,
  pulishNewsByZhjyPlatform,
  unPublishNews,
  updateNews,
  updateNewsCategory,
  updateSpecialSubjectNews
}
