<template>
  <el-container>
    <el-main>
      <div class="f-p15">
        <el-card shadow="never" class="m-card f-mb15">
          <el-dialog
            :visible.sync="isShow"
            width="400px"
            :show-close="false"
            :close-on-click-modal="false"
            class="m-dialog"
          >
            <div class="dialog-alert is-big">
              <i class="icon el-icon-warning warning"></i>
              <div class="txt">
                <p class="f-fb f-f16">{{ content }}</p>
              </div>
            </div>
            <div slot="footer">
              <el-button @click="dialogClick">取 消</el-button>
              <el-button type="primary" @click="confirm" :loading="loading">{{
                confirmText ? confirmText : '确定'
              }}</el-button>
            </div>
          </el-dialog>
        </el-card>
      </div>
    </el-main>
  </el-container>
</template>
<script lang="ts">
  import { Component, Vue, Prop } from 'vue-property-decorator'
  import ReceiveAccountVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountVo'
  import TradeInfoConfigModule from '@api/service/management/trade-info-config/TradeInfoConfigModule'
  import { debounce, bind } from 'lodash-decorators'
  @Component
  export default class extends Vue {
    @Prop({ type: String }) content: string
    @Prop({ type: Number }) requestType: number
    @Prop({ type: String }) confirmText: string
    @Prop({ type: ReceiveAccountVo }) itemDetail: ReceiveAccountVo
    isShow = true
    /**
     * 按钮loading
     */
    loading = false
    dialogClick() {
      this.$emit('dialogClick')
    }
    @bind
    @debounce(200)
    async confirm() {
      this.loading = true
      try {
        if (this.requestType == 1) {
          await this.disable()
          this.$emit('dialogClick')
        }
        if (this.requestType == 2) {
          await this.enable()
          this.$emit('dialogClick')
        }
        if (this.requestType == 3) {
          await this.detele()
          this.$emit('dialogClick')
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }
    // 停用
    async disable() {
      const doDisable = await TradeInfoConfigModule.mutationTradeInfoConfigFactory
        .getReceiveAccount(this.itemDetail)
        .doDisable()
      console.log(doDisable, 'doDisable')
      if (doDisable.isSuccess()) {
        this.$message.success('停用成功')
      } else {
        this.$message.warning('请求失败')
      }
    }
    //启用
    async enable() {
      const doEnable = await TradeInfoConfigModule.mutationTradeInfoConfigFactory
        .getReceiveAccount(this.itemDetail)
        .doEnable()
      console.log(doEnable, 'doEnable')
      if (doEnable.isSuccess()) {
        this.$message.success('启用成功')
      } else {
        this.$message.warning('请求失败')
      }
    }
    // 删除
    async detele() {
      const doDetele = await TradeInfoConfigModule.mutationTradeInfoConfigFactory
        .getReceiveAccount(this.itemDetail)
        .doDelete()
      console.log(doDetele, 'doDetele')
      if (doDetele.isSuccess()) {
        this.$emit('doQueryPage')
        this.$message.success('删除成功')
      } else {
        this.$message.warning(doDetele.getMessage())
      }
    }
  }
</script>
