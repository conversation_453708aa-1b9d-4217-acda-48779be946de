import { convertFxMenuMap, convertMenuMap, fxMenuList, fxMenuMap, menuList, menuMap } from '@/common/mock'
import {
  getLocalStorageFxNavList,
  getLocalStorageNavList,
  getRootRoute,
  setLocalStorageFxNavList,
  setLocalStorageNavList
} from '@/common/utils'
import store from '@/store'
import DevToolsModule from '@/store/devtools/DevToolsModule'
import CurrentSystemTime from '@/store/models/CurrentSystemTime'
import IBreadCrumb from '@/store/models/IBreadCrumb'
import Nav from '@/store/models/Nav'
import SwitchStatusEnum from '@/store/models/SwitchStatus'
import AuthorityModule from '@api/service/management/authority/AuthorityModule'
import Menu, { Meta } from '@api/service/management/authority/security/query/vo/Menu'
import { SecurityGroupTree } from '@api/service/management/authority/security/query/vo/SecurityGroupTree'
import UserModule from '@api/service/management/user/UserModule'
import FxConfig from '@hbfe/fx-manage/src/config'
import { Vue } from 'vue-property-decorator'
import { Route } from 'vue-router'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import IMapMenuInfo from './models/IMapMenuInfo'

function getConfigFromMenuListByRouterName(routeName: string, menuList: Array<any>) {
  for (let i = 0; i < menuList.length; i++) {
    const item = menuList[i]
    if (!item?.children?.length) {
      if (item?.router?.path === routeName) {
        return item
      }
    } else if (item.router?.path !== '/dev-help') {
      getConfigFromMenuListByRouterName(routeName, item.children)
    }
  }
}

export interface MenuMap {
  [key: string]: IMapMenuInfo
}
export interface FxMenuMap {
  [key: string]: IMapMenuInfo
}

/**
 * 初始化 nav 导航栏
 * @param menuList
 * @returns {[]}
 */
const initNavBar = (menuList: Array<Menu>) => {
  // 获取storage中缓存的上一次打开的选项卡
  const storageNavList = getLocalStorageNavList()
  // 将选项卡同步到state中
  if (storageNavList?.length) {
    return storageNavList.filter((item: Nav) => {
      return !!menuMap[item.path] || !!menuMap[`${item.path}/`]
    })
  }
  const openWhenInitMenus: Array<any> = new Array<any>()
  const getConfigFromMenuListByRouterName = function (menuList: Array<Menu>) {
    menuList.forEach((menu: Menu) => {
      if (!menu?.children?.length) {
        if (menu?.meta?.openWhenInit) {
          const nav = new Nav()
          nav.rootRoute = getRootRoute(menu.router.path)
          nav.path = menu.router.path
          nav.title = menu.name
          nav.activeRouter = menu.router.path
          nav.closeAble = menu.meta.closeAble
          openWhenInitMenus.push(nav)
        }
      } else {
        getConfigFromMenuListByRouterName(menu.children)
      }
    })
    return {}
  }
  getConfigFromMenuListByRouterName(menuList)
  return openWhenInitMenus
}

const initFxNavBar = (menuList: Array<Menu>) => {
  // 获取storage中缓存的上一次打开的选项卡
  const storageNavList = getLocalStorageFxNavList()
  // 将选项卡同步到state中
  if (storageNavList?.length) {
    return storageNavList.filter((item: Nav) => {
      return !!fxMenuMap[item.path] || !!fxMenuMap[`${item.path}/`]
    })
  }
  const openWhenInitMenus: Array<any> = new Array<any>()
  const getConfigFromMenuListByRouterName = function (menuList: Array<Menu>) {
    menuList.forEach((menu: Menu) => {
      if (!menu?.children?.length) {
        if (menu?.meta?.openWhenInit) {
          const nav = new Nav()
          nav.rootRoute = getRootRoute(menu.router.path)
          nav.path = menu.router.path
          nav.title = menu.name
          nav.activeRouter = menu.router.path
          nav.closeAble = menu.meta.closeAble
          openWhenInitMenus.push(nav)
        }
      } else {
        getConfigFromMenuListByRouterName(menu.children)
      }
    })
    return {}
  }
  getConfigFromMenuListByRouterName(menuList)
  return openWhenInitMenus
}

const analyseMenu = ($menuList: Array<Menu>) => {
  $menuList.forEach((menu: Menu) => {
    if (menu.router) {
      menu.router.path = menu.router?.path.replace(/\/$/, '')
    }
    if (!menu.meta) {
      menu.meta = new Meta()
    }
    if (menu.ext) {
      menu.meta = Object.assign(menu.meta, JSON.parse(menu.ext))
    }
    if (menu.children && menu.children.length) {
      analyseMenu(menu.children)
    }
  })
  $menuList.sort((a: Menu, b: Menu): number => {
    return (a.meta.sort || $menuList.length) - (b.meta.sort || $menuList.length)
  })
}

// 默认打开第一个菜单
const getMenuFirstNode = (menuList: Menu): Menu => {
  if (menuList?.children.length) {
    return getMenuFirstNode(menuList.children[0])
  } else {
    return menuList
  }
}

/**
 * 提取安全对象名字 例如【home.learningSelectExamPaperBtn】
 * @param permissionDto  安全对象实例
 * @param permissions   安全对象名数组
 */
function parsePermissions(permissionDto: SecurityGroupTree, permissions: Array<string>) {
  permissions.push(permissionDto.urlContent)
  permissionDto?.children?.forEach((el: SecurityGroupTree) => {
    parsePermissions(el, permissions)
  })
}

@Module({ namespaced: true, store, dynamic: true, name: 'Root' })
class RootModule extends VuexModule {
  // region state 数据缓存
  title = '管理平台'
  // 版权
  copyright = '福建华博教育科技股份有限公司'
  // 备案号
  record = '闽ICP备08103886号-2'
  // 菜单列表
  menuList = new Array<Menu>()
  //切换菜单列表(营销中心)
  fxMenuList = new Array<Menu>()
  // 当前 tab 激活的索引
  currentNavActiveIndex = 0
  // 切换菜单后的 tab 激活索引
  currentFxActivateIndex = 0
  // tab 激活集合
  navList = new Array<any>()
  // 切换菜单后的 tab 激活集合
  fxNavList = new Array<any>()
  // 保存生命周期的路由项目
  keepAliveWhiteList = new Array<string>()
  // 研发环境的系统报告信息
  systemErrors = new Array<any>()
  // 当前系统时间
  currentSystemTime: CurrentSystemTime = new CurrentSystemTime(0)
  // 菜单是否是折叠状态
  isMenuCollapse = false
  // 菜单是否加载
  menuLoaded = false
  preRemoveMetaName = ''
  // 默认的路由，会根据首页的加载判定去设置
  defaultRoutePath = '/home'
  // 用来控制顶部的一级菜单与二级菜单和导航tab的联动
  menuMap: MenuMap = {}
  // 用来控制切换菜单后顶部的一级菜单与二级菜单和导航tab的联动
  fxMenuMap: FxMenuMap = {}
  // 当前激活的一级菜单
  currentActiveTopMenu = ''
  // 当前激活的一级菜单索引
  currentActiveTopMenuIndex = 0
  // 切换菜单后 处于激活状态的菜单
  currentFxActiveTopMenu = ''
  // 切换菜单后 处于激活状态的菜单索引
  currentFxActiveTopMenuIndex = 1
  // 安全对象集合
  securityList: Array<string> = new Array<string>()
  // 当前菜单的切换状态
  fxMenu: SwitchStatusEnum = SwitchStatusEnum.NOT_SWITCH
  //渠道商数量
  channelListNum = 0
  // 当前登录的网校角色
  unitRole = ''
  // 当前是否为运营域环境
  isOperation = false
  // 营销中心第一个节点
  firstNode: Menu = new Menu()
  // 是否隐藏菜单框架
  hideMenuStatus = false
  /**
   * 对应页面关闭时弹窗提示
   * <路径, 提示语>
   */
  closePageTip = new Map<string, string>()
  // endregion

  // region Action
  @Action
  async generateSecurityList() {
    if (UserModule.queryUserFactory.queryManagerDetail.adminInfo.userInfo.userId) {
      const securityObjectList = await AuthorityModule.securityFactory.querySecurity.getCurrentUserSecurityGroup()

      FxConfig.roleCategoryList = AuthorityModule.securityFactory.querySecurity.roleInfoList.map((res) => res.category)
      const permissions = new Array<string>()
      securityObjectList?.forEach((el) => {
        parsePermissions(el, permissions)
      })
      this.SET_SECURITY_LIST(permissions)
    }
  }

  /**
   * 获取菜单列表
   */
  @Action
  async getMenuList() {
    let envMenuList: any[] = []
    if (process.env.NODE_ENV === 'development' && !DevToolsModule.developmentSettings.openRemoteMenu) {
      envMenuList = menuList
    } else {
      envMenuList = await AuthorityModule.securityFactory.querySecurity.getMergeUserMenu(menuList)
      // envMenuList = data[0]?.children || []
      // TODO 安全对象配置不可用，暂时加载本地菜单
      // envMenuList = menuList
    }

    convertMenuMap(envMenuList)
    const navList = initNavBar(envMenuList)
    this.doSetNavList(navList)
    analyseMenu(envMenuList)
    this.SET_MENU_LIST(envMenuList)
    return envMenuList
  }

  /**
   * 获取菜单列表（营销中心）
   */
  @Action
  async getFxMenuList() {
    let envFxMenuList: Menu[] = new Array<Menu>()
    if (process.env.NODE_ENV === 'development' && !DevToolsModule.developmentSettings.openRemoteMenu) {
      envFxMenuList = fxMenuList
    } else {
      envFxMenuList = await AuthorityModule.securityFactory.querySecurity.getMergeUserMenu(fxMenuList)
    }
    convertFxMenuMap(envFxMenuList)
    const fxNavList = initFxNavBar(envFxMenuList)
    this.doSetFxNavList(fxNavList)
    analyseMenu(envFxMenuList)
    this.SET_FX_MENU_LIST(envFxMenuList)
    // 默认打开第一个菜单
    this.SET_FIRST_NODE(getMenuFirstNode(envFxMenuList[0]))
    if (this.firstNode) {
      this.firstNode.meta.openWhenInit = true
    }
    return envFxMenuList
  }

  @Mutation
  SET_FIRST_NODE(menuList: Menu) {
    this.firstNode = menuList
  }

  @Action
  setMenuLoaded(loaded: boolean) {
    this.SET_MENU_LOADED(loaded)
  }

  @Mutation
  SET_MENU_LOADED(loaded: boolean) {
    this.menuLoaded = false
  }

  @Action
  setCurrentTopMenu(params: { id: string; index: number }) {
    this.SET_CURRENT_TOP_MENU(params)
  }
  @Action
  setFxCurrentTopMenu(params: { id: string; index: number }) {
    this.SET_FX_CURRENT_TOP_MENU(params)
  }

  @Mutation
  SET_CURRENT_TOP_MENU(params: { id: string; index: number }) {
    this.currentActiveTopMenu = params.id
    this.currentActiveTopMenuIndex = params.index
  }
  @Mutation
  SET_FX_CURRENT_TOP_MENU(params: { id: string; index: number }) {
    if (params.index == 0) {
      this.currentFxActiveTopMenu = params.id
      this.currentFxActiveTopMenuIndex = params.index + 1
    } else {
      this.currentFxActiveTopMenu = params.id
      this.currentFxActiveTopMenuIndex = params.index
    }
  }

  @Action
  reloadTab(params: any) {
    this.DO_RELOAD_BY_NAME(params)
  }

  @Action
  addKeepAliveWhiteList(keepAliveWhiteItem: string) {
    this.ADD_KEEP_ALIVE_WHITE_LIST(keepAliveWhiteItem)
  }

  @Action
  removeKeepAliveWhiteList(keepAliveWhiteItem: number) {
    this.REMOVE_KEEP_ALIVE_WHITE_LIST(keepAliveWhiteItem)
  }

  @Action
  doSetNavList(stateList: Array<Nav>) {
    this.DO_SET_NAV_LIST(stateList)
  }

  @Action
  doSetFxNavList(stateList: Array<Nav>) {
    this.DO_SET_FX_NAV_LIST(stateList)
  }

  @Action
  getCurrentNav() {
    return this.navList[this.currentNavActiveIndex]
  }

  @Action
  getCurrentFxNav() {
    return this.fxNavList[this.currentFxActivateIndex]
  }

  @Action
  addSystemError(error: any) {
    this.ADD_SYSTEM_ERROR(error)
  }

  @Action
  setCurrentNavActiveIndex(index: number) {
    this.SET_CURRENT_NAV_ACTIVE_INDEX(index)
  }

  @Action
  setCurrentFxActivateIndex(index: number) {
    this.SET_CURRENT_FX_NAV_ACTIVE_INDEX(index)
  }

  @Action
  getPrevNav(index: number) {
    return this.navList[index - 1]
  }

  @Action
  getPrevFxNav(index: number) {
    return this.fxNavList[index - 1]
  }

  @Action
  doAddNav(params: Route) {
    this.DO_ADD_NAV(params)
  }

  @Action
  doAddFxNav(params: Route) {
    this.DO_ADD_FX_NAV(params)
  }

  @Action
  setNavActiveRouter(params: Route) {
    this.DO_SET_NAV_ACTIVE_ROUTER(params)
  }

  @Action
  setFxNavActiveRouter(params: Route) {
    this.DO_SET_FX_NAV_ACTIVE_ROUTER(params)
  }

  @Action
  changeMenu(status: SwitchStatusEnum) {
    this.SET_FX_MENU(status)
  }
  /**
   * 改变菜单隐藏状态
   * @param hide
   */
  @Action
  changeMenuStatus(hide: boolean) {
    this.DO_CHANGE_MENU_STATUS(hide)
  }

  @Mutation
  DO_CHANGE_MENU_STATUS(hide: boolean) {
    this.hideMenuStatus = hide
  }

  @Mutation
  DO_SET_NAV_ACTIVE_ROUTER(params: Route) {
    const foundOut = this.navList.find((item: Nav, index: number) => {
      item.index = index
      return new RegExp(item.path).test(params.path)
    })
    if (foundOut) {
      foundOut.activeRouter = params.path
      RootModule.pushToBreadCrumb(foundOut, params)
      const parentInfo = this.menuMap[`/${getRootRoute(params.path)}`]
      if (parentInfo) {
        this.currentActiveTopMenu = parentInfo.path
        this.currentActiveTopMenuIndex = parentInfo.index
      }
      this.currentNavActiveIndex = foundOut.index
    }
    setLocalStorageNavList(this.navList)
  }

  @Mutation
  DO_SET_FX_NAV_ACTIVE_ROUTER(params: Route) {
    const foundOut = this.fxNavList.find((item: Nav, index: number) => {
      item.index = index
      return new RegExp(item.path).test(params.path)
    })
    if (foundOut) {
      foundOut.activeRouter = params.path
      RootModule.pushToBreadCrumb(foundOut, params)
      const parentInfo = this.fxMenuMap[`/${getRootRoute(params.path)}`]
      if (parentInfo) {
        this.currentFxActiveTopMenu = parentInfo.path
        this.currentFxActiveTopMenuIndex = parentInfo.index
      }
      this.currentFxActivateIndex = foundOut.index
    }
    setLocalStorageFxNavList(this.fxNavList)
  }

  @Action
  doRemoveNav(path: string) {
    const findOutIndex = this.navList.findIndex((nav: Nav) => {
      return nav.path === path
    })
    const findOutItem = this.navList[findOutIndex]
    this.REMOVE_KEEP_ALIVE_WHITE_LIST_BY_NAME(findOutItem.keepAliveName)
    if (this.currentNavActiveIndex === findOutIndex) {
      if (!this.navList[findOutIndex + 1]) {
        this.SET_CURRENT_NAV_ACTIVE_INDEX(findOutIndex - 1)
      } else {
        this.SET_CURRENT_NAV_ACTIVE_INDEX(findOutIndex + 1)
      }
    }
    if (findOutIndex < this.currentNavActiveIndex) {
      this.SET_CURRENT_NAV_ACTIVE_INDEX(this.currentNavActiveIndex - 1)
    }
    this.REMOVE_NAV_LIST_BY_INDEX(findOutIndex)

    this.REMOVE_KEEP_ALIVE_WHITE_LIST_BY_NAME(this.currentNav.name)
  }

  @Action
  doRemoveFxNav(path: string) {
    const findOutIndex = this.fxNavList.findIndex((nav: Nav) => {
      return nav.path === path
    })
    const findOutItem = this.fxNavList[findOutIndex]
    this.REMOVE_KEEP_ALIVE_WHITE_LIST_BY_NAME(findOutItem.keepAliveName)
    if (this.currentFxActivateIndex === findOutIndex) {
      if (!this.fxNavList[findOutIndex + 1]) {
        this.SET_CURRENT_FX_NAV_ACTIVE_INDEX(findOutIndex - 1)
      } else {
        this.SET_CURRENT_FX_NAV_ACTIVE_INDEX(findOutIndex + 1)
      }
    }
    if (findOutIndex < this.currentFxActivateIndex) {
      this.SET_CURRENT_FX_NAV_ACTIVE_INDEX(this.currentFxActivateIndex - 1)
    }
    this.REMOVE_FX_NAV_LIST_BY_INDEX(findOutIndex)

    this.REMOVE_KEEP_ALIVE_WHITE_LIST_BY_NAME(this.currentNav.name)
  }

  @Action
  async doCloseOtherNav() {
    const temp = this.navList.filter((item: Nav) => {
      return !item.closeAble
    })
    const current = this.currentNav
    if (current.closeAble) {
      temp.push(current)
    }
    this.DO_SET_NAV_LIST(temp)
    this.SET_CURRENT_NAV_ACTIVE_INDEX(this.navList.length - 1)
  }

  @Action
  async doCloseOtherFxNav() {
    const temp = this.fxNavList.filter((item: Nav) => {
      return !item.closeAble
    })
    const current = this.currentFxNav
    if (current.closeAble) {
      temp.push(current)
    }
    this.DO_SET_FX_NAV_LIST(temp)
    this.SET_CURRENT_FX_NAV_ACTIVE_INDEX(this.fxNavList.length - 1)
  }

  @Action
  async doCloseAllNav() {
    const temp = this.navList.filter((item: Nav) => {
      return !item.closeAble
    })
    this.DO_SET_NAV_LIST(temp)
    this.SET_CURRENT_NAV_ACTIVE_INDEX(this.navList.length - 1)
  }

  @Action
  async doCloseAllFxNav() {
    const temp = this.fxNavList.filter((item: Nav) => {
      return !item.closeAble
    })
    this.DO_SET_NAV_LIST(temp)
    this.SET_CURRENT_NAV_ACTIVE_INDEX(this.fxNavList.length - 1)
  }

  @Action({
    commit: 'SET_MENU_COLLAPSE'
  })
  setMenuCollapse(collapse: boolean) {
    return collapse
  }

  @Action
  setCurrentSystemTime(timestamp: number) {
    if (!timestamp) return
    this.SET_CURRENT_SYSTEM_TIME(timestamp)
  }

  // endregion

  // region Mutation
  @Mutation
  SET_CURRENT_SYSTEM_TIME(timestamp: number) {
    this.currentSystemTime.timestamp = timestamp
  }

  @Mutation
  SET_MENU_COLLAPSE(collapse: boolean) {
    this.isMenuCollapse = collapse
  }

  @Mutation
  private DO_RELOAD_BY_NAME(params: { name: string; page: string; value: boolean }) {
    this[params.name][params.page] = params.value
  }

  /**
   * 设置菜单列表
   * @param list
   */
  @Mutation
  SET_MENU_LIST(list: Array<Menu>) {
    list.forEach((menu: Menu, index: number) => {
      Vue.set(this.menuMap, menu.code, {
        name: menu.name,
        path: menu.code,
        meta: menu.meta,
        index,
        menuList: menu.children
      })
    })
    this.menuLoaded = true
    this.menuList = list
  }

  @Mutation
  SET_FX_MENU_LIST(list: Array<Menu>) {
    list.forEach((menu: Menu, index: number) => {
      Vue.set(this.fxMenuMap, menu.code, {
        name: menu.name,
        path: menu.code,
        meta: menu.meta,
        index,
        menuList: menu.children
      })
    })
    this.menuLoaded = true
    this.fxMenuList = list
  }

  @Mutation
  private ADD_KEEP_ALIVE_WHITE_LIST(keepAliveWhiteItem: string) {
    if (
      this.keepAliveWhiteList.findIndex((item) => {
        return item === keepAliveWhiteItem
      }) === -1
    ) {
      this.keepAliveWhiteList.push(keepAliveWhiteItem)
    }
  }

  @Mutation
  private REMOVE_KEEP_ALIVE_WHITE_LIST(index: number) {
    this.keepAliveWhiteList.splice(index, 1)
  }

  @Mutation
  REMOVE_KEEP_ALIVE_WHITE_LIST_BY_NAME(name: string) {
    const index = this.keepAliveWhiteList.findIndex((alive) => {
      return alive === name
    })
    // 使用负数可从数组结尾处规定位置 进行数据删除 会导致bug所以不用。
    if (index > -1) {
      this.preRemoveMetaName = this.keepAliveWhiteList[0]
      this.keepAliveWhiteList.splice(index, 1)
    }
  }

  /**
   * 添加系统错误信息
   * @param data
   * @constructor
   */
  @Mutation
  private ADD_SYSTEM_ERROR(data: any) {
    this.systemErrors.push(data)
  }

  /**
   * 保存当前激活的tab项是哪个
   * @param index
   * @constructor
   */
  @Mutation
  private SET_CURRENT_NAV_ACTIVE_INDEX(index: number) {
    this.currentNavActiveIndex = index
  }

  /**
   * 保存切换菜单后的tab栏
   * @param index
   */
  @Mutation
  private SET_CURRENT_FX_NAV_ACTIVE_INDEX(index: number) {
    this.currentFxActivateIndex = index
  }

  /**
   * 在缓存中添加一个导航元
   * @param params
   * @constructor
   */
  @Mutation
  private DO_ADD_NAV(params: Route) {
    const rootRoute = getRootRoute(params.path)
    let foundOut = this.navList.find((item: Nav, index: number) => {
      item.index = index
      return item.path === params.path
    })
    if (!foundOut) {
      const menuConfig = getConfigFromMenuListByRouterName(`/${rootRoute}`, this.menuList)
      foundOut = {
        index: this.navList.length,
        path: params.path,
        breadCrumb: [
          {
            name: params.meta.title,
            path: params.path
          }
        ],
        title: params.meta.title,
        keepAliveName: params.meta.keepAliveName,
        meta: params.meta,
        activeRouter: params.path,
        id: params.meta.id,
        closeAble: menuConfig?.meta?.closeAble !== false,
        query: params.query
      }
      this.navList.push(foundOut)
    } else {
      foundOut.activeRouter = params.path
      foundOut.query = params.query
      RootModule.pushToBreadCrumb(foundOut, params)
    }
    this.currentNavActiveIndex = foundOut.index
    const parentInfo = this.menuMap[`/${getRootRoute(params.path)}`]
    if (parentInfo) {
      this.currentActiveTopMenu = parentInfo.path
      this.currentActiveTopMenuIndex = parentInfo.index
    }
    setLocalStorageNavList(this.navList)
  }

  @Mutation
  private DO_ADD_FX_NAV(params: Route) {
    const rootRoute = getRootRoute(params.path)
    let foundOut = this.fxNavList.find((item: Nav, index: number) => {
      item.index = index
      return item.path === params.path
    })
    if (!foundOut) {
      const menuConfig = getConfigFromMenuListByRouterName(`/${rootRoute}`, this.fxMenuList)
      foundOut = {
        index: this.fxNavList.length,
        path: params.path,
        breadCrumb: [
          {
            name: params.meta.title,
            path: params.path
          }
        ],
        title: params.meta.title,
        keepAliveName: params.meta.keepAliveName,
        meta: params.meta,
        activeRouter: params.path,
        id: params.meta.id,
        closeAble: menuConfig?.meta?.closeAble !== false,
        query: params.query
      }
      this.fxNavList.push(foundOut)
    } else {
      foundOut.activeRouter = params.path
      foundOut.query = params.query
      RootModule.pushToBreadCrumb(foundOut, params)
    }
    this.currentFxActivateIndex = foundOut.index
    const parentInfo = this.fxMenuMap[`/${getRootRoute(params.path)}`]
    if (parentInfo) {
      this.currentFxActiveTopMenu = parentInfo.path
      this.currentFxActiveTopMenuIndex = parentInfo.index
    }
    setLocalStorageFxNavList(this.fxNavList)
  }

  /**
   * 设置导航tab的列表数据
   * @param list
   * @constructor
   */
  @Mutation
  private DO_SET_NAV_LIST(list: Array<Nav>) {
    this.navList = list
    setLocalStorageNavList(this.navList)
  }

  @Mutation
  private DO_SET_FX_NAV_LIST(list: Array<Nav>) {
    this.fxNavList = list
    setLocalStorageFxNavList(this.fxNavList)
  }

  @Mutation
  private REMOVE_NAV_LIST_BY_INDEX(index: number) {
    this.navList.splice(index, 1)
    setLocalStorageNavList(this.navList)
  }

  @Mutation
  private REMOVE_FX_NAV_LIST_BY_INDEX(index: number) {
    this.fxNavList.splice(index, 1)
    setLocalStorageFxNavList(this.fxNavList)
  }

  @Mutation
  SET_DEFAULT_ROUTE_PATH(routePath: string) {
    this.defaultRoutePath = routePath
  }

  @Mutation
  SET_SECURITY_LIST(list: Array<string>) {
    this.securityList = list
  }

  @Mutation
  SET_CHANNEL_LISTNUM(channelListNum: number) {
    this.channelListNum = channelListNum
  }

  @Mutation
  SET_BROWSER_TITLE() {
    document.title = this.title
  }
  /**
   * 设置当前登录的角色 用于路由合并（继续教育v2）
   * @param role
   * @constructor
   */
  @Mutation
  SET_UNIT_ROLE(role: string) {
    this.unitRole = role
  }

  /**
   * 判断当前环境是否为运营域
   * @param operation
   * @constructor
   */
  @Mutation
  SET_OPERATION_ENVIRONMENT(operation: boolean) {
    this.isOperation = operation
  }

  /**
   * 切换菜单
   * @param status
   * @constructor
   */
  @Mutation
  SET_FX_MENU(status: SwitchStatusEnum) {
    this.fxMenu = status
  }
  /**
   * 设置页面关闭提示语
   */
  @Mutation
  SET_CLOSE_PAGE_TIP(params: { path: string; tip: string }) {
    this.closePageTip.set(params.path, params.tip)
  }

  /**
   * 移除页面关闭提示语
   */
  @Mutation
  REMOVE_CLOSE_PAGE_TIP(path: string) {
    this.closePageTip.delete(path)
  }

  // endregion
  /**
   * 将访问的信息推送到面包屑
   * @param findOutNav
   * @param route
   */
  static pushToBreadCrumb(findOutNav: Nav, route: Route) {
    if (findOutNav.breadCrumb) {
      let findOutIndex = -1
      const findBread = findOutNav.breadCrumb.find((bread: IBreadCrumb, index: number) => {
        const findResult = bread.path === route.path
        if (findResult) {
          findOutIndex = index
        }
        return findResult
      })
      if (!findOutIndex) {
        return (findOutNav.breadCrumb = findOutNav.breadCrumb.slice(0, 1))
      }
      if (!findBread) {
        findOutNav.breadCrumb.push({
          name: route.meta.title,
          path: route.path
        })
      }
    }
  }

  // region Getter
  /**
   * 获取当前tab
   */
  get currentNav(): Nav {
    return this.navList[this.currentNavActiveIndex] || {}
  }

  /**
   * 获取切换后的 选中的tab
   */
  get currentFxNav() {
    return this.fxNavList[this.currentFxActivateIndex] || {}
  }

  /**
   * 判断路由是否显示在 tab 中
   */
  get routeOnTab() {
    return (routePath: string, isMenu: boolean) => {
      return this.navList.find((nav: Nav) => {
        if (isMenu) {
          return nav.path === routePath
        }
        return nav.activeRouter === routePath
      })
    }
  }

  /**
   * 判断路由是否显示在 tab 中
   */
  get fxRouteOnTab() {
    return (routePath: string, isMenu: boolean) => {
      return this.fxNavList.find((nav: Nav) => {
        if (isMenu) {
          return nav.path === routePath
        }
        return nav.activeRouter === routePath
      })
    }
  }

  // endregion
}

export default getModule(RootModule)
