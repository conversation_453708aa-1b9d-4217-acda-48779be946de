<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">资讯管理</el-breadcrumb-item>
      <el-breadcrumb-item>新建资讯</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="auto" class="m-form">
                <el-form-item label="资讯类别：" required>
                  <el-select v-model="form.region" clearable filterable placeholder="请选择资讯类别" class="form-s">
                    <el-option value="选项1"></el-option>
                    <el-option value="选项2"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="资讯标题：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入资讯标题" />
                </el-form-item>
                <el-form-item label="发布至：" required>
                  <el-button type="primary">选择专题</el-button>
                  <el-button type="primary">已选择专题（5）</el-button>
                </el-form-item>
                <!--编辑时-->
                <el-form-item label="发布至：" class="is-text">
                  专题专题专题专题1，专题专题专题专题专题2，专题专题3，专题专题专题4
                </el-form-item>
                <el-form-item label="摘要信息：">
                  <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入摘要信息" />
                </el-form-item>
                <el-form-item label="封面图片：">
                  <el-upload
                    action="#"
                    list-type="picture-card"
                    :auto-upload="false"
                    class="m-pic-upload proportion-pic"
                  >
                    <div slot="default" class="upload-placeholder">
                      <i class="el-icon-plus"></i>
                      <p class="txt">上传图片</p>
                    </div>
                    <div slot="file" slot-scope="{ file }" class="img-file">
                      <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                      <div class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                          <i class="el-icon-zoom-in"></i>
                        </span>
                        <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                          <i class="el-icon-delete"></i>
                        </span>
                      </div>
                    </div>
                    <div slot="tip" class="el-upload__tip">
                      <i class="el-icon-warning"></i>
                      <span class="txt">封面图片比例为16:9，建议尺寸：不小于400px * 225px</span>
                    </div>
                  </el-upload>
                  <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                    <img :src="dialogImageUrl" alt="" />
                  </el-dialog>
                </el-form-item>
                <el-form-item label="资讯内容：" required>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入资讯内容" />
                  </div>
                </el-form-item>
                <el-form-item label="资讯来源：">
                  <el-input v-model="form.name" clearable placeholder="请输入资讯来源" />
                </el-form-item>
                <el-form-item label="发布时间：">
                  <el-date-picker v-model="form.date1" type="datetime" placeholder="选择日期时间" class="form-s" />
                </el-form-item>
                <el-form-item label="弹窗公告：">
                  <el-radio-group v-model="form.resource">
                    <el-radio label="是"></el-radio>
                    <el-radio label="否"></el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="弹窗时间：">
                  <el-date-picker
                    v-model="form.date1"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="起始时间"
                    end-placeholder="结束时间"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="是否置顶：">
                  <el-radio-group v-model="form.resource">
                    <el-radio label="是"></el-radio>
                    <el-radio label="否"></el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button>取消</el-button>
        <el-button>保存草稿</el-button>
        <el-button type="primary">发布</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
