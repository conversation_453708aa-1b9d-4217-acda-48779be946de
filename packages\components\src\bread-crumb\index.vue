<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-09-28 14:15:53
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-10-20 09:08:51
 * @Description: 
-->
<template>
  <div class="pos-nav-box">
    <i class="pos-ico ico"></i>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return" @click="goback"></i>
      </el-button>
      <template v-for="(match, index) in matchedRoutes">
        <el-breadcrumb-item
          style="cursor: pointer; "
          v-if="index !== matchedRoutes.length - 1 && index"
          :key="match.path"
          @click.native="jump(match)"
        >
          {{ match.meta.title ? match.meta.title : '返回上一个页面' }}
        </el-breadcrumb-item>
        <el-breadcrumb-item :key="match.path" v-if="index === matchedRoutes.length - 1 && index">
          {{ match.meta.title ? match.meta.title : '返回上一个页面' }}
        </el-breadcrumb-item>
      </template>
    </el-breadcrumb>
  </div>
</template>

<script>
  import RouteModule from '@/store/RouteModule'
  import RootModule from '@/store/RootModule'
  export default {
    data() {
      return {
        matchedRoutes: []
      }
    },
    created() {
      const key = this.$route.name
      if (RouteModule.breadCrumbList[key]?.length > 0) {
        this.matchedRoutes = RouteModule.breadCrumbList[key]
      } else {
        this.matchedRoutes = this.$route.matched.filter(match => {
          if (match.path && !match.meta.hiddenBreadcrumb) {
            return match.path
          }
        })
      }
    },
    methods: {
      goback() {
        this.$router.go(-1)
      },
      jump(key) {
        const name = this.$route.name
        this.$router.push({
          path: key.path,
          params: key.params,
          query: key.query
        })
        if (RouteModule.breadCrumbList[name]) {
          RootModule.navList.forEach(item => {
            if (item.id === this.$route.meta.id) {
              RootModule.doRemoveNav(item.path)
            }
          })
        }
      }
    }
  }
</script>
