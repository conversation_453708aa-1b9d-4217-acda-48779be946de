<template>
  <el-card shadow="never" class="m-card">
    <!--条件查询-->
    <el-row :gutter="16" class="m-query is-border-bottom">
      <el-form :inline="true" label-width="auto">
        <el-col :sm="10" :md="6" :xl="5">
          <el-form-item label="证件号">
            <el-input v-model="questionnaireAnswerReportList.params.idCard" clearable placeholder="请输入证件号" />
          </el-form-item>
        </el-col>
        <el-col :sm="10" :md="6" :xl="5">
          <el-form-item label="手机号">
            <el-input v-model="questionnaireAnswerReportList.params.phone" clearable placeholder="请输入手机号" />
          </el-form-item>
        </el-col>
        <el-col :sm="10" :md="6" :xl="4">
          <el-form-item label="姓名">
            <el-input v-model="questionnaireAnswerReportList.params.name" clearable placeholder="请输入姓名" />
          </el-form-item>
        </el-col>
        <el-col :sm="10" :md="8" :xl="6">
          <el-form-item label="提交时间">
            <double-date-picker
              :begin-create-time.sync="questionnaireAnswerReportList.params.submitTime.begin"
              :end-create-time.sync="questionnaireAnswerReportList.params.submitTime.end"
            ></double-date-picker>
          </el-form-item>
        </el-col>
        <el-col :sm="10" :md="6" :xl="4" class="f-fr">
          <el-form-item class="f-tr">
            <el-button type="primary" @click="pageQuestion">查询</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <!--表格-->
    <el-table stripe :data="questionnaireAnswerReportList.list" max-height="500px" class="m-table f-mt15">
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="姓名" min-width="160">
        <template v-slot="{ row }">{{ row.name }}</template>
      </el-table-column>
      <el-table-column label="证件号" min-width="160">
        <template v-slot="{ row }">{{ row.idCard }}</template>
      </el-table-column>
      <el-table-column label="手机号" min-width="160" align="center">
        <template v-slot="{ row }">{{ row.phone }}</template>
      </el-table-column>
      <el-table-column label="提交时间" min-width="160" align="center">
        <template v-slot="{ row }">{{ row.submitTime }}</template>
      </el-table-column>
      <el-table-column label="操作" min-width="120" align="center">
        <template v-slot="{ row }">
          <el-button type="text" @click="previewQuestion(row)">查看答卷</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="questionPage" v-bind="questionPage"> </hb-pagination>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, Prop } from 'vue-property-decorator'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import QuestionnaireAnswerReportList from '@api/service/management/resource/question-naire/QuestionnaireAnswerReportList'
  import { Page, UiPage } from '@hbfe/common'
  import QuestionnaireAnswerReportItem from '@api/service/management/resource/question-naire/models/QuestionnaireAnswerReportItem'

  @Component({ components: { DoubleDatePicker } })
  export default class extends Vue {
    @Prop({ type: String, default: '' }) questionId: string

    questionnaireAnswerReportList = new QuestionnaireAnswerReportList()
    page = new Page()
    questionPage: UiPage
    list = new Array<QuestionnaireAnswerReportItem>()

    /////////////////////////////////////////////////////////////////////////////////////////
    constructor() {
      super()
      this.questionPage = new UiPage(this.pageQuestion, this.pageQuestion)
    }
    async created() {
      this.questionnaireAnswerReportList.params.id = this.questionId
      //   this.questionnaireAnswerReportList.params.id = this.$route.query?.answerId as string
      await this.pageQuestion()
    }
    async pageQuestion() {
      this.list = await this.questionnaireAnswerReportList.doQuery(this.questionPage)
      console.log('马麦皮屁屁屁屁屁皮皮', this.questionnaireAnswerReportList.list)
    }
    previewQuestion(row: QuestionnaireAnswerReportItem) {
      window.open(`/admin#/training/scheme/questionAnswer?id=${row.answerPaperId}&issueId=${row.issueId}`, '_blank')
    }
  }
</script>
