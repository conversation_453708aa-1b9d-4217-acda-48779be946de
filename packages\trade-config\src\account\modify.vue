<route-params content="/:id"></route-params>
<route-meta>
{
"title": "修改收款账号"
}
</route-meta>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/basic-data/trade/account')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/basic-data/trade/account' }">收款账户管理</el-breadcrumb-item>
      <el-breadcrumb-item>修改收款账户</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="16" style="max-width: 950px">
              <el-form ref="ruleForm" :rules="rules" :model="payment" label-width="auto" class="m-form">
                <el-form-item label="支付方式：">
                  <el-radio-group v-model="payment.accountType">
                    <el-radio :label="1" disabled>线上</el-radio>
                    <el-radio :label="2" disabled>线下</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="支付账号类型：" v-if="payment.accountType == 1">
                  <el-radio-group
                    v-model="payment.paymentChannelId"
                    v-for="(payType, index) in payTypeList"
                    :key="index"
                    disabled
                  >
                    <el-radio :label="payType.paymentChannelId" class="pay-style">{{ payType.describe }} </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="账户别名：" prop="accountName" v-if="payment.accountType == 1">
                  <el-input
                    v-model="payment.accountName"
                    :disabled="isAllowUpdate"
                    clearable
                    placeholder="请输入账户别名"
                    class="form-l"
                  />
                </el-form-item>
                <!--微信-->
                <div
                  v-if="
                    payment.accountType == 1 &&
                    (payment.paymentChannelId == 'WXPAY' || payment.paymentChannelId == 'WXPAYH5')
                  "
                >
                  <el-form-item label="商户号：" prop="accountNo">
                    <el-input
                      v-model="payment.accountNo"
                      disabled
                      clearable
                      placeholder="请输入商户号"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="API密钥：" prop="merchantKey">
                    <el-input
                      v-model="payment.merchantKey"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入API密钥"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="公众帐号ID：" prop="appId">
                    <el-input
                      v-model="payment.appId"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入公众帐号ID"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="微信证书密钥：" prop="privateKeyPWD">
                    <el-input
                      v-model="payment.privateKeyPWD"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入微信证书密钥"
                      class="form-l"
                    />
                    <span class="f-ml10 f-co">注：默认使用商户号</span>
                  </el-form-item>
                  <el-form-item label="微信证书：" prop="hbFileUploadResponse">
                    <min-upload-file v-model="hbFileUploadResponse" :isDisable="isAllowUpdate" :file-type="2">
                      <el-button type="primary" plain>点击上传</el-button>
                    </min-upload-file>
                  </el-form-item>
                </div>
                <!--支付宝-->
                <div
                  v-if="
                    payment.accountType == 1 &&
                    (payment.paymentChannelId == 'ALIPAY' || payment.paymentChannelId == 'ALIPAYH5')
                  "
                >
                  <el-form-item label="支付宝帐号：" prop="accountNo">
                    <el-input
                      v-model="payment.accountNo"
                      disabled
                      clearable
                      placeholder="请输入支付宝帐号"
                      class="form-l"
                    />
                  </el-form-item>
                  <!-- todo 可能不要，后端未给答复 -->
                  <!-- <el-form-item label="支付宝密钥：">
                    <el-input v-model="alipayKey" clearable placeholder="请输入支付宝密钥" class="form-l" />
                  </el-form-item> -->
                  <el-form-item label="合作者身份ID：" prop="partner">
                    <el-input
                      v-model="payment.partner"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入合作者身份ID"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="支付宝应用私钥：" prop="privateKey">
                    <el-input
                      v-model="payment.privateKey"
                      clearable
                      placeholder="请输入支付宝应用私钥"
                      class="form-l"
                      :disabled="isAllowUpdate"
                    />
                  </el-form-item>
                  <el-form-item label="支付宝公钥：" prop="publicKey">
                    <el-input
                      v-model="payment.publicKey"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入支付宝公钥"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item
                    label="支付宝应用ID："
                    prop="appId"
                    :rules="[{ required: true, message: '请输入支付宝应用ID', trigger: 'blur' }]"
                  >
                    <el-input
                      v-model="payment.appId"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入支付宝应用ID"
                      class="form-l"
                    />
                  </el-form-item>
                </div>

                <!--兴业银行聚合支付-->
                <div
                  v-if="
                    payment.accountType == 1 &&
                    (payment.paymentChannelId == 'CIB_PAY' || payment.paymentChannelId == 'CIB_PAYH5')
                  "
                >
                  <el-form-item label="商户号：" prop="accountNo">
                    <el-input
                      v-model="payment.accountNo"
                      disabled
                      clearable
                      placeholder="请输入商户号"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="应用ID：" prop="xyPayAppId">
                    <el-input
                      v-model="payment.xyPayAppId"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入应用ID"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="终端编号(收款APP终端编号)：" prop="terminalId">
                    <el-input
                      v-model="payment.terminalId"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入终端编号(收款APP终端编号)"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="请求报文签名私钥(SM2签名私钥)：" prop="sm2key">
                    <el-input
                      v-model="payment.sm2key"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入请求报文签名私钥(SM2签名私钥)"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="响应报文验证公钥：" prop="resPublicKey">
                    <el-input
                      v-model="payment.resPublicKey"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入响应报文验证公钥："
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="请求字段加密私钥(字段加密密钥)：" prop="reqKey">
                    <el-input
                      v-model="payment.reqKey"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入请求字段加密私钥(字段加密密钥)"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="微信公众号id：">
                    <el-input
                      v-model="payment.xyPaySubAppId"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入微信公众号id："
                      class="form-l"
                    />
                    <span class="f-ml10 f-co">注：若未配置公众号ID,学员支付完成后无法返回网校页面</span>
                  </el-form-item>
                </div>

                <!-- 建设银行聚合支付 -->
                <div
                  v-if="
                    payment.accountType == 1 &&
                    (payment.paymentChannelId == 'CCB_PAY' || payment.paymentChannelId == 'CCB_PAYH5')
                  "
                >
                  <el-form-item label="商户号：" prop="accountNo" required>
                    <el-input
                      v-model="payment.accountNo"
                      clearable
                      placeholder="请输入商户号"
                      class="form-l"
                      :disabled="isAllowUpdate"
                    />
                  </el-form-item>
                  <el-form-item label="商户柜台代码：" prop="posId" required>
                    <el-input
                      v-model="payment.posId"
                      clearable
                      placeholder="请输入商户柜台代码"
                      class="form-l"
                      :disabled="isAllowUpdate"
                    />
                  </el-form-item>
                  <el-form-item label="分行代码：" prop="branchId" required>
                    <el-input
                      v-model="payment.branchId"
                      clearable
                      placeholder="请输入分行代码"
                      class="form-l"
                      :disabled="isAllowUpdate"
                    />
                  </el-form-item>
                  <el-form-item label="网银支付接口公钥：" prop="jsPublicKey" required>
                    <el-input
                      v-model="payment.jsPublicKey"
                      clearable
                      placeholder="请输入网银支付接口公钥"
                      class="form-l"
                      :disabled="isAllowUpdate"
                    />
                  </el-form-item>
                  <el-form-item label="操作员账号：" prop="operator" required>
                    <el-input
                      v-model="payment.operator"
                      clearable
                      placeholder="请输入操作员账号"
                      class="form-l"
                      :disabled="isAllowUpdate"
                    />
                  </el-form-item>
                  <el-form-item label="操作员交易密码：" prop="password" required>
                    <el-input
                      v-model="payment.password"
                      clearable
                      placeholder="请输入操作员交易密码"
                      class="form-l"
                      :disabled="isAllowUpdate"
                    />
                  </el-form-item>
                  <el-form-item label="是否使用防钓鱼" prop="phishing" required>
                    <el-radio-group v-model="payment.phishing" :disabled="isAllowUpdate">
                      <el-radio :label="1">使用</el-radio>
                      <el-radio :label="0">不使用</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="文件证书：" prop="hbFileUploadResponse">
                    <min-upload-file v-model="hbFileUploadResponse" :isDisable="isAllowUpdate" :file-type="2">
                      <el-button type="primary" class="ml20 mt20" icon="el-icon-upload2"> 选择文件 </el-button>
                    </min-upload-file>
                  </el-form-item>
                  <el-form-item label="文件证书密码：" prop="certPassword" required>
                    <el-input
                      v-model="payment.certPassword"
                      clearable
                      placeholder="请输入文件证书密码"
                      class="form-l"
                      :disabled="isAllowUpdate"
                    />
                  </el-form-item>
                  <el-form-item label="微信公众号id：" prop="jsSubAppid">
                    <el-input
                      v-model="payment.jsSubAppid"
                      clearable
                      placeholder="请输入微信公众号id："
                      class="form-l"
                      :disabled="isAllowUpdate"
                    />
                    <span class="f-ml10 f-co">注：若未配置公众号ID,学员支付完成后无法返回网校页面</span>
                  </el-form-item>
                </div>
                <!--兴业银行聚合支付（威富通）-->
                <div
                  v-if="
                    payment.accountType == 1 &&
                    (payment.paymentChannelId == 'SWIFT_PASS_PAY' || payment.paymentChannelId == 'SWIFT_PASS_PAYH5')
                  "
                >
                  <el-form-item label="商户号：" prop="accountNo">
                    <el-input
                      v-model="payment.accountNo"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入商户号"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="商户私钥：" prop="mchPrivateKey">
                    <el-input
                      v-model="payment.mchPrivateKey"
                      clearable
                      placeholder="请输入商户私钥"
                      class="form-l"
                      :disabled="isAllowUpdate"
                    />
                  </el-form-item>
                  <el-form-item label="平台公钥：" prop="platPublicKey">
                    <el-input
                      v-model="payment.platPublicKey"
                      clearable
                      placeholder="请输入平台公钥"
                      class="form-l"
                      :disabled="isAllowUpdate"
                    />
                  </el-form-item>
                </div>
                <!-- 新大陆-聚合支付-->
                <div v-if="payment.accountType == 1 && payment.paymentChannelId == 'NEW_LAND_PAY'">
                  <el-form-item label="支付商户号：" prop="payMerchantId">
                    <el-input
                      v-model="payment.payMerchantId"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入支付商户号"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="代理商户号：">
                    <el-input
                      v-model="payment.proxyId"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入代理商户号"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="秘钥：" prop="xdlPrivateKey">
                    <el-input
                      v-model="payment.xdlPrivateKey"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入秘钥"
                      class="form-l"
                    />
                  </el-form-item>
                </div>
                <!--线下-->
                <div v-if="payment.accountType == 2">
                  <el-form-item label="账户别名：" prop="accountName">
                    <el-input
                      v-model="payment.accountName"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入账户别名"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="开户号：" prop="accountNo">
                    <el-input
                      v-model="payment.accountNo"
                      disabled
                      clearable
                      placeholder="请输入开户号"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="开户银行：" prop="depositBank">
                    <el-input
                      v-model="payment.depositBank"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入开户银行"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="开户户名：" prop="merchantName">
                    <el-input
                      v-model="payment.merchantName"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入开户户名"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="柜台号：" prop="counterNumber">
                    <el-input
                      v-model="payment.counterNumber"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请输入柜台号"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="纳税人识别号：" prop="taxPayerId">
                    <el-select
                      v-model="payment.taxPayerId"
                      :disabled="isAllowUpdate"
                      clearable
                      placeholder="请选择纳税人识别号"
                      class="form-m"
                    >
                      <el-option
                        :value="item.id"
                        v-for="item in taxPayerIdList"
                        :label="item.accountName"
                        :key="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <el-form-item label="纳税人识别号：" prop="taxPayerId" v-if="payment.accountType == 1">
                  <el-select
                    v-model="payment.taxPayerId"
                    :disabled="isAllowUpdate"
                    clearable
                    placeholder="请选择纳税人识别号"
                    class="form-m"
                  >
                    <el-option
                      :value="item.id"
                      v-for="item in taxPayerIdList"
                      :label="item.accountName"
                      :key="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item v-if="payment.accountType == 1">
                  <div slot="label">
                    <span class="f-vm"><span style="color: red">*</span> 退款方式</span>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                      <div slot="content">
                        <p>退款方式说明：</p>
                        <p>线下退款，方式需要登录微信/支付宝商户后台退款，系统只记录退款状态；</p>
                        <p>线上退款，确认退款后系统会将款项返回原账户。</p>
                      </div>
                    </el-tooltip>
                    <span>：</span>
                  </div>
                  <el-radio-group v-model="payment.refundWay">
                    <el-radio :label="1" :disabled="payment.accountType == 2">线上退款</el-radio>
                    <el-radio :label="2">线下退款</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="扫码引导提示语："
                  v-show="
                    payment.paymentChannelId === PayAccountTypeEnum.CIB_PAY ||
                    payment.paymentChannelId === PayAccountTypeEnum.CCB_PAY ||
                    payment.paymentChannelId === PayAccountTypeEnum.SWIFT_PASS_PAY ||
                    payment.paymentChannelId === PayAccountTypeEnum.NEW_LAND_PAY
                  "
                >
                  <el-input
                    type="textarea"
                    v-model="payment.qrScanPrompt"
                    clearable
                    placeholder="请输入提示语"
                    class="form-l"
                    maxlength="50"
                    show-word-limit
                  />
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-warning-outline m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>默认提示语为：请使用微信、支付宝、银联APP</p>
                      <p>扫描二维码支付</p>
                    </div>
                  </el-tooltip>
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button @click="cancel">取消</el-button>
                  <el-button type="primary" @click="save" :loading="loading">保存</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import MinUploadFile from '@hbfe/jxjy-admin-components/src/min-upload-file.vue'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import TradeInfoConfigModule from '@api/service/management/trade-info-config/TradeInfoConfigModule'
  import MutationUpdateReceiveAccount from '@api/service/management/trade-info-config/mutation/MutationUpdateReceiveAccount'
  import UpdateReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/UpdateReceiveAccountVo'
  import { PaymentChannelIdResponse } from '@api/ms-gateway/ms-trade-configuration-v1'
  import UpdateWXPayReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/UpdateWXPayReceiveAccountVo'
  import UpdateJSPayReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/UpdateJSPayReceiveAccountVo'
  import TaxPayer from '@api/service/management/trade-info-config/mutation/vo/TaxPayer'
  import HbUploadFile from '@hbfe/jxjy-admin-components/src/hb-upload-file.vue'
  import { PayAccountTypeEnum } from '@api/service/management/trade-info-config/enums/PayAccountTypeEnum'

  @Component({
    components: { HbUploadFile, MinUploadFile }
  })
  export default class extends Vue {
    @Ref('ruleForm') ruleForm: any
    id = ''
    getUpdateReceiveAccount = TradeInfoConfigModule.mutationTradeInfoConfigFactory
    updateReceiveAccountObj: MutationUpdateReceiveAccount
    // 支付宝密钥
    alipayKey = ''
    //文件上传之后的回调参数
    hbFileUploadResponse = new HBFileUploadResponse()
    // 纳税人识别号数组
    taxPayerIdList = new Array<TaxPayer>()
    getCreateReceiveAccount = TradeInfoConfigModule.mutationTradeInfoConfigFactory.getCreateReceiveAccount()
    payment = new UpdateReceiveAccountVo()
    // 是否存在大于0元订单
    isAllowUpdate = false
    rules = {
      accountName: [{ type: 'string', required: true, message: '请输入账户别名', trigger: 'blur' }],
      qrScanPrompt: [{ type: 'string', required: true, message: '扫码引导提示语不能为空', trigger: 'blur' }],
      accountNo: [{ required: true, message: '请输入账号', trigger: ['blur', 'change'] }],
      merchantKey: [{ required: true, message: '请输入API密钥', trigger: 'blur' }],
      appId: [{ required: true, message: '请输入公众帐号ID', trigger: 'blur' }],
      // appId1: [{ required: true, message: '请输入支付宝应用ID', trigger: 'blur' }],
      privateKeyPWD: [{ required: true, message: '请输入微信证书密钥', trigger: 'blur' }],
      partner: [{ required: true, message: '请输入合作者身份ID', trigger: 'blur' }],
      privateKey: [{ required: true, message: '请输入支付宝应用私钥', trigger: 'blur' }],
      publicKey: [{ required: true, message: '请输入支付宝应用公钥', trigger: 'blur' }],
      taxPayerId: [{ required: true, message: '请选择纳税人识别号', trigger: ['blur', 'change'] }],
      refundWay: [{ required: true, message: '请选择退款方式', trigger: ['blur', 'change'] }],
      posId: [{ required: true, message: '请输入商户柜台代码', trigger: ['blur', 'change'] }],
      branchId: [{ required: true, message: '请输入分行代码', trigger: ['blur', 'change'] }],
      jsPublicKey: [{ required: true, message: '请输入建行网银支付接口公钥', trigger: ['blur', 'change'] }],
      operator: [{ required: true, message: '请输入操作员账号', trigger: ['blur', 'change'] }],
      password: [{ required: true, message: '请输入操作员交易密码', trigger: ['blur', 'change'] }],
      phishing: [{ required: true, message: '请选择是否使用防钓鱼', trigger: ['blur', 'change'] }],
      certFilePath: [{ required: true, message: '请选择文件证书路径', trigger: ['blur', 'change'] }],
      certPassword: [{ required: true, message: '请选择文件证书密码', trigger: ['blur', 'change'] }],
      mchPrivateKey: [{ required: true, message: '请输入商户私钥', trigger: ['blur', 'change'] }],
      platPublicKey: [{ required: true, message: '请输入平台公钥', trigger: ['blur', 'change'] }],
      payMerchantId: [{ required: true, message: '请输入支付商户号', trigger: ['blur', 'change'] }],
      proxyId: [{ required: true, message: '请输入代理商户号', trigger: ['blur', 'change'] }],
      xdlPrivateKey: [{ required: true, message: '请输入秘钥', trigger: ['blur', 'change'] }]
    }
    payTypeList = new Array<PaymentChannelIdResponse>()

    loading = false
    PayAccountTypeEnum = PayAccountTypeEnum
    // 生命周期
    async created() {
      this.id = this.$route.params.id
      this.updateReceiveAccountObj = TradeInfoConfigModule.mutationTradeInfoConfigFactory.getUpdateReceiveAccount(
        this.id
      )
      this.isAllowUpdate = await this.updateReceiveAccountObj.verifyUpdate()
      const res = await this.updateReceiveAccountObj.queryDetail()
      if (res.isSuccess()) {
        if (this.updateReceiveAccountObj.updateReceiveAccount.paymentChannelId.indexOf('WXPAY') != -1) {
          this.payment = this.updateReceiveAccountObj.updateReceiveAccount
          this.hbFileUploadResponse = new HBFileUploadResponse()
          this.hbFileUploadResponse.fileName = (this.payment as UpdateWXPayReceiveAccountVo).privateKeyFileName
          this.hbFileUploadResponse.url = (this.payment as UpdateWXPayReceiveAccountVo).privateKeyPath
        } else if (this.updateReceiveAccountObj.updateReceiveAccount.paymentChannelId.indexOf('CCB_PAY') != -1) {
          this.payment = this.updateReceiveAccountObj.updateReceiveAccount
          this.hbFileUploadResponse.fileName = (this.payment as UpdateJSPayReceiveAccountVo).certFilePath
          this.hbFileUploadResponse.url = (this.payment as UpdateJSPayReceiveAccountVo).certPassword
        } else {
          this.payment = this.updateReceiveAccountObj.updateReceiveAccount
        }
      }
      this.payTypeList = await TradeInfoConfigModule.mutationTradeInfoConfigFactory
        .getCreateReceiveAccount()
        .getPaymentChannelIdList()
      // this.payTypeList.push({ paymentChannelId: 'CCB_PAY', describe: '建设银行聚合支付' })
      this.taxPayerIdList = await this.getCreateReceiveAccount.getTaxPayerIdList()
      console.log(this.payment, this.taxPayerIdList, 'this.payTypeList', this.payTypeList)
    }

    // 取消事件
    cancel() {
      this.$router.push('/basic-data/trade/account')
    }
    // 保存事件
    async save() {
      this.ruleForm.validate(async (boolean: boolean, value: object) => {
        if (boolean) {
          if (this.payment.accountType === 1 && this.payment.paymentChannelId.indexOf('WXPAY') != -1) {
            if (!this.hbFileUploadResponse.url || this.hbFileUploadResponse.url == '') {
              this.$message.warning('请上传微信证书')
              return
            }
          } else if (this.payment.paymentChannelId == 'CCB_PAY' || this.payment.paymentChannelId == 'CCB_PAYH5') {
            if (!this.hbFileUploadResponse.url || this.hbFileUploadResponse.url === '') {
              return this.$message.warning('请选择上传文件')
            }
          }
          this.updateReceiveAccountObj.updateReceiveAccount = this.payment
          ;(this.updateReceiveAccountObj.updateReceiveAccount as UpdateWXPayReceiveAccountVo).privateKeyPath =
            this.hbFileUploadResponse.url
          ;(this.updateReceiveAccountObj.updateReceiveAccount as UpdateWXPayReceiveAccountVo).privateKeyFileName =
            this.hbFileUploadResponse.fileName
          this.loading = true
          const res = await this.updateReceiveAccountObj.doUpdate()
          if (res.isSuccess()) {
            this.loading = false
            this.$message.success('修改成功')
            this.$router.push('/basic-data/trade/account')
          } else {
            this.loading = false
            this.$message.warning('保存失败')
          }
        }
      })
    }
  }
</script>

<style scoped>
  .pay-style {
    padding-right: 20px;
  }
</style>
