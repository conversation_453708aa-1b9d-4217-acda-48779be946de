import {
  ChooseAnswerOption,
  RadioQuestionResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import QuestionDetail from '../common/QuestionDetail'

/*
 *单选题详情
 */
class RadioQuestionDetailVo extends QuestionDetail {
  /**
   * 可选答案列表
   */
  answerOptions: Array<ChooseAnswerOption> = new Array<ChooseAnswerOption>()

  /**
   * 正确答案ID
   */
  correctAnswerId = ''

  /**
   * 正确答案内容
   */
  correctAnswerContent: ChooseAnswerOption = new ChooseAnswerOption()

  // 模型转换Vo
  from(item: RadioQuestionResponse, list: Array<string>) {
    this.questionId = item.questionId
    this.libraryId = item.libraryInfo.libraryId
    this.libraryName = item.libraryInfo.libraryName
    this.topic = item.topic
    this.dissects = item.dissects
    this.createTime = item.createTime
    this.questionType = item.questionType
    this.relateCourseIds = item.relateCourseIds
    this.isEnable = item.isEnabled
    this.relateCourseNames = list
    this.answerOptions = item.radioAnswerOptions?.map((el) => {
      if (el.id === item.correctAnswerId) {
        this.correctAnswerContent = {
          id: el.id,
          content: el.content,
          enableFillContent: el.enableFillContent,
          mustFillContent: el.mustFillContent
        }
      }
      return {
        id: el.id,
        content: el.content,
        enableFillContent: el.enableFillContent,
        mustFillContent: el.mustFillContent
      }
    })
    this.correctAnswerId = item.correctAnswerId
  }
}
export default RadioQuestionDetailVo
