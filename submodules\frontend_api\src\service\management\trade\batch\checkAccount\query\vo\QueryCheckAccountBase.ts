import { Page, ResponseStatus } from '@hbfe/common'
import CheckAccountParam from '@api/service/management/trade/batch/checkAccount/query/vo/CheckAccountParam'
import CheckAccountListResponse from '@api/service/management/trade/batch/checkAccount/query/vo/CheckAccountListResponse'
import {
  BatchOrderRequest,
  BatchReturnOrderRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import RefundCheckAccountParam from '@api/service/management/trade/batch/checkAccount/query/vo/RefundCheckAccountParam'
import RefundCheckAccountListResponse from '@api/service/management/trade/batch/checkAccount/query/vo/RefundCheckAccountListResponse'
import UserModule from '@api/service/management/user/UserModule'
import CollectiveManagerInfoVo from '@api/service/management/user/query/manager/vo/CollectiveManagerInfoVo'

export default abstract class QueryCheckAccountBase {
  /**
   * 报名订单分页查询
   * @param page 页数
   * @param queryCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<CheckAccountListResponse>
   */
  abstract queryOfRegistrationOrder(
    page: Page,
    queryCheckAccountParam?: CheckAccountParam
  ): Promise<Array<CheckAccountListResponse>>

  abstract statisticBatchOrderInServicer(batchOrderRequest: BatchOrderRequest): Promise<void>

  /**
   * 退款订单分页查询
   * @param page 页数
   * @param queryRefundCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<RefundCheckAccountListResponse>
   */
  abstract queryOfRefundOrder(
    page: Page,
    queryRefundCheckAccountParam?: RefundCheckAccountParam
  ): Promise<Array<RefundCheckAccountListResponse>>

  /**
   * 【集体报名退款订单】查询列表统计数据 statisticBatchReturnOrderInServicer
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<ResponseStatus>}
   */
  abstract queryBatchRefoundListStatistic(queryParams: BatchReturnOrderRequest): Promise<ResponseStatus>

  /**
   * 获取用户信息
   * @param ids id数组
   */
  protected async getUserInfo(ids: Array<string>) {
    // 根据用户ID获取用户信息
    const response = await UserModule.queryUserFactory.queryCollectiveManagerList.queryCollectiveManagerInfoList(ids)
    const userIdMap: Map<string, CollectiveManagerInfoVo> = new Map()
    response.forEach(item => {
      userIdMap.set(item.userId, item)
    })
    return userIdMap
  }
}
