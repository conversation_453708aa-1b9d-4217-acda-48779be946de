import { ResponseStatus } from '@hbfe/common'
import CreateExpressDetailVo from './vo/CreateExpressDetailVo'
import MsOfflineinvoiceV1 from '@api/ms-gateway/ms-offlineinvoice-v1'

/**
 * @description 更新快递备注
 */
class MutationUpdateExpressRemark {
  updateExpressDetail = new CreateExpressDetailVo()

  constructor(id: string, remark: string) {
    this.updateExpressDetail.channelId = id
    this.updateExpressDetail.remark = remark
  }

  async doUpdate(): Promise<ResponseStatus> {
    const { status } = await MsOfflineinvoiceV1.updateChannel(this.updateExpressDetail)
    return status
  }
}

export default MutationUpdateExpressRemark
