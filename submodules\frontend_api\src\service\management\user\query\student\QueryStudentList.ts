import MsBasicDataQueryBackstageGateway, {
  StudentInfoResponse,
  StudentQueryRequest,
  StudentUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import StudentUserInfoVo from './vo/StudentUserInfoVo'
import { Response, Page } from '@hbfe/common'
import StudentQueryIdVo from './vo/StudentQueryIdVo'
import StudentQueryVo from './vo/StudentQueryVo'
import TradeModule from '@api/service/management/trade/TradeModule'
import DataExportBackstage from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { getStudentInfoInSubProject } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage/graphql-importer'
import MsSchemeLearningQueryBackstage, {
  StudentSchemeLearningRequest,
  UserRequest,
  LearningRegisterRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import StudentQueryByCollectionParamVo from '@api/service/management/user/query/student/vo/StudentQueryByCollectionParamVo'

/**
 * 查询用户列表
 */
class QueryStudentList {
  // 查询学员id集合入参
  queryStudentIdParams = new StudentQueryIdVo()

  /**
   * @description: 根据筛选条件查询用户信息列表
   * @param {Page} page
   * @param {StudentQueryVo} params
   */
  async queryStudentListByCondition(page: Page, params: StudentQueryVo): Promise<Response<Array<StudentUserInfoVo>>> {
    const response = new Response<Array<StudentUserInfoVo>>()
    // 存在订单号需要装换为用户id
    if (params.orderNo) {
      const userId = await TradeModule.singleTradeBatchFactor.orderFactor.queryOrder.queryBuyerIdByOrderNo(
        params.orderNo.trim()
      )
      if (!userId) {
        console.error('订单号查询用户ID失败！')
        return
      }
      params.userId = userId
    }
    const res = await MsBasicDataQueryBackstageGateway.pageStudentInfoInServicer({
      page: page,
      request: params.toDto()
    })
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    page.pageSize = res.data?.pageSize
    page.totalSize = res.data?.totalSize
    response.status = res.status
    response.data = new Array<StudentUserInfoVo>()
    res.data?.currentPageData?.map((item: StudentInfoResponse) => {
      const temp = new StudentUserInfoVo()
      temp.from(item)
      response.data.push(temp)
    })
    return response
  }

  /**
   * @description: 根据用户id集合查询用户信息 --- 服务商查询
   * @param {UiPage} uipage
   * @param {Array} userIds
   */
  async queryStudentList(userIds: Array<string>): Promise<Response<Array<StudentUserInfoVo>>> {
    if (userIds.length === 0) {
      console.error('用户id不能为空！')
      const response = new Response<Array<StudentUserInfoVo>>()
      response.data = []
      return response
    }
    const params = new StudentQueryRequest()
    params.user = new StudentUserRequest()
    params.user.userIdList = [...new Set(userIds)]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = params.user?.userIdList?.length
    const res = await MsBasicDataQueryBackstageGateway.pageStudentInfoInServicer({ page: page, request: params })
    const response = new Response<Array<StudentUserInfoVo>>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.status = res.status
    response.data = new Array<StudentUserInfoVo>()
    res.data?.currentPageData?.map((item: StudentInfoResponse) => {
      const temp = new StudentUserInfoVo()
      temp.from(item)
      response.data.push(temp)
    })
    return response
  }
  /**
   * @description: 根据用户id集合查询用户信息 --- 子项目管理查询
   * @param {UiPage} uipage
   * @param {Array} userIds
   */
  async queryStudentListInSubject(userIds: Array<string>): Promise<Response<Array<StudentUserInfoVo>>> {
    if (userIds.length === 0) {
      console.error('用户id不能为空！')
      const response = new Response<Array<StudentUserInfoVo>>()
      response.data = []
      return response
    }
    const params = new StudentQueryRequest()
    params.user = new StudentUserRequest()
    params.user.userIdList = [...new Set(userIds)]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = params.user?.userIdList?.length
    const res = await MsBasicDataQueryBackstageGateway.pageStudentInfoInSubProject({ page: page, request: params })
    const response = new Response<Array<StudentUserInfoVo>>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.status = res.status
    response.data = new Array<StudentUserInfoVo>()
    res.data?.currentPageData?.map((item: StudentInfoResponse) => {
      const temp = new StudentUserInfoVo()
      temp.from(item)
      response.data.push(temp)
    })
    return response
  }

  /**
   * @description: 根据用户名称，身份证，手机号查询用户id集合
   */
  async queryStudentIdList(): Promise<Response<Array<string>>> {
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    const res = await MsBasicDataQueryBackstageGateway.pageStudentInfoInSubProject({
      page: page,
      request: this.queryStudentIdParams.toDto()
    })
    const response = new Response<Array<string>>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    const idList = new Array<string>()
    res.data?.currentPageData?.map((item: StudentInfoResponse) => {
      idList.push(item.userInfo?.userId)
    })
    response.status = res.status
    response.data = [...new Set(idList)]
    return response
  }

  /**
   * @description: 查询学员名单列表  【集体报名咨询】
   */
  async queryStudentListForCollectivity(
    page: Page,
    request: StudentQueryByCollectionParamVo
  ): Promise<Array<StudentUserInfoVo>> {
    const params = new StudentSchemeLearningRequest()

    if (request.phone || request.userName || request.idCard) {
      this.queryStudentIdParams = new StudentQueryIdVo()
      this.queryStudentIdParams.idCard = request.idCard
      this.queryStudentIdParams.phone = request.phone
      this.queryStudentIdParams.userName = request.userName
      const queryIdsRes = await this.queryStudentIdList()

      if (queryIdsRes?.data?.length) {
        params.student = new UserRequest()
        params.student.userIdList = queryIdsRes.data
      } else {
        return []
      }
    }
    params.learningRegister = new LearningRegisterRequest()
    params.learningRegister.status = [1, 2]
    const collectiveUserIds = new Array<string>()
    if (request.collectionUserId) {
      collectiveUserIds.push(request.collectionUserId)
    }
    console.log(params, 'params asdasd')

    const res = await MsSchemeLearningQueryBackstage.pageStudentSchemeLearningByCollectiveUserIdInServicer({
      page: page,
      request: params,
      collectiveUserIds
    })
    if (!res.status?.isSuccess()) {
      console.error('获取学员名单列表失败！')
      return new Array<StudentUserInfoVo>()
    }
    page.totalPageSize = res?.data?.totalPageSize
    page.totalSize = res?.data?.totalSize
    const studentIds = new Array<string>()

    res?.data?.currentPageData?.length &&
      res?.data.currentPageData.map(item => {
        item.userId && studentIds.push(item.userId)
      })

    if (studentIds.length) {
      const result = await this.queryStudentList(studentIds)

      return result.data
    } else {
      return new Array<StudentUserInfoVo>()
    }
  }
  /**
   * 导出学员
   */
  async export(params: StudentQueryVo) {
    //
    const response = await DataExportBackstage.exportStudentExcelInSubProject(params.toExport())
    return response
  }

  /**
   * 批量查询用户信息
   * @param userIds 用户id集合
   */
  async batchQueryStudentDetailMapByUserId(userIds: string[]): Promise<Map<string, UserDetailVo>> {
    const result = new Map<string, UserDetailVo>()
    const reWriteGraphql = new RewriteGraph<StudentInfoResponse, string>(
      MsBasicDataQueryBackstageGateway._commonQuery,
      getStudentInfoInSubProject
    )
    await reWriteGraphql.request(userIds)
    for (const [key, value] of reWriteGraphql.itemMap) {
      const realValue = new UserDetailVo()
      realValue.from(value)
      result.set(key, realValue)
    }
    return result
  }
}

export default QueryStudentList
