/*
 * @Description: 创建资讯草稿DTO
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-17 13:37:51
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-06-01 15:49:04
 */

import { ResponseStatus, Response } from '@hbfe/common'
import MsBusinessNews from '@api/ms-gateway/ms-news-v1'

import { NewsCreateRequest } from '@api/ms-gateway/ms-news-v1'

import CreateDraftNewsVo from '@api/service/management/news/mutation/mutation-news-created/vo/CreateDraftNewsVo'

class NewsCreateDto extends NewsCreateRequest {
  static from(createDraftNewsVo: CreateDraftNewsVo) {
    const newsCreateParam = new NewsCreateDto()
    newsCreateParam.title = createDraftNewsVo.title
    newsCreateParam.summary = createDraftNewsVo.abstract
    newsCreateParam.coverPath = createDraftNewsVo.bgImage
    newsCreateParam.content = createDraftNewsVo.content
    newsCreateParam.source = createDraftNewsVo.source
    newsCreateParam.publishTime = createDraftNewsVo.time
    newsCreateParam.popUps = createDraftNewsVo.isPopup
    newsCreateParam.top = createDraftNewsVo.top
    newsCreateParam.newCategoryId = createDraftNewsVo.categoryType[createDraftNewsVo.categoryType.length - 1]
    newsCreateParam.popupBeginTime = createDraftNewsVo.popupBeginTime
    newsCreateParam.popupEndTime = createDraftNewsVo.popupEndTime
    newsCreateParam.areaCodePath =
      createDraftNewsVo?.areaCodeList && createDraftNewsVo?.areaCodeList.length
        ? '/' + createDraftNewsVo.areaCodeList.join('/')
        : undefined
    return newsCreateParam
  }
  /**
   * 置为草稿
   * @returns
   */
  async toDraft(verifyPopUps = false): Promise<Response<string>> {
    this.verifyPopUps = verifyPopUps
    const response = await MsBusinessNews.createNews(this)
    return response
  }
}
export default NewsCreateDto
