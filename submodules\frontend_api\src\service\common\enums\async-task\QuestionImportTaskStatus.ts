import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 试题导入任务状态
 */
export enum QuestionImportTaskStatusEnum {
  // 1：已创建
  ALREADY_CREATED = 0,
  // 2：已就绪
  ALREADY_PREPARED = 1,
  // 3：执行中
  EXECUTING = 2,
  // 4：执行完成
  EXECUTED = 3
}

class QuestionImportTaskStatus extends AbstractEnum<QuestionImportTaskStatusEnum> {
  static enum = QuestionImportTaskStatusEnum
  constructor(status?: QuestionImportTaskStatusEnum) {
    super()
    this.current = status
    this.map.set(QuestionImportTaskStatusEnum.ALREADY_CREATED, '已创建')
    this.map.set(QuestionImportTaskStatusEnum.ALREADY_PREPARED, '已就绪')
    this.map.set(QuestionImportTaskStatusEnum.EXECUTING, '执行中')
    this.map.set(QuestionImportTaskStatusEnum.EXECUTED, '执行完成')
  }
}

export default new QuestionImportTaskStatus()
