import abstractEnum from '@api/service/common/enums/AbstractEnum'

export enum ShowUserRangeEnum {
  /**
   * 学员
   */
  student = 1,

  /**
   * 集体报名管理员
   */
  collective = 2
}

class ShowUserRange extends abstractEnum<ShowUserRangeEnum> {
  static enum = ShowUserRangeEnum

  constructor(status?: ShowUserRangeEnum) {
    super()
    this.current = status
    this.map.set(ShowUserRangeEnum.student, '学员门户可见')
    this.map.set(ShowUserRangeEnum.collective, '集体报名管理员可见')
  }
}

export default new ShowUserRange()
