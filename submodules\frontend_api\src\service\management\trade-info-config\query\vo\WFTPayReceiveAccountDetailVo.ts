import ReceiveAccountDetailVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountDetailVo'
import { PayAccountTypeEnum } from '@api/service/management/trade-info-config/enums/PayAccountTypeEnum'
import { ReceiveAccountConfigResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

export class WFTPayReceiveAccountDetailVo extends ReceiveAccountDetailVo {
  /**
   * 账号类型
   */
  paymentChannelId: PayAccountTypeEnum = undefined
  /**
   * 商户私钥
   */
  mchPrivateKey = ''
  /**
   * 平台公钥
   */
  platPublicKey = ''

  from(res: ReceiveAccountConfigResponse) {
    this.accountType = res.accountType
    this.accountNo = res.accountNo
    this.accountName = res.name
    this.taxPayerId = res.taxPayerId
    this.refundWay = res.returnType
    this.qrScanPrompt = res.qrScanPrompt
    this.paymentChannelId = res.paymentChannelId as PayAccountTypeEnum
    // JSON
    const temp = res.encryptionKeyData as any
    this.mchPrivateKey = temp.mchPrivateKey
    this.platPublicKey = temp.platPublicKey
  }
}
