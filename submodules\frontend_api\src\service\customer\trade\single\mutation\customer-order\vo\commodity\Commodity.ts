import { BuyCommodityValidationResult } from '@api/service/customer/commodity/models/BuyCommodityValidationResult'
import NormalIssueClassLSGateWay, { IssueJoinValidateRequest } from '@api/gateway/NormalIssueClassLS-default'
import MsCommodityGateway from '@api/gateway/ms-commodity-v1'
import UserModule from '@api/service/customer/user/query-user/UserModule'
export default class Commodity {
  /**
   * 商品id
   */
  commodityId = ''
  /**
   * 商品名
   */
  commoditySaleTitle = ''
  /**
   * 方案id
   */
  schemeId = ''
  /**
   * 方案名
   */
  schemeName = ''
  /**
   * 期别id
   */
  stageId = ''
  /**
   * 期别名
   */
  stageName = ''
  /**
   * 期数id
   */
  issueId = ''
  /**
   * 期数名
   */
  issueName = ''
  /**
   * 方案封面图路径
   */
  schemePicturePath = ''
  /**
   * 商品销售卖点
   */
  commoditySellingPoint = ''
  /**
   * 商品描述介绍
   */
  commodityDescription = ''
  /**
   * 商品描述介绍小程序
   */
  commodityDescriptionUniApp = ''

  /**
   * 机构ID
   */
  trainingInstitutionId = ''
  /**
   * 机构名称
   */
  trainingInstitutionName = ''
  /**
   * 机构简介
   */
  trainingInstitutionAbouts = ''
  /**
   * 课件供应商id
   */
  coursewareSupplierId = ''
  /**
   * 课件供应商名称
   */
  coursewareSupplierName = ''
  /**
   * 培训类别id(路径)
   */
  trainingCategoryId = ''
  /**
   * 培训类别名称
   */
  trainingCategoryName = ''
  /**
   * 培训类别 名称路径
   */
  trainingCategoryNamePath = ''
  /**
   * 工种id
   */
  workTypeId = ''
  /**
   * 工种name
   */
  workTypeName = ''
  /**
   * 适用人群名
   */
  suitableCrowNames: Array<string> = []
  /**
   * 学时
   */
  period = 0
  /**
   * 价格
   */
  price = 0
  /**
   * 商品状态：&quot;UPED&quot;上架|&quot;DOWNED&quot;下架
   */
  commodityState = ''
  /**
   * 商品是否有效
   */
  commodityAvailable = true

  /**
   * 机构组织代码
   */
  unitCode = ''

  /**
   * @description: 商品校验
   * @param {string} terminal 终端唯一编码 Web:Web端 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号
   * @return {*}
   */

  // TODO: 后续这边拿上下文就完事了
  async validate(terminal: string): Promise<BuyCommodityValidationResult> {
    // todo
    // const response = await platformTradeGateway.checkBeforeCreateOrder(commoditySkuId)
    const commodityValidateResult = await MsCommodityGateway.validateCommodity({
      commoditySkuId: this.commodityId,
      channelType: 1,
      terminalCode: terminal
    })
    const validationResult = new BuyCommodityValidationResult()
    validationResult.allowBuy = false
    validationResult.errMsg = '请求失败!'
    validationResult.errCode = '500'
    if (commodityValidateResult.status.isSuccess()) {
      if (commodityValidateResult.data.code === '200') {
        validationResult.allowBuy = true
        const request = new IssueJoinValidateRequest()
        request.skuId = this.commodityId
        // TODO: -LPJ 改上下文
        request.userId = UserModule.userInfo.userId
        request.issueId = this.issueId
        request.purchaseChannelType = 1
        const reserveResponse = await NormalIssueClassLSGateWay.validateJoinIssueLearningScheme(request)
        // 验证结果
        validationResult.errCode = reserveResponse.data.code
        if (reserveResponse.data.code !== '200') {
          validationResult.allowBuy = false
          validationResult.errMsg = reserveResponse.data.message
          validationResult.orderNo = reserveResponse.data.orderNo
        }
      } else {
        validationResult.allowBuy = false
        validationResult.errCode = commodityValidateResult.data.code
        validationResult.errMsg = commodityValidateResult.data.message
      }
    }
    return validationResult
  }
}
