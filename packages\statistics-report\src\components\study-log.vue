<!-- 学习日志 -->
<template>
  <div class="f-p20">
    <el-tabs v-model="activeName" type="card" class="no-margin" @tab-click="handleClick">
      <!-- 进入学习日志 -->
      <el-tab-pane label="进入学习监管日志" name="enter">
        <LearningLog ref="learningLog"></LearningLog>
      </el-tab-pane>

      <!-- 学习过程日志 -->
      <el-tab-pane label="学习过程监管日志" name="process">
        <LearningProcessLog ref="learningProcessLog"></LearningProcessLog>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  import LearningLog from '@hbfe/jxjy-admin-statisticsReport/src/components/study-log/learning-log.vue'
  import LearningProcessLog from '@hbfe/jxjy-admin-statisticsReport/src/components/study-log/learning-process-log.vue'

  @Component({
    components: {
      LearningLog,
      LearningProcessLog
    }
  })
  export default class extends Vue {
    // 激活tab: enter/process
    activeName = 'enter'
    // Ref
    @Ref('learningLog') learningLog: LearningLog
    @Ref('learningProcessLog') learningProcessLog: LearningProcessLog
    // tab切换
    handleClick() {
      if (this.activeName == 'enter') {
        this.learningLog.initSearch()
      }
      if (this.activeName == 'process') {
        this.learningProcessLog.initSearch()
      }
    }
    // 初始化搜索
    initSearch() {
      this.activeName = 'enter'
      this.handleClick()
    }
  }
</script>
