<template>
  <el-drawer
    title="专题基础配置"
    :visible.sync="showBasicDialog"
    size="640px"
    custom-class="m-drawer"
    :before-close="handleClose"
    @open="open"
    append-to-body
  >
    <div class="drawer-bd">
      <div class="f-tr">
        <el-link type="primary" :underline="false" @click="dialog2 = true"
          ><i class="el-icon-picture f-f20 f-mr5 f-vm"></i>查看专题操作示例</el-link
        >
      </div>
      <el-form
        ref="settingFormRef"
        label-width="auto"
        :rules="rules"
        :model="thematicManagementItemBase"
        class="m-form f-mt10"
      >
        <el-form-item label="专题是否开启：" required>
          <el-switch
            v-model="thematicManagementItemBase.isOpen"
            active-text="开启"
            inactive-text="关闭"
            class="m-switch"
          />
        </el-form-item>
        <el-form-item v-if="thematicManagementItemBase.isOpen" label="网校展示专题方案：" required>
          <el-switch
            v-model="thematicManagementItemBase.showSpecialScheme"
            active-text="开启"
            inactive-text="关闭"
            class="m-switch"
          />
          <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
            <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
            <div slot="content">
              <p>支持设置专题内方案展示的规则：</p>
              <p></p>
              <p>1.仅对设置展示在网校门户专题生效。</p>
              <p></p>
              <p>2.如开启网校展示专题方案，对应的报名培训列表筛选项将启用专题的行业、地区。</p>
              <p></p>
              <p>
                3.开启专题下，支持按照网校的培训行业配置在网校报名培训列表报名专题的方案，订单上的来源显示为专题，不标记则统一显示网校名称。
              </p>
            </div>
          </el-tooltip>
        </el-form-item>
        <el-form-item
          v-if="thematicManagementItemBase.showSpecialScheme && thematicManagementItemBase.isOpen"
          label="行业规则："
          required
        >
          <el-checkbox-group v-model="checkList">
            <div class="m-industry-rules">
              <div class="item" v-for="item in filterIndustry" :key="item.industryId">
                <div class="tit">
                  <el-checkbox :label="item.industryId" :disabled="item.isChecked || filterLengthJustOne">{{
                    item.industryName
                  }}</el-checkbox>
                </div>
                <div class="con" style="align-items: center">
                  <div class="tt"><i>*</i>&nbsp;订单来源标记专题：</div>
                  <div class="cc">
                    <el-radio-group v-model="item.type" :disabled="disabledStatus(item.industryId)">
                      <el-radio :label="getLabel(item.industryId, 'first')">{{
                        getRuleName(item.industryId, 'first')
                      }}</el-radio>
                      <el-radio :label="getLabel(item.industryId, 'second')">{{
                        getRuleName(item.industryId, 'second')
                      }}</el-radio>
                    </el-radio-group>
                  </div>
                </div>
              </div>
            </div>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="网校展示入口：" required>
          <el-switch
            v-model="thematicManagementItemBase.isShow"
            active-text="开启"
            inactive-text="关闭"
            class="m-switch"
          />
        </el-form-item>
        <el-form-item label="名称：">
          <el-input
            v-model="thematicManagementItemBase.name"
            placeholder="请输入专题名称，例：专题培训"
            maxlength="8"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="引导语：">
          <el-input
            v-model="thematicManagementItemBase.guide"
            type="textarea"
            placeholder="请输入专题的引导语，例：请选择地区/行业进行报名"
            maxlength="50"
            rows="3"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item class="m-btn-bar">
          <el-button @click.stop="basicCancel">取消</el-button>
          <el-button type="primary" @click.stop="basicConfirm">保存</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-dialog title="专题操作示例" :visible.sync="dialog2" width="1060px" class="m-dialog-pic" append-to-body>
      <div class="f-tc"><img src="@design/admin/assets/images/zt-load-pic.png" width="1160" alt=" " /></div>
    </el-dialog>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import ThematicManagementList from '@api/service/management/thematic-management/ThematicManagementList'
  import ThematicManagementItemBase from '@api/service/management/thematic-management/ThematicManagementItemBase'
  import { cloneDeep } from 'lodash'
  import { RuleTypeEnum } from '@api/service/management/thematic-management/enum/RuleType'
  import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
  import RoleType from '@api/service/management/thematic-management/enum/RuleType'

  @Component
  export default class extends Vue {
    dialog2 = false
    thematicManagementList = new ThematicManagementList()
    thematicManagementItemBase = new ThematicManagementItemBase()
    showBasicDialog = false
    curConfig: ThematicManagementItemBase
    rules = {
      topicsOpen: [{ required: true, message: '请选择', trigger: 'blur' }],
      showSchoolEnter: [{ required: true, message: '请选择', trigger: 'blur' }],
      guiding: [{ required: true, message: '请填写引导语', trigger: 'blur' }]
    }
    /**
     * 复选框
     */
    checkList: string[] = []
    /**
     * 行业规则类型
     */
    ruleTypeEnum = RuleTypeEnum
    /**
     * 标记类型
     */
    roleType = new RoleType()

    @Ref('settingFormRef') settingFormRef: any

    @Watch('checkList')
    onCheckListChange(newValue: string[], oldValue: string[]) {
      //当取消勾选行业时，将规则置空
      if (newValue.length < oldValue.length) {
        const subIndustryId = oldValue.find((item) => !newValue.includes(item))
        const industry = this.thematicManagementItemBase.industryRule.find((item) => item.industryId === subIndustryId)
        industry.type = null
      } else {
        const addIndustryId = newValue.find((item) => !oldValue.includes(item))
        if (addIndustryId === IndustryIdEnum.RS) {
          const industry = this.thematicManagementItemBase.industryRule.find(
            (item) => item.industryId === addIndustryId
          )
          industry.type = this.ruleTypeEnum.specialized_subjects // 原型：勾选人设行业时，默认勾选专业科目，且不支持更改
        }
      }
    }

    /**
     * 当主开关关闭时，底下配置联动关闭
     */
    @Watch('thematicManagementItemBase.isOpen')
    onIsOpenChange(newValue: boolean) {
      if (!newValue) {
        this.thematicManagementItemBase.isShow = false
        this.thematicManagementItemBase.showSpecialScheme = false
      }
    }

    get filterIndustry() {
      return this.thematicManagementItemBase.industryRule.filter((item) => item.isEnable)
    }
    /**
     * 根据行业id和顺序获取规则名称
     */
    getRuleName(industryId: string, order: 'first' | 'second') {
      return this.roleType.map.get(this.getLabel(industryId, order))
    }
    /**
     * 根据行业id和顺序获取label
     */
    getLabel(industryId: string, order: 'first' | 'second') {
      if (industryId === IndustryIdEnum.RS) {
        return order === 'first' ? this.ruleTypeEnum.specialized_subjects : this.ruleTypeEnum.common_subjects
      }
      return order === 'first' ? this.ruleTypeEnum.unification_mark : this.ruleTypeEnum.no_mark
    }
    /**
     * 规则禁用状态
     */
    disabledStatus(industryId: string) {
      if (industryId === IndustryIdEnum.RS) return true
      const industry = this.thematicManagementItemBase.industryRule.find((item) => item.industryId === industryId)

      return (
        industry.isChecked ||
        (JSON.parse(JSON.stringify(this.checkList)) as string[]).findIndex((idx) => idx === industryId) === -1
      )
    }
    /**
     * 当行业仅有一个时，默认勾选并禁用
     */
    get filterLengthJustOne() {
      return this.filterIndustry.length === 1
    }
    /**
     * 打开专题配置弹窗
     */
    openBasicDialog(item: ThematicManagementItemBase) {
      this.showBasicDialog = true
      this.thematicManagementItemBase = item
      this.curConfig = cloneDeep(this.thematicManagementItemBase)
    }
    // 关闭之前弹窗
    handleClose() {
      this.$confirm('确定要放弃编辑吗?', '提示', {
        confirmButtonText: '确定',
        showCancelButton: true,
        type: 'warning'
      }).then(() => {
        // 重置数据
        this.thematicManagementItemBase = this.curConfig
        this.showBasicDialog = false
        // console.log(this.showBasicDialog, '===========showBasicDialog')
      })
    }
    // 取消
    basicCancel() {
      //TODO
      this.$confirm('确定要放弃编辑吗?', '提示', {
        confirmButtonText: '我知道了',
        showCancelButton: true,
        type: 'warning'
      }).then(() => {
        // 重置数据
        this.thematicManagementItemBase = this.curConfig
        this.showBasicDialog = false
      })
    }
    /**
     * 必填项校验
     */
    judgeRequired() {
      if (!this.thematicManagementItemBase.showSpecialScheme) {
        return true
      }
      if (this.checkList.length == 0) {
        this.$message.error('请至少选择一个行业规则进行配置')
        return false
      }
      let isSelectedAll = true
      this.checkList.forEach((industryId) => {
        const industry = this.thematicManagementItemBase.industryRule.find((item) => item.industryId === industryId)
        if (industry.type == null) {
          isSelectedAll = false
        }
      })
      if (!isSelectedAll) {
        this.$message.error('请配置已选行业的行业规则')
      }
      return isSelectedAll
    }
    //保存
    async basicConfirm() {
      if (!this.judgeRequired()) {
        return
      }
      this.thematicManagementItemBase.showSpecialScheme &&
        this.checkList.forEach((industryId) => {
          const industry = this.thematicManagementItemBase.industryRule.find((item) => item.industryId === industryId)
          industry.isChecked = true
        })
      this.settingFormRef.validate(async (val: boolean) => {
        if (val) {
          // console.log(this.thematicManagementItemBase, 'this.thematicManagementItemBase')

          const status = await this.thematicManagementItemBase.save()
          if (status.data.code == '200') {
            this.$message.success('保存成功')
          } else {
            this.$message.error('保存失败,系统异常!')
          }
          this.showBasicDialog = false
        }
      })
    }
    async open() {
      await this.thematicManagementItemBase.getBaseConfig()
      this.curConfig = cloneDeep(this.thematicManagementItemBase)
      this.$nextTick(() => {
        //回显
        this.checkList = this.thematicManagementItemBase.industryRule.reduce((acc, cur) => {
          if (cur.isChecked) {
            acc.push(cur.industryId)
          }
          return acc
        }, [] as string[])
        if (this.filterIndustry.length === 1 && this.checkList.length === 0) {
          this.checkList = [this.filterIndustry[0].industryId]
          this.checkList.forEach((id) => {
            if (id === IndustryIdEnum.RS) {
              const industry = this.filterIndustry.find((item) => item.industryId === id)
              if (!industry.isChecked) {
                industry.type = this.ruleTypeEnum.specialized_subjects
              }
            }
          })
        }
      })
    }
  }
</script>
