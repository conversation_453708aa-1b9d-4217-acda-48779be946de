import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
export class ComponentsObj {
  name: string
  title: string
  suer: string | null
  cancel: string | null
}
@Module({ namespaced: true, dynamic: true, store, name: 'Components' })
class ForgetComponents extends VuexModule {
  phone = '' // 手机号
  active = 1 // 当前步进条进度
  dialog = false // 课程资源教师弹窗
  drawer = false // 抽题是否显示
  /**
   * 1 试题详情 抽屉
   * 2 授课教师详情 抽题
   * 3 阅读服务条款 弹窗
   * 4 线下图片阅读 弹窗
   * 5 选择专家 抽屉
   * 6 xxx专家审核结果 抽屉
   */
  dialogNum = 1 // 要展示的弹窗类型
  componentsObj: ComponentsObj = {
    name: '', // 路径名
    title: '', // 弹窗名
    suer: '', // 按钮1
    cancel: '' // 按钮2
  }
  model = false // 线上线下模式切换
  uploadNum = 0 //上传中的文件
  @Action
  async setPhone(phone: string) {
    this.SET_PHONE(phone)
  }
  @Mutation
  SET_PHONE(phone: string) {
    this.phone = phone
  }
  // 设置步进条的进度
  @Action
  async setActive(active: number) {
    this.SET_ACTIVE(active)
  }
  @Mutation
  SET_ACTIVE(active: number) {
    this.active = active
  }
  // 设置弹窗
  @Action
  async setDialog(e: { dialog: boolean; componentsObj: ComponentsObj }) {
    this.SET_DIALOG(e)
  }
  @Mutation
  SET_DIALOG(e: { dialog: boolean; componentsObj: ComponentsObj }) {
    this.dialog = e.dialog
    this.componentsObj = Object.assign({}, e.componentsObj)
  }
  // 设置抽屉
  @Action
  async setDrawer(e: { dialog: boolean; componentsObj: ComponentsObj }) {
    this.SET_DRAWER(e)
  }
  @Mutation
  SET_DRAWER(e: { dialog: boolean; componentsObj: ComponentsObj }) {
    this.drawer = e.dialog
    this.componentsObj = Object.assign({}, e.componentsObj)
  }
  // 设置线上试题模式
  @Action
  setMode(value: boolean) {
    this.SET_MODE(value)
  }
  @Mutation
  SET_MODE(value: boolean) {
    this.model = value
  }
  // 设置上传文件数
  @Action
  setUploadingNum(loading: number) {
    this.SET_UPLOADING_NUM(loading)
  }
  @Mutation
  SET_UPLOADING_NUM(loading: number) {
    this.uploadNum = this.uploadNum + loading
  }
  // 初始化上传文件数
  @Action
  initNum() {
    this.INIT_NUM()
  }
  @Mutation
  INIT_NUM() {
    this.uploadNum = 0
  }
}

export default getModule(ForgetComponents)
