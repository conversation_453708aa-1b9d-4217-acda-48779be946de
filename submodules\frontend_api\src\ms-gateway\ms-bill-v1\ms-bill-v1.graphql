"""独立部署的微服务,K8S服务名:ms-bill-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""批量获取发票状态为开票中的蓝票日志"""
	getInvoicingServiceLogOfBlueTicket(invoiceIdList:[String]):[InvoicingServiceLogResult]
	"""批量获取发票状态为开票中的红票日志"""
	getInvoicingServiceLogOfRedTicket(invoiceIdList:[String]):[InvoicingServiceLogResult]
}
type Mutation {
	"""添加或更新当前网校自动开票配置
		网校id从上下文中获取
		@param autoConfigRequest 配置
	"""
	addOrUpdateElectronicInvoiceAutoConfig(autoConfigRequest:ElectronicInvoiceAutoConfigRequest):Void
	"""请求冲红电子发票
		@param invoiceId 发票编号
		@return 开票结果
	"""
	flushElectronicInvoice(invoiceId:String):ElectronicInvoiceResultResponse
	"""请求开具电子发票
		@param invoiceId 发票编号
		@return 开票结果
	"""
	issueElectronicInvoice(invoiceId:String):ElectronicInvoiceResultResponse
	"""作废发票
		<pre>
		补偿接口，用于将未开具的发票作废
		@param invoiceId 发票编号
	"""
	obsoleteElectronicInvoice(invoiceId:String):Void
	"""取消开票（谨慎使用！！！）
		<pre>
		补偿接口，用于标记电子发票蓝票开票失败，以便后续重新开票；
		仅当已确认发票已经开具失败后使用的台账处理接口
		@param invoiceId 发票编号
	"""
	reimburseCancelElectronicInvoice(invoiceId:String):Void
	"""重试开具发票
		@param request 发票编号, 发票类型：1-蓝票，2-红票
	"""
	retryIssueElectronicInvoice(request:ElectronicInvoiceRetryIssueRequest):Void
	"""直接冲红发票
		<pre>
		补偿接口，用于冲红发票
		获取发票信息直接请求第三方进行冲红，不生成红票实体
	"""
	rushRedInvoiceItem(request:RushRedInvoiceItemRequest):RushRedInvoiceItemResult
	"""查询发票结果并下载发票
		<pre>
		补偿接口，用于查询开票结果并下载发票
	"""
	searchInvoiceResultAndDownload(request:SearchAndSaveTicketRequest):SearchResult
	"""更新电子发票信息
		@param request 更新发票信息
	"""
	updateElectronicInvoice(request:UpdateElectronicInvoiceRequest):Void
	"""用户登记"""
	userRegistration(request:UserRegistrationInfoRequest):UserRegistrationResult
}
"""电子发票自动开票配置
	<AUTHOR>
	@since 2022/3/30
"""
input ElectronicInvoiceAutoConfigRequest @type(value:"com.fjhb.ms.bill.v1.kernel.gateway.graphql.request.ElectronicInvoiceAutoConfigRequest") {
	"""是否自动开票"""
	auto:Boolean!
	"""间隔小时|自生成发票日期算起多少小时后自动开票"""
	intervalHours:Long!
	"""配置类型，1为个人缴费开票配置，2为集体缴费开票配置"""
	configType:Int!
}
"""电子发票自动开票配置
	<AUTHOR>
	@since 2022/3/30
"""
input ElectronicInvoiceRetryIssueRequest @type(value:"com.fjhb.ms.bill.v1.kernel.gateway.graphql.request.ElectronicInvoiceRetryIssueRequest") {
	"""发票编号"""
	invoiceId:String
	"""发票类型，1-蓝票，2-红票"""
	billType:Int!
}
"""冲红发票请求
	<AUTHOR>
	@since 2023/5/8
"""
input RushRedInvoiceItemRequest @type(value:"com.fjhb.ms.bill.v1.kernel.gateway.graphql.request.RushRedInvoiceItemRequest") {
	"""发票编号"""
	invoiceId:String
	"""发票实体编号"""
	invoiceItemId:String
	"""原始发票代码，仅红票有值"""
	originalBillCode:String
	"""原始发票号码，仅红票有值"""
	originalBillNo:String
	"""开票方式 1-开具红票,2-重试开票"""
	issueMethod:Int!
	"""流水号。仅当 开票方式为：重试开票 有值"""
	flowNo:String
}
"""查询开票结果
	<AUTHOR>
	@since 2023/5/9
"""
input SearchAndSaveTicketRequest @type(value:"com.fjhb.ms.bill.v1.kernel.gateway.graphql.request.SearchAndSaveTicketRequest") {
	"""发票类型"""
	billType:Int!
	"""pdf下载标识"""
	invoiceSerialNum:String
	"""销售方纳税人识别号"""
	sellerTaxpayerNo:String
	"""发票流水号"""
	flowNo:String
	"""发票编号"""
	invoiceId:String
	"""纳税人编号"""
	taxpayerId:String
}
"""更新电子发票信息
	<AUTHOR>
	@since 2021/3/26
"""
input UpdateElectronicInvoiceRequest @type(value:"com.fjhb.ms.bill.v1.kernel.gateway.graphql.request.UpdateElectronicInvoiceRequest") {
	"""发票编号"""
	invoiceId:String
	"""发票抬头，null表示不更新"""
	title:String
	"""发票抬头类型，null表示不更新
		<pre>
		1-个人
		2-企业
		</pre>
	"""
	titleType:Int
	"""购买方纳税人识别号，null表示不更新"""
	taxpayerNo:String
	"""购买方地址，null表示不更新"""
	address:String
	"""购买方电话号码，null表示不更新"""
	phone:String
	"""购买方开户行名称，null表示不更新"""
	bankName:String
	"""购买方银行账户，null表示不更新"""
	account:String
	"""购买方电子邮箱，null表示不更新"""
	email:String
	"""发票票面备注，null表示不更新"""
	remark:String
}
"""用户登记信息
	<AUTHOR>
	@since 2023/8/10
"""
input UserRegistrationInfoRequest @type(value:"com.fjhb.ms.bill.v1.kernel.gateway.graphql.request.UserRegistrationInfoRequest") {
	"""纳税人识别号"""
	taxpayerId:String
	"""纳税人名称"""
	taxpayerName:String
	"""登录身份
		1:财务负责人,
		2:法定代表人,
		3:办税人,
		4:购票员,
		5:普通管理员,
		7:开票员
		99:其他。
		不能使用办税人，必须用开票员、法人、财务负责人
	"""
	loginIdentity:String
	"""办税人员登录密码
		电子税局登录身份密码
	"""
	taxpayerPassword:String
	"""登录方式
		"2":"账密或者中间号",
		"3":"短信验证码登录
	"""
	loginMethod:String
	"""办税人员姓名"""
	taxpayerFullName:String
	"""办税人员身份证件号码"""
	taxpayerIdNumber:String
	"""办税人员手机号码"""
	taxpayerPhoneNumber:String
	"""中间号码"""
	middleNo:String
	"""登录类型
		用于区分新版登录和旧版登录，部分地区只有新版可忽略此字段，传1时为新版登录否则为旧版登录。
	"""
	loginType:String
	"""登录失效标志
		默认false,为true时登录信息失效时会直接返回错误,不再重试登录。
	"""
	loginFailFlag:String
	"""用户id"""
	userId:String
	"""地区编码
		福建：fujian
	"""
	areaCode:String
	"""登录账号
		电子税局登录账号
	"""
	loginAccount:String
	"""登录密码
		电子税局登录密码
	"""
	loginPassword:String
	"""平台服务商下的纳税人"""
	servicerTaxpayerId:String
}
"""冲红结果
	<AUTHOR>
	@since 2023/5/8
"""
type RushRedInvoiceItemResult @type(value:"com.fjhb.ms.bill.v1.api.command.result.RushRedInvoiceItemResult") {
	"""code"""
	code:String
	"""信息"""
	message:String
	"""下载标识"""
	invoiceSerialNum:String
	"""销售方纳税人识别号"""
	sellerTaxpayerNo:String
	"""发票流水号"""
	flowNo:String
	"""发票编号"""
	invoiceId:String
	"""纳税人编号"""
	taxpayerId:String
}
"""用户登记结果
	<AUTHOR>
	@since 2021/3/19
"""
type UserRegistrationResult @type(value:"com.fjhb.ms.bill.v1.api.command.result.UserRegistrationResult") {
	"""登记结果"""
	success:Boolean!
	"""信息"""
	message:String
}
"""第三方网关类型
	<AUTHOR>
	@since 2021/03/16
"""
enum GatewayType @type(value:"com.fjhb.ms.bill.v1.gateway.enums.GatewayType") {
	"""百旺金赋"""
	BAI_WANG
	"""江苏EasyApi"""
	EASY_API
	"""安徽航信"""
	ANHUI_HANG_XING
	"""诺诺发票"""
	NUO_NUO
	"""福建百旺"""
	FUJIAN_BAI_WANG
	"""诺诺发票V2"""
	NUO_NUO_V2
	"""诺税通V2全电票"""
	NUO_SHUI_TONG_V2_ALL_ELECTRONIC
	"""诺税通V2"""
	NUO_SHUI_TONG_V2
}
"""开票模式
	<AUTHOR>
	@since 2021/3/17
"""
enum IssueMode @type(value:"com.fjhb.ms.bill.v1.gateway.enums.IssueMode") {
	"""同步"""
	SYNC
	"""异步"""
	ASYNC
}
"""票据信息
	<AUTHOR>
	@since 2021/3/17
"""
type InvoiceTicketResult @type(value:"com.fjhb.ms.bill.v1.gateway.service.result.InvoiceTicketResult") {
	"""下载pdf地址"""
	pdfOriginalUrl:String
	"""下载ofd地址"""
	ofdOriginalUrl:String
	"""下载xml地址"""
	xmlOriginalUrl:String
	"""pdf存储相对路径"""
	pdfFilePath:String
	"""xml存储相对路径"""
	ofdFilePath:String
	"""ofd存储相对路径"""
	xmlFilePath:String
	"""发票提供商类型"""
	gatewayType:GatewayType
	"""发票号码"""
	billNo:String
	"""发票代码"""
	billCode:String
	"""校验码"""
	checkNo:String
	"""开票时间"""
	billTime:DateTime
	"""开票序列号"""
	serialNumber:String
}
"""查询结果
	<AUTHOR>
	@since 2021/3/18
"""
type SearchResult @type(value:"com.fjhb.ms.bill.v1.gateway.service.result.SearchResult") {
	"""返回结果代码，详见{@link ServiceResult#SUCCESS} 和 {@link ServiceResult#ERROR}"""
	code:String
	"""返回结果信息"""
	message:String
	"""发票流水号"""
	flowNo:String
	"""开票方式"""
	mode:IssueMode
	"""发票票据信息，仅当{@link #mode}等于{@link IssueMode#SYNC},有值"""
	ticket:InvoiceTicketResult
}
"""开票结果
	<AUTHOR>
	@since 2021/3/26
"""
type ElectronicInvoiceResultResponse @type(value:"com.fjhb.ms.bill.v1.kernel.gateway.graphql.response.ElectronicInvoiceResultResponse") {
	"""开票结果"""
	success:Boolean!
	"""信息"""
	message:String
}
"""开票中的日志结果
	<AUTHOR>
	@since 2024/10/16
"""
type InvoicingServiceLogResult @type(value:"com.fjhb.ms.bill.v1.kernel.gateway.graphql.response.InvoicingServiceLogResult") {
	"""发票id"""
	invoiceId:String
	"""日志编号"""
	id:String
	"""开票流水号"""
	flowNo:String
	"""服务接口类型|1-请求开票，2-查询发票，3-下载发票"""
	serviceType:Int
	"""服务接口类型名称"""
	serviceName:String
	"""请求时间"""
	requestTime:DateTime
	"""响应时间"""
	responseTime:DateTime
	"""请求报文"""
	requestData:String
	"""响应报文"""
	responseData:String
	"""响应状态码"""
	responseCode:String
	"""响应消息"""
	responseMessage:String
	"""服务商名称"""
	providerName:String
}

scalar List
