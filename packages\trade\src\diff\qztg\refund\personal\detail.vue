<route-params content="/:id"></route-params>
<route-meta>
{
"isMenu":true,
"hideMenu": true,
"onlyShowOnTab":true,
"title": "个人订单退款详情"
}
</route-meta>

<script lang="ts">
  import OrderDetail from '@hbfe/jxjy-admin-trade/src/refund/personal/detail.vue'
  import { Component, Vue, Ref } from 'vue-property-decorator'

  @Component
  export default class extends OrderDetail {
    /*
     * 同意退款
     * */
    async agreeRefund() {
      this.mutationReturnOrder.returnOrderNo = this.returnOrderDetail.returnOrderNo

      this.mutationReturnOrder.note = this.cancelReason

      this.mutationReturnOrder.orderNo = this.returnOrderDetail.orderNo

      const status = await this.mutationReturnOrder.agreeReturnApply()
      if (!status.isSuccess()) {
        this.$message.error('同意退款失败')
        return
      }
      // 延迟调用
      setTimeout(async () => {
        this.$message.success('同意退款成功')
        await this.geteRfundOrderDetail()
      }, 500)
    }

    /*
     * 确认退款
     * */
    async confirmRefund() {
      this.mutationReturnOrder.returnOrderNo = this.returnOrderDetail.returnOrderNo

      const res = await this.mutationReturnOrder.confirmRefund()

      if (!res.isSuccess()) {
        this.$message.error('确认退款失败')
        return
      }
      // 延迟调用
      setTimeout(async () => {
        this.$message.success('发起确认退款中')
        await this.geteRfundOrderDetail()
      }, 500)
    }
  }
</script>

<style scoped></style>
