<!--
 * @Author: WRP
 * @Date: 全国地区级联选择器
-->
<template>
  <el-cascader
    ref="elCascaderRef"
    v-if="showRegionCascader"
    :props="props"
    :options="regionOptions"
    v-model="selctValue"
    :placeholder="placeholder"
    :clearable="clearable"
    :style="{ width: '100%' }"
    @change="changeInput"
  ></el-cascader>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Emit, Watch, Ref } from 'vue-property-decorator'
  import QueryRegisterRegion from '@api/service/common/basic-data-dictionary/query/QueryRegisterRegion'
  import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
  import { ElCascader } from 'element-ui/types/cascader'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import QueryDeliveryRegion from '@api/service/common/basic-data-dictionary/query/QueryDeliveryRegion'
  import { cloneDeep } from 'lodash'
  @Component
  export default class extends Vue {
    @Ref('elCascaderRef') elCascaderRef: ElCascader
    showRegionCascader = true

    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      default: false
    })
    multiple: boolean

    @Prop({
      default: '请选择地区',
      type: String
    })
    placeholder: string

    // 父子节点是否强关联
    @Prop({
      default: false
    })
    checkStrictly: boolean

    // 传入的必须是数组
    @Prop({
      type: Array,
      default: [] as string[]
    })
    value: string[]

    // 当前选中的值
    selctValue: string[] = []
    // 初始化配置选项
    props = {}
    regionOptions: Array<RegionTreeVo> = new Array<RegionTreeVo>()

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string[]) {
      if (val?.length) {
        this.selctValue = val
      }
    }
    // 切换地区
    changeInput(val: string[]) {
      this.$emit('input', val)
    }

    async created() {
      await this.setProps()
      console.log(this.value, '传入地区')
      this.regionOptions = await QueryDeliveryRegion.queryRegion()
      if (this.value?.length > 2) {
        const res = await QueryDeliveryRegion.queryLowerLevelRegion(this.value[1])
        const regionList = res.map(item => {
          const region = new RegionTreeVo()
          Object.assign(region, item)
          region.leaf = true
          return region
        })
        this.regionOptions
          .find(item => item.code === this.value[0])
          ?.children?.find(item => item.code === this.value[1])
          .children.push(...regionList)
      } else if (this.value?.length == 2) {
        const region = this.regionOptions
          .find(item => item.code === this.value[0])
          ?.children?.find(item => item.code === this.value[1])
        region.leaf = true
      }
      console.log(this.regionOptions)
    }

    setProps() {
      this.props = {
        lazy: true,
        value: 'code',
        label: 'name',
        multiple: this.multiple,
        checkStrictly: this.checkStrictly,
        async lazyLoad(node: any, resolve: (val: any) => {}) {
          const { level } = node
          console.log(node, level, 'level')
          if (level < 1) {
            return
          } else if (level === 1) {
            const data = node.data as any
            resolve(data.children)
          } else {
            const data = node.data as any
            if (data?.children?.length) {
              resolve(data?.children)
              return
            }
            const res = await QueryDeliveryRegion.queryLowerLevelRegion(data.code)
            const arr = res?.map(ite => {
              const region = new RegionTreeVo()
              Object.assign(region, ite)
              return region
            })
            if (!arr.length) {
              resolve(undefined)
            } else {
              arr.map((el: RegionTreeVo) => {
                el['leaf'] = level >= 2 ? true : false
              })
              resolve(arr)
            }
          }

          //通过调用resolve将子节点数据返回，通知组件数据加载完成
        }
      }
    }

    /**
     * 地区去掉最末级children为空
     */
    treeData(data: any) {
      for (let i = 0; i < data.length; i++) {
        if (!data[i].children || data[i].children.length < 1) {
          delete data[i].children
        } else {
          this.treeData(data[i].children)
        }
      }
      return data
    }
  }
</script>
