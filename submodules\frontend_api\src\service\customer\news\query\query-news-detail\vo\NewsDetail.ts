/*
 * @Description: 资讯详情
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-25 10:33:10
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 15:36:39
 */
import NewsDetailBase from './NewsDetailBase'

import { NewsDetailWithPreviousAndNext } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { TrainingChannelDetailResponse } from '@api/platform-gateway/platform-training-channel-back-gateway'

/**
 * 上一篇资讯
 */
export class PrevNewsBase {
  id: string
  title: string
}
/**
 * 下一篇资讯
 */
export class NestNewsBase {
  id: string
  title: string
}
class NewsDetail extends NewsDetailBase {
  // 资讯摘要
  abstract: string
  // 发布时间
  date: string
  // 阅读人数
  number: number
  // 资讯内容
  content: string
  // 上一篇资讯
  prevNews = new PrevNewsBase()
  // 下一篇资讯
  nestNews = new NestNewsBase()

  /**
   * 封面图片路径
   */
  coverPath: string
  // DTO转VO
  static from(newsDetailWithPreviousAndNext: NewsDetailWithPreviousAndNext) {
    const {
      newsDetail: { newId, title, summary, content, publishTime, reviewCount, coverPath },
      previousId,
      nextId
    } = newsDetailWithPreviousAndNext
    const newsDetail = new NewsDetail()
    newsDetail.id = newId
    newsDetail.title = title
    newsDetail.abstract = summary
    newsDetail.content = content
    newsDetail.date = publishTime
    newsDetail.number = reviewCount
    newsDetail.prevNews.id = previousId
    newsDetail.nestNews.id = nextId
    newsDetail.coverPath = coverPath

    return newsDetail
  }
}
export default NewsDetail
