import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/PlatformImportOpened'

// 枚举
export enum TaskTypeEnum {
  IMPORT_TASK = 'IMPORT_TASK',
  COLLECTIVE_REGISTER = 'COLLECTIVE_REGISTER'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class ImportOpenedSubTaskQueryRequest {
  /**
   * 手机号
   */
  phone?: string
  /**
   * 用户名
   */
  userName?: string
  /**
   * 学习方案名称
   */
  schemeName?: string
  /**
   * 期别名称
   */
  issueName?: string
  /**
   * 主任务ID
   */
  taskId?: string
  /**
   * 子任务状态集合
@see com.fjhb.platform.component.task.constants.SubTaskStatusConst
   */
  statusList?: Array<number>
  /**
   * 创建时间 开始时间查询
   */
  startCreateTime?: string
  /**
   * 创建时间 结束时间查询
   */
  endCreateTime?: string
}

/**
 * 导入开通任务创建信息
 */
export class ImportOpenedTaskCreateRequest {
  /**
   * 任务名称
   */
  name?: string
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 密码默认类型
1： 000000
2: 身份证后6位
3: 自定义密码
   */
  pwdType: number
  /**
   * 自定义密码
   */
  password?: string
}

/**
 * 导入开通主任务查询信息
 */
export class TaskQueryRequest {
  /**
   * 主任务状态集合
   */
  statusList?: Array<number>
  /**
   * 创建时间 开始时间查询
   */
  startCreateTime?: string
  /**
   * 创建时间 结束时间查询
   */
  endCreateTime?: string
  /**
   * 任务的类型
   */
  taskType?: TaskTypeEnum
  /**
   * 批次号
   */
  batchNo?: string
}

/**
 * 导入开通子任务
 */
export class ImportOpenedSubTaskResponse {
  /**
   * 子任务ID
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 主任务ID
   */
  taskId: string
  /**
   * 子任务状态
@see com.fjhb.platform.component.task.constants.SubTaskStatusConst
   */
  status: number
  /**
   * 子任务执行步骤
   */
  executedStep: number
  /**
   * 子任务执行开始时间
   */
  startTime: string
  /**
   * 子任务执行完成时间
   */
  completedTime: string
  /**
   * 创建人ID
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 失败信息
   */
  failMessage: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 用户手机
   */
  phone: string
  /**
   * 开通的学习方案名称
   */
  schemeName: string
  /**
   * 开通的期数名称
   */
  issueName: string
  /**
   * 是否为补考用户
   */
  makeUpExam: string
  /**
   * 培训机构名称
   */
  teachUnitName: string
  /**
   * 省（用户）
   */
  province: string
  /**
   * 市（用户）
   */
  city: string
  /**
   * 县（用户）
   */
  county: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 培训机构ID
   */
  teachUnitId: string
  /**
   * 销售渠道ID
   */
  MarketingChannelId: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 用户身份证号码
   */
  identity: string
}

/**
 * 导入开通任务执行结果信息
 */
export class ResultResponse {
  /**
   * 子任务总数
   */
  totalCount: number
  /**
   * 处于创建状态的子任务数量
   */
  createdStatusCount: number
  /**
   * 处于执行中状态的子任务数量
   */
  executingStatusCount: number
  /**
   * 处于执行完成状态的子任务数量
   */
  executedCount: number
  /**
   * 处于执行失败状态的子任务数量
   */
  executeFailedCount: number
  /**
   * 执行到第一步完成的任务的数量
   */
  executeStepOneCount: number
}

/**
 * 导入开通主任务
 */
export class TaskResponse {
  /**
   * 主任务ID
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务状态
@see com.fjhb.platform.component.task.constants.TaskStatusConst
   */
  status: number
  /**
   * 任务执行开始时间
   */
  startTime: string
  /**
   * 任务执行完成时间
   */
  completedTime: string
  /**
   * 创建人ID
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 失败信息
   */
  failMessage: string
  /**
   * excel 文件路径
   */
  filePath: string
  /**
   * 执行结果信息
   */
  result: ResultResponse
}

export class ImportOpenedSubTaskResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ImportOpenedSubTaskResponse>
}

export class TaskResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TaskResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findImportOpenedSubTaskPage(
    params: { page?: Page; query?: ImportOpenedSubTaskQueryRequest },
    query: DocumentNode = GraphqlImporter.findImportOpenedSubTaskPage,
    operation?: string
  ): Promise<Response<ImportOpenedSubTaskResponsePage>> {
    return commonRequestApi<ImportOpenedSubTaskResponsePage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findImportOpenedTaskPage(
    params: { page?: Page; query?: TaskQueryRequest },
    query: DocumentNode = GraphqlImporter.findImportOpenedTaskPage,
    operation?: string
  ): Promise<Response<TaskResponsePage>> {
    return commonRequestApi<TaskResponsePage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createImportOpenedTask(
    createInfo: ImportOpenedTaskCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createImportOpenedTask,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { createInfo },
      operation: operation
    })
  }

  /**   * 导致一个导入开通任务的结果
   * @param taskId 主任务ID
   * @param status 状态集合
   * @return 下载路径
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportImportOpenedSubTask(
    params: { taskId?: string; status?: Array<number> },
    mutate: DocumentNode = GraphqlImporter.exportImportOpenedSubTask,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }
}

export default new DataGateway()
