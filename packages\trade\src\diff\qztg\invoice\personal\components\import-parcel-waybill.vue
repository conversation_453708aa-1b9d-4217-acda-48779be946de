<template>
  <el-drawer title="导入包裹运单" :visible.sync="importVisible" size="600px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="form" label-width="auto" class="m-form f-mt50">
            <el-form-item label="导入文件：">
              <min-upload-file v-model="hbFileUploadResponse" :file-type="1">
                <el-button type="primary" class="fl" plain>选择文件</el-button>
                <div class="el-upload__tip">
                  <i class="el-icon-warning"></i>
                  <span class="txt">导入的文件格式必须为xls,xlsx文件</span>
                </div>
              </min-upload-file>
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="importVisible = false">取消</el-button>
              <el-button v-loading="loading" type="primary" @click="doImport">确定</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import MinUploadFile from '@hbfe/jxjy-admin-components/src/min-upload-file.vue'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import TradeModule from '@api/service/management/trade/TradeModule'

  @Component({
    components: { MinUploadFile }
  })
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    importDialog: boolean

    importVisible = false

    loading = false

    // 发票配送业务实例
    mutationDeliveryInvoiceVo = TradeModule.singleTradeBatchFactor.invoiceFactor.mutationDeliveryInvoice

    //文件上传之后的回调参数
    hbFileUploadResponse = new HBFileUploadResponse()

    @Watch('importDialog')
    changeDialogCtrl() {
      this.importVisible = this.importDialog
      this.hbFileUploadResponse = new HBFileUploadResponse()
    }

    @Watch('importVisible')
    changeDialogVisible() {
      this.$emit('update:importDialog', this.importVisible)
    }

    async doImport() {
      if (!this.hbFileUploadResponse.url || this.hbFileUploadResponse.url == '') {
        this.$message.warning('请上传导入文件')
        return
      }
      this.loading = true
      const res = await this.mutationDeliveryInvoiceVo.importOfflineInvoice(this.hbFileUploadResponse.url)
      if (res.isSuccess()) {
        this.$message.success('导入成功')
        this.$emit('importSuccess')
      } else {
        this.$message.warning('导入失败')
      }
      this.hbFileUploadResponse.url = ''
      this.hbFileUploadResponse.fileName = ''
      this.importVisible = false
      this.loading = false
    }

    created() {
      this.importVisible = this.importDialog
    }
  }
</script>
<style scoped>
  ::v-deep .el-upload {
    text-align: left;
  }
  ::v-deep .el-upload-list__item {
    width: 60%;
  }
</style>
