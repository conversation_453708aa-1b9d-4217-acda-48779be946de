/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-20 09:17:47
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-19 17:10:03
 */
import { Page, ResponseStatus } from '@hbfe/common'
import QueryBatchRefundListParamVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchRefundListParamVo'
import BatchRefoundListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchRefoundListDetailVo'
import BatchRefoundDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchRefoundDetailVo'
import TradeQuery, {
  BatchReturnOrderSortRequest,
  ReturnSortRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import UserModule from '@api/service/management/user/UserModule'
import CollectiveManagerQueryIdVo from '@api/service/management/user/query/manager/vo/CollectiveManagerQueryIdVo'
import CollectiveManagerInfoVo from '@api/service/management/user/query/manager/vo/CollectiveManagerInfoVo'
import SchemeLearnind, {
  LearningRegisterRequest,
  StudentLearningRequest,
  StudentSchemeLearningRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import DataExportBackstage from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import fxnlQuery from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import QueryBatchRefundBase from '@api/service/management/trade/batch/order/query/vo/QueryBatchRefundBase'

/**
 * @description 查询批次退款订单
 */
class QueryBatchRefundList extends QueryBatchRefundBase {
  /**
   * 退款单数
   */
  totalRefoundCount = 0
  /**
   * 退款金额
   */
  totalRefoundAmount = 0
  /**
   * 【集体报名退款】是否为强制退款
   * @param batchOrderNo - 批次单id
   * @param subOrderNoList - 批次单内子单id
   * @return Array<number> 0为普通 1为存在考核结果 2为存在学习完成
   */
  async queryIsForce(batchOrderNo: string, subOrderNoList?: string) {
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 5
    const studentSchemeLearningRequest = new StudentSchemeLearningRequest()
    studentSchemeLearningRequest.learningRegister = new LearningRegisterRequest()
    studentSchemeLearningRequest.studentLearning = new StudentLearningRequest()
    studentSchemeLearningRequest.learningRegister.batchOrderNoList = [batchOrderNo]
    studentSchemeLearningRequest.learningRegister.subOrderNoList = subOrderNoList ? [subOrderNoList] : undefined
    studentSchemeLearningRequest.learningRegister.status = [1]
    studentSchemeLearningRequest.studentLearning.courseScheduleStatus = 2 //学习完成
    const learningToCompleteCount = await this.isForceMethods(page, studentSchemeLearningRequest)
    studentSchemeLearningRequest.studentLearning.courseScheduleStatus = undefined
    studentSchemeLearningRequest.studentLearning.trainingResultList = [1] //培训结果
    const trainingResultsCount = await this.isForceMethods(page, studentSchemeLearningRequest)
    const result = []
    if (learningToCompleteCount !== 0 || trainingResultsCount !== 0) {
      if (learningToCompleteCount !== 0) {
        result.push(2)
      }
      if (trainingResultsCount !== 0) {
        result.push(1)
      }
    } else {
      result.push(0)
    }
    return result
  }
  /**
   * 学习完成 判断
   */
  private async isForceMethods(page: Page, request: StudentSchemeLearningRequest) {
    const { data } = await SchemeLearnind.pageStudentSchemeLearningInServicer({ page, request })
    return data.currentPageData.length
  }

  /**
   * 【集体报名退款】是否为强制退款 （分销）
   * @param batchOrderNo - 批次单id
   * @param subOrderNoList - 批次单内子单id
   * @return Array<number> 0为普通 1为存在考核结果 2为存在学习完成
   */
  async queryFxIsForce(batchOrderNo: string, subOrderNoList?: string) {
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 5
    const studentSchemeLearningRequest = new StudentSchemeLearningRequest()
    studentSchemeLearningRequest.learningRegister = new LearningRegisterRequest()
    studentSchemeLearningRequest.studentLearning = new StudentLearningRequest()
    studentSchemeLearningRequest.learningRegister.batchOrderNoList = [batchOrderNo]
    studentSchemeLearningRequest.learningRegister.subOrderNoList = subOrderNoList ? [subOrderNoList] : undefined
    studentSchemeLearningRequest.learningRegister.status = [1]
    studentSchemeLearningRequest.studentLearning.courseScheduleStatus = 2 //学习完成
    const learningToCompleteCount = await this.isFxForceMethods(page, studentSchemeLearningRequest)
    studentSchemeLearningRequest.studentLearning.courseScheduleStatus = undefined
    studentSchemeLearningRequest.studentLearning.trainingResultList = [1] //培训结果
    const trainingResultsCount = await this.isFxForceMethods(page, studentSchemeLearningRequest)
    const result = []
    if (learningToCompleteCount !== 0 || trainingResultsCount !== 0) {
      if (learningToCompleteCount !== 0) {
        result.push(2)
      }
      if (trainingResultsCount !== 0) {
        result.push(1)
      }
    } else {
      result.push(0)
    }
    return result
  }
  /**
   * 学习完成 判断 （分销）
   */
  private async isFxForceMethods(page: Page, request: StudentSchemeLearningRequest) {
    const { data } = await SchemeLearnind.pageStudentSchemeLearningInDistributor({ page, request })
    return data.currentPageData.length
  }

  /**
   * 【集体报名退款】导出批次退货单
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<BatchRefoundListDetailVo[]>} - 【集体报名订单】列表
   */
  async exportBatchReturnOrderExcelInServicer(
    queryParams: QueryBatchRefundListParamVo,
    sortRequest?: Array<ReturnSortRequest>
  ) {
    const batchReturnOrderRequest = QueryBatchRefundListParamVo.to(queryParams)
    if (queryParams.buyerName || queryParams.buyerAccount) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const collectiveManagerQueryIdVo = new CollectiveManagerQueryIdVo()
      collectiveManagerQueryIdVo.userName = queryParams.buyerName
      collectiveManagerQueryIdVo.idCard = queryParams.buyerAccount
      const idList = await queryStudentIdList.queryCollectiveManagerIdList(collectiveManagerQueryIdVo)
      if (idList.length == 0) {
        batchReturnOrderRequest.batchOrderInfo.creatorIdList = ['-1']
      } else {
        batchReturnOrderRequest.batchOrderInfo.creatorIdList = idList
      }
    }
    const request = {
      request: batchReturnOrderRequest,
      sortRequest
    }
    const response = await DataExportBackstage.exportBatchReturnOrderExcelInServicer(request)
    return response
  }
  /**
   * 【集体报名退款】导出批次退货单（分销）
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<BatchRefoundListDetailVo[]>} - 【集体报名订单】列表
   */
  async exportFxBatchReturnOrderExcelInServicer(
    queryParams: QueryBatchRefundListParamVo,
    sortRequest?: Array<ReturnSortRequest>
  ) {
    const batchReturnOrderRequest = QueryBatchRefundListParamVo.to(queryParams)
    if (queryParams.buyerName || queryParams.buyerAccount) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const collectiveManagerQueryIdVo = new CollectiveManagerQueryIdVo()
      collectiveManagerQueryIdVo.userName = queryParams.buyerName
      collectiveManagerQueryIdVo.idCard = queryParams.buyerAccount
      const idList = await queryStudentIdList.queryCollectiveManagerIdList(collectiveManagerQueryIdVo)
      if (idList.length == 0) {
        batchReturnOrderRequest.batchOrderInfo.creatorIdList = ['-1']
      } else {
        batchReturnOrderRequest.batchOrderInfo.creatorIdList = idList
      }
    }
    const request = {
      request: batchReturnOrderRequest,
      sortRequest
    }
    const response = await fxnlQuery.exportBatchReturnOrderExcelInDistributor(request)
    return response
  }

  /**
   * 【集体报名退款】导出批次退货单明细
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<BatchRefoundListDetailVo[]>} - 【集体报名订单】列表
   */
  async exportBatchReturnOrderDetailExcelInServicer(
    queryParams: QueryBatchRefundListParamVo,
    sortRequest?: Array<ReturnSortRequest>
  ) {
    const batchReturnOrderRequest = QueryBatchRefundListParamVo.to(queryParams)
    if (queryParams.buyerName || queryParams.buyerAccount) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const collectiveManagerQueryIdVo = new CollectiveManagerQueryIdVo()
      collectiveManagerQueryIdVo.userName = queryParams.buyerName
      collectiveManagerQueryIdVo.idCard = queryParams.buyerAccount
      const idList = await queryStudentIdList.queryCollectiveManagerIdList(collectiveManagerQueryIdVo)
      if (idList.length === 0) {
        batchReturnOrderRequest.batchOrderInfo.creatorIdList = ['-1']
      } else {
        batchReturnOrderRequest.batchOrderInfo.creatorIdList = idList
      }
    }
    const request = {
      request: batchReturnOrderRequest,
      sortRequest
    }
    const response = await DataExportBackstage.exportBatchReturnOrderDetailExcelInServicer(request)
    return response
  }
  /**
   * 【集体报名退款】导出批次退货单明细（分销）
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<BatchRefoundListDetailVo[]>} - 【集体报名订单】列表
   */
  async exportFxBatchReturnOrderDetailExcelInServicer(
    queryParams: QueryBatchRefundListParamVo,
    sortRequest?: Array<ReturnSortRequest>
  ) {
    const batchReturnOrderRequest = QueryBatchRefundListParamVo.to(queryParams)
    if (queryParams.buyerName || queryParams.buyerAccount) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const collectiveManagerQueryIdVo = new CollectiveManagerQueryIdVo()
      collectiveManagerQueryIdVo.userName = queryParams.buyerName
      collectiveManagerQueryIdVo.idCard = queryParams.buyerAccount
      const idList = await queryStudentIdList.queryCollectiveManagerIdList(collectiveManagerQueryIdVo)
      if (idList.length === 0) {
        batchReturnOrderRequest.batchOrderInfo.creatorIdList = ['-1']
      } else {
        batchReturnOrderRequest.batchOrderInfo.creatorIdList = idList
      }
    }
    const request = {
      request: batchReturnOrderRequest,
      sortRequest
    }
    const response = await fxnlQuery.exportBatchReturnOrderDetailExcelInDistributor(request)
    return response
  }

  /**
   * 【集体报名退款】查询列表
   * @param {Page} page - 分页参数
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<BatchRefoundListDetailVo[]>} - 【集体报名订单】列表
   */
  async queryBatchRefoundList(
    page: Page,
    queryParams: QueryBatchRefundListParamVo,
    sortRequest?: Array<BatchReturnOrderSortRequest>
  ): Promise<BatchRefoundListDetailVo[]> {
    //
    const batchReturnOrderRequest = QueryBatchRefundListParamVo.to(queryParams)
    if (queryParams.createdUserId.length) {
      batchReturnOrderRequest.batchOrderInfo.creatorIdList = queryParams.createdUserId
    } else if (queryParams.buyerName || queryParams.buyerAccount) {
      //
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const collectiveManagerQueryIdVo = new CollectiveManagerQueryIdVo()
      collectiveManagerQueryIdVo.userName = queryParams.buyerName
      collectiveManagerQueryIdVo.identity = queryParams.buyerAccount
      const idList = await queryStudentIdList.queryCollectiveManagerIdList(collectiveManagerQueryIdVo)
      if (!idList?.length) {
        page.totalPageSize = 0
        page.totalSize = 0
        return []
      }
      batchReturnOrderRequest.batchOrderInfo.creatorIdList = idList
    }
    const request = {
      page,
      request: batchReturnOrderRequest,
      sortRequest
    }
    const response = await TradeQuery.pageBatchReturnOrderInServicer(request)
    // 获取批次退货单总数量、退款总金额  不使用同步执行，避免影响获取列表
    const statisticStatus = this.queryBatchRefoundListStatistic(queryParams)
    page.totalSize = response.data.totalSize
    page.totalPageSize = response.data.totalPageSize
    const list = new Array<BatchRefoundListDetailVo>()
    const userIdList = []
    for (let i = 0; i < response.data.currentPageData.length; i++) {
      const element = response.data.currentPageData[i]
      userIdList.push(element.batchOrderInfo.creator.userId)
      list.push(BatchRefoundListDetailVo.from(element))
    }
    const userInfoMap = await this.getUserInfo(userIdList)
    for (let i = 0; i < list.length; i++) {
      const element = list[i]
      if (userInfoMap.has(element.buyerId)) {
        const userInfo = userInfoMap.get(element.buyerId)
        element.buyerName = userInfo.userName
        element.buyerAccount = userInfo.idCard || userInfo.phone
      }
    }
    return list
  }

  /**
   * 【分销集体报名退款】查询列表
   * @param {Page} page - 分页参数
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<BatchRefoundListDetailVo[]>} - 【集体报名订单】列表
   */
  async queryFxBatchRefoundList(
    page: Page,
    queryParams: QueryBatchRefundListParamVo,
    sortRequest?: Array<BatchReturnOrderSortRequest>
  ): Promise<BatchRefoundListDetailVo[]> {
    //
    const batchReturnOrderRequest = QueryBatchRefundListParamVo.to(queryParams)
    if (queryParams.createdUserId.length) {
      batchReturnOrderRequest.batchOrderInfo.creatorIdList = queryParams.createdUserId
    } else if (queryParams.buyerName || queryParams.buyerAccount) {
      //
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const collectiveManagerQueryIdVo = new CollectiveManagerQueryIdVo()
      collectiveManagerQueryIdVo.userName = queryParams.buyerName
      collectiveManagerQueryIdVo.identity = queryParams.buyerAccount
      const idList = await queryStudentIdList.queryCollectiveManagerIdList(collectiveManagerQueryIdVo)
      if (!idList?.length) {
        page.totalPageSize = 0
        page.totalSize = 0
        return []
      }
      batchReturnOrderRequest.batchOrderInfo.creatorIdList = idList
    }
    const request = {
      page,
      request: batchReturnOrderRequest,
      sortRequest
    }
    const response = await TradeQuery.pageBatchReturnOrderInDistributor(request)
    // 获取批次退货单总数量、退款总金额  不使用同步执行，避免影响获取列表
    const statisticStatus = this.queryFxBatchRefoundListStatistic(queryParams)
    page.totalSize = response.data.totalSize
    page.totalPageSize = response.data.totalPageSize
    const list = new Array<BatchRefoundListDetailVo>()
    const userIdList = []
    for (let i = 0; i < response.data.currentPageData.length; i++) {
      const element = response.data.currentPageData[i]
      userIdList.push(element.batchOrderInfo.creator.userId)
      list.push(BatchRefoundListDetailVo.from(element))
    }
    const userInfoMap = await this.getUserInfo(userIdList)
    for (let i = 0; i < list.length; i++) {
      const element = list[i]
      if (userInfoMap.has(element.buyerId)) {
        const userInfo = userInfoMap.get(element.buyerId)
        element.buyerName = userInfo.userName
        element.buyerAccount = userInfo.idCard || userInfo.phone
      }
    }
    return list
  }

  /**
   * 【集体报名退款订单】查询列表统计数据 statisticBatchReturnOrderInServicer
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<ResponseStatus>}
   */
  async queryBatchRefoundListStatistic(queryParams: QueryBatchRefundListParamVo): Promise<ResponseStatus> {
    const { data, status } = await TradeQuery.statisticBatchReturnOrderInServicer(
      QueryBatchRefundListParamVo.to(queryParams)
    )
    this.totalRefoundCount = data?.totalBatchReturnOrderCount
    this.totalRefoundAmount = data?.totalBatchReturnOrderRefundAmount
    return status
  }

  /**
   * 【分销集体报名退款订单】查询列表统计数据 statisticBatchReturnOrderInServicer
   * @param {QueryBatchRefundListParamVo} queryParams - 查询参数
   * @return {Promise<ResponseStatus>}
   */
  async queryFxBatchRefoundListStatistic(queryParams: QueryBatchRefundListParamVo): Promise<ResponseStatus> {
    const { data, status } = await TradeQuery.statisticBatchReturnOrderInDistributor(
      QueryBatchRefundListParamVo.to(queryParams)
    )
    this.totalRefoundCount = data?.totalBatchReturnOrderCount
    this.totalRefoundAmount = data?.totalBatchReturnOrderRefundAmount
    return status
  }

  /**
   * 【集体报名退款订单】查看详情 getBatchReturnOrderInServicer
   * @param {string} batchOrderNo - 批次单id
   * @return {Promise<BatchRefoundDetailVo>}
   */
  async queryBatchRefoundDetail(batchOrderNo: string): Promise<BatchRefoundDetailVo> {
    const { data } = await TradeQuery.getBatchReturnOrderInServicer(batchOrderNo)
    const batchRefoundDetailVo = BatchRefoundDetailVo.from(data)
    const userMap = await this.getUserInfo([data?.batchOrderInfo?.creator?.userId])
    const userInfo = userMap.get(data?.batchOrderInfo?.creator?.userId)
    batchRefoundDetailVo.name = userInfo.userName
    batchRefoundDetailVo.idCard = userInfo.idCard || userInfo.phone
    return batchRefoundDetailVo
  }

  /**
   * 【集体报名退款订单】查看详情 getBatchReturnOrderInServicer （分销）
   * @param {string} batchOrderNo - 批次单id
   * @return {Promise<BatchRefoundDetailVo>}
   */
  async queryFxBatchRefoundDetail(batchOrderNo: string): Promise<BatchRefoundDetailVo> {
    const { data } = await TradeQuery.getBatchReturnOrderInDistributor(batchOrderNo)
    const batchRefoundDetailVo = BatchRefoundDetailVo.from(data)
    const userMap = await this.getUserInfo([data?.batchOrderInfo?.creator?.userId])
    const userInfo = userMap.get(data?.batchOrderInfo?.creator?.userId)
    batchRefoundDetailVo.name = userInfo.userName
    batchRefoundDetailVo.idCard = userInfo.idCard || userInfo.phone
    return batchRefoundDetailVo
  }
}

export default QueryBatchRefundList
