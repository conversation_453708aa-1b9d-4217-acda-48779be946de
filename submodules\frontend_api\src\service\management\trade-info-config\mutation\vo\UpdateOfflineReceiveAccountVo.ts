import { ReceiveAccountExtProperty, UpdateReceiveAccountRequest } from '@api/ms-gateway/ms-trade-configuration-v1'
import {
  OfflineEncryptionKeyDataResponse,
  ReceiveAccountConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import UpdateReceiveAccountVo from './UpdateReceiveAccountVo'

class UpdateOfflineReceiveAccountVo extends UpdateReceiveAccountVo {
  /**
   * 开户银行
   */
  depositBank = ''
  /**
   * 开户户名
   */
  merchantName = ''
  /**
   * 柜台号
   */
  counterNumber = ''

  from(res: ReceiveAccountConfigResponse) {
    this.id = res.id
    this.accountName = res.name
    this.accountNo = res.accountNo
    this.accountType = res.accountType
    // this.merchantName = res.merchantName
    this.refundWay = res.returnType
    this.taxPayerId = res.taxPayerId
    this.paymentChannelId = res.paymentChannelId
    if (res.encryptionKeyData.encryptionKeyType === 'offlinePay') {
      const temp = res.encryptionKeyData as OfflineEncryptionKeyDataResponse
      this.depositBank = temp.depositBank
      this.counterNumber = temp.counterNumber
      this.merchantName = temp.merchantName
    }
  }

  to() {
    const updateReceiveAccountRequest = new UpdateReceiveAccountRequest()
    updateReceiveAccountRequest.receiveAccountId = this.id
    updateReceiveAccountRequest.name = this.accountName
    // 迁移至扩展列表中
    // updateReceiveAccountRequest.merchantName = this.merchantName
    updateReceiveAccountRequest.refundWay = 2
    updateReceiveAccountRequest.taxPayerId = this.taxPayerId
    updateReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    this.updateProperties('depositBank', this.depositBank, updateReceiveAccountRequest.properties)
    this.updateProperties('counterNumber', this.counterNumber, updateReceiveAccountRequest.properties)
    this.updateProperties('merchantName', this.merchantName, updateReceiveAccountRequest.properties)
    return updateReceiveAccountRequest
  }

  private updateProperties(propertyName: string, propertyValue: string, properties: Array<ReceiveAccountExtProperty>) {
    const property = properties?.find(item => item.name === propertyName)
    if (property) {
      property.value = propertyValue
    } else {
      const item = new ReceiveAccountExtProperty()
      item.name = propertyName
      item.value = propertyValue
      properties.push(item)
    }
    return properties
  }
}

export default UpdateOfflineReceiveAccountVo
