<template>
  <div class="f-p15">
    <el-tabs
      v-if="$hasPermission('handleAction')"
      desc="活动管理"
      actions="created,handleSchemeChange"
      v-model="activeName2"
      type="card"
      class="m-tab-card"
    >
      <!-- 学习心得 -->
      <el-tab-pane label="学习心得" name="first">
        <el-card shadow="never" class="m-card f-mb15">
          <el-row :gutter="16" class="m-query is-border-bottom">
            <el-form :inline="true" label-width="96px">
              <el-col :sm="14" :md="9" :xl="7">
                <el-form-item label="培训方案名称">
                  <biz-learning-scheme-select
                    v-model="schemeObj"
                    @change="handleSchemeChange"
                  ></biz-learning-scheme-select>
                </el-form-item>
              </el-col>
              <el-col :sm="10" :md="7" :xl="5">
                <el-form-item label="主题">
                  <div @click="handleTheme">
                    <el-select
                      ref="themeSelect"
                      v-model="queryParams.theme"
                      clearable
                      filterable
                      placeholder="请选择主题"
                      @visible-change="handleTheme"
                    >
                      <template v-if="themeList.length">
                        <el-option
                          v-for="item in themeList"
                          :key="item.topicId"
                          :label="item.experienceTopicName"
                          :value="item.topicId"
                        ></el-option>
                      </template>
                    </el-select>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="4">
                <el-form-item label="学习心得类型">
                  <el-select v-model="queryParams.learningExperienceType" clearable filterable placeholder="全部">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="班级心得" :value="1"></el-option>
                    <el-option label="课程心得" :value="2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <template v-if="filterFlag">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="提交时间">
                    <double-date-picker
                      :begin-create-time.sync="queryParams.joinStartTime"
                      :end-create-time.sync="queryParams.joinEndTime"
                    ></double-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="姓名">
                    <el-input v-model="queryParams.name" clearable placeholder="请输入姓名" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="证件号">
                    <el-input v-model="queryParams.idCard" clearable placeholder="请输入证件号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="4">
                  <el-form-item label="作答方式">
                    <el-select v-model="queryParams.answerMethod" clearable filterable placeholder="全部">
                      <el-option label="全部" value=""></el-option>
                      <el-option label="提交附件" :value="1"></el-option>
                      <el-option label="在线编辑" :value="2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="4">
                  <el-form-item label="审核方式">
                    <el-select v-model="queryParams.approveMethod" clearable filterable placeholder="全部">
                      <el-option label="全部" value=""></el-option>
                      <el-option label="提交自动通过" :value="1"></el-option>
                      <el-option label="人工审核" :value="2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col v-show="auditStatus == 2" :sm="12" :md="8" :xl="4">
                  <el-form-item label="审核结果">
                    <el-select v-model="queryParams.approveResult" clearable filterable placeholder="全部">
                      <el-option label="全部" value=""></el-option>
                      <el-option label="通过" :value="1"></el-option>
                      <el-option label="不通过" :value="2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </template>
              <el-col :sm="12" :md="8" :xl="8" class="f-fr">
                <el-form-item class="f-tr">
                  <template v-if="$hasPermission('handleSearch')" desc="查询" actions="handleSearch">
                    <el-button type="primary" :loading="loading" @click="handleSearch">查询</el-button>
                  </template>
                  <template v-if="$hasPermission('handleExport')" desc="导出列表数据" actions="handleExport">
                    <el-button @click="handleExport">导出列表数据</el-button>
                  </template>
                  <template v-if="$hasPermission('handleAllExport')" desc="批量导出学习心得" actions="handleAllExport">
                    <el-button @click="handleAllExport">批量导出学习心得</el-button>
                  </template>
                  <el-button @click="handleReset">重置</el-button>
                  <el-button type="text" @click="filterFlag = !filterFlag"
                    >{{ filterFlag ? '收起' : '展开' }}<i class="el-icon-arrow-up el-icon--right"></i
                  ></el-button>
                </el-form-item>
              </el-col>
            </el-form>
            <el-table
              ref="experienceTable"
              stripe
              :data="tableData"
              max-height="500px"
              class="m-table f-mt15"
              v-loading="loading"
            >
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="用户信息" min-width="230" fixed="left" align="center">
                <template slot-scope="{ row }"
                  >姓名：{{ row.name }}
                  <br />
                  证件号：{{ row.idCard }}</template
                >
              </el-table-column>
              <el-table-column label="培训方案名称" min-width="220" align="center">
                <template slot-scope="{ row }">{{ row.schemeName }}</template>
              </el-table-column>
              <el-table-column label="主题" min-width="220" align="center">
                <template slot-scope="{ row }">{{ row.theme }}</template>
              </el-table-column>

              <el-table-column label="学习心得类型" min-width="120" align="center">
                <template slot-scope="{ row }">
                  <span v-if="row.learningExperienceType === LearningExperienceEnum.CLASS">班级心得</span>
                  <span v-if="row.learningExperienceType === LearningExperienceEnum.COURSE">课程心得</span>
                </template>
              </el-table-column>
              <el-table-column label="提交时间" min-width="240" align="center">
                <template slot-scope="{ row }">
                  <span>{{ row.detail.submitTime }}</span>
                </template>
              </el-table-column>
              <el-table-column label="作答形式" min-width="100" align="center">
                <template slot-scope="{ row }">
                  <span v-if="row.answerMethod === AnswerMethodEnum.UPLOAD">提交附件</span>
                  <span v-if="row.answerMethod === AnswerMethodEnum.EDIT">在线编辑</span>
                </template>
              </el-table-column>
              <el-table-column label="审核方式" min-width="120" align="center">
                <template slot-scope="{ row }">
                  <span v-if="row.approveMethod === ApproveMethodEnum.AUTO">提交自动通过</span>
                  <span v-if="row.approveMethod === ApproveMethodEnum.ARTIFICIAL">人工审核</span>
                </template>
              </el-table-column>
              <el-table-column label="审核结果" width="130" align="center">
                <template slot-scope="{ row }">
                  <template v-if="row.approveResult">
                    <span v-if="row.approveResult === ApproveResultEnum.SUCCESS">通过</span>
                    <span v-else-if="row.approveResult === ApproveResultEnum.FAIL">不通过</span>
                    （{{ row.approveResultScore }}）
                  </template>
                  <span v-else>--</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center" fixed="right">
                <template slot-scope="{ row, $index }">
                  <el-button type="text" size="mini" @click="toDetail(row, $index)">{{
                    auditStatus === 2 ? '详情' : '审核'
                  }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-row>
        </el-card>
      </el-tab-pane>
      <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      <!-- 研讨活动暂不做此项-注释了 -->
      <!-- <el-tab-pane label="研讨活动" name="second">
                <el-card shadow="never" class="m-card f-mb15">详见 0202_基础信息配置_栏目设置.vue</el-card>
              </el-tab-pane> -->
    </el-tabs>
    <!-- 导出任务查看 -->
    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务管理-学习心得{{ isAllExport ? '明细' : '列表' }}数据导出</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import ActivityManangerList from '@api/service/management/activity/ActivityManangerList'
  import ActivityManangeFilterModel from '@api/service/management/activity/models/ActivityManangeFilterModel'
  import ActivityManangeItemModel from '@api/service/management/activity/models/ActivityManangeItemModel'
  import {
    LearningExperienceEnum,
    AnswerMethodEnum,
    ApproveMethodEnum,
    ApproveResultEnum
  } from '@api/service/management/activity/enum/ActivityEnum'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/news-date-picker.vue'
  import { UiPage } from '@hbfe/common'
  import {
    StudentLearningExperienceStatus,
    LearningExperienceTopicResponse
  } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
  import Constant from '@api/service/common/models/constant/index'
  class SchemeObj {
    id: string
    scheme: string
    schemeId: string
  }
  @Component({
    components: {
      DoubleDatePicker
    }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.handleLoadData, this.handleLoadData)
    }
    @Ref('themeSelect') themeSelect: any
    @Ref('experienceTable') experienceTable: any
    // 1 待审核 2已审核
    @Prop({
      type: Number,
      default: 1
    })
    auditStatus: number
    @Watch('schemeObj', {
      deep: true
    })
    // 培训方案名称变化
    handleSchemeChange(val: Array<SchemeObj>) {
      if (val.length && val[0].id) {
        this.queryParams.schemeIds = val.map((item: SchemeObj) => {
          return item.id
        })
        this.queryParams.theme = ''
        this.getThemeList()
      } else {
        this.queryParams.schemeIds = []
      }
    }
    schemeObj: Array<SchemeObj> = []
    LearningExperienceEnum = LearningExperienceEnum
    AnswerMethodEnum = AnswerMethodEnum
    ApproveMethodEnum = ApproveMethodEnum
    ApproveResultEnum = ApproveResultEnum
    activeName2 = 'first'
    joinTime: Array<string> = []
    select = ''
    input = ''
    queryParams: ActivityManangeFilterModel = new ActivityManangeFilterModel()
    tableData: Array<ActivityManangeItemModel> = []
    ActivityManangerObj: ActivityManangerList = new ActivityManangerList()
    filterFlag = false
    loading = false
    page: UiPage
    themeList: Array<LearningExperienceTopicResponse> = []
    exportSuccessVisible = false
    Constant = Constant
    isAllExport = false
    async created() {
      this.queryParams.auditStatus =
        this.auditStatus === 1
          ? [StudentLearningExperienceStatus.SUBMITTED]
          : [StudentLearningExperienceStatus.PASS, StudentLearningExperienceStatus.RETURNED]
      // await this.getThemeList()
      await this.handleLoadData()
    }
    // 渲染列表
    async handleLoadData() {
      this.loading = true
      this.ActivityManangerObj.filterItem = this.queryParams
      this.ActivityManangerObj.filterItem.themeId = this.queryParams.theme
      try {
        this.tableData = await this.ActivityManangerObj.queryList(this.page)
      } catch (error) {
        console.log(error, 'error')
        this.$message.error('experienceTable error')
      } finally {
        this.$nextTick(() => {
          console.log('表格处理')
          ;(this.$refs['experienceTable'] as any)?.doLayout()
        })
      }

      this.loading = false
    }

    // 主题提示
    handleTheme(flag: boolean) {
      if (flag && !this.queryParams.schemeIds.length) {
        // 隐藏提示框
        this.themeSelect.blur()
        this.$alert('提示', {
          message: '请先选择一个培训方案',
          confirmButtonText: '我知道了',
          type: 'warning'
        })
      }
    }
    // 查询
    handleSearch() {
      this.page.pageNo = 1
      this.handleLoadData()
    }
    // 重置
    handleReset() {
      this.queryParams = new ActivityManangeFilterModel()
      this.queryParams.auditStatus =
        this.auditStatus === 1
          ? [StudentLearningExperienceStatus.SUBMITTED]
          : [StudentLearningExperienceStatus.PASS, StudentLearningExperienceStatus.RETURNED]
      this.ActivityManangerObj.filterItem = this.queryParams
      this.schemeObj = new Array<SchemeObj>()
      this.handleLoadData()
    }
    // 导出列表数据
    async handleExport() {
      if (!this.queryParams.schemeIds.length) {
        this.$alert('提示', {
          message: '请选择一个培训方案',
          confirmButtonText: '我知道了',
          type: 'warning'
        })
        return
      } else if (!this.queryParams.theme) {
        this.$alert('提示', {
          message: '请选择一个主题',
          confirmButtonText: '我知道了',
          type: 'warning'
        })
        return
      }
      this.ActivityManangerObj.filterItem.themeId = this.queryParams.theme
      const res = await this.ActivityManangerObj.exportList()
      if (res.status.code == 200 && res.data) {
        this.isAllExport = false
        this.exportSuccessVisible = true
        // this.$message.success('导出成功')
      } else {
        this.$message.warning('导出失败')
      }
    }
    /**
     * 导出任务下载
     */
    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: !this.isAllExport ? 'exportStudentLearningExperience' : 'exportStudentLearningExperienceDetail' }
      })
    }
    // 批量导出学习心得
    async handleAllExport() {
      if (!this.queryParams.schemeIds.length) {
        this.$alert('提示', {
          message: '请选择一个培训方案',
          confirmButtonText: '确定',
          type: 'warning'
        })
        return
      } else if (!this.queryParams.theme) {
        this.$alert('提示', {
          message: '请选择一个主题',
          confirmButtonText: '确定',
          type: 'warning'
        })
        return
      }
      this.ActivityManangerObj.filterItem.themeId = this.queryParams.theme
      const res = await this.ActivityManangerObj.exportLearningExpreList()
      console.log(res, 'res')

      if (res.status.code == 200 && res.data) {
        this.isAllExport = true
        this.exportSuccessVisible = true
        // this.$message.success('导出成功')
      } else {
        this.$message.warning('导出失败')
      }
    }
    // 查看详情
    async toDetail(row: ActivityManangeItemModel, index: number) {
      // if (this.auditStatus === 1) {
      //   // 待审核校验心得考核结果
      //   const isPassed = await this.isExperiencePassed(row.qualificationId)
      //   if (isPassed) {
      //     this.$confirm('提示', {
      //       message: '当前学员已完成学习心得考核，是否继续审核当前心得？若选择不审核，则打开下一条心得记录。',
      //       confirmButtonText: '继续审核',
      //       cancelButtonText: '不审核',
      //       type: 'warning'
      //     })
      //       .then(() => {
      //         this.$emit('toDetail', row)
      //       })
      //       .catch(() => {
      //         if (index + 1 == this.page.pageSize) {
      //           // 最后一条 触发分页
      //           if (this.page.pageNo == this.page.totalPageSize) {
      //             // 最后一页的最后一条
      //           }
      //         } else {
      //           // 非最后一条 正常到下一条
      //           this.$emit('toDetail', this.tableData[index + 1])
      //         }
      //       })
      //   } else {
      //     this.$emit('toDetail', row)
      //   }
      // } else {
      // 已审核跳转详情
      this.$emit('toDetail', row)
      // }
    }
    async isExperiencePassed(qualificationId: string) {
      const res = await this.ActivityManangerObj.getStudentLearningExperienceResult(qualificationId)

      if (res === 1) {
        // 学习心得考核通过
        return true
      } else {
        // 学习心得考核还未通过
        return false
      }
    }
    // 获取主题列表
    async getThemeList() {
      this.ActivityManangerObj.filterItem = this.queryParams
      this.themeList = await this.ActivityManangerObj.queryThemeList()
      // this.themeList = []
      const temp: string[] = []
      const indexList: number[] = []
      this.themeList = (this.themeList || []).filter((item, index) => {
        const topicId = item.topicId
        if (!temp.includes(topicId) && topicId) {
          temp.push(topicId)
          indexList.push(index)
        }
        return indexList.includes(index)
      })
    }
  }
</script>
