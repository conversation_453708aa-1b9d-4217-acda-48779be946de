import MsBasicDataQueryBackstage, {
  AccountRequest,
  AdminInfoResponse,
  AdminQueryRequest,
  AdminUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import Certificate, { AllocateCourseTaskQueryRequest } from '@api/ms-gateway/ms-certificate-v1'
import MsDataExportBackstageGateway, {
  JobGroupRequest,
  JobGroupResponse,
  UserJobLogResponse
} from '@api/platform-gateway/fxnl-data-export-gateway-backstage'
import PlatfromExport from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import PlatformCertificate from '@api/platform-gateway/platform-certificate-v1'
import { AsyncTaskJobStatusEnum } from '@api/service/common/enums/async-task/AsyncTaskJobStatusType'
import { Page, Response } from '@hbfe/common'
import AsyncTaskItemVo from './vo/AsyncTaskItemVo'
import AsyncTaskTypeVo from './vo/AsyncTaskTypeVo'
import JobRequestVo from './vo/JobRequestVo'
import OperateUser from './vo/OperateUser'

/**
 * 查询异步任务列表
 */
class QueryExportAsyncTask {
  /**
   * @description: 查询导出任务列表
   * @param {Page} page
   * @param {JobRequestVo} request
   */
  async queryAsyncTaskList(page: Page, request: JobRequestVo): Promise<Response<Array<AsyncTaskItemVo>>> {
    const params = request.toDto()
    const res = await MsDataExportBackstageGateway.pageExportTaskInfoInMyself({ page: page, jobRequest: params })
    const response = new Response<Array<AsyncTaskItemVo>>()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }

    response.status = res.status
    response.data = new Array<AsyncTaskItemVo>()
    page.totalSize = res.data?.totalSize
    page.pageSize = res.data?.pageSize
    const idList = new Array<string>()
    res.data?.currentPageData?.forEach((userJobLogResponse: UserJobLogResponse) => {
      const asyncTaskItemVo = new AsyncTaskItemVo()
      idList.push(userJobLogResponse.operatorUserId)
      asyncTaskItemVo.from(userJobLogResponse)
      response.data?.push(asyncTaskItemVo)
    })
    if (!idList?.length) {
      console.error('操作人id数组为空！')
      return response
    }

    let list: Array<OperateUser>
    if (idList?.length) {
      list = await this.queryOperateNameBySubProject(idList)
    } else {
      list = new Array<OperateUser>()
    }
    if (!list?.length) {
      console.error('查询操作人信息失败！')
    } else {
      response.data?.forEach((far) => {
        list.forEach((sub) => {
          if (far.operatorUserId === sub.id) {
            far.operateUser = sub.name
          }
        })
      })
    }
    return response
  }

  /**
   * 查询导出失败数据
   * * @param mainTaskId  主任务id
   */
  async exportCertificateFailedData(mainTaskId: string): Promise<Response<string>> {
    //
    const res = await PlatformCertificate.exportCertificateFailedData({ mainTaskId })
    const response = new Response<string>()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = res.data
    return response
  }

  /**
   * @description: 根据任务类型关键字查询任务类型
   * @param {string} groupName
   */
  async queryAsyncTaskTypeList(groupName: string): Promise<Response<Array<AsyncTaskTypeVo>>> {
    const params = new JobGroupRequest()
    params.groupName = groupName
    const res = await PlatfromExport.listExportTaskGroupInfoInServicer(params)
    const response = new Response<Array<AsyncTaskTypeVo>>()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = new Array<AsyncTaskTypeVo>()
    res.data.forEach((jobGroupResponse: JobGroupResponse) => {
      const asyncTaskTypeVo = new AsyncTaskTypeVo()
      asyncTaskTypeVo.from(jobGroupResponse)
      response.data.push(asyncTaskTypeVo)
    })
    return response
  }

  /**
   * @description: 根据任务类型关键字查询任务类型--专题管理员
   * @param {string} groupName
   */
  async queryTrainingChannelAsyncTaskTypeList(groupName: string): Promise<Response<Array<AsyncTaskTypeVo>>> {
    const params = new JobGroupRequest()
    params.groupName = groupName
    const res = await PlatfromExport.listExportTaskGroupInfoInTrainingChannel(params)
    const response = new Response<Array<AsyncTaskTypeVo>>()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = new Array<AsyncTaskTypeVo>()
    res.data.forEach((jobGroupResponse: JobGroupResponse) => {
      const asyncTaskTypeVo = new AsyncTaskTypeVo()
      asyncTaskTypeVo.from(jobGroupResponse)
      response.data.push(asyncTaskTypeVo)
    })
    return response
  }

  /**
   * 证明批量打印
   */
  async findTaskExecuteResponsePage(page: Page, request: JobRequestVo): Promise<Array<AsyncTaskItemVo>> {
    const param = new AllocateCourseTaskQueryRequest()
    param.name = request.jobName
    param.endTime = request.endTime
    param.startTime = request.beginTime
    switch (request.jobState) {
      case AsyncTaskJobStatusEnum.EXECUTED:
        param.taskState = 3
        break
      case AsyncTaskJobStatusEnum.EXECUTING:
        param.taskState = 2
        break
      case AsyncTaskJobStatusEnum.FAIL:
        param.taskState = 4
        // 后端说执行失败传任意值 xfc
        break
      //
    }
    const response = await Certificate.findTaskExecuteResponsePage({
      page,
      request: param
    })
    const result = response.data.currentPageData.map((item) => AsyncTaskItemVo.fromTaskExecuteResponse(item))
    page.totalPageSize = response.data.totalPageSize
    page.totalSize = response.data.totalSize
    const ids = result.map((item) => item.operatorUserId)
    let userResult: Array<OperateUser>
    if (ids?.length) {
      userResult = await this.queryOperateNameBySubProject(ids)
    } else {
      userResult = new Array<OperateUser>()
    }
    result.map((item) => {
      userResult.forEach((temp) => {
        if (temp.id === item.operatorUserId) {
          item.operateUser = temp.name
        }
      })
    })
    return result
  }

  /**
   * 根据操作人id查操作人名称
   * @param userIds
   * @private
   */
  private async queryOperateNameById(userIds: Array<string>): Promise<Array<OperateUser>> {
    const page = new Page()
    page.pageNo = 1
    page.pageSize = userIds?.length
    const params = new AdminQueryRequest()
    params.user = new AdminUserRequest()
    params.user.userIdList = new Array<string>()
    params.user.userIdList = [...new Set(userIds)]
    const res = await MsBasicDataQueryBackstage.pageAdminInfoInServicer({ page: page, request: params })
    const result = new Array<OperateUser>()
    if (!res.status?.isSuccess()) {
      return new Array<OperateUser>()
    }
    res.data?.currentPageData?.map((item: AdminInfoResponse) => {
      const user = new OperateUser()
      user.id = item.userInfo?.userId
      user.name = item.userInfo?.userName
      result.push(user)
    })
    return result
  }

  /**
   * 根据操作人id查操作人名称(网校管理员、分销商角色使用)
   * @param userIds
   * @private
   */
  private async queryOperateNameBySubProject(userIds: Array<string>): Promise<Array<OperateUser>> {
    const page = new Page()
    page.pageNo = 1
    page.pageSize = userIds?.length
    const params = new AdminQueryRequest()
    params.account = new AccountRequest()
    params.account.accountTypeList = [1, 2, 3]
    params.user = new AdminUserRequest()
    params.user.userIdList = new Array<string>()
    params.user.userIdList = [...new Set(userIds)]
    const res = await MsBasicDataQueryBackstage.pageAdminInfoInSubProject({ page: page, request: params })
    const result = new Array<OperateUser>()
    if (!res.status?.isSuccess()) {
      return new Array<OperateUser>()
    }
    res.data?.currentPageData?.map((item: AdminInfoResponse) => {
      const user = new OperateUser()
      user.id = item.userInfo?.userId
      user.name = item.userInfo?.userName
      result.push(user)
    })
    return result
  }
}

export default QueryExportAsyncTask
