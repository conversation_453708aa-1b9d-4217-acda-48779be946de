import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/fxnl-query-front-gateway-backstage'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'fxnl-query-front-gateway-backstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum Direction {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum DistributorCommoditySortEnum {
  RECORD_UPDATED_TIME = 'RECORD_UPDATED_TIME',
  RECORD_CREATED_TIME = 'RECORD_CREATED_TIME',
  DISTRIBUTION_END_TIME = 'DISTRIBUTION_END_TIME',
  SALE_CHANNEL_SORT = 'SALE_CHANNEL_SORT'
}
export enum SaleCommoditySortEnum {
  SALE_CHANNEL_SORT = 'SALE_CHANNEL_SORT',
  ISSUE_NUM_SORT = 'ISSUE_NUM_SORT'
}
export enum QueryType {
  AND = 'AND',
  OR = 'OR'
}
export enum BatchOrderSortField {
  BATCH_ORDER_UN_CONFIRMED_TIME = 'BATCH_ORDER_UN_CONFIRMED_TIME',
  BATCH_ORDER_COMMIT_TIME = 'BATCH_ORDER_COMMIT_TIME'
}
export enum BatchReturnOrderSortField {
  CREATED_TIME = 'CREATED_TIME'
}
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum OrderSortField {
  ORDER_NORMAL_TIME = 'ORDER_NORMAL_TIME',
  ORDER_COMPLETED_TIME = 'ORDER_COMPLETED_TIME'
}
export enum ReturnOrderSortField {
  APPLIED_TIME = 'APPLIED_TIME'
}
export enum DistributorRelationSortEnum {
  RELATION_CREATE_TIME = 'RELATION_CREATE_TIME',
  RELATION_START_TIME = 'RELATION_START_TIME',
  RELATION_END_TIME = 'RELATION_END_TIME'
}
export enum ProductDiscountApplySortEnum {
  APPLY_TIME = 'APPLY_TIME',
  PASS_TIME = 'PASS_TIME',
  REFUSE_TIME = 'REFUSE_TIME'
}
export enum QueryWayType {
  ONLY_ME = 'ONLY_ME',
  ONLY_MY_DISTRIBUTOR = 'ONLY_MY_DISTRIBUTOR',
  ALL = 'ALL'
}
export enum SortPolicy1 {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum StudentSchemeLearningSortField {
  REGISTER_TIME = 'REGISTER_TIME',
  SCHEME_YEAR = 'SCHEME_YEAR'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class ObsFileMetaData {
  bizType?: string
  owner?: string
  sign?: string
}

export class ProductConsignmentContractDto {
  productConsignmentContractId?: string
  contractType?: number
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  servicerId?: string
  unitId?: string
  supplierId?: string
  distributorId?: string
  superiorDistributorId?: string
  distributionLevel?: number
  contractStartTime?: string
  contractEndTime?: string
  status?: number
  effectiveStatusChangeTime?: string
  stopStatusChangeTime?: string
  contractDurationType?: number
  signTime?: string
  distributorServicerIdList?: Array<string>
  regionList?: Array<string>
  contractDistributionRegionTreeId?: string
}

export class PortalBannerDto {
  id?: string
  name?: string
  path?: string
  link?: string
  sort?: number
  enable?: boolean
  createdTime?: string
}

export class PortalFriendLinkDto {
  id?: string
  title?: string
  picture?: string
  friendLinkType?: number
  link?: string
  sort?: number
  createdTime?: string
}

export class PortalMenuDto {
  id?: string
  name?: string
  displayName?: string
  parentId?: string
  type?: number
  sourceType?: number
  link?: string
  code?: string
  referenceId?: string
  enable?: boolean
  sort?: number
  createdTime?: string
  updatedTime?: string
}

export class PortalPlateDto {
  id?: string
  name?: string
  displayName?: string
  parentId?: string
  type?: number
  sourceType?: number
  link?: string
  code?: string
  referenceId?: string
  allowChildren?: number
  sort?: number
  createdTime?: string
  updatedTime?: string
}

export class ServicerPortalDto {
  id?: string
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  servicerId?: string
  belongServiceId?: string
  category?: number
  identifier?: string
  portalType?: number
  title?: string
  shortName?: string
  logo?: string
  icon?: string
  themeColor?: string
  mobileQRcode?: string
  CSPhonePicture?: string
  CSPhone?: string
  CSCallTime?: string
  CSOnlineCodeId?: string
  CSWechat?: string
  trainingFlowPicture?: string
  footContentId?: string
  slogan?: string
  domainName?: string
  domainShortCode?: string
  contentId?: string
  isProvideServiceAccount?: number
  isPublished?: number
  publishedTime?: string
  unpublishedTime?: string
  cnzz?: string
  dirName?: string
  domainNameType?: number
  plates?: Array<PortalPlateDto>
  friendLinks?: Array<PortalFriendLinkDto>
  banner?: Array<PortalBannerDto>
  menu?: Array<PortalMenuDto>
  createdTime?: string
  updatedTime?: string
  isDeleted?: number
  deletedTime?: string
  recordCreatedTime?: string
  recordUpdatedTime?: string
}

export class BigDecimalScopeRequest {
  begin?: number
  end?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

export class BatchOrderRequest {
  batchOrderNoList?: Array<string>
  basicData?: BatchOrderBasicDataRequest
  payInfo?: OrderPayInfoRequest
  creatorIdList?: Array<string>
  isInvoiceApplied?: boolean
  distributorId?: string
  portalId?: string
  isDistributionExcludePortal?: boolean
}

export class BatchOrderSortRequest {
  field?: BatchOrderSortField
  policy?: SortPolicy
}

export class BatchOrderBasicDataRequest {
  batchOrderStatusList?: Array<number>
  batchOrderStatusChangeTime?: BatchOrderStatusChangeTimeRequest
  batchOrderPaymentStatusList?: Array<number>
  batchOrderDeliveryStatusList?: Array<number>
  batchOrderAmountScope?: BigDecimalScopeRequest
  saleChannels?: Array<number>
  saleChannelName?: string
  saleChannelIds?: Array<string>
}

export class BatchOrderStatusChangeTimeRequest {
  unConfirmed?: DateScopeRequest
  normal?: DateScopeRequest
  completed?: DateScopeRequest
  closed?: DateScopeRequest
  committing?: DateScopeRequest
  canceling?: DateScopeRequest
}

export class BatchReturnOrderApprovalInfoRequest {
  approveTime?: DateScopeRequest
}

export class BatchReturnOrderRequest {
  batchReturnOrderList?: Array<string>
  basicData?: BatchReturnOrderBasicDataRequest
  approvalInfo?: BatchReturnOrderApprovalInfoRequest
  batchOrderInfo?: BatchOrderInfoRequest
  distributorId?: string
  portalId?: string
  isDistributionExcludePortal?: boolean
}

export class BatchReturnOrderSortRequest {
  field?: BatchReturnOrderSortField
  policy?: SortPolicy
}

export class BatchOrderInfoRequest {
  batchOrderNoList?: Array<string>
  creatorIdList?: Array<string>
  receiveAccountIdList?: Array<string>
  flowNoList?: Array<string>
  paymentOrderTypeList?: Array<number>
}

export class BatchReturnCloseReasonRequest {
  closeTypeList?: Array<number>
}

export class BatchReturnOrderBasicDataRequest {
  batchReturnOrderStatus?: Array<number>
  batchReturnCloseReason?: BatchReturnCloseReasonRequest
  batchReturnStatusChangeTime?: BatchReturnOrderStatusChangeTimeRequest
  refundAmountScope?: BigDecimalScopeRequest
  saleChannels?: Array<number>
  saleChannelName?: string
  saleChannelIds?: Array<string>
}

export class BatchReturnOrderStatusChangeTimeRequest {
  applied?: DateScopeRequest
  returnCompleted?: DateScopeRequest
}

export class CommodityAuthInfoRequest {
  distributorId?: string
  distributionLevel?: number
  superiorDistributorId?: string
  supplierId?: string
  salesmanId?: string
}

export class CommoditySkuRequest {
  commoditySkuIdList?: Array<string>
  saleTitle?: string
  issueInfo?: IssueInfo
  skuProperty?: SkuPropertyRequest1
  externalTrainingPlatform?: Array<string>
  trainingInstitution?: Array<string>
}

export class RegionSkuPropertyRequest1 {
  province?: string
  city?: string
  county?: string
}

export class RegionSkuPropertySearchRequest1 {
  regionSearchType?: number
  region?: Array<RegionSkuPropertyRequest1>
}

export class SkuPropertyRequest1 {
  year?: Array<string>
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest1
  industry?: Array<string>
  subjectType?: Array<string>
  trainingCategory?: Array<string>
  trainingProfessional?: Array<string>
  technicalGrade?: Array<string>
  trainingObject?: Array<string>
  positionCategory?: Array<string>
  jobLevel?: Array<string>
  jobCategory?: Array<string>
  grade?: Array<string>
  subject?: Array<string>
  learningPhase?: Array<string>
  discipline?: Array<string>
  trainingChannelIds?: Array<string>
  certificatesType?: Array<string>
  practitionerCategory?: Array<string>
  qualificationCategory?: Array<string>
  trainingForm?: Array<string>
}

export class IssueInfo {
  issueId?: string
  issueName?: string
  issueNum?: string
  trainStartTime?: string
  trainEndTime?: string
  sourceType?: string
  sourceId?: string
}

export class OrderRequest {
  orderNoList?: Array<string>
  subOrderNoList?: Array<string>
  subOrderReturnStatus?: Array<number>
  orderBasicData?: OrderBasicDataRequest
  subOrderBasicData?: SubOrderBasicDataRequest
  payInfo?: OrderPayInfoRequest
  buyerIdList?: Array<string>
  deliveryCommodity?: CommoditySkuRequest
  currentCommodity?: CommoditySkuRequest
  saleChannel?: number
  saleChannels?: Array<number>
  excludeSaleChannels?: Array<number>
  saleChannelIds?: Array<string>
  saleChannelName?: string
  cardTypeId?: string
  distributorId?: string
  portalId?: string
  orderFixQuery?: OrderFixQueryRequest
  isDistributionExcludePortal?: boolean
  externalTrainingPlatform?: Array<string>
  unitIds?: Array<string>
  issueId?: Array<string>
  policyTrainingSchemeIdList?: Array<string>
  declarationUnitCodeList?: Array<string>
  settlementStatus?: number
  settlementDate?: DateScopeRequest
}

export class OrderSortRequest {
  field?: OrderSortField
  policy?: SortPolicy
}

export class OrderBasicDataRequest {
  orderType?: number
  batchOrderNoList?: Array<string>
  orderStatusList?: Array<number>
  orderPaymentStatusList?: Array<number>
  orderDeliveryStatusList?: Array<number>
  orderStatusChangeTime?: OrderStatusChangeTimeRequest
  channelTypesList?: Array<number>
  excludeChannelTypesList?: Array<number>
  terminalCodeList?: Array<string>
  orderAmountScope?: BigDecimalScopeRequest
}

export class OrderFixQueryRequest {
  excludeChannelTypesList?: Array<number>
  excludeSaleChannels?: Array<number>
}

export class OrderPayInfoRequest {
  receiveAccountIdList?: Array<string>
  flowNoList?: Array<string>
  paymentOrderTypeList?: Array<number>
}

export class OrderStatusChangeTimeRequest {
  normalDateScope?: DateScopeRequest
  completedDatesScope?: DateScopeRequest
}

export class SubOrderBasicDataRequest {
  discountType?: number
  discountSourceId?: string
  useDiscount?: boolean
  commodityAuthInfo?: CommodityAuthInfoRequest
}

export class ReturnOrderRequest {
  unitIdList?: Array<string>
  returnOrderNoList?: Array<string>
  basicData?: ReturnOrderBasicDataRequest
  approvalInfo?: ReturnOrderApprovalInfoRequest
  returnCommoditySkuIdList?: Array<string>
  returnCommodity?: CommoditySkuRequest
  refundCommoditySkuIdList?: Array<string>
  refundCommodity?: CommoditySkuRequest
  subOrderInfo?: SubOrderInfoRequest
  commodityAuthInfo?: CommodityAuthInfoRequest
  distributorId?: string
  portalId?: string
  isDistributionExcludePortal?: boolean
}

export class ReturnSortRequest {
  field?: ReturnOrderSortField
  policy?: SortPolicy
}

export class OrderInfoRequest {
  orderNoList?: Array<string>
  batchOrderNoList?: Array<string>
  buyerIdList?: Array<string>
  receiveAccountIdList?: Array<string>
  flowNoList?: Array<string>
  channelTypesList?: Array<number>
  terminalCodeList?: Array<string>
  saleChannel?: number
  saleChannels?: Array<number>
  saleChannelName?: string
  saleChannelIds?: Array<string>
  policyTrainingSchemeIdList?: Array<string>
  declarationUnitCodeList?: Array<string>
}

export class ReturnCloseReasonRequest {
  closeTypeList?: Array<number>
}

export class ReturnOrderApprovalInfoRequest {
  approveTime?: DateScopeRequest
}

export class ReturnOrderBasicDataRequest {
  returnOrderStatus?: Array<number>
  returnOrderTypes?: Array<number>
  applySourceType?: string
  applySourceIdList?: Array<string>
  returnCloseReason?: ReturnCloseReasonRequest
  returnStatusChangeTime?: ReturnOrderStatusChangeTimeRequest
  refundAmountScope?: BigDecimalScopeRequest
}

export class ReturnOrderStatusChangeTimeRequest {
  applied?: DateScopeRequest
  returnCompleted?: DateScopeRequest
}

export class SubOrderInfoRequest {
  subOrderNoList?: Array<string>
  orderInfo?: OrderInfoRequest
  discountType?: number
  useDiscount?: boolean
}

/**
 * <AUTHOR> linq
@date : 2025-04-15 14:57
@description : 查询可更换的分销期别列表request入参
 */
export class DistributorIssueCommodityRequest {
  /**
   * 原始商品id集合
   */
  rootCommodityIdList?: Array<string>
  /**
   * 排除的期别商品id集合
   */
  excludedIssueCommodityIdList?: Array<string>
  /**
   * 期别名称（模糊查询）
   */
  issueName?: string
  /**
   * 期别名称（精确查询）
   */
  issueNameMatch?: string
  /**
   * 期别报名时间
   */
  issueRegisterTime?: DateScopeRequest
  /**
   * 期别培训时间
   */
  issueTrainingTime?: DateScopeRequest
}

/**
 * 简略资讯查询条件
 */
export class NewsSimpleQueryRequest {
  /**
   * 资讯标题
   */
  title?: string
  /**
   * 资讯分类编号
   */
  necId?: string
  /**
   * 发布地区编码
   */
  areaCodePath?: string
  /**
   * 弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]
   */
  popUpsStatus: number
  /**
   * 资讯状态 [-1-全部，0-草稿，1-发布 默认全部]
   */
  status: number
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 门户标识
   */
  identifier?: string
}

/**
 * 商品sku属性查询条件
 */
export class PropertyRequest {
  /**
   * 商品skuKey
   */
  propertyKey?: string
  /**
   * 商品skuValue
   */
  propertyValue?: string
}

/**
 * 地区查询请求参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertySearchRequest {
  /**
   * 地区匹配方式
<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
@see RegionSearchType
   */
  regionSearchType?: number
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest>
}

/**
 * 商品sku属性查询参数
<AUTHOR>
@date 2022/01/25
 */
export class SkuPropertyRequest {
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 卫生行业-培训对象
   */
  trainingObject?: Array<string>
  /**
   * 卫生行业-岗位类别
   */
  positionCategory?: Array<string>
  /**
   * 工勤行业-技术等级
   */
  jobLevel?: Array<string>
  /**
   * 工勤行业-工种
   */
  jobCategory?: Array<string>
  /**
   * 年级
   */
  grade?: Array<string>
  /**
   * 科目
   */
  subject?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
  /**
   * 专题id
   */
  trainingChannelIds?: Array<string>
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType?: Array<string>
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory?: Array<string>
  /**
   * 培训形式
   */
  trainingForm?: Array<string>
}

export class DistributionAuthorizationDistributorRequest {
  /**
   * 分销商名称
   */
  distributorName?: string
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 分销状态
   */
  distributionStatus?: number
  /**
   * 是否授权
   */
  isAuthorized?: boolean
  /**
   * 商品ID
   */
  commodityId?: string
  /**
   * 网校ID
   */
  onlineSchoolId?: string
  /**
   * 合同id
   */
  contractId?: string
  /**
   * 分销等级
   */
  distributorLevel?: number
  /**
   * 合作状态
0 未开始
1 进行中
2 即将到期
3 已结束
   */
  cooperateStatus?: Array<number>
  /**
   * 是否去重
默认去重
false 不去重
true 去重
   */
  isDistinct?: boolean
  /**
   * 父级区域id
   */
  parentRegionId?: string
}

export class DistributionRelationshipRequest {
  /**
   * 分销商名称
   */
  distributorName?: string
}

/**
 * 网校分销Request
 */
export class OnlineSchoolDistributionRelationshipRequest {
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 网校名称
   */
  onlineSchoolName?: string
  /**
   * 域名
   */
  domain?: string
  /**
   * 分销地区
   */
  regionList?: Array<string>
  /**
   * 上级分销商名称
   */
  superDistributorName?: string
  /**
   * 分销关系状态，0代表有效，1代表失效
   */
  status?: number
  /**
   * 分销级别，1代表一级，2代表二级
   */
  distributionLevel?: number
  /**
   * 合同id
   */
  contractIds?: Array<string>
  /**
   * 合作状态
0 未开始
1 进行中
2 即将到期
3 已结束
   */
  cooperateStatus?: Array<number>
  /**
   * 状态和合作状态之间的关系（不传默认AND）
AND 并且
OR  或者
   */
  queryType?: QueryType
}

/**
 * 供应商授权出的商品
 */
export class DistributorCommodityAndRelationRequest {
  /**
   * 分销商商品名称
   */
  saleTitle?: string
  /**
   * 分销商id集合
   */
  distributorIdList?: Array<string>
  /**
   * 分销商等级
   */
  distributorLevel?: number
  /**
   * 销售状态 1-有效 2-无效
   */
  saleStatus?: number
  /**
   * 分销地区路径
   */
  contractDistributionRegionPathList?: Array<string>
  /**
   * 商品sku属性
   */
  propertyList?: Array<PropertyRequest>
  /**
   * 培训方案类型
   */
  schemeTypeList?: Array<string>
  /**
   * 授权价格类型 1-固定 2-区间
   */
  priceType?: number
  /**
   * 分销价格范围查询-最大价格
   */
  maxPrice?: number
  /**
   * 分销价格范围查询-最小价格
   */
  minPrice?: number
  /**
   * 分销价格-最大价格
   */
  policyMaxPrice?: number
  /**
   * 分销价格-最小价格
   */
  policyMinPrice?: number
  /**
   * 定价方案状态
   */
  statusList?: Array<number>
  /**
   * 分销是否有效
0-有效 1-无效
商品的分销开始时间、结束时间作为判断
   */
  distributionStatus?: number
  /**
   * 分销商商品id集合
   */
  distributorCommodityIdList?: Array<string>
  /**
   * 商品id集合
   */
  commodityIdList?: Array<string>
  /**
   * 网校原始商品id集合
   */
  rootCommodityIdList?: Array<string>
  /**
   * 需要排除的网校原始商品id集合
   */
  excludedRootCommodityIdList?: Array<string>
  /**
   * 网校id
   */
  onlineSchoolId?: string
  /**
   * 培训方案名称
   */
  schemeName?: string
  /**
   * 网校销售状态
0-开启 1-关闭
商品的网校销售开始时间、结束时间作为判断
   */
  onlineSchoolStatus?: number
  /**
   * 授权商品来源类型
   */
  commoditySourceTypeList?: Array<number>
  /**
   * 定价方案id
   */
  productPricingSchemeIdList?: Array<string>
  /**
   * 需要排除的定价方案id
   */
  excludedPricingSchemeIdList?: Array<string>
  /**
   * 是否存在定价方案
   */
  existPricingScheme?: boolean
  /**
   * 是否已启用定价方案
   */
  enablePricingScheme?: boolean
  /**
   * 是否已启用优惠申请
   */
  enableDiscountScheme?: boolean
  /**
   * 推广门户标识id
   */
  portalIdentify?: string
  /**
   * 推广门户展示名称
   */
  showName?: string
  /**
   * 推广门户简称
   */
  shortName?: string
  /**
   * 门户域名
   */
  domainName?: string
  /**
   * 门户状态
0-停用 1-启用
   */
  portalStatus?: number
  /**
   * 门户展示 (0-不展示, 1-展示）
   */
  showPortal?: number
  /**
   * 门户推广 (0-不推广, 1-推广）
   */
  portalPromotion?: number
  /**
   * 商品上架状态
0-下架 1-上架
   */
  shelveStatus?: number
  /**
   * 销售渠道类型
0-自营渠道 1-分销渠道 2-专题渠道 3-华医网 4-推广门户渠道
   */
  saleChannelType?: number
  /**
   * 销售渠道id
   */
  saleChannelId?: string
  /**
   * 优惠申请审批状态
0-待处理 1-通过 2-未通过
   */
  auditStatusList?: Array<number>
  /**
   * 优惠状态
1-开启 2-关闭
与优惠申请内容状态有关、与优惠周期约束、优惠开始时间、优惠结束时间有关
   */
  discountStatusList?: Array<number>
}

export class PolicyPortalRequset {
  /**
   * 定价方案id
   */
  productPricingSchemeId?: string
}

export class PortalInfoDetailRequest {
  /**
   * 门户id
   */
  id?: string
}

export class PortalInfoRequest {
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 门户标题
   */
  title?: string
  /**
   * 门户简称
   */
  shortName?: string
  /**
   * 域名
   */
  domainName?: string
  /**
   * 门户状态  是否已发布 1-开启 2-关闭
   */
  isPublished?: boolean
}

export class PortalRequestInSupplier {
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 分销商id
   */
  belongServiceId?: string
  /**
   * 门户展示名称
   */
  title?: string
  /**
   * 门户简称
   */
  shortName?: string
}

/**
 * 学员培训方案学习查询条件
<AUTHOR>
@version 1.0
@date 2022/1/17 11:40
 */
export class StudentSchemeLearningRequestInDistributor {
  /**
   * 学员信息
   */
  student?: UserRequest
  /**
   * 报名信息
   */
  learningRegister?: LearningRegisterRequest
  /**
   * 培训班信息
   */
  scheme?: SchemeRequest
  /**
   * 学习信息
   */
  studentLearning?: StudentLearningRequest
  /**
   * 数据分析信息
   */
  dataAnalysis?: DataAnalysisRequest
  /**
   * 对接管理系统
   */
  connectManageSystem?: ConnectManageSystemRequest
  /**
   * 扩展信息
   */
  extendedInfo?: ExtendedInfoRequest
  /**
   * 方案是否提供培训证明
   */
  openPrintTemplate?: boolean
  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  trainingChannelName?: string
  /**
   * 专题Id  用于不同专题域名 查询对应专题的培训班
   */
  trainingChannelId?: string
  /**
   * 门户id
   */
  portalId?: string
}

/**
 * 学员培训方案学习查询条件
<AUTHOR>
@version 1.0
@date 2022/1/17 11:40
 */
export class StudentSchemeLearningRequestInSupplier {
  /**
   * 学员信息
   */
  student?: UserRequest
  /**
   * 报名信息
   */
  learningRegister?: LearningRegisterRequest
  /**
   * 培训班信息
   */
  scheme?: SchemeRequest
  /**
   * 学习信息
   */
  studentLearning?: StudentLearningRequest
  /**
   * 数据分析信息
   */
  dataAnalysis?: DataAnalysisRequest
  /**
   * 对接管理系统
   */
  connectManageSystem?: ConnectManageSystemRequest
  /**
   * 扩展信息
   */
  extendedInfo?: ExtendedInfoRequest
  /**
   * 方案是否提供培训证明
   */
  openPrintTemplate?: boolean
  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  trainingChannelName?: string
  /**
   * 专题Id  用于不同专题域名 查询对应专题的培训班
   */
  trainingChannelId?: string
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 分销商ID
   */
  distributorId?: string
}

export class ExtendedInfoRequest {
  /**
   * 是否打印
true 打印 false 未打印
   */
  whetherToPrint?: boolean
}

/**
 * 学员方案学习查询排序条件
 */
export class StudentSchemeLearningSortRequest {
  /**
   * 需要排序的字段
   */
  field?: StudentSchemeLearningSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy1
}

/**
 * 学员学习信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class StudentLearningRequest {
  /**
   * 培训结果
<p>
-1:未知，培训尚未完成
1:培训合格
0:培训不合格
   */
  trainingResultList?: Array<number>
  /**
   * 培训结果时间
   */
  trainingResultTime?: DateScopeRequest
  /**
   * 无需学习的学习方式类型
<p>
1: 选课学习方式
2: 考试学习方式
3: 练习学习方式
4：自主学习课程学习方式
   */
  notLearningTypeList?: Array<number>
  /**
   * 课程学习状态（0：未学习 1：学习中 2：学习完成）
   */
  courseScheduleStatus?: number
  /**
   * 考试结果（-1：未考核 0：不合格 1：合格）
   */
  examAssessResultList?: Array<number>
}

/**
 * 地区sku属性查询条件
<AUTHOR>
@version 1.0
@date 2022/2/25 10:55
 */
export class RegionSkuPropertyRequest {
  /**
   * 地区: 省
   */
  province?: string
  /**
   * 地区: 市
   */
  city?: string
  /**
   * 地区: 区县
   */
  county?: string
}

/**
 * 地区匹配查询
<AUTHOR>
@version 1.0
@date 2022/2/25 14:19
 */
export class RegionSkuPropertySearchRequest12 {
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest>
  /**
   * 地区匹配条件
<p>
ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回，如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
PART部分匹配：查询结果返回的地区与查询条件有给值的地区就会返回 如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
   */
  regionSearchType?: number
}

/**
 * 培训方案信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class SchemeRequest {
  /**
   * 培训方案id
   */
  schemeId?: string
  /**
   * 培训方案id
   */
  schemeIdList?: Array<string>
  /**
   * 培训方案类型
<p>
chooseCourseLearning: 选课规则
autonomousCourseLearning: 自主选课
   */
  schemeType?: string
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 培训属性
   */
  skuProperty?: SchemeSkuPropertyRequest
}

/**
 * 培训属性
<AUTHOR>
@version 1.0
@date 2022/1/17 10:22
 */
export class SchemeSkuPropertyRequest {
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest12
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 岗位类别
   */
  positionCategory?: Array<string>
  /**
   * 培训对象
   */
  trainingObject?: Array<string>
  /**
   * 技术等级
   */
  jobLevel?: Array<string>
  /**
   * 工种
   */
  jobCategory?: Array<string>
  /**
   * 科目
   */
  subject?: Array<string>
  /**
   * 年级
   */
  grade?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
}

/**
 * @version: 1.0
@description: 对接管理系统
@author: sugs
@create: 2022-11-15 11:27
 */
export class ConnectManageSystemRequest {
  /**
   * 同步状态
0 未同步
1 已同步
2 同步失败
   */
  syncStatus?: number
}

/**
 * 数据分析信息
<AUTHOR>
@version 1.0
@date 2022/1/20 15:14
 */
export class DataAnalysisRequest {
  /**
   * 成果配置可获得学时
   */
  trainingResultPeriod?: DoubleScopeRequest
  /**
   * 考核要求学时
   */
  requirePeriod?: DoubleScopeRequest
  /**
   * 已获得总学时
   */
  acquiredPeriod?: DoubleScopeRequest
}

/**
 * 学员学习报名信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:08
 */
export class LearningRegisterRequest {
  /**
   * 报名方式
<p>
1:学员自主报名
2:集体报名
3:管理员导入
   */
  registerType?: number
  /**
   * 报名来源类型(ORDER：订单 SUB_ORDER：子订单 EXCHANGE_ORDER：换货单)
   */
  sourceType?: string
  /**
   * 报名来源ID
   */
  sourceId?: string
  /**
   * 学员状态(1:正常 2：冻结 3：失效)
   */
  status?: Array<number>
  /**
   * 报名时间
   */
  registerTime?: DateScopeRequest
  /**
   * 来源订单号
   */
  orderNoList?: Array<string>
  /**
   * 来源子订单号
   */
  subOrderNoList?: Array<string>
  /**
   * 来源批次单号
   */
  batchOrderNoList?: Array<string>
}

/**
 * 地区模型
<AUTHOR>
@version 1.0
@date 2022/2/27 20:01
 */
export class RegionRequest {
  /**
   * 地区：省
   */
  province?: string
  /**
   * 地区：市
   */
  city?: string
  /**
   * 地区：区
   */
  county?: string
}

/**
 * 用户属性
<AUTHOR>
@version 1.0
@date 2022/1/15 11:01
 */
export class UserPropertyRequest {
  /**
   * 所属地区路径
   */
  regionList?: Array<RegionRequest>
  /**
   * 工作单位名称
   */
  companyName?: string
  /**
   * 下单地区
   */
  payOrderRegionList?: Array<RegionRequest>
}

/**
 * 用户信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:00
 */
export class UserRequest {
  /**
   * 用户id
   */
  userIdList?: Array<string>
  /**
   * 账户id
   */
  accountIdList?: Array<string>
  /**
   * 用户属性
   */
  userProperty?: UserPropertyRequest
}

export class ProductDiscountApplyRequest {
  /**
   * 网校id
   */
  onlineSchoolId?: string
  /**
   * 分销商商品名称
   */
  saleTitle?: string
  /**
   * 培训方案名称
   */
  schemeName?: string
  /**
   * 培训方案类型
   */
  schemeTypeList?: Array<string>
  /**
   * 商品sku属性(后端自用)
   */
  propertyList?: Array<PropertyRequest>
  /**
   * 商品sku属性查询
   */
  skuPropertyRequest?: SkuPropertyRequest
  /**
   * 分销商id集合
   */
  distributorIdList?: Array<string>
  /**
   * 分销商等级
   */
  distributorLevel?: number
  /**
   * 优惠申请审批状态
0-待处理 1-通过 2-未通过
   */
  auditStatusList?: Array<number>
  /**
   * 优惠状态
1-开启 2-关闭
与优惠申请内容状态有关、与优惠周期约束、优惠开始时间、优惠结束时间有关
   */
  discountStatusList?: Array<number>
  /**
   * 分销商品状态
0-开启 1-关闭
   */
  distributionStatus?: number
  /**
   * 商品上架状态
0-下架 1-上架
   */
  shelveStatus?: number
  /**
   * 销售渠道id
   */
  saleChannelId?: string
}

/**
 * 业务员对接的分销商关系
 */
export class DockingSalesmanRelationshipRequest {
  /**
   * 业务员ID
   */
  salesmanId?: string
  /**
   * 网校名称
   */
  onlineSchoolName?: string
  /**
   * 网校域名
   */
  onlineSchoolDomainName?: string
  /**
   * 分销商名称
   */
  distributorName?: string
  /**
   * 来源合同状态 0-有效 1-终止
   */
  status?: number
  /**
   * 来源合同的分销地区路径(/省/市/区)list
   */
  regionList?: Array<string>
  /**
   * 对接开始时间排序方式
0-倒序 1-正序
   */
  dockingStartTimeSortType?: number
}

export class SalesmanRequest {
  /**
   * 业务员ID
   */
  salesmanId?: string
  /**
   * 姓名
   */
  name?: string
  /**
   * 手机号
   */
  phoneNumber?: string
  /**
   * 状态：1-启用，2-禁用
   */
  status?: string
}

/**
 * 交易统计请求参数
<AUTHOR>
 */
export class StatisticTradeRecordRequest {
  /**
   * 分销商id集合
   */
  distributorIdList?: Array<string>
  /**
   * 供应商id集合
   */
  supplierIdList?: Array<string>
  /**
   * 商品名称 模糊匹配
   */
  commodityName?: string
  /**
   * 网校id集合 List
   */
  onlineSchoolList?: Array<string>
  /**
   * 商品sku属性
   */
  propertyList?: Array<PropertyRequest>
  /**
   * 商品ID集合
   */
  commodityIdList?: Array<string>
  /**
   * 排除商品ID集合
   */
  excludeCommodityIdList?: Array<string>
  /**
   * 商品售价范围
   */
  commodityPriceScope?: DoubleScopeRequest
  /**
   * 查询时间范围
   */
  queryDateScope?: DateScopeRequest
  /**
   * 查询方式
仅查询自己
只查询下级分销商
包含自己和下级分销商
@see QueryWayType
   */
  queryWayType?: QueryWayType
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 是否是推广门户的数据 | true.是 false.否
   */
  isPortalData?: boolean
  jobName?: string
  metaData?: ObsFileMetaData
}

/**
 * @version: 1.0
@description: 分销关系排序参数
@author: sugs
@create: 2023-12-28 19:29
 */
export class DistributionRelationshipSortRequest {
  /**
   * 排序字段
   */
  field?: DistributorRelationSortEnum
  /**
   * 正序或倒序
   */
  policy?: Direction
}

export class DistributorCommoditySortRequest {
  /**
   * 排序字段
   */
  field?: DistributorCommoditySortEnum
  /**
   * 正序或倒序
   */
  policy?: Direction
}

export class ProductDiscountApplySortRequest {
  /**
   * 排序字段
   */
  field?: ProductDiscountApplySortEnum
  /**
   * 正序或倒序
   */
  policy?: Direction
}

export class SaleCommoditySortRequest {
  /**
   * 排序字段
   */
  field?: SaleCommoditySortEnum
  /**
   * 正序或倒序
   */
  policy?: Direction
}

export class ChannelInfoDto {
  purchaseChannel: number
  payTypeStatisticResponseList: Array<PaymentInfoDto>
}

export class PaymentInfoDto {
  payType: number
  statisticInfo: ReportNumStatisticDto
}

export class ReportNumStatisticDto {
  tradeSuccessCount: number
  returnCount: number
  exchangeInCount: number
  exchangeOutCount: number
  netTradeSuccessCount: number
}

export class ServicerAttachResponse1 {
  fileName: string
  filePath: string
}

/**
 * <AUTHOR> linq
@date : 2025-04-15 15:03
@description : 查询可更换的分销期别列表response返回值
 */
export class DistributorIssueCommodityResponse {
  /**
   * 期别商品信息
   */
  distributorIssueCommodityInfoResponse: NestDistributorIssueCommodityInfoResponse
  /**
   * 期别资源信息
   */
  issueResourceInfo: NestIssueResourceResponse
  /**
   * 剩余报名人数
   */
  remainingRegisterNumber: number
}

/**
 * <AUTHOR> linq
@date : 2025-04-15 15:27
@description : 购买渠道配置
 */
export class NestCommodityPurchaseChannelConfigResponse {
  /**
   * 用户自主购买
   */
  customerPurchase: NestPurchaseChannelConfigResponse
  /**
   * 集体缴费
   */
  collectivePurchase: NestPurchaseChannelConfigResponse
  /**
   * 管理员导入
   */
  administratorImport: NestPurchaseChannelConfigResponse
  /**
   * 集体报名个人缴费渠道
   */
  collectiveSignUpPersonalPay: NestPurchaseChannelConfigResponse
}

/**
 * <AUTHOR> linq
@date : 2025-04-15 15:26
@description : 分销期别商品信息
 */
export class NestDistributorIssueCommodityInfoResponse {
  /**
   * 商品所有渠道的配置信息
   */
  commodityPurchaseChannelConfig: NestCommodityPurchaseChannelConfigResponse
}

/**
 * <AUTHOR> linq
@date : 2025-04-15 15:11
@description : 冗余期别资源信息
 */
export class NestIssueResourceResponse {
  /**
   * 期别id
   */
  issueId: string
  /**
   * 期别编号
   */
  issueNum: string
  /**
   * 期别名称
   */
  issueName: string
  /**
   * 期别报名开始时间
   */
  issueSignUpBeginDate: string
  /**
   * 期别报名结束时间
   */
  issueSignUpEndDate: string
  /**
   * 期别培训开始时间
   */
  issueTrainingBeginDate: string
  /**
   * 期别培训结束时间
   */
  issueTrainingEndDate: string
}

/**
 * <AUTHOR> linq
@date : 2025-04-15 15:26
@description : 购买渠道配置
 */
export class NestPurchaseChannelConfigResponse {
  /**
   * 是否开启可见
   */
  couldSee: boolean
  /**
   * 是否开启可购买
   */
  couldBuy: boolean
}

/**
 * <AUTHOR> linq
@date : 2024-08-22 11:01
@description：推广门户线下集体报名配置
 */
export class OfflineRegistrationConfigResponse {
  /**
   * 推广门户线下集体报名入口开关
   */
  openEntrySwitch: boolean
}

/**
 * 附件
 */
export class AttachesResponse {
  /**
   * id
   */
  id: string
  /**
   * 附件名称
   */
  fileName: string
  /**
   * 附件路径
   */
  filePath: string
}

/**
 * <AUTHOR> linq
@date : 2024-09-13 15:33
@description：授权商品商品属性
 */
export class CommodityPropertyInfoResponse {
  /**
   * 分销商商品上下架状态
0-已下架 1-已上架
   */
  shelveStatus: number
}

/**
 * <AUTHOR> linq
@date : 2024-09-13 15:32
@description：授权商品方案属性
 */
export class CommoditySchemeResourceResponse {
  /**
   * 培训方案类型
   */
  schemeType: string
}

/**
 * 商品sku属性
<AUTHOR>
 */
export class CommoditySkuPropertyResponse {
  /**
   * 商品sku属性id
   */
  commoditySkuPropertyId: string
  /**
   * 商品sku属性名
   */
  commoditySkuPropertyName: string
  /**
   * 商品sku属性值集合
   */
  commoditySkuPropertyValueList: Array<CommoditySkuPropertyValueResponse>
}

/**
 * 商品sku属性值
<AUTHOR>
 */
export class CommoditySkuPropertyValueResponse {
  /**
   * 商品sku属性值id
   */
  commoditySkuPropertyValueId: string
  /**
   * 商品sku属性值展示名
   */
  commoditySkuPropertyValueName: string
  /**
   * 父级code
   */
  parentCode: string
}

/**
 * 商品sku地区属性节点
<AUTHOR>
 */
export class CommoditySkuRegionPropertyNodeResponse {
  /**
   * 商品sku地区属性id
   */
  commoditySkuRegionId: string
  /**
   * 地区编码
   */
  code: string
  /**
   * 地区名
   */
  name: string
  /**
   * 地区等级
   */
  level: number
  /**
   * 地区路径
   */
  codePath: string
  /**
   * 父级地区编码
   */
  parentCode: string
  /**
   * 下级商品sku地区属性
   */
  subRegionList: Array<CommoditySkuRegionPropertyNodeResponse>
}

/**
 * 商品sku培训专业属性节点
<AUTHOR>
 */
export class CommoditySkuTrainingProfessionPropertyNodeResponse {
  /**
   * 培训专业ID
   */
  id: string
  /**
   * 培训专业名称
   */
  name: string
  /**
   * 培训专业编码
   */
  code: string
  /**
   * 父级专业ID
   */
  parentCode: string
  /**
   * 排序序号
   */
  sort: number
  /**
   * 是否启用
   */
  isAvailable: number
  /**
   * 下级培训专业sku地区属性
   */
  subTrainingProfessionList: Array<CommoditySkuTrainingProfessionPropertyNodeResponse>
}

/**
 * 上下文信息
 */
export class OwnerInfoResponse {
  /**
   * 平台id
   */
  platformId: string
  /**
   * 平台版本id
   */
  platformVersionId: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 子项目id
   */
  subProjectId: string
  /**
   * 单位id
   */
  unitId: string
  /**
   * 服务商id
   */
  servicer: string
}

export class PortalInDistributorResponse {
  /**
   * 门户id
   */
  id: string
  /**
   * 门户简称
   */
  shortName: string
}

export class PropertyResponse {
  /**
   * 商品skuKey
   */
  propertyKey: string
  /**
   * 商品skuKeyName
   */
  propertyKeyName: string
  /**
   * 商品skuValue
   */
  propertyValue: string
  /**
   * 商品skuValue名称
   */
  propertyValueName: string
}

export class RegionResponse {
  /**
   * 地区ID
   */
  regionId: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 省ID
   */
  provideId: string
  /**
   * 市ID
   */
  cityId: string
  /**
   * 区县ID
   */
  countyId: string
  /**
   * 名称
   */
  name: string
  /**
   * 下级地区
   */
  children: Array<RegionResponse>
}

/**
 * 地区统计
 */
export class RegionStatisticsResponse {
  /**
   * 地区信息
   */
  response: RegionResponse
  /**
   * 统计数量
   */
  total: number
}

export class RegionTreeResponse {
  /**
   * 地区ID
   */
  regionId: string
  /**
   * 名称
   */
  name: string
  /**
   * 地区全路径
   */
  regionPath: string
  /**
   * 子地区
   */
  children: Array<RegionTreeResponse>
  /**
   * 子地区数量
   */
  childrenCount: number
}

/**
 * 商品-培训方案资源类型
 */
export class SchemeResourceInfoResponse {
  /**
   * 培训方案id
   */
  schemeId: string
  /**
   * 培训方案类型
   */
  schemeType: string
  /**
   * 培训方案名称
   */
  schemeName: string
  /**
   * 学时
   */
  period: string
  /**
   * 报名开始时间
   */
  registerBeginDate: string
  /**
   * 报名结束时间
   */
  registerEndDate: string
  /**
   * 上架状态 1-上架 2-下架
   */
  sellStatus: number
  /**
   * 培训开始时间
   */
  trainingBeginDate: string
  /**
   * 培训结束时间
   */
  trainingEndDate: string
}

export class DistributionAuthorizationDistributorResponse {
  /**
   * 分销商ID
   */
  distributorId: string
  /**
   * 分销商类型 1-个人 2-企业
   */
  distributorType: number
  /**
   * 分销商名字
   */
  distributorName: string
  /**
   * 统一社会信用代码
   */
  unifiedSocialCreditCode: string
  /**
   * 身份证号
   */
  idCard: string
  /**
   * 合作状态
0 未开始
1 进行中
2 即将到期
3 已结束
   */
  cooperateStatus: number
  /**
   * 合同id
   */
  contractId: string
  /**
   * 合同类型
1 供销
2 分销
   */
  contractType: number
  /**
   * 合同开始时间
   */
  relationStartTime: string
  /**
   * 合同结束时间
   */
  relationEndTime: string
  /**
   * 期限类型 1-周期 2-长期
   */
  durationType: number
  /**
   * 分销状态 0-停用 1-正常
   */
  distributionStatus: number
  /**
   * 分销地市
   */
  regionStatistics: Array<RegionTreeResponse>
  /**
   * 分销级别 1-一级 2-二级
   */
  distributionLevel: number
  /**
   * 是否已授权 1- 授权 2-未授权
   */
  isAuthorized: number
  /**
   * 上级分销商ID
   */
  superDistributorId: string
  /**
   * 上级分销商是否已授权
   */
  superDistributorIsAuthorized: number
  /**
   * 上级分销商合同
   */
  superDistributorContractId: string
  /**
   * 上级分销商详情
   */
  superDistributorInfo: DistributorServicerReponse
  /**
   * 上级分销地市
   */
  superRegionStatistics: Array<RegionStatisticsResponse>
}

export class DistributionContractDetailResponse {
  /**
   * 分销商名称
   */
  distributorName: string
  /**
   * 上级分销商名称
   */
  superiorDistributorName: string
  /**
   * 分销地市
   */
  regionStatistics: Array<RegionTreeResponse>
  /**
   * 合同ID
   */
  productConsignmentContractId: string
  /**
   * 供应商ID
   */
  supplierId: string
  /**
   * 分销商ID
   */
  distributorId: string
  /**
   * 上级分销商ID
   */
  superiorDistributorId: string
  /**
   * 分销级别：1-一级，2-二级
   */
  distributionLevel: number
  /**
   * 合同开始时间
   */
  contractStartTime: string
  /**
   * 合同结束时间
   */
  contractEndTime: string
  /**
   * 合同状态：0-有效，1-终止
   */
  status: number
  /**
   * 期限类型：1-周期，2-长期
   */
  contractDurationType: number
  /**
   * 签订时间
   */
  signTime: string
  /**
   * 分销网校ID
   */
  distributorServicerIdList: Array<string>
  /**
   * 分销地区路径
   */
  regionList: Array<string>
}

export class DistributionContractResponse {
  /**
   * 合同ID
   */
  productConsignmentContractId: string
  /**
   * 供应商ID
   */
  supplierId: string
  /**
   * 分销商ID
   */
  distributorId: string
  /**
   * 上级分销商ID
   */
  superiorDistributorId: string
  /**
   * 分销级别：1-一级，2-二级
   */
  distributionLevel: number
  /**
   * 合同开始时间
   */
  contractStartTime: string
  /**
   * 合同结束时间
   */
  contractEndTime: string
  /**
   * 合同状态：0-有效，1-终止
   */
  status: number
  /**
   * 期限类型：1-周期，2-长期
   */
  contractDurationType: number
  /**
   * 签订时间
   */
  signTime: string
  /**
   * 分销网校ID
   */
  distributorServicerIdList: Array<string>
  /**
   * 分销地区路径
   */
  regionList: Array<string>
}

export class DistributionRelationshipResponse {
  /**
   * 分销商ID
   */
  distributorId: string
  /**
   * 分销商名称
   */
  distributorName: string
  /**
   * 分销商类型 1-个人 2-企业
   */
  distributorType: number
  /**
   * 分销商创建时间
   */
  createTime: string
  /**
   * 超管账户
   */
  adminAccount: string
  /**
   * 管理员名称
   */
  adminName: string
  /**
   * 管理员手机号
   */
  adminPhone: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 统一社会信用代码
   */
  code: string
}

/**
 * 分销商服务商
<AUTHOR>
 */
export class DistributorServicerReponse {
  /**
   * 服务商id
   */
  id: string
  /**
   * 所属平台id
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目id
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商名称
   */
  servicerName: string
  /**
   * 服务商名称简称
   */
  servicerShortName: string
  /**
   * 隶属企业单位路径
   */
  attachToEnterpriseUnitIdPath: string
  /**
   * 隶属企业帐户ID
   */
  accountId: string
  /**
   * 统一社会信用代码
   */
  code: string
  /**
   * 服务商类型 1培训机构、2课件供应商、3渠道商、4参训单位、5合同供应商、6供应商、7分销商
   */
  servicerType: number
  /**
   * 证件类型
   */
  idCardType: number
  /**
   * 证件号
   */
  idCard: string
  /**
   * 合作伙伴类型（供应商、分销商类型）1-个人 2-企业
   */
  partnerType: number
  /**
   * 服务商创建时间
   */
  createdTime: string
  /**
   * 附件列表
   */
  attachList: Array<ServicerAttachResponse1>
}

export class OnlineSchoolDistributionRelationshipResponse {
  /**
   * 分销关系来源ID（合同ID）
   */
  sourceId: string
  /**
   * 分销商名称
   */
  distributerName: string
  /**
   * 分销商级别
   */
  distributerLevel: number
  /**
   * 合作伙伴类型（供应商、分销商类型）1-个人 2-企业
   */
  partnerType: number
  /**
   * 分销类型 1-周期 2-长期
   */
  durationType: number
  /**
   * 分销开始时间
   */
  distributionStartTime: string
  /**
   * 分销结束时间
   */
  distributionEndTime: string
  /**
   * 分销关系来源类型，1代表供销合同，2代表分销合同
   */
  sourceType: number
  /**
   * 分销地市
   */
  regionStatistics: Array<RegionTreeResponse>
  /**
   * 上级分销商信息
   */
  superDistributor: DistributorResponse
  /**
   * 业务员ID
   */
  salemanId: string
  /**
   * 业务员名称
   */
  salemanName: string
  /**
   * 业务员手机号
   */
  phoneNumber: string
  /**
   * 分销关系状态，0代表有效，1代表无效
   */
  status: number
}

/**
 * @version: 1.0
@description: 分销商信息
@author: sugs
@create: 2023-12-26 20:54
 */
export class DistributorResponse {
  /**
   * 分销商ID
   */
  distributorId: string
  /**
   * 分销商名称
   */
  distributorName: string
  /**
   * 分销商类型 1-个人 2-企业
   */
  distributorType: number
  /**
   * 分销商简称
   */
  distributorShortName: string
  /**
   * 分销商全称
   */
  distributorFullName: string
  /**
   * 统一社会信用代码
   */
  unifiedSocialCreditCode: string
  /**
   * 身份证号
   */
  idCard: string
}

export class PolicyPortalResponse {
  /**
   * id
   */
  id: string
  /**
   * 门户标题
   */
  title: string
  /**
   * 门户简称
   */
  shortName: string
  /**
   * 域名
   */
  domainName: string
  /**
   * 网校状态
1-正常  2-失效
   */
  onlineSchoolStatus: number
  /**
   * 门户状态 是否开启
   */
  isPublished: boolean
  /**
   * 渠道id
   */
  channel: string
  /**
   * 门户推广 (0-不推广, 1-推广）
   */
  portalPromotion: number
}

export class PortalInfoDetailResponse {
  /**
   * id
   */
  id: string
  /**
   * 门户标识
   */
  identifier: string
  /**
   * 门户类别
   */
  category: number
  /**
   * 门户类型
   */
  portalType: number
  /**
   * 门户标题
   */
  title: string
  /**
   * 门户简称
   */
  shortName: string
  /**
   * 域名
   */
  domainName: string
  /**
   * 域名短码
   */
  domainShortCode: string
  /**
   * 域名h5
   */
  domainNameh5: string
  /**
   * h5门户id
   */
  portalIdh5: string
  /**
   * h5域名短码
   */
  domainShortCodeh5: string
  /**
   * 网校状态
1-正常  2-失效
   */
  onlineSchoolStatus: number
  /**
   * 客服电话号码
   */
  CSPhone: string
  /**
   * 咨询时间
   */
  CSCallTime: string
  /**
   * 底部内容
   */
  footContent: string
  /**
   * 门户logo
   */
  logo: string
  /**
   * 浏览器图标
   */
  icon: string
  /**
   * 客服电话图片
   */
  CSPhonePicture: string
  /**
   * 友情链接
   */
  friendLinks: Array<FriendLinkInfo>
  /**
   * web端轮播图
   */
  bannersInWeb: Array<BannerInfo>
  /**
   * 移动端轮播图
   */
  bannersInh5: Array<BannerInfo>
  /**
   * 是否开启线下报名入口
   */
  isOfflineEnabled: boolean
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 线下集体报名id
   */
  offlineId: string
  /**
   * 渠道id
   */
  channelId: string
  /**
   * 门户简介说明内容
   */
  content: string
  /**
   * 域名类型（系统默认域名 1 自有域名 2)
   */
  domainNameType: number
}

export class PortalInfoResponse {
  /**
   * id
   */
  id: string
  /**
   * 门户标识
   */
  identifier: string
  /**
   * 门户类别
   */
  category: number
  /**
   * 门户类型
   */
  portalType: number
  /**
   * 门户标题
   */
  title: string
  /**
   * 门户简称
   */
  shortName: string
  /**
   * 域名
   */
  domainName: string
  /**
   * 域名短码
   */
  domainShortCode: string
  /**
   * h5域名
   */
  domainNameh5: string
  /**
   * h5域名短码
   */
  domainShortCodeh5: string
  /**
   * 网校状态
1-正常  2-失效
   */
  onlineSchoolStatus: number
  /**
   * 门户状态   是否已发布
   */
  isPublished: boolean
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 当前门户的销售渠道id
   */
  channelId: string
  /**
   * 所属服务商id (例如网校id)
   */
  belongServiceId: string
  /**
   * 域名类型（系统默认域名 1 自有域名 2)
   */
  domainNameType: number
}

export class QRCode1 {
  /**
   * 二维码图片
   */
  mobileQrcode: string
  /**
   * 二维码操作提示
   */
  qrcodeTip: string
}

/**
 * 附件
 */
export class ServicerAttachResponse {
  /**
   * 文件名
   */
  fileName: string
  /**
   * 文件路径
   */
  filePath: string
}

export class BannerInfo {
  /**
   * 轮播图id
   */
  id: string
  /**
   * 轮播图名称
   */
  name: string
  /**
   * 轮播图路径
   */
  path: string
  /**
   * 轮播图链接
   */
  link: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 轮播图排序
   */
  sort: number
}

/**
 * 分销商推广门户
 */
export class DistributorPortalInfoResponse {
  /**
   * 门户id
   */
  id: string
  /**
   * 所属平台id
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目id
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 业务平台服务商id  (例如分销商id)
   */
  servicerId: string
  /**
   * 所属服务商id (例如网校id)
   */
  belongServiceId: string
  /**
   * 0-主门户 1-子门户
   */
  category: number
  /**
   * 门户标识
   */
  identifier: string
  /**
   * 1-web端 2-移动端
   */
  portalType: number
  /**
   * 门户标题
   */
  title: string
  /**
   * 门户简称
   */
  shortName: string
  /**
   * 门户logo
   */
  logo: string
  /**
   * 浏览器图标
   */
  icon: string
  /**
   * 主题颜色
   */
  themeColor: string
  /**
   * 移动二维码
   */
  mobileQRcode: string
  /**
   * 移动二维码来源标识 1-系统生成 2-自定义
   */
  mobileQRCodeSign: number
  /**
   * 客服电话图片
   */
  CSPhonePicture: string
  /**
   * 客服电话
   */
  CSPhone: string
  /**
   * 客服咨询时间
   */
  CSCallTime: string
  /**
   * 在线客服代码内容id
   */
  CSOnlineCodeId: string
  /**
   * 培训流程图片
   */
  trainingFlowPicture: string
  /**
   * 底部内容
   */
  footContent: string
  /**
   * 宣传口号
   */
  slogan: string
  /**
   * 域名
   */
  domainName: string
  /**
   * 域名短码
   */
  domainShortCode: string
  /**
   * H5域名(请求Web端门户信息才会使用这个字段 如果请求的是H5端的门户信息，该字段为null)
   */
  domainNameH5: string
  /**
   * h5门户id
   */
  portalIdh5: string
  /**
   * 域名短码h5
   */
  domainShortCodeh5: string
  /**
   * 门户简介说明内容
   */
  content: string
  /**
   * 是否提供服务号
   */
  isProvideServiceAccount: boolean
  /**
   * 门户状态  是否已发布
   */
  isPublished: boolean
  /**
   * 网校状态
1-正常  2-失效
   */
  onlineSchoolStatus: number
  /**
   * 网校模式 1-正式实施 2-DEMO
   */
  onlineSchoolModes: number
  /**
   * 是否到期
   */
  isExpired: boolean
  /**
   * 前端模板id
   */
  portalTemplateId: string
  /**
   * 企业客服微信
   */
  CSWechat: string
  /**
   * 海报二维码
   */
  billQRCodes: Array<QRCode>
  /**
   * 门户发布时间
   */
  publishedTime: string
  /**
   * 门户取消发布时间
   */
  unPublishedTime: string
  /**
   * 信息
   */
  cnzz: string
  /**
   * 目录名
   */
  dirName: string
  /**
   * 域名类型（系统默认域名 1 自有域名 2）
   */
  domainNameType: number
  /**
   * 门户板块集合
   */
  plates: Array<PortalPlateResponse>
  /**
   * 友情链接集合
   */
  friendLinks: Array<PortalFriendLinkResponse>
  /**
   * 轮播图
   */
  banner: Array<PortalBannerResponse>
  /**
   * 移动端轮播图
   */
  bannersInh5: Array<PortalBannerResponse>
  /**
   * 门户菜单
   */
  menu: Array<PortalMenuResponse>
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 是否删除
   */
  isDeleted: number
  /**
   * 删除时间
   */
  deletedTime: string
  /**
   * 记录创建时间
   */
  recordCreatedTime: string
  /**
   * 记录更新时间
   */
  recordUpdatedTime: string
  /**
   * 销售渠道id
   */
  channelId: string
  /**
   * 当前分销商单位名称
   */
  distributorUnitName: string
}

/**
 * 友情链接信息
<AUTHOR>
 */
export class FriendLinkInfo {
  /**
   * id
   */
  id: string
  /**
   * 所属门户id
   */
  portalId: string
  /**
   * 标题
   */
  title: string
  /**
   * 图片
   */
  picture: string
  /**
   * 友情链接类型
   */
  friendLinkType: number
  /**
   * 链接
   */
  link: string
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
}

/**
 * 详细资讯信息
 */
export class NewsDetailResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 平台id
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 子项目id
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  serviceId: string
  /**
   * 分类id
   */
  necId: string
  /**
   * 标题
   */
  title: string
  /**
   * 摘要
   */
  summary: string
  /**
   * 内容
   */
  content: string
  /**
   * 封面图片路径
   */
  coverPath: string
  /**
   * 来源
   */
  source: string
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 发布地区编码
   */
  areaCodePath: string
  /**
   * 资讯状态 0 草稿 1正常
   */
  status: number
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 浏览数量
   */
  reviewCount: number
  /**
   * 弹窗起始时间
   */
  popupBeginTime: string
  /**
   * 弹窗截止时间
   */
  popupEndTime: string
  /**
   * 专题ID
   */
  specialSubjectId: string
  /**
   * 门户简称
   */
  shortName: string
  /**
   * 门户标识
   */
  identifier: string
}

/**
 * 简略资讯信息
 */
export class NewsSimpleResponse {
  /**
   * 门户id
   */
  portalId: string
  /**
   * 门户展示名称
   */
  showName: string
  /**
   * 门户简称
   */
  shortName: string
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 摘要
   */
  summary: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 资讯状态 0 草稿 1正常
   */
  status: number
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 发布地区编码
   */
  areaCodePath: string
  /**
   * 专题Id
   */
  specialSubjectId: string
}

/**
 * 门户轮播图
 */
export class PortalBannerResponse {
  /**
   * 轮播图id
   */
  id: string
  /**
   * 轮播图名称
   */
  name: string
  /**
   * 图片路径
   */
  path: string
  /**
   * 链接地址
   */
  link: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 门户友情链接
 */
export class PortalFriendLinkResponse {
  id: string
  title: string
  picture: string
  friendLinkType: number
  link: string
  sort: number
  createdTime: string
}

/**
 * 门户菜单
 */
export class PortalMenuResponse {
  /**
   * 栏目id
   */
  id: string
  /**
   * 门户栏目名称
   */
  name: string
  /**
   * 门户栏目展示名称
   */
  displayName: string
  /**
   * 父栏目id
   */
  parentId: string
  /**
   * 门户栏目类型 1-菜单 2-资讯
   */
  type: number
  /**
   * 来源类型 1-内置 2-用户创建
   */
  sourceType: number
  /**
   * 链接
   */
  link: string
  /**
   * 业务code
   */
  code: string
  /**
   * 引用id
   */
  referenceId: string
  /**
   * 是否可用
   */
  enable: boolean
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
}

export class PortalPlateResponse {
  /**
   * 板块id
   */
  id: string
  /**
   * 门户板块名称
   */
  name: string
  /**
   * 门户板块展示名称
   */
  displayName: string
  /**
   * 父板块id
   */
  parentId: string
  /**
   * 门户板块类型 1-默认 2-资讯
   */
  type: number
  /**
   * 来源类型 1-内置 2-用户创建
   */
  sourceType: number
  /**
   * 链接
   */
  link: string
  /**
   * 业务code
   */
  code: string
  /**
   * 引用id
   */
  referenceId: string
  /**
   * 是否允许存在子级
   */
  allowChildren: number
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
}

export class PosterConfigurationResponse {
  id: string
  portalId: string
  identifier: string
  title: string
  promotionalCopy: string
  templateId: string
  csPhone: string
  qrcodeList: Array<QRCode1>
  createdTime: string
  updatedTime: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  serviceId: string
}

/**
 * <AUTHOR> linq
@date : 2024-09-18 14:04
@description：二维码
 */
export class QRCode {
  /**
   * 二维码图片
   */
  mobileQrcode: string
  /**
   * 二维码操作提示
   */
  qrcodeTip: string
}

/**
 * <AUTHOR>
对接管理系统
@date 2022/11/15 14:40
 */
export class ConnectManageSystemResponse {
  /**
   * 同步状态
0 未同步
1 已同步
2 同步失败
   */
  syncStatus: number
  /**
   * 同步信息
   */
  syncMessage: string
}

export class ExtendedInfoResponse {
  /**
   * 是否打印
true 打印 false 未打印
   */
  whetherToPrint: boolean
  /**
   * 打印时间
   */
  printTime: string
  /**
   * pdf证书地址
   */
  pdfUrl: string
}

/**
 * 学员学习报名信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:08
 */
export class LearningRegisterResponse {
  /**
   * 报名方式
<p>
1:学员自主报名
2:集体报名
3:管理员导入
   */
  registerType: number
  /**
   * 报名来源类型
Order: 订单
   */
  sourceType: string
  /**
   * 报名来源ID
   */
  sourceId: string
  /**
   * 学员状态
<p>
1:正常
2:冻结
3:失效
   */
  status: number
  /**
   * 学员状态最后变更时间
   */
  statusChangeTime: string
  /**
   * 报名时间
   */
  registerTime: string
  /**
   * 来源订单号
   */
  orderNo: string
  /**
   * 来源子订单号
   */
  subOrderNo: string
  /**
   * 来源批次单号
   */
  batchOrderNo: string
  /**
   * 失效来源类型
   */
  frozenAndInvalidSourceType: string
  /**
   * 失效来源id
   */
  frozenAndInvalidSourceId: string
}

/**
 * 学员方案学习主题模型
<AUTHOR>
@version 1.0
@date 2021/12/14 11:41
 */
export class StudentSchemeLearningResponse {
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学号id
   */
  studentNo: string
  /**
   * 数据归属信息
   */
  owner: OwnerResponse
  /**
   * 学员信息
   */
  student: UserResponse
  /**
   * 学员学习报名信息
   */
  learningRegister: LearningRegisterResponse
  /**
   * 培训方案信息
   */
  scheme: SchemeResponse
  /**
   * 学员学习信息
   */
  studentLearning: StudentLearningResponse
  /**
   * 数据分析信息
   */
  dataAnalysis: DataAnalysisResponse
  /**
   * 对接管理系统
   */
  connectManageSystem: ConnectManageSystemResponse
  /**
   * 扩展信息
   */
  extendedInfo: ExtendedInfoResponse
}

/**
 * 课程学习方式学习信息
<AUTHOR>
@version 1.0
true:@date 2022/1/15 14:08
 */
export class CourseLearningResponse {
  /**
   * 整体课程学习状态
<p>
0:未学习
1:学习中
2:学习完成
   */
  courseScheduleStatus: number
  /**
   * 整体课程完成学习时间
   */
  courseQualifiedTime: string
  /**
   * 已选课程数
   */
  selectedCourseCount: number
  /**
   * 已选课总学时
   */
  selectedCoursePeriod: number
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 学习方式类型
<p>
1: 选课学习方式
2: 考试学习方式
3: 自主学习课程学习方式
   */
  learningType: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 学习资源类型
<p>
1: 课程
2: 考试
   */
  learningResourceType: number
  /**
   * 学习资源ID
<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
   */
  learningResourceId: string
  /**
   * 课程学习方式下只提供课程学习方式考核指标结果json结构
如果是在考试学习方式则只提供考核指标结果
扩展方案配置json课程或考试学习方式考核指标部分的json结构
ownerType枚举值
1：学习方案
2：期别
3：期数
4：学习方式
5：课程大纲
json结构如：
[{
assessId: 考核指标id,
ownerType: 拥有者类型,
ownerId: 拥有者id如方案id学习方式id等,
name: 考核要求模板如Scheme_Assess_001,
必修考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
},
选修考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
}
},{
...(数组结构，允许有过个考核要求配置)
}]
   */
  userAssessResult: Array<string>
  /**
   * 学员培训成果
   */
  learningResult: Array<LearningResultResponse>
}

/**
 * 数据分析信息
<AUTHOR>
@version 1.0
@date 2022/1/20 15:14
 */
export class DataAnalysisResponse {
  /**
   * 成果配置可获得学时
   */
  trainingResultPeriod: number
  /**
   * 考核要求学时
   */
  requirePeriod: number
  /**
   * 已获得总学时
   */
  acquiredPeriod: number
}

/**
 * 考试学习方式学习信息
<AUTHOR>
@version 1.0
@date 2022/1/15 14:09
 */
export class ExamLearningResponse {
  /**
   * 是否已考试
   */
  committedExam: boolean
  /**
   * 考试考核计算结果
-1：未考核
1：合格
0：不合格
   */
  examAssessResult: number
  /**
   * 考试合格时间
   */
  examQualifiedTime: string
  /**
   * 考试次数
   */
  examCount: number
  /**
   * 最高成绩
   */
  maxExamScore: number
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 学习方式类型
<p>
1: 选课学习方式
2: 考试学习方式
3: 自主学习课程学习方式
   */
  learningType: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 学习资源类型
<p>
1: 课程
2: 考试
   */
  learningResourceType: number
  /**
   * 学习资源ID
<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
   */
  learningResourceId: string
  /**
   * 课程学习方式下只提供课程学习方式考核指标结果json结构
如果是在考试学习方式则只提供考核指标结果
扩展方案配置json课程或考试学习方式考核指标部分的json结构
ownerType枚举值
1：学习方案
2：期别
3：期数
4：学习方式
5：课程大纲
json结构如：
[{
assessId: 考核指标id,
ownerType: 拥有者类型,
ownerId: 拥有者id如方案id学习方式id等,
name: 考核要求模板如Scheme_Assess_001,
必修考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
},
选修考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
}
},{
...(数组结构，允许有过个考核要求配置)
}]
   */
  userAssessResult: Array<string>
  /**
   * 学员培训成果
   */
  learningResult: Array<LearningResultResponse>
}

/**
 * 考试学习方式学习信息
<AUTHOR>
@version 1.0
@date 2022/1/15 14:09
 */
export class LearningExperienceLearningResponse {
  /**
   * 是否已提交学习心得
   */
  committedLearningExperience: boolean
  /**
   * 学习心得考核计算结果
-1：未考核
1：合格
0：不合格
   */
  learningExperienceAssessResult: number
  /**
   * 学习心得合格时间
   */
  learningExperienceQualifiedTime: string
  /**
   * 最高成绩
   */
  maxLearningExperienceScore: number
  /**
   * 心得通过数量  没有考核时使用这个字段
   */
  learningExperiencePassCount: number
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 学习方式类型
<p>
1: 选课学习方式
2: 考试学习方式
3: 自主学习课程学习方式
   */
  learningType: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 学习资源类型
<p>
1: 课程
2: 考试
   */
  learningResourceType: number
  /**
   * 学习资源ID
<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
   */
  learningResourceId: string
  /**
   * 课程学习方式下只提供课程学习方式考核指标结果json结构
如果是在考试学习方式则只提供考核指标结果
扩展方案配置json课程或考试学习方式考核指标部分的json结构
ownerType枚举值
1：学习方案
2：期别
3：期数
4：学习方式
5：课程大纲
json结构如：
[{
assessId: 考核指标id,
ownerType: 拥有者类型,
ownerId: 拥有者id如方案id学习方式id等,
name: 考核要求模板如Scheme_Assess_001,
必修考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
},
选修考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
}
},{
...(数组结构，允许有过个考核要求配置)
}]
   */
  userAssessResult: Array<string>
  /**
   * 学员培训成果
   */
  learningResult: Array<LearningResultResponse>
}

/**
 * 学员学习信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class StudentLearningResponse {
  /**
   * 培训结果
<p>
-1:未知，培训尚未完成
1:培训合格
0:培训不合格
   */
  trainingResult: number
  /**
   * 取得培训结果时间
   */
  trainingResultTime: string
  /**
   * 课程学习方式学习信息
   */
  courseLearning: CourseLearningResponse
  /**
   * 考试学习方式学习信息
   */
  examLearning: ExamLearningResponse
  /**
   * 学习心得学习方式学习信息
   */
  learningExperienceLearning: LearningExperienceLearningResponse
  /**
   * 方案考核指标结果，json结构（只提供方案部分配置的考核要求）
扩展方案配置json考核指标部分的json结构
ownerType枚举值
1：学习方案
2：期别
3：期数
4：学习方式
5：课程大纲
json结构如：
[{
assessId: 考核指标id,
ownerType: 拥有者类型,
ownerId: 拥有者id如方案id学习方式id等,
name: 考核要求模板如Scheme_Assess_001,
方案考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
}
},{
...(数组结构，允许有过个考核要求配置)
}]
   */
  userAssessResult: Array<string>
  /**
   * 学员培训成果
   */
  learningResult: Array<LearningResultResponse>
}

/**
 * 证书型型培训成果配置
<AUTHOR>
@version 1.0
@date 2022/1/15 14:30
 */
export class CertificateLearningConfigResultResponse implements LearningResultConfigResponse {
  /**
   * 证书模板ID
   */
  certificateTemplateId: string
  /**
   * 是否开放打印
   */
  openPrintTemplate: boolean
  /**
   * 成果类型
<p>
1：分数型学习成果
2：证书型学习成果
   */
  resultType: number
}

/**
 * 分数型培训成果配置
<AUTHOR>
@version 1.0
@date 2022/1/15 14:30
 */
export class GradeLearningConfigResultResponse implements LearningResultConfigResponse {
  /**
   * 分数类型
<p>说明：查询时可以根据相同的分数类型进行累加分数</p>
<p>
CREDIT: 学分
   */
  gradeType: string
  /**
   * 分数
   */
  grade: number
  /**
   * 成果类型
<p>
1：分数型学习成果
2：证书型学习成果
   */
  resultType: number
}

/**
 * 培训成果配置
<AUTHOR>
@version 1.0
@date 2022/1/15 14:30
 */
export interface LearningResultConfigResponse {
  /**
   * 成果类型
<p>
1：分数型学习成果
2：证书型学习成果
   */
  resultType: number
}

/**
 * 学员培训成果
<AUTHOR>
@version 1.0
@date 2022/1/15 14:28
 */
export class LearningResultResponse {
  /**
   * 用户成果id
   */
  learningResultId: string
  /**
   * 取得时间
   */
  gainedTime: string
  /**
   * 培训成果配置
   */
  learningResultConfig: LearningResultConfigResponse
}

/**
 * 培训方案信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class SchemeResponse {
  /**
   * 培训方案id
   */
  schemeId: string
  /**
   * 培训方案类型
<p>
chooseCourseLearning: 选课规则
autonomousCourseLearning: 自主选课
   */
  schemeType: string
  /**
   * 培训属性
   */
  skuProperty: SchemeSkuPropertyResponse
  /**
   * 培训属性
   */
  schemeName: string
  /**
   * 学习成果
   */
  learningResult: Array<LearningResultConfigResponse>
}

/**
 * 培训属性
<AUTHOR>
@version 1.0
@date 2022/1/17 10:22
 */
export class SchemeSkuPropertyResponse {
  /**
   * 年度
   */
  year: SchemeSkuPropertyValueResponse
  /**
   * 地区: 省
   */
  province: SchemeSkuPropertyValueResponse
  /**
   * 地区: 市
   */
  city: SchemeSkuPropertyValueResponse
  /**
   * 地区: 区县
   */
  county: SchemeSkuPropertyValueResponse
  /**
   * 行业
   */
  industry: SchemeSkuPropertyValueResponse
  /**
   * 科目类型
   */
  subjectType: SchemeSkuPropertyValueResponse
  /**
   * 培训类别
   */
  trainingCategory: SchemeSkuPropertyValueResponse
  /**
   * 培训专业
   */
  trainingProfessional: SchemeSkuPropertyValueResponse
  /**
   * 技术等级
   */
  technicalGrade: SchemeSkuPropertyValueResponse
  /**
   * 岗位类别
   */
  positionCategory: SchemeSkuPropertyValueResponse
  /**
   * 培训对象
   */
  trainingObject: SchemeSkuPropertyValueResponse
  /**
   * 技术等级
   */
  jobLevel: SchemeSkuPropertyValueResponse
  /**
   * 工种
   */
  jobCategory: SchemeSkuPropertyValueResponse
  /**
   * 科目
   */
  subject: SchemeSkuPropertyValueResponse
  /**
   * 年级
   */
  grade: SchemeSkuPropertyValueResponse
  /**
   * 学段
   */
  learningPhase: SchemeSkuPropertyValueResponse
  /**
   * 学科
   */
  discipline: SchemeSkuPropertyValueResponse
}

/**
 * 培训属性值
<AUTHOR>
@version 1.0
@date 2022/1/17 10:22
 */
export class SchemeSkuPropertyValueResponse {
  /**
   * sku属性值id
   */
  skuPropertyValueId: string
}

export class DistributorCommodityAndRelationResponse {
  /**
   * 指导价格
   */
  suggestedPrice: number
  /**
   * 上级分销商商品价格
   */
  superiorDistributorPrice: number
  /**
   * 产品分销授权是否展示
0-不展示 1-展示
   */
  relationIsShow: boolean
  /**
   * 来源代销合同信息
   */
  productConsignmentContract: ProductConsignmentContractResponse
  /**
   * 定价方案信息
   */
  productPricingSchemeResponse: ProductPricingSchemeResponse
  /**
   * 当前商品网校信息
   */
  onlineSchoolInfo: OnlineSchoolInfoResponse
  /**
   * 分销商商品id
   */
  distributorCommodityId: string
  /**
   * 分销商商品销售名称
   */
  saleTitle: string
  /**
   * 商品-培训方案资源类型
   */
  schemeResourceInfo: SchemeResourceInfoResponse
  /**
   * 分销商信息
   */
  distributor: DistributorResponse
  /**
   * 分销商商品sku属性
   */
  propertyList: Array<PropertyResponse>
  /**
   * 授权价格类型 1-固定 2-区间
   */
  priceType: number
  /**
   * 分销商商品价格
   */
  price: number
  /**
   * 最大价格
   */
  maxPrice: number
  /**
   * 最小价格
   */
  minPrice: number
  /**
   * 网校商品来源系统上下文信息
   */
  sourceOwnerInfo: OwnerInfoResponse
  /**
   * 原始商品id
   */
  commodityId: string
  /**
   * 分销商商品图片路径
   */
  commodityPicturePath: string
  /**
   * 分销状态
0-开启 1-关闭
商品的分销开始时间、结束时间作为判断
   */
  distributionStatus: number
  /**
   * 分销商商品销售时段类型
1-周期 2-长期
   */
  saleTimeType: number
  /**
   * 分销商商品销售开始时间
   */
  saleStartTime: string
  /**
   * 分销商商品销售结束时间
   */
  saleEndTime: string
  /**
   * 销售状态 1-有效 2-无效
   */
  saleStatus: number
  /**
   * 分销商品来源类型
1-产品分销授权
   */
  commoditySourceType: number
  /**
   * 分销商品来源id
1-产品分销授权id
   */
  commoditySourceId: string
  /**
   * 分销商品创建时间
   */
  commodityCreatedTime: string
  /**
   * 分销商品更新时间
   */
  commodityUpdatedTime: string
  /**
   * 商品是否门户可见
   */
  isShow: boolean
  /**
   * 定价方案启用个数
   */
  productPricingSchemeEnableNum: number
  /**
   * 培训方案期别数(面网授班级才有值)
   */
  issueNum: number
  /**
   * 分销商品上下架状态
   */
  shelveStatus: number
}

/**
 * <AUTHOR> linq
@date : 2024-09-13 15:15
@description：分销商商品商品属性集合
 */
export class DistributorCommodityPropertyCollectionResponse {
  /**
   * 授权商品商品属性
   */
  commodityPropertyInfoList: Array<CommodityPropertyInfoResponse>
  /**
   * 授权商品方案属性
   */
  commoditySchemeResourceList: Array<CommoditySchemeResourceResponse>
  /**
   * 商品销售地区属性树集合（商品授权地区）
   */
  commodityAuthorizedRegionTreeList: Array<CommoditySkuRegionPropertyNodeResponse>
  /**
   * 商品sku属性集合
   */
  commoditySkuPropertyList: Array<CommoditySkuPropertyResponse>
  /**
   * 商品sku地区属性树集合
   */
  commoditySkuPropertyRegionTreeList: Array<CommoditySkuRegionPropertyNodeResponse>
  /**
   * 商品sku培训专业属性树集合
   */
  commoditySkuPropertyTrainingProfessionalTreeList: Array<CommoditySkuTrainingProfessionPropertyNodeResponse>
  /**
   * 商品销售地区属性树集合（定价方案地区）
   */
  commodityRegionTreeList: Array<CommoditySkuRegionPropertyNodeResponse>
}

export class DistributorCommodityResponse {
  /**
   * 指导价格
   */
  suggestedPrice: number
  /**
   * 上级分销商商品价格
   */
  superiorDistributorPrice: number
  /**
   * 分销商商品id
   */
  distributorCommodityId: string
  /**
   * 分销商商品销售名称
   */
  saleTitle: string
  /**
   * 商品-培训方案资源类型
   */
  schemeResourceInfo: SchemeResourceInfoResponse
  /**
   * 分销商信息
   */
  distributor: DistributorResponse
  /**
   * 分销商商品sku属性
   */
  propertyList: Array<PropertyResponse>
  /**
   * 授权价格类型 1-固定 2-区间
   */
  priceType: number
  /**
   * 分销商商品价格
   */
  price: number
  /**
   * 最大价格
   */
  maxPrice: number
  /**
   * 最小价格
   */
  minPrice: number
  /**
   * 网校商品来源系统上下文信息
   */
  sourceOwnerInfo: OwnerInfoResponse
  /**
   * 原始商品id
   */
  commodityId: string
  /**
   * 分销商商品图片路径
   */
  commodityPicturePath: string
  /**
   * 分销状态
0-开启 1-关闭
商品的分销开始时间、结束时间作为判断
   */
  distributionStatus: number
  /**
   * 分销商商品销售时段类型
1-周期 2-长期
   */
  saleTimeType: number
  /**
   * 分销商商品销售开始时间
   */
  saleStartTime: string
  /**
   * 分销商商品销售结束时间
   */
  saleEndTime: string
  /**
   * 销售状态 1-有效 2-无效
   */
  saleStatus: number
  /**
   * 分销商品来源类型
1-产品分销授权
   */
  commoditySourceType: number
  /**
   * 分销商品来源id
1-产品分销授权id
   */
  commoditySourceId: string
  /**
   * 分销商品创建时间
   */
  commodityCreatedTime: string
  /**
   * 分销商品更新时间
   */
  commodityUpdatedTime: string
  /**
   * 商品是否门户可见
   */
  isShow: boolean
  /**
   * 定价方案启用个数
   */
  productPricingSchemeEnableNum: number
  /**
   * 培训方案期别数(面网授班级才有值)
   */
  issueNum: number
  /**
   * 分销商品上下架状态
   */
  shelveStatus: number
}

/**
 * <AUTHOR> linq
@date : 2024-05-06 14:04
@description：分销商品sku属性
 */
export class DistributorCommoditySkuPropertyCollectionResponse {
  /**
   * 商品sku属性集合
   */
  commoditySkuPropertyList: Array<CommoditySkuPropertyResponse>
  /**
   * 商品sku地区属性树集合
   */
  commoditySkuPropertyRegionTreeList: Array<CommoditySkuRegionPropertyNodeResponse>
  /**
   * 商品sku培训专业属性树集合
   */
  commoditySkuPropertyTrainingProfessionalTreeList: Array<CommoditySkuTrainingProfessionPropertyNodeResponse>
  /**
   * 商品销售地区属性树集合（定价方案地区）
   */
  commodityRegionTreeList: Array<CommoditySkuRegionPropertyNodeResponse>
}

/**
 * 分销商商品统计结果
 */
export class DistributorCommodityStatisticResponse {
  /**
   * 统计类型
1-我的分销 2-我的下级分销
   */
  statisticType: number
  /**
   * 统计数量
   */
  distributorCommodityNumber: number
}

/**
 * 推广门户线下集体报名配置返回值
 */
export class PortalOfflineCollectiveSignUpSettingResponse {
  /**
   * 推广门户线下集体报名配置信息
   */
  id: string
  /**
   * 推广门户线下集体报名入口开关
   */
  openEntrySwitch: boolean
  /**
   * 推广门户线下集体报名入口图片附件
   */
  entryPictureAttachments: Array<Attachment>
  /**
   * 线下集体报名名称
   */
  name: string
  /**
   * 推广门户线下集体报名模板地址
   */
  templateAttachment: Attachment
  /**
   * 访问链接
   */
  accessUrl: string
  /**
   * 底部文本说明内容id
   */
  bottomDescriptionId: string
  /**
   * 底部文本说明内容
   */
  bottomDescription: string
  /**
   * 报名步骤信息
   */
  signUpSteps: Array<SignUpStepDto>
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * <AUTHOR> linq
@date : 2024-09-12 19:36
@description：推广门户定价方案数量response
 */
export class PricingPolicyCommodityCountResponse {
  /**
   * 推广门户id
   */
  portalId: string
  /**
   * 定价方案商品数量
   */
  commodityCount: number
}

/**
 * @Description 附件请求
<AUTHOR>
@Date 2024/3/20 9:56
 */
export class Attachment {
  /**
   * 附件名称
   */
  name: string
  /**
   * 附件地址
   */
  url: string
}

/**
 * <AUTHOR> linq
@date : 2024-09-23 11:28
@description：网校相关信息
 */
export class OnlineSchoolInfoResponse {
  /**
   * 网校名称
   */
  schoolName: string
  /**
   * 业主单位全称
   */
  unitName: string
  /**
   * 业主单位简称
   */
  unitShotName: string
}

/**
 * <AUTHOR> linq
@date : 2024-09-18 17:21
@description：定价方案推广门户信息
 */
export class PricingPolicyPortalInfo {
  /**
   * 推广门户标识id
   */
  portalIdentify: string
  /**
   * 推广门户销售渠道id
   */
  saleChannelId: string
  /**
   * 推广门户渠道排序
   */
  portalSort: number
  /**
   * 门户展示 (0-不展示, 1-展示）
   */
  showPortal: number
  /**
   * 门户推广 (0-不推广, 1-推广）
   */
  portalPromotion: number
  /**
   * 推广门户定价方案商品id
   */
  portalPricingPolicyCommodityId: string
}

export class ProductConsignmentContractResponse {
  /**
   * 代销合同id
   */
  productConsignmentContractId: string
  /**
   * 代销合同类型
1-供销合同 2-分销合同
   */
  contractType: number
  /**
   * 分销商ID
   */
  distributorId: string
  /**
   * 供应商id
   */
  supplierId: string
  /**
   * 分销商简称
   */
  distributorShortName: string
  /**
   * 分销商全称
   */
  distributorFullName: string
  /**
   * 分销级别 1-一级 2-二级
   */
  distributionLevel: number
  /**
   * 期限类型 1-周期 2-长期
   */
  durationType: number
  /**
   * 合作状态
0 未开始
1 进行中
2 即将到期
3 已结束
   */
  cooperateStatus: number
  /**
   * 合同开始时间
   */
  contractStartTime: string
  /**
   * 合同结束时间
   */
  contractEndTime: string
  /**
   * 分销地市
   */
  regionStatistics: Array<RegionStatisticsResponse>
  /**
   * 上级分销商ID
   */
  superDistributorId: string
  /**
   * 上级分销商合同
   */
  superDistributorContractId: string
  /**
   * 上级分销商名称
   */
  superDistributorName: string
  /**
   * 上级分销商简称
   */
  superDistributorShortName: string
  /**
   * 统一社会信用代码
   */
  superUnifiedSocialCreditCode: string
  /**
   * 身份证号
   */
  superIdCard: string
  /**
   * 上级分销地市
   */
  superRegionStatistics: Array<RegionStatisticsResponse>
  /**
   * 地区树结构
   */
  regionTreeList: Array<RegionTreeResponse>
  /**
   * 地区树ID
   */
  contractDistributionRegionTreeId: string
}

export class ProductPricingSchemeResponse {
  /**
   * 定价方案ID
   */
  productPricingSchemeId: string
  /**
   * 产品分销授权ID
   */
  productDistributionAuthId: string
  /**
   * 授权商品id
   */
  productId: string
  /**
   * 价格
   */
  price: number
  /**
   * 启用时间
   */
  enableTime: string
  /**
   * 禁用时间
   */
  disEnableTime: string
  /**
   * 授权价格类型 1-固定 2-区间
   */
  priceType: number
  /**
   * 最大价格
   */
  maxPrice: number
  /**
   * 最小价格
   */
  minPrice: number
  /**
   * 产品销售范围
   */
  saleScopeList: Array<SaleScopeResponse>
  /**
   * 定价方案状态 (0-禁用, 1-启用)
   */
  status: number
  /**
   * 加入的推广门户信息
   */
  pricingPolicyPortalInfoList: Array<PricingPolicyPortalInfo>
}

export class RegionItemResponse {
  /**
   * 销售范围ID+地区ID 或 合同ID+地区ID（主键）
   */
  id: string
  /**
   * 地区来源类型 (1-销售地区, 2-分销地区)
   */
  sourceType: number
  /**
   * 地区来源ID（销售范围ID/合同ID）
   */
  sourceId: string
  /**
   * 地区ID
   */
  regionId: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 省ID
   */
  provinceId: string
  /**
   * 市ID
   */
  cityId: string
  /**
   * 县ID
   */
  countyId: string
  /**
   * 层级
   */
  level: number
  /**
   * 记录创建时间
   */
  recordCreatedTime: string
}

/**
 * 市粒度统计
 */
export class RegionSaleScopeResponse {
  /**
   * 地区来源类型 (1-销售地区, 2-分销地区)
   */
  sourceType: number
  /**
   * 地区来源ID（销售范围ID/合同ID）
   */
  sourceId: string
  /**
   * 地区ID
   */
  regionId: string
  /**
   * 地区名称
   */
  regionName: string
  /**
   * 统计数量(区数量)
   */
  total: number
}

/**
 * @see SaleScopeDto
 */
export class SaleScopeResponse {
  /**
   * 销售范围ID
   */
  saleScopeId: string
  /**
   * 地区销售范围类型 (0-同分销商品, 1-自定义)
   */
  regionSaleScopeType: number
  /**
   * 地区销售范围关联ID（销售范围ID/合同ID）
   */
  regionSaleScopeRelationId: string
  /**
   * 地区销售范围关联地区树id
   */
  regionSaleRegionTreeId: string
  /**
   * 单位销售范围类型 (1-不限, 2-自定义)
   */
  unitSaleScopeType: number
  /**
   * 是否启用
   */
  enable: number
  /**
   * 备注
   */
  remark: string
  /**
   * 记录创建时间
   */
  recordCreatedTime: string
  /**
   * 产品地区销售范围
   */
  regionSaleScopeList: Array<RegionSaleScopeResponse>
  /**
   * 地区树结构
   */
  regionTreeList: Array<RegionTreeResponse>
  /**
   * 销售地区明细
   */
  regionItemList: Array<RegionItemResponse>
  /**
   * 产品单位销售范围
   */
  unitSaleScopeList: Array<UnitSaleScopeResponse>
}

export class SignUpStepDto {
  /**
   * 步骤内容
   */
  content: string
  /**
   * 序号
   */
  index: number
  /**
   * 步骤标题
   */
  title: string
  /**
   * 步骤内容 Id
   */
  contentId: string
}

export class UnitSaleScopeResponse {
  /**
   * 销售范围ID+统一社会信用代码（主键）
   */
  id: string
  /**
   * 销售范围ID
   */
  saleScopeId: string
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 统一社会信用代码
   */
  creditCode: string
  /**
   * 记录创建时间
   */
  recordCreatedTime: string
}

export class CommodityDiscountApplyResponse {
  /**
   * 优惠申请id
   */
  applyId: string
  /**
   * 上下文信息
   */
  ownerInfo: OwnerInfoResponse
  /**
   * 申请人id
   */
  applyUserId: string
  /**
   * 申请方id
   */
  applicantId: string
  /**
   * 申请原因
   */
  applyReason: string
  /**
   * 审批人id
   */
  auditUserId: string
  /**
   * 审批人名字
   */
  auditUserName: string
  /**
   * 审批方id
   */
  auditPartyId: string
  /**
   * 审批信息
   */
  auditMessage: string
  /**
   * 审批状态
1-待处理 2-通过 3-不通过 4-取消
   */
  auditStatus: number
  /**
   * 申请附件
   */
  applyAttaches: Array<AttachesResponse>
  /**
   * 优惠申请时间
   */
  applyTime: string
  /**
   * 优惠通过时间
   */
  passTime: string
  /**
   * 优惠拒绝时间
   */
  refuseTime: string
  /**
   * 优惠内容
   */
  productDiscountApplyContent: CommodityDiscountApplyContentResponse
}

export class CommodityDiscountApplyContentResponse {
  /**
   * 产品分销授权关系id
   */
  productDistributionAuthId: string
  /**
   * 允许报名人数数量约束 | 1-不限 2-区间
   */
  quantityConstraint: number
  /**
   * 允许报名人数最小值
   */
  discountMinQuantity: number
  /**
   * 允许报名人数最大值
   */
  discountMaxQuantity: number
  /**
   * 剩余可报名人数
   */
  remainQuantity: number
  /**
   * 已报名人数
   */
  signedNumber: number
  /**
   * 优惠周期约束 | 1-周期 2-长期
   */
  discountDateConstraint: number
  /**
   * 优惠开始时间
   */
  discountStartTime: string
  /**
   * 优惠结束时间
   */
  discountEndTime: string
  /**
   * 优惠价格
   */
  discountPrice: number
  /**
   * 优惠状态
1-开启 2-关闭
与优惠申请内容状态有关、与优惠周期约束、优惠开始时间、优惠结束时间有关
   */
  discountStatus: number
  /**
   * 优惠地区树id
   */
  discountRegionTreeId: string
  /**
   * 优惠地市
   */
  regionStatistics: Array<RegionTreeResponse>
  /**
   * 优惠商品信息
   */
  discountCommodity: DiscountCommodityResponse
}

export class DiscountCommodityResponse {
  /**
   * 分销商商品id
   */
  distributorCommodityId: string
  /**
   * 商品id
   */
  commodityId: string
  /**
   * 分销商商品销售名称
   */
  saleTitle: string
  /**
   * 分销商商品价格
   */
  price: number
  /**
   * 授权价格类型 1-固定 2-区间
   */
  priceType: number
  /**
   * 最大价格
   */
  maxPrice: number
  /**
   * 最小价格
   */
  minPrice: number
  /**
   * 分销商商品图片路径
   */
  commodityPicturePath: string
  /**
   * 商品-培训方案资源类型
   */
  schemeResourceInfo: SchemeResourceInfoResponse
  /**
   * 分销商商品sku属性
   */
  propertyList: Array<PropertyResponse>
  /**
   * 分销商品来源类型
1-产品分销授权
   */
  commoditySourceType: number
  /**
   * 分销商品来源id
1-产品分销授权id
   */
  commoditySourceId: string
  /**
   * 当前分销商ID
   */
  distributorId: string
  /**
   * 当前分销商名称
   */
  distributorName: string
  /**
   * 当前分销级别 1-一级 2-二级
   */
  distributionLevel: number
  /**
   * 当前分销商类型
   */
  distributionType: number
  /**
   * 当前分销商单位名称
   */
  distributorUnitName: string
  /**
   * 商品授权状态
0-停用 1-正常
   */
  distributionStatus: number
  /**
   * 上级分销商ID
   */
  superDistributorId: string
  /**
   * 上级分销商全称
   */
  superDistributorFullName: string
  /**
   * 上级分销商简称
   */
  superDistributorShortName: string
  /**
   * 期别数
   */
  issueNum: number
}

/**
 * <AUTHOR> linq
@date : 2024-09-23 19:07
@description：地区数据响应体
 */
export class RegionDataResponse {
  /**
   * 地区树id
   */
  regionTreeId: string
  /**
   * 地区code
   */
  regionCode: string
  /**
   * 父级地区code
   */
  parentRegionCode: string
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 省id
   */
  provinceId: string
  /**
   * 市id
   */
  cityId: string
  /**
   * 县id
   */
  countyId: string
  /**
   * 层级 （1-省级 2-市级 3-区县级）
   */
  level: number
}

/**
 * 业务员对接的分销商关系
 */
export class DockingSalesmanRelationshipResponse {
  /**
   * 业务员ID
   */
  salesmanId: string
  /**
   * 对接开始时间
   */
  dockingStartTime: string
  /**
   * 对接结束时间
   */
  dockingEndTime: string
  /**
   * 来源合同的分销商id
   */
  distributorId: string
  /**
   * 分销商名称
   */
  distributorName: string
  /**
   * 分销商身份证
   */
  distributorIdCard: string
  /**
   * 分销商电话
   */
  distributorPhone: string
  /**
   * 分销商统一社会信用代码（企业类型）
   */
  distributorCreditCode: string
  /**
   * 来源合同分销商合作伙伴类型（供应商、分销商类型）1-个人 2-企业
   */
  distributorPartnerType: number
  /**
   * 分销合同状态 0-有效 1-终止
   */
  status: number
  /**
   * 分销地区
   */
  distributorRegionList: Array<RegionResponse>
  /**
   * 合同id
   */
  contractId: string
}

export class SalesmanResponse {
  /**
   * 业务员ID
   */
  salesmanId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 服务商ID
   */
  servicerId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 姓名
   */
  name: string
  /**
   * 手机号
   */
  phoneNumber: string
  /**
   * 状态：1-启用，2-禁用
   */
  status: string
  /**
   * 备注
   */
  remark: string
  /**
   * 所属供应商ID
   */
  supplierId: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
}

/**
 * 功能描述 : 分销服务商信息
 */
export class DistributionServicerResponse {
  /**
   * 服务商 Id
   */
  servicerId: string
  /**
   * 统一社会信用社代码
   */
  code: string
  /**
   * 服务商名称
   */
  distributorName: string
  /**
   * 附件
   */
  attach: Array<ServicerAttachResponse>
  /**
   * 分销商域名
   */
  domainInfos: Array<DomainInfo>
  /**
   * 管理账户id
   */
  adminAccountId: string
  /**
   * 管理员账户名
   */
  adminAccountName: string
  /**
   * 管理员名称
   */
  adminName: string
  /**
   * 管理员手机号
   */
  adminPhone: string
  /**
   * 身份证idcard
   */
  idCard: string
  /**
   * 服务商类型 1-个人 2- 企业
   */
  partnerType: number
}

/**
 * 分销商域名范围类
 */
export class DomainInfo {
  /**
   * 域名
   */
  domainName: string
  /**
   * 客户端类型 1-web 2-h5
   */
  clientType: number
  /**
   * 域名类型 |1-华博域名 2-业主自由域名
   */
  domainNameType: number
}

/**
 * 商品分销商开通统计返回
<AUTHOR>
 */
export class CommodityDistributorOpenReportInSupplierResponse {
  /**
   * 商品ID
   */
  commodityId: string
  /**
   * 商品名称
   */
  commodityName: string
  /**
   * 商品所属网校ID
   */
  servicerId: string
  /**
   * 商品所属网校名
   */
  servicerName: string
  /**
   * 分销商商品sku属性
   */
  propertyList: Array<PropertyResponse>
  /**
   * 商品价格
   */
  price: number
  /**
   * 供应商ID
   */
  supplierId: string
  /**
   * 分销商ID
   */
  distributorId: string
  /**
   * 分销商名称
   */
  distributorName: string
  /**
   * 分销等级
   */
  distributionLevel: number
  /**
   * 分销定价方式冗余统计信息
   */
  distributionPricingInfo: Array<DistributionPricingResponse>
}

/**
 * 商品分销商开通统计返回
<AUTHOR>
 */
export class CommodityDistributorOpenReportResponse {
  /**
   * 商品ID
   */
  commodityId: string
  /**
   * 商品名称
   */
  commodityName: string
  /**
   * 商品所属网校ID
   */
  servicerId: string
  /**
   * 商品所属网校名
   */
  servicerName: string
  /**
   * 分销商商品sku属性
   */
  propertyList: Array<PropertyResponse>
  /**
   * 商品价格
   */
  price: number
  /**
   * 供应商ID
   */
  supplierId: string
  /**
   * 商品各授权分销商的销售统计
   */
  distributorOpenReportList: Array<DistributorOpenReportResponse>
}

/**
 * 商品开通统计返回
<AUTHOR>
 */
export class CommodityOpenReportResponse {
  /**
   * 商品ID
   */
  commodityId: string
  /**
   * 商品名称
   */
  commodityName: string
  /**
   * 商品所属网校ID
   */
  servicerId: string
  /**
   * 授权供应商id
   */
  supplierId: string
  /**
   * 授权供应商名称
   */
  supplierName: string
  /**
   * 商品所属网校名
   */
  servicerName: string
  /**
   * 分销商商品sku属性
   */
  propertyList: Array<PropertyResponse>
  /**
   * 商品价格
   */
  price: number
  /**
   * 商品合计统计
   */
  statisticSummary: ReportNumStatisticResponse
  /**
   * 商品购买渠道数据
   */
  purchaseNList: Array<PurchaseStatisticResponse>
}

/**
 * 分销商销售统计
包含各渠道的统计
<AUTHOR>
 */
export class DistributorSellStatisticIncludedPurchaseResponse {
  /**
   * 分销商ID
   */
  distributorId: string
  /**
   * 分销商名称
   */
  distributorName: string
  /**
   * 分销商简称
   */
  distributorShortName: string
  /**
   * 分销商销售合计信息
   */
  distributorSellSummary: DistributionSellStatisticResponse
  /**
   * 分销商销售渠道统计信息
   */
  distributionSellPurchaseStatisticList: Array<DistributionSellPurchaseStatisticResponse>
}

/**
 * 交易统计信息
<AUTHOR>
 */
export class TradeStatisticResponse {
  /**
   * 交易完成总数量
   */
  totalTradeCompleteNum: number
  /**
   * 退班总数量
   */
  totalReturnCompleteNum: number
  /**
   * 换出总数量
   */
  totalExchangeOutNum: number
  /**
   * 换入总数量
   */
  totalExchangeInNum: number
  /**
   * 总金额
   */
  totalAmount: number
  /**
   * 净交易成功数量
   */
  netTradeSuccessCount: number
  /**
   * 净成交金额
   */
  netTradeSuccessAmount: number
}

/**
 * 交易合计统计信息
<AUTHOR>
 */
export class TradeSummaryStatisticResponse {
  /**
   * 合计类型 | 1、合计 2、个人缴费-线上支付 3、集体缴费-线上支付 4、集体缴费线下支付 5、导入开通-线下支付
   */
  summaryType: number
  /**
   * 合计统计信息
   */
  summaryInfo: TradeSummaryInfoResponse
}

/**
 * 分销定价冗余模型
 */
export class DistributionPricingResponse {
  /**
   * 交易净成交金额
   */
  tradeNetAmount: number
  /**
   * 分销定价方式 | 0、授权定价 1、优惠申请
   */
  pricingType: number
  /**
   * 缴费渠道统计信息
   */
  useSpecialChannelInfos: Array<ChannelInfoDto>
  /**
   * 开通统计合计
   */
  discountSummary: ReportNumStatisticDto
}

/**
 * 分销销售支付方式统计信息
<AUTHOR>
 */
export class DistributionSellPayTypeStatisticResponse {
  /**
   * 支付方式
@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderTypes
   */
  payType: number
  /**
   * 统计信息
   */
  statisticInfo: DistributionSellStatisticResponse
}

/**
 * 分销销售缴费渠道统计信息
<AUTHOR>
 */
export class DistributionSellPurchaseStatisticResponse {
  /**
   * 购买渠道
1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
   */
  purchaseChannel: number
  /**
   * 各支付方式统计信息
   */
  payTypeStatisticResponseList: Array<DistributionSellPayTypeStatisticResponse>
}

/**
 * 分销销售统计信息
<AUTHOR>
 */
export class DistributionSellStatisticResponse {
  /**
   * 交易成功数量
   */
  tradeSuccessCount: number
  /**
   * 退货数量
   */
  returnCount: number
  /**
   * 净交易成功数量
   */
  netTradeSuccessCount: number
  /**
   * 净成交金额
   */
  netTradeSuccessAmount: number
}

/**
 * 分销商开通统计结果
<AUTHOR>
 */
export class DistributorOpenReportResponse {
  /**
   * 分销商ID
   */
  distributorId: string
  /**
   * 分销商名称
   */
  distributorName: string
  /**
   * 分销商等级 | 1-一级，2-二级
   */
  distributionLevel: number
  /**
   * 分销定价方式冗余统计信息
   */
  distributionPricingInfo: Array<DistributionPricingResponse>
}

/**
 * 支付方式统计信息
<AUTHOR>
 */
export class PayTypeStatisticResponse {
  /**
   * 支付方式
@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderTypes
   */
  payType: number
  /**
   * 统计信息
   */
  statisticInfo: ReportNumStatisticResponse
}

/**
 * 缴费渠道统计信息
<AUTHOR>
 */
export class PurchaseStatisticResponse {
  /**
   * 购买渠道
1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
   */
  purchaseChannel: number
  /**
   * 各支付方式统计信息
   */
  payTypeStatisticResponseList: Array<PayTypeStatisticResponse>
}

/**
 * 交易统计情况
<AUTHOR>
 */
export class ReportNumStatisticResponse {
  /**
   * 交易成功数量
   */
  tradeSuccessCount: number
  /**
   * 退货数量
   */
  returnCount: number
  /**
   * 换入数量
   */
  exchangeInCount: number
  /**
   * 换出数量
   */
  exchangeOutCount: number
  /**
   * 净交易成功数量
   */
  netTradeSuccessCount: number
}

/**
 * 合计
 */
export class TradeSummaryInfoResponse {
  /**
   * 交易完成总数量
   */
  totalTradeCompleteNum: number
  /**
   * 退班总数量
   */
  totalReturnCompleteNum: number
  /**
   * 换出总数量
   */
  totalExchangeOutNum: number
  /**
   * 换入总数量
   */
  totalExchangeInNum: number
  /**
   * 净交易成功数量
   */
  totalTradeCompleteNetNum: number
}

/**
 * 所属集体缴费信息
 */
export class BatchOwnerResponse {
  /**
   * 所属集体缴费单位id
   */
  unitId: string
  /**
   * 所属集体缴费用户id
   */
  userId: string
}

/**
 * 数据归属信息
<AUTHOR>
@version 1.0
@date 2022/1/8 17:29
 */
export class OwnerResponse {
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属服务商类型
<p>
1：培训机构
2：课件供应商
3：渠道商
4：参训单位
   */
  servicerType: number
  /**
   * 所属服务商id
   */
  servicerId: string
  /**
   * 所属集体缴费信息
   */
  batchOwner: BatchOwnerResponse
}

export class RegionResponse1 {
  /**
   * 地区：省
   */
  province: string
  /**
   * 地区：市
   */
  city: string
  /**
   * 地区：区
   */
  county: string
}

/**
 * 用户属性
<AUTHOR>
@version 1.0
@date 2022/1/15 11:01
 */
export class UserPropertyResponse {
  /**
   * 所属地区
   */
  region: RegionResponse1
  /**
   * 下单地区
   */
  payOrderRegion: RegionResponse1
}

/**
 * 用户信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:00
 */
export class UserResponse {
  /**
   * 用户id
   */
  userId: string
  /**
   * 账户id
   */
  accountId: string
  /**
   * 用户属性
   */
  userProperty: UserPropertyResponse
}

export class DistributorPortalInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<DistributorPortalInfoResponse>
}

export class CommodityDistributorOpenReportResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CommodityDistributorOpenReportResponse>
}

export class CommodityDistributorOpenReportInSupplierResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CommodityDistributorOpenReportInSupplierResponse>
}

export class CommodityOpenReportResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CommodityOpenReportResponse>
}

export class DistributorCommodityAndRelationResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<DistributorCommodityAndRelationResponse>
}

export class DistributionAuthorizationDistributorResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<DistributionAuthorizationDistributorResponse>
}

export class DistributorIssueCommodityResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<DistributorIssueCommodityResponse>
}

export class DistributorSellStatisticIncludedPurchaseResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<DistributorSellStatisticIncludedPurchaseResponse>
}

export class DistributionRelationshipResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<DistributionRelationshipResponse>
}

export class DockingSalesmanRelationshipResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<DockingSalesmanRelationshipResponse>
}

export class OnlineSchoolDistributionRelationshipResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OnlineSchoolDistributionRelationshipResponse>
}

export class CommodityDiscountApplyResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CommodityDiscountApplyResponse>
}

export class PortalInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PortalInfoResponse>
}

export class SalesmanResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SalesmanResponse>
}

export class NewsSimpleResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<NewsSimpleResponse>
}

export class StudentSchemeLearningResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<StudentSchemeLearningResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param dto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async convertDistributionContractResponse(
    dto: ProductConsignmentContractDto,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.convertDistributionContractResponse,
    operation?: string
  ): Promise<Response<DistributionContractResponse>> {
    return commonRequestApi<DistributionContractResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { dto },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商 -导出批次单明细
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchOrderDetailInDistributor(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchOrderDetailInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商 - 导出批次单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchOrderInDistributor(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchOrderInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商 -导出批次对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReconciliationInDistributor(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReconciliationInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次退货单明细
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReturnOrderDetailExcelInDistributor(
    params: { request?: BatchReturnOrderRequest; sort?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReturnOrderDetailExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReturnOrderExcelInDistributor(
    params: { request?: BatchReturnOrderRequest; sort?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReturnOrderExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次退货报名对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReturnReconciliationExcelInDistributor(
    params: { request?: BatchReturnOrderRequest; sort?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReturnReconciliationExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商  - 导出个人订单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOrderExcelInDistributor(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOrderExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商  - 导出个人对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReconciliationExcelInDistributor(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReconciliationExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商 -导出个人退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnOrderExcelInDistributor(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReturnOrderExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商 -导出个人报名退货对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnReconciliationExcelInDistributor(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReturnReconciliationExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据推广门户域名获取 当前 推广门户id
   * portalType  1-web端 2-移动端
   * @param query 查询 graphql 语法文档
   * @param portalType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCurrentPortalIdInDustributor(
    portalType: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCurrentPortalIdInDustributor,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { portalType },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据优惠申请id获取优惠地区树
   * @param applyId
   * @return
   * @param query 查询 graphql 语法文档
   * @param applyId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDiscountRegionInSubject(
    applyId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDiscountRegionInSubject,
    operation?: string
  ): Promise<Response<Array<RegionTreeResponse>>> {
    return commonRequestApi<Array<RegionTreeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { applyId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据合同ID获取合同信息
   * @param contractId
   * @return
   * @param query 查询 graphql 语法文档
   * @param contractId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDistributionContractByContractId(
    contractId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDistributionContractByContractId,
    operation?: string
  ): Promise<Response<DistributionContractDetailResponse>> {
    return commonRequestApi<DistributionContractDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { contractId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 查询当前登录分销商信息接口
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDistributionServicerInfoInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDistributionServicerInfoInMyself,
    operation?: string
  ): Promise<Response<DistributionServicerResponse>> {
    return commonRequestApi<DistributionServicerResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 项目级-分销服务商详情查询接口
   * @param query 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getDistributionServicerInfoInSubProject(
    servicerId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDistributionServicerInfoInSubProject,
    operation?: string
  ): Promise<Response<DistributionServicerResponse>> {
    return commonRequestApi<DistributionServicerResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据分销商商品id获取分销商商品详情
   * @param query 查询 graphql 语法文档
   * @param distributorCommodityId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDistributorCommodityInSubProject(
    distributorCommodityId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDistributorCommodityInSubProject,
    operation?: string
  ): Promise<Response<DistributorCommodityResponse>> {
    return commonRequestApi<DistributorCommodityResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { distributorCommodityId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商 - 查询已创建的推广门户
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDistributorPortalInfosInSupplier(
    params: { page?: Page; portalRequestInSupplier?: PortalRequestInSupplier },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getDistributorPortalInfosInSupplier,
    operation?: string
  ): Promise<Response<DistributorPortalInfoResponsePage>> {
    return commonRequestApi<DistributorPortalInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据地区树id查询最末级详细地区
   * @param regionTreeId 地区树id
   * @param query 查询 graphql 语法文档
   * @param regionTreeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getLastLevelRegionDetailByRegionTreeId(
    regionTreeId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getLastLevelRegionDetailByRegionTreeId,
    operation?: string
  ): Promise<Response<Array<RegionDataResponse>>> {
    return commonRequestApi<Array<RegionDataResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { regionTreeId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询资讯详情
   * @param newId
   * @return
   * @param query 查询 graphql 语法文档
   * @param newId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getNewsDetail(
    newId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getNewsDetail,
    operation?: string
  ): Promise<Response<NewsDetailResponse>> {
    return commonRequestApi<NewsDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { newId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取推广门户线下集体报名配置
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOfflineCollectiveByPotalIdInDistributor(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOfflineCollectiveByPotalIdInDistributor,
    operation?: string
  ): Promise<Response<PortalOfflineCollectiveSignUpSettingResponse>> {
    return commonRequestApi<PortalOfflineCollectiveSignUpSettingResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取推广门户是否开启
   * @param portalId
   * @return
   * @param query 查询 graphql 语法文档
   * @param portalId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getOfflineRegistrationConfigByPortalId(
    portalId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOfflineRegistrationConfigByPortalId,
    operation?: string
  ): Promise<Response<OfflineRegistrationConfigResponse>> {
    return commonRequestApi<OfflineRegistrationConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { portalId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查看定价方案的推广门户
   * @param requset
   * @return
   * @param query 查询 graphql 语法文档
   * @param requset 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPolicyPortalList(
    requset: PolicyPortalRequset,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPolicyPortalList,
    operation?: string
  ): Promise<Response<Array<PolicyPortalResponse>>> {
    return commonRequestApi<Array<PolicyPortalResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { requset },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取当前分销商下的所有门户
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPortalInDistributor(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPortalInDistributor,
    operation?: string
  ): Promise<Response<Array<PortalInDistributorResponse>>> {
    return commonRequestApi<Array<PortalInDistributorResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取推广门户下的展示的定价方案数量
   * @param portalIds 推广门户id
   * @return
   * @param query 查询 graphql 语法文档
   * @param portalIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPortalPricingCount(
    portalIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPortalPricingCount,
    operation?: string
  ): Promise<Response<Array<PricingPolicyCommodityCountResponse>>> {
    return commonRequestApi<Array<PricingPolicyCommodityCountResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { portalIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查看海报
   * @param identifier
   * @return
   * @param query 查询 graphql 语法文档
   * @param identifier 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPosterConfigurationInDistributor(
    identifier: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPosterConfigurationInDistributor,
    operation?: string
  ): Promise<Response<PosterConfigurationResponse>> {
    return commonRequestApi<PosterConfigurationResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { identifier },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据优惠申请id获取优惠申请信息
   * @param applyId 申请id
   * @return
   * @param query 查询 graphql 语法文档
   * @param applyId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getProductDiscountApplyInfoInSubject(
    applyId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getProductDiscountApplyInfoInSubject,
    operation?: string
  ): Promise<Response<CommodityDiscountApplyResponse>> {
    return commonRequestApi<CommodityDiscountApplyResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { applyId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-推广门户详情
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPromotionPortalInfoInDistributor(
    request: PortalInfoDetailRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPromotionPortalInfoInDistributor,
    operation?: string
  ): Promise<Response<PortalInfoDetailResponse>> {
    return commonRequestApi<PortalInfoDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据优惠申请id 查询地区(供应商)
   * @param applyId
   * @param parentRegionId
   * @param regionLevel
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRegionByApplyIdInSupplier(
    params: { applyId?: string; parentRegionId?: string; regionLevel?: number },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRegionByApplyIdInSupplier,
    operation?: string
  ): Promise<Response<Array<RegionTreeResponse>>> {
    return commonRequestApi<Array<RegionTreeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据合同id查分销地区(分销商)
   * @param contractId 合同id
   * @param query 查询 graphql 语法文档
   * @param contractId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRegionByContractIdInDistributor(
    contractId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRegionByContractIdInDistributor,
    operation?: string
  ): Promise<Response<Array<RegionTreeResponse>>> {
    return commonRequestApi<Array<RegionTreeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { contractId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据合同id查分销地区(供应商)
   * @param contractId     合同id
   * @param parentRegionId 父级地区id
   * @param regionLevel    地区查询等级 0-查全部地区  1-只查省级  2-只查市级  3-只查区县级
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRegionByContractIdInSupplier(
    params: { contractId?: string; parentRegionId?: string; regionLevel?: number },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRegionByContractIdInSupplier,
    operation?: string
  ): Promise<Response<Array<RegionTreeResponse>>> {
    return commonRequestApi<Array<RegionTreeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据地区树id查详细地区
   * @param regionTreeId 地区树id
   * @param query 查询 graphql 语法文档
   * @param regionTreeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRegionDetailByRegionTreeId(
    regionTreeId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRegionDetailByRegionTreeId,
    operation?: string
  ): Promise<Response<Array<RegionTreeResponse>>> {
    return commonRequestApi<Array<RegionTreeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { regionTreeId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取业务员详情
   * @param salesmanId
   * @param query 查询 graphql 语法文档
   * @param salesmanId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSalesman(
    salesmanId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSalesman,
    operation?: string
  ): Promise<Response<SalesmanResponse>> {
    return commonRequestApi<SalesmanResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { salesmanId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询是否当前分销商下（已发布的）的推广子门户只有一个
   * 唯一时 返回门户id  不唯一时返回空
   * portalType 1-web 2-移动端
   * @return
   * @param query 查询 graphql 语法文档
   * @param portalType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async ifOnlyPorttalInDustributor(
    portalType: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.ifOnlyPorttalInDustributor,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { portalType },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 判断网校商品是否已授权给分销商
   * @param commodityIdList 网校商品id列表
   * @param distributorId 分销商id
   * @return 已被授权的网校商品id列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async judgeCommodityAuthorized(
    params: { commodityIdList?: Array<string>; distributorId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.judgeCommodityAuthorized,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 判断定价方案是否已添加到渠道
   * @param pricingSchemeIdList 定价方案id列表
   * @param channelId 渠道id
   * @return 已被添加到门户的定价方案id列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listAddedPortalPricingPolicy(
    params: { pricingSchemeIdList?: Array<string>; channelId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listAddedPortalPricingPolicy,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-获取我的分销商商品分销地区属性集合
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listMyDistributorCommodityRegionInDistributor(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listMyDistributorCommodityRegionInDistributor,
    operation?: string
  ): Promise<Response<Array<CommoditySkuRegionPropertyNodeResponse>>> {
    return commonRequestApi<Array<CommoditySkuRegionPropertyNodeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分销商-获取我的分销商商品sku属性集合
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listMyDistributorCommoditySkuPropertyInDistributor(
    request: DistributorCommodityAndRelationRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listMyDistributorCommoditySkuPropertyInDistributor,
    operation?: string
  ): Promise<Response<DistributorCommodityPropertyCollectionResponse>> {
    return commonRequestApi<DistributorCommodityPropertyCollectionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-获取当前分销商下的定价策略的商品sku属性集合
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listPricingSchemeSkuPropertyInDistributor(
    request: DistributorCommodityAndRelationRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listPricingSchemeSkuPropertyInDistributor,
    operation?: string
  ): Promise<Response<DistributorCommodityPropertyCollectionResponse>> {
    return commonRequestApi<DistributorCommodityPropertyCollectionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商-获取当前供应商下的定价方案的商品sku属性集合
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listPricingSchemeSkuPropertyInSupplier(
    request: DistributorCommodityAndRelationRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listPricingSchemeSkuPropertyInSupplier,
    operation?: string
  ): Promise<Response<DistributorCommodityPropertyCollectionResponse>> {
    return commonRequestApi<DistributorCommodityPropertyCollectionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-获取当前分销商的优惠申请sku属性
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listProductDiscountApplySkuPropertyInDistributor(
    request: DistributorCommodityAndRelationRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listProductDiscountApplySkuPropertyInDistributor,
    operation?: string
  ): Promise<Response<DistributorCommoditySkuPropertyCollectionResponse>> {
    return commonRequestApi<DistributorCommoditySkuPropertyCollectionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分销商分页获取商品分销商开通统计信息
   * 商品+分销商+是否有优惠+缴费渠道+支付方式维度的开通统计
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCommoditySkuDistributorOpenReportInDistributor(
    params: { page?: Page; request?: StatisticTradeRecordRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommoditySkuDistributorOpenReportInDistributor,
    operation?: string
  ): Promise<Response<CommodityDistributorOpenReportResponsePage>> {
    return commonRequestApi<CommodityDistributorOpenReportResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商分页获取商品分销商开通统计信息
   * 商品+分销商+是否有优惠+缴费渠道+支付方式维度的开通统计
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCommoditySkuDistributorOpenReportInSupplier(
    params: { page?: Page; request?: StatisticTradeRecordRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommoditySkuDistributorOpenReportInSupplier,
    operation?: string
  ): Promise<Response<CommodityDistributorOpenReportInSupplierResponsePage>> {
    return commonRequestApi<CommodityDistributorOpenReportInSupplierResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商分页获取商品开通统计信息
   * 商品+缴费渠道+支付方式维度的开通统计接口
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCommoditySkuOpenReportInSupplier(
    params: { page?: Page; request?: StatisticTradeRecordRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommoditySkuOpenReportInSupplier,
    operation?: string
  ): Promise<Response<CommodityOpenReportResponsePage>> {
    return commonRequestApi<CommodityOpenReportResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-分页查询分销商品
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageDistributorCommodityBasicInSubject(
    params: { page?: Page; request?: DistributorCommodityAndRelationRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageDistributorCommodityBasicInSubject,
    operation?: string
  ): Promise<Response<DistributorCommodityAndRelationResponsePage>> {
    return commonRequestApi<DistributorCommodityAndRelationResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询当前供应商下已授权商品
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageDistributorCommodityInSupplier(
    params: {
      page?: Page
      request?: DistributorCommodityAndRelationRequest
      sortRequests?: Array<DistributorCommoditySortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageDistributorCommodityInSupplier,
    operation?: string
  ): Promise<Response<DistributorCommodityAndRelationResponsePage>> {
    return commonRequestApi<DistributorCommodityAndRelationResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取当前供应商下的分销商列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageDistributorInSupplier(
    params: { page?: Page; request?: DistributionAuthorizationDistributorRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageDistributorInSupplier,
    operation?: string
  ): Promise<Response<DistributionAuthorizationDistributorResponsePage>> {
    return commonRequestApi<DistributionAuthorizationDistributorResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商分页查询可更换的分销期别列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageDistributorIssueCommodityInDistributor(
    params: { page?: Page; request?: DistributorIssueCommodityRequest; sortRequests?: Array<SaleCommoditySortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageDistributorIssueCommodityInDistributor,
    operation?: string
  ): Promise<Response<DistributorIssueCommodityResponsePage>> {
    return commonRequestApi<DistributorIssueCommodityResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询可更换的分销期别列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageDistributorIssueCommodityInServicer(
    params: { page?: Page; request?: DistributorIssueCommodityRequest; sortRequests?: Array<SaleCommoditySortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageDistributorIssueCommodityInServicer,
    operation?: string
  ): Promise<Response<DistributorIssueCommodityResponsePage>> {
    return commonRequestApi<DistributorIssueCommodityResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商获取分销商销售统计
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageDistributorSellStatisticInSupplier(
    params: { page?: Page; request?: StatisticTradeRecordRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageDistributorSellStatisticInSupplier,
    operation?: string
  ): Promise<Response<DistributorSellStatisticIncludedPurchaseResponsePage>> {
    return commonRequestApi<DistributorSellStatisticIncludedPurchaseResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取当前供应商下的分销商列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageDistributorWithOnlineSchoolInSupplier(
    params: { page?: Page; request?: DistributionRelationshipRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageDistributorWithOnlineSchoolInSupplier,
    operation?: string
  ): Promise<Response<DistributionRelationshipResponsePage>> {
    return commonRequestApi<DistributionRelationshipResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取当前供应商下的业务员对接记录列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageDockingDistributorSalesmanInSupplier(
    params: { page?: Page; request?: DockingSalesmanRelationshipRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageDockingDistributorSalesmanInSupplier,
    operation?: string
  ): Promise<Response<DockingSalesmanRelationshipResponsePage>> {
    return commonRequestApi<DockingSalesmanRelationshipResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-分页查询我的分销商商品
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageMyDistributorCommodityInDistributor(
    params: {
      page?: Page
      request?: DistributorCommodityAndRelationRequest
      sortRequests?: Array<DistributorCommoditySortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageMyDistributorCommodityInDistributor,
    operation?: string
  ): Promise<Response<DistributorCommodityAndRelationResponsePage>> {
    return commonRequestApi<DistributorCommodityAndRelationResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取当前供应商下的存在分销关系的合作网校列表
   * 供应商-分销商管理-分销设置
   * @param page
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOnlineSchoolWithDistributionRelationInSupplier(
    params: {
      page?: Page
      request?: OnlineSchoolDistributionRelationshipRequest
      sort?: Array<DistributionRelationshipSortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOnlineSchoolWithDistributionRelationInSupplier,
    operation?: string
  ): Promise<Response<OnlineSchoolDistributionRelationshipResponsePage>> {
    return commonRequestApi<OnlineSchoolDistributionRelationshipResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-分页查询优惠申请信息
   * Params:
   * page – 分页条件 request – 优惠申请筛选条件
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageProductDiscountApplyInfoInDistributor(
    params: {
      page?: Page
      request?: ProductDiscountApplyRequest
      sortRequests?: Array<ProductDiscountApplySortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageProductDiscountApplyInfoInDistributor,
    operation?: string
  ): Promise<Response<CommodityDiscountApplyResponsePage>> {
    return commonRequestApi<CommodityDiscountApplyResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商-分页查询优惠申请信息
   * @param page    分页条件
   * @param request 优惠申请筛选条件
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageProductDiscountApplyInfoInSupplier(
    params: {
      page?: Page
      request?: ProductDiscountApplyRequest
      sortRequests?: Array<ProductDiscountApplySortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageProductDiscountApplyInfoInSupplier,
    operation?: string
  ): Promise<Response<CommodityDiscountApplyResponsePage>> {
    return commonRequestApi<CommodityDiscountApplyResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-定价方案-定价方案-列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageProductPricingSchemeInDistributor(
    params: {
      page?: Page
      request?: DistributorCommodityAndRelationRequest
      sortRequests?: Array<DistributorCommoditySortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageProductPricingSchemeInDistributor,
    operation?: string
  ): Promise<Response<DistributorCommodityAndRelationResponsePage>> {
    return commonRequestApi<DistributorCommodityAndRelationResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商-定价方案-定价方案-列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageProductPricingSchemeInSupplier(
    params: {
      page?: Page
      request?: DistributorCommodityAndRelationRequest
      sortRequests?: Array<DistributorCommoditySortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageProductPricingSchemeInSupplier,
    operation?: string
  ): Promise<Response<DistributorCommodityAndRelationResponsePage>> {
    return commonRequestApi<DistributorCommodityAndRelationResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-推广门户列表
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pagePromotionPortalInfoInDistributor(
    params: { page?: Page; request?: PortalInfoRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pagePromotionPortalInfoInDistributor,
    operation?: string
  ): Promise<Response<PortalInfoResponsePage>> {
    return commonRequestApi<PortalInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取当前供应商下的业务员列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSalesmanInSupplier(
    params: { page?: Page; request?: SalesmanRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSalesmanInSupplier,
    operation?: string
  ): Promise<Response<SalesmanResponsePage>> {
    return commonRequestApi<SalesmanResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取资讯列表
   * @param queryRequest
   * @param page
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSimpleNews(
    params: { queryRequest?: NewsSimpleQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSimpleNews,
    operation?: string
  ): Promise<Response<NewsSimpleResponsePage>> {
    return commonRequestApi<NewsSimpleResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取当前服务商下的学员培训方案学习列表
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeLearningInDistributor(
    params: {
      page?: Page
      request?: StudentSchemeLearningRequestInDistributor
      sort?: Array<StudentSchemeLearningSortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentSchemeLearningInDistributor,
    operation?: string
  ): Promise<Response<StudentSchemeLearningResponsePage>> {
    return commonRequestApi<StudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取当前服务商下的学员培训方案学习列表
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeLearningInSupplier(
    params: {
      page?: Page
      request?: StudentSchemeLearningRequestInSupplier
      sort?: Array<StudentSchemeLearningSortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentSchemeLearningInSupplier,
    operation?: string
  ): Promise<Response<StudentSchemeLearningResponsePage>> {
    return commonRequestApi<StudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-统计分销商商品数量
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticAuthorizedDistributorCommodityInDistributor(
    request: DistributorCommodityAndRelationRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticAuthorizedDistributorCommodityInDistributor,
    operation?: string
  ): Promise<Response<Array<DistributorCommodityStatisticResponse>>> {
    return commonRequestApi<Array<DistributorCommodityStatisticResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商获取交易统计信息
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticTradeRecordInDistributor(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticTradeRecordInDistributor,
    operation?: string
  ): Promise<Response<TradeStatisticResponse>> {
    return commonRequestApi<TradeStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商获取交易统计信息
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticTradeRecordInSupplier(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticTradeRecordInSupplier,
    operation?: string
  ): Promise<Response<TradeStatisticResponse>> {
    return commonRequestApi<TradeStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商获取交易合计统计信息
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticTradeSummaryInDistributor(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticTradeSummaryInDistributor,
    operation?: string
  ): Promise<Response<Array<TradeSummaryStatisticResponse>>> {
    return commonRequestApi<Array<TradeSummaryStatisticResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async transformDistributorPortalInfoResponse(
    params: { servicerPortalDto?: ServicerPortalDto; unitName?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.transformDistributorPortalInfoResponse,
    operation?: string
  ): Promise<Response<DistributorPortalInfoResponse>> {
    return commonRequestApi<DistributorPortalInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param servicerPortalDto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async transformPortalInfoResponse(
    servicerPortalDto: ServicerPortalDto,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.transformPortalInfoResponse,
    operation?: string
  ): Promise<Response<PortalInfoResponse>> {
    return commonRequestApi<PortalInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerPortalDto },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
