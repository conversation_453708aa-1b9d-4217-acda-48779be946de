import CourseInCoursePool from '@api/service/common/models/course-pool/CourseInCoursePool'

/**
 * 更新课程包
 * <AUTHOR> update 2021/1/28  TODO
 */
class CoursePoolUpdate {
  /**
   * 课程供应商id【必填】
   */
  coursewareSupplierId?: string
  /**
   * 课程包id
   */
  id?: string
  /**
   * 必填，课程池名称
   */
  poolName?: string
  /**
   * 排序序号
   */
  sequence: number
  /**
   * 课程池描述
   */
  poolDescription?: string
  /**
   * 展示名称
   */
  showName?: string
  /**
   * 包内课程
   */
  courseInPool?: Array<CourseInCoursePool>
}

export default CoursePoolUpdate
