<template>
  <el-drawer :title="title" :visible.sync="showDrawer" size="800px" custom-class="m-drawer" @open="onOpen">
    <div class="drawer-bd">
      <el-form
        ref="baseForm"
        :model="questionnaire"
        :rules="questionnaireRules"
        label-width="auto"
        class="m-form f-mt10 f-mlr20"
      >
        <el-form-item label="问卷模板：" required>
          {{ questionnaire.templateName }}
          <el-button type="text" class="f-ml10" @click="preview(questionnaire.templateId)">预览</el-button>
          <el-button
            type="text"
            class="f-ml10"
            v-if="!isUpdate"
            :disabled="isDisabled"
            @click="$emit('change-template')"
            >替换</el-button
          >
        </el-form-item>
        <el-form-item label="问卷名称：" prop="questionnaireName">
          <el-input v-model="questionnaire.questionnaireName" :disabled="isDisabled" placeholder="请输入问卷名称" />
        </el-form-item>
        <el-form-item label="问卷开放时间：" prop="openDateType">
          <el-radio-group v-model="questionnaire.openDateType" @change="openDateTypeChange">
            <el-radio v-for="item in questionnaireOpenDateTypeList" :key="item.code" :label="item.code">{{
              item.desc
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!--选择 指定考试时间 后出现-->
        <el-form-item label="选择时间：" v-if="isConfigOpenDate" prop="openDateRange.dateRange">
          <el-date-picker
            v-model="questionnaire.openDateRange.dateRange"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="form-l"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否开放结果：" prop="isOpenStatistic">
          <el-radio-group v-model="questionnaire.isOpenStatistic" :disabled="isDisabled">
            <el-radio :label="true">开放</el-radio>
            <el-radio :label="false">不开放</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="问卷答题规则："> 一个帐号只允许问卷一次，不可重复问卷 </el-form-item>
        <el-form-item label="应用范围：" prop="appliedRangeType">
          <el-radio-group
            v-model="appliedRangeTypeValue"
            :disabled="isUpdate || isDisabled"
            @change="appliedRangeTypeChange"
          >
            <el-radio v-for="item in questionnaireAppliedRangeTypeList" :key="item.code" :label="item.code">{{
              item.desc
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选择期别：" v-if="isAssignedIssue" prop="curIssueId">
          <el-select
            placeholder="请选择期别"
            :disabled="isUpdate || isDisabled"
            v-model="questionnaire.curIssueId"
            @change="issueChange"
          >
            <el-option
              v-for="item in issue.issueConfigList"
              :key="item.id"
              :label="item.issueName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="前置条件：" prop="preconditionType">
          <el-radio-group v-model="questionnaire.preconditionType" :disabled="isDisabled">
            <el-radio v-for="item in questionnairePreconditionTypeList" :key="item.code" :label="item.code"
              >{{ item.desc }}{{ item.code !== QuestionnairePreconditionTypeEnum.none ? '才可做答' : '' }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div class="m-tit is-border-bottom bg-gray">
        <span class="tit-txt">问卷要求</span>
      </div>
      <el-form
        ref="assessedForm"
        :model="questionnaire"
        :rules="questionnaireRules"
        label-width="auto"
        class="m-form f-mt10 f-mlr20"
      >
        <el-form-item label="是否纳入考核：" prop="isAssessed">
          <el-radio-group v-model="questionnaire.isAssessed" :disabled="isDisabled" @change="isAssessedChange">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="noIssue && !questionnaire.isAssessed">
          <el-form-item label="是否强制问卷：" prop="isForced">
            <el-radio-group v-model="questionnaire.isForced" :disabled="isDisabled" @change="isForcedChange">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="触发问卷环节：" v-if="questionnaire.isForced" prop="triggerType">
            <el-radio-group v-model="questionnaire.triggerType" :disabled="isDisabled">
              <span @click="triggerTypeClick(QuestionnaireTriggerTypeEnum.before_exam)">
                <el-radio :label="QuestionnaireTriggerTypeEnum.before_exam" :disabled="!hasExam">{{
                  questionnaireTriggerType.map.get(QuestionnaireTriggerTypeEnum.before_exam)
                }}</el-radio>
              </span>
              <span style="margin-left: 20px" @click="triggerTypeClick(QuestionnaireTriggerTypeEnum.before_print_cert)">
                <el-radio :label="QuestionnaireTriggerTypeEnum.before_print_cert" :disabled="!hasLearningResult">{{
                  questionnaireTriggerType.map.get(QuestionnaireTriggerTypeEnum.before_print_cert)
                }}</el-radio>
              </span>
            </el-radio-group>
          </el-form-item>
        </template>
      </el-form>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Prop, PropSync, Ref, Inject } from 'vue-property-decorator'
  import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
  import QuestionnaireOpenDateType, {
    QuestionnaireOpenDateTypeEnum
  } from '@api/service/common/scheme/enum/QuestionnaireOpenDateType'
  import QuestionnaireAppliedRangeType, {
    QuestionnaireAppliedRangeTypeEnum
  } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'
  import QuestionnairePreconditionType, {
    QuestionnairePreconditionTypeEnum
  } from '@api/service/common/scheme/enum/QuestionnairePreconditionType'
  import QuestionnaireTriggerType, {
    QuestionnaireTriggerTypeEnum
  } from '@api/service/common/scheme/enum/QuestionnaireTriggerType'
  import IssueLearningType from '@api/service/common/scheme/model/IssueLearningType'
  import { ElForm } from 'element-ui/types/form'
  import QuestionnaireList from '@api/service/management/resource/question-naire/QuestionnaireList'
  import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
  import SchemeBaseInfo from '@api/service/common/scheme/model/SchemeBaseInfo'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
  import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  @Component({})
  export default class extends Vue {
    @Ref('baseForm') baseForm: ElForm
    @Ref('assessedForm') assessedForm: ElForm
    @PropSync('questionnaireConfigDrawer', { type: Boolean, default: false }) showDrawer: boolean
    @Prop({ type: Object, default: () => new QuestionnaireConfigDetail() })
    questionnaire: QuestionnaireConfigDetail
    @Prop({ type: Object, default: () => new IssueLearningType() }) issue: IssueLearningType
    @Prop({ type: Object, default: () => new SchemeBaseInfo() }) trainClassBaseInfo: SchemeBaseInfo
    @Prop({ type: Array, default: () => [] as QuestionnaireConfigDetail[] })
    questionnaireConfigList: QuestionnaireConfigDetail[]
    /**
     * 是否重算中
     */
    @Prop({ type: Boolean, default: false })
    recalculating: boolean
    /**
     * 是否智能学习中
     */
    @Prop({ type: Boolean, default: false })
    isIntelligenceLearning: boolean
    @Prop(String) title: string
    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number
    @Inject() getLearningTypeModel: () => LearningType
    @Inject() getTrainClassBaseInfo: () => TrainClassBaseModel
    hasExam = false
    hasLearningResult = false
    OperationTypeEnum = OperationTypeEnum
    questionnaireRules = {
      questionnaireName: [{ required: true, trigger: 'blur', validator: this.validateQuestionnaireName }],
      openDateType: [{ required: true, message: '？？？', trigger: 'blur' }],
      isOpenStatistic: [{ required: true, message: '请选择是否开放', trigger: 'blur' }],
      curIssueId: [{ required: true, message: '请选择期别', trigger: ['blur', 'change'] }],
      preconditionType: [{ required: true, message: '请配置前置条件', trigger: 'blur' }],
      appliedRangeType: [{ required: true, message: '请选择应用范围', trigger: 'change' }],
      isAssessed: [{ required: true, message: '请配置是否纳入考核', trigger: 'blur' }],
      'openDateRange.dateRange': [
        { required: true, message: '问卷开放时间不能为空', trigger: ['blur', 'change'] },
        {
          validator: this.openDateChange,
          trigger: ['blur', 'change']
        }
      ],
      isForced: [{ required: true, message: '请配置是否强制问卷', trigger: 'blur' }],
      triggerType: [{ required: true, message: '请配置触发问卷环节', trigger: 'blur' }]
    }
    /**
     * 筛选项选项
     */
    questionnaireOpenDateTypeList = QuestionnaireOpenDateType.list()
    questionnaireTriggerType = QuestionnaireTriggerType
    previewLoading = false
    QuestionnairePreconditionTypeEnum = QuestionnairePreconditionTypeEnum
    QuestionnaireTriggerTypeEnum = QuestionnaireTriggerTypeEnum
    /**
     * 问卷查询实例
     * @type {QuestionnaireList}
     */
    questionnaireList = new QuestionnaireList()

    /**
     * 是否禁用
     */
    get isDisabled() {
      return this.recalculating || this.isIntelligenceLearning
    }
    get appliedRangeTypeValue() {
      return this.questionnaire.appliedRangeType
    }
    set appliedRangeTypeValue(val) {
      if (
        val === QuestionnaireAppliedRangeTypeEnum.online_course &&
        !this.getLearningTypeModel().courseLearning.isSelected
      ) {
        this.$message.error('请先配置线上课程。')
      } else {
        this.questionnaire.appliedRangeType = val
        if (this.questionnaire.curIssueId) this.questionnaire.curIssueId = ''
        if (
          !this.questionnairePreconditionTypeList.map((item) => item.code).includes(this.questionnaire.preconditionType)
        ) {
          this.questionnaire.preconditionType = QuestionnairePreconditionTypeEnum.none
        }
        if (this.noIssue && !this.questionnaire.isAssessed) {
          this.questionnaire.isForced = true
          this.questionnaire.triggerType = null
        } else {
          this.questionnaire.isForced = null
          this.questionnaire.triggerType = null
        }
      }
    }

    /**
     * 应用范围选项
     */
    get questionnaireAppliedRangeTypeList() {
      if (this.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId === TrainingModeEnum.online) {
        return QuestionnaireAppliedRangeType.list().filter(
          (item) => item.code == QuestionnaireAppliedRangeTypeEnum.scheme
        )
      } else if (this.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId === TrainingModeEnum.offline) {
        return QuestionnaireAppliedRangeType.list().filter(
          (item) =>
            item.code == QuestionnaireAppliedRangeTypeEnum.assign_issue ||
            item.code == QuestionnaireAppliedRangeTypeEnum.per_issue
        )
      } else {
        return QuestionnaireAppliedRangeType.list()
      }
    }
    /**
     * 前置条件选项
     */
    get questionnairePreconditionTypeList() {
      if (this.noIssue)
        return QuestionnairePreconditionType.list().filter(
          (item) => item.code !== QuestionnairePreconditionTypeEnum.pass_issue_assess
        )
      else
        return QuestionnairePreconditionType.list().filter(
          (item) => item.code !== QuestionnairePreconditionTypeEnum.pass_online_course
        )
    }

    /**
     * 是否指定期别
     */
    get isAssignedIssue() {
      return this.questionnaire.appliedRangeType === QuestionnaireAppliedRangeTypeEnum.assign_issue
    }

    get isUpdate() {
      return this.questionnaire.operationType === OperationTypeEnum.update
    }

    /**
     * 是否配置开发时间
     */
    get isConfigOpenDate() {
      return this.questionnaire.openDateType === QuestionnaireOpenDateTypeEnum.assign
    }
    get noIssue() {
      return [QuestionnaireAppliedRangeTypeEnum.scheme, QuestionnaireAppliedRangeTypeEnum.online_course].includes(
        this.appliedRangeTypeValue
      )
    }
    onOpen() {
      this.hasExam = this.getLearningTypeModel().exam.isSelected
      this.hasLearningResult = this.getTrainClassBaseInfo().hasLearningResult
      if (this.noIssue && !this.questionnaire.isAssessed && typeof this.questionnaire.isForced !== 'boolean') {
        this.questionnaire.isForced = true
        this.isForcedChange(true)
      }
      this.baseForm?.clearValidate()
      this.assessedForm?.clearValidate()
    }
    appliedRangeTypeChange() {
      this.isAssessedChange()
    }
    isAssessedChange() {
      if (this.questionnaire.isAssessed || !this.noIssue) {
        this.questionnaire.isForced = null
      } else {
        this.questionnaire.isForced = true
      }
      this.isForcedChange(this.questionnaire.isForced)
    }
    issueChange(val: string) {
      if (val) {
        this.questionnaire.curIssueName = this.issue.issueConfigList.find((item) => item.id === val)?.issueName
      } else {
        this.questionnaire.curIssueName = ''
      }
    }

    isForcedChange(val: boolean) {
      if (val) {
        if (this.hasLearningResult) {
          this.questionnaire.triggerType = QuestionnaireTriggerTypeEnum.before_print_cert
        } else if (this.hasExam) {
          this.questionnaire.triggerType = QuestionnaireTriggerTypeEnum.before_exam
        } else {
          this.questionnaire.triggerType = null
        }
      } else this.questionnaire.triggerType = null
    }
    triggerTypeClick(type: QuestionnaireTriggerTypeEnum) {
      if (type === QuestionnaireTriggerTypeEnum.before_print_cert && !this.hasLearningResult) {
        this.$message.warning('请先选择提供培训证明，再配置。')
      } else if (type === QuestionnaireTriggerTypeEnum.before_exam && !this.hasExam) {
        this.$message.warning('请先勾选班级考试，再配置。')
      }
    }
    /**
     * 时间类型调整
     */
    openDateTypeChange() {
      this.questionnaire.openDateRange.dateRange = null
    }

    /**
     * 名称重复校验
     */
    validateQuestionnaireName(rule: any, value: string, callback: Function) {
      if (value) {
        if (
          this.questionnaireConfigList.some(
            (item) => item.id !== this.questionnaire.id && item.questionnaireName === value
          )
        ) {
          return callback('同方案内名称不可重复')
        } else if (value.length > 100) {
          return callback('问卷名称不能超过100字！')
        } else {
          callback()
        }
      } else {
        callback('问卷名称不能为空')
      }
    }
    openDateChange(rule: any, value: Array<string>, callback: Function) {
      if (value?.filter(Boolean)?.length) callback()
      else callback(new Error('填写指定时间'))
    }
    /**
     * 取消
     */
    cancel() {
      this.showDrawer = false
    }

    /**
     * 预览
     */
    async preview(id: string) {
      this.previewLoading = true
      const res = await this.questionnaireList.copy(id)
      if (res.questionList.length === 0) {
        this.$message.error('请至少配置一题试题才可预览。')
      } else {
        window.open(`/admin#/resource/questionnaire/preview?id=${id}`, '_blank')
      }
      this.previewLoading = false
    }

    /**
     * 确定
     */
    confirm() {
      Promise.all([this.baseForm.validate(), this.assessedForm.validate()]).then(() => {
        this.$emit('confirm')
        this.showDrawer = false
      })
    }
  }
</script>
