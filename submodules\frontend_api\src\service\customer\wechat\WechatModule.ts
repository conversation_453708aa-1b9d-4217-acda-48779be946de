import { Action, getModule, Module, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import WechatGateway, {
  ApplyJsSDKSignatureRequest,
  GetAppletCodeRequest,
  GetAppletCodeResponse,
  GetUserInfoRequest,
  GetUserInfoResponse,
  JsSDKSignatureResponse
} from '@api/gateway/ms-wechat-v1'
import Response from '@api/Response'
import { UnAuthorize } from '@api/Secure'
import ConfigCenter from '@api/service/common/config-center/ConfigCenter'
import AppletApp from '@api/service/common/config-center/models/PublicApp'
import AppTypeEnum from '@api/service/common/config-center/models/enums/AppTypeEnum'

@Module({ namespaced: true, dynamic: true, name: 'CustomerWechatModule', store })
class WechatModule extends VuexModule {
  @Action
  @UnAuthorize
  async applyJsSDKSignature(applyInfo: ApplyJsSDKSignatureRequest): Promise<Response<JsSDKSignatureResponse>> {
    return WechatGateway.applyJsSDKSignature(applyInfo)
  }

  @Action
  @UnAuthorize
  async getUserInfo(applyInfo: GetUserInfoRequest): Promise<Response<GetUserInfoResponse>> {
    return WechatGateway.getUserInfo(applyInfo)
  }

  @Action
  @UnAuthorize
  async getAppletCode(applyInfo: GetAppletCodeRequest): Promise<Response<GetAppletCodeResponse>> {
    /**
     * 小程序appId，不填表示当前项目默认只有一个微信开发平台程序，自动从配置中获取
     * 从Apollo上获取 type：qrCode
     */
    const appIdInfoList = ConfigCenter.getValueByNamespace('wechat', 'ms-wechat.business.applet.apps') as Array<
      AppletApp
    >
    const qrcodeAppId = appIdInfoList?.find(item => item.type === AppTypeEnum.qrCode).appId || ''
    if (qrcodeAppId) {
      applyInfo.appId = qrcodeAppId
    }
    return WechatGateway.getAppletCode(applyInfo)
  }
}

export default getModule(WechatModule)
