<template>
  <div>
    <div class="m-tit is-border-bottom">
      <span class="tit-txt">基本信息</span>
    </div>
    <el-row type="flex" justify="center">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form :inline="true" label-width="150px" class="m-text-form f-mt30">
          <el-col :span="12">
            <el-form-item label="试卷名称："
              ><span class="b">{{ createExamPaper.name }}</span></el-form-item
            >
          </el-col>
          <el-col :span="12">
            <el-form-item label="试卷分类："
              ><span class="b">{{ createExamPaper.paperPublishConfigureCategoryName }}</span></el-form-item
            >
          </el-col>
          <el-col :span="12">
            <el-form-item label="组卷方式："><span class="b">智能组卷</span></el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="试卷总分："><span class="b">100分</span></el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考试时长："
              ><span class="b"
                ><span>{{ createExamPaper.publishPattern.suggestionTimeLengthByMin }}</span
                >分钟</span
              ></el-form-item
            >
          </el-col>
          <el-col :span="12">
            <el-form-item label="出题范围：">
              <span class="b"
                ><span>{{ getFetchWay }}</span></span
              ></el-form-item
            >
          </el-col>
          <!-- 按学员课程id出题不显示已选题库 -->
          <el-col :span="24" v-if="createExamPaper.publishPattern.questionScopes != 2">
            <el-form-item label="已选题库：">
              <span class="b"
                ><span v-for="(item, index) in createExamPaper.publishPattern.libraryIds" :key="index"
                  >{{ item.name }} <span style="padding-left:5px">&nbsp;</span>
                </span></span
              ></el-form-item
            >
          </el-col>
        </el-form>
      </el-col>
    </el-row>
    <div class="m-tit is-border-bottom">
      <span class="tit-txt">配置试题</span>
    </div>
    <el-row type="flex" justify="center" class="f-mt30 f-pb30">
      <el-col :md="20" :lg="16" :xl="13">
        <el-button type="primary" icon="el-icon-plus" plain @click="popRandomConfigurationDialog">添加大题</el-button>
        <el-form label-width="90px" class="m-form">
          <draggable v-model="randomConfigurationItemList">
            <div
              class="m-question-set f-clear f-mt20"
              v-for="(item, index) in randomConfigurationItemList"
              :key="index"
            >
              <el-form-item class="is-text">
                <div slot="label" class="f-f16 f-cb">第{{ stringIndex(index + 1) }}大题</div>
                <div class="f-f16">
                  {{ item.groupName }}
                </div>
                <el-row :gutter="20" class="f-mt20">
                  <el-col :span="6">
                    题型： <span class="b">{{ resolverQuestionType(item.questionType) }}</span>
                  </el-col>
                  <el-col :span="6">
                    大题总分：<span class="b">{{ item.totalScore }}</span> 分
                  </el-col>
                  <el-col :span="6">
                    大题数量：<span class="b">{{ item.questionCount }}</span> 道题
                  </el-col>
                </el-row>
                <div class="f-mt20">
                  <el-button
                    icon="el-icon-edit"
                    size="mini"
                    type="primary"
                    plain
                    @click="popEditRandomConfigurationItemDialog(item, index)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    icon="el-icon-delete"
                    size="mini"
                    type="primary"
                    plain
                    @click="deleteRandomConfigurationItem(index)"
                  >
                    删除
                  </el-button>
                </div>
              </el-form-item>
            </div>
          </draggable>

          <div class="f-tc" style=" padding-top:50px">
            <el-button @click="cancelEdit">放弃编辑</el-button>
            <el-button @click="BackStep">返回上一步</el-button>
            <el-button @click="commitDraft">保存为草稿</el-button>
            <el-button type="primary" @click="commit(false)">保存为试卷</el-button>
          </div>
        </el-form>
      </el-col>
    </el-row>
    <!-- 添加试题弹窗 -->
    <question-dialog
      :show.sync="uiConfig.dialog.randomConfigurationShow"
      :randomConfigurationOperationType="uiConfig.randomConfigurationOperationType"
      :currentEditQuestionType="uiConfig.randomConfigurationOperationType"
      :currentItem="currentConfigurationItem"
      @questionConfigItem="questionConfigItem"
      :exam-library="createExamPaper.publishPattern.libraryIds"
      :range-question="createExamPaper.publishPattern.questionScopes"
      :question-rules="openDialogFlag"
    ></question-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, PropSync, Watch, Prop } from 'vue-property-decorator'
  import { ExamPaperUI } from '@/store/modules-ui/exam/models/ExamPaperUI'
  import ExamPaperUIModule from '@/store/modules-ui/exam/ExamPaperUIModule'
  import QuestionDialog from '@hbfe/jxjy-admin-examPaper/src/add/big-question-config.vue'
  import draggable from 'vuedraggable'
  import { ValidResult } from '@hbfe/jxjy-admin-examPaper/src/ValidResult'
  import AutomaticExamPaperVo from '@api/service/management/resource/exam-paper/mutation/vo/common/AutomaticExamPaperVo'
  import CreateExamPaperVo from '@api/service/management/resource/exam-paper/mutation/vo/create/CreateExamPaperVo'
  import Utils from '@hbfe/jxjy-admin-examPaper/src/Utils'
  import QuestionExtract from '@api/service/management/resource/exam-paper/mutation/vo/common/QuestionExtract'
  import { QuestionTypeEnum } from '@api/service/common/enums/question/QuestionType'

  @Component({
    components: {
      QuestionDialog,
      draggable
    }
  })
  export default class extends Vue {
    @PropSync('createExampaperInfo', {
      type: Object
    })
    createExamPaper!: CreateExamPaperVo<AutomaticExamPaperVo>

    @Watch('createExamPaper', {
      deep: true,
      immediate: true
    })
    async createExamPaperChange(val: any) {
      if (this.isModify) {
        await this.echoDisplay()
      }
    }

    @Prop({
      type: Boolean,
      default: false
    })
    isModify: boolean

    editoIndex = 0
    /**
     * ui配置项
     */
    uiConfig = {
      dialog: {
        randomConfigurationShow: false
      },
      // 1.增加答题  2、编辑大题
      randomConfigurationOperationType: 1,
      // 当前编辑的试题类型
      currentEditQuestionType: 0
    }
    loading = false

    /**
     * 试卷基本信息
     */
    basicInfo: ExamPaperUI = new ExamPaperUI()
    /**
     * 所有的题库数据
     */
    librayList: any = []

    /**
     * 答题配置项
     */
    currentConfigurationItem = new QuestionExtract()

    /**
     * 获取选题方式
     */
    get getFetchWay() {
      return this.createExamPaper.publishPattern.questionScopes === 1 ? '按题库出题' : '按学员课程ID出题'
    }

    /**
     * 试题类型名
     */
    get paperClassifyName() {
      return ExamPaperUIModule.paperTypeName
    }

    /**
     *  获取答题配置信息
     */
    randomConfigurationItemList = new Array<QuestionExtract>()

    /**
     * 打开大题标识--  为的是打开的时候抽屉规则默认显示 -1  智能抽屉
     */
    openDialogFlag = 0

    async echoDisplay() {
      this.createExamPaper.publishPattern.questionExtracts.forEach(p => {
        p.eachQuestionScore = p.totalScore / p.questionCount
        if (p.eachQuestionScore) p.totalScore = p.eachQuestionScore * p.questionCount
      })
      this.randomConfigurationItemList = this.createExamPaper.publishPattern.questionExtracts
    }

    BackStep() {
      this.$emit('BackFirstStep', 1)
    }

    // 下标转中文
    stringIndex(val: number) {
      return Utils.numberToChinese(val)
    }

    /**
     * 打开大题配置项弹窗
     */
    popRandomConfigurationDialog() {
      this.currentConfigurationItem = new QuestionExtract()
      this.uiConfig.dialog.randomConfigurationShow = true
      this.uiConfig.randomConfigurationOperationType = 1
      this.openDialogFlag++
    }

    resolverQuestionType(questionType: number) {
      const obj = {
        [QuestionTypeEnum.radio]: '选择',
        [QuestionTypeEnum.multiple]: '多选',
        [QuestionTypeEnum.opinion]: '判断'
      }
      return obj[questionType]
    }

    /**
     * 编辑答题配置项
     */
    popEditRandomConfigurationItemDialog(item: QuestionExtract, index: number) {
      this.uiConfig.randomConfigurationOperationType = 2
      this.uiConfig.dialog.randomConfigurationShow = true
      this.uiConfig.currentEditQuestionType = item.questionType
      this.currentConfigurationItem = JSON.parse(JSON.stringify(item))
      this.editoIndex = index
    }

    /**
     * 删除大题配置项
     */
    deleteRandomConfigurationItem(index: number) {
      // ExamPaperUIModule.DELETE_RANDOM_CONFIGURATION_ITEM(item)
      this.randomConfigurationItemList.splice(index, 1)
    }

    /**
     * 基本信息校验
     * @param draft: 是否草稿
     */
    valid(draft: boolean) {
      const result = new ValidResult()
      const paperUi = ExamPaperUIModule.paperUI
      if (draft) {
        return result
      }
      if (!this.randomConfigurationItemList || !this.randomConfigurationItemList.length) {
        return result.withInvalidError('请配置大题，不可为空！')
      }
      let totalScore = 0
      for (const item of this.randomConfigurationItemList) {
        totalScore += parseInt(item.totalScore + '')
      }
      if (totalScore !== 100) {
        return result.withInvalidError('所有大题的总分不等于100，请调整！')
      }
      return result
    }

    /**
     * 保存试卷
     */
    async commit(draft: boolean) {
      this.randomConfigurationItemList.forEach((p, i) => {
        p.eachQuestionScore = p.totalScore / p.questionCount
        p.sequence = i + 1
      })
      this.createExamPaper.publishPattern.questionExtracts = this.randomConfigurationItemList
      // console.log(res, 'res')

      // this.loading = true
      const result = this.valid(draft)
      if (!result.isSuccess()) {
        this.$message.warning(result.message)
        this.loading = false
        return
      }
      this.$emit('thirdCommit', true)
    }

    /**
     *  取消编辑
     */
    cancelEdit() {
      this.$confirm('是否放弃编辑', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      })
        .then(() => {
          this.$router.push('/resource/exam-paper')
        })
        .catch(() => {
          console.log('取消')
        })
    }

    questionConfigItem(value: any) {
      if (this.uiConfig.randomConfigurationOperationType == 1) {
        this.randomConfigurationItemList.push(value)
      } else {
        const list = this.randomConfigurationItemList
        list.splice(this.editoIndex, 1, value)
      }
    }

    commitDraft() {
      this.randomConfigurationItemList?.forEach((p, i) => {
        p.eachQuestionScore = p.totalScore / p.questionCount
        p.sequence = i + 1
      })
      this.createExamPaper.publishPattern.questionExtracts = this.randomConfigurationItemList
      this.$emit('commitDraft', true)
    }
  }
</script>
