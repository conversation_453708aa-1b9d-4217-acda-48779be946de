"""独立部署的微服务,K8S服务名:ms-basicdata-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""功能描述：企业-当前登录企业管理员信息
		描述：查询当前登录管理员的信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
	"""
	getEnterpriseUnitAdminInfoInMyself:EnterpriseUnitAdminInfoResponse
	"""功能描述：项目级-企业单位管理员详情查询接口
		描述：查询当前企业单位下指定管理员的信息，如不存在返回null
		@param accountId               : 帐户ID
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
		@date : 2022年6月9日 16:50:10
	"""
	getEnterpriseUnitAdminInfoInSubProject(accountId:String):EnterpriseUnitAdminInfoResponse @optionalLogin
	"""功能描述：项目级-企业单位管理员详情查询接口（按用户ID查询）
		描述：查询当前企业单位下指定管理员的信息，如不存在返回null
		@param adminUserId             : 帐户ID
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
		@date : 2022年6月9日 16:50:10
	"""
	getEnterpriseUnitAdminUserInfoInSubProject(adminUserId:String):EnterpriseUnitAdminInfoResponse @optionalLogin
	"""功能描述： 政府单位-查询本级及下属企业单位管理员-分页列表接口
		描述：查询政府单位管辖下的企业单位管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:10
	"""
	pageEnterpriseUnitAdminInfoInGovernmentUnit(page:Page,request:EnterpriseUnitAdminQueryRequest):EnterpriseUnitAdminInfoResponsePage @page(for:"EnterpriseUnitAdminInfoResponse") @optionalLogin
	"""功能描述：企业单位-查询当前登录企业单位下属管理员-分页列表接口
		描述：查询当前企业单位下的管理员信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:10
	"""
	pageEnterpriseUnitAdminInfoInMyself(page:Page,request:EnterpriseUnitAdminQueryRequest):EnterpriseUnitAdminInfoResponsePage @page(for:"EnterpriseUnitAdminInfoResponse") @optionalLogin
	"""功能描述：项目级-查询企业单位管理员-分页列表接口
		描述：查询当前子项目下的企业单位管理员信息，默认按创建时间降序排
		@param page                    :
		@param request                 :
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse>
		@date : 2022年6月9日 16:50:21
	"""
	pageEnterpriseUnitAdminInfoInSubProject(page:Page,request:EnterpriseUnitAdminQueryRequest):EnterpriseUnitAdminInfoResponsePage @page(for:"EnterpriseUnitAdminInfoResponse") @optionalLogin
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""功能描述：账户信息查询条件
	@Author： wtl
	@Date： 2022年5月11日 15:30:56
"""
input AccountRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.request.AccountRequest") {
	"""账户状态 1：正常，2：冻结
		@see AccountStatus
	"""
	statusList:[Int]
	"""创建时间范围"""
	createTimeScope:DateScopeRequest
	"""创建人用户id"""
	createdUserId:String
}
"""功能描述：登录认证查询条件
	@Author： wtl
	@Date： 2022年1月26日 09:30:12
"""
input AuthenticationRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.request.AuthenticationRequest") {
	"""帐号"""
	identity:String
}
"""功能描述：角色查询条件
	@Author： wtl
	@Date： 2022年5月11日 11:46:41
"""
input RoleRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.request.RoleRequest") {
	"""角色id集合"""
	roleIdList:[String]
	"""角色类型
		（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
		@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
	"""
	roleTypeList:[String]
	"""角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
		//@see RoleCategories
	"""
	roleCategoryList:[Int]
}
"""功能描述：管理员排序
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
input AdminSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.request.admin.AdminSortRequest") {
	"""管理员排序字段"""
	sortField:EnterprisePersonSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
"""功能描述：管理员查询条件
	@Author： wtl
	@Date： 2022年1月25日 15:24:10
"""
input AdminUserRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.request.admin.AdminUserRequest") {
	"""管理地区路径集合（模糊，右like）"""
	manageRegionPathList:[String]
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	phoneMatchType:Int
}
"""功能描述 : 企业单位管理员查询条件
	@date : 2022/6/17 17:32
"""
input EnterpriseUnitAdminQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.request.admin.EnterpriseUnitAdminQueryRequest") {
	"""企业单位管理员归属信息"""
	owner:EnterpriseUnitAdminOwnerRequest
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:AdminUserRequest
	"""登录认证信息"""
	authentication:AuthenticationRequest
	"""角色信息查询"""
	role:RoleRequest
	"""排序"""
	sortList:[AdminSortRequest]
}
"""功能描述 : 企业单位管理员归属查询条件
	@date : 2022/6/17 17:42
"""
input EnterpriseUnitAdminOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.request.admin.nested.EnterpriseUnitAdminOwnerRequest") {
	"""企业单位id路径集合
		String："/福建电信id/福州电信分公司id"
	"""
	enterpriseUnitIdPathList:[String]
	"""企业单位id路径匹配方式，默认为右模糊查询(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	enterpriseUnitIdPathMatchType:Int
	"""企业单位归属关系状态(0:冻结 1:正常)
		@see PersonUnitRelationshipStatus
	"""
	status:Int
}
input DateScopeRequest @type(value:"com.fjhb.ms.basicdata.repository.DateScopeRequest") {
	beginTime:DateTime
	endTime:DateTime
}
enum SortTypeEnum @type(value:"com.fjhb.ms.basicdata.enums.SortTypeEnum") {
	ASC
	DESC
}
type RegionModel @type(value:"com.fjhb.ms.basicdata.model.RegionModel") {
	regionId:String
	regionPath:String
	provinceId:String
	provinceName:String
	cityId:String
	cityName:String
	countyId:String
	countyName:String
}
"""功能描述：账户信息
	@Author： wtl
	@Date： 2022年5月11日 15:30:56
"""
type AccountResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.response.AccountResponse") {
	"""账户id"""
	accountId:String
	"""帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
		@see AccountTypes
	"""
	accountType:Int
	"""单位信息"""
	unitInfo:UnitInfoResponse
	"""所属顶级企业帐户Id"""
	rootAccountId:String
	"""帐户状态 1：正常，2：冻结，3：注销
		@see AccountStatus
	"""
	status:Int
	"""注册方式
		0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
		@see AccountRegisterTypes
	"""
	registerType:Int
	"""来源类型
		0内置，1项目主网站，2安卓，3IOS
		@see AccountSourceTypes
	"""
	sourceType:Int
	"""创建时间"""
	createdTime:DateTime
}
"""功能描述：帐户认证信息
	@Author： wtl
	@Date： 2022年5月11日 14:23:18
"""
type AuthenticationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.response.AuthenticationResponse") {
	"""帐号"""
	identity:String
	"""认证标识类型
		1用户名,2手机,3身份证,4邮箱,5第三方OpenId
	"""
	identityType:Int
	"""认证方式状态 1启用，2禁用
		@see AuthenticationStatusEnum
	"""
	status:Int
}
"""功能描述：角色信息
	@Author： wtl
	@Date： 2022/1/24 20:17
"""
type RoleResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.response.RoleResponse") {
	"""角色id"""
	roleId:String
	"""角色名称"""
	roleName:String
	"""角色类型
		（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
		@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
	"""
	roleType:String
	"""角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
		@see RoleCategories
	"""
	roleCategory:Int
	"""应用方类型
		@see SystemMemberTypes
	"""
	applicationMemberType:Int
	"""应用方ID"""
	applicationMemberId:String
}
"""功能描述 : 企业单位管理员信息
	@date : 2022/6/18 12:24
"""
type EnterpriseUnitAdminInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.response.admin.EnterpriseUnitAdminInfoResponse") {
	"""企业单位管理员归属信息"""
	businessOwnerInfo:EnterpriseUnitAdminBusinessOwnerResponse
	"""账户信息"""
	accountInfo:AccountResponse
	"""管理员用户信息"""
	userInfo:AdminUserInfoResponse
	"""人员信息"""
	personInfo:EnterpriseUnitPersonInfoResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
	"""角色信息集合"""
	roleList:[RoleResponse]
}
"""功能描述：管理员用户信息
	@Author： wtl
	@Date： 2022年1月25日 15:48:48
"""
type AdminUserInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.response.admin.nested.AdminUserInfoResponse") {
	"""管辖地区集合"""
	manageRegionList:[RegionModel]
	"""办公室（所在处/科室）"""
	office:String
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
}
"""企业单位管理员业务归属信息"""
type EnterpriseUnitAdminBusinessOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.response.admin.nested.EnterpriseUnitAdminBusinessOwnerResponse") {
	"""隶属企业单位集合"""
	relationToEnterpriseUnitList:[RelationToUnitResponse]
}
"""人员信息模型"""
type EnterpriseUnitPersonInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.response.admin.nested.EnterpriseUnitPersonInfoResponse") {
	"""是否法人帐号"""
	isCorporateAccount:Boolean
	"""人员实名认证信息"""
	personIdentityVerificationInfo:PersonIdentityVerificationResponse
}
"""人员实名认证信息模型"""
type PersonIdentityVerificationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.response.admin.nested.PersonIdentityVerificationResponse") {
	"""是否已认证"""
	identityVerification:Boolean
	"""认证渠道(1:闽政通 2：腾讯)"""
	identityVerificationChannel:Int
	"""认证时间"""
	identityVerificationTime:DateTime
}
"""与企业单位关联关系"""
type RelationToUnitResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.response.admin.nested.RelationToUnitResponse") {
	"""企业单位id路径"""
	unitIdPath:String
	"""人员与单位关系状态(0:冻结 1:正常)
		@see PersonUnitRelationshipStatus
	"""
	status:Int
}
"""单位信息模型"""
type UnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.qyfwpt.graphql.response.admin.nested.UnitInfoResponse") {
	"""单位ID"""
	unitId:String
}
"""功能描述：管理员排序字段
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
enum EnterprisePersonSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.EnterprisePersonSortFieldEnum") {
	"""创建时间"""
	createdTime
	"""账户类型"""
	accountType
	"""用户名称首字母"""
	userNameFirstLetter
}

scalar List
type EnterpriseUnitAdminInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [EnterpriseUnitAdminInfoResponse]}
