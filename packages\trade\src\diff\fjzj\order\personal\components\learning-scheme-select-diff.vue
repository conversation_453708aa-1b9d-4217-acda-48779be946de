<template>
  <div>
    <biz-select-Box
      :value-id="'schemeId'"
      :label="'scheme'"
      :options="hasSelectSchemeMode"
      @removeTag="cancelSelectScheme"
      placeholder="请选择培训方案"
      @clear="clear"
      @blur="showDialog"
      :multiple="multiple"
    ></biz-select-Box>
    <el-drawer title="选择培训方案" :visible.sync="dialogShow" size="1200px" custom-class="m-drawer">
      <div class="drawer-bd" v-loading="uiConfig.loading.pageLoading">
        <hb-search-wrapper @reset="resetQueryParam" class="m-query is-border-bottom">
          <el-form-item label="年度">
            <biz-year-select placeholder="请选择培训年度" v-model="localSkuProperty.year"></biz-year-select>
          </el-form-item>

          <el-form-item label="地区">
            <biz-region-cascader
              v-model="localSkuProperty.region"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-region-cascader>
          </el-form-item>

          <el-form-item label="行业">
            <biz-industry-select
              v-model="localSkuProperty.industry"
              @clearIndustrySelect="handleClearIndustrySelect"
              @industryPropertyId="handleIndustryPropertyId"
              @industryInfos="handleIndustryInfos"
            ></biz-industry-select>
          </el-form-item>
          <el-form-item label="培训形式">
            <biz-training-mode-select v-model="localSkuProperty.trainingMode"></biz-training-mode-select>
          </el-form-item>

          <el-form-item label="科目类型" v-if="skuVisible.subjectType && localSkuProperty.industry">
            <biz-accounttype-select
              v-model="localSkuProperty.subjectType"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            >
            </biz-accounttype-select>
          </el-form-item>

          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.societyIndustryId &&
              localSkuProperty.industry === envConfig.societyIndustryId
            "
          >
            <biz-major-cascader
              v-model="localSkuProperty.societyTrainingMajor"
              placeholder="请选择培训专业"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              (localSkuProperty.industry === envConfig.constructionIndustryId ||
                localSkuProperty.industry === envConfig.professionHealthIndustryId)
            "
          >
            <biz-training-category-select
              placeholder="请选择培训类别"
              v-model="localSkuProperty.trainingCategory"
              :industry-property-id="industryPropertyId"
              @updateTrainingCategory="handleUpdateTrainingCategory"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>

          <el-form-item
            label="技术等级"
            v-if="
              skuVisible.jobLevel && localSkuProperty.industry && localSkuProperty.industry === envConfig.workServiceId
            "
          >
            <biz-technical-grade-select
              v-model="localSkuProperty.jobLevel"
              :industry-id="localSkuProperty.industry"
              :industry-property-id="industryPropertyId"
            ></biz-technical-grade-select>
          </el-form-item>

          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-major-select
              v-model="localSkuProperty.constructionTrainingMajor"
              placeholder="请选择培训专业"
              :industry-property-id="industryPropertyId"
              :training-category-id="trainingCategoryId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训对象"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-training-object-select
              v-model="localSkuProperty.trainingObject"
              placeholder="请选择培训对象"
              :industry-property-id="industryPropertyId"
              :industry-id="localSkuProperty.industry"
              @updateTrainingCategory="updateTrainingCategory"
            />
          </el-form-item>
          <el-form-item
            label="岗位类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-obj-category-select
              v-model="localSkuProperty.positionCategory"
              placeholder="请选择岗位类别"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              :training-object-id="localSkuProperty.trainingObject"
            />
          </el-form-item>
          <template
            ><el-form-item
              label="学段"
              v-if="
                skuVisible.grade &&
                localSkuProperty.industry &&
                localSkuProperty.industry === envConfig.teacherIndustryId
              "
            >
              <biz-study-period
                v-model="localSkuProperty.grade"
                placeholder="请选择学段"
                :industry-property-id="industryPropertyId"
                :industry-id="localSkuProperty.industry"
                @updateStudyPeriod="updateGrade"
                @clearSubject="clearSubject"
              />
            </el-form-item>
            <el-form-item
              label="学科"
              v-if="
                skuVisible.subject &&
                localSkuProperty.industry &&
                localSkuProperty.industry === envConfig.teacherIndustryId
              "
            >
              <!-- //TODO待修改学段id组件待修改 -->
              <biz-subject
                v-model="localSkuProperty.subject"
                placeholder="请选择学科"
                :industry-property-id="industryPropertyId"
                :industryId="localSkuProperty.industry"
                :studyPeriodId="localSkuProperty.grade"
              /> </el-form-item
          ></template>

          <el-form-item label="培训方案类型">
            <scheme-type-diff v-model="schemeTypeInfo"></scheme-type-diff>
          </el-form-item>

          <el-form-item label="培训方案名称">
            <el-input clearable placeholder="请输入培训方案名称" v-model="schemeName" />
          </el-form-item>

          <template slot="actions">
            <el-button type="primary" @click="trainSchemePage.currentChange(1)">查询</el-button>
          </template>
        </hb-search-wrapper>

        <!--表格-->
        <el-table
          stripe
          :data="trainSchemeList"
          v-loading="trainSchemeQuery.loading"
          max-height="500px"
          class="m-table f-mt10"
          ref="schemeTable"
        >
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="培训方案名称" min-width="300">
            <template slot-scope="scope">
              {{ scope.row.commodityBasicData.saleTitle }}
            </template>
          </el-table-column>
          <el-table-column label="培训形式" min-width="160">
            <template slot-scope="scope"> {{ scope.row.skuValueNameProperty.trainingMode.skuPropertyName }} </template>
          </el-table-column>
          <el-table-column label="方案类型" min-width="160">
            <template slot-scope="scope"> {{ getSchemeType(scope.row) }} </template>
          </el-table-column>
          <el-table-column label="培训属性" min-width="240">
            <template slot-scope="scope">
              <p v-show="getSkuPropertyName(scope.row, 'industry')">
                行业：{{ getSkuPropertyName(scope.row, 'industry') }}
              </p>
              <p v-show="getSkuPropertyName(scope.row, 'region')">
                地区：{{ getSkuPropertyName(scope.row, 'region') }}
              </p>
              <p v-show="getSkuPropertyName(scope.row, 'subjectType')">
                科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
              </p>
              <p v-show="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
                培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
              </p>
              <p v-show="getSkuPropertyName(scope.row, 'trainingMajor')">
                培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
              </p>
              <p v-show="getSkuPropertyName(scope.row, 'jobLevel')">
                技术等级：{{ getSkuPropertyName(scope.row, 'jobLevel') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'trainingObject')">
                培训对象：{{ getSkuPropertyName(scope.row, 'trainingObject') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'positionCategory')">
                岗位类别：{{ getSkuPropertyName(scope.row, 'positionCategory') }}
              </p>
              <p v-show="getSkuPropertyName(scope.row, 'year')">
                培训年度：{{ getSkuPropertyName(scope.row, 'year') }}
              </p>
              <p v-show="getSkuPropertyName(scope.row, 'learningPhase')">
                学段：{{ getSkuPropertyName(scope.row, 'learningPhase') }}
              </p>
              <p v-show="getSkuPropertyName(scope.row, 'discipline')">
                学科：{{ getSkuPropertyName(scope.row, 'discipline') }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="学时" min-width="120" align="center">
            <template slot-scope="scope">{{ scope.row.period }}</template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" @click="selectScheme(scope.row)" v-if="selected(scope.row)">选择</el-button>
              <el-button type="text" @click="cancelSelectScheme(scope.row.commoditySkuId, true)" v-else
                >取消选择</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination class="f-mt15 f-tr" :page="trainSchemePage" v-bind="trainSchemePage"> </hb-pagination>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button @click="getValue">取消</el-button>
        <el-button type="primary" @click="setValue">确定</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Watch, Prop, Provide, Component, Vue } from 'vue-property-decorator'
  import Page from '@hbfe/jxjy-admin-common/src/models/Page'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import { cloneDeep } from 'lodash'
  import BizRegionCascader from '@hbfe/jxjy-admin-components/src/biz/biz-region-cascader.vue'
  import BizTechnicalGradeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-technical-grade-select.vue'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import {
    CommoditySkuRequest,
    CommoditySkuSortRequest,
    OnShelveRequest,
    RegionSkuPropertyRequest,
    RegionSkuPropertySearchRequest,
    SchemeRequest,
    SkuPropertyRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { Query, UiPage } from '@hbfe/common'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import SkuVo from '@api/service/management/train-class/query/vo/SkuVo'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import SchemeTypeDiff from '@hbfe/jxjy-admin-trade/src/diff/fjzj/order/personal/components/scheme-type-diff.vue'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import BizTrainingModeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-mode-select.vue'
  // import { HasSelectCommodityMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectCommodityMode'

  /**
   * 培训班UI模型
   */
  class UITrainClassCommodityDetail extends TrainClassCommodityVo {
    // 是否长期有效
    isLongTerm: boolean
    // 是否是人社行业
    isSocietyIndustry: boolean
    // 是否不开放报名
  }
  // sku绑定模型
  class SchemeSkuProperty {
    year: string
    region: string[]
    industry: string
    subjectType: string
    trainingCategory: string
    societyTrainingMajor: string[]
    constructionTrainingMajor: string
    jobLevel: string
    trainingObject: string
    positionCategory: string
    grade: string
    subject: string
    trainingMode: TrainingModeEnum
  }
  @Component({
    components: { BizTrainingModeSelect, BizIndustrySelect, BizRegionCascader, BizTechnicalGradeSelect, SchemeTypeDiff }
  })
  export default class extends Vue {
    /**
     * 选中之后回传的参数
     */
    @Prop({
      type: Array,
      required: true,
      default: () => new Array<HasSelectSchemeMode>()
    })
    value: Array<HasSelectSchemeMode>

    /**
     * 机构（网校）id
     */
    @Prop({
      type: Array
    })
    trainingInstitutionIdList: Array<string> | undefined

    @Prop({
      type: Boolean,
      default: false
    })
    multiple: boolean
    @Prop({ default: false, type: Boolean }) isOnlyOnline: boolean
    @Watch('isOnlyOnline', { immediate: true })
    isOnlyOnlineChange(val: boolean) {
      if (val === true) {
        //仅为面授班 把培训形式的搜索宽隐藏 然后查询带上参数
      }
    }
    /**
     * 选中的值
     */
    hasSelectSchemeMode = new Array<HasSelectSchemeMode>()

    // 选中证书模板id
    certificateTemplateId = new Array<string>()
    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = {
      /**
       * 年度
       */
      year: '',
      /**
       * 地区
       */
      region: [] as string[],
      /**
       * 行业
       */
      industry: '',
      /**
       * 科目类型
       */
      subjectType: '',
      /**
       * 培训类别
       */
      trainingCategory: '',
      /**
       * 培训专业 - 建设行业
       */
      constructionTrainingMajor: '',
      /**
       * 培训专业 - 人社行业
       */
      societyTrainingMajor: [] as string[],
      /**
       * 技术等级
       */
      jobLevel: '',
      /**
       * 培训对象
       */
      trainingObject: '',
      /**
       * 刚问类别
       */
      positionCategory: '',
      /**
       * 学段
       */
      grade: '',
      /**
       * 学科
       */
      subject: '',
      /**
       * 培训形式
       */
      trainingMode: null
    } as SchemeSkuProperty

    // 培训方案名称
    schemeName = ''

    // 查询参数 - 培训方案
    trainSchemeQueryParam: CommoditySkuRequest
    // 分页参数 - 培训方案
    trainSchemePage: UiPage
    dialogShow = false
    page: Page = new Page()
    // 分页加载参数 - 培训方案
    trainSchemeQuery: Query = new Query()
    // 培训方案列表
    trainSchemeList: Array<TrainClassCommodityVo> = new Array<TrainClassCommodityVo>()
    // 排序策略
    sortPolicy: Array<CommoditySkuSortRequest> = new Array<CommoditySkuSortRequest>()
    // 培训方案业务状态层入口
    schemeBusinessEntry: QueryTrainClassCommodityList = new QueryTrainClassCommodityList()
    /**
     * 培训方案类型
     */
    schemeTypeInfo: Array<string> = new Array<string>()

    /**
     * ui控制组
     */
    uiConfig = {
      loading: {
        pageLoading: false
      }
    }

    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 报名状态
     */
    onShelveStatus: number = null

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''

    skuVisible = {
      // 科目类型
      subjectType: true,
      // 培训类别
      trainingCategory: true,
      // 技术等级
      jobLevel: true,
      // 培训对象
      trainingObject: true,
      //   岗位类别
      positionCategory: true,
      //学段
      grade: true,
      //学科
      subject: true
    }

    /**
     * 当前网校信息
     */
    envConfig = {
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      //工勤行业
      workServiceId: '',
      // 职业卫生行业Id
      professionHealthIndustryId: '',
      //教师行业
      teacherIndustryId: ''
    }

    constructor() {
      super()
      this.initQueryParam()
      this.trainSchemePage = new UiPage(this.pageScheme, this.pageScheme)
    }

    @Provide() selectBoxTagWidth = 320
    /**
     * 把v-model的value取下来
     */
    @Watch('value', { immediate: true, deep: true })
    getValue() {
      this.hasSelectSchemeMode = cloneDeep(this.value)
      this.dialogShow = false
    }

    /**
     * 动态获取培训专业列表 - 根据培训类别变化
     */
    get skuOptions() {
      return (type: string) => {
        return this.schemeBusinessEntry.skuProperties[type]?.filter((el: SkuVo) => el.hidden === false)
      }
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: UITrainClassCommodityDetail, type: string): string {
      if (row.skuValueNameProperty[type]?.skuPropertyName) {
        const value = row.skuValueNameProperty[type].skuPropertyName
        const valuesArr = value.split('/'),
          lastIndex = valuesArr.length - 1
        return type === 'trainingMajor' && !row.isSocietyIndustry ? valuesArr[lastIndex] : value
      }
      return ''
    }

    /**
     * 培训类别切换响应
     */
    handleTrainingCategory(val: string) {
      const skuVo = new SkuVo()
      skuVo.skuPropertyValueId = val
      this.schemeBusinessEntry.switchTrainCate(skuVo)
    }

    /**
     * 回传
     */
    setValue() {
      this.$emit('input', this.hasSelectSchemeMode)
      this.dialogShow = false
    }

    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      this.initQueryParam()
      this.localSkuProperty = new SchemeSkuProperty()
      this.onShelveStatus = null
      this.schemeName = ''
      this.sortPolicy = new Array<CommoditySkuSortRequest>()
      await this.searchBase()
    }
    /**
     * 处理列表查询参数
     */
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.trainSchemeQueryParam.onShelveRequest.onShelveStatus =
        this.onShelveStatus || this.onShelveStatus === 0 ? this.onShelveStatus : undefined
      this.trainSchemeQueryParam.schemeRequest.schemeName = this.schemeName || undefined
      this.configureTrainSchemeQueryParam()
    }

    /**
     * 分页查询
     */
    async pageScheme() {
      this.trainSchemeQuery.loading = true
      try {
        this.getPageQueryParams()
        const trainSchemeList = await this.getTrainSchemeList()
        this.trainSchemeList = new Array<UITrainClassCommodityDetail>()
        trainSchemeList?.map((el: UITrainClassCommodityDetail) => {
          const item = new UITrainClassCommodityDetail()
          Object.assign(item, el)
          item.isLongTerm = false
          item.isSocietyIndustry = false
          /*if (item.trainingEndDate === this.defaultEndDate && item.trainingBeginDate === this.defaultBeginDate) {
            item.isLongTerm = true
          }*/
          if (item.skuValueNameProperty?.industry?.skuPropertyName === '人社行业') {
            item.isSocietyIndustry = true
          }
          this.trainSchemeList.push(item)
        })
        // console.log('trainSchemeList', this.trainSchemeList)
      } catch (e) {
        console.log('获取培训方案分页列表失败！', e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['schemeTable'] as any)?.doLayout()
        this.trainSchemeQuery.loading = false
      }
    }

    /**
     * 获取培训方案类型
     */
    getSchemeType(row: UITrainClassCommodityDetail) {
      if (row.schemeType === SchemeTypeEnum.chooseCourseLearning) {
        return '培训班-选课规则'
      } else if (row.schemeType === SchemeTypeEnum.autonomousCourseLearning) {
        return '培训班-自主选课'
      } else if (row.schemeType === SchemeTypeEnum.trainingCooperation) {
        return '合作办学'
      } else {
        return ''
      }
    }

    /**
     * 获取培训班列表
     */
    async getTrainSchemeList() {
      if (this.sortPolicy.length) {
        return await this.schemeBusinessEntry.queryTrainClassCommodityList(
          this.trainSchemePage,
          this.trainSchemeQueryParam,
          this.sortPolicy
        )
      } else {
        return await this.schemeBusinessEntry.queryTrainClassCommodityList(
          this.trainSchemePage,
          this.trainSchemeQueryParam
        )
      }
    }

    /**
     * 加载第一页
     */
    async searchBase() {
      this.trainSchemePage.pageNo = 1
      await this.pageScheme()
    }

    /**
     * 配置查询参数
     */
    configureTrainSchemeQueryParam() {
      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.trainSchemeQueryParam.schemeRequest.schemeType = undefined
      }
      const schemeType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      if (
        schemeType === 'chooseCourseLearning' ||
        schemeType === 'autonomousCourseLearning' ||
        schemeType === 'trainingCooperation'
      ) {
        this.trainSchemeQueryParam.schemeRequest.schemeType = schemeType
      } else {
        this.trainSchemeQueryParam.schemeRequest.schemeType = undefined
      }
    }

    /**
     * 获取本地sku选项
     */
    getLocalSkuProperty() {
      const skuProperties = cloneDeep(this.trainSchemeQueryParam.skuPropertyRequest)
      skuProperties.year = !this.localSkuProperty.year ? ([] as string[]) : [this.localSkuProperty.year]
      skuProperties.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
      const localRegion = cloneDeep(this.localSkuProperty.region)
      if (Array.isArray(localRegion) && localRegion.length) {
        const option = new RegionSkuPropertyRequest()
        option.province = localRegion.length >= 1 ? localRegion[0] : undefined
        option.city = localRegion.length >= 2 ? localRegion[1] : undefined
        option.county = localRegion.length >= 3 ? localRegion[2] : undefined
        skuProperties.regionSkuPropertySearch.region.push(option)
        skuProperties.regionSkuPropertySearch.regionSearchType = 1
      } else {
        skuProperties.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      }
      //设置默认查询工勤行业id
      skuProperties.trainingForm = this.localSkuProperty.trainingMode ? [this.localSkuProperty.trainingMode] : undefined
      skuProperties.industry = !this.localSkuProperty.industry ? ([] as string[]) : [this.localSkuProperty.industry]
      skuProperties.subjectType = !this.localSkuProperty.subjectType
        ? ([] as string[])
        : [this.localSkuProperty.subjectType]
      skuProperties.trainingCategory = !this.localSkuProperty.trainingCategory
        ? ([] as string[])
        : [this.localSkuProperty.trainingCategory]
      skuProperties.jobLevel = !this.localSkuProperty.jobLevel ? ([] as string[]) : [this.localSkuProperty.jobLevel]
      skuProperties.trainingProfessional = this.getTrainingProfessional()
      skuProperties.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      skuProperties.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      skuProperties.learningPhase = !this.localSkuProperty.grade ? ([] as string[]) : [this.localSkuProperty.grade]
      skuProperties.discipline = !this.localSkuProperty.subject ? ([] as string[]) : [this.localSkuProperty.subject]
      this.trainSchemeQueryParam.skuPropertyRequest = cloneDeep(skuProperties)
      //   console.log('selectedSkuProperties', JSON.stringify(this.trainSchemeQueryParam.skuPropertyRequest))
    }

    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      // console.log('envConfig', this.envConfig)
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人设行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }

    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.trainSchemeQueryParam = new CommoditySkuRequest()
      this.trainSchemeQueryParam.onShelveRequest = new OnShelveRequest()
      this.trainSchemeQueryParam.schemeRequest = new SchemeRequest()
      this.trainSchemeQueryParam.schemeRequest.schemeName = ''
      this.trainSchemeQueryParam.skuPropertyRequest = new SkuPropertyRequest()
      this.trainSchemeQueryParam.skuPropertyRequest.year = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.trainSchemeQueryParam.skuPropertyRequest.regionSkuPropertySearch.region =
        new Array<RegionSkuPropertyRequest>()
      this.trainSchemeQueryParam.skuPropertyRequest.industry = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.subjectType = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingProfessional = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingObject = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.positionCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.jobLevel = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.learningPhase = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.discipline = new Array<string>()
      this.sortPolicy = new Array<CommoditySkuSortRequest>()
      this.schemeTypeInfo = new Array<string>()
    }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.jobLevel = ''
      this.$set(this.localSkuProperty, 'grade', '')
      this.$set(this.localSkuProperty, 'subject', '')
      this.$set(this.localSkuProperty, 'trainingObject', '')
      this.$set(this.localSkuProperty, 'positionCategory', '')
      // this.localSkuProperty.trainingObject = ''
      // this.localSkuProperty.positionCategory = ''
    }
    updateTrainingCategory(val: string) {
      if (val) {
        this.$set(this.localSkuProperty, 'positionCategory', '')
      }
    }
    updateGrade(val: string) {
      this.$set(this.localSkuProperty, 'grade', val)
      this.$set(this.localSkuProperty, 'subject', '')
    }
    clearSubject() {
      this.localSkuProperty.grade = ''
      this.localSkuProperty.subject = ''
    }
    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const jobLevel = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const trainingObject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const positionCategory = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.POSITION_CATEGORY)
      const grade = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.LEARNING_PHASE)
      const subject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.DISCIPLINE)
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.jobLevel = jobLevel > -1
      this.skuVisible.trainingObject = trainingObject > -1
      this.skuVisible.positionCategory = positionCategory > -1
      this.skuVisible.grade = grade > -1
      this.skuVisible.subject = subject > -1
    }

    /**
     * 响应组件行业Id集合传参
     */
    handleIndustryInfos(values: any) {
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.professionHealthIndustryId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
    }

    async showDialog() {
      this.dialogShow = true
      // 删除多余请求
      this.initQueryParam()
      this.localSkuProperty = new SchemeSkuProperty()
      this.onShelveStatus = null
      this.schemeName = ''
      this.sortPolicy = new Array<CommoditySkuSortRequest>()
      await this.searchBase()
    }
    clear() {
      this.hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
      this.certificateTemplateId = new Array<string>()
      this.setValue()
    }

    selectScheme(item: any) {
      this.certificateTemplateId.push(item.certificateTemplateId)
      this.$emit('getCertificateTemplateId', this.certificateTemplateId)
      if (!this.multiple) {
        this.clear()
      }
      const obj = new HasSelectSchemeMode()
      obj.schemeId = item.commoditySkuId
      obj.scheme = item.commodityBasicData.saleTitle
      obj.id = item.schemeId
      obj.trainingMode.skuPropertyValueId = item.skuValueNameProperty.trainingMode.skuPropertyValueId
      obj.commodityId = item.commoditySkuId
      this.hasSelectSchemeMode.push(obj)
    }

    /**
     * 被选中的方案
     */
    get selected() {
      return (item: any) => {
        const index = this.hasSelectSchemeMode.findIndex((obj) => obj.schemeId === item.commoditySkuId)
        return index === -1
      }
    }
    /**
     * 取消选择学习班
     * @sure 确定删除 非弹窗情况下点击删除后需要触发set方法
     */
    cancelSelectScheme(id: string, sure?: boolean) {
      //const item = this.tableData.filter(p => p.schemeId === id)[0]
      //item['hasSelect'] = false
      const index = this.hasSelectSchemeMode.findIndex((p) => p.schemeId === id)
      this.hasSelectSchemeMode.splice(index, 1)
      if (!sure) {
        this.setValue()
      }
    }

    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }
  }
</script>
