import AbstractEnum from '@api/service/common/enums/AbstractEnum'
export enum LearningExperienceEnum {
  /**
   * 班级心得
   */
  CLASS = 1,
  /**
   * 课程心得
   */
  COURSE = 2
}

export enum AnswerMethodEnum {
  /**
   * 提交附件
   */
  UPLOAD = 1,
  /**
   * 在线编辑
   */
  EDIT = 2
}

export enum ApproveMethodEnum {
  /**
   * 提交自动通过
   */
  AUTO = 1,
  /**
   * 人工审核
   */
  ARTIFICIAL = 2
}

export enum ApproveResultEnum {
  /**
   * 通过
   */
  SUCCESS = 1,
  /**
   * 不通过
   */
  FAIL = 2
}

export enum ApproveStatusEnum {
  /**
   * 已审核
   */
  SUCCESS = 1,
  /**
   * 待审核
   */
  FAIL = 2
}

export class LearningExperienceType extends AbstractEnum<LearningExperienceEnum> {
  static enum = LearningExperienceEnum
  constructor(status?: LearningExperienceEnum) {
    super()
    this.current = status
    this.map.set(LearningExperienceEnum.CLASS, '班级心得')
    this.map.set(LearningExperienceEnum.COURSE, '课程心得')
  }
}

export class AnswerMethodType extends AbstractEnum<AnswerMethodEnum> {
  static enum = AnswerMethodEnum
  constructor(status?: AnswerMethodEnum) {
    super()
    this.current = status
    this.map.set(AnswerMethodEnum.UPLOAD, '提交附件')
    this.map.set(AnswerMethodEnum.EDIT, '在线编辑')
  }
}

export class ApproveMethodType extends AbstractEnum<ApproveMethodEnum> {
  static enum = ApproveMethodEnum
  constructor(status?: ApproveMethodEnum) {
    super()
    this.current = status
    this.map.set(ApproveMethodEnum.ARTIFICIAL, '人工审核')
    this.map.set(ApproveMethodEnum.AUTO, '提交自动通过')
  }
}

export class ApproveResultType extends AbstractEnum<ApproveResultEnum> {
  static enum = ApproveResultEnum
  constructor(status?: ApproveResultEnum) {
    super()
    this.current = status
    this.map.set(ApproveResultEnum.SUCCESS, '通过')
    this.map.set(ApproveResultEnum.FAIL, '不通过')
  }
}

export class ApproveStatusType extends AbstractEnum<ApproveStatusEnum> {
  static enum = ApproveStatusEnum
  constructor(status?: ApproveStatusEnum) {
    super()
    this.current = status
    this.map.set(ApproveStatusEnum.SUCCESS, '已审核')
    this.map.set(ApproveStatusEnum.FAIL, '待审核')
  }
}
