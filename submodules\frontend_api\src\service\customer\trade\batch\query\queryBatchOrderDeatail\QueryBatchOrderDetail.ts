import BatchOrderDetail from './vo/BatchOrderDetail'
class QueryBatchOrderDetail {
  private batchOrderNo = ''
  constructor(batchOrderNo: string) {
    this.batchOrderNo = batchOrderNo
  }
  /**
   * @description:  批次单详情
   */

  batchOrderDetail: BatchOrderDetail = new BatchOrderDetail()

  /**
   * @description: 查询批次单详情
   * @param {*}
   * @return {*}
   */

  async queryBatchOrderDetailByOrderNo() {
    return
  }
}
export default QueryBatchOrderDetail
