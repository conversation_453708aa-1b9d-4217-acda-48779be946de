<template>
  <el-drawer title="期别报到设置" :visible.sync="openDialog" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-form
        ref="reportConfigRef"
        :model="periodConfig.reportConfig"
        label-width="150px"
        class="m-form"
        :rules="rules"
      >
        <el-form-item label="报到形式：" required>
          <el-radio-group v-model="periodConfig.reportConfig.reportingType">
            <el-radio :label="1">打卡报到</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="打卡有效范围：" prop="validRange">
          在培训班办班地点定位打卡，允许微调地点，微调半径：
          <el-input class="input-num" placeholder="请输入" v-model="periodConfig.reportConfig.validRange"></el-input>
          米，建议设为500米
        </el-form-item>
        <el-form-item label="报到打卡时间：" required>
          培训班期别报到时间开始至培训班培训结束时间期间都允许打卡报到
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="openDialog = false">取消</el-button>
      <el-button type="primary" @click="setReport">确定</el-button>
    </div>
    <tem-dialog ref="templateDialogRef"></tem-dialog>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Prop, Inject } from 'vue-property-decorator'
  import TemDialog from './templateDialog.vue'
  import PeriodConfig from '@api/service/management/implement/models/PeriodConfig'
  import SchemeStepProcess from '@api/service/management/train-class/Utils/SchemeStepProcess'
  import { SchemeProcessStatusEnum } from '@api/service/management/train-class/query/enum/SchemeProcessStatusEnum'

  @Component({
    components: {
      TemDialog
    }
  })
  export default class extends Vue {
    @Ref('templateDialogRef') templateDialogRef: TemDialog
    @Ref('reportConfigRef') reportConfigRef: any
    @Inject('SchemeStepProcess')
    SchemeStepProcess: SchemeStepProcess

    // todo
    form: any = {}
    openDialog = false
    periodConfig = new PeriodConfig()

    rules = {
      validRange: [
        { required: true, message: '请输入打卡有效范围', trigger: 'blur' },
        {
          validator: (rule: any, value: any, callback: any) => {
            const reg = /^(0|[1-9]\d*)$/ // 匹配 0 或正整数
            if (value === '' || value === undefined) {
              callback(new Error('请输入打卡有效范围'))
            } else if (!reg.test(value)) {
              callback(new Error('请输入非负整数'))
            } else {
              callback() // 验证通过
            }
          },
          trigger: 'blur'
        }
      ]
    }

    async setReport() {
      try {
        await this.reportConfigRef.validate()
        const schemeId = this.$route.params.schemeId
        const resp = await this.SchemeStepProcess.checkSchemeCanModify(schemeId)
        if (resp.code === SchemeProcessStatusEnum.finish) {
          const res = await this.periodConfig.setReportPeriodInfo()
          if (res.isSuccess()) {
            this.$emit('updateSuccess')
            this.openDialog = false
          } else {
            this.$message.error(res.getMessage())
          }
        } else {
          this.$message.error(resp.message as string)
        }
      } catch (e) {
        if (e.message) {
          this.$message.error(e.message)
        }

        console.log(e)
      }
    }

    /**
     * 听课证模版
     */
    openTemplate(row: any) {
      this.templateDialogRef.openDialog = true
    }

    /**
     * 预览
     */
    preview(row: any) {
      window.open('/mfs/Cooper/templates/item.previewUrl', '_blank')
    }
  }
</script>
