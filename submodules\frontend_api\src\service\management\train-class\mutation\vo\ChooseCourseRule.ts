export class SecondElectiveMaxPeriod {
  /**
   * 分类ID
   */
  id: string = undefined
  /**
   * 学时
   */
  electiveMaxPeriod: number = undefined
}
/**
 * 选课规则
 */
class ChooseCourseRule {
  // region properties
  /**
   * 选课规则id
   */
  id = ''
  /**
   * 选课规则名称
   */
  name = ''
  /**
   *必修要求学时，类型为number
   */
  compulsoryPeriod = 0
  /**
   *选修课最大允许学时，类型为number
   */
  electiveMaxPeriod = 0
  /**
   * 选项课二级分类学时要求
   */
  secondElectiveMaxPeriod: SecondElectiveMaxPeriod[] = []
  /**
   *是否允许最后一门超出最大学时，类型为boolean
   */
  allowLastChooseOver = false
  /**
   *是否约束重复选课，类型为boolean
   */
  constrainedRepeatSelection = false
  /**
   *约束重复选课范围，类型为string[]
   */
  constrainedRangeKeyList: string[] = []
  // endregion
  // region methods

  // endregion
}
export default ChooseCourseRule
