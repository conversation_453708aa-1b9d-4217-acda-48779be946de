import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import LearningScenesAntiConfig from '@api/service/common/models/anticheat/LearningScenesAntiConfig'
import Response, { ResponseStatus } from '@api/Response'
import AntiCheatConfigModule, { LearningConfigParameter } from '@api/service/customer/anti-cheat/AntiCheatConfigModule'
import AntiVerifyResult from '@api/service/customer/anti-cheat/models/AntiVerifyResult'
import InterceptResult from '@api/service/customer/anti-cheat/models/InterceptResult'
import $http from '@packages/request'
import Marker from '@api/service/customer/anti-cheat/models/Marker'
import AntiUtils from '@api/service/customer/anti-cheat/models/AntiUtils'
import HttpResponse from '@api/service/customer/anti-cheat/models/HttpResponse'
import { Role, RoleType } from '@api/Secure'
import PushAntiCheatResultParams from '@api/service/customer/anti-cheat/models/PushAntiCheatResultParams'
import PostAntiCheatResultParams from '@api/service/customer/anti-cheat/models/PostAntiCheatResultParams'
import AntiLearningCommitTokenParams from '@api/service/customer/anti-cheat/models/AntiLearningCommitTokenParams'

/**
 * 本地状态数据
 */
interface LearningAntiCheatState {
  /**
   * 当前平台学习场景防作弊配置
   */
  learningScenesConfig: LearningScenesAntiConfig
  /**
   * 请求拦截结果
   */
  interceptResult: InterceptResult
  /**
   * 短码状态
   */
  codeStateResult: AntiVerifyResult
  /**
   * 防作弊验证结果
   */
  antiVerifyResult: AntiVerifyResult
  /**
   * 是否加载学习场景防作弊
   */
  isLearningScenesConfigLoad: boolean
  /**
   * 当前课程学习进度提交Token
   */
  antiLearningCommitToken: string
}

export class InterceptionParameter {
  /**
   * 防作弊配置编号
   * @see LearningScenesAntiConfig#id
   */
  configId = ''
  /**
   * 防作弊模式编号
   * @see LearningScenesAntiConfig#shapeModel#id
   */
  modeId = ''
  userId = ''
  platformId = ''
  platformVersionId = ''
  projectId = ''
  subProjectId = ''
  organizationId = ''
  unitId = ''
  /**
   * 学习方案编号
   */
  schemeId = ''
  /**
   * 课程学习方案编号
   */
  learningId = ''
  /**
   * 课程编号
   */
  courseId = ''
  /**
   * 课件编号
   */
  coursewareId = ''
  /**
   * 维度
   */
  dimensions: number
  /**
   * 当前学习课件的刻度值：单位：秒
   */
  timeLength?: number
  /**
   * 当前学习课件的进度值
   */
  schedule?: number
  /**
   * 记录点
   */
  recordPoint = ''
  /**
   *  跟踪点 为2时随机拍摄
   */
  traceMode?: number
}

const internalFunction = {
  generateMarkerList(schemeId: string, learningId: string, courseId: string, coursewareId: string) {
    const markerList: Array<Marker> = new Array<Marker>()
    markerList.push(
      Marker.create('SchemeId', schemeId),
      Marker.create('LearningId', learningId),
      Marker.create('CourseId', courseId),
      Marker.create('CoursewareId', coursewareId)
    )
    return markerList
  }
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerLearningAntiCheatModule', store })
class LearningAntiCheatModule extends VuexModule implements LearningAntiCheatState {
  /**
   * 当前平台学习场景防作弊配置
   */
  public learningScenesConfig: LearningScenesAntiConfig = new LearningScenesAntiConfig()
  /**
   * 请求拦截结果
   */
  public interceptResult: InterceptResult = new InterceptResult()
  /**
   * 短码状态
   */
  public codeStateResult: AntiVerifyResult = new AntiVerifyResult()
  /**
   * 防作弊验证结果
   */
  public antiVerifyResult: AntiVerifyResult = new AntiVerifyResult()
  /**
   * 是否加载学习场景防作弊
   */
  public isLearningScenesConfigLoad = false
  /**
   * 学习过程的随机拍摄点位
   */
  public randomPoints: number[] = new Array<number>()

  /**
   * 当前课程学习进度提交Token
   */
  public antiLearningCommitToken = ''
  //region action
  /**
   * 加载防作弊配置
   */
  @Action
  @Role([RoleType.user])
  public async init(param: LearningConfigParameter): Promise<ResponseStatus> {
    if (!this.isLearningScenesConfigLoad) {
      const response = await AntiCheatConfigModule.initLearningConfig(param)
      if (response.isSuccess()) {
        const config = AntiCheatConfigModule.getLearningAntiConfig(param)
        if (config) {
          this.SET_LEARNING_CONFIG(config)
        }
      }
      return Promise.resolve(response)
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 重新加载学习场景防作弊配置
   */
  @Action
  @Role([RoleType.user])
  public async doReloadLearningConfig(param: LearningConfigParameter): Promise<ResponseStatus> {
    this.SET_IS_LOAD_LEARNING_CONFIG(false)
    await AntiCheatConfigModule.doReloadLearningConfig(param)
    return this.init(param)
  }

  /**
   * 请求判断是否进行防作弊
   * @param params 参数：params - 请求参数
   * @typedef InterceptionParameter
   */
  @Action
  @Role([RoleType.user])
  public async doInterception(params: InterceptionParameter): Promise<ResponseStatus> {
    const requestParam = {
      configId: params.configId,
      modeId: params.modeId,
      userId: params.userId,
      platformId: params.platformId,
      platformVersionId: params.platformVersionId,
      projectId: params.projectId,
      subProjectId: params.subProjectId,
      organizationId: params.organizationId,
      unitId: params.unitId,
      dimensions: params.dimensions,
      recordPoint: params.recordPoint,
      markers: internalFunction.generateMarkerList(
        params.schemeId,
        params.learningId,
        params.courseId,
        params.coursewareId
      ),
      timeLength: params.timeLength,
      schedule: params.schedule
    }
    const response = await $http.post(
      AntiUtils.getInterceptionServiceUrl(),
      AntiUtils.generateRequestParameter(requestParam)
    )
    const result = Object.assign(new HttpResponse(), response.data)
    if (result.successfully()) {
      const interceptResult = Object.assign(new InterceptResult(), result.data)
      this.SET_INTERCEPT_RESULT(interceptResult)
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, result.getMessage()))
    }
  }

  /**
   * 请求获取随机拍摄的位置
   * @param params 参数：params - 请求参数
   * @typedef InterceptionParameter
   */
  @Action
  @Role([RoleType.user])
  public async applyRandomFacePoints(params: InterceptionParameter): Promise<ResponseStatus> {
    const requestParam = {
      configId: params.configId,
      modeId: params.modeId,
      userId: params.userId,
      platformId: params.platformId,
      platformVersionId: params.platformVersionId,
      projectId: params.projectId,
      subProjectId: params.subProjectId,
      organizationId: params.organizationId,
      unitId: params.unitId,
      markers: internalFunction.generateMarkerList(
        params.schemeId,
        params.learningId,
        params.courseId,
        params.coursewareId
      )
    }
    const response = await AntiCheatConfigModule.applyCoursewareRandomFacePoints(requestParam)
    if (response.isSuccess()) {
      const recordPoints = AntiCheatConfigModule.coursewareRandomPointResponse.recordPoints
      this.SET_RANDOM_POINTS(recordPoints)
    }
    return Promise.resolve(response)
  }

  /**
   * 请求检测短码状态
   * @param params 参数：code - 短码
   */
  @Action
  @Role([RoleType.user])
  public async doCheckCodeState(params: { code: string }): Promise<ResponseStatus> {
    const response = await $http.get(AntiUtils.getCheckCodeStateServiceUrl() + '?code=' + params.code)
    const result = Object.assign(new HttpResponse(), response.data)
    if (result.successfully()) {
      const codeResult = Object.assign(new AntiVerifyResult(), result.data)
      this.SET_CODE_STATE_RESULT(codeResult)
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, result.getMessage()))
    }
  }

  /**
   * 请求发送照片进行防作弊验证
   * @param params 参数：code - 短码；photo - 照片的base64字符串
   */
  @Action
  @Role([RoleType.user])
  public async doPush(params: { code: string; photo: string }): Promise<ResponseStatus> {
    const response = await $http.post(AntiUtils.getPushServiceUrl(), AntiUtils.generateRequestParameter(params))
    const result = Object.assign(new HttpResponse(), response.data)
    if (result.successfully()) {
      const pushResult = Object.assign(new AntiVerifyResult(), result.data)
      this.SET_ANTI_VERIFY_RESULT(pushResult)
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, result.getMessage()))
    }
  }

  /**
   * 通知“防作弊验证结果”
   * @param params
   */
  @Action
  @Role([RoleType.user])
  public async doPushAntiCheatResult(params: PushAntiCheatResultParams): Promise<ResponseStatus> {
    const response = await $http.post(AntiUtils.getPushServiceUrl(), AntiUtils.generateRequestParameter(params))
    const result = Object.assign(new HttpResponse(), response.data)
    if (result.successfully()) {
      const pushResult = Object.assign(new AntiVerifyResult(), result.data)
      this.SET_ANTI_VERIFY_RESULT(pushResult)
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, result.getMessage()))
    }
  }

  @Action
  @Role([RoleType.user])
  public async postAntiCheatResult(params: PostAntiCheatResultParams): Promise<ResponseStatus> {
    let host = process.env.VUE_APP_HOST
    // #ifdef H5
    host = ''
    // #endif
    const response = await $http.post(`${host}/web/randomCode/callBackV2`, params)
    console.log(response, response.data, 'post接口返回')
    // 成功：response.data.code 200 为空值  失败：response.data.code 500 有具体信息
    if (response.data?.code === 200) {
      return new ResponseStatus(200, '')
    } else {
      return new ResponseStatus(500, '')
    }
  }

  /**
   * 获取防作弊下的课程学习进度提交token
   */
  @Action
  public async applyLearningCommitToken(params: AntiLearningCommitTokenParams) {
    const response = await $http.post(
      AntiUtils.getApplyLearningCommitTokenPath(),
      AntiUtils.generateRequestParameter(params)
    )
    const result = Object.assign(new HttpResponse(), response.data)
    if (result.successfully()) {
      this.SET_ANTI_LEARNING_COMMIT_TOKEN(result.data)
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, result.getMessage()))
    }
  }

  //endregion

  //region mutation
  /**
   * 设置学习场景防作弊配置
   * @param params
   * @constructor
   */
  @Mutation
  private SET_LEARNING_CONFIG(params: LearningScenesAntiConfig) {
    this.learningScenesConfig = params
    this.isLearningScenesConfigLoad = true
  }

  /**
   * 设置加载学习场景防作弊配置
   * @param isLoad 是否加载
   * @constructor
   */
  @Mutation
  private SET_IS_LOAD_LEARNING_CONFIG(isLoad: boolean) {
    this.isLearningScenesConfigLoad = isLoad
  }

  /**
   * 设置防作弊拦截验证结果
   * @param params
   * @constructor
   */
  @Mutation
  private SET_INTERCEPT_RESULT(params: InterceptResult) {
    this.interceptResult = params
  }

  /**
   * 设置短码验证结果
   * @param params
   * @constructor
   */
  @Mutation
  private SET_CODE_STATE_RESULT(params: AntiVerifyResult) {
    this.codeStateResult = params
  }

  /**
   * 设置防作弊验证结果
   * @param params
   * @constructor
   */
  @Mutation
  private SET_ANTI_VERIFY_RESULT(params: AntiVerifyResult) {
    this.antiVerifyResult = params
  }
  /**
   * 设置随机拍摄位置
   * @param params
   * @constructor
   */
  @Mutation
  private SET_RANDOM_POINTS(params: Array<number>) {
    this.randomPoints = params
  }

  /**
   *
   */
  @Mutation
  private SET_ANTI_LEARNING_COMMIT_TOKEN(token: string) {
    this.antiLearningCommitToken = token
  }

  //endregion

  //region getter
  /**
   * 获取拦截结果
   * @see doInterception
   */
  get interception(): InterceptResult {
    return this.interceptResult
  }

  /**
   * 获取短码状态
   * @see doCheckCodeState
   */
  get codeState(): AntiVerifyResult {
    return this.codeStateResult
  }

  /**
   * 获取上传照片验证防作弊结果
   * @see doPush
   */
  get pushResult(): AntiVerifyResult {
    return this.antiVerifyResult
  }

  //endregion
}

export default getModule(LearningAntiCheatModule)
