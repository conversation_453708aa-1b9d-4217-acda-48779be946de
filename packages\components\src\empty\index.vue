<template>
  <div class="m-no-date" :class="[computedClass]">
    <img class="img is-small" src="@design/admin/assets/images/no-data-normal.png" alt="" />
    <div class="date-bd">
      <p class="txt f-c9">{{ description }}</p>
      <div class="f-mt10" v-if="showAction">
        <el-button type="primary" size="small" @click="doAction">{{ actionText }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      showAction: {
        type: Boolean,
        default: false
      },
      actionText: {
        type: String,
        default: '操作按钮'
      },
      description: {
        type: String,
        default: '暂无数据'
      },
      isVertical: {
        type: Boolean,
        default: false
      }
    },
    computed: {
      computedClass() {
        return this.isVertical ? '' : 'is-row'
      }
    },
    methods: {
      doAction() {
        this.$emit('action')
      }
    }
  }
</script>
