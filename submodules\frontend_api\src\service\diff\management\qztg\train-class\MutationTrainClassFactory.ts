import MutationTrainClassCommodityClass from '@api/service/management/train-class/mutation/MutationTrainClassCommodityClass'
import MutationCoursePackageSynchronization from '@api/service/management/train-class/mutation/MutationCoursePackageSynchronization'
import MutationCreateTrainClassCommodity from '@api/service/diff/management/qztg/train-class/mutation/MutationCreateTrainClassCommodity'
import { CreateTrainClassTypeEnum } from '@api/service/management/train-class/mutation/Enum/CreateTrainClassTypeEnum'
import MutationDeleteExamRecord from '@api/service/management/train-class/mutation/MutationDeleteExamRecord'
import MutationQualifiedCourse from '@api/service/management/train-class/mutation/MutationQualifiedCourse'
import MutationQualifiedCourseware from '@api/service/management/train-class/mutation/MutationQualifiedCourseware'
import MutationQualifiedTrainClass from '@api/service/management/train-class/mutation/MutationQualifiedTrainClass'
import MutationDeleteCourse from '@api/service/management/train-class/mutation/MutationDeleteCourse'
import QueryTrainClassDetailClass from '@api/service/diff/management/qztg/train-class/query/QueryTrainClassDetailClass'

/**
 * 培训班业务工厂类
 */
class MutationTrainClassFactory {
  // region properties
  // endregion
  // region methods
  /**
   *获取培训班商品业务对象
   */
  getMutationTrainClassCommodityClass() {
    return new MutationTrainClassCommodityClass()
  }
  /**
   *获取培训班课程包同步对象
   */
  getMutationCoursePackageSynchronization() {
    return new MutationCoursePackageSynchronization()
  }
  /**
   * 获取创建|更新|复制培训班商品类
   */
  async getMutationCreateTrainClassCommodity(
    createType = CreateTrainClassTypeEnum.CreateTrainClassTypeEnumCreate,
    commodityId?: string
  ): Promise<MutationCreateTrainClassCommodity> {
    if (createType == CreateTrainClassTypeEnum.CreateTrainClassTypeEnumCreate) {
      return new MutationCreateTrainClassCommodity()
    } else {
      const queryDetail = new QueryTrainClassDetailClass()
      queryDetail.commodityId = commodityId
      await queryDetail.queryTrainClassDetail()
      if (createType == CreateTrainClassTypeEnum.CreateTrainClassTypeEnumUpdate) {
        return queryDetail.getUpdateClass()
      } else {
        return queryDetail.getCopyClass()
      }
    }
  }

  /**
   * 获取课程一键合格业务对象
   */
  getMutationQualifiedCourse() {
    return new MutationQualifiedCourse()
  }

  /**
   * 获取课件一键合格业务对象
   */
  getMutationQualifiedCourseware() {
    return new MutationQualifiedCourseware()
  }

  /**
   * 获取培训班一键合格业务对象
   */
  getMutationQualifiedTrainClass() {
    return new MutationQualifiedTrainClass()
  }

  /**
   * 获取删除考试记录业务对象
   */
  getMutationDeleteExamRecord() {
    return new MutationDeleteExamRecord()
  }

  /**
   * 获取删除课程业务对象
   */
  getMutationDeleteCourse() {
    return new MutationDeleteCourse()
  }
  // endregion
}
export default MutationTrainClassFactory
