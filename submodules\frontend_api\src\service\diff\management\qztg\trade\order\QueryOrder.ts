import { Page, Response } from '@hbfe/common'
import QueryOrderListVo from '@api/service/diff/management/qztg/trade/order/model/QueryOrderListVo'
import OrderDetailVo from '@api/service/diff/management/qztg/trade/order/model/OrderDetailVo'
import MsTradeQuery, {
  OrderBasicDataRequest,
  OrderInfoRequest,
  OrderSortField,
  OrderSortRequest,
  ReturnOrderRequest,
  SortPolicy,
  SubOrderInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import OrderStatisticResponseVo from '@api/service/management/trade/single/order/query/vo/OrderStatisticResponseVo'
import { uniq } from 'lodash'
import { ReturnOrderStatusEnum } from '@api/service/management/trade/single/order/enum/returnOrderStatusEnum'
import QueryPlatform from '@api/service/diff/common/qztg/dictionary/QueryPlatform'
import DataExportBackstage from '@api/diff-gateway/qztg-data-export-gateway-backstage'
import QueryOrder from '@api/service/management/trade/single/order/query/QueryOrder'

/**
 * @description 订单查询
 */
class QueryOrderQztg extends QueryOrder {
  /**
   * 查询订单商品分页
   * @param {Page} page - 分页参数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryOrderList(page: Page, queryParams: QueryOrderListVo, isBusinessConsult: boolean) {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName || queryParams.loginAccount)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) {
        page.totalSize = 0
        page.totalPageSize = 0
        return [] as OrderDetailVo[]
      }
    }
    const request = await queryParams.to(isBusinessConsult)
    // 过滤批次单产生的订单
    if (!request.orderBasicData) request.orderBasicData = new OrderBasicDataRequest()
    // request.orderBasicData.channelTypesList = [1, 3]
    // request.orderBasicData.orderType = 1
    const option = new OrderSortRequest()
    option.field = OrderSortField.ORDER_NORMAL_TIME
    option.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(option) as OrderSortRequest[]
    const response = await MsTradeQuery.pageOrderInServicer({
      page,
      request,
      sortRequest
    })
    await QueryPlatform.queryList()
    page.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize
    const result =
      response.status.isSuccess() && this.isWeightyArr(response.data?.currentPageData)
        ? response.data?.currentPageData?.map(OrderDetailVo.from)
        : ([] as OrderDetailVo[])
    const res = (await this.fillBuyerInfo(result)) as OrderDetailVo[]
    // * 添加退款订单信息
    if (isBusinessConsult && response?.data?.currentPageData?.length) {
      // 返回方案id,用于判断是否是华医网特殊方案
      res.map(async (item) => {
        const orderObj = response.data.currentPageData.find((res) => res.orderNo === item.orderNo)
        if (orderObj && orderObj?.subOrderItems && orderObj?.subOrderItems?.length) {
          const detailVo = orderObj.subOrderItems[0].deliveryCommoditySku?.resource
          item.schemeId = (detailVo as any)?.schemeId || ''
        }
      })
      let orderIds = response.data.currentPageData.map((order) => {
        if (order.orderNo) {
          return order.orderNo
        }
      })

      if (orderIds.length) {
        orderIds = uniq(orderIds)
        const request = new ReturnOrderRequest()
        request.subOrderInfo = new SubOrderInfoRequest()
        request.subOrderInfo.orderInfo = new OrderInfoRequest()
        request.subOrderInfo.orderInfo.orderNoList = orderIds
        const newPage = new Page()
        newPage.pageNo = 1
        newPage.pageSize = page.pageSize
        const response = await MsTradeQuery.pageReturnOrderInServicer({
          page: newPage,
          request
        })

        res.map(async (item) => {
          const curOrder = response.data.currentPageData?.find(
            (res) => res.subOrderInfo.orderInfo.orderNo === item.orderNo
          )
          if (curOrder?.returnOrderNo) {
            if ([0, 1, 2, 4, 5, 6].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunding
            }
            if ([8, 9, 10, 11].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunded
            }
            if ([3, 7].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refundFailure
            }
          } else {
            item.returnOrderStatus = ReturnOrderStatusEnum.noRefund
          }
          if (!item.receiveAccountType) {
            const res = await MsTradeQuery.getBatchOrderInServicer(item.batchOrderNo)
            if (res.status?.isSuccess()) {
              item.receiveAccountType = res.data?.payInfo?.receiveAccount?.receiveAccountType == 1 ? 0 : 1
              item.payChannelName = res.data?.payInfo?.receiveAccount?.payChannelName
            }
          }
        })
      }
    }
    return res
  }

  /**
   * 查询分销订单商品分页
   * @param {Page} page - 分页参数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryFxOrderList(page: Page, queryParams: QueryOrderListVo, isBusinessConsult: boolean) {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return [] as OrderDetailVo[]
    }
    const request = await queryParams.to(isBusinessConsult)
    // 过滤批次单产生的订单
    if (!request.orderBasicData) request.orderBasicData = new OrderBasicDataRequest()
    // request.orderBasicData.channelTypesList = [1, 3]
    // request.orderBasicData.orderType = 1
    const option = new OrderSortRequest()
    option.field = OrderSortField.ORDER_NORMAL_TIME
    option.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(option) as OrderSortRequest[]
    const response = await MsTradeQuery.pageOrderInDistributor({
      page,
      request,
      sortRequest
    })
    page.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize
    const result =
      response.status.isSuccess() && this.isWeightyArr(response.data?.currentPageData)
        ? response.data?.currentPageData?.map(OrderDetailVo.from)
        : ([] as OrderDetailVo[])
    // console.log('filterOrderList', result, response)
    const res = await this.fillBuyerInfo(result)
    // * 添加退款订单信息
    if (isBusinessConsult && response?.data?.currentPageData?.length) {
      let orderIds = response.data.currentPageData.map((order) => {
        if (order.orderNo) {
          return order.orderNo
        }
      })

      if (orderIds.length) {
        orderIds = uniq(orderIds)
        const request = new ReturnOrderRequest()
        request.subOrderInfo = new SubOrderInfoRequest()
        request.subOrderInfo.orderInfo = new OrderInfoRequest()
        request.subOrderInfo.orderInfo.orderNoList = orderIds
        const newPage = new Page()
        newPage.pageNo = 1
        newPage.pageSize = page.pageSize
        const response = await MsTradeQuery.pageReturnOrderInDistributor({
          page: newPage,
          request
        })

        res.map(async (item) => {
          const curOrder = response.data.currentPageData.find(
            (res) => res.subOrderInfo.orderInfo.orderNo === item.orderNo
          )
          if (curOrder?.returnOrderNo) {
            if ([0, 1, 2, 4, 5, 6].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunding
            }
            if ([8, 9, 10].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunded
            }
            if ([3, 7].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refundFailure
            }
          } else {
            item.returnOrderStatus = ReturnOrderStatusEnum.noRefund
          }
          if (!item.receiveAccountType) {
            const res = await MsTradeQuery.getBatchOrderInDistributor(item.batchOrderNo)
            item.receiveAccountType = res.data.payInfo.receiveAccount.receiveAccountType == 1 ? 0 : 1
            item.payChannelName = res.data.payInfo.receiveAccount.payChannelName
          }
        })
      }
    }
    return res
  }
  /**
   * 获取订单总金额、总数量
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryOrderListStatistic(
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderStatisticResponseVo> {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return new OrderStatisticResponseVo()
    }
    const result = new OrderStatisticResponseVo()
    result.totalOrderCount = await this.getOrderTotalCount(queryParams, isBusinessConsult)
    result.totalOrderAmount = await this.getOrderTotalAmount(queryParams, isBusinessConsult)
    // console.log('orderListStatistic', result)
    return result
  }

  /**
   * 获取订单总金额、总数量 去学时
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryOrderListStatisticRemoveTotalPeriod(
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderStatisticResponseVo> {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName || queryParams.loginAccount)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return new OrderStatisticResponseVo()
    }
    const result = new OrderStatisticResponseVo()
    // result.totalOrderCount = await this.getOrderTotalCount(queryParams, isBusinessConsult)
    const res = await this.getOrderTotalAmountRemoveTotalPeriod(queryParams, isBusinessConsult)
    result.totalOrderAmount = res.totalOrderAmount
    result.totalOrderCount = res.totalOrderCount
    // console.log('orderListStatistic', result)
    return result
  }

  /**
   * 导出订单列表数据
   */
  async exportOrder(exportParams: QueryOrderListVo): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await exportParams.to(false)
    const response = await DataExportBackstage.exportOrderExcelInServicer({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
  /**
   * 导出订单列表数据（分销）
   */
  async exportFxOrder(exportParams: QueryOrderListVo): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await exportParams.to(false)
    const response = await DataExportBackstage.exportOrderExcelInDistributor({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
}

export default QueryOrderQztg
