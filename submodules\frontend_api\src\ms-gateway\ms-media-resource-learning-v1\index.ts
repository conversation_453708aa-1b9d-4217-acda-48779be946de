import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-media-resource-learning-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-media-resource-learning-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * <AUTHOR>
@since 2023/11/27
 */
export class CourseRelatedCompleteRequest {
  /**
   * 文件路径
   */
  filePath?: string
  /**
   * 课程学习记录id
   */
  courseLearningRecordId?: string
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  unitId?: string
  servicerId?: string
  userId?: string
}

/**
 * 课件媒体学习token申请
 */
export class CoursewareMediaLearningTokenApplyByCourseRecordTokenRequest {
  /**
   * 课件编号
   */
  coursewareId?: string
  /**
   * 多媒体编号
   */
  multiMediaId?: string
  /**
   * 课程学习记录token
   */
  courseRecordToken?: string
}

/**
 * 课件媒体学习token申请
 */
export class CoursewareMediaLearningTokenApplyRequest {
  /**
   * 课程学习播放凭证
   */
  courseLearningPlayToken?: string
  /**
   * 课件编号
   */
  coursewareId?: string
  /**
   * 多媒体编号
   */
  multiMediaId?: string
}

/**
 * 课件媒体学习token申请
<AUTHOR>
@since 2022/1/20
 */
export class CoursewareMediaLearningTokenResponse {
  /**
   * 申请结果
   */
  applyResult: TokenResponse
  /**
   * 课件媒体学习token申请
   */
  token: string
}

/**
 * 课程学习信息
<AUTHOR>
@since 2021/12/3
 */
export class LearningCourseRecordBeTokenResponse {
  token: string
  /**
   * 课程编号
   */
  courseId: string
  /**
   * 课件学习进度
   */
  schedule: number
  /**
   * 学习状态，0/1/2，未学习/学习中/学习完成
   */
  studyStatus: number
  /**
   * 当前课程下所有课件学习信息
   */
  learningCoursewareRecords: Array<LearningCoursewareRecordResponse>
  /**
   * 学习状态码
   */
  learningCode: LearningErrorCodeResponse
  /**
   * code&#x3D;&#x3D;L90001 有这个值
已学习(时长/课时)
   */
  timeLength: number
  /**
   * code&#x3D;&#x3D;L90001 有这个值
学习规则类型1&#x3D;时长(秒) 2&#x3D;课时
   */
  ruleType: number
}

/**
 * 课程学习信息
<AUTHOR>
@since 2021/12/3
 */
export class LearningCourseRecordResponse {
  /**
   * 课程编号
   */
  courseId: string
  /**
   * 课件学习进度
   */
  schedule: number
  /**
   * 学习状态，0/1/2，未学习/学习中/学习完成
   */
  studyStatus: number
  /**
   * 当前课程下所有课件学习信息
   */
  learningCoursewareRecords: Array<LearningCoursewareRecordResponse>
  /**
   * 学习状态码
   */
  learningCode: LearningErrorCodeResponse
  /**
   * code&#x3D;&#x3D;L90001 有这个值
已学习(时长/课时)
   */
  timeLength: number
  /**
   * code&#x3D;&#x3D;L90001 有这个值
学习规则类型1&#x3D;时长(秒) 2&#x3D;课时
   */
  ruleType: number
}

/**
 * 课件学习信息
<AUTHOR>
@since 2021/12/3
 */
export class LearningCoursewareRecordResponse {
  /**
   * 课件编号
   */
  coursewareId: string
  /**
   * 课件学习进度
   */
  schedule: number
  /**
   * 学习状态，0/1/2，未学习/学习中/学习完成
   */
  studyStatus: number
  /**
   * 当前课件下所有媒体学习信息
   */
  learningMediaRecords: Array<LearningMediaRecordResponse>
}

/**
 * 学习代码
<AUTHOR>
@since 2021/12/6
 */
export class LearningErrorCodeResponse {
  /**
   * 代码
   */
  code: string
  /**
   * 信息
   */
  message: string
}

/**
 * 媒体学习信息
<AUTHOR>
@since 2021/12/3
 */
export class LearningMediaRecordResponse {
  /**
   * 课件编号
   */
  coursewareId: string
  /**
   * 多媒体编号
   */
  multiMediaId: string
  /**
   * 多媒体学习进度
   */
  schedule: number
  /**
   * 当前学习时长
   */
  currentLearningTimeLength: number
  /**
   * 最后播放刻度
   */
  lastPlayScale: number
  /**
   * 学习状态，0/1/2，未学习/学习中/学习完成
   */
  studyStatus: number
  /**
   * 最后学习时间
   */
  lastLearningTime: string
  /**
   * 是否最后一次学习媒体
   */
  isLastLearning: boolean
}

/**
 * 学习结果
<AUTHOR>
@since 2021/12/7
 */
export class LearningResultResponse {
  /**
   * 学习状态码
   */
  learningCode: LearningErrorCodeResponse
  /**
   * 计时凭证信息
   */
  timingToken: TimingTokenResponse
  /**
   * code&#x3D;&#x3D;L90001 有这个值
已学习(时长/课时)
   */
  timeLength: number
  /**
   * code&#x3D;&#x3D;L90001 有这个值
学习规则类型1&#x3D;时长(秒) 2&#x3D;课时
   */
  ruleType: number
}

/**
 * 计时凭证信息
<AUTHOR>
@since 2021/12/7
 */
export class TimingTokenResponse {
  /**
   * 计时凭证
   */
  token: string
  /**
   * 课程编号
   */
  courseId: string
  /**
   * 课件编号
   */
  coursewareId: string
  /**
   * 多媒体编号
   */
  multiMediaId: string
  /**
   * 课程进度
   */
  courseSchedule: number
  /**
   * 课件进度
   */
  coursewareSchedule: number
  /**
   * 多媒体进度
   */
  multiMediaSchedule: number
  /**
   * 课程学习状态，0/1/2，未学习/学习中/学习完成
   */
  courseStudyStatus: number
  /**
   * 课件学习状态，0/1/2，未学习/学习中/学习完成
   */
  coursewareStudyStatus: number
  /**
   * 多媒体学习状态，0/1/2，未学习/学习中/学习完成
   */
  multiMediaStudyStatus: number
  /**
   * 当前媒体已学习刻度，单位：秒
   */
  mediaLearningTimeLength: number
  /**
   * 当前媒体最后播放刻度，单位：秒
   */
  lastmediaPlayScale: number
  /**
   * 计时间隔时间，单位：秒
   */
  intervalTime: number
}

/**
 * 凭证响应基类
<AUTHOR>
@since 2022/1/20
 */
export class TokenResponse {
  /**
   * 代码：
200-成功
   */
  code: string
  /**
   * 信息
   */
  message: string
}

/**
 * 用户在线学习实体
<AUTHOR>
@since 2021/12/3
 */
export class UserCurrentLearningResponse {
  /**
   * 用户编号
   */
  userId: string
  /**
   * 课程学习记录编号
   */
  courseLearningRecordId: string
  /**
   * 课件学习记录编号
   */
  coursewareLearningRecordId: string
  /**
   * 多媒体学习记录编号
   */
  multiMediaLearningRecordId: string
  /**
   * 参训资格编号
   */
  qualificationId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 计时凭证
   */
  token: string
  /**
   * 上线时间
   */
  startOnlineTime: string
  /**
   * 最后学习时间
   */
  lastLearningTime: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取当前用户正在学习的学习信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserCurrentLearningInfo(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getUserCurrentLearningInfo,
    operation?: string
  ): Promise<Response<UserCurrentLearningResponse>> {
    return commonRequestApi<UserCurrentLearningResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 课件媒体学习token申请
   * 状态码：
   * <pre>
   * 200 - 成功
   * E10000 - 参数异常
   * E90001 - 内部异常
   * </pre>
   * @param request 参数
   * @return token信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCoursewareLearningToken(
    request: CoursewareMediaLearningTokenApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCoursewareLearningToken,
    operation?: string
  ): Promise<Response<CoursewareMediaLearningTokenResponse>> {
    return commonRequestApi<CoursewareMediaLearningTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 课件媒体学习token申请
   * 状态码：
   * <pre>
   * 200 - 成功
   * E10000 - 参数异常
   * E90001 - 内部异常
   * </pre>
   * @param request 参数
   * @return token信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCoursewareLearningTokenByToken(
    request: CoursewareMediaLearningTokenApplyByCourseRecordTokenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCoursewareLearningTokenByToken,
    operation?: string
  ): Promise<Response<CoursewareMediaLearningTokenResponse>> {
    return commonRequestApi<CoursewareMediaLearningTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请多媒体计时
   * 状态码：
   * <pre>
   * 200 - 成功
   * E10000 - 参数异常
   * E90001 - 内部异常
   * L10000 - 由于学习的课程资源变更，学习被锁定，无法学习
   * L10001 - 由于学习的课程进度变更，学习被锁定，无法学习
   * L10002 - 由于学习的课程进度重置，学习被锁定，无法学习
   * L20000 - 您被系统判定为疑似机器学习，学习被锁定，无法学习
   * T10005 - 服务拒绝用户学习
   * </pre>
   * @param coursewareLearningToken 课件学习凭证
   * @return 学习结果
   * @param mutate 查询 graphql 语法文档
   * @param coursewareLearningToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyMediaLearningTiming(
    coursewareLearningToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyMediaLearningTiming,
    operation?: string
  ): Promise<Response<LearningResultResponse>> {
    return commonRequestApi<LearningResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { coursewareLearningToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请多媒体计时
   * 状态码：
   * <pre>
   * 200 - 成功
   * E10000 - 参数异常
   * E90001 - 内部异常
   * L10000 - 由于学习的课程资源变更，学习被锁定，无法学习
   * L10001 - 由于学习的课程进度变更，学习被锁定，无法学习
   * L10002 - 由于学习的课程进度重置，学习被锁定，无法学习
   * L20000 - 您被系统判定为疑似机器学习，学习被锁定，无法学习
   * T10005 - 服务拒绝用户学习
   * </pre>
   * @param coursewareLearningToken 课件学习凭证
   * @return 学习结果
   * @param mutate 查询 graphql 语法文档
   * @param coursewareLearningToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyMediaLearningTimingWithAnti(
    coursewareLearningToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyMediaLearningTimingWithAnti,
    operation?: string
  ): Promise<Response<LearningResultResponse>> {
    return commonRequestApi<LearningResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { coursewareLearningToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 提交多媒体计时
   * 状态码：
   * <pre>
   * 200 - 成功
   * E10000 - 参数异常
   * T10000 - 计时凭证不存在
   * T10001 - 用户已学习其他课程
   * T10002 - 用户已学习当前课程，其他课件
   * T10003 - 用户已在其他地方学习当前课程当前课件
   * T10004 - 用户学习已离线
   * T10005 - 服务拒绝用户学习
   * </pre>
   * @param token                  计时凭证
   * @param currentMediaTimeLength 当前媒体学习时长
   * @return 学习结果
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async commitMediaLearningTiming(
    params: { token?: string; currentMediaTimeLength: number },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.commitMediaLearningTiming,
    operation?: string
  ): Promise<Response<LearningResultResponse>> {
    return commonRequestApi<LearningResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 提交多媒体计时
   * 状态码：
   * <pre>
   * 200 - 成功
   * E10000 - 参数异常
   * T10000 - 计时凭证不存在
   * T10001 - 用户已学习其他课程
   * T10002 - 用户已学习当前课程，其他课件
   * T10003 - 用户已在其他地方学习当前课程当前课件
   * T10004 - 用户学习已离线
   * T10005 - 服务拒绝用户学习
   * </pre>
   * @param token                  计时凭证
   * @param currentMediaTimeLength 当前媒体学习时长
   * @return 学习结果
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async commitMediaLearningTimingWithAnti(
    params: { token?: string; currentMediaTimeLength: number },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.commitMediaLearningTimingWithAnti,
    operation?: string
  ): Promise<Response<LearningResultResponse>> {
    return commonRequestApi<LearningResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 补发课程相关完成学习事件
   * @param mutate 查询 graphql 语法文档
   * @param requestList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async courseRelatedCompleteReissue(
    requestList: Array<CourseRelatedCompleteRequest>,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.courseRelatedCompleteReissue,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { requestList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 结束多媒体计时
   * 状态码：
   * <pre>
   * 200 - 成功
   * E10000 - 参数异常
   * T10000 - 计时凭证不存在
   * T10001 - 用户已学习其他课程
   * T10004 - 用户学习已离线
   * E90001 - 在线学习用户主动离线异常
   * </pre>
   * @param token 计时凭证
   * @return 学习结果
   * @param mutate 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async endMediaLearningTiming(
    token: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.endMediaLearningTiming,
    operation?: string
  ): Promise<Response<LearningResultResponse>> {
    return commonRequestApi<LearningResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { token },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 维持在线心跳
   * 状态码：
   * <pre>
   * 200 - 成功
   * E10000 - 参数异常
   * T10000 - 计时凭证不存在
   * T10001 - 用户已学习其他课程
   * T10002 - 用户已学习当前课程，其他课件
   * T10003 - 用户已在其他地方学习当前课程当前课件
   * T10004 - 用户学习已离线
   * T10005 - 服务拒绝用户学习
   * </pre>
   * @param token 计时凭证
   * @param mutate 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async keepAliveHeartbeat(
    token: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.keepAliveHeartbeat,
    operation?: string
  ): Promise<Response<LearningErrorCodeResponse>> {
    return commonRequestApi<LearningErrorCodeResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { token },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 准备课程媒体资源计时
   * @param courseLearningPlayToken 课程学习播放凭证
   * @return 课程学习信息
   * @param mutate 查询 graphql 语法文档
   * @param courseLearningPlayToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async prepareCourseLearningTiming(
    courseLearningPlayToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.prepareCourseLearningTiming,
    operation?: string
  ): Promise<Response<LearningCourseRecordResponse>> {
    return commonRequestApi<LearningCourseRecordResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseLearningPlayToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 准备课程媒体资源计时
   * @param courseLearningPlayToken 课程学习播放凭证
   * @return 课程学习信息
   * @param mutate 查询 graphql 语法文档
   * @param courseLearningPlayToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async prepareCourseLearningTimingBeToken(
    courseLearningPlayToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.prepareCourseLearningTimingBeToken,
    operation?: string
  ): Promise<Response<LearningCourseRecordBeTokenResponse>> {
    return commonRequestApi<LearningCourseRecordBeTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseLearningPlayToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
