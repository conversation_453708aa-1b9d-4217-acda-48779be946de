"""独立部署的微服务,K8S服务名:ms-teachingplan-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""创建培训点
		@param request
		@return
	"""
	createTrainingPoint(request:TrainingPointCreateRequest):TrainingPointResponse
}
"""<AUTHOR>
	@since 安徽建设
"""
input TrainingPointCreateRequest @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.TrainingPointCreateRequest") {
	"""培训点名称"""
	name:String
	"""经度"""
	longitude:Double!
	"""纬度"""
	latitude:Double!
	"""选中的培训地址"""
	specificAddress:String
	"""所在地区"""
	areaPath:String
	"""培训教室"""
	classRoom:String
	"""单位id"""
	ownerId:String
}
"""<AUTHOR>
	@since
"""
type TrainingPointResponse @type(value:"com.fjhb.ms.teachingplan.v1.kernel.gateway.graphql.request.TrainingPointResponse") {
	"""200 正常
		E500 名称重复
		E501 已被引用不可删除
	"""
	code:String
	"""培训点id"""
	id:String
}

scalar List
