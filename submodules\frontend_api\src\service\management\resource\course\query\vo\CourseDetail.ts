import {
  CourseCategoryResponse,
  CourseChapter,
  CourseDetailResponse,
  CourseOutline,
  CourseResponse,
  TeacherResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import Chapter from '@api/service/management/resource/course/mutation/vo/Chapter'
import Courseware from '@api/service/management/resource/course/mutation/vo/Courseware'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'
import CoursewareSupplierListDetail from '@api/service/management/authority/service-provider/query/vo/CoursewareSupplierListDetail'

class CourseDetail extends CourseResponse {
  id: string

  /*
   * 课程名称
   */
  name = ''

  /*
   封面
   */
  picture = ''

  /*
   创建时间
   */
  createTime = ''

  /*
   描述信息
   */
  description = ''
  /**
   * 课件供应商ID
   */
  supplierId: string = undefined
  /**
   * 课件供应商名称
   */
  supplierName: string = undefined
  /*
   关于
   */
  abouts = ''

  /*
   教师信息
   */
  teachers: Array<SimpleUserInfo> = new Array<SimpleUserInfo>()

  /**
   * 技术登记
   */
  technicalGrade = ''

  getTeacherNames() {
    if (!this.teachers.length) return '-'
    return this.teachers
      .map((teacher: SimpleUserInfo) => {
        return teacher.name
      })
      .join('、')
  }

  categories: Array<CourseCategoryResponse>

  /**
   * 章节
   */
  chapters: Array<Chapter> = new Array<Chapter>()

  static from(courseResponse: CourseDetailResponse, supplierResponse?: CoursewareSupplierListDetail): CourseDetail {
    const courseDetail = new CourseDetail()
    courseDetail.id = courseResponse.id
    courseDetail.supplierId = courseResponse.supplierId
    courseDetail.name = courseResponse.name
    courseDetail.picture = courseResponse.iconPath
    courseDetail.description = courseResponse.aboutsContent
    courseDetail.categories = courseResponse.categorys
    courseDetail.supplierName = supplierResponse?.name
    courseDetail.teachers = courseResponse.teacherIds.map((id: string) => {
      const teacher = new SimpleUserInfo()
      teacher.id = id
      return teacher
    })
    courseDetail.chapters = courseResponse.courseOutlines
      .filter((c: CourseOutline) => c.parentId === '-1')
      .map((outline: CourseOutline) => {
        const chapter = Chapter.from(outline)
        const courseChapters = courseResponse.courseChapters.filter((chapter: CourseChapter) => {
          if (chapter.courseOutline.parentId === '-1') {
            return chapter.courseOutline.id === outline.id
          }
          chapter.name = chapter.courseOutline.name
          return chapter.courseOutline.parentId === outline.id
        })
        if (courseChapters) {
          chapter.coursewares = courseChapters.map(Courseware.from)
        }
        chapter.coursewares.sort((a, b) => a.sort - b.sort)
        return chapter
      })
    courseDetail.courseAppraiseInfo = courseResponse?.courseAppraiseInfo
    return courseDetail
  }

  async queryTeacher() {
    if (this.teachers.length) {
      const {
        data: teachersDetail
      } = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listTeacherInServicer(
        this.teachers.map((teacher: SimpleUserInfo) => teacher.id)
      )
      this.teachers.forEach((teacher: SimpleUserInfo) => {
        const findTeacher = teachersDetail.find((response: TeacherResponse) => {
          return response.id === teacher.id
        })
        if (findTeacher) {
          teacher.name = findTeacher.name
        }
      })
    }
  }
}

export default CourseDetail
