import exportExcel from './queries/exportExcel.graphql'
import exportExcelAllData from './queries/exportExcelAllData.graphql'
import findImportDataWithServicerByPage from './queries/findImportDataWithServicerByPage.graphql'
import findTaskExecuteWithServicerResponseByPage from './queries/findTaskExecuteWithServicerResponseByPage.graphql'
import queryImportOpenTemplatePath from './queries/queryImportOpenTemplatePath.graphql'
import discountImportOpen from './mutates/discountImportOpen.graphql'
import distributionImportOpen from './mutates/distributionImportOpen.graphql'

export {
  exportExcel,
  exportExcelAllData,
  findImportDataWithServicerByPage,
  findTaskExecuteWithServicerResponseByPage,
  queryImportOpenTemplatePath,
  discountImportOpen,
  distributionImportOpen
}
