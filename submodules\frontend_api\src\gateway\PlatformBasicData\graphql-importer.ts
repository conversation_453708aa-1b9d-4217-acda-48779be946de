import findTeachUnitByDomain from './queries/findTeachUnitByDomain.graphql'
import findTeachUnitCount from './queries/findTeachUnitCount.graphql'
import findTeachUnitListByIds from './queries/findTeachUnitListByIds.graphql'
import findTeachUnitPage from './queries/findTeachUnitPage.graphql'
import findTeachUnitPageByAdmin from './queries/findTeachUnitPageByAdmin.graphql'
import getAllPermission from './queries/getAllPermission.graphql'
import getAllRoles from './queries/getAllRoles.graphql'
import getCurrentDate from './queries/getCurrentDate.graphql'
import getCurrentMicroContext from './queries/getCurrentMicroContext.graphql'
import getCurrentUserSecurityGroup from './queries/getCurrentUserSecurityGroup.graphql'
import getEnabledRoles from './queries/getEnabledRoles.graphql'
import getPermissionByRoleId from './queries/getPermissionByRoleId.graphql'
import getPermissionForEditRole from './queries/getPermissionForEditRole.graphql'
import getPromoteQRCode from './queries/getPromoteQRCode.graphql'
import getRegionById from './queries/getRegionById.graphql'
import getRegionInfoByPath from './queries/getRegionInfoByPath.graphql'
import getRegionTree from './queries/getRegionTree.graphql'
import getRegionsByIds from './queries/getRegionsByIds.graphql'
import getRoleById from './queries/getRoleById.graphql'
import getShortUrl from './queries/getShortUrl.graphql'
import getSpecialTopicPage from './queries/getSpecialTopicPage.graphql'
import getSubRegionListByParentId from './queries/getSubRegionListByParentId.graphql'
import getTeachUnit from './queries/getTeachUnit.graphql'
import isRoleExist from './queries/isRoleExist.graphql'
import pageRolesByQuery from './queries/pageRolesByQuery.graphql'
import pageUserJob from './queries/pageUserJob.graphql'
import addUserOwnRoles from './mutates/addUserOwnRoles.graphql'
import deleteRole from './mutates/deleteRole.graphql'
import getPermission from './mutates/getPermission.graphql'
import removeUserOwnRoles from './mutates/removeUserOwnRoles.graphql'
import saveRole from './mutates/saveRole.graphql'

export {
  findTeachUnitByDomain,
  findTeachUnitCount,
  findTeachUnitListByIds,
  findTeachUnitPage,
  findTeachUnitPageByAdmin,
  getAllPermission,
  getAllRoles,
  getCurrentDate,
  getCurrentMicroContext,
  getCurrentUserSecurityGroup,
  getEnabledRoles,
  getPermissionByRoleId,
  getPermissionForEditRole,
  getPromoteQRCode,
  getRegionById,
  getRegionInfoByPath,
  getRegionTree,
  getRegionsByIds,
  getRoleById,
  getShortUrl,
  getSpecialTopicPage,
  getSubRegionListByParentId,
  getTeachUnit,
  isRoleExist,
  pageRolesByQuery,
  pageUserJob,
  addUserOwnRoles,
  deleteRole,
  getPermission,
  removeUserOwnRoles,
  saveRole
}
