import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-autolearning-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-autolearning-log-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 课程学习详情请求
<AUTHOR>
@date 2025/3/20 16:21
 */
export class CourseLearningDetailRequest {
  /**
   * 日志id
   */
  logId: string
  /**
   * 日志类型：1-编排日志，2-执行日志
   */
  logType: number
}

/**
 * 查看日志详情请求
<AUTHOR>
@date 2025/3/20 9:50
 */
export class ExamLearningDetailRequest {
  /**
   * 日志id
   */
  logId: string
  /**
   * 日志类型：1-编排日志，2-执行日志
   */
  logType: number
}

/**
 * 编排日志返回值
<AUTHOR>
@date 2025/3/20 14:55
 */
export class AutoLearningArrangeLogResponse {
  /**
   * 日志ID
   */
  logId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 学员自动学习任务结果ID
   */
  studentAutoLearningTaskResultId: string
  /**
   * 主任务ID
   */
  mainTaskId: string
  /**
   * 学习方案ID
   */
  learningSchemeId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 信息
   */
  message: string
  /**
   * 类型:0-导入，1-重启
@see com.fjhb.ms.autolearning.v1.api.constant.AutoLearningLogTypes;
   */
  type: number
  /**
   * 编排次数(当type&#x3D;1的时候才有值)
   */
  arrangeNum: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 完成时间
   */
  completeTime: string
  /**
   * 是否允许重叠
   */
  allowOverlap: boolean
}

/**
 * 执行日志返回值
<AUTHOR>
@date 2025/3/20 14:57
 */
export class AutoLearningExecuteLogResponse {
  /**
   * 日志ID
   */
  logId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 学员自动学习任务结果ID
   */
  studentAutoLearningTaskResultId: string
  /**
   * 主任务ID
   */
  mainTaskId: string
  /**
   * 学习方案ID
   */
  learningSchemeId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 状态:0-失败，1-中断，2-完成
@see com.fjhb.ms.autolearning.v1.kernel.consts.AutoLearningLogExecuteStatus
   */
  status: number
  /**
   * 信息
   */
  message: string
  /**
   * 类型：0-导入，1-重启
@see com.fjhb.ms.autolearning.v1.kernel.consts.AutoLearningLogTypes
   */
  type: number
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 课程学习详情返回值
<AUTHOR>
@date 2025/3/20 16:19
 */
export class CourseLearningDetailResponse {
  /**
   * code
   */
  code: string
  /**
   * message
   */
  message: string
  /**
   * 期望开始学习时间
   */
  expectStartStudyTime: string
  /**
   * 课程详情
   */
  courseDetails: Array<CourseDetail>
}

/**
 * 课程信息返回值
<AUTHOR>
@date 2025/3/21 15:06
 */
export class CourseLearningInfoResponse {
  /**
   * 课程Id
   */
  courseId: string
  /**
   * 学习开始时间
   */
  studyStartTime: string
  /**
   * 学习结束时间
   */
  studyEndTime: string
  /**
   * 完成进度-所有媒体资源进度相加(currentLearningProgress)
   */
  completionProgress: number
  /**
   * 学习完成情况
@see com.fjhb.ms.autolearning.v1.kernel.domain.autolearninglog.consts.CompleteStatus
1-未完成，2-已完成，3-终止(自主学习插入)，4-终止(培训时间缩短执行失败) 5-已终止(管理员中止)
   */
  completeStatus: number
}

/**
 * 课后测验信息返回值
<AUTHOR>
@date 2025/3/21 16:00
 */
export class CourseQuizInfoResponse {
  /**
   * 课程Id
   */
  courseId: string
  /**
   * 测验开始时间
   */
  answerStartTime: string
  /**
   * 测验结束时间
   */
  answerEndTime: string
  /**
   * 测验分数
   */
  quizScore: number
  /**
   * 测验完成情况
   */
  quizCompleteStatus: number
}

/**
 * 班级考试详情返回值
<AUTHOR>
@date 2025/3/20 9:31
 */
export class ExamLearningDetailResponse {
  /**
   * code
200-成功
60001-未获取考试类型的子任务数据
   */
  code: string
  /**
   * message
   */
  message: string
  /**
   * 考试场次名称
   */
  examSessionName: string
  /**
   * 考试开始时间
   */
  examStartTime: string
  /**
   * 考试结束时间
   */
  examEndTime: string
  /**
   * 完成状态
   */
  completeStatus: number
  /**
   * 考试成绩
   */
  examScore: number
}

/**
 * 课程详情
<AUTHOR>
@date 2025/3/24 16:30
 */
export class CourseDetail {
  /**
   * 课程信息
   */
  courseInfo: CourseLearningInfoResponse
  /**
   * 课后测验信息
   */
  courseQuizInfo: CourseQuizInfoResponse
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询自动学习编排日志
   * @param query 查询 graphql 语法文档
   * @param studentAutoLearningTaskResultId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryAutoLearningArrangeLog(
    studentAutoLearningTaskResultId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryAutoLearningArrangeLog,
    operation?: string
  ): Promise<Response<Array<AutoLearningArrangeLogResponse>>> {
    return commonRequestApi<Array<AutoLearningArrangeLogResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { studentAutoLearningTaskResultId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询自动学习执行日志
   * @param query 查询 graphql 语法文档
   * @param studentAutoLearningTaskResultId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryAutoLearningExecuteLog(
    studentAutoLearningTaskResultId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryAutoLearningExecuteLog,
    operation?: string
  ): Promise<Response<Array<AutoLearningExecuteLogResponse>>> {
    return commonRequestApi<Array<AutoLearningExecuteLogResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { studentAutoLearningTaskResultId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查看详情-课程学习
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryCourseLearningDetail(
    request: CourseLearningDetailRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryCourseLearningDetail,
    operation?: string
  ): Promise<Response<CourseLearningDetailResponse>> {
    return commonRequestApi<CourseLearningDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查看详情-考试
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryExamLearningDetail(
    request: ExamLearningDetailRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryExamLearningDetail,
    operation?: string
  ): Promise<Response<ExamLearningDetailResponse>> {
    return commonRequestApi<ExamLearningDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
