import { CheckInFrequencyEnum } from '@api/service/common/implement/enums/CheckInFrequencyEnum'

export default class AttendanceConfigDto {
  /*
   * 是否开启
   */
  isOpen = true

  /*
   * 签到频率类型
   */
  checkInFrequency: CheckInFrequencyEnum = CheckInFrequencyEnum.halfDay

  /*
   * 签到地点半径
   */
  checkInLocationRadius = 500

  /*
   * 签到时间之前
   */
  preCheckInTime: number = undefined

  /*
   * 签到时间之后
   */
  afterCheckInTime: number = undefined
}
