<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <el-row :gutter="20" type="flex" justify="center">
        <el-col :lg="20" :xl="18">
          <el-form ref="form" :inline="true" :model="form" label-width="7.5rem" class="m-form f-clear f-mt20">
            <el-col :span="12">
              <el-form-item label="电子票服务商：">
                <el-radio-group v-model="provider">
                  <el-radio label="5">诺诺发票</el-radio>
                </el-radio-group>
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                  <div slot="content">
                    1.平台仅支持诺诺发票的增值税电子普通发票的自动开票对接。请先购买诺诺开票服务，再提供以下的信息。
                    <a class="f-link f-cb f-underline" @click="goNuoNuoInvoice">前往诺诺发票>&gt;</a>
                  </div>
                </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item class="f-tr">
                <span class="f-link f-cb f-underline" @click="dialog1 = true">查看发票示例图片</span>
                <!--示例图片弹窗-->
                <el-dialog :visible.sync="dialog1" width="1100px" class="m-dialog-pic">
                  <img src="@design/admin/assets/images/demo-invoice.png" alt="" />
                </el-dialog>
              </el-form-item>
            </el-col>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
    <el-card shadow="never" class="m-card f-mb15">
      <div slot="header" class="">
        <span class="tit-txt">基础信息</span>
      </div>
      <el-row :gutter="20" type="flex" justify="center">
        <el-col :lg="20" :xl="18">
          <el-form
            ref="modifyInvoiceBaseForm"
            :inline="true"
            :model="mutationInvoiceConfig.eleInvoiceParams"
            :rules="baseRules"
            label-width="7.5rem"
            class="m-form f-clear f-mt20"
          >
            <el-col :span="12">
              <el-form-item label="单位名称：" required prop="name">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.name"
                  clearable
                  placeholder="请输入单位名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纳税人识别号：" required prop="taxpayerNo">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.taxpayerNo"
                  clearable
                  placeholder="请输入纳税人识别号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="地址：" required prop="address">
                <el-input v-model="mutationInvoiceConfig.eleInvoiceParams.address" clearable placeholder="请输入地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话：" required prop="phone">
                <el-input v-model="mutationInvoiceConfig.eleInvoiceParams.phone" clearable placeholder="请输入电话" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户行：" required prop="bankName">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.bankName"
                  clearable
                  placeholder="请输入开户行"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户帐号：" required prop="bankAccount">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.bankAccount"
                  clearable
                  placeholder="请输入开户帐号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开票最大金额：" required prop="invoiceMaxMoney">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.invoiceMaxMoney"
                  type="number"
                  min="0"
                  clearable
                  @keydown.native="
                    (e) => (e.returnValue = ['e', 'E', '-', '+'].includes(e.key) ? false : e.returnValue)
                  "
                  placeholder="请输入开票最大金额（元）"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收款人：" required prop="payee">
                <el-input v-model="mutationInvoiceConfig.eleInvoiceParams.payee" clearable placeholder="请输入收款人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="复核：" required prop="reviewer">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.reviewer"
                  clearable
                  placeholder="请输入复核"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开票人：" required prop="issuer">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.issuer"
                  clearable
                  placeholder="请输入开票人"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="授权码：" required prop="secretAssessKey">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.secretAssessKey"
                  clearable
                  placeholder="请输入授权码"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="部门ID：">
                <div slot="label">
                  <span class="f-vm">部门ID</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>部门ID用于第三方开票平台区分开票的来源，该信息由第三方开票平台提供，请确认后再填写。</p>
                      <p>如网校存在以下任一情况，需要提供部门ID：</p>
                      <p>1.网校需求区分，开票是属于单机版、网络版；</p>
                      <p>2.同一个公司，存在多税盘的情况；</p>
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.deptId"
                  clearable
                  placeholder="请输入部门ID"
                />
              </el-form-item>
            </el-col>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
    <el-card shadow="never" class="m-card f-mb15">
      <div slot="header" class="">
        <span class="tit-txt">服务类型 / 商品类型信息</span>
      </div>
      <el-row :gutter="20" type="flex" justify="center">
        <el-col :lg="20" :xl="18">
          <el-form
            ref="modifyInvoiceForm"
            :inline="true"
            :model="mutationInvoiceConfig.eleInvoiceParams"
            :rules="rules"
            label-width="10rem"
            class="m-form f-clear f-mt20"
          >
            <el-col :span="12">
              <el-form-item label="税务名称(税务编码)：" required prop="commodityCode">
                <el-select
                  v-model="mutationInvoiceConfig.eleInvoiceParams.commodityCode"
                  clearable
                  placeholder="请选择税务名称和税务编码"
                >
                  <el-option
                    v-for="item in commodityCodeList"
                    :label="item.name + '(' + item.code + ')'"
                    :value="item.code"
                    :key="item.code"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="服务名称：" required prop="serviceTitle">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.serviceTitle"
                  clearable
                  placeholder="请输入网校开票具体服务名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格型号：">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.specificationMode"
                  clearable
                  placeholder="请输入型号规格"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单位：">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.unitTitle"
                  clearable
                  placeholder="请输入单位"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否显示数量和单价：">
                <el-select
                  v-model="mutationInvoiceConfig.eleInvoiceParams.printQuantity"
                  clearable
                  placeholder="请选择发票上是否显示数量和单价"
                >
                  <el-option label="显示" :value="true"></el-option>
                  <el-option label="不显示" :value="false"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="税率：" required prop="rate">
                <el-input
                  type="number"
                  v-model="mutationInvoiceConfig.eleInvoiceParams.rate"
                  min="0"
                  @keydown.native="
                    (e) => (e.returnValue = ['e', 'E', '-', '+'].includes(e.key) ? false : e.returnValue)
                  "
                  clearable
                  placeholder="请输入税率（%）"
                />
              </el-form-item>
            </el-col>
            <!--            <el-col :span="12">
              <el-form-item label="单价：">
                <el-select
                  v-model="mutationInvoiceConfig.eleInvoiceParams.printPrice"
                  clearable
                  filterable
                  placeholder="请选择发票上是否显示单价"
                >
                  <el-option label="显示" :value="true"></el-option>
                  <el-option label="不显示" :value="false"></el-option>
                </el-select>
              </el-form-item>
            </el-col>-->
          </el-form>
        </el-col>
      </el-row>
    </el-card>
    <div class="m-btn-bar f-tc is-sticky-1">
      <el-button @click="doCancel">取消</el-button>
      <el-button type="primary" @click="doSave">保存</el-button>
    </div>
    <give-up-dialog :give-up-model="uiConfig.giveUpModel" @callBack="resetData"></give-up-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import QueryElectronicInvoiceConfig from '@api/service/management/online-school-config/functionality-setting/query/QueryElectronicInvoiceConfig'
  import CommodityCodeVo from '@api/service/management/online-school-config/functionality-setting/query/vo/CommodityCodeVo'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import { InvoiceProviderEnum } from '@api/service/common/enums/online-school-config/InvoiceProviderTypes'
  import EleInvoiceVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/common/EleInvoiceVo'
  import ElectronicInvoiceUpdateVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/ElectronicInvoiceUpdateVo'
  @Component({
    components: { GiveUpDialog }
  })
  export default class extends Vue {
    @Ref('modifyInvoiceBaseForm') modifyInvoiceBaseForm: any
    @Ref('modifyInvoiceForm') modifyInvoiceForm: any
    baseRules = {
      name: [
        {
          required: true,
          message: '请输入单位名称',
          trigger: ['change', 'blur']
        }
      ],
      taxpayerNo: [
        {
          required: true,
          message: '请输入纳税人识别号',
          trigger: ['change', 'blur']
        }
      ],
      address: [
        {
          required: true,
          message: '请输入地址',
          trigger: ['change', 'blur']
        }
      ],
      phone: [
        {
          required: true,
          message: '请输入电话',
          trigger: ['change', 'blur']
        }
      ],
      bankName: [
        {
          required: true,
          message: '请输入开户行',
          trigger: ['change', 'blur']
        }
      ],
      bankAccount: [
        {
          required: true,
          message: '请输入开户账号',
          trigger: ['change', 'blur']
        }
      ],
      invoiceMaxMoney: [
        {
          required: true,
          message: '请输入开票最大金额',
          trigger: ['change', 'blur']
        }
      ],
      payee: [
        {
          required: true,
          message: '请输入收款人',
          trigger: ['change', 'blur']
        }
      ],
      reviewer: [
        {
          required: true,
          message: '请输入复核',
          trigger: ['change', 'blur']
        }
      ],
      issuer: [
        {
          required: true,
          message: '请输入开票人',
          trigger: ['change', 'blur']
        }
      ],
      secretAssessKey: [
        {
          required: true,
          message: '请输入授权码',
          trigger: ['change', 'blur']
        }
      ]
    }
    rules = {
      rate: [
        {
          required: true,
          message: '请输入税率',
          trigger: ['change', 'blur']
        }
      ],
      commodityCode: [
        {
          required: true,
          message: '请输入税务名称（税务编码）',
          trigger: ['change', 'blur']
        }
      ],
      serviceTitle: [
        {
          required: true,
          message: '请输入服务名称',
          trigger: ['change', 'blur']
        }
      ]
    }
    dialog1 = false
    form = {
      resource: '',
      name: ''
    }
    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }
    providerList: Array<string> = new Array<string>()
    provider = ''
    commodityCodeList: Array<CommodityCodeVo> = new Array<CommodityCodeVo>()
    isModify = false
    //纳税人编号
    taxpayerNo = ''
    mutationInvoiceConfig = OnlineSchoolConfigModule.mutationFunctionalitySettingFactory.mutationElectronicInvoice

    async doModify() {
      ;(this.mutationInvoiceConfig.eleInvoiceParams as ElectronicInvoiceUpdateVo).taxpayerId = this.taxpayerNo
      const res = await this.mutationInvoiceConfig.doUpdateElectronicInvoice()
      if (res.isSuccess()) {
        this.$message.success('修改成功')
        await this.getInvoiceConfig()
      } else {
        this.$message.error('修改失败')
      }
    }

    async doCreate() {
      const res = await this.mutationInvoiceConfig.doCreateElectronicInvoice()
      if (res.status.isSuccess()) {
        this.$message.success('保存成功')
        await this.getInvoiceConfig()
      } else {
        this.$message.error('保存失败')
      }
    }

    doSave() {
      this.modifyInvoiceBaseForm.validate((baseValid: boolean) => {
        this.modifyInvoiceForm.validate((valid: boolean) => {
          if (baseValid && valid) {
            if (this.mutationInvoiceConfig.eleInvoiceParams.printQuantity) {
              this.mutationInvoiceConfig.eleInvoiceParams.printQuantity = true
              this.mutationInvoiceConfig.eleInvoiceParams.printPrice = true
            } else {
              this.mutationInvoiceConfig.eleInvoiceParams.printQuantity = false
              this.mutationInvoiceConfig.eleInvoiceParams.printPrice = false
            }
            if (this.isModify) {
              this.doModify()
            } else {
              this.doCreate()
            }
          }
        })
      })
    }

    async resetData() {
      await this.getInvoiceConfig()
    }

    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
    }

    goNuoNuoInvoice() {
      window.open('https://fp.nuonuo.com/#/', '_blank')
    }

    async getInvoiceConfig() {
      //获取纳税人编号
      const taxpayerNoRes =
        await OnlineSchoolConfigModule.queryFunctionalitySettingFactory.queryElectronicInvoiceConfig.queryElectronicInvoiceTaxpayerId()
      if (taxpayerNoRes?.status?.isSuccess()) {
        if (taxpayerNoRes?.data === '') {
          this.isModify = false
        } else {
          this.isModify = true
          this.taxpayerNo = taxpayerNoRes.data as string
        }
      }
      //获取发票服务商以及税务编码集合
      const res =
        await OnlineSchoolConfigModule.queryFunctionalitySettingFactory.queryElectronicInvoiceConfig.queryElectronicInvoiceConfig()
      if (res?.status?.code == 200) {
        this.providerList = res.data.providerList
        if (this.providerList.length) {
          //this.provider = this.providerList[0]
          this.provider = InvoiceProviderEnum.NUONUO
        }
        this.commodityCodeList = res.data.commodityCodeList
      }
      const listRes =
        await OnlineSchoolConfigModule.queryFunctionalitySettingFactory.queryElectronicInvoiceConfig.queryElectronicInvoiceDetail(
          this.taxpayerNo
        )
      if (listRes?.status?.code == 200) {
        //this.mutationInvoiceConfig.createEleInvoiceParams = Object.assign(new ElectronicInvoiceCreateVo(), listRes.data)
        this.mutationInvoiceConfig.eleInvoiceParams = Object.assign(new EleInvoiceVo(), listRes.data)
      }
    }

    async created() {
      await this.getInvoiceConfig()
    }
  }
</script>
<style scoped="scoped">
  ::v-deep .el-input input::-webkit-outer-spin-button,
  ::v-deep .el-input input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
  ::v-deep .el-input input[type='number'] {
    -moz-appearance: textfield;
  }
</style>
