import { CurrentResultEnum, StatusEnum } from '@hbfe-biz/biz-anticheat/dist/log/enums/SupervisionEnum'
import UserLearningLogItem from '@hbfe-biz/biz-anticheat/dist/log/models/UserLearningLogItem'
import { VerifyTypeEnum } from '@hbfe-biz/biz-anticheat/dist/config/enums/VerifyType'

export default class UserLogInfo {
  /**
   * 监管行为状态 1、进行中2、已完成
   */
  status: StatusEnum
  /**
   * 监管行为触发时间
   */
  createdTime: string
  /**
   * 监管行为完成时间
   */
  verifiedTime: string
  /**
   * 此次监管行为下的验证交互结果|1、验证通过2、验证不通过
   */
  currentResult: CurrentResultEnum
  /**
   * 照片信息
   */
  photoList: string
  /**
   * 创建时间
   */
  photoCreatedTime: string
  /**
   * 当前学习时长
   */
  currentLength: number | string
  /**
   * 当前学习进度
   */
  currentSchedule: number

  /**
   * 监管类型
   */
  antiType: VerifyTypeEnum = undefined

  static from(dto: UserLearningLogItem) {
    const vo = new UserLogInfo()
    vo.status = dto?.status
    vo.verifiedTime = dto?.verifiedTime
    vo.createdTime = dto?.createdTime
    vo.currentResult = dto?.currentResult
    if (dto?.superviseMessage?.length) {
      vo.photoList = dto?.superviseMessage[0]?.photoList[0]
      vo.photoCreatedTime = dto?.superviseMessage[0]?.createdTime
      vo.antiType = dto?.superviseMessage[0]?.superviseType as VerifyTypeEnum
    }
    if (dto?.sceneTypeMessage?.length) {
      const currentLength = Number(dto?.sceneTypeMessage[0].currentLength) || 0
      vo.currentSchedule = dto?.sceneTypeMessage[0].currentSchedule
      vo.currentLength = this.formateTime(currentLength)
    }
    return vo
  }
  private static formateTime(time: number) {
    let h: number | string = Math.floor((time / 3600) % 24) || 0
    let m: number | string = Math.floor((time / 60) % 60) || 0
    let s: number | string = Math.floor(time % 60) || 0
    h = h < 10 ? '0' + h : h
    m = m < 10 ? '0' + m : m
    s = s < 10 ? '0' + s : s
    if (h < 1) {
      return m + ':' + s
    } else {
      return h + ':' + m + ':' + s
    }
  }
}
