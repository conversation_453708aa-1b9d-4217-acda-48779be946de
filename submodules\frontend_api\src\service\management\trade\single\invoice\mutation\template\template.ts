/*
 * @Description: 拼graphl
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-31 17:17:50
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-11 14:08:29
 */

import { DocumentNode } from 'graphql/language'

/**
 * 批量开票Graphl
 * @param length 数量
 */
function issueElectronicInvoice(length: number): DocumentNode {
  // graph头部
  const header = `mutation issueElectronicInvoice(`
  // 参数集合
  const keyArr = []
  // 后端方法集合
  const fnArr = []
  //对参数集合和后端方法集合进行填充
  for (let i = 0; i < length; i++) {
    const key = `$invoiceId${i}: String`
    const fn = `invoiceFn${i}:issueElectronicInvoice(invoiceId: $invoiceId${i}) { _ALL_ }`
    fnArr.push(fn)
    keyArr.push(key)
  }
  // 参数集合拼接
  let keyStr = ''
  // 后端方法集合拼接
  let fnStr = ''
  for (let i = 0; i < length; i++) {
    keyStr = keyStr + keyArr[i]
    fnStr = fnStr + fnArr[i]
    if (length != i + 1) {
      keyStr = keyStr + ','
    }
  }
  // 整合整体
  keyStr = keyStr + `) {`
  fnStr = fnStr + `}`
  const graph = header + keyStr + fnStr
  return (graph as unknown) as DocumentNode
}
export { issueElectronicInvoice }
