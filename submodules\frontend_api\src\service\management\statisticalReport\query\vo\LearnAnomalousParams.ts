import { LearningResultErrorRequest } from '@api/platform-gateway/student-course-learning-query-back-gateway'

export default class LearnAnomalousParams {
  /**
   * 姓名
   */
  name = ''
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 手机号
   */
  phone = ''
  /**
   * 培训方案id
   */
  schemeName = ''
  /**
   * 学习数据异常信息类型  1.学习规则2.智能学习
   */
  errorType = 1

  static to(vo: LearnAnomalousParams) {
    const dto = new LearningResultErrorRequest()
    dto.userName = vo.name
    dto.userPhone = vo.phone
    dto.userIdCard = vo.idCard
    dto.trainingName = vo.schemeName
    dto.errorType = vo.errorType
    return dto
  }
}
