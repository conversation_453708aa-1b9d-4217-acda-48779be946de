<template>
  <el-drawer title="修改发票信息" :visible.sync="dialogVisible" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <div class="m-tit">
        <span class="tit-txt">原发票信息</span>
      </div>
      <el-form ref="form" label-width="150px" class="m-text-form f-mt10">
        <el-col :span="12">
          <el-form-item label="发票类型：" class="is-text"> 增值税专用发票 </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发票抬头：" class="is-text"> 【单位】{{ invoiceInfo.title }} </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="统一社会信用代码：" class="is-text">{{ invoiceInfo.taxpayerNo }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户银行：" class="is-text">{{ invoiceInfo.bankName }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户账号：" class="is-text">{{ invoiceInfo.account }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册电话：" class="is-text">{{ invoiceInfo.rePhone }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册地址：" class="is-text">{{ invoiceInfo.address }}</el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="营业执照：">
            <el-image
              :src="invoiceInfo.businessLicenseUrl"
              :preview-src-list="[invoiceInfo.businessLicenseUrl]"
              class="course-pic is-small"
            >
            </el-image>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户许可证：">
            <el-image
              :src="invoiceInfo.permitUrl"
              :preview-src-list="[invoiceInfo.permitUrl]"
              class="course-pic is-small"
            >
            </el-image>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="发票备注：" class="is-text" v-if="invoiceInfo.remark">
            {{ invoiceInfo.remark }}
          </el-form-item>
        </el-col>
      </el-form>
      <div class="m-sub-tit">
        <span class="tit-txt">收货信息</span>
      </div>
      <el-form label-width="150px" class="m-text-form f-mt10" v-if="invoiceInfo.shippingMethod == 1">
        <el-col :span="12">
          <el-form-item label="配送方式：">自取</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="领取时间：">{{ invoiceInfo.takePointPickupTime }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="领取地点：">{{ invoiceInfo.takePointPickupLocation }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注：">{{ invoiceInfo.takePointRemark }}</el-form-item>
        </el-col>
      </el-form>
      <el-form label-width="150px" class="m-text-form f-mt10" v-if="invoiceInfo.shippingMethod == 2">
        <el-col :span="12">
          <el-form-item label="配送方式：">邮寄</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收货人：">{{ invoiceInfo.consignee }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号：">{{ invoiceInfo.deliveryphone }}</el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="收货地址：">{{ regionName }}&nbsp;{{ invoiceInfo.deliveryAddress }}</el-form-item>
        </el-col>
      </el-form>
      <el-divider class="m-divider"></el-divider>
      <div class="m-tit">
        <span class="tit-txt">修改的发票信息</span>
      </div>
      <el-form
        ref="modifyInvoiceForm"
        :model="updateInfo"
        :rules="specialInvoiceRules"
        label-width="150px"
        class="m-form f-mt10"
      >
        <el-form-item label="发票类型：" class="is-text">增值税专用发票</el-form-item>
        <el-form-item label="发票抬头：" prop="title">
          <el-radio-group v-model="updateInfo.titleType">
            <el-radio :label="2">
              <span class="f-mr10">单位</span>
            </el-radio>
          </el-radio-group>
          <el-input v-model="updateInfo.title" clearable placeholder="请输入抬头" />
        </el-form-item>
        <el-form-item label="统一社会信用代码：" prop="taxpayerNo">
          <el-input
            v-model="updateInfo.taxpayerNo"
            @input="upperCase"
            clearable
            placeholder="请输入18位统一社会信用代码"
            class="form-l"
          />
        </el-form-item>
        <el-form-item label=" " class="is-text">
          <span class="f-co">注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span>
        </el-form-item>
        <el-form-item label="开户银行：" prop="bankName">
          <el-input v-model="updateInfo.bankName" clearable placeholder="请输入开户银行" class="form-l" />
        </el-form-item>
        <el-form-item label="开户帐号：" prop="account">
          <el-input v-model="updateInfo.account" clearable placeholder="请输入开户帐号" class="form-l" />
        </el-form-item>
        <el-form-item label="注册电话：" prop="rePhone">
          <el-input
            v-model="updateInfo.rePhone"
            clearable
            maxlength="20"
            show-word-limit
            placeholder="请输入单位注册电话"
            class="form-m"
          />
        </el-form-item>
        <el-form-item label="注册地址：" prop="address">
          <el-input
            v-model="updateInfo.address"
            clearable
            maxlength="100"
            show-word-limit
            placeholder="请输入单位注册地址"
          />
        </el-form-item>
        <!-- <el-form-item label="营业执照：" prop="businessLicenseUrl" ref="businessLicensePicture">
          <upload-images v-model="businessLicenseUrlList" :limit="1" :is-protected="true"></upload-images>
        </el-form-item>
        <el-form-item label="开户许可证：" prop="permitUrl" ref="permitPicture">
          <upload-images v-model="permitUrlList" :limit="1" :is-protected="true"></upload-images>
        </el-form-item> -->
        <el-form-item label="发票备注：">
          <el-input
            v-model="updateInfo.remark"
            maxlength="100"
            type="textarea"
            :rows="3"
            placeholder="请填写统一备注信息"
            class="f-mt5 f-mb15"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="m-sub-tit">
        <span class="tit-txt">收货信息</span>
      </div>
      <el-form
        ref="deliveryForm"
        :model="updateInfo"
        :rules="specialInvoiceRules"
        label-width="150px"
        class="m-form f-mt10"
      >
        <el-form-item label="配送方式：" required>
          <el-radio-group v-model="updateInfo.shippingMethod">
            <el-radio :label="2">快递</el-radio>
            <el-radio :label="1">自取</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="updateInfo.shippingMethod === 2">
          <el-form-item label="收货人：" prop="consignee">
            <el-input v-model="updateInfo.consignee" clearable placeholder="请输入收货人" class="form-s" />
          </el-form-item>
          <el-form-item label="手机号：" prop="deliveryphone">
            <el-input v-model="updateInfo.deliveryphone" clearable placeholder="请输入手机号" class="form-m" />
          </el-form-item>
          <el-form-item label="所在地区：" prop="deliveryRegion" ref="region">
            <national-region-cascader :check-strictly="false" v-model="deliveryRegion"></national-region-cascader>
          </el-form-item>
          <el-form-item label="详细地址：" prop="deliveryAddress">
            <el-input v-model="updateInfo.deliveryAddress" clearable placeholder="请输入详细地址" />
          </el-form-item>
        </div>
        <el-form-item label=" " class="is-text" v-if="updateInfo.shippingMethod === 1">
          <!--选中添加 is-checked-->
          <div
            class="take-address"
            :class="{ 'is-checked': curPickUpIndex == index }"
            v-for="(item, index) in pickUpList"
            :key="item.id"
            @click="changeCurIndex(index)"
          >
            <div class="label"><i class="hb-iconfont icon-finish"></i></div>
            <el-form ref="form" :model="form" label-width="auto" class="m-form">
              <el-form-item label="领取时间：" class="is-text">
                {{ item.openTakeTime }}
              </el-form-item>
              <el-form-item label="领取地点：" class="is-text">
                {{ item.address }}
              </el-form-item>
              <el-form-item label="备注：" class="is-text">
                {{ item.remark }}
              </el-form-item>
            </el-form>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="commitApply">保存发票信息</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { TitleTypeEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import UploadImages from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/upload-images.vue'
  import OffLinePageInvoiceResponseVo from '@api/service/management/trade/single/invoice/mutation/dto/OffLinePageInvoiceResponseVo'
  import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import NationalRegionCascader from '@hbfe/jxjy-admin-components/src/national-region/national-region-cascader.vue'
  import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
  import QueryDeliverWayTypeList from '@api/service/common/trade-config/query/QueryDeliverWayTypeList'
  import TakePlaceDetailVo from '@api/service/management/online-school-config/distribution-channels-config/query/vo/TakePlaceDetailVo'
  import FileModule from '@api/service/common/file/FileModule'
  import FilePreview from '@api/service/common/file/FilePreview'
  export class UploadImageFile {
    name: string
    url: string
  }

  @Component({
    components: { NationalRegionCascader, UploadImages }
  })
  export default class extends Vue {
    @Ref('modifyInvoiceForm') modifyInvoiceForm: any
    @Ref('deliveryForm') deliveryForm: any
    @Prop({
      type: Boolean,
      default: false
    })
    dialogCtrl: boolean

    @Prop({
      type: String,
      default: '1'
    })
    invoiceType: string

    @Prop({
      type: String,
      default: ''
    })
    invoiceId: string
    loading = false
    // businessLicenseUrlList: Array<UploadImageFile> = new Array<UploadImageFile>()
    // permitUrlList: Array<UploadImageFile> = new Array<UploadImageFile>()

    deliveryRegion: Array<string> = new Array<string>()

    invoiceMapType = {
      ['1']: '增值税电子普通发票（自动开票）',
      ['2']: '增值税电子发票（线下开票）'
    }

    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }

    regionName = ''

    QueryDeliverWayTypeList = new QueryDeliverWayTypeList()

    form = {
      name: '',
      resource: ''
    }

    // 原发票信息
    invoiceInfo: OffLinePageInvoiceVo = new OffLinePageInvoiceVo()
    // 修改的发票信息
    updateInfo: OffLinePageInvoiceResponseVo = new OffLinePageInvoiceResponseVo()

    pickUpList: Array<TakePlaceDetailVo> = new Array<TakePlaceDetailVo>()

    curPickUpIndex = 0

    //专票开票校验规则
    specialInvoiceRules = {
      title: [
        {
          required: true,
          message: '请填写抬头',
          trigger: ['change', 'blur']
        }
      ],
      taxpayerNo: [
        {
          required: true,
          message: '请输入统一社会信用代码',
          trigger: ['change', 'blur']
        }
      ],
      bankName: [
        {
          required: true,
          message: '请输入开户银行',
          trigger: ['change', 'blur']
        }
      ],
      account: [
        {
          required: true,
          message: '请输入开户账号',
          trigger: ['change', 'blur']
        }
      ],
      rePhone: [
        {
          required: true,
          message: '请输入注册电话',
          trigger: ['change', 'blur']
        }
      ],
      address: [
        {
          required: true,
          message: '请输入注册地址',
          trigger: ['change', 'blur']
        }
      ],
      // businessLicenseUrl: [
      //   {
      //     required: true,
      //     message: '请上传营业执照',
      //     trigger: ['change', 'blur']
      //   }
      // ],
      // permitUrl: [
      //   {
      //     required: true,
      //     message: '请上传开户许可证',
      //     trigger: ['change', 'blur']
      //   }
      // ],
      consignee: [
        {
          required: true,
          message: '请输入收货人',
          trigger: ['change', 'blur']
        }
      ],
      deliveryphone: [
        {
          required: true,
          message: '请输入手机号',
          trigger: ['change', 'blur']
        }
      ],
      deliveryRegion: [
        {
          required: true,
          message: '请选择地区',
          trigger: ['change', 'blur']
        }
      ],
      deliveryAddress: [
        {
          required: true,
          message: '请输入详细地址',
          trigger: ['change', 'blur']
        }
      ]
    }

    dialogVisible = false

    @Watch('deliveryRegion', {
      deep: true
    })
    changeRegion() {
      const el: any = this.$refs['region']
      if (el) {
        if (this.deliveryRegion?.length === 0) {
          this.updateInfo.deliveryRegion = undefined
          this.deliveryRegion = []
          el.validate()
        } else {
          this.updateInfo.deliveryRegion = this.deliveryRegion?.join('/')
          el.clearValidate()
        }
      }
    }

    @Watch('dialogCtrl')
    changeDialogCtrl() {
      if (this.dialogCtrl) {
        this.getInvoiceInfo()
      }
      this.dialogVisible = this.dialogCtrl
    }

    @Watch('dialogVisible')
    changeDialogVisible() {
      this.$emit('update:dialogCtrl', this.dialogVisible)
    }

    // @Watch('businessLicenseUrlList', {
    //   deep: true
    // })
    // changeBusinessLicenseUrl(val: any) {
    //   const el: any = this.$refs['businessLicensePicture']
    //   if (el) {
    //     if (val.length) {
    //       this.updateInfo.businessLicenseUrl = val[0].url
    //       el.clearValidate()
    //     } else {
    //       this.updateInfo.businessLicenseUrl = null
    //       el.validate()
    //     }
    //   }
    // }

    // @Watch('permitUrlList', {
    //   deep: true
    // })
    // changePermitUrl(val: any) {
    //   const el: any = this.$refs['permitPicture']
    //   if (el) {
    //     if (val.length) {
    //       this.updateInfo.permitUrl = val[0].url
    //       el.clearValidate()
    //     } else {
    //       this.updateInfo.permitUrl = null
    //       el.validate()
    //     }
    //   }
    // }

    upperCase() {
      const arr = this.updateInfo.taxpayerNo.split('')
      let newStr = ''
      arr.forEach((value) => {
        if (value >= 'a' && value <= 'z') {
          newStr += value.toUpperCase()
        } else {
          newStr += value
        }
      })
      this.updateInfo.taxpayerNo = newStr
    }

    changeCurIndex(index: number) {
      this.curPickUpIndex = index
      this.updateInfo.takePointPickupTime = this.pickUpList[index].openTakeTime
      this.updateInfo.takePointPickupLocation = this.pickUpList[index].address
      this.updateInfo.takePointRemark = this.pickUpList[index].remark
    }

    doSave() {
      this.modifyInvoiceForm.validate((valid: boolean) => {
        this.deliveryForm.validate((deliveryValid: boolean) => {
          if (valid && deliveryValid) {
            this.doModify()
          }
        })
      })
    }

    async doModify() {
      if (this.updateInfo.shippingMethod === 1 && this.curPickUpIndex == 0) {
        this.updateInfo.takePointPickupTime = this.pickUpList[0].openTakeTime
        this.updateInfo.takePointPickupLocation = this.pickUpList[0].address
        this.updateInfo.takePointRemark = this.pickUpList[0].remark
      }
      if (this.updateInfo.shippingMethod === 1) {
        this.updateInfo.consignee = undefined
        this.updateInfo.deliveryphone = undefined
        this.updateInfo.deliveryRegion = undefined
        this.updateInfo.deliveryAddress = undefined
      } else if (this.updateInfo.shippingMethod === 2) {
        this.updateInfo.takePointPickupTime = undefined
        this.updateInfo.takePointPickupLocation = undefined
        this.updateInfo.takePointRemark = undefined
      }
      this.updateInfo.invoiceVerifyStrategy = 2
      const res =
        await TradeModule.singleTradeBatchFactor.invoiceFactor.mutationOffLineInvoice.updateOfflinePaperInvoice(
          this.updateInfo
        )
      if (res.status.isSuccess()) {
        this.$message.success('修改发票成功')
        this.dialogVisible = false
        this.$emit('callBack')
      } else {
        this.$message.error(res.status?.errors[0]?.message || '修改发票失败')
      }
    }

    //四个全填和全不填的以及格式校验
    validUnitInfo() {
      const titleReg = /^[0-9a-zA-Z\u4e00-\u9fa5（）()《》—-]+$/
      const taxpayerNoReg = /^[A-Za-z0-9]{18}$/

      if (this.updateInfo.address.indexOf('·') != -1) {
        this.$message.warning('注册地址暂不支持特殊字符“ · ”')
        return false
      }
      if (!titleReg.test(this.updateInfo.title)) {
        this.$message.warning('单位名称特殊符号仅支持《》、—、-、（）')
        return false
      }
      if (!taxpayerNoReg.test(this.updateInfo.taxpayerNo)) {
        this.$message.warning('请输入正确的18位统一社会信用代码')
        return false
      }
      return true
    }

    //修改发票信息
    async commitApply() {
      this.loading = true
      await this.modifyInvoiceForm.validate(async (valid: boolean) => {
        await this.deliveryForm.validate(async (deliveryValid: boolean) => {
          if (valid && deliveryValid && this.validUnitInfo()) {
            await this.doModify()
          }
        })
      })
      this.loading = false
    }

    async getPickUpInfo() {
      try {
        this.curPickUpIndex = 0
        await this.QueryDeliverWayTypeList.query()
        this.pickUpList = this.QueryDeliverWayTypeList.pickUpList
        this.pickUpList.forEach((item: TakePlaceDetailVo, index: number) => {
          if (
            item.openTakeTime == this.updateInfo.takePointPickupTime &&
            item.address == this.updateInfo.takePointPickupLocation &&
            item.remark == this.updateInfo.takePointRemark
          ) {
            this.curPickUpIndex = index
          }
        })
      } catch (e) {
        console.log(e)
      }
    }

    // 注释====================
    //获取所在地区中文
    async getBusinessRegion(regionCode: string) {
      let regionArr: Array<string> = []
      if (regionCode.substring(0, 1) === '/') {
        regionArr = regionCode.substring(1, regionCode.length).split('/')
      } else {
        regionArr = regionCode.split('/')
      }
      // await QueryPhysicalRegion.queryPhysicalRegionByIdList(regionArr)
      // this.regionName = QueryPhysicalRegion.getNameByRegionPath(regionCode)
    }

    /*get getBusinessRegion() {
      return async (regionCode: string) => {
        let regionArr: Array<string> = []
        if (regionCode.substring(0, 1) === '/') {
          regionArr = regionCode.substring(1, regionCode.length).split('/')
        } else {
          regionArr = regionCode.split('/')
        }
        await QueryPhysicalRegion.queryPhysicalRegionByIdList(regionArr)
        const regionName = QueryPhysicalRegion.getNameByRegionPath(regionCode)
        debugger
        return regionName
      }
    }*/

    async getInvoiceInfo() {
      this.updateInfo = new OffLinePageInvoiceResponseVo()
      await FilePreview.applyResourceAccessToken()
      try {
        this.invoiceInfo =
          (await TradeModule.batchTradeBatchFactor.invoiceFactor.queryOffLineInvoice.offLineGetInvoiceInServicer(
            this.invoiceId
          )) as OffLinePageInvoiceResponseVo
        if (this.invoiceInfo.businessLicenseUrl && this.invoiceInfo.businessLicenseUrl.substring(0, 4) !== '/mfs') {
          this.invoiceInfo.businessLicenseUrl = FilePreview.getFileUrlWithToken(
            '/mfs' + this.invoiceInfo.businessLicenseUrl
          )
        } else {
          this.invoiceInfo.businessLicenseUrl = FilePreview.getFileUrlWithToken(this.invoiceInfo.businessLicenseUrl)
        }
        if (this.invoiceInfo.permitUrl && this.invoiceInfo.permitUrl.substring(0, 4) !== '/mfs') {
          this.invoiceInfo.permitUrl = FilePreview.getFileUrlWithToken('/mfs' + this.invoiceInfo.permitUrl)
        } else {
          this.invoiceInfo.permitUrl = FilePreview.getFileUrlWithToken(this.invoiceInfo.permitUrl)
        }
        if (this.invoiceInfo.deliveryRegion) {
          await this.getBusinessRegion(this.invoiceInfo.deliveryRegion)
        }
        this.updateInfo = Object.assign(new OffLinePageInvoiceResponseVo(), this.invoiceInfo)
        this.updateInfo.titleType = TitleTypeEnum.UNIT
        this.deliveryRegion = this.updateInfo?.deliveryRegion?.split('/') || undefined
        if (this.deliveryRegion?.length && this.deliveryRegion[0] === '') {
          this.deliveryRegion.splice(0, 1)
        }
        // this.businessLicenseUrlList = new Array<UploadImageFile>()
        // this.permitUrlList = new Array<UploadImageFile>()
        // this.businessLicenseUrlList.push(new UploadImageFile())
        // this.permitUrlList.push(new UploadImageFile())
        // this.businessLicenseUrlList[0].url = this.updateInfo.businessLicenseUrl
        // // this.updateInfo.businessLicenseUrl + `?token=${FileModule.resourceAccessToken}`
        // this.businessLicenseUrlList[0].name = this.updateInfo.businessLicenseUrl
        // this.permitUrlList[0].url = this.updateInfo.permitUrl
        // // this.updateInfo.permitUrl + `?token=${FileModule.resourceAccessToken}`
        // this.permitUrlList[0].name = this.updateInfo.permitUrl
        await this.getPickUpInfo()
      } catch (e) {
        console.log(e)
      } finally {
        //都要执行的操作
      }
    }

    created() {
      this.dialogVisible = this.dialogCtrl
    }
  }
</script>
