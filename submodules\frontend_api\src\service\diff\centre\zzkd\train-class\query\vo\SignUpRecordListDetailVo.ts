import SkuPropertyConvertUtils from '@api/service/centre/train-class/util/SkuPropertyConvertUtils'
import SignUpRecordListDetailVo from '@api/service/centre/train-class/query/vo/SignUpRecordListDetailVo'
import { ZZKDStudentSchemeLearningResponse } from '@api/diff-gateway/platform-jxjypxtypt-zzkd-school'

/**
 * @description 报名记录列表详情
 */

class SignUpRecordListDetailVoDiff extends SignUpRecordListDetailVo {
  /**
   * 证书编号
   */
  certificateNumber = ''

  static async diffFrom(response: ZZKDStudentSchemeLearningResponse): Promise<SignUpRecordListDetailVoDiff> {
    const detail = new SignUpRecordListDetailVoDiff()
    Object.assign(detail, response)
    detail.studentInfo.userId = response.student?.userId ?? null
    detail.schemeId = response.scheme?.schemeId ?? null
    detail.schemeType = response.scheme?.schemeType ?? null
    // 报名时间
    detail.signUpDate = response.learningRegister?.registerTime ?? null
    //差异化添加证书编号
    detail.certificateNumber = response.certificateNumber ?? null
    try {
      // sku属性
      detail.skuProperty = await SkuPropertyConvertUtils.convertSkuPropertyToValueName(detail.scheme?.skuProperty)
    } catch (e) {
      console.error(e)
    }
    return detail
  }
}

export default SignUpRecordListDetailVoDiff
