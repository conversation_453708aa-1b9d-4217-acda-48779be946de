import { CoursePackageResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class CoursePackageDetailVo {
  id: string
  /*
   课程包名称
   */
  name: string
  /*
   展示名称
   */
  showName: string

  /*
   课程数量
   */
  courseCount: number

  /*
   总学时
   */
  totalPeriod: number

  // 是否被引用为必修
  isBeingUsedAsCompulsory: boolean
  isBeingUsedByScheme: boolean

  updateTime: string
  creatTime: string

  static from(coursePackageResponse: CoursePackageResponse) {
    const detail = new CoursePackageDetailVo()
    detail.id = coursePackageResponse.coursePackageId
    detail.name = coursePackageResponse.coursePackage.name
    detail.courseCount = coursePackageResponse.courseStatisticOfCoursePackage.courseCount
    detail.creatTime = coursePackageResponse.coursePackage.createdTime
    detail.totalPeriod = coursePackageResponse.courseStatisticOfCoursePackage.courseTotalPeriod
    detail.updateTime = coursePackageResponse.coursePackage.lastUpdatedTime
    detail.showName = coursePackageResponse.coursePackage.displayName
    detail.isBeingUsedAsCompulsory = coursePackageResponse.coursePackage.isBeingUsedAsCompulsory
    detail.isBeingUsedByScheme = coursePackageResponse.coursePackage.isReferenced === 1
    return detail
  }
}

export default CoursePackageDetailVo
