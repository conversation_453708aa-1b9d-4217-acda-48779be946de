<template>
  <div
    class="f-p15"
    v-if="$hasPermission('invoiceInfo')"
    desc="发票信息"
    actions="@invoiceAutomatic,@invoiceOffline,@invoiceIncrement,@invoiceDistribution"
  >
    <el-tabs v-model="activeName2" type="card" class="m-tab-card">
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="first" :lazy="true">
        <template v-if="$hasPermission('invoiceAutomatic')" desc="电子发票" actions="@invoiceAutomatic">
          <invoice-automatic ref="invoiceAutomaticRef" :user-data="userInfo"></invoice-automatic>
        </template>
      </el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（线下开票）" name="second" :lazy="true">
        <template v-if="$hasPermission('invoiceOffline')" desc="电子发票（线下开票）" actions="@invoiceOffline">
          <invoice-offline ref="invoiceOfflineRef" :user-data="userInfo"></invoice-offline>
        </template>
      </el-tab-pane>

      <el-tab-pane label="增值税电子专用发票（线下开票）" name="fiveth" :lazy="true">
        <template
          v-if="$hasPermission('electronicSpecialInvoice')"
          desc="增值税电子专用发票（线下开票）"
          actions="@invoiceSpecial"
        >
          <invoice-special ref="invoiceSpecialRef" :user-data="userInfo"></invoice-special>
        </template>
      </el-tab-pane>

      <el-tab-pane label="增值税专用发票（纸质票）" name="third" :lazy="true">
        <template v-if="$hasPermission('invoiceIncrement')" desc="增值税专票" actions="@invoiceIncrement">
          <invoice-increment ref="invoiceIncrementRef" :user-data="userInfo"></invoice-increment>
        </template>
      </el-tab-pane>
      <el-tab-pane label="发票配送" name="fourth" :lazy="true">
        <template v-if="$hasPermission('invoiceDistribution')" desc="发票配送" actions="@invoiceDistribution">
          <invoice-distribution ref="invoiceDistributionRef" :user-data="userInfo"></invoice-distribution>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import Page from '@hbfe/jxjy-admin-common/src/models/Page'
  import invoiceAutomatic from '@hbfe/jxjy-admin-customerService/src/diff/qztg/personal/components/invoice-automatic.vue'
  import invoiceOffline from '@hbfe/jxjy-admin-customerService/src/personal/components/invoice-offline.vue'
  import invoiceSpecial from '@hbfe/jxjy-admin-customerService/src/diff/qztg/personal/components/invoice-special.vue'
  import invoiceIncrement from '@hbfe/jxjy-admin-customerService/src/diff/qztg/personal/components/invoice-increment.vue'
  import invoiceDistribution from '@hbfe/jxjy-admin-customerService/src/personal/components/invoice-distribution.vue'
  import UserModules from '@api/service/management/user/UserModule'
  import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
  @Component({
    components: {
      invoiceAutomatic,
      invoiceOffline,
      invoiceSpecial,
      invoiceIncrement,
      invoiceDistribution
    }
  })
  export default class extends Vue {
    @Ref('invoiceAutomaticRef') invoiceAutomaticRef: invoiceAutomatic
    @Ref('invoiceOfflineRef') invoiceOfflineRef: invoiceOffline
    @Ref('invoiceIncrementRef') invoiceIncrementRef: invoiceIncrement
    @Ref('invoiceDistributionRef') invoiceDistributionRef: invoiceDistribution
    @Ref('invoiceSpecialRef') invoiceSpecialRef: invoiceSpecial
    select = ''
    input = ''
    // 学员信息
    userInfo = new UserDetailVo()
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    page: Page = new Page()
    activeName2 = 'first'
    @Watch('activeName2', {
      deep: true
    })
    activeName2Change(val: any) {
      if (val === 'first') {
        this.$nextTick(async () => {
          await this.invoiceAutomaticRef.search()
        })
      } else if (val === 'second') {
        this.$nextTick(async () => {
          await this.invoiceOfflineRef.search()
        })
      } else if (val === 'third') {
        this.$nextTick(async () => {
          await this.invoiceIncrementRef.search()
        })
      } else if (val === 'fourth') {
        this.$nextTick(async () => {
          await this.invoiceDistributionRef.search()
        })
      }
    }
    // 学员id 由主文件ref传入
    userId = ''
    // @Watch('userId', {
    //   immediate: true,
    //   deep: true
    // })
    async userIdChange(val: string) {
      this.userId = val
      await this.getUserInfo()
    }
    // 查询用户信息
    async getUserInfo() {
      const res = await UserModules.queryUserFactory.queryStudentDetail(this.userId).queryDetail()
      if (res.status.isSuccess()) {
        this.userInfo = res.data
      } else {
        this.userInfo = new UserDetailVo()
      }
    }
  }
</script>
