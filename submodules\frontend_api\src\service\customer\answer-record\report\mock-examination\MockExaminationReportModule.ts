import store from '@/store'
import { Module, VuexModule, getModule, Action, Mutation } from 'vuex-module-decorators'
import PlatformExamGateway, {
  ExamRecordDTO,
  ExamRecordParamDTO,
  UserExamQuestionCorrectRateStatisticDTO,
  UserExamQuestionCorrectRateStatisticParamDTO,
  UserExamStatisticParamDTO
} from '@api/gateway/PlatformExam'
import Response, { ResponseStatus } from '../../../../../Response'
import * as graphqlImporter from './graphql-importer'
import { Role, RoleType, Secure } from '../../../../../Secure'
import AnswerRecordUtils from '@api/service/customer/answer-record/report/utils/AnswerRecordUtils'
import { DateRange } from '@api/service/customer/answer-record/report/enums/enums'

class StateCache {
  constructor(schemeId: string, learningId: string, majorId: string) {
    this.schemeId = schemeId
    this.learningId = learningId
    this.majorId = majorId
  }

  // 方案id
  schemeId: string
  // 方式id
  learningId: string
  majorId: string
  // 平均分
  aveScore = 0
  // 平均分
  aveScoreToCompare = 0
  // 最高分
  maxScore = 0
  /**
   * 平均分网校排名
   */
  examRank = 0
  /**
   * 正确率
   */
  correctRate = 0
  //用户模考答题正确率统计
  allTypeCorrectRate: UserExamQuestionCorrectRateStatisticDTO = new UserExamQuestionCorrectRateStatisticDTO()
  // 最短用时
  minUseTime = 0
  // 平均用时
  avgUseTime = 0
  // 基础排名
  examRankBase = 0
  // 要对比的排名
  examRankToCompare = 0
  // 作答记录基础
  answerRecordBase: Array<ExamRecordDTO> = new Array<ExamRecordDTO>()
  // 作答记录要对比的
  answerRecordToCompare: Array<ExamRecordDTO> = new Array<ExamRecordDTO>()
  // 是否需要重载，在学员触发去作答练习是置为true，在下次取数时先清空数据然后加载新数据
  needReload = true
  // 最新一次加载时间，从apollo配置的超时时间与当前时间判断，在下次取数时清空当前取数数据然后加载新数据
  latestLoadTime: Date = new Date()
}

export interface IState {
  learningStateCacheMap: Array<StateCache>
}

@Module({
  namespaced: true,
  name: 'MockExaminationReportModule',
  store,
  dynamic: true
})
class MockExaminationReportModule extends VuexModule implements IState {
  learningStateCacheMap = new Array<StateCache>()

  @Role([RoleType.user])
  @Action
  async init(payload: {
    schemeId: string
    learningId: string
    majorId: string
    range: DateRange
    customStartDate: Date
    customEndDate: Date
  }) {
    if (
      !this.learningStateCacheMap.find(p => p.learningId === payload.learningId) ||
      this.learningStateCacheMap.find(p => p.learningId === payload.learningId)?.needReload
    ) {
      if (!payload.schemeId || !payload.learningId || !payload.majorId || payload.range === undefined) {
        console.log('初始化模考报告参数异常' + JSON.stringify(payload))
        return new ResponseStatus(500, '参数为空，请检查')
      }
      if (!this.learningStateCacheMap.find(p => p.learningId === payload.learningId)) {
        const stateCache = new StateCache(payload.schemeId, payload.learningId, payload.majorId)
        this.setStateCacheToLearningStateCacheMap(stateCache)
      }
      const currentDate = new Date()
      // 根据条件查询值
      const stateCache = new StateCache(payload.schemeId, payload.learningId, payload.majorId)
      const statisticParam: UserExamStatisticParamDTO = new UserExamStatisticParamDTO()
      statisticParam.schemeId = payload.schemeId
      statisticParam.learningId = payload.learningId
      statisticParam.completeTimeStart = AnswerRecordUtils.getStartTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      statisticParam.completeTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )

      // 各题型对题率
      const allTypeCorrectRateParam: UserExamQuestionCorrectRateStatisticParamDTO = new UserExamQuestionCorrectRateStatisticParamDTO()
      allTypeCorrectRateParam.schemeId = payload.schemeId
      allTypeCorrectRateParam.learningId = payload.learningId
      allTypeCorrectRateParam.completeTimeStart = AnswerRecordUtils.getStartTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      allTypeCorrectRateParam.completeTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )

      // 基础数据
      const baseStatisticParam: UserExamStatisticParamDTO = new UserExamStatisticParamDTO()
      baseStatisticParam.schemeId = payload.schemeId
      baseStatisticParam.learningId = payload.learningId
      baseStatisticParam.completeTimeStart = undefined
      baseStatisticParam.completeTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      // 基础的作答记录
      const answerRecordBaseParam: ExamRecordParamDTO = new ExamRecordParamDTO()
      answerRecordBaseParam.schemeId = payload.schemeId
      answerRecordBaseParam.learningId = payload.learningId
      answerRecordBaseParam.completeTimeStart = undefined
      answerRecordBaseParam.completeTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      answerRecordBaseParam.statisticSize = AnswerRecordUtils.getTrendQueryTimes(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )

      // 要对比的
      const toCompareStatisticParam: UserExamStatisticParamDTO = new UserExamStatisticParamDTO()
      toCompareStatisticParam.schemeId = payload.schemeId
      toCompareStatisticParam.learningId = payload.learningId
      toCompareStatisticParam.completeTimeEnd = AnswerRecordUtils.getToCompareQueryEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      // 要比较的作答记录
      const answerRecordToCompareParam: ExamRecordParamDTO = new ExamRecordParamDTO()
      answerRecordToCompareParam.schemeId = payload.schemeId
      answerRecordToCompareParam.learningId = payload.learningId
      answerRecordToCompareParam.completeTimeStart = undefined
      answerRecordToCompareParam.completeTimeEnd = AnswerRecordUtils.getToCompareQueryEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      answerRecordToCompareParam.statisticSize = AnswerRecordUtils.getTrendQueryTimes(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )

      const param = {
        statisticParam: statisticParam,
        baseStatisticParam: baseStatisticParam,
        toCompareStatisticParam: toCompareStatisticParam,
        allTypeCorrectRateParam: allTypeCorrectRateParam,
        answerRecordBaseParam: answerRecordBaseParam,
        answerRecordToCompareParam: answerRecordToCompareParam
      }
      const response: Response<any> = await PlatformExamGateway._commonQuery(
        graphqlImporter.getMockExaminationReport,
        param
      )
      if (!response.status.isSuccess()) {
        return response.status
      }
      const statistic = response.data.statistic

      stateCache.aveScore = statistic.aveScore
      stateCache.examRank = statistic.examRank
      stateCache.correctRate = statistic.correctRate
      stateCache.maxScore = statistic.maxScore
      stateCache.minUseTime = statistic.minUseTime
      stateCache.avgUseTime = statistic.avgUseTime

      // 各题型对题率
      const allTypeCorrectRate = response.data.allTypeCorrectRate
      stateCache.allTypeCorrectRate = allTypeCorrectRate

      // 基础数据
      stateCache.examRankBase = response.data.baseStatistic?.examRank
      // 基础的作答记录
      stateCache.answerRecordBase = response.data.answerRecordBase

      // 要对比的
      stateCache.aveScoreToCompare = response.data.toCompareStatistic?.aveScore
      stateCache.examRankToCompare = response.data.toCompareStatistic?.examRank
      // 要比较的作答记录
      stateCache.answerRecordToCompare = response.data.answerRecordToCompare
      this.setStateCacheToLearningStateCacheMap(stateCache)
    } else if (
      (this.learningStateCacheMap.find(p => p.learningId === payload.learningId)?.latestLoadTime?.getTime() || 0) <
      new Date().getTime() - 10000
    ) {
      this.setNeedReload({
        schemeId: payload.schemeId,
        learningId: payload.learningId,
        majorId: payload.majorId
      })
      this.init(payload)
    }
  }

  @Mutation
  setStateCacheToLearningStateCacheMap(payload: StateCache) {
    this.learningStateCacheMap = this.learningStateCacheMap.filter(p => p.learningId !== payload.learningId)
    this.learningStateCacheMap.push(payload)
  }

  @Mutation
  setNeedReload(payload: any) {
    const stateCache = this.learningStateCacheMap.find(p => p.learningId === payload.learningId)
    if (stateCache) {
      stateCache.needReload = true
    }
  }

  get getState() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)
    }
  }
  get getAveScore() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.aveScore
    }
  }
  get getAveScoreToCompare() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.aveScoreToCompare
    }
  }
  get getMaxScore() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.maxScore
    }
  }
  get getExamRank() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.examRank
    }
  }
  get getCorrectRate() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.correctRate
    }
  }
  get getAllTypeCorrectRate() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.allTypeCorrectRate
    }
  }
  get getMinUseTime() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.minUseTime
    }
  }
  get getAvgUseTime() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.avgUseTime
    }
  }
  get getExamRankBase() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.examRankBase || 0
    }
  }
  get getExamRankToCompare() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.examRankToCompare || 0
    }
  }
  get getAnswerRecordBase() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.answerRecordBase
    }
  }
  get getAnswerRecordToCompare() {
    return (schemeId: string, learningId: string) => {
      return this.learningStateCacheMap.find(p => p.learningId === learningId)?.answerRecordToCompare
    }
  }
  get examRankChange() {
    return (schemeId: string, learningId: string) => {
      return this.getExamRankToCompare(schemeId, learningId) - this.getExamRankBase(schemeId, learningId)
    }
  }
  get totalAnswerCount() {
    return (schemeId: string, learningId: string) => {
      return (
        (this.getAllTypeCorrectRate(schemeId, learningId)?.judgement?.answeredCount || 0) +
        (this.getAllTypeCorrectRate(schemeId, learningId)?.comprehensive?.answeredCount || 0) +
        (this.getAllTypeCorrectRate(schemeId, learningId)?.single?.answeredCount || 0) +
        (this.getAllTypeCorrectRate(schemeId, learningId)?.multiple?.answeredCount || 0)
      )
    }
  }
}

export default getModule(MockExaminationReportModule)
