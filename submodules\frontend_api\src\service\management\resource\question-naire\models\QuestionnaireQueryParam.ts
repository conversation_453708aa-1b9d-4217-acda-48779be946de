import DateScope from '@api/service/common/models/DateScope'
import { QuestionnaireStatusEnum } from '@api/service/management/resource/question-naire/enums/QuestionnaireStatus'
import { QuestionnaireTypeEnum } from '@api/service/management/resource/question-naire/enums/QuestionnaireType'
import QuestionnaireItem from '@api/service/management/resource/question-naire/models/QuestionnaireItem'
import {
  GetQuestionnaireInIssueRequest,
  GetQuestionnaireInSchemeRequest
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'
import PlatformQuestionnaireQueryBackStage, {
  QuestionnaireTemplateRequest
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'

export default class QuestionnaireQueryParam {
  /**
   * 问卷名称
   */
  name = ''
  /**
   * 类型
   */
  type: QuestionnaireTypeEnum = null
  /**
   * 创建时间
   */
  createTimeScope: DateScope = new DateScope()
  /**
   * 状态
   */
  status: QuestionnaireStatusEnum = null
  /**
   * 方案使用情况
   */
  schemeUseStatus: boolean = null
  /**
   * 方案id
   */
  schemeId = ''
  /**
   * 模板ids
   */
  templateIds = new Array<string>()
  static to(vo: QuestionnaireQueryParam) {
    const dto = new QuestionnaireTemplateRequest()
    if (vo.type) {
      dto.type = vo.type
    }
    dto.schemeId = vo.schemeId
    dto.questionnaireName = vo.name
    dto.isReferenced = vo.schemeUseStatus
    dto.createStartTime = vo.createTimeScope.begin
    dto.createEndTime = vo.createTimeScope.end
    if (vo.status) {
      dto.answerPaperStatus = vo.status
    }
    dto.ids = vo.templateIds
    return dto
  }

  /**
   * 转换成查询期别下问卷的查询模型 pageQuestionnaireInIssue
   */
  toPeriodQuestionnaireQuery() {
    const request = new GetQuestionnaireInIssueRequest()
    request.questionnaireName = this.name
    if (this.type) {
      request.type = this.type
    }
    return request
  }

  /**
   * 转换成查询方案下问卷的查询模型 pageQuestionnaireLearningSchemeInServicer
   */
  toSchemeQuestionnaireQuery() {
    const request = new GetQuestionnaireInSchemeRequest()
    request.questionnaireName = this.name
    if (this.type) {
      request.type = this.type
    }
    return request
  }
}
