<style lang="scss">
  @import 'index';
  .foot {
    text-align: center;
    margin-bottom: 15px;
    color: #75829e;
  }
</style>

<template>
  <el-container width="240px">
    <el-aside v-if="hideMenuStatus" :style="sideStyle" :class="{ 'side-collapsed': isCollapse }">
      <a href="#" @click.prevent="collapse" class="aside-btn" :style="collapseStyle">
        <i :class="{ false: 'el-icon-s-fold', true: 'el-icon-s-unfold' }[isCollapse]" />
      </a>
      <div class="logo">
        <!--        <img class="logo-pic" src="@design/admin/assets/images/logo.png" alt="logo" />-->
        <span class="logo-txt">{{ title }}</span>
        <span class="sub" v-if="sideTitle">{{ webPortalInfo.title }}</span>
      </div>
      <user-card></user-card>
      <hb-menu class="aside-nav" :is-collapse="isCollapse" :backgroud-color="sideStyle.backgroundColor" />
      <div class="m-company-info" v-if="!isOperation">
        <div v-html="footContent"></div>
      </div>
      <div class="m-company-info" v-else>
        <p>版权所有：福建华博教育科技股份有限公司</p>
        <a class="a-txt" href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">闽ICP备08103886号-2</a>
        <p>
          增值电信业务经营许可证<a @click="dialog1 = true" class="a-txt"> 闽B2-20180687</a>
          <el-dialog :visible.sync="dialog1" width="1200px" class="m-dialog">
            <img width="1160px" src="@design/trainingInstitution/assets/images/dianxin_xuke.jpg" alt="" />
          </el-dialog>
        </p>
        <p>
          <a
            href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=%2035010202001216"
            target="_blank"
            class="a-txt"
            ><img class="f-vm f-mr5" src="@design/trainingInstitution/assets/images/beian-icon.png" alt=" " />闽公网安备
            35010202001216号</a
          >
        </p>
      </div>
    </el-aside>
    <el-container>
      <el-header v-if="hideMenuStatus" :height="headerHeight" class="f-flex">
        <top-guide></top-guide>
        <development></development>
        <div class="tools" @click="toolOperate">
          <i class="el-icon-refresh tool-operate" data-action="refresh"></i>
          <i class="el-icon-full-screen tool-operate" data-action="fullscreen"></i>
          <i class="el-icon-setting tool-operate" data-action="settings"></i>
        </div>
        <ul class="header-nav">
          <li class="nav-item nav-item-1" @click="logout">
            <i class="el-icon-switch-button"></i>
            退出
          </li>
        </ul>
      </el-header>
      <hb-navigation v-if="hideMenuStatus" />
      <!--运营域 修改网校页 面包屑-->
      <el-breadcrumb v-if="$route.path.includes('/school-management/modify')" separator-class="el-icon-arrow-right">
        <el-button type="text" size="mini" class="return-btn">
          <i class="iconfont icon-lsh-return"></i>
        </el-button>
        <el-breadcrumb-item :to="{ path: '/school-management/management' }">网校管理</el-breadcrumb-item>
        <el-breadcrumb-item>修改网校</el-breadcrumb-item>
      </el-breadcrumb>
      <el-main ref="container" class="height-full">
        <transition :name="transformName" mode="out-in">
          <keep-alive>
            <router-view :key="$route.fullPath" />
          </keep-alive>
        </transition>
      </el-main>
    </el-container>
  </el-container>
</template>

<style lang="scss" scoped>
  .bare-badge {
    .el-badge__content {
      z-index: 1;
    }
  }
</style>

<script lang="ts">
  import { Vue, Component, Watch } from 'vue-property-decorator'
  import rootModule from '@/store/RootModule'
  import HbNavigation from '@hbfe/jxjy-admin-components/src/base-container/components/nav/index.vue'
  import UserCard from '@hbfe/jxjy-admin-components/src/base-container/components/user-card.vue'
  import TopGuide from '@hbfe/jxjy-admin-components/src/base-container/components/top-guide.vue'
  import HbMenu from '@hbfe/jxjy-admin-components/src/base-container/components/menu/index.vue'
  import Message from '@hbfe/jxjy-admin-components/src/base-container/components/message/index.vue'
  import basicDataModule from '@api/service/management/basic-data/BasicDataModule'
  import { bind, debounce } from 'lodash-decorators'
  import Development from '@hbfe/jxjy-admin-components/src/base-container/components/development.vue'
  import Context from '@/RuntimeContext/index'
  import PortalVo from '@api/service/common/online-school-config/vo/PortalVo'
  import RootModule from '@/store/RootModule'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import systemContext from '@api/service/common/context/Context'
  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'

  @Component({
    components: {
      HbNavigation,
      HbMenu,
      Message,
      UserCard,
      TopGuide,
      Development
    }
  })
  export default class extends Vue {
    $authentication: any
    drawer = false
    headerHeight = '100px'
    webPortalInfo: PortalVo = OnlineSchoolConfigModule.queryPortal.webPortalInfo
    footContent = ''
    isOperation = false
    dialog1 = false
    get loginServerType() {
      return Context.businessContext?.serviceProvider?.serviceType
    }

    get collapseStyle() {
      return {
        left: parseInt(this.sideStyle.width) + 10 + 'px'
      }
    }
    get Root() {
      return RootModule || {}
    }

    /**
     * 是否隐藏菜单框架
     */
    get hideMenuStatus() {
      return !rootModule.hideMenuStatus
    }

    sideStyle = {
      width: '240px',
      backgroundColor: '#021c3f'
    }

    transformName = 'fade-right'

    get userInfo() {
      return basicDataModule.userInfo
    }

    get navList() {
      return rootModule.navList
    }

    get keepAliveWhiteList() {
      return Array.from(rootModule.keepAliveWhiteList)
    }

    get title() {
      // return rootModule.title
      return '培训管理平台'
    }
    /**
     * 侧边栏网校名称
     */
    get sideTitle() {
      const http = CommonConfigCenter.getFrontendApplication(frontendApplication.trainingInstitutionDomain)
      if (window.location.hostname == http) {
        return false
      } else {
        return true
      }
    }

    up = true

    get isCollapse() {
      return rootModule.isMenuCollapse
    }

    @Watch('currentActiveIndex')
    currentActiveIndexChange(val: number, oldVal: number) {
      this.transformName = val > oldVal ? 'fade-left' : 'fade-right'
    }

    get currentActiveIndex() {
      return rootModule.currentNavActiveIndex
    }

    @bind
    @debounce(200)
    resizeHandle() {
      if (window.document.body.clientWidth < 900) {
        this.setIsCollapse(true)
      } else {
        this.setIsCollapse(false)
      }
    }

    async created() {
      this.resizeHandle()
      window.addEventListener('resize', this.resizeHandle)
      // 服务商id
      const serviceId = CommonConfigCenter.getFrontendApplication(frontendApplication.trainingInstitutionServicerId)
      this.isOperation = systemContext.servicerInfo.id === serviceId
      this.footContent = OnlineSchoolConfigModule.queryPortal?.webPortalInfo?.footContent
    }

    collapse() {
      this.setIsCollapse(!rootModule.isMenuCollapse)
    }

    setIsCollapse(isCollapse: boolean) {
      rootModule.setMenuCollapse(isCollapse)
      if (isCollapse) {
        this.sideStyle.width = '64px'
      } else {
        this.sideStyle.width = '250px'
      }
    }

    /**
     * 刷新当前激活的路由
     */
    refresh() {
      // const reload = (this.$route.meta || { reload: 'index' }).reload || 'index'
      // this.$reload(rootModule.navList[rootModule.currentNavActiveIndex].rootRoute, reload)
    }

    async logout() {
      this.$confirm('确定要退出登录?', '友情提示', {
        confirmButtonText: '退出登录',
        cancelButtonText: '取消',
        center: true,
        type: 'warning'
      }).then(
        async () => {
          localStorage.removeItem('unitModelName')
          localStorage.removeItem('lastFeedBackTime')
          const isLogout = await this.$authentication.ssoLogout()
          await RootModule.doCloseAllNav()
          // https://mztapp.fujian.gov.cn:8304/dataset/UnifiedController/goUserCenter.do?uitype=5
          // isLogout && this.$authentication.thirdParty.getPartyTypeByKey(ThirdPartyType.minZhenTong).logout()
          if (QueryManagerDetail.hasCategory(CategoryEnums.ztgly)) {
            isLogout && window.location.replace('#/specialSubjectLogin')
          } else {
            isLogout && window.location.replace('#/login')
          }
        },
        () => {
          // nothing
        }
      )
    }

    toolOperate(event: Event) {
      const dataset: { action: string } = (event.target as any).dataset
      switch (dataset.action) {
        case 'refresh':
          console.log('执行刷新')
          location.reload()
          break
        case 'fullscreen':
          console.log('执行全屏')
          this.fullScreenOrOff()
          break
        case 'settings':
          console.log('执行打开设置')
          this.$router.push('/home/<USER>')
          break
      }
    }

    // 全屏操作
    fullScreenOrOff() {
      if (!document.fullscreenElement) {
        //进入全屏
        document.documentElement.requestFullscreen()
      } else {
        if (document.exitFullscreen) {
          //退出全屏
          document.exitFullscreen()
        }
      }
    }

    // systemMenu(command: string) {
    //   ;({

    //     personSetting: () => {
    //       this.$router.push(`/personal-center/admin?redirect=${this.$route.path}`)
    //     },
    //     systemSetting: () => {
    //       console.log('去系统设置中心去')
    //     }
    //   }[command]())
    // }
  }
</script>
