export enum IndustryPropertyCodeEnum {
  /**
   * 科目类型
   */
  SUBJECT_TYPE = 'SUBJECT_TYPE',

  /**
   * 专业类型
   */
  TRAINING_CATEGORY = 'TRAINING_CATEGORY',

  /**
   * 具体专业
   */
  TRAINING_PROFESSIONAL = 'TRAINING_PROFESSIONAL',

  /**
   * 培训对象
   */
  TRAINING_OBJECT = 'TRAINNING_OBJECT',

  /**
   * 岗位类别
   */
  POSITION_CATEGORY = 'POSITION_CATEGORY',

  /**
   * 技术等级
   */
  JOB_LEVEL = 'JOB_LEVEL',

  /**
   * 工种
   */
  JOB_CATEGORY = 'JOB_CATEGORY',

  /**
   * 学段
   */
  LEARNING_PHASE = 'LEARNING_PHASE',
  /**
   * 学科
   */
  DISCIPLINE = 'DISCIPLINE',

  /**
   * 证书类型
   */
  CERTIFICATES_TYPE = 'CERTIFICATES_TYPE',
  /**
   * 执业类别
   */
  PRACTITIONER_CATEGORY = 'PRACTITIONER_CATEGORY',
  /**
   * 科目类型 - 人员
   */
  PERSON_SUBJECT_TYPE = 'PERSON_SUBJECT_TYPE',

  /**
   * 专业类型 - 人员
   */
  PERSON_TRAINING_CATEGORY = 'PERSON_TRAINING_CATEGORY',

  /**
   * 具体专业 - 人员
   */
  PERSON_TRAINING_PROFESSIONAL = 'PERSON_TRAINING_PROFESSIONAL',

  /**
   * 培训对象 - 人员
   */
  PERSON_TRAINING_OBJECT = 'PERSON_TRAINNING_OBJECT',

  /**
   * 岗位类别 - 人员
   */
  PERSON_POSITION_CATEGORY = 'PERSON_POSITION_CATEGORY',

  /**
   * 技术等级 - 人员
   */
  PERSON_JOB_LEVEL = 'PERSON_JOB_LEVEL',

  /**
   * 工种 - 人员
   */
  PERSON_JOB_CATEGORY = 'PERSON_JOB_CATEGORY',
  /**
   * 学段 - 人员
   */
  PERSON_LEARNING_PHASE = 'PERSON_LEARNING_PHASE',
  /**
   * 学科 - 人员
   */
  PERSON_DISCIPLINE = 'PERSON_DISCIPLINE',
  /**
   * 证书类型 - 人员
   */
  PERSON_CERTIFICATES_TYPE = 'PERSON_CERTIFICATES_TYPE',
  /**
   * 执业类别 - 人员
   */
  PERSON_PRACTITIONER_CATEGORY = 'PERSON_PRACTITIONER_CATEGORY'
}
