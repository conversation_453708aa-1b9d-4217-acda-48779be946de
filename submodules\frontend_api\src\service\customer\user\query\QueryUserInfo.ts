/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-14 17:26:09
 * @LastEditors: chenweinian chenweinian
 * @LastEditTime: 2024-10-12 09:44:20
 */
import { ResponseStatus } from '@hbfe/common'
// import ConfigCenterModule from '@api/module/common/config-center/ConfigCenter'
import PlatformUser, { UserInfoDTO } from '@api/gateway/PlatformUser'
import BasicDataQueryForestage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import SchemeLearningQueryForestage, {
  LearningRegisterRequest,
  StudentSchemeLearningRequest,
  StudentSchemeLearningResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import ServicerSeriesV1Gateway from '@api/ms-gateway/ms-servicer-series-v1'
import DataGateway from '@api/platform-gateway/platform-support-fore-gateway'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import QueryTechnologyLevel from '@api/service/common/basic-data-dictionary/query/QueryTechnologyLevel'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import FileModule from '@api/service/common/file/FileModule'
import Page from '@api/service/common/models/page-query/Page'
import { PerfectInfoPoint } from '@api/service/common/webfunny/config/ConfigInfo'
import WebfunnyReport from '@api/service/common/webfunny/WefunnyReport'
import OnlineSchoolConfigModule from '@api/service/customer/online-school-config/OnlineSchoolConfigModule'
import UserModule from '@api/service/customer/user/query-user/UserModule'
import QueryLoginStatus from './QueryLoginStatus'
import UserInfoVo from './vo/UserInfoVo'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import BusinessClientTypeEnum from '@hbfe-biz/biz-authentication/src/enums/BusinessClientTypeEnum'
import SchoolServiceIdStrategy from '@api/service/common/diffSchool/SchoolServiceIdStrategy'
import SchoolEnum from '@api/service/common/diffSchool/enums/SchoolEnum'
import QueryIdCardType from '@api/service/common/basic-data-dictionary/query/QueryIdCardType'
import PlatformTrainingChannel from '@api/platform-gateway/platform-training-channel-v1'

/**
 * 用户信息
 */
class QueryUserInfo {
  private url = '/web/token/getCurrentUser'
  /**
   * 用户基本信息
   */
  basicUserInfo = new UserInfoDTO()
  /**
   * 用户信息
   */
  userInfo = new UserInfoVo()
  /**
   * 用户缺少的行业信息
   */
  lackIndustry: string[] = []
  /**
   * 用户已报名的班级行业信息
   */
  appliedIndustry: string[] = []
  /**
   * 是否完善用户信息
   */
  isPerfectInfo = true
  // * 技术等级名称
  professionalName = ''
  /**
   * 调用这个方法获取用户基本信息和用户信息
   * @returns ResponseStatus
   */
  async getCurrenUserInfo(): Promise<ResponseStatus> {
    let status = new ResponseStatus(200, 'success')
    await Promise.all([this.getUserInfo()]).then(res => {
      for (let i = 0; i < res.length; i++) {
        const element = res[i]
        if (!element.isSuccess()) {
          status = element
        }
      }
    })
    await FileModule.applyResourceAccessToken()
    return status
  }
  /**
   * 重置用户信息，退出使用
   */
  resetUserInfo(prefix = BusinessClientTypeEnum.customer) {
    console.log('重置用户信息')
    if (prefix === BusinessClientTypeEnum.customer) {
      if (localStorage) {
        localStorage.removeItem('customer.Access-Token')
        localStorage.removeItem('customer.Access-Token-Copy')
        localStorage.removeItem('customer.Refresh-Token')
        localStorage.removeItem('customer.Account-Type')
        localStorage.removeItem('customer.Account-Type')
        localStorage.removeItem('customer.WX-openId')
        localStorage.removeItem('customer.WX-unionId')
        localStorage.removeItem('nickname')
        localStorage.removeItem('DomainInformation')
        localStorage.removeItem('RETURNMESSAGE')
        localStorage.removeItem('examDialogNextIds')
      }
      if (sessionStorage) {
        sessionStorage.clear()
      }
      localStorage.removeItem('customer.Fx-Access-Token')
      localStorage.removeItem('customer.Fx-Refresh-Token')
    }
    this.userInfo = new UserInfoVo()
    QueryLoginStatus.setLoginStatus(false)
    QueryManagerDetail.distributionUnitInformationList = []
    localStorage.removeItem('currentUnitId')
    localStorage.removeItem('isNeedAccessTokenValue')
  }
  /**
   * 获取基本用户信息
   * @returns ResponseStatus
   */
  private async getBasicCurrenUserInfo() {
    const status = new ResponseStatus(200, 'sucess')
    const org = ConfigCenterModule.getIngress(ingress.auth)
    const response = await PlatformUser.getCurrentUserInfo()

    if (response) {
      this.basicUserInfo = response.data
    } else {
      status.code = 401
      status.message = '获取基本信息失败'
    }
    return status
  }
  /**
   * 校验是否完善信息
   * @returns
   */
  async checkUserInfoPerfect() {
    // 查询注册必填字段设置
    await OnlineSchoolConfigModule.queryOnlineSchoolConfigFactory.queryWebPortalConfig.queryRegisterSetting()
    const registerSetting = OnlineSchoolConfigModule.queryOnlineSchoolConfigFactory.queryWebPortalConfig.registerSetting
    const nameIsrequired = registerSetting.studentRegister.find(item => item.field === 'name')?.isRequire
    const phoneIsrequired = registerSetting.studentRegister.find(item => item.field === 'phone')?.isRequire
    const idCardIsrequired = registerSetting.studentRegister.find(item => item.field === 'idCard')?.isRequire
    const areaIsrequired = registerSetting.studentRegister.find(item => item.field === 'area')?.isRequire
    // 和项目组沟通后，证件类型是否必填不根据接口返回来做判断。详细可以联系->liuzhichun
    const idCardTypeIsrequired = true
    const companyNameIsrequired = registerSetting.studentRegister.find(item => item.field === 'companyName')?.isRequire
    const genderIsrequired = registerSetting.studentRegister.find(item => item.field === 'gender')?.isRequire
    const certificateAttachments = registerSetting.constructionIndustryRegister.find(
      item => item.field === 'certificateAttachments'
    )?.isRequire
    const professionalLevel = registerSetting.constructionIndustryRegister.find(
      item => item.field === 'professionalLevel'
    )?.isRequire
    const jobCategoryId = registerSetting.constructionIndustryRegister.find(item => item.field === 'professionalLevel')
      ?.isRequire
    const personnelCategory = registerSetting.constructionIndustryRegister.find(
      item => item.field === 'personnelCategory'
    )?.isRequire
    const positionCategory = registerSetting.constructionIndustryRegister.find(
      item => item.field === 'positionCategory'
    )?.isRequire
    const section = registerSetting.constructionIndustryRegister.find(item => item.field === 'section')?.isRequire
    const subjects = registerSetting.constructionIndustryRegister.find(item => item.field === 'subjects')?.isRequire
    //
    const certificatesType = registerSetting.constructionIndustryRegister.find(
      item => item.field === 'certificatesType'
    )?.isRequire
    const practitionerCategory = registerSetting.constructionIndustryRegister.find(
      item => item.field === 'practitionerCategory'
    )?.isRequire
    this.isPerfectInfo = true
    const checkIndustry = await this.checkStudentIndustry()
    /**南阳医专和郑州专技差异化网校不需要完善行业信息 */
    const noIndustryDiffSchool =
      SchoolServiceIdStrategy.currentSchool() === SchoolEnum.NYYZ ||
      SchoolServiceIdStrategy.currentSchool() === SchoolEnum.ZZZJ

    // 存在报名成功但未填写行业字段
    if (noIndustryDiffSchool) {
      this.isPerfectInfo = true
    } else {
      if (!checkIndustry) {
        this.isPerfectInfo = false
        return this.isPerfectInfo
      }
    }

    // 名称是否完善
    if (nameIsrequired && !this.userInfo.userInfo.userName) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    // 证件类型是否完善
    if (idCardTypeIsrequired && !this.userInfo.userInfo.idCardType) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    // 电话号码是否完善
    if (phoneIsrequired && !this.userInfo.userInfo.phone) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    // 身份证号码是否完善
    if (idCardIsrequired && !this.userInfo.userInfo.idCard) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    // 性别是否完善
    if (genderIsrequired && this.userInfo.userInfo.gender === -1) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    // 地区是否完善
    if (areaIsrequired && !this.userInfo.userInfo.companyRegion?.regionId) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    // 公司名称是否完善
    if (companyNameIsrequired && !this.userInfo.userInfo.companyName) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    // 是否开启对接天眼查和企查查且统一社会信用代码是否完善
    if (companyNameIsrequired && registerSetting.unitConfig.enableSearch && !this.userInfo.userInfo.companyCode) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    if (noIndustryDiffSchool) {
      this.isPerfectInfo = true
    } else {
      if (this.userInfo.userInfo.userIndustryList?.length > 0) {
        this.userInfo.userInfo.userIndustryList.map(item => {
          if (this.appliedIndustry.includes(item.industryId)) {
            if (item.industryId === IndustryIdEnum.JS) {
              if (!item.userCertificateList?.length) {
                this.isPerfectInfo = false
              } else {
                item.userCertificateList.map(certificate => {
                  if (certificateAttachments && !certificate.attachmentList?.length) {
                    this.isPerfectInfo = false
                  }
                  if (
                    !certificate.certificateCategory &&
                    !certificate.certificateNo &&
                    !certificate.certificateNo &&
                    !certificate.releaseStartTime &&
                    !certificate.certificateEndTime
                  ) {
                    this.isPerfectInfo = false
                  }
                })
              }
            } else if (item.industryId === IndustryIdEnum.RS) {
              if (!item.firstProfessionalCategory) {
                this.isPerfectInfo = false
              }
              if (!item.professionalQualification) {
                this.isPerfectInfo = false
              }
            } else if (item.industryId === IndustryIdEnum.WS) {
              if (!item.personnelCategory && personnelCategory) {
                this.isPerfectInfo = false
              }
              if (!item.positionCategory && positionCategory) {
                this.isPerfectInfo = false
              }
            } else if (item.industryId === IndustryIdEnum.GQ) {
              if (!item.professionalLevel && professionalLevel) {
                this.isPerfectInfo = false
              }
              if (!item.jobCategoryId && jobCategoryId) {
                this.isPerfectInfo = false
              }
            } else if (item.industryId === IndustryIdEnum.LS) {
              if ((!item.sectionAndSubjects || !item.sectionAndSubjects.length) && (section || subjects)) {
                this.isPerfectInfo = false
              } else if (
                (!item.sectionAndSubjects[0].section && section) ||
                (!item.sectionAndSubjects[0].subjects && subjects)
              ) {
                this.isPerfectInfo = false
              }
            } else if (item.industryId === IndustryIdEnum.YS) {
              if (!item.certificatesType && certificatesType) {
                this.isPerfectInfo = false
              }
              if (!item.practitionerCategory && practitionerCategory) {
                this.isPerfectInfo = false
              }
            }
          } else {
            this.isPerfectInfo = true
          }
        })

        if (!this.isPerfectInfo) {
          return
        }
      }
    }

    this.isPerfectInfo = true
    return this.isPerfectInfo
  }

  /**
   * 判断用户是否存在报名成功但未填写行业字段
   */
  async checkStudentIndustry() {
    this.lackIndustry = []
    const industryList: string[] = []
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 20
    if (this.userInfo.userInfo?.userIndustryList) {
      for (let i = 0; i < this.userInfo.userInfo.userIndustryList.length; i++) {
        const element = this.userInfo.userInfo.userIndustryList[i]
        industryList.push(element.industryId)
      }
    }
    const data = await this.getSchemeLearning(page)
    console.log(this.userInfo.userInfo, '-=-=-=-=-=-=', data)
    const temp: string[] = []

    data.map(item => {
      temp.push(item.scheme.skuProperty.industry.skuPropertyValueId)
      if (!industryList.includes(item.scheme.skuProperty.industry.skuPropertyValueId)) {
        if (!this.lackIndustry.includes(item.scheme.skuProperty.industry.skuPropertyValueId)) {
          this.lackIndustry.push(item.scheme.skuProperty.industry.skuPropertyValueId)
        }
      }
    })

    this.appliedIndustry = [...new Set(temp)]
    // 存在报名成功但未填写行业字段
    if (this.lackIndustry.length > 0) {
      return false
    }
    return true
  }

  private async getSchemeLearning(
    page: Page,
    studyList: Array<StudentSchemeLearningResponse> = []
  ): Promise<Array<StudentSchemeLearningResponse>> {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.status = [1]
    const { data } = await SchemeLearningQueryForestage.pageSchemeLearningInMyself({ page, request: request })
    studyList.push(...data.currentPageData)
    if (data.totalPageSize > studyList.length) {
      // 继续请求
      page.pageNo = page.pageNo + 1
      page.pageSize = data.totalPageSize - page.pageSize < 200 ? data.totalPageSize - studyList.length : 199
      return await this.getSchemeLearning(page, studyList)
    } else {
      return studyList
    }
  }
  /**
   * 获取用户信息
   * @returns ResponseStatus
   */
  private async getUserInfo(): Promise<ResponseStatus> {
    const { status, data } = await BasicDataQueryForestage.getStudentInfoInMyself()

    if (status.isSuccess()) {
      this.userInfo = data
      let idCardTypeFlag = true
      const result = await QueryIdCardType.queryRegisterSettingFormIdCardTypeList()
      result.data.find(item => {
        if (item.code === Number(this.userInfo.userInfo.idCardType)) {
          idCardTypeFlag = false
        }
      })
      if (idCardTypeFlag) {
        this.userInfo.userInfo.idCardType = null
      }
      this.checkName()
      const userIndustryList = this.userInfo?.userInfo?.userIndustryList?.length
        ? this.userInfo.userInfo.userIndustryList
        : []

      // 人设行业的一级专业id后端不再返回 需要根据二级去查取一级
      const rsIndustry = userIndustryList.find(it => it.industryId === IndustryIdEnum.RS)
      if (rsIndustry && rsIndustry.secondProfessionalCategory) {
        const findParentRes = await BasicDataQueryForestage.listBusinessDictionaryAcrossTypeBySalveId(
          rsIndustry.secondProfessionalCategory
        )
        if (findParentRes?.data?.length) {
          rsIndustry.firstProfessionalCategory = findParentRes.data[0].masterId
        }
      }

      QueryLoginStatus.setLoginStatus(true)
      QueryLoginStatus.setShowDialog(false)
    }
    this.professionalName = await this.queryTechnologyLevel(String(this.userInfo.userInfo.professionalLevel))
    return status
  }

  /**
   * 判断名字大于等于4位往上报
   */
  checkName() {
    try {
      if (this.userInfo.userInfo.userName.length >= 4) {
        const description = new PerfectInfoPoint()
        description.abnormalMessage = '姓名大于等于4位'
        description.domainName = location.origin
        description.response = JSON.stringify(this.userInfo.userInfo.userName)
        description.userId = UserModule.userInfo.userId
        WebfunnyReport.upPerfectInfo(description)
        // const description = new Description()
        // description.message = JSON.stringify(this.userInfo.userInfo.userName)
        // upMyLog(description, '名字大于等于4位')
      }
    } catch (e) {
      console.group(
        '%c%s',
        'padding:3px 60px;color:#fff;background-image: linear-gradient(to right, #ffa17f, #00223e)',
        'schemeRefundList调试'
      )
      console.log(e, 'e')
      console.groupEnd()
    }
  }

  // 获取技术等级
  async queryTechnologyLevel(id: string) {
    const res = await QueryTechnologyLevel.query()
    let name = ''
    QueryTechnologyLevel.data.forEach(item => {
      if (parseInt(id) == item.code) {
        name = item.showName
      }
    })
    return name
  }

  /**
   * 获取登录强制修改密码配置
   */
  async getLoginForceModifyPassword(): Promise<boolean> {
    const res = await ServicerSeriesV1Gateway.getForceModifyInitialPassword()
    if (res.status.isSuccess()) {
      if (res.data?.enabledModifyInitPassword) {
        return res.data.enabledModifyInitPassword
      } else {
        return false
      }
    }
    return false
  }

  /**
   * 判断当前用户是否为新用户（是否需要修改密码）
   */
  async judgeCurrentUserPasswordUpdateForce() {
    const res = await DataGateway.judgeCurrentUserPasswordUpdateForce()
    if (res.status.isSuccess()) {
      return res.data === 1 ? true : false
    }
  }

  /**
   * 校验当前用户单位是否与对应专题单位一致
   * @param trainingChannelId 专题Id
   */
  async judgeTrainingChannelUnitConsistent(trainingChannelId: string) {
    const res = await PlatformTrainingChannel.compareTrainingChannelUnitWithStudentUnit({
      userId: this.userInfo.userInfo?.userId,
      trainingChannelId: trainingChannelId
    })

    if (res?.status && res.status.isSuccess()) {
      return !!res?.data
    } else {
      return false
    }
  }
}

export default new QueryUserInfo()
