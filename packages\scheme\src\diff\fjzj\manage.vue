<route-meta>
{
"isMenu": true,
"title": "培训方案管理",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <manage-diff></manage-diff>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import SchemeManage from '@hbfe/jxjy-admin-scheme/src/manage.vue'
  import DiffQueryTrainClassCommodityList from '@api/service/diff/management/fjzj/train-class/QueryTrainClassCommodityList'
  import { CommoditySkuSortRequest } from '@api/diff-gateway/fjzj-data-export-gateway-backstage'

  @Component
  class ManageDiff extends SchemeManage {
    // 差异化接口
    DiffQueryTrainClassCommodityList = new DiffQueryTrainClassCommodityList()

    sortPolicy: Array<CommoditySkuSortRequest> = new Array<CommoditySkuSortRequest>()

    async doExportScheme() {
      return await this.DiffQueryTrainClassCommodityList.exportCommoditySkuInServicer(
        this.trainSchemeQueryParam,
        this.sortPolicy
      )
    }
  }

  @Component({
    components: {
      ManageDiff
    }
  })
  export default class extends Vue {}
</script>
