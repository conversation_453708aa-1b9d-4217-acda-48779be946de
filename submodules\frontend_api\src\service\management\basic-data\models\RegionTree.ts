import { BaseRegionTreeDTO } from '@api/gateway/PlatformBasicData'
import { RegionInfo } from '@api/service/management/basic-data/models/RegionInfo'

export class RegionTree extends RegionInfo {
  children: Array<RegionTree>

  static from(dto: BaseRegionTreeDTO): RegionTree {
    const tree = new RegionTree()
    tree.id = dto.id
    tree.name = dto.name
    tree.parentId = dto.parentId
    tree.regionPath = dto.regionPath
    tree.sort = dto.sort
    tree.version = dto.version
    tree.available = dto.available
    if (dto.children && dto.children.length > 0) {
      tree.children = new Array<RegionTree>()
      dto.children.forEach(child => tree.children.push(RegionTree.from(child)))
    }
    return tree
  }

  toRegionInfo(): RegionInfo {
    const region = new RegionInfo()
    region.id = this.id
    region.name = this.name
    region.parentId = this.parentId
    region.regionPath = this.regionPath
    region.sort = this.sort
    region.version = this.version
    region.available = this.available
    return region
  }
}
