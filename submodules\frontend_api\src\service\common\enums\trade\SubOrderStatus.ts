/**
 * 子订单状态
 * 1 - 待付款
 * 2 - 未发货
 * 3 - 发货中
 * 4 - 已发货
 * 5 - 买家已签收
 * 6 - 已换货
 * 7 - 退货中
 * 8 - 已退货
 * 9 - 已取消
 */
import AbstractEnum from '../AbstractEnum'

export enum SubOrderStatusEnum {
  // 待付款
  wait_pay = 1,
  // 未发货
  not_deliver = 2,
  // 发货中
  delivering = 3,
  // 已发货
  delivered = 4,
  // 买家已签收
  sign_received = 5,
  // 换货成功
  exchanged_success = 6,
  // 退款中
  refunding = 7,
  // 退货成功
  return_success = 8,
  // 已取消
  canceled = 9,
  // 发货失败
  deliver_fail = 10,
  // 退货并退款成功
  return_refund_success = 11,
  // 退货失败
  return_fail = 12,
  // 退货中
  returning = 13
}

class SubOrderStatus extends AbstractEnum<SubOrderStatusEnum> {
  constructor() {
    super()
    this.map[SubOrderStatusEnum.wait_pay] = '待付款'
    this.map[SubOrderStatusEnum.not_deliver] = '未发货'
    this.map[SubOrderStatusEnum.delivering] = '发货中'
    this.map[SubOrderStatusEnum.delivered] = '已发货'
    this.map[SubOrderStatusEnum.sign_received] = '买家已签收'
    this.map[SubOrderStatusEnum.exchanged_success] = '换货成功'
    this.map[SubOrderStatusEnum.refunding] = '退款中'
    this.map[SubOrderStatusEnum.return_success] = '退货成功'
    this.map[SubOrderStatusEnum.canceled] = '已取消'
    this.map[SubOrderStatusEnum.deliver_fail] = '发货失败'
    this.map[SubOrderStatusEnum.return_refund_success] = '退货并退款成功'
  }
}

export default SubOrderStatus
