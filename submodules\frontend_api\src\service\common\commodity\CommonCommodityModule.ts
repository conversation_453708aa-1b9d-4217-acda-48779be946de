import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import PlatformCommodity, {
  LazyCommodityDTO,
  LazyCommodityDTOPage,
  LazyCommodityQueryParamDTO,
  LazyCommodityWithAppraiseDTO,
  LazyCommodityWithAppraiseQueryParamDTO,
  Page,
  SkuUsedQueryParamDTO,
  TrainingInstitutionBaseInfoDTO,
  WorkTypeCategoryWhichUsed
} from '@api/gateway/PlatformCommodity'
import Response, { ResponseStatus } from '@api/Response'
import Vue from 'vue'
import { cloneDeep } from 'lodash'
import PlatformLearningSchemeGateWay from '@api/gateway/PlatformLearningScheme'
import Constant from '@api/service/common/models/constant/index'
import { UnAuthorize } from '@api/Secure'
import CommonModule from '@api/service/common/common/CommonModule'
import StateKey from '@api/service/common/models/common/enums/StateKey'
import ServicerModule from '@api/service/common/servicer-query/ServicerModule'

class CommodityPage {
  list: Array<LazyCommodityDTO>
  totalSize: number
}

class UnitCommodityNumber {
  unitId: string
  commodityNumber: number
}

// 商品模块数据状态
interface CommonCommodityState {
  // 商品总数
  commodityNumber: number
  // 每个单位的商品总数缓存
  unitCommodityNumber: Array<UnitCommodityNumber>
  // 同类班级列表
  sameWorkTypeCommodityList: {
    [key: string]: Array<LazyCommodityDTO>
  }
  // 详情：调用详情口与分页口会把信息都存在这里面管理，当调用get by id时走这里
  commodityDetailList: Array<LazyCommodityDTO>
  // 带mark的请求结果管理
  mapList: { [key: string]: CommodityPage }
  // 已被引用的工种类别与工种树
  workTypeCategoryWhichUsedList: Array<WorkTypeCategoryWhichUsed>
  // 已有发布商品的单位
  unitList: Array<TrainingInstitutionBaseInfoDTO>
  // sku是否已加载 || workTypeCategoryWhichUsedList与unitList是否已加载
  skuWhichUsedLoad: boolean
  commodityWithAppraiseList: Array<LazyCommodityWithAppraiseDTO>
  // 商品数量【机构】
  commodityNumberForTrainingInstitution: number
}

@Module({ namespaced: true, dynamic: true, name: 'CommonCommodityModule', store })
class CommonCommodityModule extends VuexModule implements CommonCommodityState {
  commodityNumber = 0
  unitCommodityNumber: Array<UnitCommodityNumber> = new Array<UnitCommodityNumber>()
  sameWorkTypeCommodityList: { [key: string]: Array<LazyCommodityDTO> } = {}
  commodityDetailList: Array<LazyCommodityDTO> = new Array<LazyCommodityDTO>()
  mapList: { [key: string]: CommodityPage } = {}
  workTypeCategoryWhichUsedList: Array<WorkTypeCategoryWhichUsed> = new Array<WorkTypeCategoryWhichUsed>()
  unitList: Array<TrainingInstitutionBaseInfoDTO> = new Array<TrainingInstitutionBaseInfoDTO>()
  suitableTarget: Array<string> = new Array<string>()
  skuWhichUsedLoad = false
  commodityWithAppraiseList: Array<LazyCommodityWithAppraiseDTO> = new Array<LazyCommodityWithAppraiseDTO>()
  commodityNumberForTrainingInstitution = 0

  @Mutation
  private SET_LAZY_COMMODITY_LIST_APPEND(param: { mark: string; list: Array<LazyCommodityDTO>; totalSize: number }) {
    Vue.set(this.mapList, param.mark, { list: param.list, totalSize: param.totalSize })
  }

  @Mutation
  private PUSH_LAZY_COMMODITY_LIST_APPEND(param: { mark: string; list: Array<LazyCommodityDTO>; totalSize: number }) {
    if (this.mapList[param.mark]?.list) {
      this.mapList[param.mark].list.push(...param.list)
    } else {
      this.mapList[param.mark] = new CommodityPage()
      this.mapList[param.mark].list = param.list
    }
    Vue.set(this.mapList, param.mark, {
      list: this.mapList[param.mark].list,
      totalSize: this.mapList[param.mark].list?.length
    })
  }

  @Mutation
  CLEAR_LAZY_COMMODITY_LIS(mark: string) {
    Vue.delete(this.mapList, mark)
  }

  @Mutation
  private SET_LAZY_COMMODITY_NUMBER(commodityNumber: number) {
    this.commodityNumber = commodityNumber
  }

  @Mutation
  private SET_SAME_WORK_TYPE_COMMODITY_LIST(params: {
    workTypeId: string
    trainingInstitutionId: string
    sameWorkTypeCommodityList: Array<LazyCommodityDTO>
  }) {
    const key = params.trainingInstitutionId + '-' + params.workTypeId
    this.sameWorkTypeCommodityList[key] = params.sameWorkTypeCommodityList
  }

  @Mutation
  private PUSH_COMMODITY_DETAIL_LIST(details: Array<LazyCommodityDTO>) {
    details?.map(d => {
      const findIndex = this.commodityDetailList.findIndex(c => c.commodityId === d.commodityId)
      if (findIndex === -1) {
        this.commodityDetailList.push(d)
      } else {
        // lodash.remove(this.commodityDetailList, (item: LazyCommodityDTO) => {
        //   return item.commodityId === d.commodityId
        // })
        this.commodityDetailList.splice(findIndex, 1)
        this.commodityDetailList.push(d)
      }
    })
  }

  /**
   * 机构主页的课程列表 主要解决当课程未评价时 默认赋值五颗星并排序以供前端展示
   * @param params
   * @constructor
   */
  @Mutation
  private PUSH_COMMODITY_WITH_APPRAISE_LIST(params: {
    list: Array<LazyCommodityWithAppraiseDTO>
    commodityState: string
  }) {
    this.commodityWithAppraiseList = cloneDeep(params.list)
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const that = this

    that.commodityWithAppraiseList?.forEach(item => {
      if (item.comprehensiveAppraise === 0) {
        item.comprehensiveAppraise = 10
      }
    })
    if (params.commodityState === 'ASC') {
      that.commodityWithAppraiseList.sort((a, b) => a.comprehensiveAppraise - b.comprehensiveAppraise)
    } else {
      that.commodityWithAppraiseList.sort((a, b) => b.comprehensiveAppraise - a.comprehensiveAppraise)
    }
    that.commodityWithAppraiseList.push({} as LazyCommodityWithAppraiseDTO)
    that.commodityWithAppraiseList.pop()
    return that.commodityWithAppraiseList
  }

  @Mutation
  private SET_WORK_TYPE_CATEGORY_WHICH_USED(workTypeCategoryWhichUsedList: Array<WorkTypeCategoryWhichUsed>) {
    // 根据需求筛选优先展示的sku
    const firstList = new Array<WorkTypeCategoryWhichUsed>()
    const otherList = new Array<WorkTypeCategoryWhichUsed>()
    workTypeCategoryWhichUsedList = workTypeCategoryWhichUsedList.sort((a, b) => {
      return a.sort - b.sort
    })
    workTypeCategoryWhichUsedList.forEach(w => {
      if (
        w.id === CommonModule.KNOWLEDGE_WORK_TYPE_ID ||
        w.id === CommonModule.OPERATE_WORK_TYPE_ID ||
        w.id === CommonModule.URGENT_NEED_WORK_TYPE_ID
      ) {
        firstList.push(w)
      } else {
        otherList.push(w)
      }
    })
    firstList.push(...otherList)
    this.workTypeCategoryWhichUsedList = firstList
  }

  @Mutation
  private SET_UNIT_WHICH_USED(unitList: Array<TrainingInstitutionBaseInfoDTO>) {
    this.unitList = unitList
  }

  @Mutation
  SET_SUITABLE_TARGET(suitableList: Array<string>) {
    // 根据需求筛选优先展示的sku
    const firstList = new Array<string>()
    const otherList = new Array<string>()
    suitableList.forEach(s => {
      if (s === CommonModule.SUITABLE_ALL) {
        firstList.push(s)
      } else {
        otherList.push(s)
      }
    })
    firstList.push(...otherList)
    this.suitableTarget = firstList
  }

  @Mutation
  private SET_SKU_LOAD(reload: boolean) {
    this.skuWhichUsedLoad = reload
  }

  @Mutation
  private PUSH_UNIT_COMMODITY_NUMBER(param: { unitId: string; num: number }) {
    if (!this.unitCommodityNumber.find(u => u.unitId === param.unitId)) {
      this.unitCommodityNumber.push({
        unitId: param.unitId,
        commodityNumber: param.num
      })
    }
  }

  @Mutation
  SET_COUNT_COMMODITY_FOR_TRAINING_INSTITUTION(count: number) {
    this.commodityNumberForTrainingInstitution = count
  }

  /**
   * 获取当前状态所有数据
   */
  get getAppendLazyCommodityPageList() {
    return (mark: string) => {
      return this.mapList[mark]?.list
    }
  }

  /**
   * 获取分页总条数
   */
  get getLazyCommodityPageTotalSize() {
    return (mark: string) => {
      return this.mapList[mark].totalSize
    }
  }

  /**
   * 获取商品总数
   */
  get getLazyCommodityNumber() {
    return () => {
      return this.commodityNumber
    }
  }

  /**
   * 根据商品id获取商品信息
   */
  get getCommodityInfoById() {
    return (commodityId: string) => {
      return this.commodityDetailList.find(c => c.commodityId === commodityId) || new LazyCommodityDTO()
    }
  }

  /**
   * 根据方案id获取商品信息
   */
  get getCommodityInfoBySchemeId() {
    return (schemeId: string) => {
      return this.commodityDetailList.find(c => c.schemeId === schemeId) || new LazyCommodityDTO()
    }
  }

  /**
   * 根据期数id获取商品信息
   */
  get getCommodityInfoByIssueId() {
    return (issueId: string) => {
      return this.commodityDetailList.find(c => c.issueId === issueId) || new LazyCommodityDTO()
    }
  }

  /**
   * 获取期数商品下是否配置了培训起止时间
   * @see CommonCommodityModule.getLazyCommodityById
   */
  get isCommodityIssueConfigStudyTime() {
    return (commodityId: string): boolean => {
      const detail = this.commodityDetailList.find(c => c.commodityId === commodityId)
      if (detail) {
        if (detail.trainingTimeEnd === null) {
          return false
        }
        return detail.trainingTimeEnd !== Constant.END_TIME_DEFAULT
      }
      return false
    }
  }

  /**
   * 获取指定工种下的同类商品列表
   */
  get getSameWorkTypeCommodityList() {
    return (params: { workTypeId: string; trainingInstitutionId: string }) => {
      const key = params.trainingInstitutionId + '-' + params.workTypeId
      return this.sameWorkTypeCommodityList[key] || undefined
    }
  }

  /**
   * 获取所有被引用的工种类别sku树
   */
  get getWorkTypeCategoryWhichUsedTree() {
    return this.workTypeCategoryWhichUsedList
  }

  /**
   * 获取所有被引用的单位列表
   */
  get getUnitWhichUsedList() {
    return this.unitList
  }

  /**
   * 获取单个单位商品数量
   */
  get getUnitCommodityNumber() {
    return (unitId: string) => {
      return this.unitCommodityNumber.find(u => u.unitId === unitId) || undefined
    }
  }

  /**
   * 查询延迟商品分页
   * @param params.mark 请求标记，需要根据该标记去getAppendLazyCommodityPageList方法取到请求结果
   * 默认请传值StateKey.DEFAULT,不传取不到后果自负昂
   * @return response
   */
  @Action
  @UnAuthorize
  async pageLazyCommodity(params: {
    mark?: string
    page: Page
    paramDTO: LazyCommodityQueryParamDTO
    append?: boolean
  }) {
    const mark = params.mark || StateKey.DEFAULT
    params.paramDTO.commodityAvailable = true
    if (typeof params.paramDTO.commodityState === 'undefined') {
      params.paramDTO.commodityState = 'UPED'
    }
    const response = await PlatformCommodity.pageLazyCommodity({
      page: params.page,
      paramDTO: params.paramDTO
    })
    const contentIds = response.data.currentPageData.map(d => d.commoditySellingPoint)
    const contentResponse = await PlatformLearningSchemeGateWay.getContentByIds(contentIds)
    if (contentResponse.status.isSuccess()) {
      response.data.currentPageData.forEach(d => {
        d.commoditySellingPoint = contentResponse.data.find(c => c.id === d.commoditySellingPoint)?.content
      })
    }
    if (params.append) {
      // 清空数组
      if (params.page.pageNo === 1) {
        this.CLEAR_LAZY_COMMODITY_LIS(mark)
      }
      this.PUSH_LAZY_COMMODITY_LIST_APPEND({
        mark: mark,
        list: response.data.currentPageData,
        totalSize: response.data.totalSize
      })
    } else {
      this.SET_LAZY_COMMODITY_LIST_APPEND({
        mark: mark,
        list: response.data.currentPageData,
        totalSize: response.data.totalSize
      })
    }
    this.PUSH_COMMODITY_DETAIL_LIST(response.data.currentPageData)
    return response
  }

  /**
   * 查询商品分页 【渠道商】
   * @param params
   * @returns
   */
  @Action
  async pageLazyCommodityForChannelVendor(params: { page: Page; paramDTO: LazyCommodityQueryParamDTO }) {
    return await this.pageLazyCommodityCommonFn({
      page: params.page,
      paramDTO: params.paramDTO,
      event: PlatformCommodity.pageLazyCommodityForChannelVendor
    })
  }

  /**
   * 查询商品分页 【课件供应商】
   * @param params
   * @returns
   */
  @Action
  async pageLazyCommodityForCoursewareSupplier(params: { page: Page; paramDTO: LazyCommodityQueryParamDTO }) {
    return await this.pageLazyCommodityCommonFn({
      page: params.page,
      paramDTO: params.paramDTO,
      event: PlatformCommodity.pageLazyCommodityForCoursewareSupplier
    })
  }

  /**
   * 查询商品分页 【培训机构】
   * @param params
   * @returns
   */
  @Action
  async pageLazyCommodityForTrainingInstitution(params: { page: Page; paramDTO: LazyCommodityQueryParamDTO }) {
    return await this.pageLazyCommodityCommonFn({
      page: params.page,
      paramDTO: params.paramDTO,
      event: PlatformCommodity.pageLazyCommodityForTrainingInstitution
    })
  }

  /**
   * 查询商品分页 【参训单位】
   * @param params
   * @returns
   */
  @Action
  async pageLazyCommodityForParticipatingUnit(params: { page: Page; paramDTO: LazyCommodityQueryParamDTO }) {
    return await this.pageLazyCommodityCommonFn({
      page: params.page,
      paramDTO: params.paramDTO,
      event: PlatformCommodity.pageLazyCommodityForParticipatingUnit
    })
  }

  /**
   *  商品分页通用封装函数函数
   * @param 传入的事件
   */
  @Action
  private async pageLazyCommodityCommonFn(params: {
    page: Page
    paramDTO: LazyCommodityQueryParamDTO
    event: (params: { page: Page; paramDTO: LazyCommodityQueryParamDTO }) => Promise<Response<LazyCommodityDTOPage>>
  }) {
    params.paramDTO.commodityAvailable = true
    if (typeof params.paramDTO.commodityState === 'undefined') {
      params.paramDTO.commodityState = 'UPED'
    }
    const response = await params.event({
      page: params.page,
      paramDTO: params.paramDTO
    })
    const contentIds = response.data.currentPageData.map(d => d.commoditySellingPoint)
    const contentResponse = await PlatformLearningSchemeGateWay.getContentByIds(contentIds)
    if (contentResponse.status.isSuccess()) {
      response.data.currentPageData.forEach(d => {
        d.commoditySellingPoint = contentResponse.data.find(c => c.id === d.commoditySellingPoint)?.content
      })
    }
    return response
  }

  /**
   * 查询带延迟的商品集合（带评价），以评价维度展开去查商品
   * @param params
   * @return Response: { status: ResponseStatus, data: Array<LazyCommodityWithAppraiseDTO> }
   */
  @Action
  async pageCommodityWithAppraise(params: {
    mark: string
    page: Page
    paramDTO: LazyCommodityWithAppraiseQueryParamDTO
  }) {
    params.paramDTO.commodityAvailable = true
    if (typeof params.paramDTO.commodityState === 'undefined') {
      params.paramDTO.commodityState = 'UPED'
    }
    const response = await PlatformCommodity.pageCommodityWithAppraise({ page: params.page, paramDTO: params.paramDTO })
    if (response.status.isSuccess()) {
      if (
        response.data.currentPageData != undefined &&
        response.data.currentPageData != null &&
        response.data.currentPageData.length > 0
      ) {
        const contentIds = response.data.currentPageData.map(d => d.commoditySellingPoint)
        const contentResponse = await PlatformLearningSchemeGateWay.getContentByIds(contentIds)
        if (contentResponse.status.isSuccess()) {
          response.data.currentPageData.forEach(d => {
            d.commoditySellingPoint = contentResponse.data.find(c => c.id === d.commoditySellingPoint)?.content
          })
        }
      }
      this.PUSH_COMMODITY_WITH_APPRAISE_LIST({
        list: response.data.currentPageData,
        commodityState: params.paramDTO.comprehensiveAppraiseSoft
      })
    }
    return response
  }

  /**
   * 获取商品详情
   * @param commodityId
   */
  @Action
  @UnAuthorize
  async getLazyCommodityById(commodityId: string): Promise<ResponseStatus> {
    const find = this.commodityDetailList.find(c => c.commodityId === commodityId)
    if (find) {
      return new ResponseStatus(200, '')
    }
    const response = await PlatformCommodity.getLazyCommodityById(commodityId)
    if (response.status.isSuccess()) {
      const res = await PlatformLearningSchemeGateWay.getContent(response.data.commoditySellingPoint)
      if (res || res.status.isSuccess()) {
        response.data.commoditySellingPoint = res.data
      } else {
        response.data.commoditySellingPoint = ''
      }
      this.PUSH_COMMODITY_DETAIL_LIST([response.data])
    }
    return response.status
  }

  /**
   * 根据同种id和请求条数获取商品集合（获取同类班级列表）
   * 优先同机构同工种 其次同机构
   * @param params
   */
  @Action
  @UnAuthorize
  async listSameWorkTypeCommodity(params: {
    size: number
    workTypeId: string
    trainingInstitutionId: string
    thisCommodityId: string
  }) {
    const key = params.trainingInstitutionId + '-' + params.workTypeId
    if (this.sameWorkTypeCommodityList[key] && this.sameWorkTypeCommodityList[key].length === params.size) {
      return new ResponseStatus(200, '')
    }
    const response = await PlatformCommodity.pageLazyCommodity({
      page: {
        pageNo: 1,
        pageSize: params.size
      },
      paramDTO: {
        workTypeId: params.workTypeId,
        trainingInstitutionIdList: params.trainingInstitutionId ? [params.trainingInstitutionId] : undefined,
        excludeCommodityIds: [params.thisCommodityId]
      }
    })
    if (response.status.isSuccess()) {
      if (response.data.totalSize === 0) {
        const res = await PlatformCommodity.pageLazyCommodity({
          page: {
            pageNo: 1,
            pageSize: params.size
          },
          paramDTO: {
            trainingInstitutionIdList: params.trainingInstitutionId ? [params.trainingInstitutionId] : undefined,
            excludeCommodityIds: [params.thisCommodityId]
          }
        })
        if (res.status.isSuccess()) {
          this.SET_SAME_WORK_TYPE_COMMODITY_LIST({
            workTypeId: params.workTypeId,
            trainingInstitutionId: params.trainingInstitutionId,
            sameWorkTypeCommodityList: res.data.currentPageData
          })
        }
        return res.status
      }

      this.SET_SAME_WORK_TYPE_COMMODITY_LIST({
        workTypeId: params.workTypeId,
        trainingInstitutionId: params.trainingInstitutionId,
        sameWorkTypeCommodityList: response.data.currentPageData
      })
    }
    return response.status
  }

  /**
   * 获取班级总数 【超管角色使用】
   * @param commodityState 指定一个商品状态，如果没指定则返回所有 UPED:上架|DOWNED下架
   * @return response
   */
  @Action
  @UnAuthorize
  async countCommodityNumber(param?: {
    commodityState?: string
    trainingInstitutionIdList?: string[]
    trainingInstitutionStatus?: boolean
  }) {
    const res = await PlatformCommodity.countCommodityNumber({
      commodityState: param?.commodityState ? param.commodityState : '',
      trainingInstitutionIdList: param?.trainingInstitutionIdList ? param?.trainingInstitutionIdList : undefined,
      trainingInstitutionStatus: param?.trainingInstitutionStatus || true
    })
    if (res.status.isSuccess()) {
      this.SET_LAZY_COMMODITY_NUMBER(res.data)
    }
    return res
  }

  /**
   * 获取某个单位的获取班级总数
   * @param param 指定一个单位
   * @return response
   */
  @Action
  @UnAuthorize
  async countUnitCommodityNumber(param: {
    commodityState?: string
    unitId: string
    trainingInstitutionStatus?: boolean
  }) {
    const find = this.unitCommodityNumber.find(u => u.unitId === param.unitId)
    if (find) {
      return {
        status: new ResponseStatus(200, ''),
        data: find.commodityNumber
      }
    }
    const res = await PlatformCommodity.countCommodityNumber({
      commodityState: param.commodityState ? param.commodityState : 'UPED',
      trainingInstitutionIdList: [param.unitId],
      trainingInstitutionStatus: param.trainingInstitutionStatus
    })
    if (res.status.isSuccess()) {
      this.PUSH_UNIT_COMMODITY_NUMBER({
        unitId: param.unitId,
        num: res.data
      })
    }
    return res
  }

  /**
   * 获取所有被使用的sku
   * @return Response.status
   */
  @Action
  @UnAuthorize
  async getSkuWhichUsedByCommodity() {
    if (this.skuWhichUsedLoad === true) {
      return new ResponseStatus(200, '')
    }
    const { status, data } = await PlatformCommodity.getSkuWhichUsedByCommodity('')
    if (status.isSuccess()) {
      const unitIds = data.trainingInstitutionList?.map(el => el.id)
      await ServicerModule.servicerListByIds(unitIds)

      data.trainingInstitutionList.forEach(u => {
        const unit = ServicerModule.servicerList.find(el => el.id === u.id)
        u.name = unit?.abouts || unit?.name
      })
      this.SET_WORK_TYPE_CATEGORY_WHICH_USED(data.workTypeCategoryWhichUsedList)
      this.SET_UNIT_WHICH_USED(data.trainingInstitutionList)
      this.SET_SUITABLE_TARGET(data.suitablePeopleList)
      this.SET_SKU_LOAD(true)
    }
    return status
  }
  @Action
  @UnAuthorize
  async getSkuWhichUsedByCommodityForTrainingInstitution(trainingInstitutionId: string) {
    if (this.skuWhichUsedLoad === true) {
      return new ResponseStatus(200, '')
    }
    const params = new SkuUsedQueryParamDTO()
    params.trainingInstitutionIdList = [trainingInstitutionId]
    const { status, data } = await PlatformCommodity.getSkuWhichUsedByCommodityQuery(params)
    if (status.isSuccess()) {
      const unitIds = data.trainingInstitutionList?.map(el => el.id)
      await ServicerModule.servicerListByIds(unitIds)

      data.trainingInstitutionList.forEach(u => {
        const unit = ServicerModule.servicerList.find(el => el.id === u.id)
        u.name = unit?.abouts || unit?.name
      })
      this.SET_WORK_TYPE_CATEGORY_WHICH_USED(data.workTypeCategoryWhichUsedList)
      this.SET_UNIT_WHICH_USED(data.trainingInstitutionList)
      this.SET_SUITABLE_TARGET(data.suitablePeopleList)
      this.SET_SKU_LOAD(true)
    }
    return status
  }
  @Action
  @UnAuthorize
  async getSkuWhichUsedByCommodityForChannelVendor(param: { trainingInstitutionId: string; channelVendorId: string }) {
    if (this.skuWhichUsedLoad === true) {
      return new ResponseStatus(200, '')
    }
    const params = new SkuUsedQueryParamDTO()
    params.trainingInstitutionIdList = [param.trainingInstitutionId]
    params.channelVendorIds = [param.channelVendorId]
    const { status, data } = await PlatformCommodity.getSkuWhichUsedByCommodityQuery(params)
    if (status.isSuccess()) {
      const unitIds = data.trainingInstitutionList?.map(el => el.id)
      await ServicerModule.servicerListByIds(unitIds)

      data.trainingInstitutionList.forEach(u => {
        const unit = ServicerModule.servicerList.find(el => el.id === u.id)
        u.name = unit?.abouts || unit?.name
      })
      this.SET_WORK_TYPE_CATEGORY_WHICH_USED(data.workTypeCategoryWhichUsedList)
      this.SET_UNIT_WHICH_USED(data.trainingInstitutionList)
      this.SET_SUITABLE_TARGET(data.suitablePeopleList)
      this.SET_SKU_LOAD(true)
    }
    return status
  }

  /**
   * 重新加载sku
   */
  @Action
  @UnAuthorize
  async reloadSkuWhichUsedByCommodity() {
    this.SET_SKU_LOAD(false)
    return this.getSkuWhichUsedByCommodity()
  }

  /**
   * 批量获取期数商品信息
   * @param issueIds
   */
  @Action
  async listIssueCommodityInfo(issueIds: Array<string>) {
    const ids = new Array<string>()
    issueIds.forEach(i => {
      if (!this.commodityDetailList.find(c => c.issueId === i)) {
        ids.push(i)
      }
    })
    if (ids.length === 0) {
      return new ResponseStatus(200, '')
    }
    const res = await PlatformCommodity.listIssueCommodityInfo(issueIds)
    if (res.status.isSuccess()) {
      this.PUSH_COMMODITY_DETAIL_LIST(res.data)
    }
    return res.status
  }

  /**
   * 获取当前渠道供应商班级总数 【渠道商】
   */
  @Action
  async countCommodityNumberForChannelVendor(param: LazyCommodityQueryParamDTO) {
    let count = 0
    await PlatformCommodity.countCommodityNumberForChannelVendor(param).then(res => {
      count = res.data
    })
    return count
  }

  /**
   * 获取当前课件供应商班级总数 【课件供应商】
   */
  @Action
  async countCommodityNumberForCoursewareSupplier(param: LazyCommodityQueryParamDTO) {
    let count = 0
    await PlatformCommodity.countCommodityNumberForCoursewareSupplier(param).then(res => {
      count = res.data
    })
    return count
  }

  /**
   * 获取当前机构班级总数 【机构】
   */
  @Action
  async countCommodityNumberForTrainingInstitution(param: LazyCommodityQueryParamDTO) {
    let count = 0
    await PlatformCommodity.countCommodityNumberForTrainingInstitution(param).then(res => {
      count = res.data
    })
    this.SET_COUNT_COMMODITY_FOR_TRAINING_INSTITUTION(count)
    return count
  }
}

export default getModule(CommonCommodityModule)
