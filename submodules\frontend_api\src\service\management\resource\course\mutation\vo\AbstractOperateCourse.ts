import Chapter from '@api/service/management/resource/course/mutation/vo/Chapter'

class AbstractOperateCourse {
  /**
   * 课程名称
   */
  name: string

  /**
   * 封面
   */
  picture: string

  /**
   * 分类id
   */
  categoryId: Array<string> | string = new Array<string>()
  /**
   * 课件供应商ID
   */
  supplierId: string = undefined
  /**
   * 课件供应商名称
   */
  supplierName: string = undefined

  /**
   * 课程简介
   */
  description: string

  /**
   * 章节
   */
  chapters: Array<Chapter> = new Array<Chapter>()

  addChapter() {
    const chapter = new Chapter()
    chapter.name = `章节 ${this.chapters.length + 1}`
    this.chapters.push(chapter)
  }

  removeChapter(index: number) {
    this.chapters.splice(index, 1)
  }
}

export default AbstractOperateCourse
