/*
 * @Author: WRP
 * @Date: 导出任务模块
 */
import store from '@api/store'
import { VuexModule, getModule, Module } from 'vuex-module-decorators'
import MutationAsyncTaskFactory from './MutationAsyncTaskFactory'
import QueryAsyncTaskFactory from './QueryAsyncTaskFactory'

@Module({ namespaced: true, name: 'AsyncTaskModule', dynamic: true, store })
class AsyncTaskModule extends VuexModule {
  /* 
    异步任务查询工厂  
  */
  get queryAsyncTaskFactory() {
    return QueryAsyncTaskFactory
  }

  /* 
    异步任务业务工厂
  */
  get mutationAsyncTaskFactory() {
    return MutationAsyncTaskFactory
  }
}

export default getModule(AsyncTaskModule)
