import moment from 'moment'
import { Constants } from '@api/service/common/models/common/Constants'

class WorkType {
  id: string
  name: string
  code: string
  sort: number
  creatorId: string
  createTime: Date

  parse(response: any) {
    this.id = response.id
    this.name = response.name
    this.code = response.code
    this.sort = response.sort
    this.creatorId = response.creatorId
    this.createTime = moment(response.createTime, Constants.DATE_PATTERN).toDate()
    return this
  }
}

export default WorkType
