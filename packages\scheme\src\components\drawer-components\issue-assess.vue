<template>
  <el-drawer
    title="查看培训期别考核内容"
    :visible.sync="showDrawer"
    size="900px"
    custom-class="m-drawer"
    @open="onOpen"
  >
    <div class="drawer-bd">
      <el-collapse v-model="activeNames" class="m-collapse-custom">
        <el-collapse-item
          v-for="item in issue.issueConfigList"
          :key="item.issueNo"
          :title="`${item.issueNo}  ${item.issueName}`"
          :name="item.issueNo"
        >
          <div class="u-bg-light-base f-p10">
            <p v-for="ite in assessed(item)" :key="ite">{{ ite }}</p>
          </div>
          <!--表格-->
          <el-table
            stripe
            v-if="tableQuestionnaireList(item).length"
            :data="tableQuestionnaireList(item)"
            max-height="500px"
            class="m-table f-mt10"
          >
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="问卷名称" min-width="200">
              <template v-slot="{ row }">{{ row.questionnaireName }}</template>
            </el-table-column>
            <el-table-column label="问卷开放时间" min-width="200" align="center">
              <template v-slot="{ row }">
                <template v-if="isAssignedDate(row)">
                  <p><el-tag type="info" size="mini">起始</el-tag>{{ row.openDateRange.startDate }}</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>{{ row.openDateRange.startDate }}</p>
                </template>
                <template v-else-if="isLongTerm">长期有效</template>
                <template v-else-if="hasTrainClassLearningTime">
                  <p><el-tag type="info" size="mini">起始</el-tag>{{ trainClassBaseInfo.trainingBeginDate }}</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>{{ trainClassBaseInfo.trainingEndDate }}</p>
                </template>
                <template v-else>—</template>
              </template>
            </el-table-column>
            <el-table-column label="问卷状态" min-width="100" align="center" fixed="right">
              <template v-slot="{ row }">{{ statusDesc(row) }}</template>
            </el-table-column>
            <el-table-column label="应用范围" min-width="120" align="center" fixed="right">
              <template v-slot="{ row }">{{ appliedRangeName(row) || item.issueName }}</template>
            </el-table-column>
            <el-table-column label="前置条件" min-width="120" align="center" fixed="right">
              <template v-slot="{ row }">{{ preconditionType(row.preconditionType) }}</template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Vue } from 'vue-property-decorator'
  import QuestionnaireLearningType from '@api/service/common/scheme/model/QuestionnaireLearningType'
  import IssueLearningType from '@api/service/common/scheme/model/IssueLearningType'
  import issueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import QuestionnaireAppliedRangeType, {
    QuestionnaireAppliedRangeTypeEnum
  } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'
  import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
  import { QuestionnaireOpenDateTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireOpenDateType'
  import SchemeBaseInfo from '@api/service/common/scheme/model/SchemeBaseInfo'
  import QuestionnairePreconditionType, {
    QuestionnairePreconditionTypeEnum
  } from '@api/service/common/scheme/enum/QuestionnairePreconditionType'
  import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
  import { QuestionnaireStatusEnum } from '@api/service/common/scheme/enum/QuestionnaireStatus'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  @Component
  export default class extends Vue {
    @PropSync('issueAssess', { type: Boolean, default: false }) showDrawer: boolean
    @Prop({ type: Object, default: () => new QuestionnaireLearningType() }) questionnaire: QuestionnaireLearningType
    @Prop({ type: Object, default: () => new IssueLearningType() }) issue: IssueLearningType
    @Prop({ type: Object, default: () => new SchemeBaseInfo() }) trainClassBaseInfo: SchemeBaseInfo
    activeNames: string[] = []
    /**
     * 期别相关问卷列表
     */
    get tableQuestionnaireList() {
      return (row: issueConfigDetail) => {
        return this.questionnaire.questionnaireConfigList.filter(
          (item) =>
            item.isAssessed &&
            (item.curIssueId === row.id ||
              [QuestionnaireAppliedRangeTypeEnum.per_issue].includes(item.appliedRangeType))
        )
      }
    }
    /**
     * 开发时间是否指定时间
     */
    get isAssignedDate() {
      return (row: QuestionnaireConfigDetail) => {
        return row.openDateType === QuestionnaireOpenDateTypeEnum.assign
      }
    }
    /**
     * 当前培训班是否长期有效
     */
    get isLongTerm() {
      return (
        this.trainClassBaseInfo.trainingBeginDate === CreateSchemeUtils.defaultBeginDate &&
        this.trainClassBaseInfo.trainingEndDate === CreateSchemeUtils.defaultEndDate
      )
    }
    /**
     * 当前培训班学习时间
     */
    get hasTrainClassLearningTime() {
      return this.trainClassBaseInfo.trainingBeginDate && this.trainClassBaseInfo.trainingEndDate
    }
    /**
     * 获取应用范围名称
     */
    get appliedRangeName() {
      return (item: QuestionnaireConfigDetail) => {
        if (item.appliedRangeType === QuestionnaireAppliedRangeTypeEnum.assign_issue) return
        return QuestionnaireAppliedRangeType.map.get(item.appliedRangeType)
      }
    }

    /**
     * 获取前置条件类型
     */
    get preconditionType() {
      return (item: QuestionnairePreconditionTypeEnum) => {
        return QuestionnairePreconditionType.map.get(item)
      }
    }

    /**
     * 考考核展示
     */
    get assessed() {
      return (item: issueConfigDetail) => {
        const assessedStr: string[] = []
        const noList = ['①', '②', '③']
        if (item.isOpenAttendance) {
          assessedStr.push(`${noList.shift()}考勤考核要求考勤率 ≥ ${item.attendanceRate}%`)
        }
        if (item.isOpenGraduationTest) {
          assessedStr.push(`${noList.shift()}需完成结业测试`)
        }
        if (this.tableQuestionnaireList(item).length) {
          assessedStr.push(`${noList.shift()}需完成调研问卷`)
        }
        return assessedStr
      }
    }
    get statusDesc() {
      return (row: QuestionnaireConfigDetail) => {
        if (row.operationType !== OperationTypeEnum.update) return '—'
        else if (row.status == QuestionnaireStatusEnum.enabled) return '启用'
        else return '停用'
      }
    }
    onOpen() {
      !this.activeNames.length &&
        !!this.issue.issueConfigList.length &&
        this.activeNames.push(this.issue.issueConfigList[0].issueNo)
    }
  }
</script>
