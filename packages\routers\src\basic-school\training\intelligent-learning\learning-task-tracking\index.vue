<route-meta>
{
"isMenu": true,
"title": "智能学习任务跟踪",
"sort": 2
}
</route-meta>

<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  import TaskTrack from '@hbfe/jxjy-admin-intelligentLearning/src/learning-task-tracking/index.vue'
  @RoleTypeDecorator({
    query: [WXGLY],
    arrangement: [WXGLY],
    execution: [WXGLY],
    importTask: [WXGLY],
    reset: [WXGLY],
    logArrangement: [WXGLY],
    logDetail: [WXGLY],
    logExecution: [WXGLY],
    arrangementDetail: [WXGLY],
    executionDetail: [WXGLY],
    ZNXXFB: [WXGLY],
    terminateTask: [WXGLY],
    terminateTaskList: [WXGLY]
  })
  export default class extends TaskTrack {}
</script>
