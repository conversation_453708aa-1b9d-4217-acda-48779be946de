<template>
  <el-container>
    <el-aside width="240px">
      <!--侧边栏按钮 展开时显示-->
      <a href="#" class="aside-btn el-icon-s-fold"></a>
      <!--侧边栏按钮 收起时显示-->
      <!--<a href="#" class="aside-btn el-icon-s-unfold"></a>-->
      <div class="logo">
        <span class="logo-txt">通用平台运营管理后台</span>
      </div>
      <div class="user-info">
        <img class="photo" src="./assets/images/default-photo.jpg" alt=" " />
        <p class="name">张某某</p>
        <div class="op-btn f-mt10">
          <el-button type="primary" size="mini" round plain>
            <i class="el-icon-s-tools"></i>
            帐号设置
          </el-button>
        </div>
      </div>
      <el-menu default-active="0" class="aside-nav" unique-opened="true" @open="handleOpen" @close="handleClose">
        <!--帐号设置-->
        <el-menu-item index="0">
          <template slot="title">
            <i class="hb-iconfont icon-setup"></i>
            <span>帐号设置</span>
          </template>
        </el-menu-item>
      </el-menu>
      <div class="m-company-info">
        <p>福建华博教育科技股份有限公司</p>
        <a class="a-txt" href="http://beian.miit.gov.cn/" target="_blank">闽ICP备2021002737号</a>
      </div>
    </el-aside>
    <el-container>
      <el-header height="100px" class="f-flex">
        <ul class="header-nav f-flex-sub">
          <li class="nav-item current">
            <i class="iconfont icon-weiwangxiao"></i>
            <span class="txt">网校管理</span>
          </li>
          <li class="current-bg" style="min-width: 124px;"></li>
        </ul>
        <ul class="header-nav">
          <li class="nav-item nav-item-1"><i class="iconfont icon-tuichu"></i>退出</li>
        </ul>
      </el-header>
      <div class="tags">
        <span class="prev"><i class="el-icon el-icon-arrow-left"></i></span>
        <div class="tags-bd">
          <div class="tags-items" style="width: 500%;">
            <el-tag class="current" closable>帐号设置</el-tag>
          </div>
        </div>
        <span class="next"><i class="el-icon el-icon-arrow-right"></i></span>
        <el-dropdown :hide-on-click="false">
          <span class="more"><i class="el-icon el-icon-more"></i></span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>黄金糕</el-dropdown-item>
            <el-dropdown-item>狮子头</el-dropdown-item>
            <el-dropdown-item>螺蛳粉</el-dropdown-item>
            <el-dropdown-item disabled>双皮奶</el-dropdown-item>
            <el-dropdown-item divided>蚵仔煎</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <el-main>
        <div class="f-p15">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">帐号信息</span>
            </div>
            <div class="f-p20">
              <el-row type="flex" justify="center" class="width-limit">
                <el-col :md="20" :lg="16" :xl="13">
                  <el-form ref="form" :model="form" label-width="140px" class="m-form f-pt20">
                    <el-form-item label="姓名：" class="is-text">
                      <span class="f-mr15">林林一</span>
                      <a href="#" class="f-link f-cb f-underline">修改</a>
                    </el-form-item>
                    <el-form-item label="登录帐号：" class="is-text">
                      linlin001
                    </el-form-item>
                    <el-form-item label="手机号：" class="is-text">
                      <span class="f-mr15">1588547658</span>
                      <a href="#" class="f-link f-cb f-underline">换绑手机</a>
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">重置密码</span>
            </div>
            <div class="f-p20">
              <el-row type="flex" justify="center" class="width-limit">
                <el-col :md="20" :lg="16" :xl="13">
                  <el-form ref="form" :model="form" label-width="140px" class="m-form f-pt20">
                    <el-form-item label="旧密码：" required>
                      <el-input v-model="form.name" clearable show-password placeholder="请输入旧密码" class="form-m" />
                    </el-form-item>
                    <el-form-item label="新密码：" required>
                      <div class="form-m">
                        <el-input clearable show-password placeholder="请输入6-18位由字母、数字或符号组合的密码" />
                        <!--密码安全判断-->
                        <div class="psw-tips">
                          <el-progress :percentage="33.33" color="#e93737" :show-text="false"></el-progress>
                          <!--弱：txt-l，中：txt-m，强：txt-h-->
                          <span class="txt txt-l">弱</span>
                        </div>
                        <div class="psw-tips">
                          <el-progress :percentage="66.66" color="#ee9e2d" :show-text="false"></el-progress>
                          <!--弱：txt-l，中：txt-m，强：txt-h-->
                          <span class="txt txt-m">中</span>
                        </div>
                        <div class="psw-tips">
                          <el-progress :percentage="100" color="#49b042" :show-text="false"></el-progress>
                          <!--弱：txt-l，中：txt-m，强：txt-h-->
                          <span class="txt txt-h">强</span>
                        </div>
                      </div>
                    </el-form-item>
                    <el-form-item label="确认密码：" required>
                      <el-input
                        v-model="form.name"
                        clearable
                        show-password
                        placeholder="请再次输入密码"
                        class="form-m"
                      />
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </div>
          </el-card>
          <div class="m-btn-bar f-tc is-sticky-1">
            <el-button>取消</el-button>
            <el-button type="primary">保存</el-button>
          </div>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        activeNames: ['3'],
        props: { multiple: true },
        radio: 3,
        radio1: '1',
        input: '',
        select: '',
        checked: true,
        checked2: false,
        checked3: false,
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        visible: false,
        fits: ['cover'],
        options: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'zujian',
            label: '组件',
            children: [
              {
                value: 'basic',
                label: 'Basic',
                children: [
                  {
                    value: 'layout',
                    label: 'Layout 布局'
                  },
                  {
                    value: 'color',
                    label: 'Color 色彩'
                  },
                  {
                    value: 'typography',
                    label: 'Typography 字体'
                  },
                  {
                    value: 'icon',
                    label: 'Icon 图标'
                  },
                  {
                    value: 'button',
                    label: 'Button 按钮'
                  }
                ]
              },
              {
                value: 'form',
                label: 'Form',
                children: [
                  {
                    value: 'radio',
                    label: 'Radio 单选框'
                  },
                  {
                    value: 'checkbox',
                    label: 'Checkbox 多选框'
                  },
                  {
                    value: 'input',
                    label: 'Input 输入框'
                  },
                  {
                    value: 'input-number',
                    label: 'InputNumber 计数器'
                  },
                  {
                    value: 'select',
                    label: 'Select 选择器'
                  },
                  {
                    value: 'cascader',
                    label: 'Cascader 级联选择器'
                  },
                  {
                    value: 'switch',
                    label: 'Switch 开关'
                  },
                  {
                    value: 'slider',
                    label: 'Slider 滑块'
                  },
                  {
                    value: 'time-picker',
                    label: 'TimePicker 时间选择器'
                  },
                  {
                    value: 'date-picker',
                    label: 'DatePicker 日期选择器'
                  },
                  {
                    value: 'datetime-picker',
                    label: 'DateTimePicker 日期时间选择器'
                  },
                  {
                    value: 'upload',
                    label: 'Upload 上传'
                  },
                  {
                    value: 'rate',
                    label: 'Rate 评分'
                  },
                  {
                    value: 'form',
                    label: 'Form 表单'
                  }
                ]
              },
              {
                value: 'data',
                label: 'Data',
                children: [
                  {
                    value: 'table',
                    label: 'Table 表格'
                  },
                  {
                    value: 'tag',
                    label: 'Tag 标签'
                  },
                  {
                    value: 'progress',
                    label: 'Progress 进度条'
                  },
                  {
                    value: 'tree',
                    label: 'Tree 树形控件'
                  },
                  {
                    value: 'pagination',
                    label: 'Pagination 分页'
                  },
                  {
                    value: 'badge',
                    label: 'Badge 标记'
                  }
                ]
              },
              {
                value: 'notice',
                label: 'Notice',
                children: [
                  {
                    value: 'alert',
                    label: 'Alert 警告'
                  },
                  {
                    value: 'loading',
                    label: 'Loading 加载'
                  },
                  {
                    value: 'message',
                    label: 'Message 消息提示'
                  },
                  {
                    value: 'message-box',
                    label: 'MessageBox 弹框'
                  },
                  {
                    value: 'notification',
                    label: 'Notification 通知'
                  }
                ]
              },
              {
                value: 'navigation',
                label: 'Navigation',
                children: [
                  {
                    value: 'menu',
                    label: 'NavMenu 导航菜单'
                  },
                  {
                    value: 'tabs',
                    label: 'Tabs 标签页'
                  },
                  {
                    value: 'breadcrumb',
                    label: 'Breadcrumb 面包屑'
                  },
                  {
                    value: 'dropdown',
                    label: 'Dropdown 下拉菜单'
                  },
                  {
                    value: 'steps',
                    label: 'Steps 步骤条'
                  }
                ]
              },
              {
                value: 'others',
                label: 'Others',
                children: [
                  {
                    value: 'dialog',
                    label: 'Dialog 对话框'
                  },
                  {
                    value: 'tooltip',
                    label: 'Tooltip 文字提示'
                  },
                  {
                    value: 'popover',
                    label: 'Popover 弹出框'
                  },
                  {
                    value: 'card',
                    label: 'Card 卡片'
                  },
                  {
                    value: 'carousel',
                    label: 'Carousel 走马灯'
                  },
                  {
                    value: 'collapse',
                    label: 'Collapse 折叠面板'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ]
      }
    },
    methods: {
      onPreview() {
        this.$refs.preview.clickHandler()
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
