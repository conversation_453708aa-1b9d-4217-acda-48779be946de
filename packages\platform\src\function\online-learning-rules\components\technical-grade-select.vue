<template>
  <el-select v-model="selectValue" :placeholder="placeholder" filterable clearable class="form-l" multiple>
    <el-option
      v-for="item in technologyLevelOptions"
      :label="item.name"
      :value="item.propertyId"
      :key="item.propertyId"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Mixins } from 'vue-property-decorator'
  import QueryJobLevel from '@api/service/common/basic-data-dictionary/query/QueryJobLevel'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
  import CommonSkuMixins from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSkuMixins'
  @Component
  export default class extends Mixins(CommonSkuMixins) {
    technologyLevelOptions: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()

    async created() {
      this.technologyLevelOptions = await QueryJobLevel.QueryJobLevelByIndustry(
        this.industryId,
        this.industryPropertyId
      )
      const param = new TrainingPropertyResponse()
      param.propertyId = '-1'
      param.name = '全部'
      param.sort = 0
      param.showName = ''
      this.technologyLevelOptions.unshift(param)
    }
  }
</script>
