import { ResponseStatus } from '@hbfe/common'
import CourseResource, {
  ExtensionResourceData,
  ExtensionResourceDataDto,
  GeneralMutationResponse
} from '@api/ms-gateway/ms-course-resource-v1'
import UpdateCoursewareVo from '@api/service/management/resource/courseware/mutation/vo/UpdateCoursewareVo'
import CoursewareDetail from '@api/service/management/resource/courseware/query/vo/CoursewareDetail'

class MutationUpdateCourseware {
  request: UpdateCoursewareVo = new UpdateCoursewareVo()

  async doUpdate(): Promise<GeneralMutationResponse> {
    const res = await CourseResource.updateCourseware(this.request.from(this.request))
    return res?.data || new GeneralMutationResponse()
  }

  from(dto: CoursewareDetail) {
    this.request.id = dto.id
    this.request.categoryIds = dto.categoryIdList.length ? dto.categoryIdList : [dto.categoryId]
    this.request.name = dto.name
    this.request.supplierId = dto.providerId
    this.request.teacherId = dto.teachers[0]?.id || ''
    this.request.teacherName = dto.teachers[0]?.name || ''
    this.request.teacherAboutsContent = dto.teachers[0]?.description || ''
    this.request.aboutsContent = dto.description
    this.request.enable = dto.enable
    this.request.isBeingUsed = dto.isBeingUsed
    this.request.isOuter = dto.isOuter
    this.request.type = dto.type
    this.request.resourceDataDto.timeLength = dto.timeLength
    this.request.status = dto.status
    if (dto.isOuter) {
      const extensionResourceDataDto = new ExtensionResourceDataDto()
      extensionResourceDataDto.extensionVideoInfo = new ExtensionResourceData()
      extensionResourceDataDto.extensionVideoInfo.videoInfoDtos = []
      if (dto.standardAddress) {
        extensionResourceDataDto.extensionVideoInfo.videoInfoDtos.push({ path: dto.standardAddress, clarity: 9 })
        this.request.standardAddress = dto.standardAddress
      }
      if (dto.highAddress) {
        extensionResourceDataDto.extensionVideoInfo.videoInfoDtos.push({ path: dto.highAddress, clarity: 10 })
        this.request.highAddress = dto.highAddress
      }
      if (dto.superAddress) {
        extensionResourceDataDto.extensionVideoInfo.videoInfoDtos.push({ path: dto.superAddress, clarity: 11 })
        this.request.superAddress = dto.superAddress
      }
      try {
        const json = JSON.stringify(extensionResourceDataDto)
        this.request.configJSON = json || ''
      } catch (error) {
        console.error(error, '详情json')
      }
    }

    return this.request
  }
}

export default MutationUpdateCourseware
