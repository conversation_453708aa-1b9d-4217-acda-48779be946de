<template>
  <el-dialog :visible.sync="isShow" width="400px" :show-close="false" :close-on-click-modal="false" class="m-dialog">
    <div class="dialog-alert is-big">
      <i class="icon el-icon-warning warning"></i>
      <div class="txt">
        <p class="f-fb f-f16">{{ content }}</p>
      </div>
    </div>
    <div slot="footer">
      <el-button @click="isShowDialog">取 消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts">
  import { Component, Vue, Prop } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    @Prop({ type: String, default: '' }) content: string
    isShow = false
    type = 0
    isShowDialog() {
      this.isShow = !this.isShow
    }
    confirm() {
      if (this.type == 1) {
        this.$emit('disable')
      }
      if (this.type == 2) {
        this.$emit('enable')
      }
      if (this.type == 3) {
        this.$emit('detele')
      }
    }
  }
</script>
