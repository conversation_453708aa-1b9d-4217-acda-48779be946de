import { Response, ResponseStatus } from '@hbfe/common'
import DeleteCourseVo from '@api/service/management/train-class/mutation/vo/DeleteCourseVo'
import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
import MsChooseCourseLearningScene from '@api/ms-gateway/ms-choosecourselearningscene-v1'
import MsAutonomousCourseLearningScene from '@api/ms-gateway/ms-autonomouscourselearningscene-v1'
import MsLearningScheme, { ApplyReLearnTokenRequest } from '@api/ms-gateway/ms-learningscheme-v1'

/**
 * @description
 */
class MutationDeleteCourse {
  /**
   * 删除参数
   */
  deleteParams: DeleteCourseVo = new DeleteCourseVo()

  /**
   * 删除学员课程
   */
  async doDeleteStudentCourse(): Promise<ResponseStatus> {
    let response1: Response<void>
    // 1. 获取重学token
    const request = new ApplyReLearnTokenRequest()
    request.learningId = this.deleteParams.learningId
    request.qualificationId = this.deleteParams.qualificationId
    const tokenRes = await MsLearningScheme.applyRelearnTokenForManage(request)
    if (!tokenRes.status?.isSuccess()) {
      return tokenRes.status
    }
    const token = tokenRes.data?.token
    if (!token) return new ResponseStatus(500, '操作失败')
    // 2. 删除学员课程
    if (this.deleteParams.schemeType === TrainClassSchemeEnum.Autonomous_Course_Learning) {
      /** 自主选课*/
      response1 = await MsAutonomousCourseLearningScene.invalidStudentCourse({
        studentReLearnToken: token,
        studentCourseId: this.deleteParams.studentCourseId
      })
    } else {
      /** 选课规则*/
      response1 = await MsChooseCourseLearningScene.invalidStudentCourse({
        studentReLearnToken: token,
        studentCourseId: this.deleteParams.studentCourseId
      })
    }
    return response1.status
  }
}

export default MutationDeleteCourse
