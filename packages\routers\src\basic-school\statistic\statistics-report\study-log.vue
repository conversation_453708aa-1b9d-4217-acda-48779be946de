<!--
 * @Author: l<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-24 21:16:58
 * @LastEditors: lixinye <EMAIL>
 * @LastEditTime: 2023-07-26 20:02:37
 * @FilePath: \jxjyv2_frontend_web_admin\src\unit-type\basic-school\statistic\statistics-report\study-log.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<route-meta>
{
"isMenu": true,
"hideMenu": true,
"title": "监管日志",
"sort": 99,
"icon": "icon-ribaotongji"
}
</route-meta>

<script lang="ts">
  import StudyLog from '@hbfe/jxjy-admin-statisticsReport/src/study-log.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import {
    // 施教机构管理员
    WXGLY,ZTGLY
  } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    userInfo: [WXGLY,ZTGLY],
    classInfo: [WXGLY,ZTGLY],
    studyLog: [WXGLY,ZTGLY]
  })
  export default class extends StudyLog {}
</script>
