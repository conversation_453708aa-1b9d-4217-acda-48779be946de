import getAllWorkTypeList from './queries/getAllWorkTypeList.graphql'
import getWorkType from './queries/getWorkType.graphql'
import getWorkTypeListByCategoryId from './queries/getWorkTypeListByCategoryId.graphql'
import getWorkTypeListByCategoryIds from './queries/getWorkTypeListByCategoryIds.graphql'
import getWorkTypeListByIds from './queries/getWorkTypeListByIds.graphql'
import getWorkTypeListByPortalCourseCategoryId from './queries/getWorkTypeListByPortalCourseCategoryId.graphql'
import getWorkTypeListByPortalCourseCategoryIds from './queries/getWorkTypeListByPortalCourseCategoryIds.graphql'
import getWorkTypePage from './queries/getWorkTypePage.graphql'
import moveDown from './mutates/moveDown.graphql'
import moveUp from './mutates/moveUp.graphql'

export {
  getAllWorkTypeList,
  getWorkType,
  getWorkTypeListByCategoryId,
  getWorkTypeListByCategoryIds,
  getWorkTypeListByIds,
  getWorkTypeListByPortalCourseCategoryId,
  getWorkTypeListByPortalCourseCategoryIds,
  getWorkTypePage,
  moveDown,
  moveUp
}
