import findBatchImportQuestionByPage from './queries/findBatchImportQuestionByPage.graphql'
import batchExportAllChooseQuestionData from './mutates/batchExportAllChooseQuestionData.graphql'
import batchExportFailChooseQuestionData from './mutates/batchExportFailChooseQuestionData.graphql'
import batchImportChooseQuestions from './mutates/batchImportChooseQuestions.graphql'
import createLibrary from './mutates/createLibrary.graphql'
import createQuestion from './mutates/createQuestion.graphql'
import createQuestionnaireLibrary from './mutates/createQuestionnaireLibrary.graphql'
import createTagQuestion from './mutates/createTagQuestion.graphql'
import disableQuestion from './mutates/disableQuestion.graphql'
import enableQuestion from './mutates/enableQuestion.graphql'
import initQuestionData from './mutates/initQuestionData.graphql'
import removeLibrary from './mutates/removeLibrary.graphql'
import removeQuestion from './mutates/removeQuestion.graphql'
import removeTagQuestion from './mutates/removeTagQuestion.graphql'
import systemCreateLibrary from './mutates/systemCreateLibrary.graphql'
import updateLibrary from './mutates/updateLibrary.graphql'
import updateQuestion from './mutates/updateQuestion.graphql'
import updateTagQuestion from './mutates/updateTagQuestion.graphql'

export {
  findBatchImportQuestionByPage,
  batchExportAllChooseQuestionData,
  batchExportFailChooseQuestionData,
  batchImportChooseQuestions,
  createLibrary,
  createQuestion,
  createQuestionnaireLibrary,
  createTagQuestion,
  disableQuestion,
  enableQuestion,
  initQuestionData,
  removeLibrary,
  removeQuestion,
  removeTagQuestion,
  systemCreateLibrary,
  updateLibrary,
  updateQuestion,
  updateTagQuestion
}
