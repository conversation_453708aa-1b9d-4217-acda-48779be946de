<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">个人报名退款</el-breadcrumb-item>
      <el-breadcrumb-item>详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <!--退款审批中-->
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>退款单号：rao202112141224187489587622</p>
          <p class="state f-co">待审核</p>
          <!--
          <p class="state f-c9">已取消</p>
          <p class="state f-cr">已拒绝</p>
          <p class="state f-co">待退货/款</p>
          <p class="state f-co">待退货</p>
          <p class="state f-cb">已退货</p>
          <p class="state f-cr">退货失败</p>
          <p class="state f-co">待确认退款</p>
          <p class="state f-co">待退款</p>
          <p class="state f-cb">已退款</p>
          <p class="state f-cg">退货/款成功</p>
          -->
          <div class="op f-mt15">
            <el-button type="warning" size="mini" plain>取消退款</el-button>
            <el-button type="warning" size="mini" plain>拒绝退款</el-button>
            <el-button type="warning" size="mini" plain>同意退款</el-button>
          </div>
        </div>
        <el-steps :active="1" align-center class="process">
          <el-step title="提交申请" description="2021-02-02 15:23:23" icon="hb-iconfont icon-mytraining"></el-step>
          <el-step title="审批申请" description="2021-02-02 15:23:23" icon="hb-iconfont icon-auditrecord"></el-step>
          <el-step title="处理退货" description="2021-02-02 15:23:23" icon="hb-iconfont icon-refundmanage"></el-step>
          <el-step title="退款确认" description="2021-02-02 15:23:23" icon="hb-iconfont icon-invoicemanage"></el-step>
          <el-step title="处理退款" description="2021-02-02 15:23:23" icon="hb-iconfont icon-payment"></el-step>
          <el-step title="完成" description="2021-02-02 15:23:23" icon="hb-iconfont icon-select"></el-step>
        </el-steps>
      </el-card>
      <!--订单信息-->
      <el-card shadow="never" class="m-card is-header m-order-info f-mb15">
        <div class="f-flex-sub f-plr20 f-pt10">
          <div class="m-tit">
            <span class="tit-txt">退款信息</span>
          </div>
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="12">
              <el-form-item label="退款类型：">退货不退款</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属报名批次号：">batch211215142418748958770000</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款金额：">￥0.00</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款方式：">线下退款</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款原因：">报错班级</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款说明：">退款说明退款说明</el-form-item>
            </el-col>
          </el-form>
          <div class="m-tit">
            <span class="tit-txt">分销信息</span>
          </div>
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="12">
              <el-form-item label="分销商：">这里读取分销商名称</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分销层级：">一级</el-form-item>
            </el-col>
          </el-form>
        </div>
        <div class="right f-plr20 f-ptb10">
          <div class="m-tit">
            <span class="tit-txt">退款单号</span>
          </div>
          <el-form label-width="auto" class="m-text-form is-column f-pt10 f-pl20">
            <el-form-item label="退款单号：">rao2021082017520774898907056</el-form-item>
            <el-form-item label="集体报名批次号：">
              <a href="#" class="f-link f-cb f-underline">210820151658748989080296</a>
            </el-form-item>
          </el-form>
          <div class="m-tit">
            <span class="tit-txt">退款操作记录</span>
          </div>
          <div class="f-pl20">
            <p>
              <span class="f-fb">子项目管理员</span>
              在
              <span class="f-fb">2020-04-29 13:41:17</span>
              发起退款申请
            </p>
            <el-form label-width="auto" class="m-text-form is-column f-pt10 f-pb20">
              <el-form-item label="退款金额：">¥ 0.01</el-form-item>
              <el-form-item label="退款状态：">退款审批中</el-form-item>
              <el-form-item label="退款说明：">退款说明退款说明退款说明</el-form-item>
            </el-form>
          </div>
        </div>
      </el-card>
      <!--初始订单-->
      <el-card shadow="never" class="m-card is-header m-order-info f-mb15">
        <div class="f-flex-sub f-plr20 f-pt10">
          <div class="m-tit">
            <span class="tit-txt">初始订单</span>
          </div>
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="12">
              <el-form-item label="订单号：">
                <a href="#" class="f-cb f-link f-underline">210820174632748989080313</a>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="订单商品：">初始订单对应的培训方案名称</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="交易流水号：">wxp2019103111253675774891164012612</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款金额：">¥ 0.01</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款单号：">210820174632748989080313</el-form-item>
            </el-col>
          </el-form>
        </div>
      </el-card>
      <!--购买清单-->
      <el-card shadow="never" class="m-card is-header m-order-info f-mb15">
        <div class="f-plr20 f-pt10">
          <div class="m-tit is-small">
            <span class="tit-txt">商品信息</span>
          </div>
          <el-form label-width="auto" class="m-text-form f-pt10 f-pb5">
            <el-col :span="8">
              <el-form-item label="购买人：">林林一</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证件号：">******************</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="手机号：">15632587412</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单号：">
                <a href="#" class="f-cb f-link f-underline">210820174632748989080313</a>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="交易流水号：">wxp2019103111253675774891164012612</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="退款单号：">210820174632748989080313</el-form-item>
            </el-col>
          </el-form>
          <el-table :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="物品名称" min-width="300">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  <p>
                    这是一个培训班名称培训班名称<el-tag size="small" class="f-ml10">换班</el-tag
                    ><el-tag type="warning" size="small" class="f-ml10">换期</el-tag>
                  </p>
                  <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                  <p><el-tag type="info" size="mini">资源供应商</el-tag>读取资源供应商名称</p>
                </div>
                <div v-else>
                  <div class="f-flex f-align-start">
                    <el-tag type="danger" size="mini">初始物品</el-tag>
                    <div>
                      <p>培训班名称或课程名称或学时商品名称</p>
                      <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                      <p><el-tag type="info" size="mini">资源供应商</el-tag>读取资源供应商名称</p>
                    </div>
                  </div>
                  <div class="f-ml30 f-pl30"><i class="el-icon-caret-bottom"></i></div>
                  <div class="f-flex f-align-start">
                    <el-tag type="danger" size="mini">最新物品</el-tag>
                    <div>
                      <p>培训班名称或课程名称或学时商品名称</p>
                      <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                      <p><el-tag type="info" size="mini">资源供应商</el-tag>读取资源供应商名称</p>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="学时" min-width="150" align="center">
              <template>2</template>
            </el-table-column>
            <el-table-column label="数量" min-width="150" align="center">
              <template>1</template>
            </el-table-column>
            <el-table-column label="单价(元)" min-width="150" align="right">
              <template>200.00</template>
            </el-table-column>
            <el-table-column label="实付金额(元)" min-width="150" align="right">
              <template>200.00</template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
