import { ResponseStatus } from '@hbfe/common'
import Order, { ApplyBatchOrderReturnResponse, SellerApplyBatchOrderReturnRequest } from '@api/ms-gateway/ms-order-v1'
import ReturnOrder, {
  BatchReturnOrderAgreeApplyRequest,
  BatchReturnOrderAgreeApplyResponse,
  BatchReturnOrderAgreeBatchApplyRequest,
  BatchReturnOrderAgreeBatchApplyResponse,
  BatchReturnOrderCancelApplyRequest,
  BatchReturnOrderCancelApplyResponse,
  BatchReturnOrderConfirmRefundResponse,
  BatchReturnOrderConfirmBatchRefundResponse,
  BatchReturnOrderRejectApplyRequest,
  BatchReturnOrderRejectApplyResponse
} from '@api/ms-gateway/ms-batch-return-v1'
import SellerApplyBatchOrderReturnRequestVo from './vo/SellerApplyBatchOrderReturnRequestVo'
import { Response } from '@hbfe/common'
class MutationBatchOrderRefund {
  /******************************************************************************************* */
  /******************************************************************************************* */
  /**************该状态层接口返回业务code:200为正常,如果非200，弹窗显示message字段*************** */
  /******************************************************************************************* */
  /******************************************************************************************* */
  /**
   * 申请退款And批次内订单开申请退款：区别在参数里的returnSubOrders
   * @returns
   */
  async applyBatchRefund(
    sellerApplyBatchOrderReturnRequest: SellerApplyBatchOrderReturnRequestVo
  ): Promise<Response<ApplyBatchOrderReturnResponse>> {
    const response = await Order.sellerApplyBatchReturn(sellerApplyBatchOrderReturnRequest)
    return response
  }
  /**
   * 继续退款
   * @param batchReturnOrderNo
   * @returns
   */
  async retryRefund(batchReturnOrderNo: string) {
    const { status } = await ReturnOrder.retryRefund({ batchReturnOrderNo })
    return status
  }
  /**
   * 重新回收资源
   * @param batchReturnOrderNo 批次单号
   * @returns
   */
  async againRecyclingbatchReturnOrderNo(batchReturnOrderNo: string) {
    const { status } = await ReturnOrder.retryRecycleResource({ batchReturnOrderNo })
    return status
  }
  /**
   * 拒绝退款
   * @param batchReturnOrderNo 批次单号
   * @param approveComment 审批意见
   * @returns
   */
  async refuseRefund(batchReturnOrderNo: string, approveComment: string): Promise<BatchReturnOrderRejectApplyResponse> {
    const batchReturnOrderRejectApplyRequest = new BatchReturnOrderRejectApplyRequest()
    batchReturnOrderRejectApplyRequest.batchReturnOrderNo = batchReturnOrderNo
    batchReturnOrderRejectApplyRequest.approveComment = approveComment
    const { data } = await ReturnOrder.rejectReturnApply(batchReturnOrderRejectApplyRequest)
    return data
  }
  /**
   * 同意退款
   * @param batchReturnOrderNo 批次单号
   * @param message 说明
   * @returns
   */
  async agreeRefund(batchReturnOrderNo: string, message?: string): Promise<BatchReturnOrderAgreeApplyResponse> {
    const batchReturnOrderAgreeApplyRequest = new BatchReturnOrderAgreeApplyRequest()
    batchReturnOrderAgreeApplyRequest.batchReturnOrderNo = batchReturnOrderNo
    batchReturnOrderAgreeApplyRequest.approveComment = message
    const { data } = await ReturnOrder.agreeReturnApply(batchReturnOrderAgreeApplyRequest)
    return data
  }
  /**
   * 确认退款
   * @param batchReturnOrderNo 批次单号
   * @returns
   */
  async confirmRefund(batchReturnOrderNo: string): Promise<BatchReturnOrderConfirmRefundResponse> {
    const { data } = await ReturnOrder.confirmRefund({ batchReturnOrderNo })
    return data
  }
  /**
   * 取消退款
   * @param batchReturnOrderNo 批次单号
   * @param cancelReason 说明
   * @returns
   */
  async cancelRefund(batchReturnOrderNo: string, cancelReason: string): Promise<BatchReturnOrderCancelApplyResponse> {
    const batchReturnOrderCancelApplyRequest = new BatchReturnOrderCancelApplyRequest()
    batchReturnOrderCancelApplyRequest.batchReturnOrderNo = batchReturnOrderNo
    batchReturnOrderCancelApplyRequest.cancelReason = cancelReason
    const { data } = await ReturnOrder.sellerCancelReturnApply(batchReturnOrderCancelApplyRequest)
    return data
  }
  /**
   * 批量同意退款
   * @param batchReturnOrderNoList 批次单号数组
   * @param message 说明
   * @returns
   */
  async batchAgreeReturnApply(
    batchReturnOrderNoList: string[],
    message?: string
  ): Promise<BatchReturnOrderAgreeBatchApplyResponse> {
    const batchReturnOrderAgreeBatchApplyRequest = new BatchReturnOrderAgreeBatchApplyRequest()
    batchReturnOrderAgreeBatchApplyRequest.batchReturnOrderNoList = batchReturnOrderNoList
    batchReturnOrderAgreeBatchApplyRequest.approveComment = message
    const { data } = await ReturnOrder.agreeReturnBatchApply(batchReturnOrderAgreeBatchApplyRequest)
    return data
  }
  /**
   *  批量确认退款
   * @param batchReturnOrderNoList 批次单号数组
   * @returns
   */
  async batchConfirmRefund(batchReturnOrderNoList: string[]): Promise<BatchReturnOrderConfirmBatchRefundResponse> {
    const { data } = await ReturnOrder.confirmBatchRefund({ batchReturnOrderNoList })
    return data
  }
}
export default MutationBatchOrderRefund
