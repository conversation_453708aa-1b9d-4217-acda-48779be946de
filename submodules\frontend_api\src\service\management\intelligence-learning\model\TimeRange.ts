export default class TimeRange {
  constructor(startTime?: string, endTime?: string) {
    this.startTime = startTime
    this.endTime = endTime
  }
  /**
   * 开始时间
   */
  startTime = ''

  /**
   * 结束时间
   */
  endTime = ''

  /**
   * 时间范围
   */
  get timeRange() {
    return [this.startTime ?? '', this.endTime ?? '']
  }
  set timeRange([startTime, endTime]: [string, string]) {
    this.startTime = startTime
    this.endTime = endTime
  }
}
