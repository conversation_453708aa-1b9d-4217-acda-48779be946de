import { PayChannelCodeConstants } from './PayChannelCodeConstants'

/**
 * 订单所用的收款账号信息
 */
export class PayeeAccount {
  /**
   * 收款账号id
   */
  id: string
  /**
   * 支付渠道的code，code对应的支付渠道名在PayChannelCodeConstants中定义了
   */
  payChannelCode: string
  /**
   * 支付渠道的logoPath
   */
  logoPath: string
  /**
   * 支付类型 1线上 2线下
   */
  payType: number
  /**
   * 开户人
   */
  merchantName: string
  /**
   * 开户银行
   */
  depositBank: string
  /**
   * 账户账号
   */
  accountNo: string

  /**
   * 获取渠道code对应的渠道名称
   */
  getPayChannelName(): string {
    return PayChannelCodeConstants.getTradeChannelName(this.payChannelCode)
  }

  // static from(dto: StudentMerchantAccountDTO): PayeeAccount {
  //   const account = new PayeeAccount()
  //   account.id = dto.id
  //   account.payChannelCode = dto.code
  //   account.logoPath = dto.logoPath
  //   account.payType = dto.payType
  //   account.merchantName = dto.merchantName
  //   account.depositBank = dto.depositBank
  //   account.accountNo = dto.accountNo
  //   return account
  // }
}
