import {
  StudentSchemeLearningRequestVoDiff as StudentSchemeLearningRequestVo,
  SyncResultEnmu
} from '@api/service/diff/management/gstyb/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
import { QueryStudentLearningManagerRegionList } from '@api/service/management/statisticalReport/query/QueryStudentLearningManagerRegionList'

export default class QueryStudentLearningManagerRegionListDiff extends QueryStudentLearningManagerRegionList {
  syncResult: Record<SyncResultEnmu, number> = {
    [SyncResultEnmu.DisableSynchronized]: -1,
    [SyncResultEnmu.Unsynchronized]: 0,
    [SyncResultEnmu.Synchronized]: 1,
    [SyncResultEnmu.SynchronizationFailure]: 2,
    [SyncResultEnmu.Waitsynchronized]: 3
  }
}
