import { CourserPackageSyncSchemeResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import Mockjs from 'mockjs'
import SyncStatus from '@api/service/management/resource/course-package/enums/SyncStatus'
import DataStatus from '@api/service/management/resource/course-package/enums/DataStatus'

class CoursePackageSyncSchemeInfo {
  // 方案名称
  name: string

  // 方案id
  id: string
  // 数据状态
  dataStatus: DataStatus
  // 方案是否执行任务中
  executed: boolean
  // 最新同步时间
  syncTime: string

  /**
   * 消息，
   * 如果失败，则会有失败原因
   */
  message: string

  syncStatus: SyncStatus

  isSyncSuccess() {
    return this.syncStatus.equal(SyncStatus.enum.SYNCED)
  }

  isSyncFailed() {
    return this.syncStatus.equal(SyncStatus.enum.SYNC_FAILED)
  }

  static from(coursePackageSyncSchemeResponse: CourserPackageSyncSchemeResponse[]) {
    return coursePackageSyncSchemeResponse.map(res => {
      const info = new CoursePackageSyncSchemeInfo()
      info.syncStatus = new SyncStatus(res.syncStatus)
      info.id = res.schemeId
      info.dataStatus = new DataStatus(res.dataStatus)
      info.syncTime = res.syncTime
      info.executed = res.executed
      // todo
      info.message = res.errorMessage
      return info
    })
  }
}

export default CoursePackageSyncSchemeInfo
