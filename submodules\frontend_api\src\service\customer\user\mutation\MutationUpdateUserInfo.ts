/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-10-17 09:30:31
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-11-07 16:01:01
 * @Description:
 */
import MsAccountGateway, { TokenResponse } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { ResponseStatus } from '@hbfe/common'
import UpdateStudentRequestVo from './vo/create/UpdateStudentRequestVo'
import { Response } from '@hbfe/common'

import UserModule from '@api/service/customer/user/query-user/UserModule'
import { PerfectInfoPoint } from '@api/service/common/webfunny/config/ConfigInfo'
import WebfunnyReport from '@api/service/common/webfunny/WefunnyReport'

/**
 * 更新用户信息
 */
class MutationUpdateUserInfo {
  updateStudentRequestVo = new UpdateStudentRequestVo()

  /**
   * 更新用户信息方法
   */
  async doUpdate(): Promise<Response<TokenResponse>> {
    this.checkName()
    const res = await MsAccountGateway.updateStudent(this.updateStudentRequestVo)
    return res
  }

  /**
   * 判断名字大于等于4位往上报
   */
  checkName() {
    try {
      if (this.updateStudentRequestVo.name.length >= 4) {
        const description = new PerfectInfoPoint()
        description.abnormalMessage = '姓名大于等于4位'
        description.domainName = location.origin
        description.requestBody = JSON.stringify(this.updateStudentRequestVo.name)
        description.userId = UserModule.userInfo.userId
        WebfunnyReport.upPerfectInfo(description)
      }
    } catch (e) {
      console.group(
        '%c%s',
        'padding:3px 60px;color:#fff;background-image: linear-gradient(to right, #ffa17f, #00223e)',
        'schemeRefundList调试'
      )
      console.log(e, 'e')
      console.groupEnd()
    }
  }
}
export default MutationUpdateUserInfo
