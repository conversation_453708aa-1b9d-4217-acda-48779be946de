schema {
	query:Query
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取课程被学习的人次 - 来源中间表(已实现)
		@param courseId
		@return
	"""
	getCourseLearningCount(courseId:String!):Double! @NotAuthenticationRequired
	"""获取最后一次学习的课程 - 来源中间表(已实现)
		@param paramDTO
		@return UserLastLearningCourseDTO || null:未选课的时候
	"""
	getUserLastCourseLearning(paramDTO:UserLastLearningCourseParamDTO):UserLastLearningCourseDTO
	"""获取用户方案内的已选课程-课程服务
		@param paramDTO
		@return
	"""
	listUserCourse(paramDTO:UserCourseParamDTO):[UserCourseDTO]
	"""获取用户指定课程的学习进度 -课程服务
		@param paramDTO
		@return
	"""
	listUserCourseLearningSchedule(paramDTO:UserCourseLearningScheduleParamDTO):[UserCourseLearningScheduleDTO]
	"""根据方案id和课程学习方式id获取所有可选课程包
		@param paramDTO
		@return
	"""
	listUserCoursePool(paramDTO:UserCourseParamDTO):[CoursePoolBaseInfoDTO]
	"""获取指定课程下的所有课件进度
		@param paramDTO
		@return
	"""
	listUserCoursewareLearningSchedule(paramDTO:UserCourseLearningScheduleParamDTO):[UserCoursewareLearningScheduleDTO]
	"""获取用户方案内未选择的课程 -课程服务
		@param paramDTO
		@return
	"""
	listUserUnSelectCourse(page:Page,paramDTO:UserCourseParamDTO):UserCourseDTOPage @page(for:"UserCourseDTO")
	"""获取用户课程学习记录
		@param page
		@param param
		@return
	"""
	pageUserCourseLearning(page:Page,param:UserCourseRecordParam):UserCourseLearningRecordResponsePage @page(for:"UserCourseLearningRecordResponse")
	"""课程学习统计
		@param param
		@return
	"""
	statisticCourseLearning(page:Page,param:CourseLearningStatisticParam):CourseLearningStatisticDTOPage @page(for:"CourseLearningStatisticDTO") @NotAuthenticationRequired
	"""统计用户学习方案内的课程学习情况  - 来源中间表(已实现)
		@param paramDTO
		@return
	"""
	statisticUserLearningCourseLearningInfo(paramDTO:UserSchemeCourseLearningStatisticParamDTO):UserSchemeCourseLearningStatisticDTO
}
"""课程学习统计参数
	@author: eleven
	@date: 2020/4/13
"""
input CourseLearningStatisticParam @type(value:"com.fjhb.btpx.integrative.service.course.dto.param.CourseLearningStatisticParam") {
	"""学习方案ID"""
	schemeId:String
	"""学习方式id"""
	learningId:String
	"""课程id集合"""
	courseIdList:[String]
}
"""用户课程学习进度查询
	@author: eleven
	@date: 2020/3/5
"""
input UserCourseLearningScheduleParamDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.param.UserCourseLearningScheduleParamDTO") {
	"""课程id集合"""
	courseIdList:[String]
	"""用户id -运营域参数，学员端忽略该参数"""
	userId:String
	"""选课类型
		@see com.fjhb.platform.core.courselearning.v1.api.constants.UserCourseSource
	"""
	source:String
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""用户已选课程查询参数
	@author: eleven
	@date: 2020/3/5
"""
input UserCourseParamDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.param.UserCourseParamDTO") {
	"""所属课程包id"""
	coursePoolId:String
	"""选课类型
		@see com.fjhb.platform.core.courselearning.v1.api.constants.UserCourseSource
	"""
	source:String
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""用户最后学习所在的课程查询条件
	@author: eleven
	@date: 2020/3/5
"""
input UserLastLearningCourseParamDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.param.UserLastLearningCourseParamDTO") {
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""用户课程
	@author: eleven
	@date: 2020/3/5
"""
input UserSchemeCourseLearningStatisticParamDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.param.UserSchemeCourseLearningStatisticParamDTO") {
	"""学习方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String
}
"""用户课程学习记录查询参数
	@author: eleven
	@date: 2020/5/18
"""
input UserCourseRecordParam @type(value:"com.fjhb.btpx.platform.dao.elasticsearch.dto.UserCourseRecordParam") {
	userId:String
	schemeId:String
	issueId:String
	learningId:String
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""
	课程被学习次数统计
	@author: eleven
	@date: 2020/4/13
"""
type CourseLearningStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.CourseLearningStatisticDTO") {
	"""课程id"""
	courseId:String
	"""总数被选课次数"""
	selectedCount:Long!
	"""课程待学习总数"""
	waitStudyCount:Long!
	"""本课程学习中总数"""
	studyCount:Long!
	"""本课程学习完成总数"""
	studyFinishCount:Long!
}
"""课程包基础信息
	Author:FangKunSen
	Time:2020-06-06,11:17
"""
type CoursePoolBaseInfoDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.CoursePoolBaseInfoDTO") {
	"""课程包id"""
	coursePoolId:String
	"""课程包名"""
	showName:String
}
"""用户已选课程
	@author: eleven
	@date: 2020/3/5
"""
type UserCourseDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.UserCourseDTO") {
	"""所属课程包编号"""
	poolId:String
	"""所属课程类型
		1:必修课；2：选修；
	"""
	courseType:Int!
	"""课程ID"""
	id:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""权重,表示学时,学分等
		当查询未选课列表时，为课程在课程包的学时
		当查询已选课时，用户选课时，课程包配置的学时
	"""
	period:Double!
	"""课程简介"""
	abouts:String
	"""课程的课件状态，0表示解析中，1表示解析成功，2表示解析失败"""
	status:Int!
	"""课程时长"""
	timeLength:Long!
	"""是否支持试听"""
	supportAudition:Boolean!
	"""计划课件数量"""
	courseWareCount:Int!
	"""已更新的课件数量"""
	courseWareUpdateCount:Int!
	"""课件教师id集合"""
	teacherIdList:[String]
	"""所属课程分类集合（正常只有一个）"""
	courseCategoryDtoList:[CourseCategoryDto]
	"""课程所属考纲id集合"""
	tagIdList:[String]
	"""创建时间"""
	createTime:DateTime
	"""是否启用"""
	enabled:Boolean!
}
"""课程学习记录，来自于清洗中间表
	<AUTHOR>
	@date 2020/8/25
	@description
"""
type UserCourseLearningRecordResponse @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.UserCourseLearningRecordResponse") {
	"""用户"""
	userId:String
	"""方案id"""
	schemeId:String
	"""方式id"""
	learningId:String
	"""期别编号"""
	stageId:String
	"""期数编号"""
	issueId:String
	"""年度"""
	year:Int!
	"""选课规则id"""
	ruleId:String
	"""包id"""
	poolId:String
	"""课程id"""
	courseId:String
	"""选课类型
		@see UserChooseCourseType
	"""
	chooseCourseType:Int!
	"""课程学时"""
	period:Double!
	"""课程时长"""
	timeLength:Double!
	"""课程学习进度"""
	schedule:Double!
	"""课程学习时长"""
	learningTimeLength:Double!
	"""课程学习状态
		@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
	"""
	studyState:Int!
	"""开始学习时间"""
	startStudyTime:String
	"""最后学习时间"""
	lastStudyTime:String
	"""学习完成时间"""
	studyCompleteTime:String
}
"""用户指定学习方案内指定课程的学习进度
	@author: eleven
	@date: 2020/3/5
"""
type UserCourseLearningScheduleDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.UserCourseLearningScheduleDTO") {
	"""学习方案id"""
	schemeId:String
	"""课程id"""
	courseId:String
	"""学习进度"""
	schedule:Double!
	"""学习状态
		0/1/2，未学习/学习中/学习完成
		@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
	"""
	studyState:Int!
	"""最新学习时间"""
	lastStudyTime:DateTime
}
"""用户课件学习记录编号
	<AUTHOR>
	@date 2020/3/14
	@since 1.0.0
"""
type UserCoursewareLearningScheduleDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.UserCoursewareLearningScheduleDTO") {
	"""学习方案id"""
	schemeId:String
	"""课程id"""
	courseId:String
	"""课件编号"""
	courseWareId:String
	"""课件学习进度"""
	schedule:Double!
	"""课件学习状态
		0/1/2，未学习/学习中/学习完成
		@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
	"""
	studyState:Int!
	"""最新学习时间"""
	lastStudyTime:DateTime
	"""课件学习创建时间"""
	createTime:DateTime
}
"""用户最后一次学习的课程
	@author: eleven
	@date: 2020/3/5
"""
type UserLastLearningCourseDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.UserLastLearningCourseDTO") {
	"""学习方案id"""
	schemeId:String
	"""学习方式id"""
	learningId:String
	"""包id"""
	poolId:String
	"""课程id"""
	courseId:String
	"""课程学习进度"""
	schedule:Double!
	"""最后一次学习时间"""
	lastStudyTime:String
}
"""用户方案内正在的学习的课程数统计
	@author: eleven
	@date: 2020/3/5
"""
type UserSchemeCourseLearningStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.response.UserSchemeCourseLearningStatisticDTO") {
	"""待学习课程数"""
	waitStudyCount:Double!
	"""学习中课程数"""
	studyCount:Double!
	"""学习完成课程数"""
	studyFinishCount:Double!
}
type CourseCategoryDto @type(value:"com.fjhb6.course.v2.north.api.dto.CourseCategoryDto") {
	id:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	organizationId:String
	name:String
	parentId:String
	sort:Int!
	remarks:String
	objectId:String
}

scalar List
type UserCourseDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserCourseDTO]}
type UserCourseLearningRecordResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserCourseLearningRecordResponse]}
type CourseLearningStatisticDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseLearningStatisticDTO]}
