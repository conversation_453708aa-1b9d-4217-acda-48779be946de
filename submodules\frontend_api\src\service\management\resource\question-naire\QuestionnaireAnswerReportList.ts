import QuestionnaireAnswerReportParams from '@api/service/management/resource/question-naire/models/QuestionnaireAnswerReportParams'
import QuestionnaireAnswerReportItem from '@api/service/management/resource/question-naire/models/QuestionnaireAnswerReportItem'
import PlatQuestionnaireQueryBackStage, {
  QuestionnaireAnswerRequest
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'
import { Page } from '@hbfe/common'
/**
 * 实施管理-问卷答题记录
 */
export default class QuestionnaireAnswerReportList {
  /**
   * 筛选条件
   */
  params = new QuestionnaireAnswerReportParams()
  /**
   * 问卷答题记录列表
   */
  list = new Array<QuestionnaireAnswerReportItem>()

  /**
   * 查询问卷答题记录列表
   */
  async doQuery(page: Page) {
    const request = new QuestionnaireAnswerRequest()
    request.questionnaireId = this.params.id
    request.idCard = this.params.idCard
    request.name = this.params.name
    request.phoneNum = this.params.phone
    request.startSubmitTime = this.params.submitTime.begin
    request.endSubmitTime = this.params.submitTime.end
    const res = await PlatQuestionnaireQueryBackStage.pageQuestionnaireAnswerInServicer({ page, request })
    this.list = []
    if (res.status.isSuccess() && res.data?.currentPageData?.length) {
      this.list = res.data.currentPageData.map(item => {
        return QuestionnaireAnswerReportItem.from(item)
      })
    }
    page.totalPageSize = res.data?.totalPageSize || 0
    page.totalSize = res.data?.totalSize || 0
    return this.list
  }
}
