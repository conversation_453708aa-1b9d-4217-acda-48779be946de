const customerBase = 'https://rsjxjy.test2.59iedu.com:9443'
const mockPrefix = `/mock/${process.env.VUE_APP_YAPI_PROJECT_ID}`
// 微服务前缀
const microServiceMockPrefix = `/mock/${process.env.VUE_APP_YAPI_MICRO_SERVICE_PROJECT_ID}`
const commonMsPrefix = `/mock/${process.env.VUE_APP_YAPI_COMMON_MICRO_SERVICE_PROJECT_ID}`
module.exports = {
  '/mfs': {
    target: 'http://*************:8080',
    secure: false
  },
  [mockPrefix]: {
    target: process.env.VUE_APP_YAPI_SERVER,
    changeOrigin: true,
    secure: false
  },
  [microServiceMockPrefix]: {
    target: process.env.VUE_APP_YAPI_SERVER,
    changeOrigin: true,
    secure: false
  },
  [commonMsPrefix]: {
    target: process.env.VUE_APP_YAPI_SERVER,
    changeOrigin: true,
    secure: false
  },
  // '/web': {
  //   target: 'https://minqing.test1.59iedu.com:9443',
  //   secure: false,
  //   headers: {
  //     host: 'minqing.test1.59iedu.com:9443'
  //   }
  // },
  '/web': {
    target: 'https://fjzjty.dev.59iedu.com:9443/',
    secure: false,
    headers: {
      host: 'fjzjty.dev.59iedu.com:9443'
    }
  },
  '/play': {
    target: customerBase,
    changeOrigin: true,
    onProxyRes(proxyRes, req, res) {
      res.writeHead(302, {
        Location: `${customerBase}${req.url}`
      })
      res.end()
    }
  }
}
