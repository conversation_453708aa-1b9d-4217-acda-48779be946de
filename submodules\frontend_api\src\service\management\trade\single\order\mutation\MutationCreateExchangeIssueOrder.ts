import SdsPlatformLearningscheme, { ChangeIssueRequest } from '@api/ms-gateway/ms-learningscheme-enrollment-v1'
import { ResponseStatus } from '@hbfe/common'
import ChangeIssueResCode, {
  ChangeIssueResCodeEnum
} from '@api/service/management/trade/single/order/enum/ChangeIssueResCodeEnum'

export default class MutationCreateExchangeIssueOrder {
  /**
   * 换期
   * @param param
   */
  async changeIssue(param: ChangeIssueRequest) {
    const res = await SdsPlatformLearningscheme.changeIssue(param)

    if (res?.status && res.status.isSuccess() && res.data?.code) {
      const code = Number(res.data?.code)
      if (code == 200) {
        return Promise.resolve(new ResponseStatus(200, ''))
      } else {
        return Promise.reject(new ResponseStatus(code as ChangeIssueResCodeEnum, ChangeIssueResCode.map.get(code)))
      }
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }
}
