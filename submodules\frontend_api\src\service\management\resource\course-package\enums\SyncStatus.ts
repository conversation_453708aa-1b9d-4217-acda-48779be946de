import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum SyncStatusEnum {
  NOT_SYNCED = 0,
  SYNCED = 1,
  SYNCING = 2,
  SYNC_FAILED = 3
}

class SyncStatus extends AbstractEnum<SyncStatusEnum> {
  static enum = SyncStatusEnum

  constructor(status?: SyncStatusEnum) {
    super()
    this.current = status
    this.map.set(SyncStatusEnum.NOT_SYNCED, '未同步')
    this.map.set(SyncStatusEnum.SYNCING, '同步中')
    this.map.set(SyncStatusEnum.SYNCED, '同步成功')
    this.map.set(SyncStatusEnum.SYNC_FAILED, '同步失败')
  }
}

export default SyncStatus
