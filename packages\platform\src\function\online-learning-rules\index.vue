<template>
  <div class="f-p15">
    <el-tabs type="card" class="m-tab-card">
      <el-card shadow="never" class="m-card">
        <div class="f-mb20 f-flex f-align-center f-justify-between">
          <template
            v-if="$hasPermission('addOnlineLearningRules')"
            desc="添加在线规则"
            actions="@hbfe/jxjy-admin-platform/src/function/online-learning-rules/add-online-learning-rules.vue"
          >
            <el-button type="primary" @click="addOnlineLearningRules">添加规则</el-button>
          </template>
        </div>
        <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb15 f-mb15">
          <p>
            1.开启学员学习规则后，学员进入课程学习时会判断培训班是否有规则，若有配置规则时，会根据规则限制学员每日在线学习时长，达到设置时长时无法继续学习。
          </p>
          <p>2.添加、编辑、停用、启用规则时，在次日0点生效。</p>
        </el-alert>
        <!-- 学习规则 -->
        <template
          v-if="$hasPermission('onlineLearningRuleList')"
          desc="在线学习规则列表"
          actions="@OnlineLearningRulesList,@hbfe/jxjy-admin-platform/src/function/online-learning-rules/detail.vue,@hbfe/jxjy-admin-platform/src/function/online-learning-rules/modify.vue"
        >
          <online-learning-rules-list></online-learning-rules-list>
        </template>
      </el-card>
    </el-tabs>
  </div>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import OnlineLearningRulesList from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/online-learning-rules-list.vue'

  @Component({
    components: {
      OnlineLearningRulesList
    }
  })
  export default class extends Vue {
    // 添加规则
    addOnlineLearningRules() {
      this.$router.push('/basic-data/platform/function/online-learning-rules/add-online-learning-rules')
    }
  }
</script>
