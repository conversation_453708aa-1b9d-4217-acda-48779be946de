import LearningHeatBeatResult from '@api/service/customer/learning/course/learning-heat-beat/LearningHeatBeatResult'
import LearningResourceNotSupportResult from '@api/service/customer/learning/course/play-course/LearningResourceNotSupportResult'

/**
 * 课程播放状态管理
 */
class PlayCourseResultStatus {
  learningHeatBeatResult: LearningHeatBeatResult = new LearningHeatBeatResult()
  learningResourceNotSupportResult: LearningResourceNotSupportResult = new LearningResourceNotSupportResult()

  reset() {
    this.learningResourceNotSupportResult = new LearningResourceNotSupportResult()
    this.learningHeatBeatResult = new LearningHeatBeatResult()
  }

  hasError() {
    return this.learningHeatBeatResult
  }
}

export default PlayCourseResultStatus
