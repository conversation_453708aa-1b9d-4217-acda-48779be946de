/*
 * @Description: 资讯详情
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-25 10:33:10
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 15:35:58
 */
import { NewsDetailWithPreviousAndNext } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

class NewsDetail {
  id: string
  // 分类id
  necId: string = null
  // * 资讯类别名称
  necName: string = null
  // * 标题
  title: string = null
  // 资讯摘要
  abstract: string = null
  // 发布时间
  date: string = null
  // 阅读人数
  number: number
  // 资讯内容
  content: string = null
  /**
   * 封面图片路径
   */
  coverPath: string = null
  static from(newsDetailWithPreviousAndNext: NewsDetailWithPreviousAndNext) {
    const {
      newsDetail: { newId, title, summary, content, publishTime, reviewCount, coverPath, necId }
    } = newsDetailWithPreviousAndNext
    const newsDetail = new NewsDetail()
    newsDetail.id = newId
    newsDetail.necId = necId
    newsDetail.title = title
    newsDetail.abstract = summary
    newsDetail.content = content
    newsDetail.date = publishTime
    newsDetail.number = reviewCount
    newsDetail.coverPath = coverPath
    return newsDetail
  }
}
export default NewsDetail
