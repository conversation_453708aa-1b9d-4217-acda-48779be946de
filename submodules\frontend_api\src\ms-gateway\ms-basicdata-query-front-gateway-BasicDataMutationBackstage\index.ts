import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-basicdata-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-basicdata-query-front-gateway-BasicDataMutationBackstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 新增字典请求体
 */
export class AddIndustryPropertyRequest {
  /**
   * 业务数据字典类型|对应枚举BusinessDataDictionaryTypeEnum
@see BusinessDataDictionaryTypeEnum
   */
  type?: string
  /**
   * 行业ID
   */
  industryId?: string
  /**
   * 字典名称
   */
  name?: string
  /**
   * 字典编码
   */
  code?: number
  /**
   * 字典编码字典扩展，用于解决code适用度不够<br/>
默认用于存放字典国标\通用代码的字段，开放编辑的字段则存放管理员自定义的代码
   */
  codeExt?: string
}

/**
 * 新增字典请求体
 */
export class UpdateIndustryPropertyRequest {
  /**
   * 字典ID
   */
  id?: string
  /**
   * 字典名称
   */
  name?: string
  /**
   * 字典编码
   */
  code?: number
  /**
   * 字典编码字典扩展，用于解决code适用度不够<br/>
默认用于存放字典国标\通用代码的字段，开放编辑的字段则存放管理员自定义的代码
   */
  codeExt?: string
}

/**
 * 字典变更响应
 */
export class IndustryPropertyMutationResponse {
  /**
   * 错误码<br/>
<li>200：成功</li>
<li>500：非以下类别的响应</li>
<li>50001：工种或培训类别名称已存在</li>
<li>50002：工种code已存在</li>
@see IndustryPropertyMutationErrCodeConstants
   */
  code: number
  /**
   * 错误信息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 新增行业培训属性
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addIndustryProperty(
    request: AddIndustryPropertyRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.addIndustryProperty,
    operation?: string
  ): Promise<Response<IndustryPropertyMutationResponse>> {
    return commonRequestApi<IndustryPropertyMutationResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新行业培训属性
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateIndustryProperty(
    request: UpdateIndustryPropertyRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.updateIndustryProperty,
    operation?: string
  ): Promise<Response<IndustryPropertyMutationResponse>> {
    return commonRequestApi<IndustryPropertyMutationResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
