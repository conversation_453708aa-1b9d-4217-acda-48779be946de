import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 异步任务执行状态枚举
 */
export enum AsyncTaskExecStatusEnum {
  // 1：已执行
  Has_Execed = 1,
  // 2：正在执行
  In_Exec,
  // 3：执行失败
  Fail_Exced
}

/**
 * @description 异步任务执行状态列表
 */
class AsyncTaskExecStatusList extends AbstractEnum<AsyncTaskExecStatusEnum> {
  static enum = AsyncTaskExecStatusEnum
  constructor(status?: AsyncTaskExecStatusEnum) {
    super()
    this.current = status
    this.map.set(AsyncTaskExecStatusEnum.Has_Execed, '已执行')
    this.map.set(AsyncTaskExecStatusEnum.In_Exec, '正在执行')
    this.map.set(AsyncTaskExecStatusEnum.Fail_Exced, '执行失败')
  }
}

export default new AsyncTaskExecStatusList()
