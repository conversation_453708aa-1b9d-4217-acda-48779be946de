import msExamgataway, {
  CourseQuizAnswerPaperResponse,
  SortTypeEnum
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { Page } from '@hbfe/common'
import QueryCourseList from '@api/service/customer/course/query/QueryCourseList'

export class CourseQuizAnswerPaper extends CourseQuizAnswerPaperResponse {
  courseName = ''
}

export default class QueryPraticeRecordList {
  /**
   * 获取学员课后测验记录列表
   */
  async pagePracticeRecordInSubProject(page: Page, qualificationId: string): Promise<Array<CourseQuizAnswerPaper>> {
    try {
      console.log('page参数=', page, 'qualificationId参数=', qualificationId)
      const res = await msExamgataway.pageCourseQuizRecordInSubProject({
        page: page,
        qualificationId: qualificationId,
        answerPaperStatus: 2,
        sort: SortTypeEnum.DESC
      })
      const tmpArr: CourseQuizAnswerPaper[] = []

      if (res.status.isSuccess()) {
        Object.assign(tmpArr, res.data.currentPageData)
        page.totalSize = res.data.totalSize
      }
      tmpArr.map(res => {
        res.courseName = ''
      })
      let courseIds: string[] = []
      if (res.data.currentPageData && res.data.currentPageData.length) {
        res.data.currentPageData.map(item => {
          if (item.answerPaperBasicInfo.courseId) {
            courseIds.push(item.answerPaperBasicInfo.courseId)
          }
        })
      }
      const queryCourseList = new QueryCourseList()
      courseIds = Array.from(new Set(courseIds))
      if (courseIds && courseIds.length) {
        const courseInfo = await queryCourseList.queryCoursePageByIdList(courseIds)
        tmpArr.map(res => {
          const curCourseInfo = courseInfo.find(item => item.id === res.answerPaperBasicInfo.courseId)
          if (curCourseInfo) {
            res.courseName = curCourseInfo.name || ''
          }
        })
      }

      console.log('调用了pagePracticeRecordInSubProject方法，返回值=', tmpArr)
      return tmpArr
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryPraticeRecordList.ts所处方法，pagePracticeRecordInSubProject',
        e
      )
    }
  }
}
