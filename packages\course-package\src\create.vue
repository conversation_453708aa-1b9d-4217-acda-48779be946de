<route-meta>
{ "title": "新建课程包" }
</route-meta>
<template>
  <el-main>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/training/course-package')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/course-package' }">课程包管理</el-breadcrumb-item>
      <el-breadcrumb-item>新建课程包</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit f-p20">
          <el-col :md="14" :lg="13" :xl="10">
            <el-form
              ref="addForm"
              :model="mutationCreateCoursePackage.createCoursePackageVo"
              :rules="checkRules"
              label-width="140px"
              class="m-form"
            >
              <div :class="{ isRed: isInputRed }">
                <el-form-item label="课程包名称：" prop="name">
                  <el-input
                    @focus="isInputRed = false"
                    v-model="mutationCreateCoursePackage.createCoursePackageVo.name"
                    placeholder="请输入课程包名称"
                    clearable
                    :style="{ width: '100%' }"
                  >
                  </el-input>
                </el-form-item>
              </div>
              <el-form-item label="展示名称：">
                <el-input
                  v-model="mutationCreateCoursePackage.createCoursePackageVo.showName"
                  placeholder="请输入展示名称"
                  clearable
                  :style="{ width: '100%' }"
                >
                </el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-row :gutter="15" class="is-height f-mb15">
        <el-col :md="8">
          <!-- 课程分类 -->
          <workType v-model="workValue"></workType>
        </el-col>
        <el-col :md="16">
          <course-choose
            ref="chooseCourseComponent"
            :course-param="courseParam"
            :create-course-package="mutationCreateCoursePackage.createCoursePackageVo"
            :change-data-num="changeDataNum"
          ></course-choose>
        </el-col>
      </el-row>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="f-clear">
          <span class="tit-txt f-fl"
            >已选待确认课程（共 {{ chooseCourseLength }} 门 ， {{ chooseCoursePeriod }} 学时）</span
          >
          <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
            <i class="el-icon-info m-tooltip-icon f-c9"></i>
            <div slot="content">
              课程学习学时必须大于0，支持小数点后一位，如需调整课程的展示顺序，可长按具体课程拖拽至想要的位置。
            </div>
          </el-tooltip>
        </div>
        <wait-choose-course
          :course-param="courseParam"
          :added-list="mutationCreateCoursePackage.createCoursePackageVo.addedList"
          :create-course-package="mutationCreateCoursePackage.createCoursePackageVo"
          @packageChange="getPackageChange"
        ></wait-choose-course>
      </el-card>
      <div class="m-btn-bar f-mt10 f-tc is-sticky-1">
        <el-button @click="$router.push('/training/course-package')">取消</el-button>
        <el-button
          type="primary"
          style="width: 100px"
          @click="createCoursePool"
          :disabled="uiConfig.saveButton"
          class="lg"
          v-loading="uiConfig.saveButton"
        >
          保存
        </el-button>
      </div>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import Page from '@hbfe/jxjy-admin-common/src/models/Page'
  import { ElForm } from 'element-ui/types/form'
  import workType from '@hbfe/jxjy-admin-coursePackage/src/components/courseType.vue'
  import CourseChoose from '@hbfe/jxjy-admin-coursePackage/src/components/course-choose.vue'
  import WaitChooseCourse from '@hbfe/jxjy-admin-coursePackage/src/components/wati-choose-course.vue'
  import CourseCategoryListDetail from '@api/service/management/resource/course-category/query/vo/CourseCategoryListDetail'
  import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'
  import MutationCreateCoursePackage from '@api/service/management/resource/course-package/mutation/MutationCreateCoursePackage'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { bind, debounce } from 'lodash-decorators'
  import { forEach } from 'lodash'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'

  @Component({
    components: {
      CourseChoose,
      workType,
      WaitChooseCourse
    }
  })
  export default class extends Vue {
    @Ref('addForm') addForm: ElForm
    @Ref('chooseCourseComponent') chooseCourseComponent: any
    uiConfig = {
      saveButton: false,
      giveUpCtrl: false
    }
    mutationCreateCoursePackage: MutationCreateCoursePackage

    constructor() {
      super()
      this.mutationCreateCoursePackage = ResourceModule.coursePackageFactory.createCoursePackage
    }

    changeDataNum = 1
    index = 1
    formCheck = false
    study = '222'
    page: Page = new Page()
    flag = false
    CourseCategoryListDetail: CourseCategoryListDetail = new CourseCategoryListDetail()
    workValue = '' // 选中的工种分类
    chooseData = new Array<CourseListDetail>()
    chooseCourseLength = 0
    chooseCoursePeriod = 0
    isInputRed = false

    get courseParam() {
      return this.workValue
    }

    @Watch('courseParam', {
      immediate: false,
      deep: true
    })
    courseParamChange(val: any) {
      console.log(val, 'courseParam1111')
    }

    @Watch('mutationCreateCoursePackage.createCoursePackageVo.addedList', {
      immediate: true,
      deep: true
    })
    addlistChange(val: any) {
      if (val) {
        this.chooseCoursePeriod = 0
        val.forEach((item: any) => {
          this.chooseCoursePeriod = CalculatorObj.add(this.chooseCoursePeriod, item.period)
        })
        this.chooseCourseLength = val.length
      }
    }

    async checkName(rule: any, value: any, callback: any) {
      const res: any = await ResourceModule.coursePackageFactory.getCheckPackage(value)
      if (res?.code !== '200') {
        return callback('课程包名称重复，请修改')
      }
      return callback()
    }

    // 表单校验规则
    checkRules = {
      name: [
        { required: true, message: '课程包名称不可为空！', trigger: ['change', 'blur'] },
        {
          validator: async (rule: any, value: any, callback: any) => {
            await this.checkName(rule, value, callback)
          },
          trigger: 'blur'
        }
      ]
      // showName: [
      //   {
      //     required: true,
      //     message: '课程包展示名称必填！',
      //     trigger: 'blur'
      //   }
      // ]
    }

    async createCoursePoolCheck() {
      return new Promise((resolve) => {
        this.addForm.validate((valid: any) => {
          this.formCheck = valid
          resolve(valid)
        })
      })
    }

    @bind
    @debounce(200)
    async createCoursePool() {
      if (this.uiConfig.saveButton) return
      this.uiConfig.saveButton = true
      const check = await this.createCoursePoolCheck()
      if (!check) {
        // this.$message.warning('课程包名称不能为空')
        this.isInputRed = true
        this.uiConfig.saveButton = false
        return
      }
      const res = await this.mutationCreateCoursePackage.doCreate()
      if (!res.isSuccess()) {
        // this.$message.error(res.message.toString())
        this.uiConfig.saveButton = false
        this.$confirm(res.message.toString(), '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.isInputRed = true
        })
      } else {
        this.$message.success('创建成功')
        setTimeout(() => {
          this.$router.push('/training/course-package')
          this.uiConfig.saveButton = false
        }, 1000)
      }
      // this.uiConfig.saveButton = false
    }

    getPackageChange() {
      this.changeDataNum++
    }
  }
</script>

<style scoped>
  .isRed ::v-deep .el-input__inner {
    border-color: #f56c6c;
  }
</style>
