import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-shortmessage-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-shortmessage-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 校验短信授权配置是否启用响应
<AUTHOR> By Cb
@since 2022/6/14 14:03
 */
export class VerifyAuthConfigEnableResponse {
  /**
   * 是否启用
   */
  enable: boolean
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 校验短信授权配置是否启用
   * @return {@link VerifyAuthConfigEnableResponse}
   * <AUTHOR> By Cb
   * @date 2022/6/14 14:06
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async verifyAuthConfigEnable(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.verifyAuthConfigEnable,
    operation?: string
  ): Promise<Response<VerifyAuthConfigEnableResponse>> {
    return commonRequestApi<VerifyAuthConfigEnableResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
