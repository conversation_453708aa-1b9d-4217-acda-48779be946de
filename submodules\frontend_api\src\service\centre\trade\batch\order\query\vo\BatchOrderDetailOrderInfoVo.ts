import { BatchOrderStatusEnum } from '@api/service/centre/trade/batch/order/enum/BatchOrderStatusList'
import { PayModeEnum } from '@api/service/centre/trade/batch/order/enum/PayModeList'

/**
 * @description 批次订单-订单信息
 */
class BatchOrderDetailOrderInfoVo {
  /**
   * 批次单号
   */
  batchOrderNo = ''

  /**
   * 订单状态
   */
  batchOrderStatus: BatchOrderStatusEnum = null

  /**
   * 支付方式 1：线上支付 3：线下支付
   */
  payMode: PayModeEnum = null

  /**
   * 支付渠道名称 支付宝：支付宝
   */
  payChannelName = ''

  /**
   * 汇款凭证示意小图
   */
  remittanceVoucherUrlList: string[] = []
}

export default BatchOrderDetailOrderInfoVo
