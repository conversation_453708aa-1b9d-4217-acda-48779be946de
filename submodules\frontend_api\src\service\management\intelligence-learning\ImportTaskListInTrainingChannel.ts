import { Page, ResponseStatus } from '@hbfe/common'
import TaskTrackingList from '@api/service/management/intelligence-learning/model/TaskTrackingList'
import ImportTaskParams from '@api/service/management/intelligence-learning/model/ImportTaskParams'
import MsImportopenGateway from '@api/ms-gateway/ms-importopen-v1'
import ImportTaskItem from '@api/service/management/intelligence-learning/model/ImportTaskItem'

/**
 * 导入任务-专题管理员
 */
export default class ImportTaskListInTrainingChannel extends TaskTrackingList<ImportTaskParams, ImportTaskItem> {
  constructor() {
    super(new ImportTaskParams())
  }
  /**
   * 查询导入任务列表
   */
  async queryList(page: Page) {
    const { status, data } = await MsImportopenGateway.findTaskExecuteWithSelfResponseByPage({
      param: ImportTaskParams.to(this.queryParam),
      page
    })
    if (status.isSuccess() && data) {
      page.totalPageSize = data.totalPageSize
      page.totalSize = data.totalSize
      this.list = data.currentPageData.map(ImportTaskItem.from)
      return status
    } else {
      this.list = []
      page.totalPageSize = 0
      page.totalSize = 0
      return new ResponseStatus(500, '列表查询失败')
    }
  }
}
