<template>
  <div>
    <el-form ref="form" label-width="auto" class="m-text-form f-ml30 f-mt30">
      <el-col :span="12">
        <el-form-item label="考核要求：">
          <span v-if="inAssessment">调研问卷纳入考核，按具体问卷要求提交。</span>
          <span v-else>无。</span>
        </el-form-item>
      </el-col>
    </el-form>
    <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15" v-loading="loading" ref="tableRef">
      <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
      <el-table-column label="问卷名称" min-width="240">
        <template v-slot="{ row }">
          <p>{{ row.questionnaireName || '-' }}</p>
          <el-tag type="danger" size="mini" v-if="!row.enable">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="应用范围" min-width="120">
        <template v-slot="{ row }">
          <span v-text="questionnaireUseRange(row)"></span>
        </template>
      </el-table-column>
      <el-table-column label="是否纳入考核" min-width="120">
        <template v-slot="{ row }">{{ row.inAssessment ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column label="是否强制完成" min-width="120">
        <template v-slot="{ row }">
          <div v-if="!row.inAssessment">{{ row.isForced ? '是' : '-' }}</div>
          <div v-else>-</div>
        </template>
      </el-table-column>
      <el-table-column label="问卷开放时间" min-width="240" align="center">
        <template v-slot="{ row }">
          <div v-if="isLongTime(getObjValue(row.openTime, 'begin'), getObjValue(row.openTime, 'end'))">长期有效</div>
          <div v-else>
            <p>
              <el-tag type="info" size="mini">开始</el-tag><span v-text="getObjValue(row.openTime, 'begin')"></span>
            </p>
            <p><el-tag type="info" size="mini">结束</el-tag><span v-text="getObjValue(row.openTime, 'end')"></span></p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" min-width="120" align="center">
        <template v-slot="{ row }">
          <!--          存在模拟学习数据时，出现红色标识，鼠标移入查看模拟学习数据显示该门课程的开始学习时间、结束学习时间模拟学习数据-->
          <!--          <el-tooltip class="item" effect="dark" content="同步第三方数据：2025-02-12 09:23:25" placement="top-start">-->
          <!--            <el-badge is-dot type="danger" class="badge-status"></el-badge>-->
          <!--          </el-tooltip>-->
          <span>{{ row.submitTime || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提交状态" min-width="120" align="center" fixed="right">
        <template v-slot="{ row }">
          <div v-if="row.submitStatus == QuestionnaireSubmitStatusEnum.submit">
            <el-badge is-dot type="primary" class="badge-status">已提交</el-badge>
          </div>
          <div v-if="row.submitStatus == QuestionnaireSubmitStatusEnum.notSubmit">
            <el-badge is-dot type="danger" class="badge-status">未提交</el-badge>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="120" align="center" fixed="right">
        <template v-slot="{ row }">
          <el-button
            type="text"
            size="mini"
            @click="previewQuestion(row.answerPaperId, row.issueId)"
            :disabled="row.submitStatus == QuestionnaireSubmitStatusEnum.notSubmit"
            >查看问卷</el-button
          >
          <el-button
            type="text"
            size="mini"
            @click="answerReport(row.questionnaireId)"
            :disabled="row.submitStatus == QuestionnaireSubmitStatusEnum.notSubmit"
            >答题记录</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex; justify-content: flex-end">
      <hb-pagination class="f-mt15 f-tr" :page="page" v-bind="page"> </hb-pagination>
    </div>
  </div>
</template>

<script lang="ts">
  import { Vue, Component, Prop } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import QueryStudentTrainClass from '@api/service/management/train-class/query/QueryStudentTrainClass'
  import StudentQuestionnaireItem from '@api/service/management/train-class/offlinePart/model/StudentQuestionnaireItem'
  import { QuestionnaireUseRangeEnum } from '@api/service/management/train-class/offlinePart/enum/QuestionnaireUseRangeEnum'
  import { QuestionnaireSubmitStatusEnum } from '@api/service/management/train-class/offlinePart/enum/QuestionnaireSubmitStatusEnum'
  import StudentTrainClassDetailVo from '@api/service/management/train-class/query/vo/StudentTrainClassDetailVo'
  import QueryStudentStudy from '@api/service/management/train-class/offlinePart/QueryStudentStudy'
  @Component({
    components: {}
  })
  export default class extends Vue {
    /**
     * @description 学号
     * */
    @Prop({
      type: String,
      default: ''
    })
    studentNo: string

    /**
     * @description 基础信息
     * */
    @Prop({
      type: Object,
      default: () => {
        return new StudentTrainClassDetailVo()
      }
    })
    baseInfoDetail: StudentTrainClassDetailVo

    /**
     * @description 数据加载
     * */
    loading = false

    /**
     * @description 调研问卷数据
     * */
    tableData = new Array<StudentQuestionnaireItem>()

    /**
     * @description 查询调研问卷类
     * */
    queryStudentTrainClassVo = new QueryStudentTrainClass()

    /**
     * @description 应用范围枚举
     * */
    QuestionnaireUseRangeEnum = QuestionnaireUseRangeEnum

    /**
     * @description 提交状态枚举
     * */
    QuestionnaireSubmitStatusEnum = QuestionnaireSubmitStatusEnum

    /**
     * @description 应用范围枚举转文字
     * */
    get questionnaireUseRange() {
      return (row: StudentQuestionnaireItem) => {
        // 线上课程
        if (row.questionnaireRange == QuestionnaireUseRangeEnum.online) return '线上课程'
        // 当前方案
        if (row.questionnaireRange == QuestionnaireUseRangeEnum.scheme) return '培训方案'
        // 培训期别
        if (row.questionnaireRange == QuestionnaireUseRangeEnum.period) {
          return this.baseInfoDetail.getIssueNameByQuestionnaireId(row.questionnaireId)
        }
        if (row.questionnaireRange == QuestionnaireUseRangeEnum.all_period) {
          return '培训期别'
        }
        return '-'
      }
    }

    /**
     * @description 对象取数
     * */
    get getObjValue() {
      return (obj: object, key: string) => {
        const hasValue = typeof obj === 'object' && obj !== null && Object.keys(obj).length > 0
        if (hasValue) return obj[key] ?? '-'
        return '-'
      }
    }
    /**
     * @description 时间是否长期有效
     * */
    get isLongTime() {
      return (beginTime: string, endTime: string) => {
        const begin = new Date(beginTime).getTime()
        const beginLongTime = new Date('1900-01-01 00:00:00').getTime()
        const end = new Date(endTime).getTime()
        const endLongTime = new Date('2100-01-01 00:00:00').getTime()
        return begin === beginLongTime && end === endLongTime
      }
    }

    /**
     * @description 是否纳入考核
     * */
    get inAssessment() {
      const { periodId, schemeQuestionnaire } = this.baseInfoDetail?.periodStudy || {}
      const require = schemeQuestionnaire?.require
      return (periodId && this.baseInfoDetail?.getIssueConfigQuestionnaireAssessRequire(periodId)) || require || false
    }

    page: UiPage
    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
    }

    /**
     * @description 解决时序问题
     * @mark 由于外部使用ref调用，在初始化时，ref获取为undefined导致查询报错，补充此钩子，在初始化时手动调用查询方法，后续操作直接触发init，不会触发此钩子
     * */
    created() {
      this.init()
    }

    /**
     * @mark Ref调用 查询方法
     * */
    init() {
      this.doQuery()
    }

    /**
     * @mark Ref调用 清除数据方法
     * */
    clearList() {
      this.tableData = new Array<StudentQuestionnaireItem>()
    }

    /**
     * @description 查询调研问卷数据
     * */
    async doQuery() {
      try {
        if (!this.studentNo) throw new Error('学号为空')
        this.loading = true
        // const res = await this.queryStudentTrainClassVo.queryStudentQuestionnaire(this.studentNo, this.page)
        const res = await QueryStudentStudy.queryStudentQuestionnaire(this.studentNo, this.page)
        this.tableData = res.questionnaireContentList
      } catch (e) {
        console.log(e, '=====err')
        this.$message.error('调研问卷数据加载失败！')
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['tableRef'] as any)?.doLayout()
        this.loading = false
      }
    }

    previewQuestion(answerPaperId: string, issueId: string) {
      window.open(`/admin#/training/scheme/questionAnswer?id=${answerPaperId}&issueId=${issueId}`, '_blank')
    }
    answerReport(questionnaireId: string) {
      this.$router.push(`/training/scheme/holisticReport/${questionnaireId}?otherRouter=true`)
    }
  }
</script>
