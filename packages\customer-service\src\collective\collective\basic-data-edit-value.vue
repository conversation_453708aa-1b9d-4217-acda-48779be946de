<!--
 * @Author: WRP
 * @Date: 单个属性修改输入框
-->
<template>
  <span>
    <el-tooltip class="item" effect="dark" placement="top">
      <span class="el-icon-edit-outline f-c9 edit-icon" @click="isShowEditbox = true"></span>
      <div slot="content">编辑</div>
    </el-tooltip>
    <div class="edit-box" v-if="isShowEditbox">
      <el-input v-model="editInputValue" clearable :placeholder="placeholder" class="f-flex-sub" />
      <div class="op">
        <el-tooltip class="item" effect="dark" placement="top">
          <span class="el-icon-circle-check f-cb edit-icon" @click="saveEdit"></span>
          <div slot="content">保存</div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" placement="top">
          <span class="el-icon-circle-close f-c9 edit-icon" @click="cancelEdit"></span>
          <div slot="content">取消</div>
        </el-tooltip>
      </div>
    </div>
  </span>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    // 修改的值
    editInputValue = ''
    // 输入框显隐
    isShowEditbox = false
    // 修改后的值
    newValue = ''

    // 输入框提示语
    @Prop({
      type: String,
      default: '请输入证件号'
    })
    placeholder: string

    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Watch('value', {
      deep: true
    })
    valChange(val: string) {
      this.editInputValue = val
    }

    // 修改的值
    @Watch('editInputValue', {
      deep: true
    })
    inputValueChange(val: string) {
      this.newValue = val
    }

    // 确认修改属性值
    saveEdit() {
      this.$emit('input', this.newValue)
      this.$emit('saveEdit', true)
      this.isShowEditbox = false
    }

    // 放弃修改属性值
    cancelEdit() {
      this.newValue = ''
      this.editInputValue = this.value || ''
      this.isShowEditbox = false
    }
  }
</script>
