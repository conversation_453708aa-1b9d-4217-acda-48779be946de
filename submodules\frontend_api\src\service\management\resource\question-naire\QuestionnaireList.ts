import QuestionnaireItem from '@api/service/management/resource/question-naire/models/QuestionnaireItem'
import QuestionnaireQueryParam from '@api/service/management/resource/question-naire/models/QuestionnaireQueryParam'
import QuestionnaireDetail from '@api/service/management/resource/question-naire/QuestionnaireDetail'
import { Page } from '@hbfe/common'
import MsExamextraction from '@api/ms-gateway/ms-examextraction-v1'
import PlatformQuestionnaireQueryBackStage from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'
export default class QuestionnaireList {
  /**
   * 筛选项
   */
  params: QuestionnaireQueryParam = new QuestionnaireQueryParam()
  /**
   * 列表
   */
  list: Array<QuestionnaireItem> = new Array<QuestionnaireItem>()

  /**
   * 查询列表
   */
  async queryList(page: Page) {
    const request = QuestionnaireQueryParam.to(this.params)
    const res = await PlatformQuestionnaireQueryBackStage.pageQuestionnaireTemplateInSchool({ page, request })
    this.list = []
    if (res.status.isSuccess()) {
      this.list = res.data?.currentPageData.map((item) => {
        return QuestionnaireItem.from(item)
      })
    }
    page.totalPageSize = res.data?.totalPageSize || 0
    page.totalSize = res.data?.totalSize || 0
  }
  /**
   * 复制问卷列表的问卷第一步 只查询
   * @param id 问卷id
   */
  async copy(id: string) {
    // 要查详情 然后详情对应模型给UI 创建问卷需要用到试题的id
    const detail = new QuestionnaireDetail()
    detail.id = id
    await detail.queryDetail()
    return detail
  }

  /**
   * 删除问卷
   * @param id 问卷id
   */
  async delete(id: string) {
    return await MsExamextraction.removePaperPublishConfigure(id)
  }
}
