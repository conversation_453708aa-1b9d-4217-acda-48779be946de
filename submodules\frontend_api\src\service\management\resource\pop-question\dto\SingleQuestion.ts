import QuestionChoiceOption from '@api/service/management/resource/pop-question/dto/QuestionChoiceOption'
import QuestionDTO from '@api/service/management/resource/pop-question/dto/QuestionDTO'

const minCount = 2

export class SingleQuestion extends QuestionDTO {
  constructor() {
    super()
    this.questionChoiceOptions = new Array<QuestionChoiceOption>(2)
    this.questionChoiceOptions.map(() => new QuestionChoiceOption())
  }

  get choiceItemCount(): number {
    return this._choiceItemCount
  }

  set choiceItemCount(value: number) {
    if (value < minCount) {
      throw new Error('设置必须大于2')
    }
    if (value > this._choiceItemCount) {
      const addItems = new Array<QuestionChoiceOption>(value - this._choiceItemCount)
      addItems.map(() => new QuestionChoiceOption())
      this.questionChoiceOptions.concat(addItems)
    } else {
      const deleteCount = this._choiceItemCount - value
      this.questionChoiceOptions = this.questionChoiceOptions.splice(value, deleteCount)
    }
    this._choiceItemCount = value
  }

  private _choiceItemCount = minCount
  questionChoiceOptions: Array<QuestionChoiceOption> = new Array<QuestionChoiceOption>()
  correctChoice: string
}
