<template>
  <manage-diff ref="manageRef" v-bind="$attrs" v-on="$listeners">
    <template #operating-button-slot="{ scope }">
      <template v-if="$hasPermission('enrollClose')" desc="立即下架" actions="vaildOnShelvesFrom">
        <el-button
          v-show="scope.row.onShelve.shelveStatus === 1"
          type="text"
          size="mini"
          @click="vaildOnShelvesFrom(scope.row, false)"
          >立即下架</el-button
        >
      </template>
      <template v-if="$hasPermission('enrollOpen')" desc="立即上架" actions="vaildOnShelvesFrom">
        <el-button
          v-show="scope.row.onShelve.shelveStatus === 0"
          type="text"
          size="mini"
          @click="vaildOnShelvesFrom(scope.row, true)"
          >立即上架</el-button
        >
      </template>
      <template v-if="$hasPermission('delete')" desc="删除" actions="deleteScheme">
        <el-button type="text" size="mini" @click="deleteScheme(scope.row)">删除</el-button>
      </template>
      <template
        v-if="$hasPermission('mergeRegistration')"
        desc="合并报名管理"
        actions="@hbfe/jxjy-admin-scheme/src/diff/qztg/merge-registration.vue"
      >
        <el-button
          type="text"
          size="mini"
          @click="handleMerge(scope.row)"
          v-if="scope.row.skuValueNameProperty.trainingMode.skuPropertyValueId === TrainingModeEnum.online"
          >合并报名管理</el-button
        >
      </template>
    </template>
  </manage-diff>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  import SchemeManage from '@hbfe/jxjy-admin-scheme/src/manage.vue'
  import UITrainClassCommodityDetail from '@hbfe/jxjy-admin-scheme/src/models/UITrainClassCommodityDetail'
  import FullScreen from '@hbfe/jxjy-admin-common/src/util/FullScreen'
  import { bind, debounce } from 'lodash-decorators'
  import QueryTrainClassCommodityList from '@api/service/diff/management/qztg/train-class/query/QueryTrainClassCommodityList'
  import QueryTrainClassDetailClass from '@api/service/diff/management/qztg/train-class/query/QueryTrainClassDetailClass'
  import QztgDiffCommoditySkuRequest from '@api/service/diff/management/qztg/train-class/query/vo/QztgDiffCommoditySkuRequest'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

  @Component
  class ManageDiff extends SchemeManage {
    // 培训方案业务状态层入口
    QueryTrainClassCommodityListDiff = new QueryTrainClassCommodityList()

    async doExportScheme() {
      return await this.QueryTrainClassCommodityListDiff.exportCommoditySkuInServicer(
        this.trainSchemeQueryParam,
        this.sortPolicy
      )
    }
  }

  @Component({
    components: {
      ManageDiff
    }
  })
  export default class extends Vue {
    @Ref('manageRef') manageRef: ManageDiff

    // 培训方案业务状态层入口
    QueryTrainClassCommodityList = new QueryTrainClassCommodityList()

    /**
     * @description 培训形式枚举
     * */
    TrainingModeEnum = TrainingModeEnum

    /**
     * 校验方案上/下架 弹窗表单
     */
    async vaildOnShelvesFrom(row: UITrainClassCommodityDetail, futureStatus: boolean) {
      let msg = ''
      let confirmButtonText = ''
      if (futureStatus) {
        msg = '确定立即上架该培训方案？'
        confirmButtonText = '确定上架'
      } else {
        const state = await this.doQueryCommodityIsReferenced(row.commoditySkuId)
        console.log('state', state)
        msg = state
          ? '当前方案为合并报名方案，下架后将导致学员端无法报名主方案。确定立即下架该培训方案？'
          : '确定立即下架该培训方案？'
        confirmButtonText = '确定下架'
      }
      this.$confirm(msg, '提示', {
        confirmButtonText: confirmButtonText,
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.doVaildOnShelvesFrom(row, futureStatus)
        })
        .catch(() => {
          console.log('取消')
        })
    }

    async doVaildOnShelvesFrom(row: UITrainClassCommodityDetail, futureStatus: boolean) {
      // this.MutationTrainClassCommodityClass.commoditySkuId = row.commoditySkuId
      this.manageRef.vaildOnShelvesFrom(row, futureStatus)
    }

    /**
     * 删除培训方案
     */
    @bind
    @debounce(200)
    async deleteScheme(row: UITrainClassCommodityDetail) {
      const state = await this.doQueryCommodityIsReferenced(row.commoditySkuId)
      const msg = state
        ? '当前方案为合并报名方案，删除后将从主方案的合并报名方案中移除，主方案和其他合并报名方案仍然可以报名。是否确认删除？'
        : '删除后需要重新创建培训班，是否确认删除？'
      this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.doDeleteScheme(row)
        })
        .catch(() => {
          console.log('取消')
        })
    }

    async doDeleteScheme(row: UITrainClassCommodityDetail) {
      this.manageRef.deleteScheme(row)
    }

    /**
     * 培训方案商品判断是否被引用
     * @param commoditySkuId
     */
    async doQueryCommodityIsReferenced(commoditySkuId: string) {
      const state = await QueryTrainClassDetailClass.queryCommodityIsReferenced(commoditySkuId)
      return state
    }

    /**
     * 合并报名管理
     */
    handleMerge(row: UITrainClassCommodityDetail) {
      this.manageRef.setScrollPosition()
      FullScreen.closeFull()
      this.$router.push('/training/scheme/merge-registration/' + row.commoditySkuId)
    }
  }
</script>
