<template>
  <div>
    <el-form
      ref="checkPhoneRef"
      :model="checkPhone"
      :rules="checkPhoneRules"
      @keyup.native.enter="doContinueLogging"
      label-width="auto"
      class="m-form f-mt30"
    >
      <el-form-item>
        <el-alert type="warning" show-icon :closable="false" class="m-alert"
          >当前登录用户绑定手机号:{{ phone }}</el-alert
        >
      </el-form-item>
      <el-form-item prop="captcha">
        <div class="f-flex">
          <el-input v-model="checkPhone.captcha" clearable placeholder="请输入图形验证码" class="f-flex-sub">
            <i slot="prefix" class="el-input__icon iconfont icon-yanzhengma"></i>
          </el-input>
          <div class="code">
            <img :src="validateCodePic" alt=" " @click="refreshValidateCodePic" title="看不清，点击刷新" />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="" prop="smsCode">
        <div class="f-flex">
          <el-input
            v-model="checkPhone.smsCode"
            :disabled="isCaptchaValid || !isGetCode"
            clearable
            placeholder="请输入短信校验码"
            class="f-flex-sub"
          >
            <i slot="prefix" class="el-input__icon iconfont icon-yanzhengma"></i>
          </el-input>
          <div class="code">
            <el-button type="primary" plain v-if="sending" :disabled="isCaptchaValid" @click="getPhoneCaptcha"
              >获取短信验证码</el-button
            >
            <el-button type="info" plain disabled v-if="!sending">重新获取（{{ countTime }}s）</el-button>
          </div>
        </div>
      </el-form-item>
      <el-form-item class="m-btn-bar">
        <el-button
          type="primary"
          :disabled="isCaptchaValid"
          :loading="continueLoading || loading"
          @click="doContinueLogging"
          >继续登录</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts">
  import { Vue, Component, Prop, Watch, Ref } from 'vue-property-decorator'
  import { ElForm } from 'element-ui/types/form'
  import { BusinessTypeEnum } from '@hbfe-biz/biz-authentication/dist/enums/BusinessTypeEnum'
  import { CaptchaApplyRequest, SmsCodeApplyRequest } from '@hbfe-ms/ms-basicdata-domain-gateway'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import Authentication from '@api/service/common/authentication/Authentication'
  import { SmsCodeCredentialRequest } from '@api/ms-gateway/ms-identity-authentication-v1'
  export enum RoleType {
    ADMIN = 1,
    SERVICEPROVIDER = 2,
    REGIONADMINISTRATOR = 3,
    DISTRIBUTOR = 4
  }

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    continueLoading: boolean

    @Prop({
      type: String,
      default: ''
    })
    phone: string

    @Prop({
      type: String,
      default: ''
    })
    token: string

    @Prop({
      type: Number,
      default: 0
    })
    roleType: number

    @Ref('checkPhoneRef') checkPhoneRef: ElForm

    // 验证码发送
    sending = true
    // 预计倒计时时间
    count = 60
    // 发送后倒计时计时
    countTime = 60
    //图形验证码报错后请求次数
    imgIndex = 5
    checkPhone = {
      captcha: '',
      smsCode: ''
    }
    checkPhoneRules = {
      captcha: [
        {
          // 以大写小写字母或者数字组合的4位
          regexp: /^[a-zA-z0-9]{4}$/,
          validator: this.captchaValid,
          required: true,
          trigger: 'change'
        },
        { required: true, message: '请输入图形验证码', trigger: 'blur' }
      ],
      smsCode: {
        required: true,
        message: '请输入正确短信验证码',
        trigger: 'blur'
      }
    }
    validateCodePic = ''
    intervalTimer: any = null
    loading = false
    $authentication: Authentication
    isCaptchaValid = true
    // 是否获取验证码
    isGetCode = false
    created() {
      this.refreshValidateCodePic()
    }

    async captchaValid(rule: any, value: any, callback: (error?: Error) => void) {
      if (value && rule.regexp.test(value)) {
        try {
          const res = await this.$authentication.verify.msValidateCaptcha(value)
          console.log('这是图形验证码成功', res)
          if (res.status.isSuccess() && res.data.code == 200) {
            this.change(false)
            callback()
          } else {
            await this.refreshValidateCodePic()
            this.change(true)
            callback(new Error('请输入正确的图形验证码'))
          }
        } catch (e) {
          console.log(e)
          callback(new Error('请输入正确的图形验证码'))
          await this.refreshValidateCodePic()
          this.change(true)
        }
      } else {
        callback(new Error('验证码为4位数'))
      }
    }

    change(type: boolean) {
      this.isCaptchaValid = type
    }

    /**
     * 获取图片验证码
     */
    async refreshValidateCodePic() {
      try {
        const params = new CaptchaApplyRequest()
        params.token = this.token
        params.businessType = BusinessTypeEnum.login_account
        const res = await this.$authentication.verify.applyCaptcha(params)
        if (res.status.code == 200 && res.data.captcha) {
          this.validateCodePic = `data:image/jpeg;base64,${res.data.captcha}`
          this.imgIndex = 5
        } else {
          this.catchImg(res)
        }
      } catch (e) {
        this.catchImg(e)
      }
    }
    async catchImg(e: any) {
      this.imgIndex--
      if (this.imgIndex > 0) {
        await this.refreshValidateCodePic()
      } else {
        console.log(e, '连接失败')
        this.$message.warning('连接失败，请刷新重试')
        this.imgIndex = 5
      }
    }
    // 验证码倒计时
    countDown(time: number) {
      this.countTime = time
      console.log('countStart')
      this.intervalTimer = setInterval(() => {
        if (this.countTime === 0) {
          this.sending = true
          clearInterval(this.intervalTimer)
          this.countTime = this.count
        } else {
          this.countTime = this.countTime - 1
        }
      }, 1000)
    }

    // 发送手机短信验证码
    async getPhoneCaptcha() {
      this.isGetCode = false
      this.sending = false
      const params = new SmsCodeApplyRequest()
      params.phone = this.phone
      params.token = this.$authentication.verify.captchaToken
      params.businessType = BusinessTypeEnum.login_account
      const res = await this.$authentication.verify.msSendSmsCode(params)
      if (res.data.code != 200) {
        this.sending = true
        await this.refreshValidateCodePic()
      }
      if (res.status.code == 200 && res.status.isSuccess()) {
        this.countDown(60)
        this.$message.success('短信验证码已发送至手机，请查收！')
        this.isGetCode = true
      }
      if (res.data.code === 409) {
        this.$message.error('一个手机号一天只能接收4条短信，今日次数已用完，请隔天再试。')
      } else if (res.data.code === 410) {
        this.$message.error('发送验证码失败')
      } else if (res.data.code === 701) {
        this.$message.error('请重新验证图形验证码后，再次获取短信验证码')
      } else if (res.data.code === 515) {
        this.$message.error('不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)')
      } else if (res.data.code === 400) {
        this.$message.error('token无效')
      } else if (res.data.code === 509) {
        this.$message.error('未绑定手机号')
      } else if (res.data.code === 514) {
        this.$message.error('token中未携带手机号')
      }
    }

    /**
     * 继续登录
     */
    async doContinueLogging() {
      try {
        await this.checkPhoneRef.validateField('smsCode')
        if (!this.checkPhone.smsCode) return
        this.loading = true
        const smsCode = this.checkPhone.smsCode
        const res = await this.$authentication.verify.msValidSmsCode(smsCode, this.phone)
        console.log(res, 'msValidSmsCode')
        if (res.data.code !== 200) {
          return this.errerMassage(res.data.code, res.data?.message)
        }
        if (res.data.code == 200) {
          const loginParams = new SmsCodeCredentialRequest()
          loginParams.smsCode = this.checkPhone.smsCode
          loginParams.token = this.$authentication.verify.shortMessageCaptchaToken
          const response = await this.$authentication.doApplyAuthenticateBySmsCode(loginParams)
          console.log(response, 'doApplyAuthenticateBySmsCode')
          if (response.status.isSuccess() && response.data.code === '200') {
            if (!response.data?.identityAuthenticationToken) {
              this.$message.error('身份认证失败')
            } else {
              this.$emit('success')
            }
          } else {
            this.$message.error(response.data.message)
          }
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    desensitizePhoneNumber(phoneNumber: string) {
      if (phoneNumber.length < 5) {
        return phoneNumber // 如果字符串长度小于5，直接返回原字符串
      }
      // 获取前3位
      const prefix = phoneNumber.slice(0, 3)
      // 获取后2位
      const suffix = phoneNumber.slice(-2)
      // 计算中间需要被星号替代的部分长度
      const maskLength = phoneNumber.length - 5
      // 生成相应长度的星号字符串
      const maskedPart = '*'.repeat(maskLength)
      // 返回组合后的字符串
      return `${prefix}${maskedPart}${suffix}`
    }

    // 报错信息
    errerMassage(code: number, message: string) {
      switch (code) {
        case 400:
          this.$message.warning('错误的请求')
          break
        case 408:
          this.$message.warning('短信验证码已过期，请重新获取')
          break
        case 501:
          this.$message.warning('手机号或账号不存在')
          break
        case 502:
          this.$message.warning('手机号或账号异常')
          break
        case 503:
          this.$message.warning('姓名和账号不匹配')
          break
        case 504:
          this.$message.warning('姓名不存在')
          break
        case 506:
          this.$message.warning('可用账户不唯一')
          break
        case 509:
          this.$message.warning('未绑定手机号')
          break
        case 500:
          if (message === '验证码不匹配') {
            this.$message.warning('请输入正确的短信验证码')
          } else {
            this.$message.warning(message || '请求失败，请稍后重试！')
          }
          break
        default:
          this.$message.warning(message || '请求失败，请稍后重试！')
          break
      }
    }
  }
</script>
