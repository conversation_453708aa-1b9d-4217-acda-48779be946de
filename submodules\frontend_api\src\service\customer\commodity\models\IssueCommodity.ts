/**
 * 期别商品信息
 */
import { IssueDTO } from '@api/gateway/PlatformTrade'
import moment from 'moment'
import { Constants } from '@api/service/common/models/common/Constants'

class IssueCommodity {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 期数ID（对应前端期别）
   */
  issueId: string
  /**
   * 商品SkuId
   */
  commoditySkuId: string
  /**
   * 标题
   */
  title: string
  /**
   * 学习开始时间
   */
  startTime: Date
  /**
   * 学习结束时间
   */
  endTime: Date
  /**
   * 开放报名开始时间
   */
  upPlainTime: Date
  /**
   * 开放报名结束时间
   */
  downPlainTime: Date
  /**
   * 销售价格
   */
  price: number

  static from(issueDTO: IssueDTO): IssueCommodity {
    const issue = new IssueCommodity()
    issue.startTime = moment(issueDTO.startTime, Constants.DATE_PATTERN).toDate()
    issue.commoditySkuId = issueDTO.commoditySkuId
    issue.endTime = moment(issueDTO.endTime, Constants.DATE_PATTERN).toDate()
    if (issueDTO.upPlainTime?.length) {
      issue.upPlainTime = moment(issueDTO.upPlainTime, Constants.DATE_PATTERN).toDate()
    }
    if (issueDTO.downPlainTime?.length) {
      issue.downPlainTime = moment(issueDTO.downPlainTime, Constants.DATE_PATTERN).toDate()
    }
    issue.issueId = issueDTO.issueId
    issue.price = issueDTO.price
    issue.schemeId = issueDTO.schemeId
    issue.title = issueDTO.title
    return issue
  }
}

export default IssueCommodity
