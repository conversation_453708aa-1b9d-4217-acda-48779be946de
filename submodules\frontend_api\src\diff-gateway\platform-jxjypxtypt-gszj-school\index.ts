import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-gszj-school'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

export class StudentSchemeLearningRequest {
  student?: UserRequest
  learningRegister?: LearningRegisterRequest
  scheme?: SchemeRequest
  studentLearning?: StudentLearningRequest
  dataAnalysis?: DataAnalysisRequest
  connectManageSystem?: ConnectManageSystemRequest
  extendedInfo?: ExtendedInfoRequest
  openPrintTemplate?: boolean
  saleChannels?: Array<number>
  trainingChannelName?: string
  trainingChannelId?: string
  notDistributionPortal?: boolean
  trainingType?: string
}

export class ConnectManageSystemRequest {
  syncStatus?: number
}

export class DataAnalysisRequest {
  trainingResultPeriod?: DoubleScopeRequest
  requirePeriod?: DoubleScopeRequest
  acquiredPeriod?: DoubleScopeRequest
}

export class ExtendedInfoRequest {
  whetherToPrint?: boolean
  applyCompanyCode?: string
  policyTrainingSchemeId?: string
  policyTrainingSchemeName?: string
}

export class LearningRegisterRequest {
  registerType?: number
  sourceType?: string
  sourceId?: string
  status?: Array<number>
  registerTime?: DateScopeRequest
  saleChannels?: Array<number>
  orderNoList?: Array<string>
  subOrderNoList?: Array<string>
  batchOrderNoList?: Array<string>
  distributorId?: string
  portalId?: string
}

export class RegionRequest {
  province?: string
  city?: string
  county?: string
}

export class StudentLearningRequest {
  trainingResultList?: Array<number>
  trainingResultTime?: DateScopeRequest
  notLearningTypeList?: Array<number>
  courseScheduleStatus?: number
  examAssessResultList?: Array<number>
}

export class RegionSkuPropertyRequest {
  province?: string
  city?: string
  county?: string
}

export class RegionSkuPropertySearchRequest {
  region?: Array<RegionSkuPropertyRequest>
  regionSearchType?: number
}

export class SchemeRequest {
  schemeId?: string
  schemeIdList?: Array<string>
  schemeType?: string
  schemeName?: string
  skuProperty?: SchemeSkuPropertyRequest
}

export class SchemeSkuPropertyRequest {
  year?: Array<string>
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  industry?: Array<string>
  subjectType?: Array<string>
  trainingCategory?: Array<string>
  trainingProfessional?: Array<string>
  technicalGrade?: Array<string>
  positionCategory?: Array<string>
  trainingObject?: Array<string>
  jobLevel?: Array<string>
  jobCategory?: Array<string>
  subject?: Array<string>
  grade?: Array<string>
  learningPhase?: Array<string>
  discipline?: Array<string>
  qualificationCategory?: Array<string>
  trainingWay?: Array<string>
  trainingInstitution?: Array<string>
  mainAdditionalItem?: Array<string>
}

export class UserPropertyRequest {
  regionList?: Array<RegionRequest>
  companyName?: string
  payOrderRegionList?: Array<RegionRequest>
}

export class UserRequest {
  userIdList?: Array<string>
  accountIdList?: Array<string>
  userProperty?: UserPropertyRequest
}

/**
 * 批量导入试题查询请求
<AUTHOR>
 */
export class BatchImportQueryRequest {
  /**
   * 任务名称
   */
  taskName?: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState?: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult?: number
  /**
   * 执行时间（起始）
   */
  executeStartTime?: string
  /**
   * 执行时间（终止）
   */
  executeEndTime?: string
}

export class CreCourseSubjectRequest {
  /**
   * 课程Id
   */
  courseId?: string
  /**
   * 年度sku属性
   */
  year?: string
  /**
   * 地区sku属性
   */
  regional?: string
  /**
   * 科目类型sku属性
   */
  subjectType?: string
}

/**
 * @Description
@Date 2025/5/23
 */
export class ImportCourseSubjectRequest {
  /**
   * excel文件路径
   */
  filePath?: string
  /**
   * 文件名
   */
  fileName?: string
}

/**
 * 导入公需课返回值
<AUTHOR>
@date 2024/9/10 10:24
 */
export class CourseSubjectQuestionResponse {
  code: string
  message: string
  /**
   * 任务编号
   */
  importTaskResult: ImportTaskResult
}

/**
 * @Description
@Date 2025/5/22
 */
export class CreCourseSubjectResponse {
  /**
   * Id
   */
  id: string
  /**
   * 课程Id
   */
  courseId: string
  /**
   * 年度sku属性
   */
  year: string
  /**
   * 地区sku属性
   */
  regional: string
  /**
   * 科目类型sku属性
   */
  subjectType: string
}

/**
 * 各状态及执行结果对应数量
<AUTHOR>
 */
export class EachStateCount {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

/**
 * 批量导入查询响应
<AUTHOR>
 */
export class FindBatchImportByPageResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 【必填】服务商id
   */
  servicerId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务分类
   */
  category: string
  /**
   * 所属批次单编号
   */
  batchNo: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 就绪时间
   */
  alreadyTime: string
  /**
   * 执行时间
   */
  executingTime: string
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount>
}

/**
 * 导入任务结果
<AUTHOR>
@date 2024/9/10 10:25
 */
export class ImportTaskResult {
  /**
   * 任务id
   */
  taskId: string
}

export class FindBatchImportByPageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<FindBatchImportByPageResponse>
}

export class CreCourseSubjectResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CreCourseSubjectResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 甘肃专技批量导入公需课下载全部数据
   * @param mainTaskId
   * @return
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchExportAllChooseDataInServicer(
    mainTaskId: string,
    query: DocumentNode = GraphqlImporter.batchExportAllChooseDataInServicer,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 甘肃专技批量导入公需课查看失败数据
   * @param mainTaskId
   * @return
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchExportFailChooseDataInServicer(
    mainTaskId: string,
    query: DocumentNode = GraphqlImporter.batchExportFailChooseDataInServicer,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 甘肃专技删除当前服务商下的公需课
   * @param id
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteCourseSubjectByIdInServicer(
    id: string,
    query: DocumentNode = GraphqlImporter.deleteCourseSubjectByIdInServicer,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出学员学习明细报盘数据（甘肃欣宇云锦+八冶+天亿博） 按人课维度
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportStudentCourseLearningQuotationInServicer(
    request: StudentSchemeLearningRequest,
    query: DocumentNode = GraphqlImporter.exportStudentCourseLearningQuotationInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: true
      })
    )
  }

  /**   * 甘肃专技批量导入公需课任务分页查询查询
   * @param request
   * @param page
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findBatchImportByPageInServicer(
    params: { request?: BatchImportQueryRequest; page?: Page },
    query: DocumentNode = GraphqlImporter.findBatchImportByPageInServicer,
    operation?: string
  ): Promise<Response<FindBatchImportByPageResponsePage>> {
    return commonRequestApi<FindBatchImportByPageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 甘肃专技批量导入公需课课程
   * @param request
   * @return Boolean
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importCourseSubjectByExcelInServicer(
    request: ImportCourseSubjectRequest,
    query: DocumentNode = GraphqlImporter.importCourseSubjectByExcelInServicer,
    operation?: string
  ): Promise<Response<CourseSubjectQuestionResponse>> {
    return commonRequestApi<CourseSubjectQuestionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 甘肃专技分页查询当前服务商下的公需课
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCourseSubjectInServicer(
    params: { page?: Page; request?: CreCourseSubjectRequest },
    query: DocumentNode = GraphqlImporter.pageCourseSubjectInServicer,
    operation?: string
  ): Promise<Response<CreCourseSubjectResponsePage>> {
    return commonRequestApi<CreCourseSubjectResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
