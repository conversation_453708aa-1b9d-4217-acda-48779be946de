import QueryBatchOrderList from '@api/service/management/trade/batch/order/query/QueryBatchOrderList'
import QueryBatchOrderDetail from '@api/service/management/trade/batch/order/query/QueryBatchOrderDetail'
import QueryBatchOrderMainOrderDetail from '@api/service/management/trade/batch/order/query/QueryBatchOrderMainOrderDetail'
import QueryBatchRefund from './query/QueryBatchRefund'
import QueryRefundCauseList from './query/QueryRefundCauseList'

/**
 * 集体报名订单查询工厂
 */
class QueryOrderFactor {
  /**
   * 集体报名订单查询
   */
  get queryBatchOrderList() {
    return new QueryBatchOrderList()
  }

  /**
   * 集体报名订单详情查询
   */
  get queryBatchOrderDetail() {
    return new QueryBatchOrderDetail()
  }

  /**
   * 集体报名订单主单详情查询
   */
  get queryBatchOrderMainOrderDetail() {
    return new QueryBatchOrderMainOrderDetail()
  }
  /**
   * 集体报名退款查询  包含列表和详情 和 是否为强制退款
   */
  get queryBatchRefound() {
    return new QueryBatchRefund()
  }
  /**
   * 获取退款原因
   */
  get queryRefundCauseList() {
    return new QueryRefundCauseList()
  }
}
export default QueryOrderFactor
