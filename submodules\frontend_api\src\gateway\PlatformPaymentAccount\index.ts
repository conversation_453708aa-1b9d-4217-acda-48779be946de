import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/PlatformPaymentAccount'

// 枚举
export enum AuthorizationStateEnum {
  AUTHORIZATION = 'AUTHORIZATION',
  CANCEL_AUTHORIZATION = 'CANCEL_AUTHORIZATION'
}
export enum MerchantAccountCreateTypeForSearch {
  ALL = 'ALL',
  INTERNAL = 'INTERNAL',
  NORMAL = 'NORMAL'
}
export enum MerchantAccountStatusForSearch {
  ALL = 'ALL',
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 纳税人识别号查询
@author: eleven
@date: 2020/5/19
 */
export class TaxPayerRequest {
  /**
   * 名称
   */
  name?: string
  /**
   * 纳税人识别号
   */
  taxCode?: string
  /**
   * 发票地址
   */
  address?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 开户行
   */
  bank?: string
  /**
   * 账号
   */
  account?: string
}

/**
 * @author: eleven
@date: 2020/2/13
@description:
 */
export class PaymentAccountAddRequest {
  /**
   * 收款账户ID
   */
  id?: string
  /**
   * 收款账号（微信为商户号，支付宝为支付宝账号，建设银行为开户号）
   */
  accountNo?: string
  /**
   * 账户别名
   */
  accountAlias?: string
  /**
   * (只在线上收款账号有使用到) 1支付宝；2微信；3建行
   */
  createType: number
  /**
   * 证书密匙
   */
  merchantKey?: string
  /**
   * 支付宝为合作者ID:微信为公众号Id
   */
  appId?: string
  /**
   * 文件上传成功后的文件名称
   */
  privateKeyFileName?: string
  /**
   * 微信证书密匙
   */
  privateKeyPwd?: string
  /**
   * 微信证书地址
   */
  privateKeyPath?: string
  /**
   * 建行接口需要的POSID(商户柜台代码)
   */
  posId?: string
  /**
   * 建行接口需要的BRANCHID(分行代码)
   */
  branchBankId?: string
  /**
   * 建行网银支付接口的公钥
   */
  publicKey?: string
  /**
   * 建行的操作员账号不能为空
   */
  operator?: string
  /**
   * 建行操作员的登陆密码
   */
  password?: string
  /**
   * 账户一级类型代表的是支付方式(1:线上,2:线下)
只能是1或2
   */
  firstType: number
  /**
   * 企业名称（开户户名）
   */
  merchantName?: string
  /**
   * 开户银行
   */
  depositBank?: string
  /**
   * 开户银行的行号（开户号）
   */
  bankNumber?: string
  /**
   * 柜台号
   */
  counterNumber?: string
  /**
   * 账户类别 1.对私 2.对公
   */
  accountType: number
  /**
   * 支付宝应用私钥
   */
  alipayAppPrivateKey?: string
  /**
   * 支付宝公钥
   */
  alipayPublicKey?: string
  /**
   * 支付宝应用id
   */
  alipayAppId?: string
  /**
   * 纳税人识别号
   */
  taxPayerId?: string
}

export class MerchantAccountSearchCondition {
  projectId?: string
  platformId?: string
  platformVersionId?: string
  subProjectId?: string
  unitId?: string
  organizationId?: string
  accountAlias?: string
  accountNo?: string
  firstType?: number
  secondType?: string
  tradeChannelCode?: string
  createStartTime?: string
  createEndTime?: string
  certificateStartTime?: string
  certificateEndTime?: string
  status?: MerchantAccountStatusForSearch
  createType?: MerchantAccountCreateTypeForSearch
}

/**
 * 纳税人信息表
@author: eleven
@date: 2020/5/19
 */
export class TaxPayerResponse {
  /**
   * 主键ID
   */
  id: string
  /**
   * 名称
   */
  name: string
  /**
   * 纳税人识别号
   */
  taxCode: string
  /**
   * 发票地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bank: string
  /**
   * 账号
   */
  account: string
}

/**
 * @author: eleven
@date: 2020/2/13
@description:
 */
export class PaymentAccountInfo {
  /**
   * 主键ID
   */
  id: string
  /**
   * 收款账户别名
   */
  accountAlias: string
  /**
   * 收款账户的第三方接口的账号
   */
  accountNo: string
  /**
   * 账户一级类型代表的是支付方式(1:线上,2:线下)
只能是1或2
   */
  firstType: number
  /**
   * 交易渠道代码
   */
  tradeChannelCode: string
  /**
   * 二级类型(在一级类型的基础上拓展的描述在线下支付方式的情况下可能有&quot;对汇&quot;等)
如果没有填写为空
   */
  secondType: string
  /**
   * 开户银行
   */
  depositBank: string
  /**
   * 开户银行的行号
   */
  bankNumber: string
  /**
   * 柜台号
   */
  counterNumber: string
  /**
   * 企业名称
   */
  merchantName: string
  /**
   * 企业联系电话
   */
  merchantPhone: string
  /**
   * 商户第三方平台分配的签名时用到的密钥(不同接口可能没有)
   */
  merchantKey: string
  /**
   * 状态0:停用,1:启用
   */
  status: number
  /**
   * 收款账号创建方式 1:支付宝2:微信,3建设银行，4微信订阅号5微信小程序
   */
  createType: number
  /**
   * 账户类别，createType&#x3D;&#x3D;3 建设银行时有效 1.对私 2.对公
   */
  accountType: number
  /**
   * 文件上传成功后的文件名称
   */
  privateKeyFileName: string
  /**
   * 微信证书密匙
   */
  privateKeyPwd: string
  /**
   * 微信证书地址
   */
  privateKeyPath: string
  /**
   * 支付宝为合作者ID:微信为公众号Id
   */
  appId: string
  /**
   * 支付网关名称
   */
  tradeChannelName: string
  /**
   * 建行接口需要的POSID(商户柜台代码)
   */
  posId: string
  /**
   * 建行接口需要的BRANCHID(分行代码)
   */
  branchBankId: string
  /**
   * 建行网银支付接口的公钥
   */
  publicKey: string
  /**
   * 建行的操作员账号不能为空
   */
  operator: string
  /**
   * 支付宝应用私钥
   */
  alipayAppPrivateKey: string
  /**
   * 支付宝公钥
   */
  alipayPublicKey: string
  /**
   * 支付宝应用id
   */
  alipayAppId: string
  /**
   * 纳税人识别号id
   */
  taxPayerId: string
  /**
   * 纳税人识别号名字
   */
  taxPayerName: string
  /**
   * 创建单位
   */
  createUnitName: string
  /**
   * 是否为授权账号
   */
  fromAuthorize: boolean
  /**
   * 授权状态
   */
  authorizationState: AuthorizationStateEnum
  /**
   * 是否授权
   */
  hasAuthorize: boolean
  /**
   * 数据token
   */
  dataToken: string
  /**
   * 原收款账号id
   */
  rootId: string
}

export class PaymentAccountInfoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PaymentAccountInfo>
}

export class TaxPayerResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TaxPayerResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 是否允许修改收款账号
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async allowUpdate(
    id: string,
    query: DocumentNode = GraphqlImporter.allowUpdate,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: { id },
      operation: operation
    })
  }

  /**   * 是否支持真正电子票
   * @param
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableElectronInvoice(
    query: DocumentNode = GraphqlImporter.enableElectronInvoice,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 获取收款账号
   * @param id 收款账号Id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async get(
    id: string,
    query: DocumentNode = GraphqlImporter.get,
    operation?: string
  ): Promise<Response<PaymentAccountInfo>> {
    return commonRequestApi<PaymentAccountInfo>(SERVER_URL, {
      query: query,
      variables: { id },
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param page 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async page(
    page: Page,
    query: DocumentNode = GraphqlImporter.page,
    operation?: string
  ): Promise<Response<PaymentAccountInfoPage>> {
    return commonRequestApi<PaymentAccountInfoPage>(SERVER_URL, {
      query: query,
      variables: { page },
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageByarams(
    params: { page?: Page; queryParams?: MerchantAccountSearchCondition },
    query: DocumentNode = GraphqlImporter.pageByarams,
    operation?: string
  ): Promise<Response<PaymentAccountInfoPage>> {
    return commonRequestApi<PaymentAccountInfoPage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageTaxPayer(
    params: { page?: Page; request?: TaxPayerRequest },
    query: DocumentNode = GraphqlImporter.pageTaxPayer,
    operation?: string
  ): Promise<Response<TaxPayerResponsePage>> {
    return commonRequestApi<TaxPayerResponsePage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 验证收款账号别名
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validateName(
    params: { id?: string; name?: string },
    query: DocumentNode = GraphqlImporter.validateName,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async create(
    request: PaymentAccountAddRequest,
    mutate: DocumentNode = GraphqlImporter.create,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**   * 删除收款账号
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async remove(
    id: string,
    mutate: DocumentNode = GraphqlImporter.remove,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: mutate,
      variables: { id },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async update(
    request: PaymentAccountAddRequest,
    mutate: DocumentNode = GraphqlImporter.update,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**   * 停用/启用收款账号
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateEnable(
    params: { id?: string; enable: boolean },
    mutate: DocumentNode = GraphqlImporter.updateEnable,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }
}

export default new DataGateway()
