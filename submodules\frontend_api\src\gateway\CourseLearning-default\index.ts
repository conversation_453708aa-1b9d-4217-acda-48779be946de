import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/CourseLearning-default'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'CourseLearning-default'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum ScaleType {
  CUSTOM = 'CUSTOM',
  SATISFACTION = 'SATISFACTION',
  RECOGNITION = 'RECOGNITION',
  IMPORTANCE = 'IMPORTANCE',
  WILLING = 'WILLING',
  CONFORMITY = 'CONFORMITY'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 批量选课信息
 */
export class BatchChooseCourseRequest {
  /**
   * 课程包ID
   */
  poolId?: string
  /**
   * 课程ID列表
   */
  courseIds?: Array<string>
}

/**
 * <AUTHOR> create 2019/11/26 14:24
 */
export class CourseLearningDescribeRequest {
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 学习方式id
   */
  learningId?: string
  /**
   * 选课规则id
   */
  ruleId?: string
  /**
   * 课程包id
   */
  poolId?: string
  sourceType?: string
}

/**
 * <AUTHOR> create 2019/11/5 16:49
 */
export class MarkerRequest {
  /**
   * 特征标记键
   */
  key?: string
  /**
   * 特征标记值
   */
  value?: string
}

/**
 * <AUTHOR> create 2019/11/26 14:25
 */
export class NoRuleValidateDescribeRequest {
  /**
   * 课程包id
   */
  poolId?: string
  /**
   * 特征标记
   */
  markers?: Array<MarkerRequest>
  sourceType?: string
}

/**
 * 提交弹窗题
<AUTHOR> create 2020/4/15 16:02
 */
export class SubmitPopQuestionRequest {
  /**
   * 课程ID
   */
  courseId?: string
  /**
   * 课件ID
   */
  courseWareId?: string
  /**
   * 媒体ID
   */
  mediaId?: string
  /**
   * 弹窗题Id
   */
  questionId?: string
  /**
   * 用户答题信息,单选题、多选题将用户选择的选项ID填入； 判断题 将
   */
  answer?: Array<string>
}

/**
 * 用户批量选课dto
<AUTHOR> create 2019/11/26 14:33
 */
export class UserBatchChooseCourseRequest {
  /**
   * 用户id
   */
  userId?: string
  /**
   * 来源类型
@see UserCourseSourceType
   */
  sourceType?: string
  /**
   * 课程id集合
   */
  courseIds?: Array<string>
  /**
   * 选课学习来源描述
   */
  courseLearning?: CourseLearningDescribeRequest
  /**
   * 无规则校验来源描述
   */
  noRuleValidate?: NoRuleValidateDescribeRequest
  /**
   * 是否测试数据
   */
  test: boolean
}

/**
 * 用户批量退课dto
<AUTHOR> create 2019/11/26 14:33
 */
export class UserBatchRemoveCourseRequest {
  /**
   * 用户id
   */
  userId?: string
  /**
   * 来源类型
@see UserCourseSourceType
   */
  sourceType?: string
  /**
   * 课程id集合
   */
  courseIds?: Array<string>
  /**
   * 选课学习来源描述
   */
  courseLearning?: CourseLearningDescribeRequest
  /**
   * 无规则校验来源描述
   */
  noRuleValidate?: NoRuleValidateDescribeRequest
}

/**
 * <AUTHOR> create 2019/11/26 14:09
 */
export class UserCourseCreateRequest {
  /**
   * 用户id
   */
  userId?: string
  /**
   * 来源类型
@see UserCourseSourceType
   */
  sourceType?: string
  /**
   * 选课学习来源描述
   */
  courseLearning?: CourseLearningDescribeRequest
  /**
   * 无规则校验来源描述
   */
  noRuleValidate?: NoRuleValidateDescribeRequest
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 是否测试数据
   */
  test: boolean
}

/**
 * 用户课程查询
<AUTHOR> create 2019/11/26 14:13
 */
export class UserCourseQueryRequest {
  /**
   * 用户id
   */
  userId?: string
  /**
   * 课程名称
   */
  courseName?: string
  /**
   * 课程名称是否精确查询
   */
  excat: boolean
  /**
   * 学习状态，-1/0/1/2，全部/未学习/学习中/学习完成
   */
  studyState: number
  /**
   * 起始最后一次学习时间
   */
  startLastStudyTime?: string
  /**
   * 截止最后一次学习时间
   */
  endLastStudyTime?: string
  /**
   * 课程包编号集合
   */
  poolIdList?: Array<string>
  /**
   * 课程编号集合
   */
  courseIdList?: Array<string>
  /**
   * 选课特征标记列表
   */
  markers?: Array<MarkerRequest>
  /**
   * 课程分类编号列表
   */
  courseCatalogIdList?: Array<string>
}

/**
 * <AUTHOR> create 2019/11/28 13:57
 */
export class UserCourseSortRequest {
  /**
   * 排序字段
0-最后学习时间
1-学习进度
   */
  orderByField: number
  /**
   * 是否降序
   */
  descending: boolean
}

export class TagDTO {
  id: string
  code: string
  tag: string
}

export class BlankFillingDTO {
  answerCount: number
  answersGroup: Array<string>
  answersItemScore: Array<number>
  answerType: number
  sequence: boolean
  standard: string
}

export class ChoiceItemDTO {
  id: string
  content: string
}

export class ComprehensiveChildQuestionDTO {
  questionId: string
  title: string
  questionType: number
  judgement: JudgementDTO
  singleChoice: SingleChoiceDTO
  multipleChoice: MultipleChoiceDTO
  blankFilling: BlankFillingDTO
  essay: EssayDTO
  scale: ScaleDTO
  mode: number
  difficultyValue: number
  description: string
}

export class ComprehensiveDTO {
  children: Array<ComprehensiveChildQuestionDTO>
}

export class EssayDTO {
  referenceAnswer: string
  standard: string
  limitAnswerLength: boolean
  permitAnswerLengthMin: number
  permitAnswerLengthMax: number
}

export class JudgementDTO {
  correctAnswer: boolean
  correctText: string
  incorrectText: string
}

export class MultipleChoiceDTO {
  choiceItems: Array<ChoiceItemDTO>
  correctAnswers: Array<string>
}

export class ScaleDTO {
  scaleType: ScaleType
  startDegree: string
  endDegree: string
  series: number
  initialValue: number
}

export class SingleChoiceDTO {
  choiceItems: Array<ChoiceItemDTO>
  correctAnswer: string
}

export class SourceDescribeDTO {
  sourceType: string
}

/**
 * 章节播放结构信息
 */
export class ChaptersPlayResponse {
  /**
   * 章节编号
   */
  id: string
  /**
   * 章节名称
   */
  name: string
  /**
   * 上级章节编号
   */
  parentId: string
  /**
   * 播放时间点
   */
  timePoint: number
  /**
   * 排序
   */
  sort: number
}

/**
 * 课程分类dto
<AUTHOR> create 2019/11/28 14:23
 */
export class CourseCategoryResponse {
  /**
   * 课程分类ID
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属服务单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 课程分类名称
   */
  name: string
  /**
   * 上级分类ID
   */
  parentId: string
  /**
   * 排序
   */
  sort: number
  /**
   * 备注
   */
  remarks: string
  /**
   * 业务实体ID
   */
  objectId: string
}

/**
 * 课程来源描述详情
<AUTHOR> create 2019/12/3 14:59
 */
export class CourseDescDetailResponse {
  /**
   * 课程信息
   */
  course: CourseResponse
  /**
   * 最后学习媒体
   */
  lastLearningMedia: LastLearningMediaResponse
  /**
   * 课程id
   */
  courseId: string
  /**
   * 学习的特征标记
   */
  markers: Array<MarkerResponse>
}

/**
 * <AUTHOR> create 2019/12/3 15:06
 */
export class CourseInPoolResponse {
  /**
   * 课程池与课程关系编号
   */
  id: string
  /**
   * 所属课程池编号
   */
  poolId: string
  /**
   * 课程编号
   */
  courseId: string
  /**
   * 课程信息
   */
  course: CourseResponse
  /**
   * 课程序号
   */
  sequence: number
  /**
   * 课程标量值|在课程池规则中，课程在课程池中权重值
   */
  quantitative: number
  /**
   * 课程学时|课程在课程池中的学时
   */
  period: number
  /**
   * 课程过期时间|课程在课程池中到期时间，null表示该课程在课程池中无期限限制
   */
  courseExpireTime: string
  /**
   * 特征标记列表
   */
  markers: Array<MarkerResponse>
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 课程目录播放结构信息
 */
export class CourseOutlinePlayResponse {
  /**
   * 课程目录ID
   */
  id: string
  /**
   * 课程目录名称
   */
  name: string
  /**
   * 父级ID，0表示顶级
   */
  parentId: string
  /**
   * 排序
   */
  sort: number
}

/**
 * <AUTHOR> create 2019/11/26 16:56
 */
export class CourseOutlineResponse {
  /**
   * 课程目录ID
   */
  id: string
  /**
   * 课程ID
   */
  cseId: string
  /**
   * 挂载的课件ID
   */
  cweId: string
  /**
   * 课件信息
   */
  courseWare: CoursewareResponse
  /**
   * 目录名称
   */
  name: string
  /**
   * 上级目录ID
   */
  parentId: string
  /**
   * 排序
   */
  sort: number
  /**
   * 自定义状态 0不可以试听，1可以试听
   */
  customeStatus: number
  /**
   * 自定义拓展数据
   */
  expendData: string
  /**
   * 下级目录
   */
  subCourseOutlines: Array<CourseOutlineResponse>
}

/**
 * 课程播放结构信息
 */
export class CoursePlayResponse {
  /**
   * 课程ID
   */
  id: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 课程已学习的进度
   */
  schedule: number
  /**
   * 最后播放时间
   */
  lastPlayTime: string
  /**
   * 课程计划总课件数
   */
  plannedCoursewareCount: number
  /**
   * 当前课件数
   */
  currentCoursewareCount: number
  /**
   * 课程简介
   */
  abouts: string
  /**
   * 教师ID集合
   */
  teacherIds: Array<string>
  /**
   * 课程目录信息
   */
  courseOutlines: Array<CourseOutlinePlayResponse>
  /**
   * 课件列表
   */
  coursewareList: Array<CoursewarePlayResponse>
}

/**
 * <AUTHOR> create 2019/11/26 16:53
 */
export class CourseResponse {
  /**
   * 课程ID
   */
  id: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 权重,表示学时,学分等
   */
  period: number
  /**
   * 课程简介
   */
  abouts: string
  /**
   * 课程目录
   */
  courseOutline: Array<CourseOutlineResponse>
  /**
   * 计划授课讲数
   */
  plannedLecturesNum: number
  /**
   * 章节
   */
  chapters: Array<TagResponse>
}

/**
 * 课程进度信息
<AUTHOR>
@date 2020/3/12
@since 1.0.0
 */
export class CourseScheduleResponse {
  /**
   * 课程编号
   */
  courseId: string
  /**
   * 用户编号
   */
  userId: string
  /**
   * 课程学习进度
   */
  schedule: number
  /**
   * 学习状态：0/1/2，未学习/学习中/学习完成
   */
  studyState: number
  /**
   * 最后学习时间
   */
  lastStudyTime: string
  /**
   * 课件学习进度列表
   */
  coursewareScheduleList: Array<CoursewareScheduleResponse>
}

/**
 * <AUTHOR> create 2019/12/3 15:03
 */
export class CourseWareDescDetailResponse {
  /**
   * 课件信息
   */
  courseWare: CoursewareResponse
  /**
   * 课件id
   */
  courseWareId: string
}

/**
 * 课件播放结构信息
 */
export class CoursewarePlayResponse {
  /**
   * 课件ID
   */
  id: string
  /**
   * 课件名称
   */
  name: string
  /**
   * 课件时长，单位秒
   */
  timeLength: number
  /**
   * 课件类型：1表示文档，2表示单视频，3表示多媒体
   */
  type: number
  /**
   * 自定义状态 0不可以试听，1可以试听
   */
  customeStatus: number
  /**
   * 排序
   */
  sort: number
  /**
   * 课件已学习的进度
   */
  schedule: number
  /**
   * 是否有课程目录
   */
  hedCourseOutline: boolean
  /**
   * 课程目录ID
   */
  courseOutlineId: string
  /**
   * 是否最后一次播放的课件
   */
  lastedPlay: boolean
  /**
   * 最后播放时间
   */
  lastPlayTime: string
}

/**
 * 课件播放资源结构信息
 */
export class CoursewareResourcePlayResponse {
  /**
   * 课件ID
   */
  id: string
  /**
   * 课件类型
   */
  type: number
  /**
   * 试听时长
   */
  listenTime: number
  /**
   * 视频信息
   */
  video: VideoPlayResponse
  /**
   * 文档信息
   */
  document: DocumentPlayResponse
}

/**
 * 课件信息
<AUTHOR> create 2019/11/12 10:42
 */
export class CoursewareResponse {
  /**
   * 课件id
   */
  id: string
  /**
   * 课件名称
   */
  name: string
  /**
   * 媒体进度
   */
  schedule: number
  /**
   * 媒体列表
   */
  mediaList: Array<MediaResponse>
}

/**
 * 课件学习进度信息
<AUTHOR>
@date 2020/3/12
@since 1.0.0
 */
export class CoursewareScheduleResponse {
  /**
   * 课件编号
   */
  coursewareId: string
  /**
   * 课件学习进度
   */
  schedule: number
  /**
   * 学习状态：0/1/2，未学习/学习中/学习完成
   */
  studyState: number
  /**
   * 最后学习时间
   */
  lastStudyTime: string
  /**
   * 媒体学习进度信息
   */
  mediaScheduleList: Array<MediaScheduleResponse>
}

/**
 * 文档播放结构信息
 */
export class DocumentPlayResponse {
  /**
   * 文档ID
   */
  id: string
  /**
   * 文档类型
   */
  type: number
  /**
   * 文档播放路径
   */
  path: string
  /**
   * 文档时长
   */
  timeLength: number
  /**
   * 已播放时长
   */
  playedTimeLength: number
}

/**
 * 800里视频dto
<AUTHOR> create 2019/11/13 16:52
 */
export class EHLIVideoResponse {
  /**
   * 资源地址.mp3.mp4
   */
  resource: string
  /**
   * 资源域名
   */
  streamHost: string
}

/**
 * 华为云资源dto
<AUTHOR> create 2019/11/13 16:54
 */
export class ExternalLinksResourceResponse {
  /**
   * 地址
   */
  url: string
  /**
   * 清晰度
   */
  quality: number
}

/**
 * 华为云视频播放结构信息
 */
export class ExternalLinksVideoPlayResponse {
  /**
   * 视频资源信息
   */
  videoResources: Array<ExternalLinksResourceResponse>
}

/**
 * 华为云音频资源播放结构信息
 */
export class HWYAudioResourcePlayResponse {
  /**
   * 音频播放地址
   */
  path: string
}

/**
 * 华为云资源dto
<AUTHOR> create 2019/11/13 16:54
 */
export class HWYResourceResponse {
  /**
   * 地址
   */
  url: string
  /**
   * 清晰度
   */
  quality: number
}

/**
 * 华为云视频播放结构信息
 */
export class HWYVideoPlayResponse {
  /**
   * 视频资源信息
   */
  videoResources: Array<HWYVideoResourcePlayResponse>
  /**
   * 音频资源信息
   */
  audioResource: HWYAudioResourcePlayResponse
}

export class HWYVideoResourcePlayResponse {
  path: string
  clarity: number
}

/**
 * 华为云视频dto
<AUTHOR> create 2019/11/13 16:53
 */
export class HWYVideoResponse {
  /**
   * 华为云媒体资源
   */
  resources: Array<HWYResourceResponse>
  /**
   * 资源域名
   */
  streamHost: string
}

/**
 * <AUTHOR> create 2019/12/13 15:42
 */
export class LastLearningMediaResponse {
  /**
   * 多媒体学习记录编号
   */
  multimediaRecordId: string
  /**
   * 用户编号
   */
  userId: string
  /**
   * 业务实体编号
   */
  objectId: string
  /**
   * 课程编号
   */
  courseId: string
  /**
   * 课件编号
   */
  coursewareId: string
  /**
   * 学习进度，百分比小数点后两位
   */
  schedule: number
  /**
   * 上次播放刻度|最后一次播放的刻度,int
   */
  lastPlayScale: number
  /**
   * 学习状态，0/1/2，未学习/学习中/学习完成
   */
  studyState: number
  /**
   * 学习完成时间，如果学习状态不是学习完成，该值为默认日期时间1970-01-01
   */
  completedTime: string
  /**
   * 最后一次学习时间，日期格式yyyy-MM-dd HH:mm:ss
   */
  lastStudyTime: string
  /**
   * 多媒体ID
   */
  multiMediaId: string
}

/**
 * 学习模型
<AUTHOR> create 2019/12/3 14:56
 */
export class LearningResponse {
  /**
   * token
<p>
以token作为id是因为目前学习服务依靠课程id+marker来标识唯一的学习记录，相关接口也是以课程id+marker来查询。
学习领域依靠 培训方案或其他调用方生成的token来获取学习信息（其中来源类型和来源的具体值都是描述在token内）。
</p>
   */
  id: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 来源类型
   */
  sourceType: number
  /**
   * 课程来源描述
   */
  courseDescribe: CourseDescDetailResponse
  /**
   * 课程池内课程描述
   */
  poolCourseDescribe: PoolCourseDescDetailResponse
  /**
   * 用户课程描述
   */
  userCourseDescribe: UserCourseDescDetailResponse
  /**
   * 课件描述
   */
  courseWareDescribe: CourseWareDescDetailResponse
  /**
   * 进度
   */
  schedule: number
}

/**
 * 讲义播放结构信息
 */
export class LecturePlayResponse {
  /**
   * 讲义ID
   */
  id: string
  /**
   * 讲义类型，1代表html|2代表image|3表示vga
   */
  type: number
  /**
   * 播放时间点，单位秒
   */
  timePoint: number
  /**
   * 讲义路径
   */
  path: string
}

/**
 * 讲义信息
<AUTHOR> create 2019/11/13 17:07
 */
export class LectureResponse {
  /**
   * 讲义ID
   */
  id: string
  /**
   * 所属课件ID
   */
  cweId: string
  /**
   * 讲义类型，1代表html|2代表image|3表示vga
   */
  type: number
  /**
   * 播放时间点，单位秒
   */
  timePoint: number
  /**
   * 讲义路径
   */
  path: string
  /**
   * 拓展信息
   */
  expand: string
}

/**
 * <AUTHOR> create 2019/11/5 16:49
 */
export class MarkerResponse {
  /**
   * 特征标记键
   */
  key: string
  /**
   * 特征标记值
   */
  value: string
}

/**
 * 媒体信息
<AUTHOR> create 2019/11/12 10:43
 */
export class MediaResponse {
  /**
   * 媒体id
   */
  id: string
  /**
   * 媒体名称
   */
  name: string
  /**
   * 时长
   */
  time: number
  /**
   * 媒体类型 1.pdf 2.单视频 3.三分 4.web
   */
  type: number
  /**
   * 播放资源
   */
  playResources: PlayResourcesResponse
  /**
   * 媒体时长
   */
  timelength: number
  /**
   * 自定义状态 0不可以试听，1可以试听
   */
  customeStatus: number
  /**
   * 播放模式 1.支持试听 2.不支持试听 3.购买后播放
   */
  mode: number
  /**
   * 试听时长
   */
  listenTime: number
  /**
   * 媒体进度
   */
  schedule: number
  /**
   * 已经播放时长
   */
  alreadyPlayTime: number
  /**
   * 允许播放次数,播放学习时用到,未有限制传null
   */
  allowPlayTimes: number
  /**
   * 讲义信息
   */
  lectureList: Array<LectureResponse>
  /**
   * 视频章节信息
   */
  catalogList: Array<VideoChapterResponse>
}

/**
 * 媒体学习进度信息
<AUTHOR>
@date 2020/3/12
@since 1.0.0
 */
export class MediaScheduleResponse {
  /**
   * 媒体编号
   */
  mediaId: string
  /**
   * 媒体学习进度
   */
  schedule: number
  /**
   * 媒体学习状态：0/1/2，未学习/学习中/学习完成
   */
  studyState: number
  /**
   * 最后学习时间
   */
  lastStudyTime: string
}

/**
 * 播放资源dto
<AUTHOR> create 2019/11/13 16:50
 */
export class PlayResourcesResponse {
  /**
   * 文档地址
   */
  docPath: string
  /**
   * 800里视频资源
   */
  player_800li: EHLIVideoResponse
  /**
   * 华为云视频资源
   */
  player_hwCloud: HWYVideoResponse
  /**
   * 默认资源类型（当存在800里及华为云两种视频资源时默认播放的视频资源）
   */
  defaultResourceType: string
}

/**
 * 播放信息
 */
export class PlayingResponse {
  /**
   * 播放模式，1表示用户学习，2表示预览 3表示试听
   */
  mode: number
  /**
   * 播放资源类型，1表示课程，2表示课件
   */
  resourceType: number
  /**
   * 用户ID
   */
  userId: string
  /**
   * 课程资源信息
   */
  course: CoursePlayResponse
}

/**
 * 课程池内课程来源详情
<AUTHOR> create 2019/12/3 15:04
 */
export class PoolCourseDescDetailResponse {
  /**
   * 课程池内课程
   */
  courseInPool: CourseInPoolResponse
  /**
   * 最后学习媒体
   */
  lastLearningMedia: LastLearningMediaResponse
  /**
   * 课程包id
   */
  poolId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 学习特征标记
   */
  markers: Array<MarkerResponse>
}

/**
 * 弹窗题
<AUTHOR> create 2020/4/16 8:06
 */
export class PopQuestionResponse {
  /**
   * 弹窗题id、主键id
   */
  popQuestionId: string
  /**
   * 弹窗时间：秒
   */
  timePoint: number
  /**
   * 课件id
   */
  courseWareId: string
  id: string
  applyTypes: Array<number>
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  organizationId: string
  libraryId: string
  title: string
  judgement: JudgementDTO
  singleChoice: SingleChoiceDTO
  multipleChoice: MultipleChoiceDTO
  blankFilling: BlankFillingDTO
  essay: EssayDTO
  scale: ScaleDTO
  comprehensive: ComprehensiveDTO
  questionType: number
  mode: number
  difficulty: number
  description: string
  createUserId: string
  createTime: string
  lastChangeTime: string
  enabled: boolean
  rootId: string
  token: string
  relateCourseId: string
  relateCourseIds: Array<string>
  tags: Array<TagDTO>
}

/**
 * 简单的弹窗题dto
<AUTHOR> create 2020/4/15 16:21
 */
export class SimplePopQuestionResponse {
  /**
   * 弹窗题id、主键id
   */
  popQuestionId: string
  /**
   * 试题id
   */
  id: string
  /**
   * 弹窗时间：秒
   */
  timePoint: number
  /**
   * 课件id
   */
  courseWareId: string
}

/**
 * <AUTHOR> create 2020/1/13 14:12
 */
export class TagResponse {
  /**
   * 标签id
   */
  id: string
  /**
   * 标签code
   */
  code: string
  /**
   * 标签
   */
  tag: string
}

/**
 * <AUTHOR> create 2019/12/3 15:08
 */
export class UserCourseDescDetailResponse {
  /**
   * 用户课程
   */
  userCourse: UserCourseResponse
  /**
   * 最后学习媒体
   */
  lastLearningMedia: LastLearningMediaResponse
  /**
   * 用户课程id
   */
  id: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 选课规则id
   */
  ruleId: string
  /**
   * 课程包id
   */
  poolId: string
}

/**
 * 用户课程分页项
<AUTHOR> create 2019/11/26 14:11
 */
export class UserCourseItemResponse {
  /**
   * 选课记录id
   */
  id: string
  /**
   * 所属平台编号
   */
  platformId: string
  /**
   * 所属平台版本编号
   */
  platformVersionId: string
  /**
   * 所属项目编号
   */
  projectId: string
  /**
   * 所属子项目编号
   */
  subProjectId: string
  /**
   * 所属组织机构编号
   */
  organizationId: string
  /**
   * 所属单位编号
   */
  unitId: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 来源类型
@see UserCourseSourceType
   */
  sourceType: string
  /**
   * 来源描述
   */
  sourceDescribe: SourceDescribeDTO
  /**
   * 用户课程学时
   */
  period: number
  /**
   * 课程状态|0/1/2，正常/冻结/过期，课程在用户个人选课池中的状态
   */
  state: number
  /**
   * (常规)选课时间
   */
  chooseTime: string
  /**
   * 过期时间|表示当前选择的课程有效期过期时间，默认2099/12/31，表示该选择的课程无期限的限制
   */
  expireTime: string
  /**
   * 课程信息
   */
  course: CourseResponse
  /**
   * 课程分类
   */
  courseCategory: CourseCategoryResponse
  /**
   * 学习记录信息
   */
  courseSchedule: CourseScheduleResponse
}

/**
 * 用户课程dto
<AUTHOR> create 2019/11/26 14:59
 */
export class UserCourseResponse {
  id: string
  userId: string
  /**
   * 来源类型
@see UserCourseSourceType
   */
  sourceType: string
  /**
   * 来源描述
   */
  sourceDescribe: SourceDescribeDTO
  /**
   * 用户课程学时
   */
  period: number
  /**
   * 课程id
   */
  courseId: string
  /**
   * 课程信息
   */
  course: CourseResponse
  /**
   * 进度
   */
  schedule: number
  /**
   * 是否测试数据
   */
  test: boolean
}

/**
 * 视频章节信息
<AUTHOR> create 2019/11/13 17:07
 */
export class VideoChapterResponse {
  /**
   * 章节编号
   */
  id: string
  /**
   * 所属课件编号
   */
  cweId: string
  /**
   * 所属课件视频编号
   */
  vdoId: string
  /**
   * 章节名称
   */
  name: string
  /**
   * 上级章节编号
   */
  parentId: string
  /**
   * 播放时间点，-1代表不参与播放，或许它就是用于展示章节结构的展示
   */
  timePoint: number
  /**
   * 排序
   */
  sort: number
}

/**
 * 视频播放结构信息
 */
export class VideoPlayResponse {
  /**
   * 视频ID
   */
  id: string
  /**
   * 视频时长
   */
  timeLength: number
  /**
   * 已播放时长
   */
  playedTimeLength: number
  /**
   * 华为云视频信息
   */
  hwyVideo: HWYVideoPlayResponse
  /**
   * 视频章节信息
   */
  chapters: Array<ChaptersPlayResponse>
  /**
   * 视频讲义信息
   */
  lectures: Array<LecturePlayResponse>
  /**
   * 外部链接视频信息
   */
  externalLinksVideo: ExternalLinksVideoPlayResponse
}

export class UserCourseItemResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserCourseItemResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取用户课程分页
   * @param page
   * @param query
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findUserCoursePage(
    params: { page?: Page; query?: UserCourseQueryRequest; sort?: UserCourseSortRequest },
    query: DocumentNode = GraphqlImporter.findUserCoursePage,
    operation?: string
  ): Promise<Response<UserCourseItemResponsePage>> {
    return commonRequestApi<UserCourseItemResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课件媒体提交的进度的key
   * @param token
   * @param courseWareId
   * @param mediaId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAssessKey(
    params: { token?: string; courseWareId?: string; mediaId?: string },
    query: DocumentNode = GraphqlImporter.getAssessKey,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取最后学习的媒体信息
   * @param token 学习方案学习参数凭证
   * @return 最后学习媒体信息
   * @param query 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getLastLearningMedia(
    token: string,
    query: DocumentNode = GraphqlImporter.getLastLearningMedia,
    operation?: string
  ): Promise<Response<LastLearningMediaResponse>> {
    return commonRequestApi<LastLearningMediaResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { token },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据toekn获取学习信息
   * @param token
   * @return
   * @param query 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getLearningByToken(
    token: string,
    query: DocumentNode = GraphqlImporter.getLearningByToken,
    operation?: string
  ): Promise<Response<LearningResponse>> {
    return commonRequestApi<LearningResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { token },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 依据Token查询课程进度信息
   * @param token 学习方案学习参数凭证
   * @return 课程进度信息
   * @param query 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getLearningScheduleByToken(
    token: string,
    query: DocumentNode = GraphqlImporter.getLearningScheduleByToken,
    operation?: string
  ): Promise<Response<CourseScheduleResponse>> {
    return commonRequestApi<CourseScheduleResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { token },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取需要作答的弹窗题，默认规则是所有
   * @return
   * @param token
   * @param courseWareId
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getNeedAnswerPopQuestions(
    params: { token?: string; courseWareId?: string },
    query: DocumentNode = GraphqlImporter.getNeedAnswerPopQuestions,
    operation?: string
  ): Promise<Response<Array<SimplePopQuestionResponse>>> {
    return commonRequestApi<Array<SimplePopQuestionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取弹窗题
   * @param popQuestionId
   * @return
   * @param query 查询 graphql 语法文档
   * @param popQuestionId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPopQuestion(
    popQuestionId: string,
    query: DocumentNode = GraphqlImporter.getPopQuestion,
    operation?: string
  ): Promise<Response<PopQuestionResponse>> {
    return commonRequestApi<PopQuestionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { popQuestionId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户课程
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserCourse(
    id: string,
    query: DocumentNode = GraphqlImporter.getUserCourse,
    operation?: string
  ): Promise<Response<UserCourseResponse>> {
    return commonRequestApi<UserCourseResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请课件播放资源
   * @param token 申请播放token
   * @param coursewareId 课件ID
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCoursewarePlayingResource(
    params: { token?: string; coursewareId?: string },
    mutate: DocumentNode = GraphqlImporter.applyCoursewarePlayingResource,
    operation?: string
  ): Promise<Response<CoursewareResourcePlayResponse>> {
    return commonRequestApi<CoursewareResourcePlayResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请预览试听的播放token
   * @param courseId 要试听的课程ID
   * @param mutate 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyListenCourseToken(
    courseId: string,
    mutate: DocumentNode = GraphqlImporter.applyListenCourseToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请播放
   * @param token 申请播放token
   * @param mutate 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyPlaying(
    token: string,
    mutate: DocumentNode = GraphqlImporter.applyPlaying,
    operation?: string
  ): Promise<Response<PlayingResponse>> {
    return commonRequestApi<PlayingResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { token },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请预览课程的播放token
   * @param courseId 要预览的课程ID
   * @param mutate 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyPreviewCourseToken(
    courseId: string,
    mutate: DocumentNode = GraphqlImporter.applyPreviewCourseToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请重新加载华为云视频防盗链资源
   * @param videoId 视频ID
   * @param mutate 查询 graphql 语法文档
   * @param videoId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyReloadHwyVideoAntiLeech(
    videoId: string,
    mutate: DocumentNode = GraphqlImporter.applyReloadHwyVideoAntiLeech,
    operation?: string
  ): Promise<Response<HWYVideoPlayResponse>> {
    return commonRequestApi<HWYVideoPlayResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { videoId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量选课
   * @param token
   * @param chooseInfoList
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchChooseCourse(
    params: { token?: string; chooseInfoList?: Array<BatchChooseCourseRequest> },
    mutate: DocumentNode = GraphqlImporter.batchChooseCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量删除用户选课
   * @param batchRemove
   * @param mutate 查询 graphql 语法文档
   * @param batchRemove 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchRemoveUserCourse(
    batchRemove: UserBatchRemoveCourseRequest,
    mutate: DocumentNode = GraphqlImporter.batchRemoveUserCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { batchRemove },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除用户选课
   * @param id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeUserCourse(
    id: string,
    mutate: DocumentNode = GraphqlImporter.removeUserCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 提交答案
   * @param token
   * @param submit
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async submitAnswer(
    params: { token?: string; submit?: SubmitPopQuestionRequest },
    mutate: DocumentNode = GraphqlImporter.submitAnswer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新课件进度
   * @param token 播放token
   * @param coursewareId 课件ID
   * @param schedule 要更新的进度
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCoursewareSchedule(
    params: { token?: string; coursewareId?: string; schedule: number },
    mutate: DocumentNode = GraphqlImporter.updateCoursewareSchedule,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 用户批量选课
   * @deprecated 选课数据6个字段从上下文中获取，和方案的6个字段id不一定相同 推荐换成 {@link #batchChooseCourse}
   * @param batchChoose
   * @param mutate 查询 graphql 语法文档
   * @param batchChoose 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async userBatchChooseCourse(
    batchChoose: UserBatchChooseCourseRequest,
    mutate: DocumentNode = GraphqlImporter.userBatchChooseCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { batchChoose },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 用户选课
   * @param create
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param create 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async userChooseCourse(
    create: UserCourseCreateRequest,
    mutate: DocumentNode = GraphqlImporter.userChooseCourse,
    operation?: string
  ): Promise<Response<UserCourseResponse>> {
    return commonRequestApi<UserCourseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { create },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
