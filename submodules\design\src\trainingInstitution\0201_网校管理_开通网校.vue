<template>
  <el-container>
    <el-aside width="240px">
      <!--侧边栏按钮 展开时显示-->
      <a href="#" class="aside-btn el-icon-s-fold"></a>
      <!--侧边栏按钮 收起时显示-->
      <!--<a href="#" class="aside-btn el-icon-s-unfold"></a>-->
      <div class="logo">
        <span class="logo-txt">通用平台运营管理后台</span>
      </div>
      <div class="user-info">
        <img class="photo" src="./assets/images/default-photo.jpg" alt=" " />
        <p class="name">张某某</p>
        <div class="op-btn f-mt10">
          <el-button type="primary" size="mini" round plain>
            <i class="el-icon-s-tools"></i>
            帐号设置
          </el-button>
        </div>
      </div>
      <el-menu default-active="0" class="aside-nav" unique-opened="true" @open="handleOpen" @close="handleClose">
        <el-menu-item index="0">
          <template slot="title">
            <i class="iconfont icon-weiwangxiao"></i>
            <span>开通网校</span>
          </template>
        </el-menu-item>
        <!--网校管理-->
        <el-menu-item index="1">
          <template slot="title">
            <i class="hb-iconfont icon-setup"></i>
            <span>网校管理</span>
          </template>
        </el-menu-item>
      </el-menu>
      <div class="m-company-info">
        <p>福建华博教育科技股份有限公司</p>
        <a class="a-txt" href="http://beian.miit.gov.cn/" target="_blank">闽ICP备2021002737号</a>
      </div>
    </el-aside>
    <el-container>
      <el-header height="100px" class="f-flex">
        <ul class="header-nav f-flex-sub">
          <li class="nav-item current">
            <i class="iconfont icon-weiwangxiao"></i>
            <span class="txt">网校管理</span>
          </li>
          <li class="current-bg" style="min-width: 124px;"></li>
        </ul>
        <ul class="header-nav">
          <li class="nav-item nav-item-1"><i class="iconfont icon-tuichu"></i>退出</li>
        </ul>
      </el-header>
      <div class="tags">
        <span class="prev"><i class="el-icon el-icon-arrow-left"></i></span>
        <div class="tags-bd">
          <div class="tags-items" style="width: 500%;">
            <el-tag class="current" closable>开通网校</el-tag>
          </div>
        </div>
        <span class="next"><i class="el-icon el-icon-arrow-right"></i></span>
        <el-dropdown :hide-on-click="false">
          <span class="more"><i class="el-icon el-icon-more"></i></span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>黄金糕</el-dropdown-item>
            <el-dropdown-item>狮子头</el-dropdown-item>
            <el-dropdown-item>螺蛳粉</el-dropdown-item>
            <el-dropdown-item disabled>双皮奶</el-dropdown-item>
            <el-dropdown-item divided>蚵仔煎</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <el-main>
        <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb10">
          配置提示：<br />
          1.本平台开通的网校只适用8.0通用平台提供的标准化功能，若网校功能与通用平台提供的标准化功能有较大差异，请独立建设平台，不适合开通网校。<br />
          2.网校配置成功后不会自动发布，需要网校确认相关功能都已配置完成，才开放网校前端访问。
        </el-alert>
        <div class="f-p15">
          <el-card shadow="never" class="m-card f-mb15 is-header-sticky">
            <el-collapse v-model="activeNames" accordion>
              <el-collapse-item name="1" class="m-collapse-item">
                <template slot="title">基础信息</template>
                <div class="f-plr20 f-pt40">
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
                      <el-form ref="form" :model="form" label-width="auto" class="m-form">
                        <el-form-item label="网校平台名称：" required>
                          <el-input
                            v-model="form.name"
                            clearable
                            placeholder="请输入网校平台名称，网校名称会同步显示给学员"
                          />
                        </el-form-item>
                        <el-form-item label="服务地区：" required>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">全国范围</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">选择地区</el-radio>
                          <span class="f-co">注：选择的网校开展培训的地区范围</span>
                          <div class="f-mt10">
                            <el-cascader :options="options2" :props="props" collapse-tags clearable></el-cascader>
                          </div>
                        </el-form-item>
                        <el-form-item label="培训行业：" required>
                          <el-checkbox v-model="checked1" label="人社行业" border class="f-mr10"></el-checkbox>
                          <el-checkbox v-model="checked1" label="建设行业" border class="f-mr10"></el-checkbox>
                          <el-checkbox v-model="checked1" label="职业卫生行业" border class="f-mr10"></el-checkbox>
                          <el-checkbox v-model="checked1" label="工勤行业" border class="f-mr10"></el-checkbox>
                          <el-checkbox v-model="checked1" label="教师行业" border class="f-mr10"></el-checkbox>
                        </el-form-item>
                        <el-form-item label="业务属性：" required>
                          <el-form
                            ref="form"
                            :model="form"
                            label-width="auto"
                            labelPosition="top"
                            class="m-form pb0 bg-gray f-pt5 f-pl15 f-pr15"
                          >
                            <div class="f-co">
                              <i class="el-icon-warning f-f16 f-mr5 f-vm"></i
                              >请设置培训行业对应的业务属性，需要配置公共的业务属性值和行业属性值
                            </div>
                            <el-form-item label="公共业务属性：">
                              <!--竖式表格-->
                              <div class="info-table">
                                <div class="info-row col-merge">
                                  <div class="info-th f-tl">年度</div>
                                  <div class="info-td p0">
                                    <div class="m-radio-border-list">
                                      <el-checkbox v-model="checked1" label="2023年" border></el-checkbox>
                                      <el-checkbox v-model="checked2" label="2022年" border></el-checkbox>
                                      <el-checkbox v-model="checked3" label="2021年" border></el-checkbox>
                                      <el-checkbox v-model="checked3" label="2020年" border></el-checkbox>
                                      <el-checkbox v-model="checked3" label="2019年" border></el-checkbox>
                                      <el-checkbox v-model="checked3" label="2018年" border></el-checkbox>
                                      <el-checkbox v-model="checked3" label="2017年" border></el-checkbox>
                                      <el-checkbox v-model="checked3" label="2016年" border></el-checkbox>
                                      <el-checkbox v-model="checked3" label="2015年" border></el-checkbox>
                                      <el-checkbox v-model="checked3" label="2014年" border></el-checkbox>
                                      <el-checkbox v-model="checked3" label="2013年" border></el-checkbox>
                                      <el-checkbox v-model="checked3" label="2012年" border></el-checkbox>
                                      <el-checkbox v-model="checked3" label="2011年" border></el-checkbox>
                                      <el-checkbox v-model="checked3" label="2010年" border></el-checkbox>
                                    </div>
                                  </div>
                                </div>
                                <div class="info-row col-merge">
                                  <div class="info-th f-tl">地区</div>
                                  <div class="info-td p0">
                                    <div class="m-city-btn-list">
                                      <el-button type="primary">北京市<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">天津市<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">河北省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">山西省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary"
                                        >内蒙古自治区<i class="el-icon-arrow-right"></i
                                      ></el-button>
                                      <el-button type="primary">辽宁省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">吉林省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">黑龙江省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">上海市<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">江苏省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">浙江省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">安徽省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-tooltip placement="top" effect="light">
                                        <div slot="content">
                                          <!--级联面板需要默认全部展开，需前端人员开发-->
                                          <el-cascader-panel
                                            :options="options1"
                                            class="m-cascader-noborder"
                                          ></el-cascader-panel>
                                        </div>
                                        <el-button type="primary">福建省<i class="el-icon-arrow-right"></i></el-button>
                                      </el-tooltip>
                                      <el-button type="primary">江西省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">山东省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">河南省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">湖北省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">湖南省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary">广东省<i class="el-icon-arrow-right"></i></el-button>
                                      <el-button type="primary"
                                        >广西壮族自治区<i class="el-icon-arrow-right"></i
                                      ></el-button>
                                      <el-button type="primary">海南省<i class="el-icon-arrow-right"></i></el-button>
                                    </div>
                                  </div>
                                </div>
                                <div class="info-row col-merge">
                                  <div class="info-th f-tl">地区</div>
                                  <div class="info-td p0">
                                    <div class="f-co">
                                      <i class="el-icon-warning f-f16 f-mr5 f-ml10 f-vm"></i>请先选择服务地区
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </el-form-item>
                            <el-form-item label="行业培训属性：" required>
                              <!--竖式表格-->
                              <div class="info-table f-mb15">
                                <div class="info-row col-merge">
                                  <div class="info-th f-tl">行业属性</div>
                                  <div class="info-td">
                                    <el-button>选择人社行业下的培训属性</el-button>
                                    <el-button type="primary" plain>已选择建设行业下的人员属性</el-button>
                                  </div>
                                </div>
                              </div>
                            </el-form-item>
                          </el-form>
                        </el-form-item>
                        <el-form-item label="人员行业属性：" required>
                          <el-form
                            ref="form"
                            :model="form"
                            label-width="auto"
                            labelPosition="top"
                            class="m-form f-p15 bg-gray"
                          >
                            <!--竖式表格-->
                            <div class="info-table">
                              <div class="info-row col-merge">
                                <div class="info-th f-tl">行业属性</div>
                                <div class="info-td">
                                  <el-button>选择人社行业下的培训属性</el-button>
                                  <el-button type="primary" plain>已选择建设行业下的人员属性</el-button>
                                </div>
                              </div>
                            </div>
                          </el-form>
                        </el-form-item>
                        <el-form-item label="业主单位全称：" required>
                          <el-input clearable placeholder="请输入业主单位全称" class="form-l" />
                          <el-checkbox v-model="checked" class="f-ml10">配置简称</el-checkbox>
                        </el-form-item>
                        <el-form-item label="单位简称：">
                          <el-input clearable placeholder="请在此输入单位简称" class="form-l" />
                        </el-form-item>
                        <el-form-item label="业主负责人：">
                          <el-input
                            v-model="form.name"
                            clearable
                            placeholder="请输入业主单位负责人姓名"
                            class="form-l"
                          />
                        </el-form-item>
                        <el-form-item label="手机号：">
                          <el-input
                            v-model="form.name"
                            clearable
                            placeholder="请输入业主单位负责人手机号"
                            class="form-l"
                          />
                        </el-form-item>
                        <el-form-item label="网校性质：" required>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">正式实施</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">DEMO</el-radio>
                        </el-form-item>
                        <el-form-item label="合同签订情况：">
                          <el-radio v-model="radio1" label="1" border class="f-mr10">已签约</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">未签约</el-radio>
                        </el-form-item>
                        <el-form-item label="合同签定时间：">
                          <el-date-picker v-model="value1" type="date" class="form-l" placeholder="请选择合同签定日期">
                          </el-date-picker>
                        </el-form-item>
                        <el-form-item label="归属市场经办：" required>
                          <el-input clearable placeholder="请输入该网校市场经办负责人姓名" class="form-l" />
                        </el-form-item>
                        <el-form-item label="网校背景说明：">
                          <el-input
                            type="textarea"
                            placeholder="请输入网校背景说明，文本框就可"
                            :rows="6"
                            v-model="textarea"
                            maxlength="30"
                            show-word-limit
                          >
                          </el-input>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-card>
          <el-card shadow="never" class="m-card f-mb15 is-header-sticky">
            <el-collapse v-model="activeNames" accordion>
              <el-collapse-item name="2" class="m-collapse-item">
                <template slot="title">网校配置</template>
                <div class="f-plr20 f-pt40">
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
                      <el-form ref="form" :model="form" label-width="auto" class="m-form">
                        <el-form-item label="网校域名：" required>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">华博统一域名</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">业主自有域名</el-radio>
                        </el-form-item>
                        <el-form-item label="网校域名：" required>
                          <el-input clearable placeholder="请输入网校域名，例如www.XXX.com" class="form-l f-mr10" />
                          <div class="f-co">注：域名确认后需技术部配合处理方可生效</div>
                        </el-form-item>
                        <el-form-item label="网校域名：" required>
                          <el-input
                            clearable
                            placeholder="请输入业主提供的网校完整域名，如www.XXX.com"
                            class="form-l f-mr10"
                          />
                          <div class="f-co f-mt5 lh20">
                            注：域名若使用业主自有的，请注意需完成工信部备案和华为云接入流程，<br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;若是https的域名，请确认是否已购买https证书。
                          </div>
                        </el-form-item>
                        <el-form-item label="提供终端：" required>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">PC端</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">移动端（H5）</el-radio>
                        </el-form-item>
                        <el-form-item label="H5域名：" required>
                          <el-input clearable placeholder="https：//zypx.h5.59iedu.com" class="form-l" />
                        </el-form-item>
                        <el-form-item label="短信服务：" required>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">名商通</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">联麓</el-radio>
                        </el-form-item>
                        <el-form-item label="短信帐号：">
                          <el-input clearable placeholder="提供短信服务请在此输入短信帐号信息" class="form-l" />
                        </el-form-item>

                        <el-form-item label="短信密码：">
                          <el-input clearable placeholder="请在此输入短信帐号对应密码" class="form-l" />
                        </el-form-item>
                        <el-form-item label="服务期限：" required>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">长期培训</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">指定期限</el-radio>
                        </el-form-item>
                        <el-form-item label="到期时间：" required>
                          <el-date-picker
                            v-model="value1"
                            type="date"
                            placeholder="请选择网校服务到期时间"
                            class="form-l"
                          >
                          </el-date-picker>
                        </el-form-item>
                        <el-form-item label="友盟统计：" required>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">默认</el-radio>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">业主自有</el-radio>
                          <div class="f-co f-mt5 lh20">
                            注：默认使用友盟统计，如需查看网校的具体统计情况，请联系联系运维部门。本网校ID为XXXX。
                          </div>
                          <div class="f-mt10">
                            <el-input clearable placeholder="请输入友盟统计代码（cnzz code )" class="form-l" />
                          </div>
                        </el-form-item>
                        <el-form-item required>
                          <div slot="label" class="f-vm">
                            完善信息页设置<el-tooltip effect="dark" placement="top" popper-class="m-tooltip"
                              ><i class="el-icon-info m-tooltip-icon f-co f-ml5"></i>
                              <div slot="content">
                                1.强制跳过完善信息页可能会导致网校某些业务逻辑无法正常闭环、字段信息缺失等风险，请在使用此功能前仔细评估后再修改！<br />2.当学员信息不全时，会在登录/报名班级/打印培训证书三个环节触发完善信息页。<br />3.若在登录/报名班级环节不想触发完善信息机制，请选择第二个选项“强制跳过完善信息页面”。<br />4.该设置项在web端和移动端生效。
                              </div> </el-tooltip
                            >：
                          </div>
                          <el-radio v-model="radio1" label="1" border class="f-mr10"
                            >当学员信息不全时，强制触发完善信息页面</el-radio
                          >
                          <el-radio v-model="radio1" label="2" border>强制跳过完善信息页面</el-radio>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-card>
          <el-card shadow="never" class="m-card f-mb15 is-header-sticky">
            <el-collapse v-model="activeNames" accordion>
              <el-collapse-item name="3" class="m-collapse-item">
                <template slot="title">模板配置</template>
                <div class="f-plr20">
                  <div class="m-tit is-small is-border-bottom f-pl0">
                    <span class="tit-txt">人社行业</span>
                  </div>
                  <el-row :gutter="20" type="flex" justify="center">
                    <el-col :lg="20" :xl="18">
                      <el-form label-width="auto" class="m-form f-clear f-mt40">
                        <el-form-item label="PC端模板：">
                          <ul class="m-demo-pic">
                            <li>
                              <div class="demo-pic">
                                <div class="pic">
                                  <img src="./assets/images/s-default.jpg" />
                                </div>
                                <el-checkbox v-model="checked">当前已选</el-checkbox>
                              </div>
                              <div class="demo-pic-info">
                                <p><span class="t">bananer轮播图片尺寸：</span>1920*360</p>
                                <p><span class="t">客服电话图片尺寸：</span>1920*360</p>
                                <p><span class="t">集体报名图片尺寸：</span>200*360</p>
                                <p><span class="t">移动端悬浮图片尺寸：</span>100*100</p>
                                <p><span class="t">友情链接图片尺寸：</span>100*10</p>
                              </div>
                            </li>
                            <li>
                              <div class="demo-pic">
                                <div class="pic"><img src="./assets/images/s-template01.jpg" alt=" " /></div>
                                <el-checkbox v-model="checked2">请选择</el-checkbox>
                              </div>
                              <div class="demo-pic-info">
                                <p><span class="t">bananer轮播图片尺寸：</span>1920*360</p>
                                <p><span class="t">客服电话图片尺寸：</span>1920*360</p>
                                <p><span class="t">集体报名图片尺寸：</span>200*360</p>
                                <p><span class="t">移动端悬浮图片尺寸：</span>100*100</p>
                                <p><span class="t">友情链接图片尺寸：</span>100*10</p>
                              </div>
                            </li>
                            <li>
                              <div class="demo-pic">
                                <div class="pic"><img src="./assets/images/s-template02.jpg" alt=" " /></div>
                                <el-checkbox v-model="checked3">请选择</el-checkbox>
                              </div>
                              <div class="demo-pic-info">
                                <p><span class="t">bananer轮播图片尺寸：</span>1920*360</p>
                                <p><span class="t">客服电话图片尺寸：</span>1920*360</p>
                                <p><span class="t">集体报名图片尺寸：</span>200*360</p>
                                <p><span class="t">移动端悬浮图片尺寸：</span>100*100</p>
                                <p><span class="t">友情链接图片尺寸：</span>100*10</p>
                              </div>
                            </li>
                            <li>
                              <div class="demo-pic">
                                <div class="pic"><img src="./assets/images/s-template04.jpg" alt=" " /></div>
                                <el-checkbox v-model="checked3">请选择</el-checkbox>
                              </div>
                              <div class="demo-pic-info">
                                <p><span class="t">bananer轮播图片尺寸：</span>1920*360</p>
                                <p><span class="t">客服电话图片尺寸：</span>1920*360</p>
                                <p><span class="t">集体报名图片尺寸：</span>200*360</p>
                                <p><span class="t">移动端悬浮图片尺寸：</span>100*100</p>
                                <p><span class="t">友情链接图片尺寸：</span>100*10</p>
                              </div>
                            </li>
                            <li>
                              <div class="demo-pic">
                                <div class="pic"><img src="./assets/images/s-template05.jpg" alt=" " /></div>
                                <el-checkbox v-model="checked3">请选择</el-checkbox>
                              </div>
                              <div class="demo-pic-info">
                                <p><span class="t">bananer轮播图片尺寸：</span>1920*480</p>
                                <p><span class="t">客服电话图片尺寸：</span>220*50</p>
                                <p><span class="t">移动端悬浮图片尺寸：</span>100*100</p>
                                <p><span class="t">友情链接图片尺寸：</span>400*90</p>
                              </div>
                            </li>
                            <li>
                              <div class="demo-pic">
                                <div class="pic"><img src="./assets/images/s-template06.jpg" alt=" " /></div>
                                <el-checkbox v-model="checked3">请选择</el-checkbox>
                              </div>
                              <div class="demo-pic-info">
                                <p><span class="t">bananer轮播图片尺寸：</span>1920*420</p>
                                <p><span class="t">客服电话图片尺寸：</span>300*60</p>
                                <p><span class="t">移动端悬浮图片尺寸：</span>100*100</p>
                                <p><span class="t">友情链接图片尺寸：</span>290*52</p>
                              </div>
                            </li>
                          </ul>
                        </el-form-item>
                        <el-form-item label="移动端（H5）模板：">
                          <ul class="m-demo-pic">
                            <li>
                              <div class="demo-pic">
                                <div class="pic">
                                  <img src="./assets/images/demo-h5-homepage-1.jpg" />
                                </div>
                                <el-checkbox v-model="checked">当前已选</el-checkbox>
                              </div>
                              <div class="demo-pic-info">
                                <p><span class="t">bananer轮播图片尺寸：</span>1920*360</p>
                                <p><span class="t">客服电话图片尺寸：</span>1920*360</p>
                                <p><span class="t">集体报名图片尺寸：</span>200*360</p>
                                <p><span class="t">移动端悬浮图片尺寸：</span>100*100</p>
                                <p><span class="t">友情链接图片尺寸：</span>100*10</p>
                              </div>
                            </li>
                            <li>
                              <div class="demo-pic">
                                <div class="pic">
                                  <img src="./assets/images/demo-h5-homepage-2.png" />
                                </div>
                                <el-checkbox v-model="checked2">请选择</el-checkbox>
                              </div>
                              <div class="demo-pic-info">
                                <p><span class="t">bananer轮播图片尺寸：</span>1920*360</p>
                                <p><span class="t">客服电话图片尺寸：</span>1920*360</p>
                                <p><span class="t">集体报名图片尺寸：</span>200*360</p>
                                <p><span class="t">移动端悬浮图片尺寸：</span>100*100</p>
                                <p><span class="t">友情链接图片尺寸：</span>100*10</p>
                              </div>
                            </li>
                          </ul>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                  <div class="m-tit is-small is-border-bottom f-pl0">
                    <span class="tit-txt">建设行业</span>
                  </div>
                  <el-row :gutter="20" type="flex" justify="center">
                    <el-col :lg="20" :xl="18">
                      <el-form label-width="auto" class="m-form f-clear f-mt40">
                        <el-form-item label="PC端模板：">
                          <ul class="m-demo-pic">
                            <li>
                              <div class="demo-pic">
                                <div class="pic"><img src="./assets/images/demo-web-homepage-1.png" alt=" " /></div>
                                <el-checkbox v-model="checked">当前已选</el-checkbox>
                              </div>
                              <div class="demo-pic-info">
                                <p><span class="t">bananer轮播图片尺寸：</span>1920*360</p>
                                <p><span class="t">客服电话图片尺寸：</span>1920*360</p>
                                <p><span class="t">集体报名图片尺寸：</span>200*360</p>
                                <p><span class="t">移动端悬浮图片尺寸：</span>100*100</p>
                                <p><span class="t">友情链接图片尺寸：</span>100*10</p>
                              </div>
                            </li>
                          </ul>
                        </el-form-item>
                        <el-form-item label="移动端（H5）模板：">
                          <ul class="m-demo-pic">
                            <li>
                              <div class="demo-pic">
                                <div class="pic">
                                  <img src="./assets/images/demo-h5-homepage-1.png" />
                                </div>
                                <el-checkbox v-model="checked">当前已选</el-checkbox>
                              </div>
                              <div class="demo-pic-info">
                                <p><span class="t">bananer轮播图片尺寸：</span>1920*360</p>
                                <p><span class="t">客服电话图片尺寸：</span>1920*360</p>
                                <p><span class="t">集体报名图片尺寸：</span>200*360</p>
                                <p><span class="t">移动端悬浮图片尺寸：</span>100*100</p>
                                <p><span class="t">友情链接图片尺寸：</span>100*10</p>
                              </div>
                            </li>
                          </ul>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-card>
          <el-card shadow="never" class="m-card f-mb15 is-header-sticky">
            <el-collapse v-model="activeNames" accordion>
              <el-collapse-item name="4" class="m-collapse-item">
                <template slot="title">模板配置</template>
                <div class="f-plr20">
                  <div class="m-no-date f-mt20">
                    <img class="img" src="./assets/images/no-data-normal.png" alt="" />
                    <div class="date-bd">
                      <p class="f-f15 f-c9">勾选具体培训行业后可选择模板</p>
                    </div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-card>
          <el-card shadow="never" class="m-card f-mb15 is-header-sticky">
            <el-collapse v-model="activeNames" accordion>
              <el-collapse-item name="5" class="m-collapse-item">
                <template slot="title">管理员帐号</template>
                <div class="f-plr20 f-pt40">
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
                      <el-form ref="form" :model="form" label-width="auto" class="m-form">
                        <el-form-item label="管理员姓名：" required>
                          <el-input clearable placeholder="请输入管理员名称" class="form-l" />
                        </el-form-item>
                        <el-form-item label="管理员帐号：" required>
                          <el-input clearable placeholder="管理员帐号支持6-18位由数字、字母、符号组合" class="form-l" />
                        </el-form-item>
                        <el-form-item label="手机号：">
                          <el-input clearable placeholder="请输入11位有效手机号，可作为登录帐号" class="form-l" />
                        </el-form-item>
                        <el-form-item label="密码：" required>
                          <div class="form-l">
                            <el-input
                              clearable
                              show-password
                              placeholder="请输入6-18位由字母、数字和符号两种及以上组合的密码"
                            />
                            <!--密码安全判断-->
                            <div class="psw-tips">
                              <el-progress :percentage="33.33" color="#e93737" :show-text="false"></el-progress>
                              <!--弱：txt-l，中：txt-m，强：txt-h-->
                              <span class="txt txt-l">弱</span>
                            </div>
                            <div class="psw-tips">
                              <el-progress :percentage="66.66" color="#ee9e2d" :show-text="false"></el-progress>
                              <!--弱：txt-l，中：txt-m，强：txt-h-->
                              <span class="txt txt-m">中</span>
                            </div>
                            <div class="psw-tips">
                              <el-progress :percentage="100" color="#49b042" :show-text="false"></el-progress>
                              <!--弱：txt-l，中：txt-m，强：txt-h-->
                              <span class="txt txt-h">强</span>
                            </div>
                          </div>
                        </el-form-item>
                        <el-form-item label="确认密码：" required>
                          <el-input clearable placeholder="请再次确认密码" class="form-l" />
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-card>
          <el-card shadow="never" class="m-card f-mb15 is-header-sticky">
            <el-collapse v-model="activeNames" accordion>
              <el-collapse-item name="6" class="m-collapse-item">
                <div slot="title" class="f-flex f-align-center f-flex-sub">
                  <span class="tit-txt f-flex-sub">增值服务</span>
                  <a class="f-link f-cb f-mr10 f-f14 f-fn">增值服务说明</a>
                </div>
                <div class="f-p10">
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
                      <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
                        <!--<el-form-item label="分销服务：">-->
                        <!--  <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />-->
                        <!--</el-form-item>-->
                        <el-form-item label="学习规则：">
                          <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                        </el-form-item>
                        <el-form-item label="智能学习：">
                          <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                        </el-form-item>
                        <el-form-item label="分销服务：">
                          <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                          <div class="f-ci f-mt10">请选择开通的分销服务类型，网校开通成功后无法修改，请谨慎操作：</div>
                          <div>
                            <el-radio v-model="radio1" label="1" class="f-mr30">基础版</el-radio>
                            <el-radio v-model="radio1" label="2">专业版</el-radio>
                          </div>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-card>
          <div class="m-btn-bar f-tc is-sticky-1">
            <el-button>取消</el-button>
            <el-button type="primary">确认开通</el-button>
          </div>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        activeNames: ['1', '2', '3', '4'],
        props: { multiple: true },
        radio: 3,
        radio1: '1',
        input: '',
        select: '',
        checked: true,
        checked2: false,
        checked3: false,
        cascader: [
          {
            value: 'fujian',
            label: '福建省',
            children: [
              {
                value: 'fuzhou',
                label: '福州',
                children: [
                  {
                    value: 'gulou',
                    label: '鼓楼区'
                  },
                  {
                    value: 'taijiang',
                    label: '台江区'
                  },
                  {
                    value: 'cangshan',
                    label: '仓山区'
                  },
                  {
                    value: 'mawei',
                    label: '马尾区'
                  },
                  {
                    value: 'jinan',
                    label: '晋安区'
                  },
                  {
                    value: 'changle',
                    label: '长乐区'
                  }
                ]
              },
              {
                value: 'xiamen',
                label: '厦门',
                children: [
                  {
                    value: 'siming',
                    label: '思明区'
                  },
                  {
                    value: 'huli',
                    label: '湖里区'
                  },
                  {
                    value: 'jimei',
                    label: '集美区'
                  },
                  {
                    value: 'haicang',
                    label: '海沧区'
                  },
                  {
                    value: 'tongan',
                    label: '同安区'
                  },
                  {
                    value: 'xiangan',
                    label: '翔安区'
                  }
                ]
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        visible: false,
        fits: ['cover'],
        options1: [
          {
            value: 'fuzhou',
            label: '福州市',
            children: [
              {
                value: 'gulou',
                label: '鼓楼区'
              },
              {
                value: 'taijiang',
                label: '台江区'
              },
              {
                value: 'cangshan',
                label: '仓山区'
              },
              {
                value: 'mawei',
                label: '马尾区'
              },
              {
                value: 'jinan',
                label: '晋安区'
              },
              {
                value: 'changle',
                label: '长乐区'
              }
            ]
          },
          {
            value: 'xiamen',
            label: '厦门市',
            children: [
              {
                value: 'siming',
                label: '思明区'
              },
              {
                value: 'huli',
                label: '湖里区'
              },
              {
                value: 'data',
                label: '集美区'
              }
            ]
          },
          {
            value: 'zhangzhou',
            label: '漳州市'
          },
          {
            value: 'quanzhou',
            label: '泉州市'
          }
        ],
        options2: [
          {
            value: 'fuzhou',
            label: '福建省',
            children: [
              {
                value: 'gulou',
                label: '福州市'
              },
              {
                value: 'taijiang',
                label: '厦门市'
              },
              {
                value: 'cangshan',
                label: '泉州市'
              },
              {
                value: 'mawei',
                label: '漳州市'
              },
              {
                value: 'jinan',
                label: '三明市'
              },
              {
                value: 'changle',
                label: '莆田市'
              },
              {
                value: 'changle',
                label: '南平市'
              },
              {
                value: 'changle',
                label: '龙岩市'
              },
              {
                value: 'changle',
                label: '宁德市'
              }
            ]
          },
          {
            value: 'xiamen',
            label: '江苏省',
            children: [
              {
                value: 'suzhou',
                label: '苏州'
              },
              {
                value: 'wuxi',
                label: '无锡'
              },
              {
                value: 'chanzhou',
                label: '常州'
              },
              {
                value: 'zhenjiang',
                label: '镇江'
              },
              {
                value: 'nanjing',
                label: '南京'
              },
              {
                value: 'nantong',
                label: '南通'
              },
              {
                value: 'yangzhou',
                label: '扬州'
              }
            ]
          },
          {
            value: 'gansu',
            label: '甘肃省'
          },
          {
            value: 'guangdong',
            label: '广东省'
          }
        ]
      }
    },
    methods: {
      onPreview() {
        this.$refs.preview.clickHandler()
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
