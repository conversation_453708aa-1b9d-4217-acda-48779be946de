<template>
  <el-main>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/basic-data/account/monographic-account' }">
        专题管理员账号管理
      </el-breadcrumb-item>
      <el-breadcrumb-item>修改账号</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <!--基本信息 -->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基本信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="formRef" :rules="rules" :model="updateSystemManager" label-width="120px" class="m-form">
                <el-form-item label="帐号：" prop="account">
                  {{ updateSystemManager.account }}
                </el-form-item>
                <el-form-item label="姓名 / 昵称：" prop="name">
                  <el-input
                    v-model="updateSystemManager.name"
                    clearable
                    placeholder="请输入姓名 / 昵称"
                    class="updateSystemManager-m"
                  />
                </el-form-item>
                <el-form-item label="手机号：" prop="phone">
                  <el-input v-model="updateSystemManager.phone" clearable placeholder="请输入手机号" class="form-m" />
                </el-form-item>

                <el-form-item label="启用状态：" required>
                  <el-radio-group v-model="updateSystemManager.status">
                    <el-radio :label="1">启用</el-radio>
                    <el-radio :label="2">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <!--分配角色 -->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">分配角色</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="120px" class="m-form">
                <el-form-item required>
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    class="f-mb20"
                    @click="openAddRoleDialog()"
                    :disabled="isLoading"
                    >添加角色
                  </el-button>
                  <!-- 角色列表 -->
                  <el-table stripe :data="roleList" max-height="500px" class="m-table" v-if="roleList.length">
                    <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                    <el-table-column prop="name" label="角色" width="280"></el-table-column>
                    <el-table-column prop="description" label="说明" align="center"></el-table-column>
                    <el-table-column label="操作" width="100" align="center" fixed="right">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="closeRoleInfo(scope.row.id)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-empty :image-size="40" description="暂无数据，请添加角色~" v-if="!roleList.length" />
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">管理的专题</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="120px" class="m-form">
                <el-form-item>
                  <div slot="label"></div>
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    class="f-mb20"
                    @click="openAddMonographic('add')"
                    :disabled="isLoading"
                    >选择专题</el-button
                  >
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <!--表格-->
          <el-table
            ref="topicsListRef"
            :data="monographicList"
            max-height="500px"
            class="m-table"
            v-if="monographicList.length"
          >
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="专题名称" min-width="150">
              <template slot-scope="scope">{{ scope.row.basicInfo.subjectName }}</template>
            </el-table-column>
            <el-table-column label="专题类型" min-width="150">
              <template slot-scope="scope">
                <el-tooltip placement="top" effect="light" v-if="scope.row.basicInfo.suiteIndustry">
                  <div slot="content">
                    <i class="f-c4">
                      <el-tag type="warning" size="mini" class="f-mr5">行业</el-tag
                      >{{ scope.row.basicInfo.suiteIndustry }}</i
                    >
                  </div>
                  <el-button type="text" class="f-to-three">
                    <i class="f-c4">
                      <el-tag type="warning" size="mini" class="f-mt5"> 行业 </el-tag>
                      {{ scope.row.basicInfo.suiteIndustry }}</i
                    >
                  </el-button>
                </el-tooltip>
                <el-tooltip placement="top" effect="light" v-if="scope.row.basicInfo.suiteArea">
                  <div slot="content">
                    <i class="f-c4"
                      ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag
                      >{{ scope.row.basicInfo.suiteArea }}</i
                    >
                  </div>
                  <el-button type="text" class="f-to-three"
                    ><i class="f-c4"
                      ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag
                      >{{ scope.row.basicInfo.suiteArea }}</i
                    ></el-button
                  >
                </el-tooltip>
                <el-tooltip placement="top" effect="light" v-if="scope.row.basicInfo.unitName">
                  <div slot="content">
                    <i class="f-c4">
                      <el-tag type="warning" size="mini" class="f-mr5">单位</el-tag
                      >{{ scope.row.basicInfo.unitName }}</i
                    >
                  </div>
                  <el-button type="text" class="f-to-three">
                    <i class="f-c4">
                      <el-tag type="warning" size="mini" class="f-mt5"> 单位 </el-tag>
                      {{ scope.row.basicInfo.unitName }}</i
                    >
                  </el-button>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="显示在网校" min-width="120" align="center">
              <template slot-scope="scope">{{ scope.row.basicInfo.displayInSchool ? '显示' : '不显示' }}</template>
            </el-table-column>
            <el-table-column label="状态" min-width="100">
              <template slot-scope="scope">
                <div v-if="scope.row.enable">
                  <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                </div>
                <div v-else>
                  <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button size="mini" type="text" @click="deleteMonographic(scope.row.topicID)"> 取消选择 </el-button>
              </template>
            </el-table-column>
            <el-empty :image-size="40" description="暂无数据，请添加专题~" v-if="!monographicList.length" />
          </el-table>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="goBack">放弃编辑</el-button>
        <el-button :loading="isLoading" type="primary" @click="openAddMonographic('save')">保存</el-button>
      </div>
    </div>
    <choose-list
      :value.sync="showRoleDialog"
      @input="input"
      @confirmDialog="confirmDialog"
      :roleList="roleList"
    ></choose-list>
    <choose-monographic
      :value.sync="showMonographicDialog"
      @input="monographicInput"
      @confirmDialog="monographicConfirmDialog"
      :monographicList="monographicList"
    ></choose-monographic>
  </el-main>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import UserModule from '@api/service/management/user/UserModule'
  import CreateSubAdminRequestVo from '@api/service/management/user/mutation/manager/system-manager/vo/CreateSubAdminRequestVo'
  import { UiPage } from '@hbfe/common'
  import chooseList from '@hbfe/jxjy-admin-account/src/monographic-account/__components__/choose-role.vue'
  import chooseMonographic from '@hbfe/jxjy-admin-account/src/monographic-account/__components__/choose-monographic.vue'
  import ThematicAdministratorItem from '@api/service/management/user/thematic-administrator/ThematicAdministratorItem'
  import ThematicManagementItemBase from '@api/service/management/thematic-management/ThematicManagementItemBase'
  import ThematicManagementList from '@api/service/management/thematic-management/ThematicManagementList'
  class CreateSubAdminRequest extends CreateSubAdminRequestVo {
    certainPassword: string
  }

  import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'

  class RoleInfoResponse extends RoleInfoResponseVo {
    isChecked: boolean
  }

  @Component({
    components: { chooseList, chooseMonographic }
  })
  export default class extends Vue {
    @Ref('formRef') formRef: any

    isLoading = false
    page: UiPage
    roleList: Array<RoleInfoResponse> = new Array<RoleInfoResponse>()
    monographicList: any = []
    rules = {
      account: [{ required: true, message: '请选择登录账户', trigger: 'blur' }],
      name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
      phone: [{ required: true, validator: this.validatePhone, trigger: 'blur' }]
      // password: [{ required: true, validator: this.validatePassword, trigger: 'blur' }],
      // certainPassword: [{ required: true, validator: this.validateAgainPassword, trigger: 'blur' }]
    }
    reg = new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\\W]{8,18}$') // 验证密码的正则是否符合的正则
    regPhone = new RegExp(/^[1]([3-9])[0-9]{9}$/)
    form = new CreateSubAdminRequest()
    updateSystemManager = new ThematicAdministratorItem()
    thematicManagementItemBase = new ThematicManagementItemBase()
    thematicManagementList = new ThematicManagementList()
    isOpenConfig = false
    // 密码强度低
    passwordStrengthLow = false
    // 密码强度中
    passwordStrengthIntermediate = false
    // 密码强度高
    passwordStrengthHigh = false
    //
    showRoleDialog = false
    showMonographicDialog = false

    async created() {
      try {
        this.isLoading = true
        await this.updateSystemManager.getAdministratorDetail(this.$route.params.id)
        await this.updateSystemManager.getAllThematicListByCurrent()
        this.roleList = this.updateSystemManager.roleList as Array<RoleInfoResponse>
        this.monographicList = this.updateSystemManager.thematicList
        this.isLoading = false
      } catch (e) {
        console.log(e)
        this.isLoading = false
      }
    }

    async save() {
      this.isLoading = true

      this.formRef.validate(async (boolean: boolean, value: object) => {
        if (boolean) {
          console.group(
            '%c%s',
            'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#800080, #C71585)',
            'updateSystemManager调试输出'
          )
          console.log(this.updateSystemManager)
          console.count('updateSystemManager输出次数')

          if (this.roleList.length === 0) {
            this.$message.error('角色不能为空')
            this.isLoading = false
            return
          }
          if (this.monographicList.length === 0) {
            this.$message.error('专题不能为空')
            this.isLoading = false
            return
          }

          try {
            this.updateSystemManager.roleList = this.roleList
            this.updateSystemManager.thematicList = this.monographicList
            const res = await this.updateSystemManager.updateAdministrator()

            if (res?.code === 200) {
              this.$message.success('发布成功')
              this.isLoading = false
              this.formRef.resetFields()
              await this.$router.push('/basic-data/account/monographic-account')
            } else {
              this.$message.error(res?.message as string)
              this.isLoading = false
            }
          } catch (e) {
            this.$message.warning('创建失败')
            this.isLoading = false
          }
        } else {
          this.isLoading = false
        }
      })
    }

    // //密码强弱验证
    // checkoutPasswordStrength() {
    //   const reg1 = /^.{1,8}$|^\d{9,}$|^[a-zA-Z]{9,}$|^(?=[\x21-\x7e]+)[^A-Za-z0-9]{9,}$/ //密码低强度正则--纯数字或字母或字符或长度1-8
    //   const reg2 = /^(?!\d+$)[a-zA-Z0-9]{9,}$|^(?![0-9]+$)[^a-zA-Z]{9,}$|^(?![a-zA-Z]+$)[^0-9]{9,}$/ //密码中强度正则--有两种且长度在9-12
    //   const reg3 = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[^0-9a-zA-Z]).{13,}$/ //密码高强度正则--三种都有且长度大于12
    //   const reg4 = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[^0-9a-zA-Z]).{9,12}$/ //密码中强度正则--介于9-12位的三种字符都有的密码
    //   if (!this.updateSystemManager.password) {
    //     this.passwordStrengthLow = false
    //     this.passwordStrengthIntermediate = false
    //     this.passwordStrengthHigh = false
    //   } else if (reg1.test(this.updateSystemManager.password)) {
    //     this.passwordStrengthLow = true
    //     this.passwordStrengthIntermediate = false
    //     this.passwordStrengthHigh = false
    //   } else if (reg2.test(this.updateSystemManager.password)) {
    //     this.passwordStrengthLow = false
    //     this.passwordStrengthIntermediate = true
    //     this.passwordStrengthHigh = false
    //   } else if (reg3.test(this.updateSystemManager.password)) {
    //     this.passwordStrengthLow = false
    //     this.passwordStrengthIntermediate = false
    //     this.passwordStrengthHigh = true
    //   } else if (reg4.test(this.updateSystemManager.password)) {
    //     this.passwordStrengthLow = false
    //     this.passwordStrengthIntermediate = true
    //     this.passwordStrengthHigh = false
    //   }
    // }

    // //密码验证
    // validatePassword(rule: any, value: any, callback: any) {
    //   if (!this.reg.test(this.updateSystemManager.password)) {
    //     callback(new Error('请输入8~18位由数字、字母或符号组成的密码'))
    //   } else {
    //     callback()
    //   }
    // }

    // //再次输入密码验证
    // validateAgainPassword(rule: any, value: any, callback: any) {
    //   if (!this.form.certainPassword) {
    //     callback(new Error('请再次输入密码'))
    //   } else if (this.form.certainPassword != this.updateSystemManager.password) {
    //     callback(new Error('两次输入的密码不一致'))
    //   } else {
    //     callback()
    //   }
    // }

    /**
     * 手机号校验规则
     */
    validatePhone(rule: any, value: any, callback: any) {
      if (!this.updateSystemManager.phone) {
        callback(new Error('请输入手机号码'))
      } else if (!this.regPhone.test(this.updateSystemManager.phone)) {
        callback(new Error('请输入11位真实有效手机号'))
      } else {
        callback()
      }
    }

    // 返回上一页
    goBack() {
      this.$router.push('/basic-data/account/monographic-account')
    }

    //打开
    async openAddRoleDialog() {
      this.showRoleDialog = true
    }
    //网校专题功能是否开启
    async openAddMonographic(type: string) {
      try {
        await this.thematicManagementItemBase.getBaseConfig()
        this.isOpenConfig = this.thematicManagementItemBase.isOpen
        if (!this.isOpenConfig) {
          this.$confirm('网校专题功能暂未开启，请先完成专题基础配置，再进行具体专题页的发布。', '系统提醒', {
            confirmButtonText: '立即前往',
            showCancelButton: false,
            showClose: false,
            closeOnClickModal: false
          }).then(() => {
            this.$router.push({
              path: '/training/special-topics/manage'
            })
          })
        } else {
          await this.hasThematicManagementList(type)
        }
      } catch (e) {
        this.$message.error('系统异常')
      }
    }

    async hasThematicManagementList(type: string) {
      const page = new UiPage()
      page.pageSize = 1
      await this.thematicManagementList.queryList(page)
      if (page.totalSize <= 0) {
        this.$confirm('暂无专题，是否前往新建？', '系统提醒', {
          confirmButtonText: '立即前往',
          showCancelButton: false,
          showClose: false,
          closeOnClickModal: false
        }).then(() => {
          this.$router.push({
            path: '/training/special-topics/add'
          })
        })
      } else {
        if (type === 'add') {
          this.showMonographicDialog = true
        } else {
          await this.save()
        }
      }
    }

    input(showRoleDialog: boolean) {
      this.showRoleDialog = showRoleDialog
    }
    monographicInput(showRoleDialog: boolean) {
      this.showMonographicDialog = showRoleDialog
    }
    //确认
    monographicConfirmDialog(result: any) {
      this.monographicList = result
    }
    deleteMonographic(topicID: string) {
      this.monographicList = this.monographicList.filter((i: any) => i.topicID !== topicID)
    }

    //确认
    confirmDialog(result: Array<RoleInfoResponse>) {
      this.roleList = result
    }

    //删除列表
    closeRoleInfo(id: string) {
      this.roleList = this.roleList.filter((i) => i.id !== id)
    }
  }
</script>
