import {
  RegionSkuPropertyRequest,
  RegionSkuPropertySearchRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import DataResolve from '@api/service/common/utils/DataResolve'
import MsSchemeLearningQueryForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import UserModule from '@api/service/centre/user/UserModule'
import { Page } from '@hbfe/common'

/**
 * @description
 */
class TrainClassUtils {
  /**
   * 将地区数组转换为查询条件
   */
  setRegion(region: string[]): RegionSkuPropertySearchRequest {
    const result = new RegionSkuPropertySearchRequest()
    if (!DataResolve.isWeightyArr(region)) return result
    result.region = [] as RegionSkuPropertyRequest[]
    const option = new RegionSkuPropertyRequest()
    option.province = region.length >= 1 ? region[0] : undefined
    option.city = region.length >= 2 ? region[1] : undefined
    option.county = region.length >= 3 ? region[2] : undefined
    result.region.push(option)
    result.regionSearchType = 1
    return result
  }

  /**
   * 获取培训班json配置
   */
  async queryTrainClassConfig(schemeId: string): Promise<any> {
    if (!schemeId) return ''
    // 获取培训班配置模板jsonString
    const res = await MsSchemeLearningQueryForestage.getSchemeConfigInServicer({
      schemeId,
      needField: []
    })
    let jsonObj
    try {
      jsonObj = JSON.parse(res.data.schemeConfig)
    } catch (e) {
      return ''
    }
    return jsonObj
  }
  /**
   * 批量获取培训班json配置
   */
  async pageTrainClassConfig(schemeIds: string[]) {
    const configMap = new Map<string, any>()
    if (!schemeIds?.length) return configMap
    const page = new Page()
    page.pageSize = schemeIds.length
    // 获取培训班配置模板jsonString
    const res = await MsSchemeLearningQueryForestage.pageSchemeConfigInServicer({
      page,
      schemeIds,
      needField: []
    })
    try {
      res.data?.currentPageData?.map((item) => {
        configMap.set(item.schemeId, JSON.parse(item.schemeConfig))
      })
    } catch (e) {
      return configMap
    }
    return configMap
  }

  /**
   * 根据用户名、身份证获取
   */
  async getUserIdList(userName: string, idCard: string): Promise<string[]> {
    let result: string[] = undefined
    const queryRemote = UserModule.queryUserFactory.queryStudentList
    if (DataResolve.validIsNotEmpty(userName)) queryRemote.request.userName = userName
    if (DataResolve.validIsNotEmpty(idCard)) queryRemote.request.idCard = idCard
    const response = await queryRemote.queryStudentIdList()
    if (DataResolve.isWeightyArr(response)) {
      result = response
    }
    return result
  }
}

export default new TrainClassUtils()
