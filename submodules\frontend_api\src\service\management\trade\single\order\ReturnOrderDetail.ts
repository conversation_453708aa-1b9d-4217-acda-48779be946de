import OrderRefundStatus, { OrderRefundStatusEnum } from '@api/service/common/return-order/enums/OrderRefundStatus'
import { OrderRefundTypeEnum, OnlyReturnList } from '@api/service/common/return-order/enums/OrderRefundType'
import RefundInfo from '@api/service/management/trade/single/order/models/RefundInfo'
import RefundCommodity from '@api/service/management/trade/single/order/models/RefundCommodity'
import { ReturnOrderRecordVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRecordVo'
import RefundProcess from '@api/service/management/trade/single/order/models/RefundProcess'
import MsTradeQueryFront, {
  CommodityAuthInfoResponse,
  CommoditySkuPropertyResponse,
  CommoditySkuResponse,
  ReturnOrderResponse,
  SubOrderInfoResponse1
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'
import StudentUserInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'
import { RefundProcessTypeEnum } from '@api/service/management/trade/single/order/enum/RefundProcessTypeEnum'
import RefundOrderStatusMapType, { RefundOrderStatusEnum } from '@api/service/common/trade/RefundOrderStatusEnum'
import BasicDataQueryBackstage, {
  AdminInfoResponse,
  AdminQueryRequest,
  AdminUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { Page } from '@hbfe/common'
import { SkuPropertyConvertUtils } from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import { SchemeSkuPropertyResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

export default class ReturnOrderDetail {
  /**
   * 退款单号
   */
  returnOrderNo = ''

  /**
   * 订单号
   */
  orderNo = ''

  /**
   * 子订单号
   */
  subOrderNo = ''

  /**
   * 交易流水号
   */
  flowNo = ''

  /**
   * 购买人信息
   */
  buyer: StudentUserInfoVo = new StudentUserInfoVo()

  /**
   * 主单实付金额
   */
  orderPayAmount = 0

  /**
   * 退货状态
   */
  returnOrderStatus: OrderRefundStatusEnum = undefined

  /**
   * 退款类型
   */
  refundType: OrderRefundTypeEnum = undefined

  /**
   * 退货步骤状态
   */
  refundProcessList: Array<RefundProcess> = new Array<RefundProcess>()

  /**
   * 步骤条完成步骤位置
   */
  refundProcessActivePoint = 0

  /**
   * 退款信息
   */
  refundInfo: RefundInfo = new RefundInfo()

  /**
   * 退款记录
   */
  refundRecords: Array<ReturnOrderRecordVo> = new Array<ReturnOrderRecordVo>()

  /**
   * 商品授权信息
   */
  commodityAuthInfo: CommodityAuthInfoResponse = new CommodityAuthInfoResponse()

  /**
   * 退货 / 款商品（由退货 / 退款商品提级而来）
   */
  operationCommodity: RefundCommodity = new RefundCommodity()

  /**
   * UI使用退款状态
   */
  uiReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumApproving

  constructor(returnOrderNo?: string) {
    this.returnOrderNo = returnOrderNo
  }

  /**
   * 模型转化
   * @param dto 后端元数据
   * @param studentUserInfoList 学员用户信息映射列表
   * @param adminUserInfo 管理员用户信息映射列表
   */
  async from(
    dto: ReturnOrderResponse,
    studentUserInfoList: Array<StudentUserInfoVo>,
    adminUserInfo: Array<AdminInfoResponse>
  ) {
    const { returnOrderNo, subOrderInfo, refundInfo, basicData, returnCommodity, refundCommodity, commodityAuthInfo } =
      dto
    this.returnOrderNo = returnOrderNo
    if (subOrderInfo.orderInfo?.buyer?.userId) {
      const buyer = studentUserInfoList.find((it) => it.userId === subOrderInfo.orderInfo.buyer.userId)
      this.buyer.userName = buyer.userName
      this.buyer.idCard = buyer.idCard
      this.buyer.phone = buyer.phone
      this.buyer.loginAccount = buyer.loginAccount
    }
    if (basicData) {
      this.returnOrderStatus = OrderRefundStatus.transferDtoToCurrentEnum(
        basicData.returnOrderStatus,
        basicData.returnCloseReason?.closeType
      )
      this.uiReturnOrderStatue = RefundOrderStatusMapType.transferDtoToUiOperationStatus(
        basicData.returnOrderStatus,
        basicData.returnCloseReason?.closeType
      )
      this.refundType = basicData.returnOrderType
      this.refundInfo.refundInfo = basicData.applyInfo?.description
      this.refundInfo.refundReason = basicData.applyInfo?.reasonContent
      this.refundInfo.refundPrice = basicData.refundAmount
    }
    if (refundInfo) {
      this.refundInfo.refundWay = refundInfo.refundOrderType
    }

    // 拼凑基础信息和商品信息
    if (subOrderInfo) {
      // 子单身上能取到主单的信息以及支付信息（销售渠道、实付金额）
      this.subOrderNo = subOrderInfo.subOrderNo
      if (subOrderInfo.orderInfo) {
        this.orderNo = subOrderInfo.orderInfo.orderNo
        const payment = subOrderInfo.orderInfo.orderPaymentInfo
        if (payment) {
          this.flowNo = payment.flowNo
          this.orderPayAmount = payment.payAmount
        }
      }
    }
    // todo 目前退货单只有一个子单一个商品，故按单商品处理，后续有多个子单多个商品时这里需要做修改
    this.refundInfo.refundCommodityList = new Array<RefundCommodity>()
    let commodity = new RefundCommodity()
    // 仅退货从退货上取，仅退款从退款上取 两个都有默认从退款上取
    if (OnlyReturnList.includes(this.refundType)) {
      const dtoCommodity = returnCommodity?.commoditySku || new CommoditySkuResponse()
      const mergeSku = Object.assign(new CommoditySkuPropertyResponse(), new SchemeSkuPropertyResponse())
      const sku = await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
        Object.assign(mergeSku, dtoCommodity.skuProperty)
      )
      commodity = RefundCommodity.from(dtoCommodity, subOrderInfo || new SubOrderInfoResponse1(), sku)
      commodity.refundQuantity = returnCommodity?.quantity
    } else {
      const dtoCommodity = refundCommodity?.commoditySku || new CommoditySkuResponse()
      const mergeSku = Object.assign(new CommoditySkuPropertyResponse(), new SchemeSkuPropertyResponse())
      const sku = await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
        Object.assign(mergeSku, dtoCommodity.skuProperty)
      )
      commodity = RefundCommodity.from(dtoCommodity, subOrderInfo || new SubOrderInfoResponse1(), sku)
      commodity.refundQuantity = refundCommodity?.quantity
    }
    // 换班订单比较特殊，换班的订单退货退款商品都有，退款上是原商品信息，退货上新商品信息
    if (commodity.exchanged || commodity.isExchangeIssue) {
      const curReturnCommodity = returnCommodity?.commoditySku || new CommoditySkuResponse()
      const curRefundCommodity = refundCommodity?.commoditySku || new CommoditySkuResponse()
      commodity.commodityId = curReturnCommodity.commoditySkuId
      commodity.commodityName = curReturnCommodity.saleTitle
      commodity.originCommodityId = curRefundCommodity.commoditySkuId
      commodity.originCommodityName = curRefundCommodity.saleTitle
      if (curRefundCommodity.issueInfo) {
        commodity.originTrainingPeriodId = curRefundCommodity.issueInfo.issueId
        commodity.originTrainingPeriodName = curRefundCommodity.issueInfo.issueName
        commodity.originTrainingPeriodNo = curRefundCommodity.issueInfo.issueNum
      }
      if (curReturnCommodity.issueInfo) {
        commodity.trainingPeriodId = curReturnCommodity.issueInfo.issueId
        commodity.trainingPeriodNo = curReturnCommodity.issueInfo.issueNum
        commodity.trainingPeriodName = curReturnCommodity.issueInfo.issueName
      }
    }

    if (commodity.commodityId) {
      this.operationCommodity = commodity
      this.refundInfo.refundCommodityList.push(commodity)
    }

    // 处理分销信息
    this.commodityAuthInfo = commodityAuthInfo

    // 处理退款记录
    this.refundRecords = new Array<ReturnOrderRecordVo>()
    this.refundRecords.push(
      ReturnOrderRecordVo.from(
        RefundOrderStatusEnum.RefundOrderStatusEnumApproving,
        dto,
        studentUserInfoList,
        adminUserInfo
      )
    )
    if (this.uiReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumApproving) {
      // 不用额外处理
    } else if (
      [
        RefundOrderStatusEnum.RefundOrderStatusEnumHandling,
        RefundOrderStatusEnum.RefundOrderStatusEnumRefuse,
        RefundOrderStatusEnum.RefundOrderStatusEnumFail
      ].includes(this.uiReturnOrderStatue)
    ) {
      this.refundRecords.push(
        ReturnOrderRecordVo.from(
          this.uiReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumFail
            ? RefundOrderStatusEnum.RefundOrderStatusEnumHandling
            : this.uiReturnOrderStatue,
          dto,
          studentUserInfoList,
          adminUserInfo
        )
      )
    } else if (this.uiReturnOrderStatue == RefundOrderStatusEnum.RefundOrderStatusEnumCancel) {
      this.refundRecords.push(
        ReturnOrderRecordVo.from(
          RefundOrderStatusEnum.RefundOrderStatusEnumCancel,
          dto,
          studentUserInfoList,
          adminUserInfo
        )
      )
    } else {
      this.refundRecords.push(
        ReturnOrderRecordVo.from(
          RefundOrderStatusEnum.RefundOrderStatusEnumHandling,
          dto,
          studentUserInfoList,
          adminUserInfo
        )
      )
      this.refundRecords.push(
        ReturnOrderRecordVo.from(
          RefundOrderStatusEnum.RefundOrderStatusEnumSuccess,
          dto,
          studentUserInfoList,
          adminUserInfo
        )
      )
    }
    // 组装步骤条信息
    this.refundProcessList = new Array<RefundProcess>()
    const applyProcess = RefundProcess.from(RefundProcessTypeEnum.submitApplication, dto, this.returnOrderStatus) // 提交申请
    const approveProcess = RefundProcess.from(RefundProcessTypeEnum.approveApplication, dto, this.returnOrderStatus) // 审批申请
    const handleReturnProcess = RefundProcess.from(RefundProcessTypeEnum.handleReturn, dto, this.returnOrderStatus) // 处理退货
    const refundConfirmProcess = RefundProcess.from(
      RefundProcessTypeEnum.refundConfirmation,
      dto,
      this.returnOrderStatus
    ) // 退款确认
    const handleRefundProcess = RefundProcess.from(RefundProcessTypeEnum.processRefund, dto, this.returnOrderStatus) // 处理退款
    const completeProcess = RefundProcess.from(RefundProcessTypeEnum.completeProcess, dto, this.returnOrderStatus) // 完成

    this.refundProcessList.push(applyProcess)
    this.refundProcessList.push(approveProcess)
    // 仅退货
    if ([OrderRefundTypeEnum.returnOnly, OrderRefundTypeEnum.partialReturnNoRefund].includes(this.refundType)) {
      this.refundProcessList.push(handleReturnProcess)
    } else if ([OrderRefundTypeEnum.refundOnly, OrderRefundTypeEnum.partialRefundNoReturn].includes(this.refundType)) {
      // 仅退款
      if (dto.needConfirmRefund) {
        this.refundProcessList.push(refundConfirmProcess)
      }
      this.refundProcessList.push(handleRefundProcess)
    } else {
      // 有退货有退款
      this.refundProcessList.push(handleReturnProcess)
      if (dto.needConfirmRefund) {
        this.refundProcessList.push(refundConfirmProcess)
      }
      this.refundProcessList.push(handleRefundProcess)
    }
    this.refundProcessList.push(completeProcess)

    // 判断步骤条完成位置
    let processActivePoint = 0
    for (let i = 0; i < this.refundProcessList.length; i++) {
      const process = this.refundProcessList[i]
      if (process.processEnd) {
        processActivePoint = i
      }
    }
    this.refundProcessActivePoint = processActivePoint
  }

  /**
   * 查询退款单详情
   */
  async queryDetail() {
    const res = await MsTradeQueryFront.getReturnOrderInServicer(this.returnOrderNo)

    if (res?.status && res.status.isSuccess() && res?.data) {
      // 提取所有需要转换的学员id  一次性取出所以学员信息
      const userIds = new Array<string>()
      const data = res.data || new ReturnOrderResponse()
      if (data.subOrderInfo?.orderInfo?.buyer?.userId) {
        userIds.push(data.subOrderInfo?.orderInfo?.buyer?.userId)
      }
      if (data.approvalInfo?.approveUser?.userId) {
        userIds.push(data.approvalInfo?.approveUser?.userId)
      }
      if (data.basicData?.applyInfo?.applyUser?.userId) {
        userIds.push(data.basicData?.applyInfo?.applyUser?.userId)
      }
      if (data.confirmUser?.userId) {
        userIds.push(data.confirmUser?.userId)
      }
      if (data.basicData?.returnCloseReason?.cancelUser?.userId) {
        userIds.push(data.basicData?.returnCloseReason?.cancelUser?.userId)
      }
      const queryStudentUser = new QueryStudentList()

      // 这边因为有的用户是学员有的用户是管理员，而且区分不开，故两个都查
      let studentUserInfoList = new Array<StudentUserInfoVo>()
      let adminUserInfoList = new Array<AdminInfoResponse>()
      if (userIds) {
        const studentRes = await queryStudentUser.queryStudentListInSubject(userIds)
        if (studentRes.data.length) {
          studentUserInfoList = studentRes.data
        }
        adminUserInfoList = await this.getAdminUserByIds(userIds)
      }
      await this.from(res.data, studentUserInfoList, adminUserInfoList)
    }
  }

  /**
   * 查询退款单详情 （分销）
   */
  async queryDetailByFx() {
    const res = await MsTradeQueryFront.getReturnOrderInDistributor(this.returnOrderNo)

    if (res?.status && res.status.isSuccess() && res?.data) {
      // 提取所有需要转换的学员id  一次性取出所以学员信息
      const userIds = new Array<string>()
      const data = res.data || new ReturnOrderResponse()
      if (data.subOrderInfo?.orderInfo?.buyer?.userId) {
        userIds.push(data.subOrderInfo?.orderInfo?.buyer?.userId)
      }
      if (data.approvalInfo?.approveUser?.userId) {
        userIds.push(data.approvalInfo?.approveUser?.userId)
      }
      if (data.basicData?.applyInfo?.applyUser?.userId) {
        userIds.push(data.basicData?.applyInfo?.applyUser?.userId)
      }
      if (data.confirmUser?.userId) {
        userIds.push(data.confirmUser?.userId)
      }
      if (data.basicData?.returnCloseReason?.cancelUser?.userId) {
        userIds.push(data.basicData?.returnCloseReason?.cancelUser?.userId)
      }
      const queryStudentUser = new QueryStudentList()

      // 这边因为有的用户是学员有的用户是管理员，而且区分不开，故两个都查
      let studentUserInfoList = new Array<StudentUserInfoVo>()
      let adminUserInfoList = new Array<AdminInfoResponse>()
      if (userIds) {
        const studentRes = await queryStudentUser.queryStudentListInSubject(userIds)
        if (studentRes.data.length) {
          studentUserInfoList = studentRes.data
        }
        adminUserInfoList = await this.getAdminUserByIds(userIds)
      }
      await this.from(res.data, studentUserInfoList, adminUserInfoList)
    }
  }

  /**
   * 获取管理员信息
   * @param userIds 管理员用户Id
   * @private
   */
  async getAdminUserByIds(userIds: string[]) {
    const adminRequest = new AdminQueryRequest()
    adminRequest.user = new AdminUserRequest()
    adminRequest.user.userIdList = userIds
    const page = new Page()
    page.pageNo = 1
    page.pageSize = userIds.length
    const adminRes = await BasicDataQueryBackstage.pageAdminInfoInSubProject({
      page,
      request: adminRequest
    })
    if (adminRes?.data?.currentPageData?.length) {
      return adminRes.data.currentPageData
    } else {
      return []
    }
  }
}
