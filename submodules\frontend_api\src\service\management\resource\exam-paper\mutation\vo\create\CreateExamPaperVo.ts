import {
  AutomaticPublishPatternRequest,
  PaperPublishConfigureCreateRequest
} from '@api/ms-gateway/ms-examextraction-v1'
import ExamPublishPattern from '../common/ExamPublishPattern'
import AutomaticExamPaperVo from '../common/AutomaticExamPaperVo'
import { PublishPatternTypes } from '@api/service/management/resource/exam-paper/enum/ExamPaperPublishPatternTypes'

/*
  创建试卷
*/
class CreateExamPaperVo<
  T extends ExamPublishPattern = AutomaticExamPaperVo
> extends PaperPublishConfigureCreateRequest {
  // 出卷配置名称
  name?: string = ''

  /**
   * 适用范围 用于筛选自定义的分类
   * 这个 1 测验卷 2考试卷
   */
  usageScope = 2

  // 出卷模式
  declare publishPattern: T

  /* 
    试卷分类id
  */
  paperPublishConfigureCategoryId = '-1'

  /* 
    试卷分类名称
  */
  paperPublishConfigureCategoryName = ''

  /**
   * 是否是草稿    1-是 2-不是
   */
  isDraft = 2

  /**
   * 课程来源
   * 1 考试和测验用 [默认为1]
    @see UserCourseSources
   */
  private userCourseSource = 1

  /**
   * @description: 初始化参数
   * @param {object} publishPatternConstructor: T的构造函数（把类传进来
   * @return {void}
   */
  constructor(publishPatternConstructor: { new (): T }) {
    super()
    this.publishPattern = new publishPatternConstructor()
  }

  /**
   * @description: 试卷模型转换
   * @return {object}
   */
  toDto() {
    const paramsDto = new PaperPublishConfigureCreateRequest()
    paramsDto.name = this.name
    paramsDto.usageScope = this.usageScope
    paramsDto.paperPublishConfigureCategoryId = this.paperPublishConfigureCategoryId
    paramsDto.isDraft = this.isDraft
    // 出卷模式
    if (this.publishPattern.type === PublishPatternTypes.AutomaticPublishPattern) {
      // 智能出卷
      const pattern = new AutomaticPublishPatternRequest()
      paramsDto.publishPattern = this.publishPattern.toDto(pattern)
    }
    return paramsDto
  }
}

export default CreateExamPaperVo
