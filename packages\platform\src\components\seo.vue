<template>
  <el-card shadow="never" class="m-card f-mb15">
    <div class="f-p10">
      <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
        配置该网站的标题、描述、关键词，使之能够被搜索引擎更容易收录采集，便于学员在通过搜索引擎（如：百度）搜索到的结果排名较为靠前
      </el-alert>
      <el-row type="flex" justify="center" class="width-limit">
        <el-col :md="20" :lg="16" :xl="13">
          <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
          <el-form ref="form" :model="seoConfigModel.config" label-width="auto" class="m-form">
            <el-form-item label="标题：">
              <el-input
                v-model="webPortalInfo.title"
                clearable
                placeholder="读取“门户信息配置 - 平台名称”字段内容"
                class="wp-95"
                disabled
              />

              <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                <div slot="content">请前往“门户信息配置 - 平台名称”修改</div>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="描述：">
              <el-input
                type="textarea"
                placeholder="请输入该网校的描述"
                :rows="6"
                v-model="seoConfigModel.config.description"
                class="wp-95"
              />
            </el-form-item>
            <el-form-item label="关键词：">
              <el-input
                v-model="seoConfigModel.config.keywords"
                clearable
                placeholder="多个关键词请使用英文(,)逗号隔开"
                class="wp-95"
              />
            </el-form-item>
          </el-form>
          <div class="m-btn-bar f-tc">
            <el-button @click="doCancel" :loading="isLoading">取消</el-button>
            <el-button type="primary" @click="saveConfig" :loading="isLoading">保存</el-button>
          </div>
        </el-col>
      </el-row>
    </div>
    <give-up-dialog :give-up-model="uiConfig.giveUpModel" @callBack="resetData"></give-up-dialog>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import SeoConfigModel from '@api/service/management/online-school-config/seo-setting/SeoConfigModel'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import PortalVo from '@api/service/common/online-school-config/vo/PortalVo'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import { debounce, bind } from 'lodash-decorators'
  @Component({
    components: {
      GiveUpDialog
    }
  })
  export default class extends Vue {
    /**
     * 保存配置模型
     */
    seoConfigModel = new SeoConfigModel()

    /**
     * 门户基础信息
     */
    webPortalInfo: PortalVo = OnlineSchoolConfigModule.queryPortal.webPortalInfo

    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }

    isLoading = false

    async created() {
      await this.getSeoConfig()
    }

    /**
     * 保存配置
     */
    @bind
    @debounce(200)
    async saveConfig() {
      try {
        this.isLoading = true
        await this.seoConfigModel.saveConfig()
        this.$message.success('保存成功')
      } catch (error) {
        this.$message.error('保存失败')
        console.log(error)
      } finally {
        this.isLoading = false
        setTimeout(() => {
          this.getSeoConfig()
        }, 300)
      }
    }

    /**
     * 获取seo配置
     */
    async getSeoConfig() {
      try {
        await this.seoConfigModel.getConfig()
      } catch (error) {
        console.log(error)
      }
    }

    /**
     * 取消按钮
     */
    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
    }

    /**
     * 重置数据
     */
    async resetData() {
      await this.getSeoConfig()
    }
  }
</script>
