import { Page, Response } from '@hbfe/common'
import MsBasicDataQueryBackstageGateway, {
  AdminInfoResponse,
  AdminQueryRequest,
  AdminUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import AdminUserInfoVo from './vo/AdminUserInfoVo'
import PageAdminInfoResponse from './vo/PageAdminInfoResponse'
import PageAdminListRequest from './vo/PageAdminListRequest'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import basicdata, { RoleResponse } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import * as GraphqlImporter from '@api/ms-gateway/ms-basicdata-domain-gateway-v1/graphql-importer'
import AuthorityModule from '@api/service/management/authority/AuthorityModule'
import UserModule from '@api/service/management/user/UserModule'

/**
 * 查询管理员列表
 */
class QueryManager {
  /**
   * @description: 根据管理员id集合查询管理员信息
   * @param {UiPage} uipage
   * @param {Array} userIds
   */
  async queryManager(userIds: Array<string>): Promise<Response<Array<AdminUserInfoVo>>> {
    const params = new AdminQueryRequest()
    params.user = new AdminUserRequest()
    params.user.userIdList = [...new Set(userIds)]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = params.user?.userIdList?.length || 0
    const res = await MsBasicDataQueryBackstageGateway.pageAdminInfoInServicer({ page: page, request: params })
    const response = new Response<Array<AdminUserInfoVo>>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.status = res.status
    response.data = new Array<AdminUserInfoVo>()
    res.data?.currentPageData?.map((item: AdminInfoResponse) => {
      const temp = new AdminUserInfoVo()
      temp.from(item)
      response.data.push(temp)
    })
    return response
  }
  /**
   * @description: 根据管理员id集合查询管理员信息-分销商
   * @param {UiPage} uipage
   * @param {Array} userIds
   */
  async queryManagerInDistributor(userIds: Array<string>): Promise<Response<Array<AdminUserInfoVo>>> {
    const params = new AdminQueryRequest()
    params.user = new AdminUserRequest()
    params.user.userIdList = [...new Set(userIds)]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = params.user?.userIdList?.length || 0
    const res = await MsBasicDataQueryBackstageGateway.pageAdminInInDistributor({ page: page, request: params })
    const response = new Response<Array<AdminUserInfoVo>>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.status = res.status
    response.data = new Array<AdminUserInfoVo>()
    res.data?.currentPageData?.map((item: AdminInfoResponse) => {
      const temp = new AdminUserInfoVo()
      temp.from(item)
      response.data.push(temp)
    })
    return response
  }
  /**
   * 查询管理员列表 Tips:含管理员账号和地区管理员,入参不同 type  1：运营管理员 2：地区管理员
   * @param page 分页
   * @param request 查询参数
   */
  async queryPageAdminList(
    page: Page,
    pageAdminListRequest?: PageAdminListRequest
  ): Promise<Array<PageAdminInfoResponse>> {
    // * 加载全部角色Id
    const request = PageAdminListRequest.from(pageAdminListRequest)
    const { data } = await MsBasicDataQueryBackstageGateway.pageOnlineSchoolAdminInfoInServicer({ page, request })
    const temp = data.currentPageData.map((item) => PageAdminInfoResponse.from(item))
    // * 角色赋值
    let roleIds: string[] = []
    data.currentPageData.map((res) => {
      if (res.roleList && res.roleList.length) {
        res.roleList.map((item) => {
          if (item.roleId) {
            roleIds.push(item.roleId)
          }
        })
      }
    })
    roleIds = Array.from(new Set(roleIds))
    const roleMap = await this.batchQueryRoleDetail(roleIds)
    if (temp && temp.length) {
      temp.map((res) => {
        if (res.roleList && res.roleList.length)
          res.roleList.map((item) => {
            item.roleName = roleMap.get(item.roleId).name
          })
      })
    }
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    return temp
  }

  /**
   * 查询分销商管理员列表
   * @param page
   * @param pageAdminListRequest
   */
  async queryPageDistributorAdminList(
    page: Page,
    pageAdminListRequest?: PageAdminListRequest
  ): Promise<Array<PageAdminInfoResponse>> {
    // * 加载全部角色Id
    const request = PageAdminListRequest.toContractProviderAdminQueryRequest(pageAdminListRequest)
    const { data } = await MsBasicDataQueryBackstageGateway.pageOnlineSchoolAdminInfoInFxpt({ page, request })
    const temp = data.currentPageData.map((item) => PageAdminInfoResponse.from(item))
    // * 角色赋值
    let roleIds: string[] = []
    data.currentPageData.map((res) => {
      if (res.roleList && res.roleList.length) {
        res.roleList.map((item) => {
          if (item.roleId) {
            roleIds.push(item.roleId)
          }
        })
      }
    })
    roleIds = Array.from(new Set(roleIds))
    const roleMap = await this.batchQueryRoleDetail(roleIds)
    if (temp && temp.length) {
      temp.map((res) => {
        if (res.roleList && res.roleList.length)
          res.roleList.map((item) => {
            const role = roleMap.get(item.roleId)
            if (role) {
              item.roleName = role.name
              item.roleCode = role.code
            }
          })
      })
    }
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    return temp
  }

  /**
   * 带ID查询管理员详情
   * @param userId
   */
  async queryAdminInfo(userId: string): Promise<PageAdminInfoResponse> {
    const { data } = await MsBasicDataQueryBackstageGateway.getAdminInfoInServicer(userId)
    return PageAdminInfoResponse.from(data)
  }

  /**
   * 获取用户信息
   */
  async batchQueryUserInfo(ids: string[]) {
    // 根据用户ID获取用户信息
    const response = await this.queryManager(ids)
    const userIdMap: Map<string, AdminUserInfoVo> = new Map()
    response.data?.length &&
      response.data.forEach((item) => {
        userIdMap.set(item.userId, item)
      })
    return userIdMap
  }

  /**
   * 获取用户信息-分销商
   */
  async batchQueryUserInfoInDistributor(ids: string[]) {
    // 根据用户ID获取用户信息
    const response = await this.queryManagerInDistributor(ids)
    const userIdMap: Map<string, AdminUserInfoVo> = new Map()
    response.data?.length &&
      response.data.forEach((item) => {
        userIdMap.set(item.userId, item)
      })
    return userIdMap
  }

  private async batchQueryRoleDetail(ids: string[]) {
    const rew = new RewriteGraph<RoleResponse, string>(basicdata._commonQuery, GraphqlImporter.findRoleById)
    await rew.request(ids)
    return rew.itemMap
  }
}

export default QueryManager
