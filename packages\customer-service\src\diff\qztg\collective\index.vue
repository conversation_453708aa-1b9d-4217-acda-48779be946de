<route-meta>
  {
  "isMenu": true,
  "title": "集体报名咨询",
  "sort": 2,
  "icon": "icon_menhuxinxiguanli"
  }
</route-meta>
<script lang="ts">
  import { Component } from 'vue-property-decorator'
  import BatchInfo from '@hbfe/jxjy-admin-customerService/src/diff/qztg/collective/components/batch-info.vue'
  import InvoiceInfo from '@hbfe/jxjy-admin-customerService/src/diff/qztg/collective/components/invoice-info.vue'
  import Collective from '@hbfe/jxjy-admin-customerService/src/collective/index.vue'
  @Component({
    components: { BatchInfo, InvoiceInfo }
  })
  export default class extends Collective {}
</script>
