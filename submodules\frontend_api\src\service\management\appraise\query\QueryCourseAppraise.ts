import MsCourseLearningQueryFrontGatewayCourseLearningForestage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import { UiPage } from '@hbfe/common'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import MsBasicdataQueryFrontGatewayBasicDataQueryBackstage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import CourseAppraiseListDetail from '@api/service/management/appraise/query/vo/CourseAppraiseListDetail'

class QueryCourseAppraise {
  get loadingCourseInfo(): boolean {
    return this._loadingCourseInfo
  }

  get loadingUserInfo(): boolean {
    return this._loadingUserInfo
  }

  get courseId(): string {
    return this._courseId
  }

  private readonly _courseId: string
  list: Array<CourseAppraiseListDetail> = new Array<CourseAppraiseListDetail>()
  page: UiPage = new UiPage()
  private _loadingUserInfo = false
  private _loadingCourseInfo = false

  constructor(courseId: string) {
    this._courseId = courseId
  }

  /**
   * 查询分页
   */
  async queryCourseAppraise() {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageStudentCourseAppraiseInServicer({
      page: this.page,
      courseId: this._courseId
    })
    this.list = result.data.currentPageData.map(CourseAppraiseListDetail.from)
    this.fillCourseName()
    this.fillAppraiseUserInfo()
  }

  /**
   * 查询课程信息
   * @private
   */
  private async fillCourseName() {
    this._loadingCourseInfo = true
    const courseIdList = this.list.map(detail => detail.courseId)
    const courseList = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseInServicer({
      request: {
        courseIdList
      }
    })
    if (courseList.status.isSuccess()) {
      this.list.forEach(detail => {
        const course = courseList.data.currentPageData.find(remoteCourse => remoteCourse.id === detail.courseId)
        if (course) {
          detail.courseName = course.name
        }
      })
    }
    this._loadingCourseInfo = false
  }

  private async fillAppraiseUserInfo() {
    this._loadingUserInfo = true
    const userIdList = this.list.map(detail => detail.appraiser.id)
    const userList = await MsBasicdataQueryFrontGatewayBasicDataQueryBackstage.pageStudentInfoInServicer({
      request: {
        user: {
          userIdList
        }
      }
    })
    if (userList.status.isSuccess()) {
      this.list.forEach(detail => {
        const user = userList.data.currentPageData.find(
          remoteUser => remoteUser.userInfo.userId === detail.appraiser.id
        )
        if (user) {
          detail.appraiser.name = user.userInfo.userName
          detail.appraiser.sex = user.userInfo.gender
          detail.appraiser.photo = user.userInfo.photo
        }
      })
    }
    this._loadingUserInfo = false
  }
}

export default QueryCourseAppraise
