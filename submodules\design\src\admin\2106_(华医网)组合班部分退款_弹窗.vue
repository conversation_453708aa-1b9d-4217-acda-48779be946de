<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--发起退货/款-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">发起退货/款</el-button>
        <el-drawer
          title="发起退货/款"
          :visible.sync="dialog1"
          :direction="direction"
          size="640px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              请根据实际情况选择退货/款类型，请仔细确认！
            </el-alert>
            <el-row>
              <el-col :span="24">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="退货/款物品" required>
                    <div class="m-refund-good-group">
                      <el-checkbox class="m-refund-good">
                        <div class="tit f-to">
                          <i class="el-icon-s-goods"></i
                          >培训方案名称名称培训方案名称名称培训方案名称名称培训方案名称名称培训方案名称名称
                        </div>
                        <div class="tag">
                          <el-tag type="info" size="small">选课规则</el-tag>
                          <el-tag type="info" size="small">2024年</el-tag>
                          <el-tag type="info" size="small">人社行业</el-tag>
                          <el-tag type="info" size="small">福建省</el-tag>
                          <el-tag type="info" size="small">公需科目</el-tag>
                          <el-tag type="info" size="small">60学时</el-tag>
                        </div>
                      </el-checkbox>
                      <el-checkbox class="m-refund-good f-mt10" disabled>
                        <div class="tit f-to">
                          <i class="el-icon-s-goods"></i
                          >培训方案名称名称培训方案名称名称培训方案名称名称培训方案名称名称培训方案名称名称
                        </div>
                        <div class="tag">
                          <el-tag type="info" size="small">选课规则</el-tag>
                          <el-tag type="info" size="small">2024年</el-tag>
                          <el-tag type="info" size="small">人社行业</el-tag>
                          <el-tag type="info" size="small">福建省</el-tag>
                          <el-tag type="info" size="small">公需科目</el-tag>
                          <el-tag type="info" size="small">60学时</el-tag>
                        </div>
                        <div class="bottom">
                          <div class="status">已退货</div>
                        </div>
                      </el-checkbox>
                      <div class="total">合计实付金额<span class="f-cr">500.00</span>元</div>
                    </div>
                  </el-form-item>
                  <el-form-item label="退货/款类型" required>
                    <el-select clearable placeholder="请选择">
                      <el-option label="选项1" value=""></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="退款金额" required>
                    <el-input v-model="input" clearable placeholder="退款金额上限¥500，可自行修改" />
                  </el-form-item>
                  <el-form-item label="退款原因" required>
                    <el-select clearable placeholder="请选择">
                      <el-option label="选项1" value=""></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="退款说明" required>
                    <el-input type="textarea" :rows="5" placeholder="请输入退款说明"> </el-input>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--查看退货/款记录-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20 f-mb20">查看退货/款记录</el-button>
        <el-dialog title="查看退货/款记录" :visible.sync="dialog2" width="1000px" class="m-dialog">
          <el-descriptions title="">
            <el-descriptions-item label="物品名称">XXXXXXX</el-descriptions-item>
            <el-descriptions-item label="金额">¥500</el-descriptions-item>
            <el-descriptions-item label="数量">1</el-descriptions-item>
            <el-descriptions-item label="当前商品状态">已部分退款，未退货</el-descriptions-item>
          </el-descriptions>
          <el-table stripe :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="发起退款时间" min-width="150">
              <template>2025-02-07 17:34:18</template>
            </el-table-column>
            <el-table-column label="退款原因" min-width="150">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  不想要了
                </div>
                <div v-else>
                  不想要了
                </div>
              </template>
            </el-table-column>
            <el-table-column label="不想要了" min-width="120">
              <template>仅部分退款</template>
            </el-table-column>
            <el-table-column label="退款金额" min-width="100">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  100
                </div>
                <div v-else>
                  100
                </div>
              </template>
            </el-table-column>
            <el-table-column label="退货内容" min-width="100">
              <template>退货内容</template>
            </el-table-column>
            <el-table-column label="退款状态" min-width="100">
              <template>退款成功</template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template>
                <el-button type="text">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
          <div slot="footer">
            <el-button type="primary">立即前往</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
