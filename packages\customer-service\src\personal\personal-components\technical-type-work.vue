<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-09-20 15:27:08
 * @LastEditors: chenweinian
 * @LastEditTime: 2023-09-20 20:18:26
 * @Description:
-->
<route-meta>
  {
  "title": "工种选择器"
  }
  </route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    filterable
    clearable
  >
    <el-option v-for="item in typeWork" :label="item.name" :value="item.propertyId" :key="item.propertyId"></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
  import QueryPersonIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'

  @Component
  export default class extends Vue {
    selected = ''
    technologyLevelOptions: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
    //工勤行业工种
    typeWork: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Prop({
      type: String,
      default: '请选择工种'
    })
    placeholder: string

    //行业属性分类id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    // 行业id
    @Prop({
      type: String,
      default: ''
    })
    industryId: string

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }
    async created() {
      this.typeWork = await QueryPersonIndustry.getJobCategory(this.industryId)
    }
  }
</script>
