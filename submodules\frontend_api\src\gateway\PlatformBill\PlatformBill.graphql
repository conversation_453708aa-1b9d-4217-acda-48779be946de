schema {
	query:Query
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取发票自动开票配置信息
		@return
	"""
	getAutoBillConfig:BillConfigDTO
	lazyPageBill(page:Page,queryParams:LazyBillRequestParam):LazyBillResponsePage @page(for:"LazyBillResponse")
	lazyStatisticsBill(queryParams:LazyBillRequestParam):LazyBillStatisticsResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""发票查询参数
	<AUTHOR> create 2020/4/22 13:47
"""
input LazyBillRequestParam @type(value:"com.fjhb.fjszyws.integrative.service.trade.dto.request.LazyBillRequestParam") {
	"""集体缴费批次号"""
	batchNo:String
	"""集体缴费单位id"""
	batchUnitId:String
	"""是否集体缴费发票"""
	isBatchBill:Boolean
	"""收款账号id"""
	receiptAccount:String
	"""主订单号"""
	orderNos:[String]
	"""购买人姓名"""
	buyerName:String
	"""买家id"""
	buyerId:String
	"""购买人身份证号"""
	buyerIdentity:String
	"""购买人手机号"""
	buyerPhoneNumber:String
	"""蓝票开具状态：0未开票、1开票中、2开票成功、3开票失败"""
	blueState:[Int]
	"""红票开具状态：0未开票、1开票中、2开票成功、3开票失败"""
	redState:[Int]
	"""是否冲红
		true 是
		false 否
	"""
	redBill:Boolean
	"""是否冻结"""
	frozen:Boolean
	"""发票代码"""
	billCode:String
	"""发票验证码"""
	billVeriCode:String
	"""索取发票开始时间"""
	getBillStartTime:DateTime
	"""索取发票结束时间"""
	getBillEndTime:DateTime
	"""发票类型：1普通发票，2增值税普通发票，3增值税专用发票"""
	invoiceType:String
	"""是否开票"""
	makeInvoice:Boolean
	"""开票开始时间"""
	makeInvoiceStartTime:DateTime
	"""开票结束时间"""
	makeInvoiceEndTime:DateTime
	"""是否非税务发票"""
	noTaxBill:Boolean
	"""是否电子发票"""
	electron:Boolean
	"""是否测试数据"""
	test:Boolean
	"""订单所属培训机构
		单位id
	"""
	unitId:String
	"""发票号"""
	billNo:String
	"""发票抬头"""
	billTitle:String
	"""发票抬头类别"""
	billTitleType:String
}
"""发票配置
	<AUTHOR> create 2020/5/19 10:29
"""
type BillConfigDTO @type(value:"com.fjhb.fjszyws.integrative.service.trade.dto.response.BillConfigDTO") {
	"""配置主键ID"""
	id:String
	"""子项目编号"""
	subProjectId:String
	"""是否自动开票|默认0"""
	autoBill:Boolean!
	"""间隔天数|自生成发票日期算起多少天后自动开票，默认7"""
	intervalDay:Int!
	"""是否来源默认配置"""
	isDefault:Boolean!
	"""创建时间"""
	createTime:DateTime
	"""更新时间"""
	updateTime:DateTime
}
"""发票信息对象
	<AUTHOR> create 2020/4/22 13:46
"""
type LazyBillResponse @type(value:"com.fjhb.fjszyws.integrative.service.trade.dto.response.LazyBillResponse") {
	"""主键 发票ID"""
	id:String
	"""发票号"""
	billNo:String
	"""发票对应的订单号"""
	orderNo:String
	"""发票代码"""
	billCode:String
	"""发票对应的集体缴费单号"""
	batchOrderNo:String
	"""集体缴费单位id"""
	batchUnitId:String
	"""集体缴费单位名"""
	batchUnitName:String
	"""蓝票开具状态：0未开票、1开票中、2开票成功、3开票失败"""
	blueState:Int
	"""红票开具状态：0未开票、1开票中、2开票成功、3开票失败"""
	redState:Int
	"""退款状态
		-1 没退款
	"""
	refundStatus:Int!
	"""付款金额"""
	payAmount:BigDecimal
	"""开票金额"""
	money:BigDecimal
	"""税额"""
	tax:BigDecimal
	"""是否补考"""
	makeUpExam:Boolean!
	"""购买人"""
	buyerName:String
	"""手机号"""
	buyerPhoneNumber:String
	"""证件号"""
	buyerIdentity:String
	"""纳税人识别号"""
	taxpayerNo:String
	"""发票抬头"""
	title:String
	"""发票抬头类型 1：个人 2：企业"""
	titleType:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账号"""
	account:String
	"""是否电子发票"""
	electron:Boolean!
	"""发票类型：1普通发票，2增值税普通发票，3增值税专用发票"""
	invoiceType:String
	"""是否冻结"""
	frozen:Boolean!
	"""索要发票时间"""
	billCreatedTime:DateTime
	"""开票完成时间
		成功/失败时间
	"""
	makeInvoiceTime:DateTime
	"""冲红发票金额"""
	redMoney:BigDecimal
	"""冲红发票税额"""
	redTaxRate:BigDecimal
	"""冲红发票代码"""
	redBillCode:String
	"""冲红发票号码"""
	redBillNo:String
}
"""延时
	发票统计对象
	<AUTHOR> create 2020/5/7 9:10
"""
type LazyBillStatisticsResponse @type(value:"com.fjhb.fjszyws.integrative.service.trade.dto.response.LazyBillStatisticsResponse") {
	"""开票总金额"""
	drawTotalAmount:Double!
	"""开票税额总金额"""
	drawTaxTotalAmount:Double!
	"""冲红发票总金额"""
	redBillTotalAmount:Double!
	"""冲红税额总金额"""
	redBillTaxTotalAmount:Double!
}

scalar List
type LazyBillResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [LazyBillResponse]}
