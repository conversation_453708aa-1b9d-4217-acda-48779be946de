import AbstractEnum from '../AbstractEnum'

enum InvoiceCategoryEunm {
  // 普通发票
  GENERAL_INVOICE = 1,
  // 增值税普通发票
  VAT_GENERAL_INVOICE = 2,
  // 增值税专用发票
  VAT_SPECIAL_INVOICE = 3
}

export { InvoiceCategoryEunm }

class InvoiceCategorys extends AbstractEnum<InvoiceCategoryEunm> {
  static enum = InvoiceCategoryEunm
  constructor() {
    super()
    this.map[InvoiceCategoryEunm.GENERAL_INVOICE] = '普通发票'
    this.map[InvoiceCategoryEunm.VAT_GENERAL_INVOICE] = '增值税普通发票'
    this.map[InvoiceCategoryEunm.VAT_SPECIAL_INVOICE] = '增值税专用发票'
  }
}
export default new InvoiceCategorys()
