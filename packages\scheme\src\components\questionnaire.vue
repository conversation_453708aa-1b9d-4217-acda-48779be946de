<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div class="f-plr20" style="margin-bottom: 20px">
      <el-button
        type="primary"
        icon="el-icon-plus"
        class="f-mb15 f-mt20"
        :disabled="recalculating || isIntelligenceLearning"
        @click="createQuestionnaire"
        v-if="!type"
        >添加调研问卷</el-button
      >
      <div v-else style="height: 20px"></div>
      <el-table stripe :data="tableList" ref="tableRef" max-height="500px" class="m-table">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="问卷名称" min-width="240" fixed="left">
          <template v-slot="{ row }">{{ row.questionnaireName }}</template>
        </el-table-column>
        <el-table-column label="问卷开放时间" min-width="240">
          <template v-slot="{ row }">
            <template v-if="isAssignedDate(row)">
              <p><el-tag type="info" size="mini">起始</el-tag>{{ row.openDateRange.startDate }}</p>
              <p><el-tag type="info" size="mini">结束</el-tag>{{ row.openDateRange.endDate }}</p>
            </template>
            <template v-else-if="isLongTerm">长期有效</template>
            <template v-else-if="hasTrainClassLearningTime">
              <p><el-tag type="info" size="mini">起始</el-tag>{{ trainClassBaseInfo.trainingBeginDate }}</p>
              <p><el-tag type="info" size="mini">结束</el-tag>{{ trainClassBaseInfo.trainingEndDate }}</p>
            </template>
            <template v-else>—</template>
          </template>
        </el-table-column>
        <el-table-column label="问卷结果" min-width="120" align="center">
          <template v-slot="{ row }">{{ row.isOpenStatistic ? '' : '不' }}开放</template>
        </el-table-column>
        <el-table-column label="问卷状态" min-width="120" align="center">
          <template v-slot="{ row }">{{ statusDesc(row) }} </template>
        </el-table-column>
        <el-table-column label="应用范围" min-width="120" align="center">
          <template v-slot="{ row }">{{ appliedRangeName(row) }}</template>
        </el-table-column>
        <el-table-column label="前置条件" min-width="120" align="center">
          <template v-slot="{ row }">{{ preconditionType(row.preconditionType) }}</template>
        </el-table-column>
        <el-table-column label="考核要求" min-width="130" align="center">
          <template v-slot="{ row }">{{ isAssessed(row) }}</template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template v-slot="{ row, $index }">
            <el-button type="text" @click="previewQuestionnaire(row)">预览</el-button>
            <template v-if="type">
              <el-button type="text" v-if="row.status === QuestionnaireStatusEnum.enabled" @click="getQrCode(row)"
                >二维码</el-button
              >
              <el-button type="text" @click="goReport(row)">查看统计报告</el-button>
            </template>
            <template v-else>
              <el-button type="text" @click="editQuestionnaire(row)">编辑</el-button>
              <el-button
                type="text"
                v-if="statusDesc(row) != '-'"
                :disabled="recalculating || isIntelligenceLearning"
                @click="changeStatus(row)"
                >{{ row.status === QuestionnaireStatusEnum.enabled ? '停用' : '启用' }}</el-button
              >
              <el-button
                type="text"
                :disabled="recalculating || isIntelligenceLearning"
                @click="deleteQuestionnaire(row, $index)"
                >删除</el-button
              >
            </template>
          </template>
        </el-table-column>
      </el-table>
      <hb-pagination :page="page" v-bind="page"> </hb-pagination>
    </div>
    <operate-questionnaire-template
      :questionnaire-template-drawer.sync="drawerConfig.templateDrawer"
      :questionnaire="drawerConfig.questionnaire"
      :operateType="drawerConfig.operateType"
      @select-template="selectTemplate"
    ></operate-questionnaire-template>
    <operate-questionnaire-config
      :questionnaire-config-drawer.sync="drawerConfig.configDrawer"
      :questionnaire="drawerConfig.questionnaire"
      :questionnaire-config-list="questionnaire.questionnaireConfigList"
      :issue="issue"
      :title="drawerConfig.configDrawerTitle"
      :routerMode="routerMode"
      :trainClassBaseInfo="trainClassBaseInfo"
      @change-template="changeTemplate"
      @confirm="operateQuestionnaire"
      :recalculating="recalculating"
      :isIntelligenceLearning="isIntelligenceLearning"
    ></operate-questionnaire-config>
    <questionnaire-qrcode
      :questionnaire="drawerConfig.questionnaire"
      :showDrawer.sync="drawerConfig.qrCodeDrawer"
      :scheme-id="trainClassBaseInfo.id"
    ></questionnaire-qrcode>
  </el-card>
</template>
<script lang="ts">
  import { Vue, Component, Prop, Ref } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import OperateQuestionnaireConfig from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/operate-questionnaire-config.vue'
  import OperateQuestionnaireTemplate from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/operate-questionnaire-template.vue'
  import { OperateTypeEnum } from '@hbfe/jxjy-admin-scheme/src/models/OperateTypeEnum'
  import QuestionnaireLearningType from '@api/service/common/scheme/model/QuestionnaireLearningType'
  import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
  import QuestionnaireAppliedRangeType, {
    QuestionnaireAppliedRangeTypeEnum
  } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'
  import QuestionnairePreconditionType, {
    QuestionnairePreconditionTypeEnum
  } from '@api/service/common/scheme/enum/QuestionnairePreconditionType'
  import QuestionnaireTriggerType from '@api/service/common/scheme/enum/QuestionnaireTriggerType'
  import { cloneDeep } from 'lodash'
  import QuestionnaireItem from '@api/service/management/resource/question-naire/models/QuestionnaireItem'
  import IssueLearningType from '@api/service/common/scheme/model/IssueLearningType'
  import { QuestionnaireOpenDateTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireOpenDateType'
  import SchemeBaseInfo from '@api/service/common/scheme/model/SchemeBaseInfo'
  import { ElTable } from 'element-ui/types/table'
  import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
  import { QuestionnaireStatusEnum } from '@api/service/common/scheme/enum/QuestionnaireStatus'
  import MutationCreateTrainClassCommodity from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'
  import QuestionnaireList from '@api/service/management/resource/question-naire/QuestionnaireList'
  import QuestionnaireQrcode from '@hbfe/jxjy-admin-scheme/src/components/detail/questionnaire-qrcode.vue'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import CourseLearningLearningType from '@api/service/management/train-class/mutation/vo/CourseLearningLearningType'
  import TrainingPeriod from '@packages/scheme/src/components/training-period.vue'

  @Component({
    components: { TrainingPeriod, QuestionnaireQrcode, OperateQuestionnaireConfig, OperateQuestionnaireTemplate }
  })
  export default class extends Vue {
    @Ref('tableRef') tableRef: ElTable
    @Prop({ type: Object, default: () => new QuestionnaireLearningType() }) questionnaire: QuestionnaireLearningType
    @Prop({ type: Object, default: () => new IssueLearningType() }) issue: IssueLearningType
    @Prop({ type: Object, default: () => new SchemeBaseInfo() }) trainClassBaseInfo: SchemeBaseInfo
    /**
     * 是否重算中
     */
    @Prop({ type: Boolean, default: false })
    recalculating: boolean
    /**
     * 是否智能学习中
     */
    @Prop({ type: Boolean, default: false })
    isIntelligenceLearning: boolean
    /**
     * 类型
     * 详情
     */
    @Prop({ type: String, default: '' }) type: string
    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number
    /**
     * 问卷抽屉配置
     */
    drawerConfig = {
      templateDrawer: false,
      configDrawer: false,
      configDrawerTitle: '添加调研问卷',
      qrCodeDrawer: false,
      operateType: OperateTypeEnum.CREATE,
      questionnaire: new QuestionnaireConfigDetail()
    }
    page: UiPage
    tableList: QuestionnaireConfigDetail[] = []
    QuestionnaireStatusEnum = QuestionnaireStatusEnum
    OperationTypeEnum = OperationTypeEnum
    constructor() {
      super()
      this.page = new UiPage(this.getPageList, this.getPageList)
    }
    /**
     * 获取应用范围名称
     */
    get appliedRangeName() {
      return (item: QuestionnaireConfigDetail) => {
        if (item.appliedRangeType === QuestionnaireAppliedRangeTypeEnum.assign_issue) {
          // return item.curIssueName
          return (
            this.issue.issueConfigList.find((ite) => {
              return ite.id == item.curIssueId
            })?.issueName || ''
          )
        } else return QuestionnaireAppliedRangeType.map.get(item.appliedRangeType)
      }
    }

    /**
     * 获取前置条件类型
     */
    get preconditionType() {
      return (item: QuestionnairePreconditionTypeEnum) => {
        return QuestionnairePreconditionType.map.get(item)
      }
    }

    /**
     * 考核要求
     */
    get isAssessed() {
      return (item: QuestionnaireConfigDetail) => {
        if (item.isAssessed) {
          return '纳入考核'
        } else if (item.isForced && item.triggerType) {
          return `${QuestionnaireTriggerType.map.get(item.triggerType)}强制问卷`
        } else {
          return '—'
        }
      }
    }
    created() {
      this.getPageList()
    }
    /**
     * 新建问卷
     */
    async createQuestionnaire() {
      if (this.canOperate()) return
      this.drawerConfig.configDrawerTitle = '添加调研问卷'
      this.drawerConfig.operateType = OperateTypeEnum.CREATE
      this.drawerConfig.questionnaire = new QuestionnaireConfigDetail()
      this.drawerConfig.templateDrawer = true
    }

    /**
     * 选择模板
     * @param item
     */
    selectTemplate(item: QuestionnaireItem) {
      this.drawerConfig.questionnaire.templateId = item.id
      this.drawerConfig.questionnaire.templateName = item.name
      this.drawerConfig.configDrawer = true
    }
    changeTemplate() {
      this.drawerConfig.operateType = OperateTypeEnum.UPDATE
      this.drawerConfig.templateDrawer = true
    }
    /**
     * 编辑问卷
     */
    async editQuestionnaire(item: QuestionnaireConfigDetail) {
      this.drawerConfig.configDrawerTitle = '编辑调研问卷'
      this.drawerConfig.operateType = OperateTypeEnum.UPDATE
      this.drawerConfig.questionnaire = cloneDeep(item)
      this.drawerConfig.configDrawer = true
    }

    /**
     * 操作问卷
     */
    operateQuestionnaire() {
      let index = this.questionnaire.questionnaireConfigList.findIndex(
        (item) => item.id === this.drawerConfig.questionnaire.id
      )
      if (index >= 0) {
        Object.assign(this.questionnaire.questionnaireConfigList[index], this.drawerConfig.questionnaire)
      } else {
        this.questionnaire.questionnaireConfigList.push(this.drawerConfig.questionnaire)
        index = this.questionnaire.questionnaireConfigList.length - 1
      }
      this.page.currentChange(Math.floor(index / this.page.pageSize) + 1)
    }

    /**
     * 删除问卷
     */
    async deleteQuestionnaire(row: QuestionnaireConfigDetail, pageIndex: number) {
      if (row.operationType === OperationTypeEnum.update) {
        const res = await new MutationCreateTrainClassCommodity().questionnaireDeleteVerify(row.configId)
        if (!res?.isSuccess()) {
          return this.$message.error(res?.message?.toString() || '是否存在提交记录校验失败')
        }
      }
      // 判断是否存在统计数据
      this.$confirm(`是否确认删除该问卷？`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const index = (this.page.pageNo - 1) * this.page.pageSize + pageIndex
          this.questionnaire.questionnaireConfigList.splice(index, 1)
          this.$message.success('删除成功')
          if (this.tableList.length == 1) {
            this.page.currentChange(this.page.pageNo - 1 || 1)
          } else {
            this.getPageList()
          }
        })
        .catch(() => {
          //   todo
        })
    }

    /**
     * 预览问卷
     */
    async previewQuestionnaire(item: QuestionnaireConfigDetail) {
      // 新窗口打开
      const res = await new QuestionnaireList().copy(item.templateId)
      if (res.questionList.length === 0) {
        this.$message.error('请至少配置一题试题才可预览。')
      } else {
        window.open(`/admin#/resource/questionnaire/preview?id=${item.templateId}`, '_blank')
      }
    }

    /**
     * 二维码
     */
    getQrCode(item: QuestionnaireConfigDetail) {
      this.drawerConfig.questionnaire = item
      this.drawerConfig.qrCodeDrawer = true
    }
    /**
     * 获取分页列表
     */
    async getPageList() {
      this.tableList = this.questionnaire.questionnaireConfigList.slice(
        this.page.pageSize * (this.page.pageNo - 1),
        this.page.pageSize * this.page.pageNo
      )
      this.page.totalSize = this.questionnaire.questionnaireConfigList.length
      this.page.totalPageSize = Math.ceil(this.page.totalSize / this.page.pageSize)
      this.tableRef?.doLayout()
    }
    /**
     * 是否可操作
     * @returns {boolean}
     */
    canOperate() {
      if (!this.questionnaire.isSelected) {
        this.$message.error('请先勾选“调研问卷”的学习内容！')
        return true
      }
      return false
    }
    /**
     * 开发时间是否指定时间
     */
    get isAssignedDate() {
      return (row: QuestionnaireConfigDetail) => {
        return row.openDateType === QuestionnaireOpenDateTypeEnum.assign
      }
    }

    /**
     * 当前培训班是否长期有效
     */
    get isLongTerm() {
      return (
        this.trainClassBaseInfo.trainingBeginDate === CreateSchemeUtils.defaultBeginDate &&
        this.trainClassBaseInfo.trainingEndDate === CreateSchemeUtils.defaultEndDate
      )
    }
    /**
     * 当前培训班学习时间
     */
    get hasTrainClassLearningTime() {
      return this.trainClassBaseInfo.trainingBeginDate && this.trainClassBaseInfo.trainingEndDate
    }
    get statusDesc() {
      return (row: QuestionnaireConfigDetail) => {
        if (row.operationType !== OperationTypeEnum.update) return '-'
        else if (row.status == QuestionnaireStatusEnum.enabled) return '启用'
        else return '停用'
      }
    }

    /**
     * 启停用
     */
    async changeStatus(row: QuestionnaireConfigDetail) {
      switch (row.status) {
        case QuestionnaireStatusEnum.enabled:
          this.$confirm(
            '停用问卷为纳入考核的问卷，停用后将影响班级考核。如确认停用，问卷将自动调整为不纳入考核，是否确认停用？'
          )
            .then(() => {
              row.status = QuestionnaireStatusEnum.disabled
            })
            .catch(() => {
              //   todo
            })
          break
        case QuestionnaireStatusEnum.disabled:
          row.status = QuestionnaireStatusEnum.enabled
          break
      }
    }
    resetQuestionnaire() {
      this.drawerConfig = {
        templateDrawer: false,
        configDrawer: false,
        configDrawerTitle: '新建调研问卷',
        qrCodeDrawer: false,
        operateType: OperateTypeEnum.CREATE,
        questionnaire: new QuestionnaireConfigDetail()
      }
      this.page.pageNo = 1
      this.tableList = []
    }

    /**
     * 移除线上课程
     * 若为已发布的则停用
     */
    removeOnlineCourse() {
      this.questionnaire.questionnaireConfigList = this.questionnaire.questionnaireConfigList.filter((item) => {
        if (item.operationType === OperationTypeEnum.update) {
          item.status = QuestionnaireStatusEnum.disabled
        }
        return (
          item.operationType !== OperationTypeEnum.update &&
          item.appliedRangeType !== QuestionnaireAppliedRangeTypeEnum.online_course
        )
      })
      this.page.currentChange(1)
    }
    goReport(item: QuestionnaireConfigDetail) {
      this.$router.push(`/training/scheme/holisticReport/${item.configId}?schemeId=${this.trainClassBaseInfo.id}`)
    }
  }
</script>

<style scoped lang="scss"></style>
