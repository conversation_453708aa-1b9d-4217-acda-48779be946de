import AbstractEnum from '../AbstractEnum'

/**
 * 说明：
 * 1 表示单选题
 * 2 表示多选题
 * 3 表示填空题
 * 4 表示判断题
 * 5 表示简答题
 * 6 表示父子题
 */
enum QuestionTypeEnum {
  radio = 1,
  multiple = 2,
  fill = 3,
  opinion = 4,
  ask = 5,
  father = 6
}

export { QuestionTypeEnum }

class QuestionType extends AbstractEnum<QuestionTypeEnum> {
  static enum = QuestionTypeEnum

  constructor() {
    super()
    this.map.set(QuestionTypeEnum.radio, '单选题')
    this.map.set(QuestionTypeEnum.multiple, '多选题')
    this.map.set(QuestionTypeEnum.fill, '填空题')
    this.map.set(QuestionTypeEnum.opinion, '判断题')
    this.map.set(QuestionTypeEnum.father, '父子题')
    this.map.set(QuestionTypeEnum.ask, '问答题')
  }
}

export default QuestionType
