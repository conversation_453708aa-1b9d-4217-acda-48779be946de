import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 网授选课方式枚举
 * choose_course_learning 选课规则
 * autonomous_course_learning 自主选课
 */
export enum SelectCourseTypeEnum {
  choose_course_learning = 'chooseCourseLearning',
  autonomous_course_learning = 'autonomousCourseLearning'
}

/**
 * @description 网授选课方式
 */
class SelectCourseType extends AbstractEnum<SelectCourseTypeEnum> {
  static enum = SelectCourseTypeEnum

  constructor(status?: SelectCourseTypeEnum) {
    super()
    this.current = status
    this.map.set(SelectCourseTypeEnum.choose_course_learning, '选课规则')
    this.map.set(SelectCourseTypeEnum.autonomous_course_learning, '自主选课')
  }
  /**
   * 是否是选课规则
   * @param type 方案类型
   */
  isChooseCourseLearning(type: SelectCourseTypeEnum) {
    return type === SelectCourseTypeEnum.choose_course_learning
  }

  /**
   * 是否是自主选课
   * @param type 方案类型
   */
  isAutonomousCourseLearning(type: SelectCourseTypeEnum) {
    return type === SelectCourseTypeEnum.autonomous_course_learning
  }
}

export default new SelectCourseType()
