import UserModule from '@api/service/management/user/UserModule'
import StudentUserInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'
import { Page } from '@hbfe/common'
import QueryOffLinePageInvoiceParam from '@api/service/management/trade/single/invoice/query/vo/QueryOffLinePageInvoiceParam'
import { OfflineInvoiceSortRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'

export default abstract class QueryOffLineInvoiceBase {
  /**
   * 分页电子查询发票
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  abstract offLinePageInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest>
  ): Promise<Array<OffLinePageInvoiceVo>>

  /**
   * 电子发票导出
   * @param QueryOffLinePageInvoiceParam 查询参数
   */
  abstract offLinePageInvoiceInExport(queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam): Promise<boolean>

  /**
   * 分页专票查询发票（纸质票）
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  abstract offLinePageVatspecialplaInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest>
  ): Promise<Array<OffLinePageInvoiceVo>>

  /**
   * 分页专票查询发票（电子）
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  abstract offLinePageElectVatspecialplaInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest>
  ): Promise<Array<OffLinePageInvoiceVo>>

  /**
   * 个人线下发票导出 - 专票（纸质票）
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  abstract offLinePageVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean>

  /**
   * 个人线下发票导出 - 专票（电子票）
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  abstract offLinePageElectVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean>

  /**
   * 获取用户信息
   * @param ids id数组
   */
  protected async getUserInfo(ids: Array<string>) {
    if (ids.length === 0) {
      return new Map()
    }
    // 根据用户ID获取用户信息
    const response = await UserModule.queryUserFactory.queryStudentList.queryStudentList(ids)
    const userIdMap: Map<string, StudentUserInfoVo> = new Map()
    response.data.forEach(item => {
      userIdMap.set(item.userId, item)
    })
    return userIdMap
  }
}
