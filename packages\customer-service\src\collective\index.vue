<route-meta>
  {
  "isMenu": true,
  "title": "集体报名咨询",
  "sort": 2,
  "icon": "icon_menhuxinxiguanli"
  }
</route-meta>
<template>
  <div v-if="$hasPermission('query')" desc="查询" actions="created,doQuery,clickSearch">
    <el-card shadow="never" class="m-card is-bg is-overflow-hidden">
      <div class="f-plr20 f-pt20">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="姓名">
                <el-input v-model="searchCondition.name" clearable placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="管理员帐号">
                <el-input v-model="searchCondition.adminAccount" clearable placeholder="请输入管理员帐号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="4" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary" @click="clickSearch">查询</el-button>
                <el-button @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--人员信息-->
        <el-collapse v-model="activeNames" class="m-collapse no-border">
          <el-collapse-item name="1">
            <div slot="title" class="m-tit">
              <span class="tit-txt">人员信息</span>
            </div>
            <!--表格-->
            <el-table
              stripe
              :data="adminTableData"
              max-height="240"
              highlight-current-row
              class="m-table"
              @current-change="currentChangeRow"
              ref="adminTableRef"
              v-loading="uiStatus.query.loadAdminPage"
            >
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="姓名" min-width="160" fixed="left">
                <template slot-scope="scope">{{ scope.row.userInfo.userName }}</template>
              </el-table-column>
              <el-table-column label="帐号" min-width="200">
                <template slot-scope="scope">{{ getAdminAccount(scope.row.authenticationList) }}</template>
              </el-table-column>
              <el-table-column label="注册时间" min-width="180">
                <template slot-scope="scope">{{ scope.row.accountInfo.createdTime }}</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <hb-pagination v-bind="page" :page="page" class="f-mt15 f-tr"></hb-pagination>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-border-top is-sticky">
      <template v-if="$hasPermission('basicData')" desc="账号信息" actions="@BasicData">
        <el-tab-pane label="帐号信息" name="basic-data" :lazy="true">
          <basic-data :user-id="selectedId"></basic-data>
        </el-tab-pane>
      </template>

      <template v-if="$hasPermission('batchInfo')" desc="发票信息" actions="@BatchInfo">
        <el-tab-pane label="批次信息" name="batch-info" :lazy="true">
          <batch-info :user-id="selectedId"></batch-info>
        </el-tab-pane>
      </template>
      <template v-if="$hasPermission('invoiceInfo')" desc="发票信息" actions="@InvoiceInfo">
        <el-tab-pane label="发票信息" name="invoice-info" :lazy="true">
          <invoice-info :user-id="selectedId"></invoice-info>
        </el-tab-pane>
      </template>
      <template v-if="$hasPermission('refundInfo')" desc="退款信息" actions="@RefundInfo">
        <el-tab-pane label="退款信息" name="refund-info" :lazy="true">
          <refund-info :user-id="selectedId"></refund-info>
        </el-tab-pane>
      </template>

      <template v-if="$hasPermission('studentList')" desc="学员名单" actions="@StudentList">
        <el-tab-pane label="学员名单" name="student-list" :lazy="true">
          <student-list :user-id="selectedId"></student-list>
        </el-tab-pane>
      </template>
    </el-tabs>
  </div>
</template>
<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import BasicData from '@hbfe/jxjy-admin-customerService/src/collective/components/basic-data.vue'
  import BatchInfo from '@hbfe/jxjy-admin-customerService/src/collective/components/batch-info.vue'
  import InvoiceInfo from '@hbfe/jxjy-admin-customerService/src/collective/components/invoice-info.vue'
  import RefundInfo from '@hbfe/jxjy-admin-customerService/src/collective/components/refund-info.vue'
  import StudentList from '@hbfe/jxjy-admin-customerService/src/collective/components/student-list.vue'
  import UserModule from '@api/service/management/user/UserModule'
  import { UiPage } from '@hbfe/common'
  import {
    AuthenticationResponse,
    CollectiveInfoResponse
  } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import { ElTable } from 'element-ui/types/table'

  class SearchCondition {
    name: string
    adminAccount: string
    constructor() {
      this.name = ''
      this.adminAccount = ''
    }
  }

  @Component({
    components: { BasicData, BatchInfo, InvoiceInfo, RefundInfo, StudentList }
  })
  export default class extends Vue {
    @Ref('adminTableRef')
    adminTableRef: ElTable

    activeName = 'basic-data'
    // 管理人员列表实例
    collectiveManagerObj = UserModule.queryUserFactory.queryCollectiveManagerList
    page: UiPage

    searchCondition: SearchCondition
    adminTableData = Array<CollectiveInfoResponse>()
    activeNames = ['1']
    uiStatus = {
      query: {
        loadAdminPage: false
      }
    }
    // 当前行的人员id以及详情
    selectedId = ''

    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
      this.searchCondition = new SearchCondition()
    }

    // 表单数据首次加载结束，默认设置第一行数据
    @Watch('adminTableData', {
      deep: true
    })
    loadingAdminPageChange(val: Array<CollectiveInfoResponse>) {
      if (val?.length) {
        this.selectedId = val[0]?.userInfo?.userId || ''
        this.$nextTick(() => {
          this.adminTableRef.setCurrentRow(val[0])
        })
      }
    }

    // 选中当前行
    currentChangeRow(val: CollectiveInfoResponse) {
      this.selectedId = val?.userInfo?.userId
    }

    async created() {
      await this.doQuery()
    }

    // 点击查询人员信息列表
    async clickSearch() {
      this.page.pageNo = 1
      await this.doQuery()
    }

    // 查询人员列表信息
    async doQuery() {
      try {
        this.uiStatus.query.loadAdminPage = true
        const params = new SearchCondition()
        params.name = this.searchCondition.name
        params.adminAccount = this.searchCondition.adminAccount
        this.adminTableData = await this.collectiveManagerObj.queryPageCollectiveList(this.page, params)
      } catch (e) {
        console.log(e)
        this.$message.error('查询人员信息列表请求失败！')
      } finally {
        this.uiStatus.query.loadAdminPage = false
      }
    }

    async resetQuery() {
      this.searchCondition.name = undefined
      this.searchCondition.adminAccount = undefined
      await this.doQuery()
    }

    getAdminAccount(arr: Array<AuthenticationResponse>) {
      if (arr?.length) {
        const res = arr[0]?.identity
        return res
      }
      return '-'
    }
  }
</script>
