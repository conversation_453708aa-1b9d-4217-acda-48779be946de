"""独立部署的微服务,K8S服务名:ms-autolearning-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
type Query {
	getSchemaName:String
}
type Mutation {
	"""批量立刻执行学员的自动学习任务
		@param qualificationIdList 学员资格ID列表，用于标识需要执行自动学习任务的学员
		<AUTHOR> By Cb
		@since 2024/8/17 17:15
	"""
	batchImmediatelyAutoLearning(qualificationIdList:[String]):Void
	"""立刻执行学员的自动学习任务
		@param qualificationId:
		<AUTHOR> By Cb
		@since 2024/6/7 11:57
	"""
	immediatelyAutoLearning(qualificationId:String):Void
}

scalar List
