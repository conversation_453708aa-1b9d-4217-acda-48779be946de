import {
  BatchReturnOrderResponse,
  OrderResponse,
  SubOrderResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
import { ChangeOrderType } from '@api/service/common/trade/ChangeOrderType'
import DataResolve from '@api/service/common/utils/DataResolve'
import { BatchOrderMainOrderAfterSaleStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderMainOrderAfterSaleStatus'
import { BatchOrderMainOrderStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderMainOrderStatus'
import BatchOrderUtils from '@api/service/management/trade/batch/order/query/utils/BatchOrderUtils'
import BatchOrderDetailBuyerInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailBuyerInfoVo'

/**
 * @description 【集体报名订单】批次单主单列表详情
 */
class BatchOrderMainOrderListDetailVo {
  /**
   * 主订单号
   */
  mainOrderNo = ''

  /**
   * 子单id
   */
  subOrderNo = ''

  /**
   * 是否换班
   */
  isExchange = false

  /**
   * 是否换期
   */
  isExchangePeriod = false

  /**
   * 购买学员信息
   */
  buyerInfo: BatchOrderDetailBuyerInfoVo = new BatchOrderDetailBuyerInfoVo()

  /**
   * 学时
   */
  period: number = null

  /**
   * 实付金额
   */
  payAmount: number = null

  /**
   * 状态
   */
  mainOrderStatus: BatchOrderMainOrderStatusEnum = null

  /**
   * 【主单】售后
   */
  afterSaleStatus: BatchOrderMainOrderAfterSaleStatusEnum = null

  /**
   * 销售渠道
   */
  saleChannel: number

  /**
   * 商品id
   */
  commodityId: string[] = []
  /**
   * 换货状态
   */
  changeOrderStatus: ChangeOrderType[] = []
  /**
   * 培训期别名称
   */
  trainingPeriodName = ''
  /**
   * 培训形式
   */
  trainingForm = ''
  /**
   * 培训形式ID
   */
  trainingFormId: TrainingModeEnum = undefined

  /**
   * 单位id
   */
  unitId = ''

  static async from(
    response: OrderResponse,
    batchRefundMap: Map<string, BatchReturnOrderResponse>
  ): Promise<BatchOrderMainOrderListDetailVo> {
    const detail = new BatchOrderMainOrderListDetailVo()
    detail.mainOrderNo = response.orderNo ?? ''
    if (DataResolve.isWeightyArr(response.subOrderItems)) {
      const subOrderItems = response.subOrderItems
      detail.subOrderNo = subOrderItems[0].subOrderNo ?? ''
      detail.isExchange = BatchOrderUtils.validateSubOrderIsExchange(subOrderItems)
      detail.isExchangePeriod = subOrderItems[0].isExchangeIssue
      detail.period = BatchOrderUtils.getMainOrderPeriod(subOrderItems)
      const batchRefund = batchRefundMap.get(detail.mainOrderNo)
      detail.afterSaleStatus = BatchOrderMainOrderListDetailVo.getMainOrderAfterSaleStatus(
        subOrderItems[0],
        batchRefund
      )
    }
    detail.buyerInfo.buyerId = response.buyer?.userId ?? ''
    detail.payAmount = response.basicData?.amount ?? 0
    detail.mainOrderStatus = BatchOrderUtils.getMainOrderStatus(response)
    detail.saleChannel = response.saleChannel
    detail.commodityId =
      response.subOrderItems?.map((item) => {
        return item.deliveryCommoditySku?.commoditySkuId
      }) || ([] as string[])

    detail.unitId = response?.basicData?.unitId
    detail.trainingPeriodName = response?.subOrderItems[0]?.deliveryCommoditySku?.issueInfo?.issueName
    detail.trainingForm =
      response?.subOrderItems[0]?.deliveryCommoditySku?.skuProperty?.trainingWay?.skuPropertyValueName
    const trainingWayId = response?.subOrderItems[0]?.deliveryCommoditySku?.skuProperty?.trainingWay?.skuPropertyValueId
    detail.trainingFormId = trainingWayId as TrainingModeEnum
    // 处理换班、换期标签
    const { subOrderItems } = response
    if (subOrderItems && subOrderItems.length) {
      const subOrderItem = subOrderItems[0]
      const isExistExchangeScheme = subOrderItem.exchangeStatus !== 0
      const isExistExchangeIssue = subOrderItem.isExchangeIssue
      if (isExistExchangeScheme) {
        detail.changeOrderStatus.push(ChangeOrderType.CLASS_TYPE)
      }
      if (isExistExchangeIssue) {
        detail.changeOrderStatus.push(ChangeOrderType.PERIOD_TYPE)
      }
    }
    return detail
  }

  /**
   * 获取退款状态
   */
  static getMainOrderAfterSaleStatus(
    subOrder: SubOrderResponse,
    refundDetail?: BatchReturnOrderResponse
  ): BatchOrderMainOrderAfterSaleStatusEnum {
    const returnStatus = subOrder.returnStatus ?? undefined
    if (returnStatus === 0) {
      return BatchOrderMainOrderAfterSaleStatusEnum.Wait_For_Refund
    }
    if ([1, 2, 4].includes(returnStatus)) {
      return BatchOrderMainOrderAfterSaleStatusEnum.Refunding
    }
    if ([3, 5].includes(returnStatus)) {
      if (refundDetail) {
        if ([9, 10, 11, 12].includes(refundDetail.basicData.batchReturnOrderStatus)) {
          return BatchOrderMainOrderAfterSaleStatusEnum.Success_Refund
        } else {
          return BatchOrderMainOrderAfterSaleStatusEnum.Refunding
        }
      } else {
        return BatchOrderMainOrderAfterSaleStatusEnum.Success_Refund
      }
    }
    return null
  }
}

export default BatchOrderMainOrderListDetailVo
