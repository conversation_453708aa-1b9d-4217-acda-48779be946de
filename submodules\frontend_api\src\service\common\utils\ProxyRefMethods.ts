/**
 * 装饰器：将 ref 组件的方法透传到当前组件实例上
 * @param refName 要透传的方法来源组件的 ref 名
 * @param proxyValue 是否需要透传属性值
 * 来源：http://192.168.1.225:8090/pages/viewpage.action?pageId=442040331
 */
export function ProxyRef(refName: string, proxyValue: boolean): ClassDecorator {
  return function (target: any) {
    const originalMounted = target.prototype.mounted
    target.prototype.mounted = function () {
      const refComponent = this.$refs[refName]
      if (refComponent) {
        for (const key in refComponent) {
          // 过滤掉包含 '$' 或 '_' 的属性名
          if (!key.includes('$') && !key.includes('_')) {
            const element = refComponent[key]
            // 检查属性不是构造函数
            if (key !== 'constructor') {
              if (typeof element === 'function') {
                // 如果当前对象中不存在同名属性，则绑定函数到当前上下文
                if (!(key in this)) {
                  this[key] = element.bind(refComponent)
                }
              }
              if (proxyValue && typeof element !== 'function') {
                // 使用 defineProperty 在当前组件上定义同名属性，实现 getter/setter 与子组件同步
                Object.defineProperty(this, key, {
                  enumerable: true,
                  configurable: true,
                  get() {
                    return refComponent[key]
                  },
                  set(val) {
                    refComponent[key] = val
                  }
                })
              }
            }
          }
        }
      } else {
        console.warn(`需要进行替换的ref对象： "${refName}" 找不到.`)
      }
      // 调用原始 mounted（若有）
      if (typeof originalMounted === 'function') {
        originalMounted.call(this)
      }
    }
  }
}
