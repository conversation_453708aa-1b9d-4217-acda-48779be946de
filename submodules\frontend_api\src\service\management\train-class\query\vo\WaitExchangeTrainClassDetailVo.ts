import {
  BuyerValidCommodityResponse,
  SchemeResourceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { TrainClassResourceEnum } from '@api/service/management/train-class/query/enum/TrainClassResourceType'
import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
import { TrainClassStatusEnum } from '@api/service/management/train-class/query/enum/TrainClassStatusType'
import { TrainClassAssessmentStatusEnum } from '@api/service/management/train-class/query/enum/TrainClassAssessmentStatusType'
import { Page } from '@hbfe/common'
import MsSchemeLearningQuery, {
  LearningRegisterRequest,
  SchemeRequest,
  StudentSchemeLearningRequest,
  UserRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * @description 待更换班级列表下详情
 */
class WaitExchangeTrainClassDetailVo {
  /**
   * 学习方案ID
   */
  schemeId = ''

  /**
   * 培训班商品id
   */
  commoditySkuId = ''
  /**
   * 培训方案类型
   * 1：选课规则
   * 2：自主选课
   */
  schemeType: TrainClassSchemeEnum = null

  /**
   * 培训班名称
   */
  schemeName = ''

  /**
   * 行业
   */
  industry = ''

  /**
   * 行业id
   */
  industryId = ''

  /**
   * 行业属性id
   */
  industryPropertyId = ''

  /**
   * 年度
   */
  year = ''

  /**
   * 科目类型
   */
  subjectType = ''

  /**
   * 培训类别
   */
  trainingCategory = ''
  /**
   * 培训专业
   */
  trainingProfessional = ''

  /**
   * 岗位类别（卫生）
   */
  positionCategory = ''

  /**
   * 培训对象
   */
  trainingObject = ''

  /**
   * 技术等级（工勤）
   */
  jobLevel = ''

  /**
   * 工种（工勤）
   */
  jobCategory = ''
  /**
   * 学段（教师）
   */
  grade = ''
  /**
   * 学科（教师）
   */
  subject = ''
  /**
   * 证书类型（药师）
   */
  certificatesType = ''
  /**
   * 执业类别（药师）
   */
  practitionerCategory = ''
  /**
   * 单价
   */
  price = 0

  /**
   * 考核情况
   * 1：不合格
   * 2：待考核
   * 3：已合格
   */
  assessmentStatus: TrainClassAssessmentStatusEnum = null

  /**
   * 状态
   * 1；有效
   * 2：冻结
   * 3：失效
   */
  status: TrainClassStatusEnum = null

  /**
   * 来源类型
   * 1；个人报名
   * 2：导入开通
   * 3：换班
   * 4：集体报名
   */
  resourceType: TrainClassResourceEnum = null

  /**
   * 订单号，用于换货
   */
  orderNo = ''

  /**
   * 子订单号，用于换货
   */
  subOrderNo = ''

  // * 技术等级名字
  professionalName = ''
  /**
   * 销售渠道
   */
  saleChannel: SaleChannelEnum = null
  /**
   * 专题id
   */
  saleChannelId = ''
  /**
   * 专题名称
   */
  saleChannelName = ''
  /**
   * 分销商id
   */
  distributorId = ''

  /**
   * 班级类型
   */
  type: TrainingModeEnum = undefined

  /**
   * 期别id
   */
  periodId: string = undefined

  /**
   * 期别名称
   */
  periodName: string = undefined

  /**
   * 期别编号
   */
  periodNo: string = undefined

  /**
   * 来源类型 面网授特有
   */
  sourceType: string = undefined

  /**
   * 来源id 面网授特有
   */
  sourceId: string = undefined

  /**
   * 转换远端模型
   * @param {string} userId - 用户id，用于查询考核情况以及状态
   */
  static async from(userId: string, response: BuyerValidCommodityResponse): Promise<WaitExchangeTrainClassDetailVo> {
    const detail = new WaitExchangeTrainClassDetailVo()
    detail.schemeId = (response.commoditySku?.resource as SchemeResourceResponse)?.schemeId || ''
    detail.commoditySkuId = response.commoditySku?.commoditySkuId || ''
    detail.schemeType = WaitExchangeTrainClassDetailVo.getSchemeType(response)
    detail.schemeName = (response.commoditySku?.resource as SchemeResourceResponse)?.schemeName || ''
    detail.industryPropertyId = response.commoditySku?.skuProperty?.industry?.skuPropertyValueId
    detail.trainingObject =
      response.commoditySku?.skuProperty?.trainingObject?.skuPropertyValueShowName ||
      response.commoditySku?.skuProperty?.trainingObject?.skuPropertyValueName
    detail.positionCategory =
      response.commoditySku?.skuProperty?.positionCategory?.skuPropertyValueShowName ||
      response.commoditySku?.skuProperty?.positionCategory?.skuPropertyValueName
    detail.jobLevel =
      response.commoditySku?.skuProperty?.jobLevel?.skuPropertyValueShowName ||
      response.commoditySku?.skuProperty?.jobLevel?.skuPropertyValueName
    detail.jobCategory =
      response.commoditySku?.skuProperty?.jobCategory?.skuPropertyValueShowName ||
      response.commoditySku?.skuProperty?.jobCategory?.skuPropertyValueName
    detail.industry = response.commoditySku?.skuProperty?.industry?.skuPropertyValueName || ''
    detail.industryId = response.commoditySku?.skuProperty?.industry?.skuPropertyValueId || ''

    detail.year = response.commoditySku?.skuProperty?.year?.skuPropertyValueName || ''
    detail.subjectType = response.commoditySku?.skuProperty?.subjectType?.skuPropertyValueName || ''
    detail.trainingCategory = response.commoditySku?.skuProperty?.trainingCategory?.skuPropertyValueName || ''
    detail.trainingProfessional = response.commoditySku?.skuProperty?.trainingProfessional?.skuPropertyValueName || ''

    detail.subject = response.commoditySku?.skuProperty?.discipline?.skuPropertyValueName || ''
    detail.grade = response.commoditySku?.skuProperty?.learningPhase?.skuPropertyValueName || ''
    detail.certificatesType = response.commoditySku?.skuProperty?.certificatesType?.skuPropertyValueName || ''
    detail.practitionerCategory = response.commoditySku?.skuProperty?.practitionerCategory?.skuPropertyValueName || ''
    detail.price = response.order.basicData.amount || 0
    detail.resourceType = WaitExchangeTrainClassDetailVo.getResourceType(response)
    detail.saleChannelName = response.saleChannelName || ''
    detail.saleChannelId = response.saleChannelId || ''
    detail.saleChannel = response.saleChannel || null
    detail.distributorId = response.distributorId || ''
    // detail.professionalName = await QueryTechnologyLevel.queryTechnologyLevelById(
    //   response.commoditySku.skuProperty.technicalGrade.skuPropertyValueId
    // )
    const { assessmentStatus, status } = await WaitExchangeTrainClassDetailVo.getUserStatusInScheme(
      userId,
      detail.schemeId
    )
    detail.assessmentStatus = assessmentStatus
    detail.status = status
    detail.orderNo = response.orderNo || ''
    detail.subOrderNo = response.subOrderNo || ''
    detail.periodName = response?.commoditySku?.issueInfo?.issueName
    detail.periodNo = response?.commoditySku?.issueInfo?.issueNum
    detail.periodId = response?.commoditySku?.issueInfo?.issueId
    detail.sourceType = response?.commoditySku?.issueInfo?.sourceType
    detail.sourceId = response?.commoditySku?.issueInfo?.sourceId
    return detail
  }

  /**
   * 批量转换远端模型
   * @param userId
   * @param responseList
   */
  static batchFrom(responseList: BuyerValidCommodityResponse[]) {
    const result = new Array<WaitExchangeTrainClassDetailVo>()
    responseList.forEach((response) => {
      const detail = new WaitExchangeTrainClassDetailVo()
      detail.schemeId = (response.commoditySku?.resource as SchemeResourceResponse)?.schemeId || ''
      detail.commoditySkuId = response.commoditySku?.commoditySkuId || ''
      detail.schemeType = WaitExchangeTrainClassDetailVo.getSchemeType(response)
      detail.schemeName = (response.commoditySku?.resource as SchemeResourceResponse)?.schemeName || ''
      detail.industryPropertyId = response.commoditySku?.skuProperty?.industry?.skuPropertyValueId
      detail.trainingObject =
        response.commoditySku?.skuProperty?.trainingObject?.skuPropertyValueShowName ||
        response.commoditySku?.skuProperty?.trainingObject?.skuPropertyValueName
      detail.positionCategory =
        response.commoditySku?.skuProperty?.positionCategory?.skuPropertyValueShowName ||
        response.commoditySku?.skuProperty?.positionCategory?.skuPropertyValueName
      detail.jobLevel =
        response.commoditySku?.skuProperty?.jobLevel?.skuPropertyValueShowName ||
        response.commoditySku?.skuProperty?.jobLevel?.skuPropertyValueName
      detail.jobCategory =
        response.commoditySku?.skuProperty?.jobCategory?.skuPropertyValueShowName ||
        response.commoditySku?.skuProperty?.jobCategory?.skuPropertyValueName
      detail.industry = response.commoditySku?.skuProperty?.industry?.skuPropertyValueName || ''
      detail.industryId = response.commoditySku?.skuProperty?.industry?.skuPropertyValueId || ''

      detail.year = response.commoditySku?.skuProperty?.year?.skuPropertyValueName || ''
      detail.subjectType = response.commoditySku?.skuProperty?.subjectType?.skuPropertyValueName || ''
      detail.trainingCategory = response.commoditySku?.skuProperty?.trainingCategory?.skuPropertyValueName || ''
      detail.trainingProfessional = response.commoditySku?.skuProperty?.trainingProfessional?.skuPropertyValueName || ''

      detail.subject = response.commoditySku?.skuProperty?.discipline?.skuPropertyValueName || ''
      detail.grade = response.commoditySku?.skuProperty?.learningPhase?.skuPropertyValueName || ''
      detail.certificatesType = response.commoditySku?.skuProperty?.certificatesType?.skuPropertyValueName || ''
      detail.practitionerCategory = response.commoditySku?.skuProperty?.practitionerCategory?.skuPropertyValueName || ''
      if (response.order?.subOrderItems?.length) {
        const findItem = response.order?.subOrderItems.find((item) => {
          return (
            item.currentCommoditySku?.commoditySkuId &&
            response.commoditySku?.commoditySkuId &&
            item.currentCommoditySku.commoditySkuId === response.commoditySku?.commoditySkuId
          )
        })
        detail.price = findItem ? findItem.price : 0
      } else {
        // detail.price = response.order?.basicData?.amount || 0
        detail.price = 0
      }
      detail.resourceType = WaitExchangeTrainClassDetailVo.getResourceType(response)
      detail.saleChannelName = response.saleChannelName || ''
      detail.saleChannelId = response.saleChannelId || ''
      detail.saleChannel = response.saleChannel || null
      detail.distributorId = response.distributorId || ''
      // detail.assessmentStatus = assessmentStatus
      // detail.status = status
      detail.orderNo = response.orderNo || ''
      detail.subOrderNo = response.subOrderNo || ''
      detail.periodName = response?.commoditySku?.issueInfo?.issueName
      detail.periodNo = response?.commoditySku?.issueInfo?.issueNum
      detail.periodId = response?.commoditySku?.issueInfo?.issueId
      detail.sourceType = response?.commoditySku?.issueInfo?.sourceType
      detail.sourceId = response?.commoditySku?.issueInfo?.sourceId
      result.push(detail)
    })
    return result
  }
  /**
   * 获取培训方案类型
   */
  static getSchemeType(response: BuyerValidCommodityResponse): TrainClassSchemeEnum | null {
    const schemeResource = (response.commoditySku?.resource as SchemeResourceResponse)?.schemeType
    if (schemeResource) {
      if (schemeResource === 'chooseCourseLearning') {
        return TrainClassSchemeEnum.Choose_Course_Learning
      }
      if (schemeResource === 'autonomousCourseLearning') {
        return TrainClassSchemeEnum.Autonomous_Course_Learning
      }
      return null
    }
    return null
  }

  /**
   * 获取用户培训班内状态
   */
  static async getUserStatusInScheme(userId: string, schemeId: string) {
    const data: {
      assessmentStatus: TrainClassAssessmentStatusEnum | null
      status: TrainClassStatusEnum | null
    } = {
      assessmentStatus: null,
      status: null
    }
    // 构建查询参数
    const page: Page = new Page()
    page.pageNo = 1
    page.pageSize = 1
    const request: StudentSchemeLearningRequest = new StudentSchemeLearningRequest()
    // 学员id
    request.student = new UserRequest()
    request.student.userIdList = [userId]
    // 方案id
    request.scheme = new SchemeRequest()
    request.scheme.schemeId = schemeId
    // 学员学习状态
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.status = [1, 2]
    const response = await MsSchemeLearningQuery.pageStudentSchemeLearningInServicer({
      page,
      request
    })
    // 获取查询结果
    const assessmentStatus: number = response.data.currentPageData[0]?.studentLearning?.trainingResult || null
    const status: number = response.data.currentPageData[0]?.learningRegister?.status || 0
    // 设置考核情况
    switch (assessmentStatus) {
      // 未知（待考核）
      case -1:
        data.assessmentStatus = TrainClassAssessmentStatusEnum.For_Inspection
        break
      // 培训合格
      case 1:
        data.assessmentStatus = TrainClassAssessmentStatusEnum.Qualified
        break
      // 培训不合格
      case 0:
        data.assessmentStatus = TrainClassAssessmentStatusEnum.Unqualified
        break
      default:
        data.assessmentStatus = null
        break
    }
    // 设置状态
    switch (status) {
      // 正常
      case 1:
        data.status = TrainClassStatusEnum.Effective
        break
      // 冻结
      case 2:
        data.status = TrainClassStatusEnum.Frozen
        break
      // 失效
      case 3:
        data.status = TrainClassStatusEnum.Failure
        break
      default:
        data.status = null
        break
    }
    return data
  }

  static async batchGetUserStatusInScheme(userId: string, schemeIds: Array<string>) {
    const result = new Map<
      string,
      {
        assessmentStatus: TrainClassAssessmentStatusEnum | null
        status: TrainClassStatusEnum | null
      }
    >()
    const request = new StudentSchemeLearningRequest()
    // 学员id
    request.student = new UserRequest()
    request.student.userIdList = [userId]
    // 方案id
    request.scheme = new SchemeRequest()
    request.scheme.schemeIdList = schemeIds
    // 学员学习状态
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.status = [1, 2]
    const page: Page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    const response = await MsSchemeLearningQuery.pageStudentSchemeLearningInServicer({
      page,
      request
    })
    if (response.status.isSuccess()) {
      response.data.currentPageData.forEach((studentScheme) => {
        schemeIds.forEach((id) => {
          if (studentScheme.scheme.schemeId === id) {
            const data: {
              assessmentStatus: TrainClassAssessmentStatusEnum | null
              status: TrainClassStatusEnum | null
            } = {
              assessmentStatus: null,
              status: null
            }
            const assessmentStatus: number =
              studentScheme?.studentLearning?.trainingResult === 0
                ? 0
                : studentScheme?.studentLearning?.trainingResult || null
            const status: number = studentScheme?.learningRegister?.status || 0
            // 设置考核情况
            switch (assessmentStatus) {
              // 未知（待考核）
              case -1:
                data.assessmentStatus = TrainClassAssessmentStatusEnum.For_Inspection
                break
              // 培训合格
              case 1:
                data.assessmentStatus = TrainClassAssessmentStatusEnum.Qualified
                break
              // 培训不合格
              case 0:
                data.assessmentStatus = TrainClassAssessmentStatusEnum.Unqualified
                break
              default:
                data.assessmentStatus = null
                break
            }
            // 设置状态
            switch (status) {
              // 正常
              case 1:
                data.status = TrainClassStatusEnum.Effective
                break
              // 冻结
              case 2:
                data.status = TrainClassStatusEnum.Frozen
                break
              // 失效
              case 3:
                data.status = TrainClassStatusEnum.Failure
                break
              default:
                data.status = null
                break
            }
            result.set(id, data)
          }
        })
      })
    }
    return result
  }

  /**
   * 获取来源类型
   */
  static getResourceType(response: BuyerValidCommodityResponse): TrainClassResourceEnum | null {
    if (response.channelType === 1) {
      return TrainClassResourceEnum.Personal_Register
    }
    if (response.channelType === 2) {
      return TrainClassResourceEnum.Collective_Register
    }
    if (response.channelType === 3) {
      return TrainClassResourceEnum.Import_Open
    }
    if (response.formExchangeOrder) {
      return TrainClassResourceEnum.Exchange_Train_Class
    }
    return null
  }
}

export default WaitExchangeTrainClassDetailVo
