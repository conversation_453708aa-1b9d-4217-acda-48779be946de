import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
import { Component, Vue, Prop } from 'vue-property-decorator'
import CommonSchemeListModel from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSchemeListModel'
@Component
export default class extends Vue {
  /**
   * 选项值
   */
  @Prop({
    type: Array,
    default: () => new Array<string>()
  })
  value: string[]

  /**
   * 选择器类型
   */
  @Prop({
    type: String,
    default: ''
  })
  selectType: string

  /**
   * 行业id
   */
  @Prop({
    type: String,
    default: ''
  })
  industryId: string

  //  行业属性id
  @Prop({
    type: String,
    default: ''
  })
  industryPropertyId: string

  //行业属性分类id
  @Prop({
    type: String,
    default: ''
  })
  categoryCode: IndustryPropertyCodeEnum

  /**
   * 提示语
   */
  @Prop({
    type: String,
    default: '请选择'
  })
  placeholder: string

  /**
   * 当前存储行业信息
   */
  commonSchemeListModel = CommonSchemeListModel

  /**
   * 当前点击移除的值
   */
  removeValue: string[] = []

  // 是否被禁用
  get isDisabled() {
    return this.value?.includes('-1')
  }

  /**
   * 不包含行业下的行业信息
   */
  get selectSchemeIndustry() {
    return this.commonSchemeListModel.noIncludeSchemeList.filter((item) => {
      return item.sku.industry.skuPropertyValueId == this.industryId
    })
  }

  /**
   * 不包含行业下的相同行业属性
   */
  get selectSchemeValue() {
    return this.selectSchemeIndustry.some((ite) => {
      return this.removeValue.includes(ite.sku[this.selectType].skuPropertyValueId.toString())
    })
  }

  /**
   * 特殊规则下的行业信息
   */
  get specialIndustry() {
    const arr: RuleSchemeItem[] = []
    this.commonSchemeListModel.schemeSpecialRuleList.forEach((item) => {
      item.schemeList.forEach((ite) => {
        if (ite.sku.industry.skuPropertyValueId == this.industryId) {
          arr.push(ite)
        }
      })
    })
    return arr
  }

  /**
   * 特殊规则下的相同行业属性
   */
  get specialSchemeValue() {
    return this.specialIndustry.some((ite) => {
      return this.removeValue.includes(ite.sku[this.selectType].skuPropertyValueId.toString())
    })
  }

  get selectValue() {
    return this.value
  }

  /**
   * 设置选项值
   */
  set selectValue(val: string[]) {
    const removeDialogContent =
      '当前属性对应的培训方案存在被特殊规则引用的方案，取消属性，将移除特殊规则里已添加的对应属性培训方案。是否确定取消？'
    // 当前点击移除的信息
    this.removeValue = this.value.filter((item) => {
      return !val.includes(item)
    })
    if (val.includes('-1')) {
      // 全部直接赋值
      this.$emit('input', ['-1'])
    } else if (this.value.includes('-1')) {
      if (!this.selectSchemeIndustry.length && !this.specialIndustry.length) {
        // 若全部数据中不存在已设置的行业属性，直接删除
        this.$emit('input', val)
      } else {
        this.$emit('removeSku', 'removeAll', this.industryId, this.selectType, this.removeValue)
        this.$confirm(removeDialogContent, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
          .then((e) => {
            this.$emit('removeSku', this.industryId, this.selectType, this.removeValue)
            this.$emit('input', val)
          })
          .catch((e) => {
            console.log(e)
          })
      }
    } else if (val.length > this.value.length || (!this.selectSchemeValue && !this.specialSchemeValue)) {
      this.$emit('input', val)
    } else {
      this.$confirm(removeDialogContent, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then((e) => {
          this.$emit('removeSku', this.industryId, this.selectType, this.removeValue)
          this.$emit('input', val)
        })
        .catch((e) => {
          console.log(e)
        })
    }
  }
}
