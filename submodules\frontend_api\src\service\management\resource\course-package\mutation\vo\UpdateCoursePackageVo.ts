import CreateCoursePackageVo from '@api/service/management/resource/course-package/mutation/vo/CreateCoursePackageVo'
import CoursePackageDetailVo from '@api/service/management/resource/course-package/query/vo/CoursePackageDetailVo'

class UpdateCoursePackageVo extends CreateCoursePackageVo {
  id: string

  static from(detail: CoursePackageDetailVo) {
    const updateVo = new UpdateCoursePackageVo()
    updateVo.id = detail.id
    updateVo.name = detail.name
    updateVo.showName = detail.showName
    return updateVo
  }
}

export default UpdateCoursePackageVo
