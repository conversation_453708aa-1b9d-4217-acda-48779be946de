import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import { ResponseStatus } from '@hbfe/common'
import MsCoursePlayResourceV1 from '@api/ms-gateway/ms-course-play-resource-v1'

/**
 * 申请课程试听 token
 */
class ApplyTrialListenCourseToken extends AbstractApplyToken {
  /**
   * 课程学习播放凭证/课程试听凭证/课程预览凭证
   */
  private readonly courseId?: string

  constructor(courseId: string) {
    super()
    this.courseId = courseId
  }

  async apply(): Promise<ResponseStatus> {
    const { status, data } = await MsCoursePlayResourceV1.applyCourseAudition(this.courseId)
    if (status.isSuccess()) {
      if (data.applyResult.code !== '200') {
        return Promise.reject(new ResponseStatus(parseInt(data.applyResult.code), data.applyResult.message))
      }
      this.token = data.token
    }
  }

  // * 不用配置
  async applyWithoutValidate(): Promise<ResponseStatus> {
    const { status, data } = await MsCoursePlayResourceV1.applyCourseAuditionWithoutValidate(this.courseId)
    if (status.isSuccess()) {
      if (data.applyResult.code !== '200') {
        return Promise.reject(new ResponseStatus(parseInt(data.applyResult.code), data.applyResult.message))
      }
      this.token = data.token
    }
  }
}

export default ApplyTrialListenCourseToken
