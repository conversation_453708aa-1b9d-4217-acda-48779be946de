<template>
  <div>
    <el-drawer
      title="请选择打印方式"
      :visible.sync="visible"
      size="700px"
      custom-class="m-drawer"
      :append-to-body="true"
      destroy-on-close
      @close="cancel"
    >
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form ref="form" :model="printCertificationsVo" label-width="auto" class="m-form f-mt50">
              <!-- 文件类型 -->
              <el-form-item>
                <div slot="label">
                  <span class="f-cr f-mr5">*</span>
                  <span class="f-vm">文件类型</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>支持配置批量打印的类型：</p>
                      <p>1.连贯打印：本次批量打印的所有合格记录，是一份PDF，命名方式同导出文件名。</p>
                      <p>2.单个文件：每个学员对应的一条合格记录，是一份PDF，且以学员的姓名、证件号命名。</p>
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <el-radio-group v-model="printCertificationsVo.printType" @change="printTypeChange">
                  <el-radio :label="1">连贯打印</el-radio>
                  <el-radio :label="2">单个文件</el-radio>
                </el-radio-group>
              </el-form-item>

              <!-- 是否合并打印 -->
              <el-form-item v-if="printCertificationsVo.printType === 1">
                <div slot="label">
                  <span class="f-cr f-mr5">*</span>
                  <span class="f-vm">是否合并打印</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>
                        连贯打印模式下，如打印的证明尺寸为四分之一的A4纸张或
                        二分之一的A4纸，如需合并在一张A4纸上进行打印，是否合并打印请选择“是”
                      </p>
                      <p class="f-mt5">
                        <i class="f-co">注意：</i>如本次需要打印的证明尺寸超过四分之一的A4纸张或
                        二分之一的A4纸，无法选择合并打印。
                      </p>
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <el-radio-group v-model="printCertificationsVo.isMerge">
                  <el-radio label="1" :disabled="!schemeIsMerge">是</el-radio>
                  <el-radio label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item class="m-btn-bar">
                <el-button @click="cancel">取消</el-button>
                <el-button type="primary" @click="sure" :loading="sureBtnLoading">确定</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>

    <!-- 导出成功弹窗提示 -->
    <el-dialog :visible.sync="exportDialog" width="400px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务管理-批量打印证明</p>
        </div>
      </div>
      <div slot="footer">
        <el-button type="info" @click="exportDialog = false">暂 不</el-button>
        <el-button type="primary" @click="toDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop } from 'vue-property-decorator'
  import { debounce, bind } from 'lodash-decorators'
  import PrintCertificationsVo from '@api/service/management/personal-leaning/mutation/vo/PrintCertificationsVo'
  import MutationBatchPrintTraining from '@api/service/diff/management/jxgx/personal-leaning/mutation/MutationBatchPrintTraining'
  import LearningArcjovesRequest from '@api/service/management/personal-leaning/query/vo/LearningArcjovesRequest'
  import { FileTypesEnum } from '@api/service/common/enums/personal-leaning/FileTypes'

  @Component({
    components: {}
  })
  export default class extends Vue {
    @Prop({
      type: Object,
      default: () => {
        return new LearningArcjovesRequest()
      }
    })
    certifyParam: LearningArcjovesRequest

    /**
     * 是否合并打印
     */
    @Prop({
      type: Boolean,
      default: false
    })
    schemeIsMerge: boolean

    /**
     * 是否显示抽屉控制
     */
    visible = false

    /**
     * 导出成功弹窗
     */
    exportDialog = false

    // 按钮加载状态
    sureBtnLoading = false

    // 数据
    printCertificationsVo = new PrintCertificationsVo()

    batchPrintTrainingModule = new MutationBatchPrintTraining()

    open() {
      this.visible = true
    }

    // 确定按钮
    @bind
    @debounce(200)
    async sure() {
      // 校验必填项
      if (this.printCertificationsVo.printType === 1 && this.printCertificationsVo.isMerge === '') {
        return this.$message.error('请选择是否合并打印')
      }

      try {
        this.sureBtnLoading = true
        this.printCertificationsVo.fileType = FileTypesEnum.PDF
        //   走接口请求
        const result = await this.batchPrintTrainingModule.doBatchPrintCertificates(
          this.printCertificationsVo,
          this.certifyParam
        )
        // 接口请求成功后导出成功弹框提示
        if (result?.status?.code === 200) {
          this.exportDialog = true
          this.visible = false
        } else {
          this.$message.error('批量打印失败')
        }
        this.visible = false
      } catch (e) {
        console.log()
      } finally {
        this.sureBtnLoading = false
      }
    }

    /**
     * @description 文件类型切换
     * */
    printTypeChange() {
      // 是否单个文件打印
      const isSingleFilePrint = this.printCertificationsVo.printType === 2
      // 判断是否能提供默认选中项入参回显逻辑
      this.printCertificationsVo.isMerge = isSingleFilePrint ? '' : this.schemeIsMerge ? '1' : '0'
    }

    // 取消按钮
    cancel() {
      // 清除所有的已选
      this.printCertificationsVo = new PrintCertificationsVo()
      this.sureBtnLoading = false
      this.visible = false
    }

    // 前往导出任务查看页面
    toDownloadPage() {
      this.exportDialog = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: {
          type: 'exportCertificatePrintFile'
        }
      })
    }
  }
</script>

<style></style>
