import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 异步任务执行状态枚举
 */
export enum HywSchemeRefundEnum {
  /**
   * 存在退款中的课程
   */
  scheme_refunding,

  /**
   * 公需课退款完成
   */
  public_refunded,
  /**
   * 专业课退款完成
   */
  professional_refunded
}
/**
 * @description 异步任务执行状态列表
 */
export default class HywSchemeRefundStatus extends AbstractEnum<HywSchemeRefundEnum> {
  constructor(status?: HywSchemeRefundEnum) {
    super()
    this.current = status
    this.map.set(HywSchemeRefundEnum.scheme_refunding, '存在退款中的课程')
    this.map.set(HywSchemeRefundEnum.public_refunded, '公需课退款完成')
    this.map.set(HywSchemeRefundEnum.professional_refunded, '专业课退款完成')
  }
}
