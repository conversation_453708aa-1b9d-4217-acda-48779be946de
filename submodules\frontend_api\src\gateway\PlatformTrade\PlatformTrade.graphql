schema {
	query:Query
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	exportBatchOrder(requestParams:BatchOrderQueryParam):Boolean!
	exportBatchRefundOrder(requestParams:BatchRefundOrderParam):Boolean!
	"""导出订单管理
		@param param
		@return
	"""
	exportOrder(param:OrderQueryDTO):Boolean!
	"""导出订单管理
		为渠道商提供接口
		@param param
		@return
	"""
	exportOrderForChannelVendor(param:OrderQueryDTO):Boolean!
	"""导出订单管理
		为参训单位提供接口
		@param param
		@return
	"""
	exportOrderForParticipatingUnit(param:OrderQueryDTO):Boolean!
	"""导出订单管理
		为机构提供接口
		@param param
		@return
	"""
	exportOrderForTrainingInstitution(param:OrderQueryDTO):Boolean!
	"""导出对账管理
		@param param
		@return
	"""
	exportReconciliation(param:OrderQueryDTO):Boolean!
	"""导出对账管理
		为渠道商提供接口
		@param param
		@return
	"""
	exportReconciliationForChannelVendor(param:OrderQueryDTO):Boolean!
	"""导出对账管理
		为机构提供接口
		@param param
		@return
	"""
	exportReconciliationForTrainingInstitution(param:OrderQueryDTO):Boolean!
	exportRefundOrder(requestParams:RefundOrderParam):Boolean!
	"""获取某个专业关系节点下一级章节子节点
		@param majorRelationId 专业关系子节点
		@return 章节列表
	"""
	findAllChildChapterByMajor(majorRelationId:String):[ChapterDTO] @NotAuthenticationRequired
	"""获取指定多个专业关系下章节节点及子节点
		@param majorRelationIdList 专业关系节点列表
		@return 专业关系节点对应章节列表
	"""
	findAllChildChapterByMajorList(majorRelationIdList:[String]):[MajorMapChapterListDTO] @NotAuthenticationRequired
	"""获取所有行业及专业
		@return 行业专业关系树
	"""
	findAllIndustryRelationList:[IndustryDTO] @NotAuthenticationRequired
	"""延迟查询退款订单统计信息
		@param queryParam
		@return
	"""
	getBatchRefundStatistics(queryParam:BatchRefundOrderParam):LazyRefunOrderStatisticsDTO
	"""根据期别商品id查询期别商品信息"""
	getIssueByCommoditySkuId(commoditySkuId:String):CommodityInfoDTO1 @NotAuthenticationRequired
	"""查询我的订单"""
	getMyOrder(orderNo:String):OrderDTO
	"""lazy查询订单
		查询二次清洗表，一般用在管理后台
	"""
	getOrder(orderNo:String):OrderDTO
	"""根据方案id查询方案信息"""
	getPreExamLSById(schemeId:String):PreExamLSInfoDTO @NotAuthenticationRequired
	"""延迟查询退款订单详情
		@param refunOrderNo
		@return
	"""
	getRefundOrder(refunOrderNo:String):RefundOrderDetailsDTO
	"""lazy查询订单
		查询二次清洗表，一般用在管理后台
	"""
	lazyBatchGetOrder(batchNo:String):BatchOrderDTO
	"""延迟查询退款订单详情
		@param refunOrderNo
		@return
	"""
	lazyBatchGetRefundOrder(refunOrderNo:String):BatchRefundOrderDetailsDTO
	"""lazy查询订单统计
		查询二次清洗表，一般用在管理后台
	"""
	lazyBatchOrderStatistics(queryParam:BatchOrderQueryParam):OrderStatisticsDTO
	"""lazy查询订单分页
		查询二次清洗表，一般用在管理后台
	"""
	lazyBatchPageOrder(page:Page,queryParam:BatchOrderQueryParam):BatchOrderDTOPage @page(for:"BatchOrderDTO")
	"""延迟查询退款订单分页
		@param page
		@param queryParam
		@return
	"""
	lazyBatchPageRefundOrder(page:Page,queryParam:BatchRefundOrderParam):BatchRefundOrderDTOPage @page(for:"BatchRefundOrderDTO")
	"""根据期别商品id集合查询期别商品信息
		@return
	"""
	listIssueByCommoditySkuIds(commoditySkuIds:[String]):[CommodityInfoDTO1] @NotAuthenticationRequired
	"""根据方案id查询期别商品信息"""
	listIssueBySchemeId(schemeId:String):[IssueDTO] @NotAuthenticationRequired
	"""根据方案id 获取 方案内课程以及讲师"""
	listPreExamLSCourse(schemeId:String):[PreExamLSCourseDTO] @NotAuthenticationRequired
	"""获取所有商品的年度
		@deprecated 目前补贴未用到，暂未提供
	"""
	listYear:[YearDTO] @NotAuthenticationRequired
	"""查询订单分页
		查询二次清洗表，一般用在管理后台
		为渠道商提供接口
		@param page
		@param param
		@return
	"""
	pageForChannelVendor(page:Page,param:OrderQueryDTO):OrderDTOPage @page(for:"OrderDTO")
	"""查询订单分页
		查询二次清洗表，一般用在管理后台
		为参训单位提供接口
		@param page
		@param param
		@return
	"""
	pageForParticipatingUnit(page:Page,param:OrderQueryDTO):OrderDTOPage @page(for:"OrderDTO")
	"""查询订单分页
		查询二次清洗表，一般用在管理后台
		为机构提供接口
		@param page
		@param param
		@return
	"""
	pageForTrainingInstitution(page:Page,param:OrderQueryDTO):OrderDTOPage @page(for:"OrderDTO")
	"""查询我的订单分页(多用于学员侧查询，只会查询出自己的订单)"""
	pageMyOrder(page:Page,param:MyOrderQueryDTO):OrderDTOPage @page(for:"OrderDTO")
	"""查询订单分页
		查询二次清洗表，一般用在管理后台
	"""
	pageOrder(page:Page,param:OrderQueryDTO):OrderDTOPage @page(for:"OrderDTO")
	"""查询已发布的培训方案分页(附带教师信息)"""
	pagePutawayPreExamLS(page:Page,query:PreExamLSQueryDTO):PreExamLSInfoDTOPage @page(for:"PreExamLSInfoDTO") @NotAuthenticationRequired
	"""延迟查询退款订单分页
		@param page
		@param queryParam
		@return
	"""
	pageRefundOrder(page:Page,queryParam:RefundOrderParam):RefundOrderDTOPage @page(for:"RefundOrderDTO")
	"""lazy查询订单统计"""
	statisticsOrder(param:OrderQueryDTO):OrderStatisticsDTO
	"""lazy查询订单统计
		为渠道商提供接口
	"""
	statisticsOrderForChannelVendor(param:OrderQueryDTO):OrderStatisticsDTO
	"""lazy查询订单统计
		为参训单位提供接口
	"""
	statisticsOrderForParticipatingUnit(param:OrderQueryDTO):OrderStatisticsDTO
	"""lazy查询订单统计
		为机构提供接口
	"""
	statisticsOrderForTrainingInstitution(param:OrderQueryDTO):OrderStatisticsDTO
	"""延迟查询退款订单统计信息
		@param queryParam
		@return
	"""
	statisticsRefundOrder(queryParam:RefundOrderParam):LazyRefunOrderStatisticsDTO
}
"""延迟查询批次单的查询参数
	<AUTHOR> create 2020/3/11 11:27
"""
input BatchOrderQueryParam @type(value:"com.fjhb.btpx.integrative.service.trade.dto.request.BatchOrderQueryParam") {
	"""集体缴费批次号"""
	batchNo:String
	"""集体缴费单位id"""
	batchUnitId:String
	"""收款账号id"""
	receiveAccountId:String
	"""银行交易流水号"""
	payFlowNo:String
	"""交易状态"""
	status:[Int]
	"""退款状态 1.已申请   2.退款成功 3.退款失败
		-1标识为未退款状态，其他值则都无效
		与底层状态不同，只关心最终状态。
		如果是拒绝退款就直接清空退款的数据了
	"""
	refundStatus:[Int]
	"""是否为0元批次单"""
	zeroOrder:Boolean
	"""用户id"""
	userIds:[String]
	"""手机号码(后台根据这个获取用户id)"""
	buyerPhoneNumber:String
	"""证件号(后台根据这个获取用户id)"""
	buyerIdentity:String
	"""用户名"""
	buyerName:String
	"""买家登录账户"""
	buyerLoginInput:String
	"""批次单创建开始时间 >"""
	createStartTime:DateTime
	"""批次单创建结束时间 <="""
	createEndTime:DateTime
	"""批次单提交开始时间 >"""
	commitStartTime:DateTime
	"""批次单提交结束时间 <="""
	commitEndTime:DateTime
	"""批次单完成开始时间 >"""
	completeStartTime:DateTime
	"""批次单创建结束时间 <="""
	completeEndTime:DateTime
	"""是否是测试数据"""
	test:Boolean
}
"""退款查询参数
	<AUTHOR> create 2020/4/14 11:40
"""
input BatchRefundOrderParam @type(value:"com.fjhb.btpx.integrative.service.trade.dto.request.BatchRefundOrderParam") {
	"""集体缴费批次号"""
	batchNo:String
	"""集体缴费单位id"""
	batchUnitId:String
	"""银行交易流水号"""
	payFlowNo:String
	"""退款批次单状态"""
	refundStatus:[Int]
	"""批次退款类型
		@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderTypeConst
	"""
	refundType:Int
	"""退款方式 1.线上 2.线下"""
	refundWay:Int
	"""退款模式 1：普通退款 2：强制退款"""
	refundMode:Int
	"""收款账号id"""
	receiveAccountId:String
	"""退款申请时间"""
	applyStartTime:DateTime
	"""退款申请时间"""
	applyEndTime:DateTime
	"""审核时间查询-开始时间"""
	auditStartTime:DateTime
	"""审核时间查询-结束时间"""
	auditEndTime:DateTime
	"""退款完成时间"""
	refundCompleteStartTime:DateTime
	"""退款完成时间"""
	refundCompleteEndTime:DateTime
	"""是否是测试数据"""
	test:Boolean
	"""用户id"""
	userIds:[String]
	"""手机号码(后台根据这个获取用户id)"""
	buyerPhoneNumber:String
	"""证件号(后台根据这个获取用户id)"""
	buyerIdentity:String
	"""用户名"""
	buyerName:String
	"""买家登录账户"""
	buyerLoginInput:String
}
"""我的订单查询参数对象"""
input MyOrderQueryDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.request.MyOrderQueryDTO") {
	"""订单号"""
	orderNo:String
	"""订单号或方案名称"""
	orderNoOrSchemeName:String
	"""订单状态
		@see com.fjhb.btpx.support.constants.trade.OrderStatus
		"等待付款", 1
		"等待卖家确认款项", 2
		"支付中", 8
		"支付成功", 3
		"发货中", 4
		"发货部分失败", 9
		"发货失败",10
		"发货已完成", 5
		"交易成功", 6
		"交易关闭", 7
	"""
	states:[Int]
	"""订单创建时间"""
	createTime:TimeRegionRequest
	"""订单方案"""
	schemeId:String
	"""订单购买的商品名称"""
	schemeName:String
}
"""延迟查询订单的查询参数
	<AUTHOR> create 2020/3/11 11:27
"""
input OrderQueryDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.request.OrderQueryDTO") {
	"""订单号"""
	orderNo:String
	"""订单号"""
	orderNoList:[String]
	"""机构ID"""
	trainingInstitutionIdList:[String]
	"""机构名称"""
	trainingInstitutionName:String
	"""课件供应商id"""
	coursewareSupplierIdList:[String]
	"""参训单位名称"""
	participatingUnitName:String
	"""参训单位ID"""
	participatingUnitIdList:[String]
	"""渠道商id"""
	channelVendorIdList:[String]
	"""交易状态
		"等待付款", 1
		"等待卖家确认款项", 2
		"支付中", 8
		"支付成功", 3
		"发货中", 4
		"发货部分失败", 9
		"发货失败",10
		"发货已完成", 5
		"交易成功", 6
		"交易关闭", 7
	"""
	status:[Int]
	"""退款状态 1.已申请   2.退款成功 3.退款失败
		-1标识为未退款状态，其他值则都无效
		与底层状态不同，只关心最终状态。
		如果是拒绝退款就直接清空退款的数据了
	"""
	refundStatus:[Int]
	"""购买渠道code
		自主报名、集体缴费、导入开通
		@see PurchaseChannelTypes
		用户自主购买  1;
		集体缴费  2;
		管理员导入  3;
		补贴系统 4;
	"""
	purchaseChannelCodeList:[Int]
	"""报名渠道（终端）code
		web、小程序、公众号
		@see PurchaseChannelTerminalCodes
		Web端 "Web";
		IOS端 "IOS";
		安卓端  "Android";
		微信小程序  "WechatMini";
		微信公众号  "WechatOfficial";
		补贴管理系统 "ExternalSystemManage";
	"""
	placeChannelCodeList:[String]
	"""订单创建时间"""
	createTime:TimeRegionRequest
	"""订单交易完成时间"""
	completeTime:TimeRegionRequest
	"""是否为补考"""
	makeUpExam:Boolean
	"""是否为0元订单"""
	zeroOrder:Boolean
	"""集体缴费批次号"""
	batchNo:String
	"""集体缴费单位id"""
	batchUnitId:String
	"""收款账号id"""
	receiveAccountId:String
	"""交易流水号"""
	payFlowNo:String
	"""发券机构（属地人社局）（培训券的信息）"""
	publishOrgNameList:[String]
	"""用户id"""
	buyerId:String
	"""用户id"""
	buyerIds:[String]
	"""用户名称"""
	buyerName:String
	"""手机号码(后台根据这个获取用户id)"""
	buyerPhoneNumber:String
	"""证件号(后台根据这个获取用户id)"""
	buyerIdentity:String
	"""方案id"""
	schemeId:String
	"""订单购买的方案名称"""
	schemeName:String
	"""订单号或方案"""
	orderNoOrSchemeName:String
	"""方案id"""
	schemeIdList:[String]
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeIdList:[String]
	"""path:培训工种类别/培训工种id
		用于培训类别联合工种多条件查询
	"""
	workTypeIdPathList:[String]
	"""期数id"""
	issueId:String
	"""适用人群"""
	suitableCrowNames:[String]
	"""是否是测试数据"""
	test:Boolean
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""上架方案查询参数对象"""
input PreExamLSQueryDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.request.PreExamLSQueryDTO") {
	"""年度id"""
	yearCode:Int
	"""方案名称"""
	schemeName:String
	"""方案id集合"""
	schemeIds:[String]
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""退款查询参数
	<AUTHOR> create 2020/4/14 11:40
"""
input RefundOrderParam @type(value:"com.fjhb.btpx.integrative.service.trade.dto.request.RefundOrderParam") {
	"""退款对应的主订单号"""
	orderNo:String
	"""退款对应的子订单号"""
	subOrderNo:String
	"""集体缴费批次号"""
	batchNo:String
	"""集体缴费单位id"""
	batchUnitId:String
	"""收款账号id"""
	receiveAccountId:String
	"""银行交易流水号"""
	bankTrasactionNumber:String
	"""所属培训方案id"""
	schemeId:String
	"""所属期别id"""
	issueId:String
	"""交易渠道信息   web、小程序、公众号
		@see PaymentChannelTypeEnum
	"""
	placeChannel:[String]
	"""单位id （所属培训机构id）"""
	unitId:String
	"""退款对应的订单是否补考"""
	makeUpExam:Boolean
	"""退款订单状态"""
	refundStatus:[Int]
	"""退款模式 1：普通退款 2：强制退款"""
	refundMode:Int
	"""用户id"""
	userIds:[String]
	"""手机号码(后台根据这个获取用户id)"""
	buyerPhoneNumber:String
	"""证件号(后台根据这个获取用户id)"""
	buyerIdentity:String
	"""买家登录账户"""
	buyerLoginInput:String
	"""退款申请时间"""
	applyStartTime:DateTime
	"""退款申请时间"""
	applyEndTime:DateTime
	"""审核时间查询-开始时间"""
	auditStartTime:DateTime
	"""审核时间查询-结束时间"""
	auditEndTime:DateTime
	"""退款完成时间"""
	refundCompleteStartTime:DateTime
	"""退款完成时间"""
	refundCompleteEndTime:DateTime
	"""是否是测试数据"""
	test:Boolean
}
"""时间范围
	<AUTHOR>
	@date 2020/5/3116:42
"""
input TimeRegionRequest @type(value:"com.fjhb.btpx.support.dto.TimeRegionRequest") {
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""商品信息
	<AUTHOR> create 2020/3/5 14:28
"""
type CommodityInfoDTO1 @type(value:"com.fjhb.btpx.integrative.gateway.graphql.dto.CommodityInfoDTO") {
	"""商品skuid"""
	commoditySkuId:String
	"""期数ID  (前端需要取这个id做期别id)"""
	issueId:String
	"""期别ID(暂时没用)"""
	stageId:String
	"""期别标题（期别名称）"""
	title:String
	"""方案id"""
	schemeId:String
	"""培训方案名称"""
	schemeName:String
	"""方案推荐指数
		产品要求为前端写死，该字段暂时无用
	"""
	recommendIndex:Double!
	"""开始时间"""
	startTime:String
	"""结束时间"""
	endTime:String
	"""销售价格"""
	price:BigDecimal
	"""班级图片"""
	picture:String
	"""商品学时"""
	period:Double!
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""方案内展示课程讲师树状对象
	<AUTHOR> create 2020/3/5 13:53
"""
type PreExamLSCourseDTO @type(value:"com.fjhb.btpx.integrative.gateway.graphql.dto.PreExamLSCourseDTO") {
	"""课程ID"""
	id:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""权重,表示学时,学分等"""
	period:Double!
	"""课程简介"""
	abouts:String
	"""所属课程包编号"""
	poolId:String
	"""所属课程类型
		1:必修课；2：选修；
	"""
	courseType:Int!
	"""课程包名称"""
	poolName:String
	"""课程内讲师"""
	teacherList:[TeacherSimpleDTO]
}
"""课程考核信息
	@author: eleven
	@date: 2020/6/5
"""
type CourseLearningAssessSettingResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.CourseLearningAssessSettingResponse") {
	allSelectedComplete:Boolean!
	schedule:Double!
}
"""@author: eleven
	@date: 2020/6/6
"""
type CourseLearningResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.CourseLearningResponse") {
	"""学习方案要求的最少学时"""
	minTotalPeriod:Double!
	"""课程考核要求"""
	assessSetting:CourseLearningAssessSettingResponse
	learningId:String
	enabled:Boolean!
	compulsoryPackages:[PackageRuleSettingDto]
	optionalPackages:[PackageRuleSettingDto]
}
"""@author: eleven
	@date: 2020/6/5
"""
type ExamAssessSettingResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.ExamAssessSettingResponse") {
	score:Double!
}
"""@author: eleven
	@date: 2020/6/6
"""
type ExamLearningResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.ExamLearningResponse") {
	"""考试考核，null表示不设置考核"""
	assessSetting:ExamAssessSettingResponse
	learningId:String
	enabled:Boolean!
	name:String
	examPaperId:String
	examTimeLength:Int!
	examCount:Int!
	configExamTime:Boolean!
	beginTime:DateTime
	endTime:DateTime
	passScore:Double!
	openResolvedExam:Boolean!
	minSubmitTimeLength:Int!
	less:Boolean!
	missScorePattern:Int!
}
"""@author: eleven
	@date: 2020/6/5
"""
type IssueClassLSAchieveSettingResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.IssueClassLSAchieveSettingResponse") {
	enabledAssess:Boolean!
	grade:Double
	templateId:String
}
type TeacherSimpleDTO @type(value:"com.fjhb.btpx.integrative.service.course.dto.TeacherSimpleDTO") {
	"""教师ID"""
	id:String
	"""教师名称"""
	name:String
	"""教师头像"""
	photo:String
	"""教师简介"""
	abouts:String
}
"""年度对象"""
type YearDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.YearDTO") {
	"""年度id"""
	id:String
	"""年度code"""
	yearCode:Int!
}
"""lazy批次单的分页对象
	<AUTHOR> create 2020/3/17 14:08
"""
type BatchOrderBillDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.BatchOrderBillDTO") {
	batchNo:String
	"""主键 发票ID"""
	billId:String
	"""发票号"""
	billNo:String
	"""发票类型：1普通发票，2增值税普通发票，3增值税专用发票"""
	invoiceType:Int!
	"""是否电子票"""
	electron:Boolean!
	"""发票抬头"""
	title:String
	"""统一社会信用代码"""
	taxpayerNo:String
	"""开户行"""
	billBankName:String
	"""账号"""
	billAccount:String
	"""地址"""
	billAddress:String
	"""电话"""
	billPhone:String
	"""是否非税发票"""
	noTaxBill:Boolean!
	"""发票状态"""
	billBlueStatus:Int!
	"""红票状态"""
	billRedStatus:Int!
}
"""lazy批次单的分页对象
	<AUTHOR> create 2020/3/17 14:08
"""
type BatchOrderDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.BatchOrderDTO") {
	batchNo:String
	"""批次单所属单位ID"""
	unitId:String
	"""集体缴费单位id"""
	batchUnitId:String
	"""集体缴费单位名称"""
	batchUnitName:String
	"""批次单总金额"""
	totalAmount:BigDecimal
	"""批次单待下单总金额"""
	pendingOrderTotalAmount:BigDecimal
	"""收款账号id"""
	receiveAccountId:String
	"""批次单状态"""
	status:Int!
	"""是否是测试数据"""
	test:Boolean!
	"""批次单应用类型 1:普通批次单,2:换货生成的新批次单"""
	applyType:Int!
	"""支付方式  支付方式 1：线上支付 2：线下支付,-1:表示未支付"""
	payType:Int!
	"""微信支付。支付宝支付"""
	tradeChannelCode:String
	"""微信支付。支付宝支付"""
	tradeChannelCodeName:String
	"""批次单实付总价"""
	batchTotalPayAmount:BigDecimal
	"""银行交易流水号"""
	payFlowNo:String
	"""缴费人次"""
	people:Int
	"""批次提交成功人次"""
	successPeople:Int
	"""培训总学时"""
	trainingHours:String
	"""批次单创建时间, yyyy-mm-dd 24hh:MM:dd"""
	createTime:DateTime
	"""批次单提交时间"""
	commitTime:DateTime
	"""发货成功时间 yyyy-mm-dd 24hh:MM:dd"""
	deliverySuccessTime:DateTime
	"""完成交易的时间, yyyy-mm-dd 24hh:MM:dd"""
	completeTime:DateTime
	"""批次单自动关闭时间"""
	autoCloseTime:DateTime
	"""付款时间, yyyy-mm-dd 24hh:MM:dd"""
	payTime:DateTime
	"""付款完成时间, yyyy-mm-dd 24hh:MM:dd"""
	payFinishTime:DateTime
	"""线下汇款时间"""
	remittanceCertificateDate:DateTime
	"""线下汇款凭证图片地址"""
	remittanceCertificateImagePaths:[String]
	"""买家id"""
	buyerId:String
	"""买家姓名"""
	buyerName:String
	"""买家电话号码"""
	buyerPhoneNumber:String
	"""买家身份证号码"""
	buyerIdentity:String
	"""买家登录账户"""
	buyerLoginInput:String
	"""退款批次单号"""
	batchRefundNo:String
	"""退款类型"""
	refundType:String
	"""退款申请人id"""
	refundApplyUserId:String
	"""退款申请时间"""
	refundApplyTime:DateTime
	"""退款总金额"""
	refundTotalAmount:BigDecimal
	"""退款方式 1.线上 2.线下"""
	refundWay:Int!
	"""退款状态 1.已申请   2.退款成功 3.退款失败
		与底层状态不同，只关心最终状态。
		如果是拒绝退款就直接清空退款的数据了
	"""
	refundStatus:Int!
	"""退款完成时间"""
	refundCompleteTime:DateTime
	"""是否需要发票"""
	needBill:Boolean!
	billList:[BatchOrderBillDTO]
	"""本条记录的创建时间"""
	recordCreateTime:DateTime
	"""本条记录的最后更新时间"""
	recordLastUpdateTime:DateTime
}
"""退款分页信息对象
	<AUTHOR> create 2020/4/14 11:45
"""
type BatchRefundOrderDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.BatchRefundOrderDTO") {
	"""退款批次单号"""
	batchRefundNo:String
	"""退款对应的批次单号"""
	batchNo:String
	"""集体缴费单位id"""
	batchUnitId:String
	"""集体缴费单位名称"""
	batchUnitName:String
	"""交易渠道信息   web、小程序、公众号"""
	placeChannel:String
	"""批次单总金额"""
	totalAmount:BigDecimal
	"""批次单实付总价"""
	batchTotalPayAmount:BigDecimal
	"""缴费人次"""
	people:Int
	"""银行交易流水号"""
	payFlowNo:String
	"""支付方式  支付方式 1：线上支付 2：线下支付,-1:表示未支付"""
	payType:Int
	"""退款总金额"""
	refundTotalAmount:BigDecimal
	"""退款批次单状态"""
	refundStatus:Int!
	"""创建方式
		1:系统创建
		2:用户创建
		3:管理员创建
		4:历史迁移
		5:外部接口
	"""
	createType:Int!
	"""退款方式 1.线上 2.线下"""
	refundWay:Int!
	"""批次退款类型
		@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderTypeConst
	"""
	refundType:Int
	"""退款模式 1：普通退款 2：强制退款"""
	refundMode:Int
	"""申请人id"""
	applyUserId:String
	"""申请人姓名"""
	applyUserName:String
	"""退款申请时间"""
	applyTime:DateTime
	"""退款申请审核人"""
	auditUserId:String
	"""审核人姓名"""
	auditUserName:String
	"""退款申请审核时间"""
	auditTime:DateTime
	"""取消退款人id"""
	cancelUserId:String
	"""取消退款人姓名"""
	cancelUserName:String
	"""取消退款时间"""
	cancelTime:DateTime
	"""资源回收时间"""
	recycledTime:DateTime
	"""批次退款完成时间"""
	refundCompleteTime:DateTime
	"""财务退款审核人id"""
	financeAuditUserId:String
	"""财务退款审核人名称"""
	financeAuditUserName:String
	"""财务审核同意/拒绝放款时间"""
	financeAuditTime:DateTime
	"""买家id"""
	buyerId:String
	"""买家姓名"""
	buyerName:String
	"""买家电话号码"""
	buyerPhoneNumber:String
	"""买家身份证号码"""
	buyerIdentity:String
	"""买家登录账户"""
	buyerLoginInput:String
}
"""退款详细
	<AUTHOR> create 2020/4/14 14:07
"""
type BatchRefundOrderDetailsDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.BatchRefundOrderDetailsDTO") {
	"""退款原因id"""
	refundReasonId:String
	"""退款原因"""
	refundReason:String
	"""申请退款描述"""
	refundDescription:String
	"""拒绝申请原因描述"""
	refuseApplyDesc:String
	"""拒绝退款原因描述"""
	refuseRefundDesc:String
	"""批次退款取消原因"""
	cancelReason:String
	"""退款批次单号"""
	batchRefundNo:String
	"""退款对应的批次单号"""
	batchNo:String
	"""集体缴费单位id"""
	batchUnitId:String
	"""集体缴费单位名称"""
	batchUnitName:String
	"""交易渠道信息   web、小程序、公众号"""
	placeChannel:String
	"""批次单总金额"""
	totalAmount:BigDecimal
	"""批次单实付总价"""
	batchTotalPayAmount:BigDecimal
	"""缴费人次"""
	people:Int
	"""银行交易流水号"""
	payFlowNo:String
	"""支付方式  支付方式 1：线上支付 2：线下支付,-1:表示未支付"""
	payType:Int
	"""退款总金额"""
	refundTotalAmount:BigDecimal
	"""退款批次单状态"""
	refundStatus:Int!
	"""创建方式
		1:系统创建
		2:用户创建
		3:管理员创建
		4:历史迁移
		5:外部接口
	"""
	createType:Int!
	"""退款方式 1.线上 2.线下"""
	refundWay:Int!
	"""批次退款类型
		@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderTypeConst
	"""
	refundType:Int
	"""退款模式 1：普通退款 2：强制退款"""
	refundMode:Int
	"""申请人id"""
	applyUserId:String
	"""申请人姓名"""
	applyUserName:String
	"""退款申请时间"""
	applyTime:DateTime
	"""退款申请审核人"""
	auditUserId:String
	"""审核人姓名"""
	auditUserName:String
	"""退款申请审核时间"""
	auditTime:DateTime
	"""取消退款人id"""
	cancelUserId:String
	"""取消退款人姓名"""
	cancelUserName:String
	"""取消退款时间"""
	cancelTime:DateTime
	"""资源回收时间"""
	recycledTime:DateTime
	"""批次退款完成时间"""
	refundCompleteTime:DateTime
	"""财务退款审核人id"""
	financeAuditUserId:String
	"""财务退款审核人名称"""
	financeAuditUserName:String
	"""财务审核同意/拒绝放款时间"""
	financeAuditTime:DateTime
	"""买家id"""
	buyerId:String
	"""买家姓名"""
	buyerName:String
	"""买家电话号码"""
	buyerPhoneNumber:String
	"""买家身份证号码"""
	buyerIdentity:String
	"""买家登录账户"""
	buyerLoginInput:String
}
"""类说明
	<AUTHOR> create 2020/4/14 11:49
"""
type CommodityInfoDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.CommodityInfoDTO") {
	"""商品id"""
	commodityId:String
	"""所属培训方案名称"""
	schemeName:String
	"""所属培训方案id"""
	schemeId:String
	"""所属期别(期数)名称"""
	issueTitle:String
	"""所属期别id"""
	stageId:String
	"""所属期数id"""
	issueId:String
	"""子订单商品年度 属性key"""
	yearPropertyId:String
	"""子订单商品年度"""
	year:String
	"""子订单商品年度 年度名称"""
	yearName:String
	"""子订单商品专业id"""
	professionId:String
	"""子订单商品专业id 属性key"""
	professionPropertyId:String
	"""子订单商品专业名称"""
	professionName:String
	"""子订单商品行业id 属性key"""
	professionTypePropertyId:String
	"""子订单商品行业id"""
	professionTypeId:String
	"""子订单商品类别、 行业名称"""
	professionTypeName:String
	"""培训类别"""
	trainingTypeId:String
	"""培训类别名称"""
	trainingTypeName:String
	"""培训对象"""
	traineesId:String
	"""培训对象名称"""
	traineesName:String
	"""岗位类别"""
	jobCategoryId:String
	"""岗位类别名称"""
	jobCategoryName:String
	"""单位类别"""
	unitCategoryId:String
	"""单位类别名称"""
	unitCategoryName:String
	"""培训学时"""
	trainingHours:String
}
"""期数信息
	对应前端的期别（原来叫期别，后面期别隐藏了，后端叫期数）
"""
type IssueDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.IssueDTO") {
	"""学习方案ID"""
	schemeId:String
	"""期数ID（对应前端期别）"""
	issueId:String
	"""商品SkuId"""
	commoditySkuId:String
	"""标题"""
	title:String
	"""学习开始时间"""
	startTime:DateTime
	"""学习结束时间"""
	endTime:DateTime
	"""销售价格"""
	price:BigDecimal
	"""开放报名开始时间"""
	upPlainTime:DateTime
	"""开放报名结束时间"""
	downPlainTime:DateTime
}
"""退款统计信息
	<AUTHOR> create 2020/4/14 14:14
"""
type LazyRefunOrderStatisticsDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.LazyRefunOrderStatisticsDTO") {
	"""退款总笔数"""
	refunOrderCount:Int!
	"""退款总额"""
	totalAmountCount:Double!
	"""补考总笔数"""
	totalMakeUpCount:Double!
	"""补考总额"""
	totalMakeUpAmountCount:Double!
}
"""lazy订单的分页对象
	<AUTHOR> create 2020/3/17 14:08
"""
type OrderDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.OrderDTO") {
	"""订单所属平台ID"""
	platformId:String
	"""订单所属平台版本ID"""
	platformVersionId:String
	"""订单所属项目ID"""
	projectId:String
	"""订单所属子项目ID"""
	subProjectId:String
	"""订单所属组织机构ID"""
	organizationId:String
	"""机构ID"""
	trainingInstitutionId:String
	"""机构名称"""
	trainingInstitutionName:String
	"""课件供应商id"""
	coursewareSupplierId:String
	"""课件供应商名称"""
	coursewareSupplierName:String
	"""渠道商id"""
	channelVendorId:String
	"""渠道商名称"""
	channelVendorName:String
	"""参训单位ID"""
	participatingUnitId:String
	"""参训单位名称"""
	participatingUnitName:String
	"""订单号"""
	orderNo:String
	"""子订单对象"""
	subOrderList:[SubOrderDTO]
	"""购买渠道code
		自主报名、集体缴费、导入开通
		@see PurchaseChannelTypes
		用户自主购买  1;
		集体缴费  2;
		管理员导入  3;
	"""
	purchaseChannelCode:Int!
	"""报名渠道（终端）code
		web、小程序、公众号
		@see PurchaseChannelTerminalCodes
		/**
		Web端 "Web";
		IOS端 "IOS";
		安卓端  "Android";
		微信小程序  "WechatMini";
		微信公众号  "WechatOfficial";
	"""
	placeChannelCode:String
	"""订单总金额"""
	totalAmount:BigDecimal
	"""主订单状态
		"等待付款", 1
		"等待卖家确认款项", 2
		"支付中", 8
		"支付成功", 3
		"发货中", 4
		"发货部分失败", 9
		"发货失败",10
		"发货已完成", 5
		"交易成功", 6
		"交易关闭", 7
	"""
	status:Int!
	"""订单应用类型
		@see OrderTypes
		1:普通订单,2:批次单
	"""
	applyType:Int!
	"""交易关闭类型
		@see OrderClosedTypes
		未关闭 0;
		买家取消 1;
		买家取消 2;
		超时取消 3;
		批次关联取消 4;
	"""
	closedType:Int!
	"""交易关闭原因ID"""
	reasonId:String
	"""交易关闭原因说明"""
	reason:String
	"""失败信息"""
	failMessage:String
	"""订单创建时间, yyyy-mm-dd 24hh:MM:dd"""
	createTime:DateTime
	"""订单发货时间 yyyy-mm-dd 24hh:MM:dd"""
	deliverTime:DateTime
	"""订单发货完成时间 yyyy-mm-dd 24hh:MM:dd"""
	deliveredTime:DateTime
	"""完成交易的时间, yyyy-mm-dd 24hh:MM:dd"""
	completeTime:DateTime
	"""是否为补考"""
	makeUpExam:Boolean!
	"""批次单号"""
	batchNo:String
	"""集体缴费单位id"""
	batchUnitId:String
	"""集体缴费单位名称"""
	batchUnitName:String
	"""交易流水号"""
	payFlowNo:String
	"""收款账号id"""
	receiveAccountId:String
	"""支付方式
		支付方式 1：线上支付 2：线下支付,-1:表示未支付
		@see PayType
	"""
	payType:Int!
	"""支付渠道Id 微信支付、支付宝支付"""
	payChannelId:String
	"""支付渠道名称 微信支付、支付宝支付"""
	payChannelName:String
	"""发券机构（属地人社局）（培训券的信息）"""
	publishOrgName:String
	"""培训券号"""
	couponCode:String
	"""主订单实付总价"""
	orderTotalPayAmount:BigDecimal
	"""付款时间, yyyy-mm-dd 24hh:MM:dd"""
	payTime:DateTime
	"""付款完成时间, yyyy-mm-dd 24hh:MM:dd"""
	payFinishTime:DateTime
	"""卖家id"""
	sellerId:String
	"""卖家名称"""
	sellerName:String
	"""买家id"""
	buyerId:String
	"""买家姓名"""
	buyerName:String
	"""买家电话号码"""
	buyerPhoneNumber:String
	"""买家身份证号码"""
	buyerIdentity:String
	"""买家登录账户"""
	buyerLoginInput:String
	"""买家所属单位id"""
	buyerBelongUnitId:String
	"""买家所属单位名称"""
	buyerBelongUnitName:String
	"""退款订单号"""
	refundOrderNo:String
	"""退款类型"""
	refundType:String
	"""退款申请人id"""
	refundApplyUserId:String
	"""退款申请时间"""
	refundApplyTime:DateTime
	"""退款原因id"""
	refundReasonId:String
	"""退款总金额"""
	refundTotalAmount:BigDecimal
	"""退款方式 1.线上 2.线下"""
	refundWay:Int!
	"""退款状态 1.已申请   2.退款成功 3.退款失败
		与底层状态不同，只关心最终状态。
		如果是拒绝退款就直接清空退款的数据了
	"""
	refundStatus:Int!
	"""退款完成时间"""
	refundCompleteTime:DateTime
	"""是否需要发票"""
	needBill:Boolean!
	"""主键 发票ID"""
	billId:String
	"""发票号"""
	billNo:String
	"""是否电子票"""
	electron:Boolean!
	"""发票抬头"""
	title:String
	"""统一社会信用代码"""
	taxpayerNo:String
	"""开户行"""
	billBankName:String
	"""账号"""
	billAccount:String
	"""地址"""
	billAddress:String
	"""电话"""
	billPhone:String
	"""是否非税发票"""
	noTaxBill:Boolean!
	"""发票状态"""
	billStatus:Int!
	"""红票状态"""
	redBillStatus:Int!
	"""本条记录的创建时间"""
	recordCreateTime:DateTime
	"""本条记录的最后更新时间"""
	recordLastUpdateTime:DateTime
	"""是否是测试数据"""
	test:Boolean!
}
"""订单统计信息对象
	<AUTHOR> create 2020/3/13 9:27
"""
type OrderStatisticsDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.OrderStatisticsDTO") {
	"""订单成交笔数"""
	orderCount:Int!
	"""订单成交总额"""
	totalAmount:Double!
	"""补考成交总额"""
	makeUpExamAmount:Double!
	"""补考成交总额"""
	makeUpExamCount:Int!
}
"""方案详情对象"""
type PreExamLSInfoDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.PreExamLSInfoDTO") {
	"""方案id"""
	schemeId:String
	"""培训方案名称"""
	name:String
	"""封面图片地址"""
	picture:String
	"""培训方案状态
		@see TrainingConfigStatusConst
	"""
	status:Int!
	"""发布时间"""
	publishTime:DateTime
	"""创建人ID"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""方案推荐指数"""
	recommendIndex:Double!
	"""课程学习方式信息
		分页接口不返回
	"""
	courseLearning:CourseLearningResponse
	"""考试学习方式信息
		分页接口不返回
	"""
	examLearning:ExamLearningResponse
	"""试题练习学习方式信息
		分页接口不返回
	"""
	questionPracticeLearning:QuestionPracticeLearningDto
	"""培训班成果设置
		分页接口不返回
	"""
	achieveSetting:IssueClassLSAchieveSettingResponse
	"""方案列表的讲师信息 一般是方案上外挂的讲师。
		详情接口暂时不返回
	"""
	teachers:[TeacherSimpleDTO]
	"""销售价格"""
	price:BigDecimal
	"""商品skuid"""
	commoditySkuId:String
	"""唯一的期数的id"""
	issueId:String
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""退款分页信息对象
	<AUTHOR> create 2020/4/14 11:45
"""
type RefundOrderDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.RefundOrderDTO") {
	"""退款订单号"""
	refundOrderNo:String
	"""退款对应的主订单号"""
	orderNo:String
	"""退款对应的子订单号"""
	subOrderNo:String
	"""集体缴费批次号"""
	batchNo:String
	"""集体缴费单位id"""
	batchUnitId:String
	"""集体缴费单位名称"""
	batchUnitName:String
	"""交易渠道信息   web、小程序、公众号"""
	placeChannel:String
	"""订单总金额"""
	totalAmount:BigDecimal
	"""主订单实付总价"""
	orderTotalPayAmount:BigDecimal
	"""培训学时"""
	trainingHours:String
	"""卖家id"""
	sellerId:String
	"""卖家名称"""
	sellerName:String
	"""买家id"""
	buyerId:String
	"""买家姓名"""
	buyerName:String
	"""买家电话号码"""
	buyerPhoneNumber:String
	"""买家身份证号码"""
	buyerIdentity:String
	"""买家登录账户"""
	buyerLoginInput:String
	"""买家所属单位id"""
	buyerBelongUnitId:String
	"""买家所属单位名称"""
	buyerBelongUnitName:String
	"""退款商品信息"""
	commodityInfoDTO:CommodityInfoDTO
	"""创建方式
		1:系统创建
		2:用户创建
		3:管理员创建
		4:历史迁移
		5:外部接口
	"""
	createType:Int!
	"""商品标价"""
	subOrderLabelPrice:BigDecimal
	"""商品购买数量"""
	purchaseQuantity:Int!
	"""银行交易流水号"""
	bankTrasactionNumber:String
	"""退款单是否补考"""
	makeUpExam:Boolean!
	"""退款对应订单的单位id"""
	orderUnitId:String
	"""退款总金额"""
	refundTotalAmount:BigDecimal
	"""退款订单状态"""
	refundStatus:Int!
	"""退款方式 1.线上 2.线下"""
	refundWay:Int!
	"""退款模式 1：普通退款 2：强制退款"""
	refundMode:Int
	"""退款类型"""
	refundType:Int!
	"""申请人id"""
	applyUserId:String
	"""申请人姓名"""
	applyUserName:String
	"""退款原因id"""
	refundReasonId:String
	"""退款申请时间"""
	applyTime:DateTime
	"""退款申请审核人"""
	auditUserId:String
	"""审核人姓名"""
	auditUserName:String
	"""退款申请审核时间"""
	auditTime:DateTime
	"""取消退款人id"""
	cancelUserId:String
	"""取消退款人姓名"""
	cancelUserName:String
	"""取消退款时间"""
	cancelTime:DateTime
	"""资源回收时间"""
	recycledTime:DateTime
	"""财务退款审核人id"""
	financeAuditUserId:String
	"""财务退款审核人名称"""
	financeAuditUserName:String
	"""财务审核同意/拒绝放款时间"""
	financeAuditTime:DateTime
	"""退款完成时间"""
	refundCompleteTime:DateTime
	"""是否是测试数据"""
	test:Boolean
}
"""退款详细
	<AUTHOR> create 2020/4/14 14:07
"""
type RefundOrderDetailsDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.RefundOrderDetailsDTO") {
	"""退款原因"""
	reason:String
	"""申请退款描述"""
	description:String
	"""拒绝申请原因描述"""
	refuseApplyDesc:String
	"""拒绝退款原因描述"""
	refuseRefundDesc:String
	"""退款订单号"""
	refundOrderNo:String
	"""退款对应的主订单号"""
	orderNo:String
	"""退款对应的子订单号"""
	subOrderNo:String
	"""集体缴费批次号"""
	batchNo:String
	"""集体缴费单位id"""
	batchUnitId:String
	"""集体缴费单位名称"""
	batchUnitName:String
	"""交易渠道信息   web、小程序、公众号"""
	placeChannel:String
	"""订单总金额"""
	totalAmount:BigDecimal
	"""主订单实付总价"""
	orderTotalPayAmount:BigDecimal
	"""培训学时"""
	trainingHours:String
	"""卖家id"""
	sellerId:String
	"""卖家名称"""
	sellerName:String
	"""买家id"""
	buyerId:String
	"""买家姓名"""
	buyerName:String
	"""买家电话号码"""
	buyerPhoneNumber:String
	"""买家身份证号码"""
	buyerIdentity:String
	"""买家登录账户"""
	buyerLoginInput:String
	"""买家所属单位id"""
	buyerBelongUnitId:String
	"""买家所属单位名称"""
	buyerBelongUnitName:String
	"""退款商品信息"""
	commodityInfoDTO:CommodityInfoDTO
	"""创建方式
		1:系统创建
		2:用户创建
		3:管理员创建
		4:历史迁移
		5:外部接口
	"""
	createType:Int!
	"""商品标价"""
	subOrderLabelPrice:BigDecimal
	"""商品购买数量"""
	purchaseQuantity:Int!
	"""银行交易流水号"""
	bankTrasactionNumber:String
	"""退款单是否补考"""
	makeUpExam:Boolean!
	"""退款对应订单的单位id"""
	orderUnitId:String
	"""退款总金额"""
	refundTotalAmount:BigDecimal
	"""退款订单状态"""
	refundStatus:Int!
	"""退款方式 1.线上 2.线下"""
	refundWay:Int!
	"""退款模式 1：普通退款 2：强制退款"""
	refundMode:Int
	"""退款类型"""
	refundType:Int!
	"""申请人id"""
	applyUserId:String
	"""申请人姓名"""
	applyUserName:String
	"""退款原因id"""
	refundReasonId:String
	"""退款申请时间"""
	applyTime:DateTime
	"""退款申请审核人"""
	auditUserId:String
	"""审核人姓名"""
	auditUserName:String
	"""退款申请审核时间"""
	auditTime:DateTime
	"""取消退款人id"""
	cancelUserId:String
	"""取消退款人姓名"""
	cancelUserName:String
	"""取消退款时间"""
	cancelTime:DateTime
	"""资源回收时间"""
	recycledTime:DateTime
	"""财务退款审核人id"""
	financeAuditUserId:String
	"""财务退款审核人名称"""
	financeAuditUserName:String
	"""财务审核同意/拒绝放款时间"""
	financeAuditTime:DateTime
	"""退款完成时间"""
	refundCompleteTime:DateTime
	"""是否是测试数据"""
	test:Boolean
}
"""子订单对象
	<AUTHOR> create 2020/3/17 14:09
"""
type SubOrderDTO @type(value:"com.fjhb.btpx.integrative.service.trade.dto.response.SubOrderDTO") {
	"""子订单号"""
	subOrderNo:String
	"""成交单价"""
	subOrderDealPrice:BigDecimal
	"""子订单实付总价"""
	subOrderTotalPayAmount:BigDecimal
	"""购买数量"""
	subOrderPurchaseQuantity:Int!
	"""子订单状态
		1 - 待付款
		2 - 未发货
		3 - 发货中
		4 - 已发货
		5 - 买家已签收
		6 - 已换货
		7 - 退货中
		8 - 已退货
		9 - 已取消
		@see SubOrderStatus
	"""
	subOrderStatus:Int!
	"""子订单发货的时间, yyyy-mm-dd 24hh:MM:dd"""
	subOrderDeliverTime:DateTime
	"""子订单发货完成时间, yyyy-mm-dd 24hh:MM:dd"""
	subOrderDeliveredTime:DateTime
	"""完成交易的时间, yyyy-mm-dd 24hh:MM:dd"""
	subOrderCompleteTime:DateTime
	"""子订单最后一次状态变更的时间"""
	lastUpdateTime:DateTime
	"""商品id"""
	productId:String
	"""商品标价"""
	subOrderLabelPrice:BigDecimal
	"""所属培训方案id"""
	schemeId:String
	"""方案展示图片"""
	schemePicture:String
	"""所属培训方案名称"""
	schemeName:String
	"""所属期别(期数)名称"""
	issueTitle:String
	"""培训学时"""
	trainingHours:String
	"""商品创建人id"""
	productCreateUserId:String
	"""所属期别id"""
	stageId:String
	"""所属期数id"""
	issueId:String
	"""期数开始时间"""
	issueStartTime:DateTime
	"""期数结束时间"""
	issueEndTime:DateTime
	"""期数线下考试时间"""
	issueOfflineExamTime:DateTime
	"""适用人群"""
	suitableCrowNames:[String]
	"""培训类别Id path"""
	trainingTypeIdPath:String
	"""培训类别"""
	trainingTypeName:String
	"""培训类别名称/name1/name2/name3"""
	trainingTypeNamePath:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeName:String
	"""培训工种类别/培训工种id path
		用于培训类别联合工种多条件查询
	"""
	workTypeIdPath:String
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
type LibraryWaySettingDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.LibraryWaySettingDto") {
	libraryIds:[String]
	recursive:Boolean!
}
type PackageRuleSettingDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.PackageRuleSettingDto") {
	packageId:String
	limit:Boolean!
	maxPeriod:Double
}
type QuestionPracticeLearningDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.QuestionPracticeLearningDto") {
	learningId:String
	enabled:Boolean!
	fetchWay:Int!
	libraryWaySetting:LibraryWaySettingDto
	tagsWaySetting:TagWaySettingDto
}
type TagWaySettingDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.TagWaySettingDto") {
	tagIds:[String]
}
type ChapterDTO @type(value:"com.fjhb.platform.core.v1.knowledge.api.dto.ChapterDTO") {
	relationId:String
	parentRelationId:String
	sort:Long
	id:String
	type:String
	name:String
	code:String
	enabled:Boolean
}
type IndustryDTO @type(value:"com.fjhb.platform.core.v1.knowledge.api.dto.IndustryDTO") {
	relationId:String
	majorModelList:[MajorDTO]
	id:String
	type:String
	name:String
	code:String
	enabled:Boolean
}
type MajorDTO @type(value:"com.fjhb.platform.core.v1.knowledge.api.dto.MajorDTO") {
	relationId:String
	sort:Long
	id:String
	type:String
	name:String
	code:String
	enabled:Boolean
}
type MajorMapChapterListDTO @type(value:"com.fjhb.platform.core.v1.knowledge.api.dto.MajorMapChapterListDTO") {
	majorRelationId:String
	chapterList:[ChapterDTO]
}

scalar List
type BatchOrderDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BatchOrderDTO]}
type BatchRefundOrderDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BatchRefundOrderDTO]}
type OrderDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [OrderDTO]}
type PreExamLSInfoDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [PreExamLSInfoDTO]}
type RefundOrderDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [RefundOrderDTO]}
