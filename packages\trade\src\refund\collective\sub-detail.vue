<route-params content="/:id/:subId"></route-params>
<route-meta>
{
"title": "集体退款详情"
}
</route-meta>
<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/training/trade/refund/collective' }">集体报名退款</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/training/trade/refund/collective/detail/' + batchOrderNo }"
        >退款订单</el-breadcrumb-item
      >
      <el-breadcrumb-item>详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <!--退款审批中    refoundStatus==1,2 -->
      <el-card
        shadow="never"
        class="m-card is-header m-order-state"
        v-if="refundDetailList.refoundStatus === 0 || refundDetailList.refoundStatus === 1"
      >
        <div class="info">
          <p>退款单号：{{ $route.params.subId }}</p>
          <p class="state f-cb">退款审批中</p>
          <!-- 退款审批中👇 -->
          <!-- <div class="op f-mt15">
            <el-button type="warning" @click="openCancelRefund()" size="mini" plain>取消退款</el-button>
            <el-button type="warning" @click="openRefuseRefund" size="mini" plain>拒绝退款</el-button>
            <el-button type="warning" size="mini" @click="agreeRefund" plain>同意退款</el-button>
          </div> -->
        </div>
        <el-steps :active="1" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundList.basicData.returnOrderStatusChangeTime.applied"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step title="审批通过" icon="hb-iconfont icon-success"></el-step>
          <el-step title="退款成功" icon="hb-iconfont icon-s-pay"></el-step>
        </el-steps>
      </el-card>
      <!--退款被拒绝-->
      <el-card
        shadow="never"
        class="m-card is-header m-order-state"
        v-if="refundDetailList.refoundStatus === 12 && refundDetailList.closeType === 2"
      >
        <div class="info">
          <p>退款单号：{{ $route.params.subId }}</p>
          <p class="state f-c9">退款被拒绝</p>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundList.basicData.returnOrderStatusChangeTime.applied"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="退款被拒绝"
            :description="refundList.basicData.returnOrderStatusChangeTime.closed"
            icon="hb-iconfont icon-s-close"
          ></el-step>
        </el-steps>
      </el-card>
      <!--资源回收中-->
      <el-card shadow="never" class="m-card is-header m-order-state" v-if="refundDetailList.refoundStatus === 3">
        <div class="info">
          <p>退款单号：{{ $route.params.subId }}</p>
          <p class="state f-cb">资源回收中</p>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundList.basicData.returnOrderStatusChangeTime.applied"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="审批通过"
            :description="refundList.basicData.returnOrderStatusChangeTime.returning"
            icon="hb-iconfont icon-success"
          ></el-step>
          <el-step title="退款成功" icon="hb-iconfont icon-s-pay"></el-step>
        </el-steps>
      </el-card>
      <!--资源回收失败-->
      <el-card shadow="never" class="m-card is-header m-order-state" v-if="refundDetailList.refoundStatus === 4">
        <div class="info">
          <p>退款单号：{{ $route.params.subId }}</p>
          <p class="state f-cr">资源回收失败</p>
          <div class="op f-mt15">
            <!-- <el-button type="warning" size="mini" @click="retryRecycleRefund" plain>重新回收资源</el-button> -->
          </div>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundList.basicData.returnOrderStatusChangeTime.applied"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="审批通过"
            :description="refundList.basicData.returnOrderStatusChangeTime.returning"
            icon="hb-iconfont icon-success"
          ></el-step>
          <el-step title="退款成功" icon="hb-iconfont icon-s-pay"></el-step>
        </el-steps>
      </el-card>
      <!--退款处理中-->
      <el-card
        shadow="never"
        class="m-card is-header m-order-state"
        v-if="
          refundDetailList.refoundStatus === 5 ||
          refundDetailList.refoundStatus === 6 ||
          refundDetailList.refoundStatus === 7
        "
      >
        <div class="info">
          <p>退款单号：{{ $route.params.subId }}</p>
          <p class="state f-cb">退款处理中</p>
          <div class="op f-mt15">
            <!-- <el-button type="warning" size="mini" @click="confirmRefund" plain>确认退款</el-button> -->
          </div>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundList.basicData.returnOrderStatusChangeTime.applied"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="审批通过"
            :description="refundList.basicData.returnOrderStatusChangeTime.returning"
            icon="hb-iconfont icon-success"
          ></el-step>
          <el-step title="退款成功" icon="hb-iconfont icon-s-pay"></el-step>
        </el-steps>
      </el-card>

      <!--退款失败-->
      <el-card shadow="never" class="m-card is-header m-order-state" v-if="refundDetailList.refoundStatus === 8">
        <div class="info">
          <p>退款单号：{{ $route.params.subId }}</p>
          <p class="state f-cr">退款失败</p>
          <div class="op f-mt15">
            <!-- <el-button type="warning" size="mini" @click="continueRefund" plain>继续退款</el-button> -->
          </div>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundList.basicData.returnOrderStatusChangeTime.applied"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="审批通过"
            :description="refundList.basicData.returnOrderStatusChangeTime.returning"
            icon="hb-iconfont icon-success"
          ></el-step>
          <el-step title="退款成功" icon="hb-iconfont icon-s-pay"></el-step>
        </el-steps>
      </el-card>
      <!--退款成功-->
      <el-card
        shadow="never"
        class="m-card is-header m-order-state"
        v-if="
          refundDetailList.refoundStatus === 9 ||
          refundDetailList.refoundStatus === 10 ||
          refundDetailList.refoundStatus === 11
        "
      >
        <div class="info">
          <p>退款单号：{{ $route.params.subId }}</p>
          <p class="state f-cg">退款成功</p>
        </div>
        <el-steps :active="3" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundList.basicData.returnOrderStatusChangeTime.applied"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="审批通过"
            :description="refundList.basicData.returnOrderStatusChangeTime.returning"
            icon="hb-iconfont icon-success"
          ></el-step>
          <el-step title="退款成功" :description="successTime" icon="hb-iconfont icon-s-pay"></el-step>
        </el-steps>
      </el-card>
      <!--退款已取消-->
      <el-card
        shadow="never"
        class="m-card is-header m-order-state"
        v-if="
          refundDetailList.refoundStatus === 12 &&
          (refundDetailList.closeType === 1 || refundDetailList.closeType === 3 || refundDetailList.closeType === 4)
        "
      >
        <div class="info">
          <p>退款单号：{{ $route.params.subId }}</p>
          <p class="state f-c9">退款已取消</p>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step
            title="退款审批"
            :description="refundList.basicData.returnOrderStatusChangeTime.applied"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="退款取消"
            :description="refundDetailList.cancelDate"
            icon="hb-iconfont icon-s-close"
          ></el-step>
        </el-steps>
      </el-card>
      <!--订单信息-->

      <el-card shadow="never" class="m-card is-header m-order-info f-mb15">
        <div class="f-flex-sub f-plr20 f-pt10">
          <div class="m-tit">
            <span class="tit-txt">退款信息</span>
          </div>
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="12">
              <el-form-item label="退款类型：">
                <!-- {{refundDetailList.refoundType == 1
                  ? '仅退货'
                  : refundList.basicData.returnOrderType == 2
                  ? '仅退款'
                  : '退货并退款'
              }} -->
                退货退款
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属报名批次号：">{{ refundDetailList.batchOrderNo }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款金额："
                >￥{{
                  refundList.returnCommodity.quantity * refundList.returnCommodity.commoditySku.price
                }}</el-form-item
              >
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款方式：">{{
                refundDetailList.refoundWay === 1 ? '线上退款' : '线下退款'
              }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款原因：">{{ refundDetailList.refoundReason || '-' }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款说明：">{{ refundDetailList.refoundExplain || '-' }}</el-form-item>
            </el-col>
          </el-form>
        </div>
        <div class="right f-plr20 f-ptb10">
          <div class="m-tit">
            <span class="tit-txt">退款单号</span>
          </div>
          <el-form label-width="auto" class="m-text-form is-column f-pt10 f-pl20">
            <el-form-item label="退款单号：">{{ $route.params.subId || '-' }}</el-form-item>
          </el-form>
          <el-form label-width="auto" class="m-text-form is-column f-pt10 f-pl20">
            <el-form-item label="集体报名批次号：">
              <span class="f-cb f-link f-underline" @click="toOrderDetail">
                {{ refundDetailList.batchOrderNo || '-' }}
              </span>
            </el-form-item>
          </el-form>
          <div class="m-tit">
            <span class="tit-txt">退款操作记录</span>
          </div>
          <div class="f-pl20" v-for="(item, index) in refundList.records" :key="index">
            <p>
              <span class="f-fb" v-if="item.UIReturnOrderStatue !== 4">{{ item.name || '-' }}在</span>

              <span class="f-fb">{{ item.time || '-' }}</span>
              {{
                item.UIReturnOrderStatue === 0
                  ? '发起退款申请'
                  : item.UIReturnOrderStatue === 1
                  ? '同意退款申请'
                  : item.UIReturnOrderStatue === 2
                  ? '拒绝退款申请'
                  : item.UIReturnOrderStatue === 3
                  ? '发起“取消退款”操作'
                  : '退款成功'
              }}
            </p>
            <el-form label-width="auto" class="m-text-form is-column f-pt10 f-pb20">
              <el-form-item label="退款金额：" v-if="item.UIReturnOrderStatue === 0">¥ {{ item.money }}</el-form-item>
              <el-form-item label="退款状态：" v-if="item.UIReturnOrderStatue === 0">{{
                statusMapType[item.UIReturnOrderStatue] || '-'
              }}</el-form-item>
              <el-form-item label="退款说明：" v-if="item.UIReturnOrderStatue === 0">{{
                item.tipMsg || '-'
              }}</el-form-item>
              <el-form-item label="拒绝原因：" v-if="item.UIReturnOrderStatue === 2">{{
                item.tipMsg || '-'
              }}</el-form-item>
              <el-form-item label="取消原因：" v-if="item.UIReturnOrderStatue === 3">{{
                item.tipMsg || '-'
              }}</el-form-item>
            </el-form>
          </div>
        </div>
      </el-card>
      <!--商品信息-->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="f-plr20 f-pt10">
          <div class="m-tit is-small">
            <span class="tit-txt">商品信息</span>
          </div>
          <el-form label-width="auto" class="m-text-form f-pt10 f-pb5">
            <el-col :span="8">
              <el-form-item label="购买人：">{{ refundList.buyer.userName || '-' }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证件号：">{{ refundList.buyer.idCard || '-' }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="手机号：">{{ refundList.buyer.phone || '-' }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单号：">
                <span @click="toOrder(refundList.subOrderInfo.orderInfo.orderNo)" class="f-cb f-link f-underline">{{
                  refundList.subOrderInfo.orderInfo.orderNo || '-'
                }}</span>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="退款人次：">{{ refundDetailList.refoundCount || 0 }}</el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="交易流水号：">{{ refundDetailList.flowNo || '-' }}</el-form-item>
            </el-col>
          </el-form>
          <el-table :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="物品名称" min-width="300">
              <template>
                <div v-if="!refundList.subOrderInfo.exchanged">
                  <p>{{ refundList.returnCommodity.commoditySku.saleTitle || '-' }}</p>
                  <p v-if="refundList.returnCommodity.commoditySku.issueInfo">
                    <el-tag type="info" size="mini">培训期别</el-tag
                    >{{ refundList.returnCommodity.commoditySku.issueInfo.issueName }}
                  </p>
                </div>
                <div v-else>
                  <div class="f-flex f-align-start">
                    <el-tag type="danger" size="mini">初始物品</el-tag>
                    <div>
                      <p>{{ refundList.refundCommodity.commoditySku.saleTitle || '-' }}</p>
                      <p v-if="refundList.refundCommodity.commoditySku.issueInfo">
                        <el-tag type="info" size="mini">培训期别</el-tag
                        >{{ refundList.refundCommodity.commoditySku.issueInfo.issueName }}
                      </p>
                    </div>
                  </div>
                  <div class="f-flex f-align-start">
                    <el-tag type="danger" size="mini">最新物品</el-tag>
                    <div>
                      <p>{{ refundList.returnCommodity.commoditySku.saleTitle || '-' }}</p>
                      <p v-if="refundList.returnCommodity.commoditySku.issueInfo">
                        <el-tag type="info" size="mini">培训期别</el-tag
                        >{{ refundList.returnCommodity.commoditySku.issueInfo.issueName }}
                      </p>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="学时" min-width="150" align="center">
              <template>{{ refundList.returnCommodity.commoditySku.resource.period || '-' }}</template>
            </el-table-column>
            <el-table-column label="数量" min-width="150" align="center">
              <template>{{ refundList.returnCommodity.quantity }}</template>
            </el-table-column>
            <el-table-column label="单价(元)" min-width="150" align="right">
              <template>{{ refundList.returnCommodity.commoditySku.price }}</template>
            </el-table-column>
            <el-table-column label="实付金额(元)" min-width="150" align="right">
              <template>{{
                refundList.returnCommodity.quantity * refundList.returnCommodity.commoditySku.price
              }}</template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { RefundOrderStatusEnum } from '@api/service/common/trade/RefundOrderStatusEnum'
  import ReturnOrderResponseVo, {
    ReturnCommodityResponse
  } from '@api/service/management/trade/single/order/query/vo/ReturnOrderResponseVo'
  import MutationBatchOrderRefund from '@api/service/management/trade/batch/order/mutation/MutationBatchOrderRefund'
  import QueryBatchRefundList from '@api/service/management/trade/batch/order/query/QueryBatchRefund'
  import BatchRefoundDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchRefoundDetailVo'
  import { BatchRefundTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
  import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
  import QueryRefundList from '@api/service/management/trade/single/order/query/QueryRefundList'
  import QueryRefundList1 from '@api/service/management/trade/single/order/query/QueryRefundDetail'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  @Component
  export default class extends Vue {
    tableData: Array<ReturnCommodityResponse> = new Array<ReturnCommodityResponse>()
    cancelRefundDialog = false //取消退款弹窗标识
    refuseRefundDialog = false //拒绝退款弹窗标识
    // 页面分页控件
    page: UiPage
    // 分页查询
    query: Query = new Query()
    form = {
      data1: ''
    }
    BatchRefundTradeStatusEnum = BatchRefundTradeStatusEnum
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    // 退款状态
    statusMapType = {
      [RefundOrderStatusEnum.RefundOrderStatusEnumApproving]: '退款审批中',
      [RefundOrderStatusEnum.RefundOrderStatusEnumHandling]: '退款处理中',
      [RefundOrderStatusEnum.RefundOrderStatusEnumRefuse]: '退款拒绝',
      [RefundOrderStatusEnum.RefundOrderStatusEnumCancel]: '退款取消',
      [RefundOrderStatusEnum.RefundOrderStatusEnumSuccess]: '退款成功',
      [RefundOrderStatusEnum.RefundOrderStatusEnumFail]: '退款失败'
    }
    cancelReason = ''

    //查询接口请求
    queryBatchRefound: QueryBatchRefundList =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchRefound
    //业务接口请求
    mutationBatchOrderRefund: MutationBatchOrderRefund =
      TradeModule.batchTradeBatchFactor.orderFactor.mutationOrderFactor.mutationBatchOrderRefund
    //退款单详情对象
    refundList: ReturnOrderResponseVo = new ReturnOrderResponseVo()
    refundDetailList: BatchRefoundDetailVo = new BatchRefoundDetailVo()
    batchOrderNo = ''
    //查询接口请求
    queryRefundOrder: QueryRefundList = TradeModule.singleTradeBatchFactor.orderFactor.getQueryRefundOrder()
    //接口查询参数
    returnOrderRequestVo: ReturnOrderRequestVo = new ReturnOrderRequestVo()
    //查询接口结果
    returnOrderResponseVo: Array<ReturnOrderResponseVo> = new Array<ReturnOrderResponseVo>()
    //接口请求
    queryRefundOrderDetail: QueryRefundList1 =
      TradeModule.singleTradeBatchFactor.orderFactor.getQueryRefundOrderDetail()
    /*
     * 跳转订单详情页
     * */
    toOrder(id: string) {
      this.$router.push(
        '/training/trade/order/collective/sub-detail/' +
          this.refundDetailList.batchOrderNo +
          '/' +
          this.refundList.subOrderInfo.orderInfo.orderNo
      )
    }

    async created() {
      this.batchOrderNo = this.$route.params.id
      this.queryRefundOrderDetail.returnOrderNo = this.$route.params.subId
      await this.geteRfundOrderDetail()
      await this.getCommodityInformation()
    }
    /*
     * 退款详情
     * */
    async geteRfundOrderDetail() {
      try {
        if (this.isFxlogin && this.isHadFxAbility) {
          this.refundDetailList = await this.queryBatchRefound.queryFxBatchRefoundDetail(this.batchOrderNo)
        } else {
          this.refundDetailList = await this.queryBatchRefound.queryBatchRefoundDetail(this.batchOrderNo)
        }
      } catch (e) {
        console.log(e, '加载个人报名退款订单详情页失败')
      } finally {
        console.log(this.refundDetailList, 'this.refundDetailList')
      }
    }
    /*
     * 查看子单
     * */
    toDetail(subId: string) {
      this.$router.push('/training/trade/refund/collective/sub-detail/' + this.batchOrderNo + '/' + subId)
    }
    toOrderDetail() {
      this.$router.push('/training/trade/order/collective/detail/' + this.refundDetailList.batchOrderNo)
    }
    /*
     * 商品信息
     * */
    async getCommodityInformation() {
      try {
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.queryRefundOrderDetail.queryFxRefundOrderDetail()
        } else {
          await this.queryRefundOrderDetail.queryRefundOrderDetail()
        }
      } catch (e) {
        console.log(e, '加载商品信息失败')
      } finally {
        this.refundList = this.queryRefundOrderDetail.returnOrderDetail
        this.tableData = [this.refundList.returnCommodity]
        console.log(this.refundList, 'this.refundList')
      }
    }

    get successTime() {
      switch (this.refundList?.basicData?.returnOrderType) {
        case 1:
          return this.refundList.basicData.returnOrderStatusChangeTime.returnCompleted || ''
        case 2:
          return this.refundList.basicData.returnOrderStatusChangeTime.refunded || ''
        case 3:
          return this.refundList.basicData.returnOrderStatusChangeTime.returnedAndRefunded || ''
        default:
          return ''
      }
    }
  }
</script>

<style scoped></style>
