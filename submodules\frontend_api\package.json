{"name": "@hbfe/jxjy_frontend_api", "version": "30.0.0-SNAPSHOT.29", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "generator:schema": "node node_modules/@hbfe/graphql2typescript/src/bin/index.js PlatformCommodity", "generator:schema:path": "node node_modules/@hbfe/graphql2typescript/src/bin/generateWithPath.js --config src/schemas.json --serviceCapability fx --target src/ms-gateway", "generator:schema:platform:path": "node node_modules/@hbfe/graphql2typescript/src/bin/generateWithPath.js --config src/platform-schemas.json --serviceCapability fx --target src/platform-gateway --remove", "generator:schema:diff:path": "node node_modules/@hbfe/graphql2typescript/src/bin/generateWithPath.js --config src/platform-schemas.json --target src/diff-gateway --remove", "分割-ms-start": "****************************", "generate:graphql:dev": "cross-env NODE_ENV=development node node_modules/@hbfe/graphql2typescript/src/manage-ms-dependencies.js --target src/schemas.json --source micro-service.json && npm run generator:schema:path", "generate:graphql:prod": "cross-env NODE_ENV=production node node_modules/@hbfe/graphql2typescript/src/manage-ms-dependencies.js --target src/schemas.json --source micro-service.json  && npm run generator:schema:path", "分割-ms-end": "****************************", "分割-platform-start": "console.log('生成项目依赖平台服务列表')", "generate:platform:graphql:dev": "cross-env NODE_ENV=development node node_modules/@hbfe/graphql2typescript/src/manage-ms-dependencies.js --target src/platform-schemas.json --source platform-service.json && npm run generator:schema:platform:path", "generate:platform:graphql:prod": "cross-env NODE_ENV=production node node_modules/@hbfe/graphql2typescript/src/manage-ms-dependencies.js --target src/platform-schemas.json --source platform-service.json  && npm run generator:schema:platform:path", "分割-platform-end": "****************************", "分割-diff-start": "****************************", "generate:diff:graphql:dev": "cross-env NODE_ENV=development node node_modules/@hbfe/graphql2typescript/src/manage-ms-dependencies.js --target src/platform-schemas.json --source diff-service.json && npm run generator:schema:diff:path", "generate:diff:graphql:prod": "cross-env NODE_ENV=production node node_modules/@hbfe/graphql2typescript/src/manage-ms-dependencies.js --target src/platform-schemas.json --source diff-service.json && npm run generator:schema:diff:path", "分割-diff-end": "****************************", "auto-scure": "cross-env API=src/module/customer,src/module/common node node_modules/@fjhb/fjhb-nodejs-plugin/src/auto-secure --grantRoles=user", "generate-constant-key:from-apollo": "node ./build/libs/apollo/gen-enum.from-config.js"}, "hb-scripts-desc": {"generate-constant-key:from-apollo": "从 apollo 读取当前 appkey 的所有配置，自动生成 enum 枚举类"}, "dependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@hbfe/common": "1.1.2", "@hbfe/eslint-plugin-rules": "1.0.0", "@hbfe/graphql2typescript": "1.4.1", "@types/events": "^3.0.0", "axios": "^0.19.2", "core-js": "^3.6.4", "cross-env": "^7.0.2", "eslint-formatter-pretty": "^3.0.1", "events": "^3.3.0", "got": "^11.8.2", "graphql": "^15.0.0", "graphql-tag": "^2.10.3", "handlebars": "^4.7.6", "husky": "^4.2.5", "metalsmith": "^2.3.0", "metalsmith-rename": "^1.0.0", "moment": "^2.24.0", "raw-loader": "^4.0.1", "vue": "^2.6.11", "vue-class-component": "^7.2.3", "vue-property-decorator": "^8.4.1", "vuex": "^3.1.3", "vuex-module-decorators": "^0.16.1"}, "devDependencies": {"@fjhb/fjhb-nodejs-plugin": "^1.0.9", "@types/chai": "^4.2.11", "@types/lodash": "^4.14.176", "@types/mocha": "^5.2.4", "@typescript-eslint/eslint-plugin": "^2.26.0", "@typescript-eslint/parser": "^2.26.0", "@vue/cli-plugin-babel": "^4.3.0", "@vue/cli-plugin-eslint": "^4.3.0", "@vue/cli-plugin-typescript": "^4.3.0", "@vue/cli-plugin-unit-mocha": "^4.3.0", "@vue/cli-plugin-vuex": "^4.3.0", "@vue/cli-service": "^4.3.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "@vue/test-utils": "1.0.0-beta.31", "chai": "^4.1.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-vue": "^6.2.2", "lint-staged": "^9.5.0", "prettier": "^1.19.1", "typescript": "^3.9.8", "vue-template-compiler": "^2.6.11"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"**/*.{js,jsx,vue,ts,tsx}": ["cross-env NODE_ENV=development vue-cli-service lint", "git add"]}, "types": "./src/shim.d.ts"}