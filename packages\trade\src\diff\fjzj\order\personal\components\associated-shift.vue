<template>
  <el-drawer title="关联售后信息" :visible.sync="isShow" size="1200px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="换班" name="first">
          <el-table
            stripe
            :data="exchangeCommodityList"
            max-height="500px"
            class="m-table"
            ref="exchangeCommodityListRef"
            v-loading="exchangeCommodityListLoading"
          >
            <el-table-column label="操作时间" min-width="180" fixed="left">
              <template #default="scope">{{ scope.row.basicData.statusChangeTime.applied }}</template>
            </el-table-column>
            <el-table-column label="初始班级" min-width="300" fixed="left">
              <template #default="scope">
                <!-- <el-popover placement="top-start" width="400" trigger="hover"> -->
                <div slot="reference">
                  <p>
                    <el-tag type="info" size="mini">培训班</el-tag
                    >{{ scope.row.originalCommodity.commoditySku.saleTitle }}
                  </p>

                  <p></p>
                  <p
                    v-show="
                      scope.row.originalCommodity.commoditySku.issueInfo &&
                      scope.row.originalCommodity.commoditySku.issueInfo.issueId
                    "
                  >
                    <el-tag type="info" size="mini">培训期别</el-tag
                    >{{
                      scope.row.originalCommodity.commoditySku.issueInfo
                        ? scope.row.originalCommodity.commoditySku.issueInfo.issueName
                        : ''
                    }}
                  </p>
                  <p v-if="exchangeCommodityList.length === 1 || scope.$index === exchangeCommodityList.length - 1">
                    <el-tag type="danger" size="mini">初始物品</el-tag>
                  </p>
                </div>
                <!-- </el-popover> -->
              </template>
            </el-table-column>
            <el-table-column label="新班级" min-width="300" fixed="left">
              <template #default="scope">
                <p>
                  <el-tag type="info" size="mini">培训班</el-tag>
                  {{ scope.row.exchangeCommodity.commoditySku.saleTitle }}
                </p>
                <p
                  v-show="
                    scope.row.originalCommodity.commoditySku.issueInfo &&
                    scope.row.originalCommodity.commoditySku.issueInfo.issueId
                  "
                >
                  <el-tag type="info" size="mini">培训期别</el-tag
                  >{{
                    scope.row.exchangeCommodity.commoditySku.issueInfo
                      ? scope.row.exchangeCommodity.commoditySku.issueInfo.issueName
                      : ''
                  }}
                </p>

                <p v-if="exchangeCommodityList.length === 1 || scope.$index === 0">
                  <el-tag type="danger" size="mini">最新物品</el-tag>
                </p>
              </template>
            </el-table-column>
            <el-table-column label="单价(元)" min-width="120" align="right">
              <template #default="scope">{{ scope.row.originalCommodity.commoditySku.price }}</template>
            </el-table-column>
            <el-table-column label="售后状态" min-width="120" align="center">
              <template #default="scope">
                <div v-if="scope.row.exchangeStatue == 1">
                  <el-tag type="success">换班成功</el-tag>
                  <p class="f-mt5">
                    <a
                      class="f-link f-underline f-cb f-f12"
                      @click="isShowChangeShiftsState(scope.row.subOrderInfo.subOrderNo)"
                      >查看详情</a
                    >
                  </p>
                </div>
                <div v-if="scope.row.exchangeStatue == 0">
                  <el-tag type="warning">换班中</el-tag>
                  <p class="f-mt5">
                    <a
                      class="f-link f-underline f-cb f-f12"
                      @click="isShowChangeShiftsState(scope.row.subOrderInfo.subOrderNo)"
                      >查看详情</a
                    >
                  </p>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作人" min-width="110">
              <template #default="scope">{{ scope.row.applyUserName || '-' }}</template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="换期" name="second" v-if="isOnlineClassSupport">
          <el-table
            stripe
            :data="exchangeRenewalList"
            max-height="500px"
            class="m-table"
            ref="exchangeRenewalListRef"
            v-loading="exchangeRenewalListLoading"
          >
            <el-table-column label="操作时间" min-width="180" fixed="left">
              <template #default="scope">{{ scope.row.operateTime }}</template>
            </el-table-column>
            <el-table-column label="初始期别" min-width="300">
              <template #default="scope">
                <!-- <el-popover placement="top-start" width="400" trigger="hover"> -->
                <div slot="reference">
                  <p>
                    <el-tag type="info" size="mini">培训期别</el-tag>{{ scope.row.originalIssueCommodity.issueName }}
                  </p>
                  <div class="f-to-two">
                    <el-tag type="info" size="mini">所属方案</el-tag>{{ scope.row.originalIssueCommodity.schemeName }}
                  </div>
                  <p v-if="exchangeRenewalList.length === 1 || scope.$index === exchangeRenewalList.length - 1">
                    <el-tag type="danger" size="mini">初始物品</el-tag>
                  </p>
                </div>
                <!-- </el-popover> -->
              </template>
            </el-table-column>
            <el-table-column label="新期别" min-width="300">
              <template #default="scope">
                <p><el-tag type="info" size="mini">培训期别</el-tag>{{ scope.row.exchangeIssueCommodity.issueName }}</p>
                <div class="f-to-two">
                  <el-tag type="info" size="mini">所属方案</el-tag>{{ scope.row.exchangeIssueCommodity.schemeName }}
                </div>
                <p v-if="exchangeRenewalList.length === 1 || scope.$index === 0">
                  <el-tag type="danger" size="mini">最新物品</el-tag>
                </p>
              </template>
            </el-table-column>
            <el-table-column label="操作类型" min-width="120" align="right">
              <template #default="scope">{{
                scope.row.operateType === ExchangeIssueOperateTypeEnum.cross_scheme ? '换班换期' : '班内换期'
              }}</template>
            </el-table-column>
            <el-table-column label="售后状态" min-width="120" align="center">
              <!-- <template #default="scope">
                <div v-if="scope.$index === 0"> -->
              <el-tag type="success">换期成功</el-tag>
              <!-- </div>
              </template> -->
            </el-table-column>
            <el-table-column label="操作人" min-width="110">
              <template #default="scope">{{ scope.row.operatorName || '-' }}</template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <change-shifts-detail
      ref="ChangeShiftsDetail"
      :changeShiftsDetailList="changeShiftsDetailList"
    ></change-shifts-detail>
  </el-drawer>
</template>

<script lang="ts">
  import { ExchangeOrderRecordVo } from '@api/service/management/trade/single/order/query/vo/ExchangeOrderRecordVo'
  import ExchangeOrderResponseVo from '@api/service/management/trade/single/order/query/vo/ExchangeOrderResponseVo'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { Component, Vue, Ref, Prop } from 'vue-property-decorator'
  import ChangeShiftsDetail from '@hbfe/jxjy-admin-trade/src/diff/fjzj/order/personal/components/change-shifts-detail.vue'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryExchangeCommodity from '@api/service/management/trade/single/order/query/QueryExchangeCommodity'
  import { UiPage } from '@hbfe/common'
  import ExchangeIssueDetail from '@api/service/management/trade/single/order/query/vo/ExchangeIssueDetail'
  import ExchangeIssueOperateType, {
    ExchangeIssueOperateTypeEnum
  } from '@api/service/common/enums/trade/ExchangeIssueOperateType'
  import { ElTable } from 'element-ui/types/table'
  import QueryShowOffline from '@api/service/common/config/QueryShowOffline'

  @Component({
    components: { ChangeShiftsDetail }
  })
  export default class extends Vue {
    @Prop({ type: Array, default: () => new Array<ExchangeOrderResponseVo>() })
    exchangeOrderList: Array<ExchangeOrderResponseVo>
    @Ref('ChangeShiftsDetail') ChangeShiftsDetail: ChangeShiftsDetail
    @Ref('exchangeCommodityListRef') exchangeCommodityListRef: ElTable
    @Ref('exchangeRenewalListRef') exchangeRenewalListRef: ElTable
    constructor() {
      super()
      this.shiftChange = new UiPage(this.queryExchangeCommodityList, this.queryExchangeCommodityList)
      this.renewalChange = new UiPage()
    }
    isShow = false
    ExchangeIssueOperateType = ExchangeIssueOperateType
    ExchangeIssueOperateTypeEnum = ExchangeIssueOperateTypeEnum

    /**
     * 是否展示面网授
     */
    get isOnlineClassSupport() {
      const show = QueryShowOffline.getShowOfflineApolloConfig()
      return !show
    }
    isShowDialog(orderNo: string) {
      this.orderNo = orderNo
      this.isShow = !this.isShow
      this.queryExchangeCommodityList()
    }

    changeShiftsDetailList = new Array<ExchangeOrderRecordVo>() // 换班详情列表
    // 换货单详情根据子订单号查询实例
    queryExchangeDetailByOrderObj = TradeModule.singleTradeBatchFactor.orderFactor.getQueryExchangeDetailByOrder()
    /**
     * 订单号
     */
    orderNo = ''
    /**
     * tab激活项
     */
    activeName = 'first'
    /**
     * 查询模型
     */
    queryExchangeCommodity = new QueryExchangeCommodity()
    /**
     * 换班分页
     */
    shiftChange = new UiPage()
    /**
     * 换班数据
     */
    exchangeCommodityList = new Array<ExchangeOrderResponseVo>()
    /**
     * 换班数据loading
     */
    exchangeCommodityListLoading = false
    /**
     * 换期分页
     */
    renewalChange = new UiPage()
    /**
     * 换期数据
     */
    exchangeRenewalList = new Array<ExchangeIssueDetail>()
    /**
     * 换期数据loading
     */
    exchangeRenewalListLoading = false
    /**
     * tab切换
     */
    handleClick(val: any) {
      if (val.name === 'first') {
        this.queryExchangeCommodityList()
      } else {
        this.queryExchangeRenewalList()
      }
    }

    // 换班详情查询
    async queryExchangeDetail(subOrderNo: string) {
      this.queryExchangeDetailByOrderObj.subOrderNo = subOrderNo
      // let res
      // if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
      //   res = await this.queryExchangeDetailByOrderObj.queryRefundOrderDetailInDistributor()
      // } else {
      const res = await this.queryExchangeDetailByOrderObj.queryRefundOrderDetail()
      // }
      if (res.code === 200) {
        console.log(this.queryExchangeDetailByOrderObj.exchangeOrderDetail)
        this.changeShiftsDetailList = this.queryExchangeDetailByOrderObj.exchangeOrderDetail?.records
        this.ChangeShiftsDetail.isShowDialog()
      } else {
        this.$message.warning('请求失败')
      }
    }
    /**
     * 换班列表查询
     */
    async queryExchangeCommodityList() {
      this.exchangeCommodityListLoading = true
      try {
        this.shiftChange.pageNo = 1
        this.shiftChange.pageSize = 200
        this.queryExchangeCommodity.subOrderNo = this.orderNo

        if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
          this.exchangeCommodityList = await this.queryExchangeCommodity.queryExchangeOrderListInDistributor(
            this.shiftChange
          )
        } else {
          this.exchangeCommodityList = await this.queryExchangeCommodity.queryExchangeOrderList(this.shiftChange)
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.exchangeCommodityListLoading = false
        this.exchangeCommodityListRef.doLayout()
      }
    }
    /**
     * 换期列表查询
     */
    async queryExchangeRenewalList() {
      this.exchangeRenewalListLoading = true
      try {
        this.renewalChange.pageNo = 1
        this.renewalChange.pageSize = 200
        this.queryExchangeCommodity.subOrderNo = this.orderNo
        if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
          this.exchangeRenewalList = await this.queryExchangeCommodity.queryExchangeIssueListInDistributor(
            this.renewalChange
          )
        } else {
          this.exchangeRenewalList = await this.queryExchangeCommodity.queryExchangeIssueList(this.renewalChange)
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.exchangeRenewalListLoading = false
        this.exchangeRenewalListRef.doLayout()
      }
    }

    isShowChangeShiftsState(id: string) {
      this.queryExchangeDetail(id)
    }
  }
</script>

<style scoped></style>
