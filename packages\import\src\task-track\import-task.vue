<template>
  <div>
    <el-input id="input" clearable v-model="importName" placeholder="请选择具体导入任务" @focus="selectImport" />

    <template
      v-if="$hasPermission('queryTaskWx,queryTaskZt')"
      desc="queryTaskWx:查询(网校),queryTaskZt:查询(专题)"
      actions="queryTaskWx:queryQueryImportList
    #queryTaskZt:queryQueryImportListByThemeManeger"
    >
      <el-drawer title="选择具体导入任务" :visible.sync="show" size="1200px" custom-class="m-drawer">
        <div class="drawer-bd">
          <el-row :gutter="16" class="m-query f-mt10">
            <el-form :inline="true" label-width="auto">
              <el-col :span="6">
                <el-form-item label="执行状态">
                  <el-select
                    v-model="taskExecuteParamRequest.taskState"
                    clearable
                    filterable
                    placeholder="请选择执行状态"
                  >
                    <el-option label="已创建" :value="0">已创建</el-option>
                    <el-option label="已就绪" :value="1">已就绪</el-option>
                    <el-option label="执行中" :value="2">执行中</el-option>
                    <el-option label="执行完成" :value="3">执行完成</el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="9">
                <el-form-item label="执行时间">
                  <double-date-picker
                    :begin-create-time.sync="taskExecuteParamRequest.executeStartTime"
                    :end-create-time.sync="taskExecuteParamRequest.executeEndTime"
                    begin-time-placeholder="请选择导入开始时间"
                    end-time-placeholder="请选择导入结束时间"
                  ></double-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item>
                  <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
          <!--表格-->
          <el-table stripe :data="queryImportTaskVo" v-loading="query.loading" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="任务名称" min-width="280" prop="name">
              <!-- <template>任务名称任务名称任务名称</template> -->
            </el-table-column>
            <el-table-column label="任务处理时间 - 任务结束时间" min-width="220">
              <template slot-scope="scope">
                <p>处理：{{ scope.row.executingTime || '-' }}</p>
                <p>结束：{{ scope.row.completedTime || '-' }}</p>
              </template>
            </el-table-column>
            <el-table-column label="任务执行状态" min-width="120">
              <template slot-scope="scope">
                <div>
                  <el-tag :type="invoiceStatusMapType[scope.row.taskState]">{{
                    questionImportTaskStatusEnum[scope.row.taskState]
                  }}</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="任务处理结果" min-width="120">
              <template slot-scope="scope">
                <div v-if="scope.row.processResult === 0">
                  <el-badge is-dot type="danger" class="badge-status">未知</el-badge>
                </div>
                <div v-else-if="scope.row.processResult === 2">
                  <el-badge is-dot type="danger" class="badge-status">{{ scope.row.result }}失败</el-badge>
                </div>
                <div v-else-if="scope.row.processResult === 1">
                  <el-badge is-dot type="success" class="badge-status">{{ scope.row.result }}成功</el-badge>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="处理总条数/成功条数/失败条数" width="240" align="center">
              <template slot-scope="scope"
                >{{ scope.row.totalCount || 0 }} / {{ scope.row.successCount || 0 }} /
                {{ scope.row.failCount || 0 }}</template
              >
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <!-- <el-button type="text" @click="importSelect(scope.row)">选择</el-button> -->
                <!-- <el-checkbox :v-model="checked" @change="importSelect(scope.row)">选择</el-checkbox> -->
                <el-radio v-model="radio" :label="scope.row.batchNo" @change="choice(scope.row)">选择</el-radio>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page"></hb-pagination>
          <div class="m-btn-bar f-tc f-mt20">
            <el-button @click="close">取消</el-button>
            <el-button @click="importSelect(choiceInfo)" type="primary">确定</el-button>
          </div>
        </div>
      </el-drawer>
    </template>
  </div>
</template>

<script lang="ts">
  import { Query, UiPage } from '@hbfe/common'
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'

  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'

  import { TaskExecuteParamRequest } from '@api/ms-gateway/ms-importopen-v1'
  import { QuestionImportTaskStatusEnum } from '@api/service/common/enums/async-task/QuestionImportTaskStatus'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import QueryQuestionAsynTask from '@api/service/management/resource/question/query/QueryQuestionAsynTask'
  import QueryImportTaskVo from '@api/service/management/resource/question/query/vo/QueryImportTaskVo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  class NewQueryImportTaskVo extends QueryImportTaskVo {
    totalCount = 0
    successCount = 0
    failCount = 0
  }
  @Component({
    components: {
      DoubleDatePicker
    }
  })
  export default class extends Vue {
    @Prop({
      default: false
    })
    clearImportDialog: boolean
    page: UiPage
    query: Query = new Query()
    show = false
    select = ''
    radio = ''
    // 选中信息
    choiceInfo = {}
    //查询接口请求
    queryQuestionAsynTask: QueryQuestionAsynTask = ResourceModule.queryQuestionFactory.queryQuestionAsynTask
    //入参
    taskExecuteParamRequest: TaskExecuteParamRequest = new TaskExecuteParamRequest()
    //查询结果
    queryImportTaskVo: Array<NewQueryImportTaskVo> = new Array<NewQueryImportTaskVo>()
    //选择的列表
    idList: Array<QueryImportTaskVo> = new Array<QueryImportTaskVo>()
    importName = ''
    questionImportTaskStatusEnum = {
      [QuestionImportTaskStatusEnum.ALREADY_CREATED]: '已创建',
      [QuestionImportTaskStatusEnum.ALREADY_PREPARED]: '已就绪',
      [QuestionImportTaskStatusEnum.EXECUTING]: '执行中',
      [QuestionImportTaskStatusEnum.EXECUTED]: '执行完成'
    }
    invoiceStatusMapType = {
      [QuestionImportTaskStatusEnum.ALREADY_CREATED]: 'info',
      [QuestionImportTaskStatusEnum.ALREADY_PREPARED]: 'primary',
      [QuestionImportTaskStatusEnum.EXECUTING]: 'primary',
      [QuestionImportTaskStatusEnum.EXECUTED]: 'success'
    }
    currentItem: QueryImportTaskVo = new QueryImportTaskVo()

    /**
     * 判断当前用户是否拥有专题管理员角色类型
     */
    get isZtglyLogin() {
      return QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    }

    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }
    @Watch('importName', {
      deep: true,
      immediate: true
    })
    importNameChange(val: any) {
      if (val == '') {
        this.importSelect(this.currentItem)
      }
    }
    async selectImport() {
      const inputEl = document.getElementById('input')
      inputEl.blur()
      this.show = true
      await this.doSearch()
    }
    //判断是否被选中
    get isSelected() {
      return (item: any) => {
        const inculde = this.idList.find((el) => el.taskId === item.taskId)
        return inculde ? true : false
      }
    }
    //选择
    importSelect(item: QueryImportTaskVo) {
      const index = this.idList.findIndex((el) => el.taskId === item.taskId)
      if (index > -1) {
        //说明有值
        this.idList.splice(index, 1)
        this.importName = ''
      } else {
        this.idList = [] as any[]
        this.idList.push(item)
        this.currentItem = item
        this.$emit('getImport', this.idList)
        this.importName = item.name
        this.show = false
        this.radio = ''
      }
    }
    // 取消选择
    close() {
      this.show = false
      this.radio = ''
    }

    // 专题管理员查询
    async queryQueryImportListByThemeManeger() {
      return await this.queryQuestionAsynTask.queryQueryImportListByThemeManeger(
        this.taskExecuteParamRequest,
        this.page
      )
    }

    // 网校管理员查询
    async queryQueryImportList() {
      return await this.queryQuestionAsynTask.queryQueryImportList(this.taskExecuteParamRequest, this.page)
    }

    async doSearch() {
      // 导入学员并开班为默认，现需加上导入学员开班并学习
      this.taskExecuteParamRequest.taskCategoryList = ['ADMIN_NORMAL_IMPORT']
      this.query.loading = true
      try {
        if (this.isZtglyLogin) {
          this.queryImportTaskVo = (await this.queryQueryImportListByThemeManeger()) as Array<NewQueryImportTaskVo>
        } else {
          this.queryImportTaskVo = (await this.queryQueryImportList()) as Array<NewQueryImportTaskVo>
        }
        this.queryImportTaskVo.forEach((item) => {
          const res = item.eachStateCounts.map((itm) => {
            if (itm.result === 1) {
              if (item.successCount == undefined) {
                item.successCount = 0
              }
              item.successCount = item.successCount + itm.count
            } else if (itm.result === 2) {
              if (item.failCount == undefined) {
                item.failCount = 0
              }
              item.failCount = item.failCount + itm.count
            }
            if (item.totalCount == undefined) {
              item.totalCount = 0
            }
            item.totalCount = item.totalCount + itm.count
          })
        })
        console.log(this.queryImportTaskVo, ' this.queryImportTaskVo')
      } catch (e) {
        console.log(e, '获取导入信息失败')
      } finally {
        this.query.loading = false
      }
    }
    choice(item: QueryImportTaskVo) {
      this.choiceInfo = item
    }
  }
</script>
