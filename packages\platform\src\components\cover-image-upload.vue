<template>
  <div>
    <el-upload
      ref="upload"
      action="#"
      list-type="picture-card"
      :auto-upload="false"
      :limit="limit"
      :file-list="fileList"
      :multiple="false"
      class="m-pic-upload proportion-pic"
      :class="{ hideUpload: isHideUpload }"
    >
      <div slot="default" class="upload-placeholder" @click.stop="showCropper" v-show="!showUrl">
        <i class="el-icon-plus"></i>
        <p class="txt">上传图片</p>
      </div>
      <div slot="file" slot-scope="{ file }" class="img-file" v-show="showUrl">
        <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
        <div class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <i class="el-icon-zoom-in"></i>
          </span>
          <span v-if="hasPermissionRemoveImage" class="el-upload-list__item-delete" @click="handleRemove(file)">
            <i class="el-icon-delete"></i>
          </span>
        </div>
      </div>
      <div slot="tip" class="el-upload__tip">
        <i class="el-icon-warning f-mt5"></i>
        <div class="txt f-mt5">
          图片比例为16:9，尺寸：400px * 225px。
        </div>
        <!-- <el-button type="primary" size="small" plain class="f-ml10" @click="chooseBuiltInImage">
          选择内置图片
        </el-button> -->
      </div>
    </el-upload>
    <!-- 剪裁组件弹窗 -->
    <vue-cropper
      ref="vueCropper"
      :visible.sync="cropperModel"
      :action="actionUrl"
      :headers="headersObj"
      :title="title"
      v-model="showUrl"
      :initWidth="initWidth"
      :dialogStyleOpation="dialogStyleOpation"
      class="vue-cropper"
      :hbOpaction="{
        ratioArr: ratioArr,
        replaceOptions: { centerBox: false, mode: mode }
      }"
    ></vue-cropper>
    <!--选择封面图片-->
    <el-drawer
      title="封面图片"
      :visible.sync="uiConfig.dialog.builtInImageDialogVisible"
      size="800px"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      custom-class="m-drawer"
    >
      <div class="drawer-bd">
        <div class="f-ml20">
          <div class="m-pic-list f-clear">
            <div class="item" v-for="(item, index) in builtInImageList" :key="index">
              <img :src="builtInImageUrl(item.url)" class="course-pic" />
              <div class="info">
                <div class="f-flex-sub">{{ item.name }}</div>
                <el-badge v-if="imageIsSelected(item)" is-dot type="primary" class="badge-status f-cb">已选择</el-badge>
                <el-button v-else type="primary" size="mini" @click="selectImage(item)">选择</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button @click="cancelImageSelection">取消</el-button>
        <el-button type="primary" @click="confirmImageSelection">确定</el-button>
      </div>
    </el-drawer>
    <!--缩略图弹窗-->
    <el-dialog
      width="1100px"
      class="m-dialog-pic"
      :visible.sync="uiConfig.dialog.imagePreviewDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <img :src="dialogImageUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Prop, Watch } from 'vue-property-decorator'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import VueCropper from '@hbfe-vue-components/image-cropper'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  /**
   * 图片信息 - 模拟
   */
  export class BuiltInImageDetail {
    // 图片路径
    url: string
    // 图片名称
    name: string
  }

  @Component({
    components: {
      VueCropper
    }
  })
  export default class CoverImageUpload extends Vue {
    // 上传文件列表
    fileList: Array<any> = []
    // 是否隐藏上传按钮
    isHideUpload = false
    // 文件上传限制
    limit = 1
    // 请求路径
    actionUrl = ''
    // 请求头
    headersObj = {
      'App-Authentication': '',
      Authorization: ''
    }
    url: string | null = null
    // showUrl = ''
    cropperModel = false //裁剪弹窗
    // 是否允许删除图片
    hasPermissionRemoveImage = true

    // ui相关的变量控制
    uiConfig = {
      // 对话框是否展示
      dialog: {
        // 内置图片
        builtInImageDialogVisible: false,
        // 图片预览
        imagePreviewDialogVisible: false
      }
    }
    // 内置图片列表
    builtInImageList: Array<BuiltInImageDetail> = new Array<BuiltInImageDetail>()

    dialogImageUrl = ''

    @Prop({ type: String, default: '', required: true }) value: string
    @Prop({ type: String, default: '班级封面' }) title: string
    @Prop({ type: Number, default: 150 }) initWidth: number
    @Prop({ type: Array, default: ['16:9'] }) ratioArr: string[]
    @Prop({
      type: Object,
      default: {
        width: '300px',
        height: '300px'
      }
    })
    dialogStyleOpation: object
    /**
     * 获取默认宽高
     */
    @Prop({ type: String, default: '' }) mode: string

    get showUrl() {
      if (this.value !== '') {
        const mfsHeadReg = /^\/mfs\/\.*/
        const localHeadReg = /^img\/\.*/
        if (mfsHeadReg.test(this.value) || localHeadReg.test(this.value)) {
          return this.value
        }
        return `/mfs${this.value}`
      }
      return null
    }
    set showUrl(val) {
      let url = ''
      if (val) {
        const mfsHeadReg = /^\/mfs\/\.*/
        const localHeadReg = /^img\/\.*/
        if (mfsHeadReg.test(val) || localHeadReg.test(val)) {
          url = val?.split('/mfs/')[0]
        } else {
          url = val
        }
      }
      this.$emit('input', url)
    }

    get builtInImageUrl() {
      return (url: string) => {
        const mfsHeadReg = /^\/mfs\/\.*/
        return mfsHeadReg.test(url) ? url : `/mfs${url}`
      }
    }

    @Watch('showUrl', {
      immediate: true,
      deep: true
    })
    showUrlChange(val: any) {
      if (val) {
        this.fileList = new Array<any>()
        this.fileList.push({ url: val })
        this.isHideUpload = this.fileList.length >= this.limit
      }
    }

    @Ref('upload') upload: any
    @Ref('vueCropper') vueCropper: any

    showCropper() {
      // 点击弹出剪裁框
      this.cropperModel = true
    }
    beforeClose() {
      this.cropperModel = false
    }
    mounted() {
      this.headersObj.Authorization = `Mship ${this.$authentication.getAccessToken()}`
      this.headersObj['App-Authentication'] = `Basic ${process.env.VUE_APP_KEY}`
      this.headersObj['Content-Type'] = 'application/json;charset=UTF-8'
      //   api.59iedu.com/web/ms-file/web/uploadPublicBase64
      //   this.actionUrl = `${ConfigCenterModule.getIngress(ingress.resource)}/auth/uploadBase64ToProtectedFile`
      this.actionUrl = `${ConfigCenterModule.getIngress(ingress.apiendpoint)}/web/ms-file-v1/web/uploadPublicBase64`
      //   console.group(
      //     '%c%s',
      //     'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#32FFFF, #fff)',
      //     ' this.actionUrl调试输出'
      //   )
      //   console.log(this.actionUrl)
      //   console.count(' this.actionUrl输出次数')
      //   console.groupEnd()
    }

    chooseBuiltInImage() {
      if (!this.builtInImageList.length) {
        this.builtInImageList = this.getBuiltInImageList()
      }
      this.uiConfig.dialog.builtInImageDialogVisible = true
    }

    /**
     * 获取图片内置方法
     */
    getBuiltInImageList(): Array<BuiltInImageDetail> {
      const prePath = `${ConfigCenterModule.getFrontendApplication(frontendApplication.mfsSubprojectAddress)}`
      const result = new Array<BuiltInImageDetail>()
      for (let i = 0; i < 6; i++) {
        const option = new BuiltInImageDetail()
        option.url = prePath + `/default-cover-pic-0${i + 1}.png`
        option.name = ''
        result[i] = option
      }
      return result
    }

    /**
     * 图片是否被选中
     */
    imageIsSelected(item: BuiltInImageDetail) {
      let result = false
      const selectedUrl = this.showUrl
      const mfsHeadReg = /^\/mfs\/\.*/
      const currentURL = mfsHeadReg.test(item.url) ? item.url : `/mfs${item.url}`
      if (selectedUrl === currentURL) {
        result = true
      }
      return result
    }

    /**
     * 选择图片
     */
    selectImage(item: BuiltInImageDetail) {
      this.showUrl = item.url
      this.uiConfig.dialog.builtInImageDialogVisible = false
    }

    /**
     * 确定-选择内置图片
     */
    confirmImageSelection() {
      // 如果没有选择封面
      const url = this.showUrl
      if (!url) {
        this.$message.error('请选择一张封面图片')
      }
    }

    /**
     * 取消-选择内置图片
     */
    cancelImageSelection() {
      this.uiConfig.dialog.builtInImageDialogVisible = false
    }

    /**
     * 删除封面图片
     */
    handleRemove() {
      this.fileList = new Array<any>()
      this.isHideUpload = this.fileList.length >= this.limit
      this.showUrl = undefined
    }

    /**
     * 预览封面图片
     */
    handlePictureCardPreview(file: any) {
      if (file.url) {
        this.dialogImageUrl = file.url
        this.uiConfig.dialog.imagePreviewDialogVisible = true
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep.hideUpload .el-upload--picture-card {
    display: none;
  }
</style>
