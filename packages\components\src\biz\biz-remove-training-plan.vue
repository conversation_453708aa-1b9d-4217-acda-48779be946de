<route-meta>
{
"title": "剔除培训方案"
}
</route-meta>
<template>
  <div>
    <el-select
      v-model="selected"
      multiple
      filterable
      remote
      reserve-keyword
      collapse-tags
      clearable
      :multiple-limit="multipleLimit"
      :placeholder="placeholder"
      :remote-method="remoteMethod"
      :loading="loading"
      @clear="clear"
    >
      <el-option
        v-for="item in options"
        :label="item.commodityBasicData.saleTitle"
        :value="item.commoditySkuId"
        :key="item.commoditySkuId"
      ></el-option>
    </el-select>
  </div>
</template>
<script lang="ts">
  import { Prop, Emit, Watch, Component, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import {
    CommoditySkuRequest,
    OnShelveRequest,
    RegionSkuPropertyRequest,
    RegionSkuPropertySearchRequest,
    SchemeRequest,
    SkuPropertyRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'

  @Component({})
  export default class extends Vue {
    selected: string[] = []

    list: any[] = []

    options: TrainClassCommodityVo[] = []

    loading = false

    states = [
      'Alabama',
      'Alaska',
      'Arizona',
      'Arkansas',
      'California',
      'Colorado',
      'Connecticut',
      'Delaware',
      'Florida',
      'Georgia',
      'Hawaii',
      'Idaho',
      'Illinois',
      'Indiana',
      'Iowa',
      'Kansas',
      'Kentucky',
      'Louisiana',
      'Maine',
      'Maryland',
      'Massachusetts',
      'Michigan',
      'Minnesota',
      'Mississippi',
      'Missouri',
      'Montana',
      'Nebraska',
      'Nevada',
      'New Hampshire',
      'New Jersey',
      'New Mexico',
      'New York',
      'North Carolina',
      'North Dakota',
      'Ohio',
      'Oklahoma',
      'Oregon',
      'Pennsylvania',
      'Rhode Island',
      'South Carolina',
      'South Dakota',
      'Tennessee',
      'Texas',
      'Utah',
      'Vermont',
      'Virginia',
      'Washington',
      'West Virginia',
      'Wisconsin',
      'Wyoming'
    ]

    // 分页参数 - 培训方案
    trainSchemePage: UiPage = new UiPage()
    // 培训方案名称
    schemeName = ''
    // 查询参数 - 培训方案
    trainSchemeQueryParam: CommoditySkuRequest
    // 培训方案业务状态层入口
    schemeBusinessEntry: QueryTrainClassCommodityList = new QueryTrainClassCommodityList()
    @Prop({
      type: String,
      default: '请选择不纳入统计的培训方案'
    })
    placeholder: string

    // 最多选择10个剔除统计的方案
    @Prop({
      type: Number,
      default: 10
    })
    multipleLimit: number

    @Prop({
      type: Array
    })
    value: Array<string>
    @Watch('value', { immediate: true, deep: true })
    valueChange(val: string[]) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected', { immediate: true, deep: true })
    selectedChange() {
      console.log(this.selected, 'this.selected')
      return this.selected
    }

    remoteMethod(query: string) {
      console.log(query, 'query')
      if (query !== '') {
        this.schemeName = query
        this.searchBase()
      } else {
        this.options = []
      }
    }

    async searchBase() {
      this.loading = true
      try {
        this.trainSchemePage.pageNo = 1
        this.trainSchemePage.pageSize = 5
        this.trainSchemeQueryParam.schemeRequest.schemeName = this.schemeName || undefined
        const trainSchemeList = await this.schemeBusinessEntry.queryTrainClassCommodityList(
          this.trainSchemePage,
          this.trainSchemeQueryParam
        )
        this.options = trainSchemeList || []
        console.log(trainSchemeList, 'trainSchemeList')
      } catch (e) {
        console.log('获取培训方案分页列表失败！', e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.trainSchemeQueryParam = new CommoditySkuRequest()
      this.trainSchemeQueryParam.onShelveRequest = new OnShelveRequest()
      this.trainSchemeQueryParam.schemeRequest = new SchemeRequest()
      this.trainSchemeQueryParam.schemeRequest.schemeName = ''
      this.trainSchemeQueryParam.skuPropertyRequest = new SkuPropertyRequest()
      this.trainSchemeQueryParam.skuPropertyRequest.year = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.trainSchemeQueryParam.skuPropertyRequest.regionSkuPropertySearch.region =
        new Array<RegionSkuPropertyRequest>()
      this.trainSchemeQueryParam.skuPropertyRequest.industry = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.subjectType = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingProfessional = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.trainingObject = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.positionCategory = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.jobLevel = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.learningPhase = new Array<string>()
      this.trainSchemeQueryParam.skuPropertyRequest.discipline = new Array<string>()
    }

    clear() {
      console.log('clear')
      this.selected = []
    }
    created() {
      //
      this.initQueryParam()
    }
  }
</script>
