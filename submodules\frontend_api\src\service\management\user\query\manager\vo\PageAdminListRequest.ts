import {
  AccountRequest,
  AdminQueryRequest,
  AdminUserRequest,
  AuthenticationRequest,
  ContractProviderAdminQueryRequest,
  RoleRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { findRoleByCategory } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1/graphql-importer'

export default class PageAdminListRequest {
  /**
   * 管理员名称
   */
  name = ''
  /**
   * 管理员账号
   */
  account = ''
  /**
   * 账户状态 1：正常，2：冻结，3：注销
   */
  status: number = null
  /**
   * 角色ID
   */
  roleId = ''
  /**
   * 管理地区路径集合（模糊，右like）
   */
  manageRegionPathList?: Array<string>
  /**
   * 管理员类型： 1：运营管理员 2：地区管理员 3：运营管理员 和 地区管理员 4: 分销商管理员 5: 专题管理员
   */
  type: number = null
  /**
   * 角色类别
   */
  roleCategory: number = null

  static from(pageAdminListRequest: PageAdminListRequest) {
    const adminQueryRequest = new AdminQueryRequest()
    adminQueryRequest.user = new AdminUserRequest()
    adminQueryRequest.user.userName = pageAdminListRequest.name
    adminQueryRequest.user.userNameMatchType = 1
    adminQueryRequest.account = new AccountRequest()
    adminQueryRequest.role = new RoleRequest()
    adminQueryRequest.authentication = new AuthenticationRequest()
    adminQueryRequest.authentication.identity = pageAdminListRequest.account
    adminQueryRequest.account.statusList = pageAdminListRequest.status ? [pageAdminListRequest.status] : undefined
    if (pageAdminListRequest.type === 1) {
      // OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员
      adminQueryRequest.role.excludeRoleCategoryList = [320, 19]
      // adminQueryRequest.role.roleTypeList = ['OPERATOR_MANAGER']
    } else if (pageAdminListRequest.type === 2) {
      adminQueryRequest.user.manageRegionPathList = pageAdminListRequest.manageRegionPathList
      adminQueryRequest.role.roleTypeList = ['REGION_MANAGER']
    } else if (pageAdminListRequest.type === 3) {
      adminQueryRequest.role.roleTypeList = ['REGION_MANAGER', 'OPERATOR_MANAGER']
    } else if (pageAdminListRequest.type === 4) {
      adminQueryRequest.role.roleCategoryList = [18]
    } else if (pageAdminListRequest.type === 5) {
      adminQueryRequest.role.roleCategoryList = [19]
    }
    adminQueryRequest.role.roleIdList = pageAdminListRequest.roleId ? [pageAdminListRequest.roleId] : undefined
    return adminQueryRequest
  }

  static toContractProviderAdminQueryRequest(pageAdminListRequest: PageAdminListRequest) {
    const adminQueryRequest = new ContractProviderAdminQueryRequest()
    adminQueryRequest.user = new AdminUserRequest()
    adminQueryRequest.user.userName = pageAdminListRequest.name
    adminQueryRequest.user.userNameMatchType = 1
    adminQueryRequest.account = new AccountRequest()
    adminQueryRequest.account.accountTypeList = [3]
    adminQueryRequest.role = new RoleRequest()
    adminQueryRequest.authentication = new AuthenticationRequest()
    adminQueryRequest.authentication.identity = pageAdminListRequest.account
    adminQueryRequest.account.statusList = pageAdminListRequest.status ? [pageAdminListRequest.status] : undefined
    if (pageAdminListRequest.type === 1) {
      // OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员
      adminQueryRequest.role.excludeRoleCategoryList = [320]
      // adminQueryRequest.role.roleTypeList = ['OPERATOR_MANAGER']
    } else if (pageAdminListRequest.type === 2) {
      adminQueryRequest.user.manageRegionPathList = pageAdminListRequest.manageRegionPathList
      adminQueryRequest.role.roleTypeList = ['REGION_MANAGER']
    } else if (pageAdminListRequest.type === 3) {
      adminQueryRequest.role.roleTypeList = ['REGION_MANAGER', 'OPERATOR_MANAGER']
    } else if (pageAdminListRequest.type === 4) {
      adminQueryRequest.role.roleCategoryList = [18]
    }
    adminQueryRequest.role.roleIdList = pageAdminListRequest.roleId ? [pageAdminListRequest.roleId] : undefined
    return adminQueryRequest
  }
}
