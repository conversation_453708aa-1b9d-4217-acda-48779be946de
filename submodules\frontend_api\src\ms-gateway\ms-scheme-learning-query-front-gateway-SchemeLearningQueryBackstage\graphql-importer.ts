import getCertificateTemplate from './queries/getCertificateTemplate.graphql'
import getElectronicSealInServicer from './queries/getElectronicSealInServicer.graphql'
import getIssueConfigureInfoInDistributor from './queries/getIssueConfigureInfoInDistributor.graphql'
import getIssueConfigureInfoInServicer from './queries/getIssueConfigureInfoInServicer.graphql'
import getLearningPeriodStatisticsInSubProject from './queries/getLearningPeriodStatisticsInSubProject.graphql'
import getMySchemeConfig from './queries/getMySchemeConfig.graphql'
import getMySchemeConfigByRequest from './queries/getMySchemeConfigByRequest.graphql'
import getMySchemeIssueConfigInMySelf from './queries/getMySchemeIssueConfigInMySelf.graphql'
import getRegionLearningReportSummeryBySkuRegionInServicerManageRegion from './queries/getRegionLearningReportSummeryBySkuRegionInServicerManageRegion.graphql'
import getRegionLearningReportSummeryInServicer from './queries/getRegionLearningReportSummeryInServicer.graphql'
import getRegionLearningReportSummeryInServicerJXJY from './queries/getRegionLearningReportSummeryInServicerJXJY.graphql'
import getRegionLearningReportSummeryInServicerManageRegion from './queries/getRegionLearningReportSummeryInServicerManageRegion.graphql'
import getSchemeConfigByRequestInServicer from './queries/getSchemeConfigByRequestInServicer.graphql'
import getSchemeConfigInDistributor from './queries/getSchemeConfigInDistributor.graphql'
import getSchemeConfigInServicer from './queries/getSchemeConfigInServicer.graphql'
import getSchemeConfigInSubject from './queries/getSchemeConfigInSubject.graphql'
import getSchemeIssueConfigInServicer from './queries/getSchemeIssueConfigInServicer.graphql'
import getSchemeIssuePlanItemConfigInServicer from './queries/getSchemeIssuePlanItemConfigInServicer.graphql'
import getSchemeIssuePlanItemInDistributor from './queries/getSchemeIssuePlanItemInDistributor.graphql'
import getSchemeIssuePlanItemInServicer from './queries/getSchemeIssuePlanItemInServicer.graphql'
import getSchemeIssueRuleConfigInServicer from './queries/getSchemeIssueRuleConfigInServicer.graphql'
import getSchemeLearningReportSummeryInServicer from './queries/getSchemeLearningReportSummeryInServicer.graphql'
import getStudentSchemeConfig from './queries/getStudentSchemeConfig.graphql'
import getStudentSchemeConfigInServicer from './queries/getStudentSchemeConfigInServicer.graphql'
import getStudentSchemeConfigInSubProject from './queries/getStudentSchemeConfigInSubProject.graphql'
import getStudentSchemeLearningDetailInServicer from './queries/getStudentSchemeLearningDetailInServicer.graphql'
import getStudentSchemeLearningInServicer from './queries/getStudentSchemeLearningInServicer.graphql'
import getStudentSchemeLearningInSubProject from './queries/getStudentSchemeLearningInSubProject.graphql'
import getStudentSchemeLearningTotalStatisticsInServicer from './queries/getStudentSchemeLearningTotalStatisticsInServicer.graphql'
import getTeacherListInServicer from './queries/getTeacherListInServicer.graphql'
import getTrainingPointDetailInServicer from './queries/getTrainingPointDetailInServicer.graphql'
import listRegionLearningReportFormsInServicer from './queries/listRegionLearningReportFormsInServicer.graphql'
import listRegionLearningReportFormsInServicerJXJY from './queries/listRegionLearningReportFormsInServicerJXJY.graphql'
import listRegionLearningReportFormsInServicerManageRegion from './queries/listRegionLearningReportFormsInServicerManageRegion.graphql'
import listSchemeLearningReportFormsInServicer from './queries/listSchemeLearningReportFormsInServicer.graphql'
import listSkuRegionLearningReportFormsInServicer from './queries/listSkuRegionLearningReportFormsInServicer.graphql'
import listSkuRegionLearningReportFormsInServicerManageRegion from './queries/listSkuRegionLearningReportFormsInServicerManageRegion.graphql'
import pageAppointStudentSchemeIssueRegistrationInServicer from './queries/pageAppointStudentSchemeIssueRegistrationInServicer.graphql'
import pageCertificateTemplate from './queries/pageCertificateTemplate.graphql'
import pageCertificateTemplateInDistributor from './queries/pageCertificateTemplateInDistributor.graphql'
import pageIssueStudyConfigInServicer from './queries/pageIssueStudyConfigInServicer.graphql'
import pageSchemeConfigByRequestInServicer from './queries/pageSchemeConfigByRequestInServicer.graphql'
import pageSchemeConfigInDistributor from './queries/pageSchemeConfigInDistributor.graphql'
import pageSchemeConfigInServicer from './queries/pageSchemeConfigInServicer.graphql'
import pageSchemeIssueConfigListInDistributor from './queries/pageSchemeIssueConfigListInDistributor.graphql'
import pageSchemeIssueConfigListInServicer from './queries/pageSchemeIssueConfigListInServicer.graphql'
import pageSchemeIssueConfigListOptionalLoginInServicer from './queries/pageSchemeIssueConfigListOptionalLoginInServicer.graphql'
import pageSchemeIssuePlanItemInServicer from './queries/pageSchemeIssuePlanItemInServicer.graphql'
import pageSchemeIssueReplaceableInServicer from './queries/pageSchemeIssueReplaceableInServicer.graphql'
import pageStudentExchangeIssueRecordInServicer from './queries/pageStudentExchangeIssueRecordInServicer.graphql'
import pageStudentSchemeConfigInMySelf from './queries/pageStudentSchemeConfigInMySelf.graphql'
import pageStudentSchemeConfigInServicer from './queries/pageStudentSchemeConfigInServicer.graphql'
import pageStudentSchemeConfigInSubProject from './queries/pageStudentSchemeConfigInSubProject.graphql'
import pageStudentSchemeIssueRegistrationInServicer from './queries/pageStudentSchemeIssueRegistrationInServicer.graphql'
import pageStudentSchemeLearningByCollectiveUserIdInServicer from './queries/pageStudentSchemeLearningByCollectiveUserIdInServicer.graphql'
import pageStudentSchemeLearningBySkuRegionInServicerManageRegion from './queries/pageStudentSchemeLearningBySkuRegionInServicerManageRegion.graphql'
import pageStudentSchemeLearningDetailInDistributor from './queries/pageStudentSchemeLearningDetailInDistributor.graphql'
import pageStudentSchemeLearningDetailInServicer from './queries/pageStudentSchemeLearningDetailInServicer.graphql'
import pageStudentSchemeLearningDetailInServicerManageRegion from './queries/pageStudentSchemeLearningDetailInServicerManageRegion.graphql'
import pageStudentSchemeLearningDetailInTrainingChannel from './queries/pageStudentSchemeLearningDetailInTrainingChannel.graphql'
import pageStudentSchemeLearningInDistributor from './queries/pageStudentSchemeLearningInDistributor.graphql'
import pageStudentSchemeLearningInServicer from './queries/pageStudentSchemeLearningInServicer.graphql'
import pageStudentSchemeLearningInServicer4PrintProof from './queries/pageStudentSchemeLearningInServicer4PrintProof.graphql'
import pageStudentSchemeLearningInServicer4PrintProofInDistributor from './queries/pageStudentSchemeLearningInServicer4PrintProofInDistributor.graphql'
import pageStudentSchemeLearningInServicer4PrintProofInTrainingChannel from './queries/pageStudentSchemeLearningInServicer4PrintProofInTrainingChannel.graphql'
import pageStudentSchemeLearningInServicerManageRegion from './queries/pageStudentSchemeLearningInServicerManageRegion.graphql'
import pageStudentSchemeLearningInServicerV2 from './queries/pageStudentSchemeLearningInServicerV2.graphql'
import pageStudentSchemeLearningInSubProject from './queries/pageStudentSchemeLearningInSubProject.graphql'
import pageStudentSchemeLearningInTrainingChannelV2 from './queries/pageStudentSchemeLearningInTrainingChannelV2.graphql'
import pageStudentSchemeLearningInformationInServicer from './queries/pageStudentSchemeLearningInformationInServicer.graphql'
import pageStudentSchemeLearningStatisticsInServicer from './queries/pageStudentSchemeLearningStatisticsInServicer.graphql'
import pageTrainingClassroomInDistributor from './queries/pageTrainingClassroomInDistributor.graphql'
import pageTrainingClassroomInMyself from './queries/pageTrainingClassroomInMyself.graphql'
import pageTrainingPointInDistributor from './queries/pageTrainingPointInDistributor.graphql'
import pageTrainingPointInServicer from './queries/pageTrainingPointInServicer.graphql'

export {
  getCertificateTemplate,
  getElectronicSealInServicer,
  getIssueConfigureInfoInDistributor,
  getIssueConfigureInfoInServicer,
  getLearningPeriodStatisticsInSubProject,
  getMySchemeConfig,
  getMySchemeConfigByRequest,
  getMySchemeIssueConfigInMySelf,
  getRegionLearningReportSummeryBySkuRegionInServicerManageRegion,
  getRegionLearningReportSummeryInServicer,
  getRegionLearningReportSummeryInServicerJXJY,
  getRegionLearningReportSummeryInServicerManageRegion,
  getSchemeConfigByRequestInServicer,
  getSchemeConfigInDistributor,
  getSchemeConfigInServicer,
  getSchemeConfigInSubject,
  getSchemeIssueConfigInServicer,
  getSchemeIssuePlanItemConfigInServicer,
  getSchemeIssuePlanItemInDistributor,
  getSchemeIssuePlanItemInServicer,
  getSchemeIssueRuleConfigInServicer,
  getSchemeLearningReportSummeryInServicer,
  getStudentSchemeConfig,
  getStudentSchemeConfigInServicer,
  getStudentSchemeConfigInSubProject,
  getStudentSchemeLearningDetailInServicer,
  getStudentSchemeLearningInServicer,
  getStudentSchemeLearningInSubProject,
  getStudentSchemeLearningTotalStatisticsInServicer,
  getTeacherListInServicer,
  getTrainingPointDetailInServicer,
  listRegionLearningReportFormsInServicer,
  listRegionLearningReportFormsInServicerJXJY,
  listRegionLearningReportFormsInServicerManageRegion,
  listSchemeLearningReportFormsInServicer,
  listSkuRegionLearningReportFormsInServicer,
  listSkuRegionLearningReportFormsInServicerManageRegion,
  pageAppointStudentSchemeIssueRegistrationInServicer,
  pageCertificateTemplate,
  pageCertificateTemplateInDistributor,
  pageIssueStudyConfigInServicer,
  pageSchemeConfigByRequestInServicer,
  pageSchemeConfigInDistributor,
  pageSchemeConfigInServicer,
  pageSchemeIssueConfigListInDistributor,
  pageSchemeIssueConfigListInServicer,
  pageSchemeIssueConfigListOptionalLoginInServicer,
  pageSchemeIssuePlanItemInServicer,
  pageSchemeIssueReplaceableInServicer,
  pageStudentExchangeIssueRecordInServicer,
  pageStudentSchemeConfigInMySelf,
  pageStudentSchemeConfigInServicer,
  pageStudentSchemeConfigInSubProject,
  pageStudentSchemeIssueRegistrationInServicer,
  pageStudentSchemeLearningByCollectiveUserIdInServicer,
  pageStudentSchemeLearningBySkuRegionInServicerManageRegion,
  pageStudentSchemeLearningDetailInDistributor,
  pageStudentSchemeLearningDetailInServicer,
  pageStudentSchemeLearningDetailInServicerManageRegion,
  pageStudentSchemeLearningDetailInTrainingChannel,
  pageStudentSchemeLearningInDistributor,
  pageStudentSchemeLearningInServicer,
  pageStudentSchemeLearningInServicer4PrintProof,
  pageStudentSchemeLearningInServicer4PrintProofInDistributor,
  pageStudentSchemeLearningInServicer4PrintProofInTrainingChannel,
  pageStudentSchemeLearningInServicerManageRegion,
  pageStudentSchemeLearningInServicerV2,
  pageStudentSchemeLearningInSubProject,
  pageStudentSchemeLearningInTrainingChannelV2,
  pageStudentSchemeLearningInformationInServicer,
  pageStudentSchemeLearningStatisticsInServicer,
  pageTrainingClassroomInDistributor,
  pageTrainingClassroomInMyself,
  pageTrainingPointInDistributor,
  pageTrainingPointInServicer
}
