<template>
  <div class="pure">
    <!--强制关闭批次-->
    <el-drawer
      title="提示"
      :visible.sync="show"
      size="600px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form ref="form" label-width="auto" class="m-form f-mt20">
              <el-form-item>
                <p>请输入强制关闭批次原因：</p>
                <el-input type="textarea" :rows="6" v-model="reason" />
              </el-form-item>
              <el-form-item class="m-btn-bar f-tc">
                <el-button @click="cancelForceCloseBatchOrder">取消</el-button>
                <el-button type="primary" @click="confirmForceCloseBatchOrder">确定</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, PropSync, Vue } from 'vue-property-decorator'
  import { bind, debounce } from 'lodash-decorators'
  import TradeModule from '@api/service/management/trade/TradeModule'

  @Component
  export default class extends Vue {
    /**
     * 是否展示
     */
    @PropSync('visible', {
      type: Boolean
    })
    show!: boolean

    /**
     * 批次单
     */
    @Prop({
      type: String
    })
    batchOrderNo: string

    reason = ''

    /**
     * 取消
     */
    cancelForceCloseBatchOrder() {
      this.show = false
    }

    /**
     * 确定
     */
    @bind
    @debounce(200)
    async confirmForceCloseBatchOrder() {
      if (!this.reason) {
        this.$message.error('请输入强制关闭批次原因，不能为空！')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        const payResult =
          await TradeModule.batchTradeBatchFactor.orderFactor.mutationOrderFactor.mutationBatchOrderSignUp.prepareBatchOrderRepay(
            this.batchOrderNo
          )
        if (payResult.paymentStatus === 2) {
          return this.$message.warning('该批次订单已支付，不能关闭！')
        } else if (payResult.paymentStatus === 0 || payResult.paymentStatus === 1) {
          const params = {
            batchNo: this.batchOrderNo,
            reason: this.reason
          }
          const status =
            await TradeModule.batchTradeBatchFactor.orderFactor.mutationOrderFactor.mutationBatchOrderSignUp.cancelCollectivePaySignUp(
              params
            )
          if (status?.isSuccess()) {
            this.$message.success('关闭批次成功')
            this.show = false
            this.$emit('reloadData')
          }
        }
      } catch (e) {
        this.$message.error(e || '关闭批次失败')
      } finally {
        loading.close()
      }
    }
  }
</script>
