"""独立部署的微服务,K8S服务名:ms-autolearning-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询自动学习编排日志"""
	queryAutoLearningArrangeLog(studentAutoLearningTaskResultId:String):[AutoLearningArrangeLog]
	"""查询自动学习执行日志"""
	queryAutoLearningExecuteLog(studentAutoLearningTaskResultId:String):[AutoLearningExecuteLog]
	"""查看详情-课程学习"""
	queryCourseLearningDetail(logId:String):Void
}
"""自动学习编排日志
	<AUTHOR>
	@date 2025/3/18 10:28
"""
type AutoLearningArrangeLog @type(value:"com.fjhb.ms.autolearning.v1.kernel.domain.autolearninglog.model.AutoLearningArrangeLog") {
	"""日志ID"""
	logId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""学员自动学习任务结果ID"""
	studentAutoLearningTaskResultId:String
	"""主任务ID"""
	mainTaskId:String
	"""学习方案ID"""
	learningSchemeId:String
	"""学号"""
	studentNo:String
	"""参训资格ID"""
	qualificationId:String
	"""信息"""
	message:String
	"""类型
		@see com.fjhb.ms.autolearning.v1.kernel.consts.AutoLearningLogTypes
	"""
	type:Int
	"""编排次数(当type=1的时候才有值)"""
	arrangeNum:Int
	"""创建时间"""
	createTime:DateTime
	"""完成时间"""
	completeTime:DateTime
}
"""自动学习执行日志
	<AUTHOR>
	@date 2025/3/18 10:31
"""
type AutoLearningExecuteLog @type(value:"com.fjhb.ms.autolearning.v1.kernel.domain.autolearninglog.model.AutoLearningExecuteLog") {
	"""日志ID"""
	logId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""学员自动学习任务结果ID"""
	studentAutoLearningTaskResultId:String
	"""主任务ID"""
	mainTaskId:String
	"""学习方案ID"""
	learningSchemeId:String
	"""学号"""
	studentNo:String
	"""参训资格ID"""
	qualificationId:String
	"""状态
		@see com.fjhb.ms.autolearning.v1.kernel.consts.AutoLearningLogExecuteStatus
	"""
	status:Int
	"""信息"""
	message:String
	"""类型
		@see com.fjhb.ms.autolearning.v1.kernel.consts.AutoLearningLogTypes
	"""
	type:Int
	"""创建时间"""
	createTime:DateTime
}

scalar List
