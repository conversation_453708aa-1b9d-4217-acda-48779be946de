import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-certificate-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-certificate-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 分页查询入参
<AUTHOR>
@date 2022/12/19 16:32
 */
export class AllocateCourseTaskQueryRequest {
  /**
   * 任务名
   */
  name?: string
  /**
   * 执行状态
@see com.fjhb.batchtask.core.enums.TaskState  0 - 已创建 1 - 已就绪 2 - 执行中 3 - 已完成
   */
  taskState?: number
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 结束时间
   */
  endTime?: string
}

/**
 * 防伪二维码
<AUTHOR>
 */
export class AntiBogusQRCode {
  /**
   * 防伪二维码id
   */
  id?: string
  /**
   * 平台id
   */
  platformId?: string
  /**
   * 平台版本id
   */
  platformVersionId?: string
  /**
   * 项目id
   */
  projectId?: string
  /**
   * 子项目id
   */
  subProjectId?: string
  /**
   * 单位id
   */
  unitId?: string
  /**
   * 服务商id
   */
  servicerId?: string
  /**
   * 防伪二维码url
   */
  url?: string
  /**
   * 预览路径
   */
  previewUrl?: string
  /**
   * 是否提供短链
   */
  transShortUrl: boolean
  /**
   * 创建时间
   */
  createdTime?: string
  /**
   * 创建人id
   */
  createUserId?: string
  /**
   * 更新时间
   */
  updateTime?: string
}

/**
 * 批量打印证书请求
<AUTHOR>
 */
export class BatchPrintCertificatesRequest {
  /**
   * 学号集合
   */
  studentNos?: Array<string>
  /**
   * 文件类型 1-PDF   2-IMAGE
@see FileTypes
   */
  fileType: number
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 打印方式
@see PrintTypes
   */
  printType: number
}

export class CertificateCryptoRequest {
  pageNo: number
  pageSize: number
  servicerId?: string
  projectId?: string
}

/**
 * 证书模板
<AUTHOR>
@date 2023/08/16
 */
export class CertificateTemplate {
  /**
   * 模板id
   */
  id?: string
  /**
   * 平台id
   */
  platformId?: string
  /**
   * 平台版本id
   */
  platformVersionId?: string
  /**
   * 项目id
   */
  projectId?: string
  /**
   * 子项目id
   */
  subProjectId?: string
  /**
   * 单位id
   */
  unitId?: string
  /**
   * 服务商id
   */
  servicerId?: string
  /**
   * 模板名称
   */
  name?: string
  /**
   * 模板说明
   */
  describe?: string
  /**
   * 所属行业id
   */
  belongsIndustryId?: string
  /**
   * 使用范围
   */
  usableRange?: string
  /**
   * 适用培训方案形式
   */
  suitableSchemeType?: string
  /**
   * html模板地址
   */
  url?: string
  /**
   * 预览html模板地址
   */
  previewUrl?: string
  /**
   * 打印快照数据源
   */
  printSnapShotDataSource?: string
  /**
   * 是否应用电子章
   */
  provideElectronicSeal: boolean
  /**
   * 电子章数据源
   */
  electronicDataSource?: string
  /**
   * 是否提供防伪二维码
   */
  provideAntiBogusQRCode: boolean
  /**
   * 防伪二维码id
   */
  AntiBogusQRCodeId?: string
  /**
   * 创建人id
   */
  createUserId?: string
  /**
   * 创建时间
   */
  createdTime?: string
  /**
   * 更新时间
   */
  updatedTime?: string
  /**
   * 是否可用
   */
  available: boolean
}

/**
 * 配置电子章请求
<AUTHOR>
 */
export class ConfigureElectronicSealRequest {
  /**
   * 生成形式
1-图片
   */
  generateType: number
  /**
   * 电子章url
   */
  url: string
  /**
   * 电子章落款
   */
  sign?: string
}

/**
 * 电子印章
<AUTHOR>
@date 2023/08/16
 */
export class ElectronicSeal {
  /**
   * 电子章id
   */
  id?: string
  /**
   * 平台id
   */
  platformId?: string
  /**
   * 平台版本id
   */
  platformVersionId?: string
  /**
   * 项目id
   */
  projectId?: string
  /**
   * 子项目id
   */
  subProjectId?: string
  /**
   * 单位id
   */
  unitId?: string
  /**
   * 服务商id
   */
  servicerId?: string
  /**
   * 生成形式
   */
  generateType: number
  /**
   * 电子章url
   */
  url?: string
  /**
   * 电子章落款
   */
  sign?: string
  /**
   * 创建时间
   */
  createdTime?: string
  /**
   * 创建人id
   */
  createUserId?: string
  /**
   * 更新时间
   */
  updateTime?: string
}

/**
 * 通过证书id查询快照请求
<AUTHOR>
@date 2023/9/8 11:32
 */
export class GetSnapShotRequest {
  /**
   * 证书id
   */
  certificateId?: string
}

/**
 * 导入学员批量任务查询请求
<AUTHOR>
@date 2024/06/18
 */
export class ImportPrintTaskQueryRequest {
  /**
   * 任务分类
   */
  category?: string
  /**
   * 任务名
   */
  name?: string
  /**
   * 执行状态
@see com.fjhb.batchtask.core.enums.TaskState  0 - 已创建 1 - 已就绪 2 - 执行中 3 - 已完成
   */
  taskState?: number
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 结束时间
   */
  endTime?: string
}

/**
 * 打印证书请求 - 下载
<AUTHOR>
 */
export class PrintCertificateRequest {
  /**
   * 学号
   */
  studentNo?: string
  /**
   * 文件类型 1-PDF   2-IMAGE
@see FileTypes
   */
  fileType: number
  /**
   * 打印方式
@see com.fjhb.domain.learningscheme.api.certificate.consts.PrintTypes
   */
  printType: number
}

/**
 * 打印
<AUTHOR>
 */
export class PrintCertificateTemplateRequest {
  /**
   * 证明模板id
   */
  certificateTemplateId?: string
  /**
   * 打印文件类型
@see FileTypes
1-PDF    2-图片
   */
  printFileType: number
}

/**
 * 导出任务查询入参
<AUTHOR>
@date 2023/09/04
 */
export class QueryExportTaskRequest {
  /**
   * 任务分类
   */
  category?: string
  /**
   * 执行状态
@see com.fjhb.batchtask.core.enums.TaskState  0 - 已创建 1 - 已就绪 2 - 执行中 3 - 已完成
   */
  taskState?: number
  /**
   * 用户id
   */
  userId?: string
}

/**
 * 扫描证书二维码生成证明请求体
 */
export class ScanQrCodeGenerateCertificateRequest {
  /**
   * 证书快照id
   */
  snapshotId: string
  /**
   * {@link FileTypes} 文件类型 1:PDF 2:IMAGE
   */
  fileType: number
}

/**
 * 证件照上传请求
 */
export class UploadPhotoRequest {
  /**
   * 用户id
   */
  userId: string
  /**
   * 证件照mfs文件路径
   */
  photoUrl: string
}

/**
 * 证书模板
<AUTHOR>
 */
export class CertificateTemplate1 {
  /**
   * 模板id
   */
  id: string
  /**
   * 平台id
   */
  platformId: string
  /**
   * 平台版本id
   */
  platformVersionId: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 子项目id
   */
  subProjectId: string
  /**
   * 单位id
   */
  unitId: string
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 模板名称
   */
  name: string
  /**
   * 模板说明
   */
  describe: string
  /**
   * 所属行业id
   */
  belongsIndustryId: string
  /**
   * 使用范围
   */
  usableRange: string
  /**
   * 适用培训方案形式
   */
  suitableSchemeType: string
  /**
   * html模板地址
   */
  url: string
  /**
   * 预览html模板路径
   */
  previewUrl: string
  /**
   * 打印快照数据源
   */
  printSnapShotDataSource: string
  /**
   * 是否应用电子章
   */
  provideElectronicSeal: boolean
  /**
   * 电子章数据源
   */
  electronicDataSource: string
  /**
   * 是否提供防伪二维码
   */
  provideAntiBogusQRCode: boolean
  /**
   * 防伪二维码id
   */
  AntiBogusQRCodeId: string
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 是否合并
   */
  isMerge: string
  /**
   * 模板尺寸
   */
  size: string
  /**
   * 是否可用
   */
  available: boolean
  /**
   * 数据范围
   */
  dataRangeList: Array<DataRange>
  /**
   * 数据范围
   */
  formatType: string
}

/**
 * 任务执行情况
<AUTHOR>
@date 2022/12/19 16:41
 */
export class ImportTaskQueryResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息 (有过执行失败这是报错信息)
   */
  message: string
  /**
   * 创建时间
   */
  ceratedTime: string
  /**
   * 处理时间
   */
  executingTime: string
  /**
   * 操作人id
   */
  createUserId: string
  /**
   * 结束（完成）时间
   */
  completedTime: string
  /**
   * 压缩包路径
   */
  zipPath: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount>
  /**
   * 处理总条数
   */
  totalCount: number
  /**
   * 成功条数
   */
  successCount: number
  /**
   * 失败条数
   */
  failCount: number
  /**
   * 导入进度 1-导入成功 2-导入中 3-导入失败 4-部分成功
   */
  importProgress: number
}

/**
 * 各状态及执行结果对应数量
 */
export class EachStateCount {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

/**
 * <AUTHOR>
@date 2024/7/15
 */
export class DataRange {
  /**
   * 数据大类名称
   */
  name: string
  /**
   * 数据key列表
   */
  keyList: Array<string>
}

export class ImportTaskQueryResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ImportTaskQueryResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据网校id(服务商id)判断该网校证明模版是否需要上传证件照
   * @return true为需要 false为不需要
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async determineCertificateTemplateIsNeedPhoto(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.determineCertificateTemplateIsNeedPhoto,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取模板信息
   * @param query 查询 graphql 语法文档
   * @param templateId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async findCertificateTemplate(
    templateId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findCertificateTemplate,
    operation?: string
  ): Promise<Response<CertificateTemplate1>> {
    return commonRequestApi<CertificateTemplate1>(
      SERVER_URL,
      {
        query: query,
        variables: { templateId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定批次下的任务执行情况
   * @param request
   * @param page    分页信息
   * @return 执行情况
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findImportPrintTaskExecuteResponsePage(
    params: { request?: ImportPrintTaskQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findImportPrintTaskExecuteResponsePage,
    operation?: string
  ): Promise<Response<ImportTaskQueryResponsePage>> {
    return commonRequestApi<ImportTaskQueryResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取证书快照信息
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async findSnapShotByCertificateId(
    request: GetSnapShotRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findSnapShotByCertificateId,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询指定批次下的任务执行情况
   * @param request
   * @param page    分页信息
   * @return 执行情况
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTaskExecuteResponsePage(
    params: { request?: AllocateCourseTaskQueryRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findTaskExecuteResponsePage,
    operation?: string
  ): Promise<Response<ImportTaskQueryResponsePage>> {
    return commonRequestApi<ImportTaskQueryResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据用户id查询用户证件照
   * @param userId
   * @return 证件照url
   * @param query 查询 graphql 语法文档
   * @param userId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findUserIdentificationPhotoByUserId(
    userId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findUserIdentificationPhotoByUserId,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { userId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询指定批次下的导出任务执行情况
   * @param request
   * @param page  分页信息
   * @return 执行情况
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryExportTaskResponsePage(
    params: { request?: QueryExportTaskRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryExportTaskResponsePage,
    operation?: string
  ): Promise<Response<ImportTaskQueryResponsePage>> {
    return commonRequestApi<ImportTaskQueryResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据用户id校验用户是否完成证件照上传
   * @return true为完成 false为未完成
   * @param query 查询 graphql 语法文档
   * @param userId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async verifyPhotoIsUpload(
    userId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.verifyPhotoIsUpload,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { userId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重发证书事件
   * @param mutate 查询 graphql 语法文档
   * @param eventId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async anewConsumeCertificateEvent(
    eventId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.anewConsumeCertificateEvent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { eventId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量打印证书
   * @param request 批量打印证书请求
   * @return zip压缩包路径
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchPrintCertificates(
    request: BatchPrintCertificatesRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchPrintCertificates,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 调用接口执行无编号证书记录表数据加密操作
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async certificateNumberRecordUpdateCrypto(
    request: CertificateCryptoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.certificateNumberRecordUpdateCrypto,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 调用接口执行证书快照表数据加密操作
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async certificateSnapshotUpdateCrypto(
    request: CertificateCryptoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.certificateSnapshotUpdateCrypto,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 调用接口执行证书表数据加密操作
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async certificateUpdateCrypto(
    request: CertificateCryptoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.certificateUpdateCrypto,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 配置电子章（覆盖）
   * @param request 配置电子章请求
   * @return 电子章id
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async configureElectronicSeal(
    request: ConfigureElectronicSealRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.configureElectronicSeal,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新证书二维码
   * @param request 请求
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCertificateDimensionalCode(
    request: AntiBogusQRCode,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createCertificateDimensionalCode,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新证书电子印章
   * @param request 电子印章
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCertificateElectronicSeal(
    request: ElectronicSeal,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createCertificateElectronicSeal,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新证书模板
   * @param request 证书模板
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCertificateTemplate(
    request: CertificateTemplate,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createCertificateTemplate,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取证书快照
   * @param studentNo 学号
   * @return 获取证书快照json
   * @param mutate 查询 graphql 语法文档
   * @param studentNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCertificateSnapShot(
    studentNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.getCertificateSnapShot,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { studentNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 打印证书
   * @param request 打印证书请求
   * @return 证书path
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async printCertificate(
    request: PrintCertificateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.printCertificate,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 打印证明模板
   * @param request 打印证明模板请求
   * @return 文件路径
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async printCertificateTemplate(
    request: PrintCertificateTemplateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.printCertificateTemplate,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param list 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rebuildCertificateNo(
    list: Array<string>,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.rebuildCertificateNo,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { list },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 扫描证书二维码生成证明
   * @param request 请求
   * @return 证明路径
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async scanQrCodeGenerateCertificate(
    request: ScanQrCodeGenerateCertificateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.scanQrCodeGenerateCertificate,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 用户证件照上传（用于证书打印）
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async uploadPhoto(
    request: UploadPhotoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.uploadPhoto,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
