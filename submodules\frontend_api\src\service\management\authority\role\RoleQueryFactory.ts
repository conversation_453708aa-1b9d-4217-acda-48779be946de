import QueryRole from '@api/service/management/authority/role/query/QueryRole'
import { QueryRoleList } from '@api/service/management/authority/role/query/QueryRoleList'
import { QueryAllPermission } from '@api/service/management/authority/role/query/QueryAllPermission'
import { QueryCurrentUserSecurityGroup } from '@api/service/management/authority/role/query/QueryCurrentUserSecurityGroup'
import { QuerySecurityGroupByRoleId } from '@api/service/management/authority/role/query/QuerySecurityGroupByRoleId'
import { QuerySecurityGroupForEdidRole } from '@api/service/management/authority/role/query/QuerySecurityGroupForEdidRole'
import { QueryRoleDetail } from '@api/service/management/authority/role/query/QueryRoleDetail'
import QueryCurrentUserRoleList from '@api/service/management/authority/role/query/QueryCurrentUserRoleList'

class RoleQueryFactory {
  /*
   *  获取角色列表
   * */
  getQueryRoleList() {
    return new QueryRoleList()
  }
  /*
   *  获取角色详情
   * */
  getQueryRoleDetail() {
    return new QueryRoleDetail()
  }
  /*
   *  查询所有的安全对象树
   * */
  getQueryAllPermission() {
    return new QueryAllPermission()
  }
  /*
   *  查询当前角色安全对象树
   * */
  getQueryCurrentUserSecurityGroup() {
    return new QueryCurrentUserSecurityGroup()
  }
  /*
   *  根据角色id查询安全对象树
   * */
  getQuerySecurityGroupByRoleId() {
    return new QuerySecurityGroupByRoleId()
  }
  /*
   *  根据roleId获取安全对象组(编辑使用)
   * */
  getQuerySecurityGroupForEdidRole() {
    return new QuerySecurityGroupForEdidRole()
  }
  /**
   * 查询当前登录用户角色列表
   */
  getQueryCurrentUserRoleList() {
    return new QueryCurrentUserRoleList()
  }
}

export default RoleQueryFactory
