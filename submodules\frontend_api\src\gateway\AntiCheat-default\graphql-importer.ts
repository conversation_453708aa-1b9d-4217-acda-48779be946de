import findAllUserExamAnswerFSResults from './queries/findAllUserExamAnswerFSResults.graphql'
import findAllUserSchemeCourseFSRecordPage from './queries/findAllUserSchemeCourseFSRecordPage.graphql'
import findAllUserSchemeCoursewareFSRecordPage from './queries/findAllUserSchemeCoursewareFSRecordPage.graphql'
import findAllUserSchemeCoursewareFSRecordPageByLasted from './queries/findAllUserSchemeCoursewareFSRecordPageByLasted.graphql'
import findAllUserSchemeExamFSRecordPage from './queries/findAllUserSchemeExamFSRecordPage.graphql'
import findDatumConfig from './queries/findDatumConfig.graphql'
import findDatumConfigByPlatform from './queries/findDatumConfigByPlatform.graphql'
import findExamAppealCountInfoByCurrentUser from './queries/findExamAppealCountInfoByCurrentUser.graphql'
import findExamAppealListByCurrentUser from './queries/findExamAppealListByCurrentUser.graphql'
import findExamScenesConfig from './queries/findExamScenesConfig.graphql'
import findExamScenesConfigInfo from './queries/findExamScenesConfigInfo.graphql'
import findExamUserAppealById from './queries/findExamUserAppealById.graphql'
import findExamUserAppealPageList from './queries/findExamUserAppealPageList.graphql'
import findLearningScenesConfig from './queries/findLearningScenesConfig.graphql'
import findLearningScenesConfigInfo from './queries/findLearningScenesConfigInfo.graphql'
import findLoginScenesConfig from './queries/findLoginScenesConfig.graphql'
import findLoginScenesConfigInfo from './queries/findLoginScenesConfigInfo.graphql'
import findPlatformLearningScenesConfigInfo from './queries/findPlatformLearningScenesConfigInfo.graphql'
import findSupervisionConfigByByUnit from './queries/findSupervisionConfigByByUnit.graphql'
import findSupervisionConfigByPlatform from './queries/findSupervisionConfigByPlatform.graphql'
import findSupervisionConfigBySubProject from './queries/findSupervisionConfigBySubProject.graphql'
import findUserDatum from './queries/findUserDatum.graphql'
import findUserExamAnswerPagerFSRecords from './queries/findUserExamAnswerPagerFSRecords.graphql'
import findUserExamAnswerPagerFSRecordsByLasted from './queries/findUserExamAnswerPagerFSRecordsByLasted.graphql'
import findUserLoginFSRecordPage from './queries/findUserLoginFSRecordPage.graphql'
import listUserDatumPhotoChangeRecord from './queries/listUserDatumPhotoChangeRecord.graphql'
import addExamScenesConfig from './mutates/addExamScenesConfig.graphql'
import addLearningScenesConfig from './mutates/addLearningScenesConfig.graphql'
import addLoginScenesConfig from './mutates/addLoginScenesConfig.graphql'
import addPlatformLearningScenesConfig from './mutates/addPlatformLearningScenesConfig.graphql'
import agreeAppeal from './mutates/agreeAppeal.graphql'
import appendPhoto from './mutates/appendPhoto.graphql'
import applyCoursewareRandomFacePoints from './mutates/applyCoursewareRandomFacePoints.graphql'
import comparePhoto from './mutates/comparePhoto.graphql'
import comparePhotoFromDatum from './mutates/comparePhotoFromDatum.graphql'
import createDatumConfig from './mutates/createDatumConfig.graphql'
import createExamAppeal from './mutates/createExamAppeal.graphql'
import createOrUpdateSupervisionConfigByPlatform from './mutates/createOrUpdateSupervisionConfigByPlatform.graphql'
import createOrUpdateSupervisionConfigBySubProject from './mutates/createOrUpdateSupervisionConfigBySubProject.graphql'
import createOrUpdateSupervisionConfigByUnit from './mutates/createOrUpdateSupervisionConfigByUnit.graphql'
import createUserDatum from './mutates/createUserDatum.graphql'
import detectPhotoFace from './mutates/detectPhotoFace.graphql'
import rejectAppeal from './mutates/rejectAppeal.graphql'
import resetAllowUpdateCount from './mutates/resetAllowUpdateCount.graphql'
import resetUpdateCount from './mutates/resetUpdateCount.graphql'
import updateDatumConfig from './mutates/updateDatumConfig.graphql'
import updateDatumConfigState from './mutates/updateDatumConfigState.graphql'
import updateExamScenesConfig from './mutates/updateExamScenesConfig.graphql'
import updateLearningScenesConfig from './mutates/updateLearningScenesConfig.graphql'
import updateLoginScenesConfig from './mutates/updateLoginScenesConfig.graphql'
import updatePhotoPaths from './mutates/updatePhotoPaths.graphql'
import updatePlatformLearningScenesConfig from './mutates/updatePlatformLearningScenesConfig.graphql'
import verifyPhotoLiveness from './mutates/verifyPhotoLiveness.graphql'

export {
  findAllUserExamAnswerFSResults,
  findAllUserSchemeCourseFSRecordPage,
  findAllUserSchemeCoursewareFSRecordPage,
  findAllUserSchemeCoursewareFSRecordPageByLasted,
  findAllUserSchemeExamFSRecordPage,
  findDatumConfig,
  findDatumConfigByPlatform,
  findExamAppealCountInfoByCurrentUser,
  findExamAppealListByCurrentUser,
  findExamScenesConfig,
  findExamScenesConfigInfo,
  findExamUserAppealById,
  findExamUserAppealPageList,
  findLearningScenesConfig,
  findLearningScenesConfigInfo,
  findLoginScenesConfig,
  findLoginScenesConfigInfo,
  findPlatformLearningScenesConfigInfo,
  findSupervisionConfigByByUnit,
  findSupervisionConfigByPlatform,
  findSupervisionConfigBySubProject,
  findUserDatum,
  findUserExamAnswerPagerFSRecords,
  findUserExamAnswerPagerFSRecordsByLasted,
  findUserLoginFSRecordPage,
  listUserDatumPhotoChangeRecord,
  addExamScenesConfig,
  addLearningScenesConfig,
  addLoginScenesConfig,
  addPlatformLearningScenesConfig,
  agreeAppeal,
  appendPhoto,
  applyCoursewareRandomFacePoints,
  comparePhoto,
  comparePhotoFromDatum,
  createDatumConfig,
  createExamAppeal,
  createOrUpdateSupervisionConfigByPlatform,
  createOrUpdateSupervisionConfigBySubProject,
  createOrUpdateSupervisionConfigByUnit,
  createUserDatum,
  detectPhotoFace,
  rejectAppeal,
  resetAllowUpdateCount,
  resetUpdateCount,
  updateDatumConfig,
  updateDatumConfigState,
  updateExamScenesConfig,
  updateLearningScenesConfig,
  updateLoginScenesConfig,
  updatePhotoPaths,
  updatePlatformLearningScenesConfig,
  verifyPhotoLiveness
}
