import { ResponseStatus, Response } from '@hbfe/common'
import CreateCommodityBase from '@api/service/management/train-class/mutation/vo/CreateCommodityBase'
import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
import AssessmentTargetModel from '@api/service/management/train-class/mutation/vo/AssessmentTargetModel'
import MsLearningScheme from '@api/ms-gateway/ms-learningscheme-v1'
import TrainClassConfigJsonManager from '@api/service/management/train-class/Utils/TrainClassConfigJsonManager'
import CertificateVo from '@api/service/management/train-class/mutation/vo/CertificateVo'
import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
import PlatformQztgSchool, { QZTGUpdateMergeSkuRelationRequest } from '@api/diff-gateway/platform-qztg-school'
import MutationCreateTrainClassCommodity from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'

/**
 * 创建制培训班商品类//创建，修改和复制，最终都是拿到这个业务类，ui层无需关心，就按照统一逻辑调用即可
 */
class MutationCreateTrainClassCommodityDiff extends MutationCreateTrainClassCommodity {
  // region properties
  /**
   *培训班基础信息，类型为TrainClassBaseModel
   */
  trainClassBaseInfo = new TrainClassBaseModel()
  /**
   *学习方式，类型为LearningType
   */
  learningTypeModel = new LearningType()
  /**
   *学习方式初始数据，类型为LearningType，修改用
   */
  learningTypeModelCopy = new LearningType()
  /**
   * 证明(修改方案判断证明是否移除)
   */
  certificate = new CertificateVo()
  /**
   * 合并的商品列表
   */
  mergeCommodityList: Array<TrainClassCommodityVo> = new Array<TrainClassCommodityVo>()

  /**
   * 原被合并商品列表（用于修改时比对剔除与新增了哪些商品）
   * @private
   */
  originalAssociatedCommodityList: Array<TrainClassCommodityVo> = new Array<TrainClassCommodityVo>()

  /**
   * 被合并的商品列表
   */
  associatedCommodityList: Array<TrainClassCommodityVo> = new Array<TrainClassCommodityVo>()
  /**
   * 时间配置
   */
  timeConfig: {
    onShelvesPlanTime: string
    offShelvesPlanTime: string
    registerBeginDate: string
    registerEndDate: string
  } = null
  /**
   * 是否基础信息修改 调用只修改基础信息接口 UI判断页面组件显隐
   */
  isBaseInfoModify = false
  // /**
  //  *考核规则，类型为AssessmentTargetModel[]
  //  */
  // assessmentRules: AssessmentTargetModel[] = []
  // endregion
  // region methods

  // /**
  //  * 创建或者更新培训班商品
  //  */
  // async createTrainClassCommodity(): Promise<Response<string>> {
  //   // if (this.trainClassBaseInfo.id)
  //   //   console.log(TrainClassConfigJsonManager.convertCreateCommodityToJsonString(this, this.learningTypeModelCopy))
  //   // else console.log(TrainClassConfigJsonManager.convertCreateCommodityToJsonString(this))
  //   // return new ResponseStatus(30006, '报错')
  //   if (this.trainClassBaseInfo.id) {
  //     const params: { token: string; configJson: string } = {
  //       token: 'W10=',
  //       configJson: TrainClassConfigJsonManager.convertCreateCommodityToJsonString(this, this.learningTypeModelCopy)
  //     }
  //     const res: Response<string> = this.isBaseInfoModify
  //       ? await MsLearningScheme.specialUpdateLearningScheme(params)
  //       : await MsLearningScheme.asyncUpdateLearningScheme(params)
  //     return res
  //   } else {
  //     const res = await MsLearningScheme.asyncCreateLearningScheme({
  //       token: 'W10=',
  //       configJson: TrainClassConfigJsonManager.convertCreateCommodityToJsonString(this)
  //     })
  //     return res
  //   }
  // }

  /**
   * 更新合并商品关系
   */
  async updateMergeRelation() {
    const mergeRequest = new QZTGUpdateMergeSkuRelationRequest()
    const { added, removed } = this.compareCommodityLists(
      this.originalAssociatedCommodityList,
      this.associatedCommodityList
    )

    mergeRequest.skuId = this.commoditySkuId
    mergeRequest.addSkuIds = added.map((item) => {
      return item.commoditySkuId
    })
    mergeRequest.removeSkuIds = removed.map((item) => {
      return item.commoditySkuId
    })

    const updateMergeCommodityRelationRes = await PlatformQztgSchool.updateMergeCommodityRelation(mergeRequest)

    if (updateMergeCommodityRelationRes?.status && updateMergeCommodityRelationRes.status.isSuccess()) {
      return Promise.resolve(new ResponseStatus(200, ''))
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  /**
   * 比较两个商品列表，取出新增和删除的商品列表
   * @param originalList 原合并商品列表
   * @param newList 新合并商品列表
   * @returns 包含新增和删除商品列表的对象
   */
  private compareCommodityLists(
    originalList: Array<TrainClassCommodityVo>,
    newList: Array<TrainClassCommodityVo>
  ): { added: Array<TrainClassCommodityVo>; removed: Array<TrainClassCommodityVo> } {
    const added: Array<TrainClassCommodityVo> = []
    const removed: Array<TrainClassCommodityVo> = []

    // 找出新增的商品
    newList.forEach((newItem) => {
      if (!originalList.some((originalItem) => originalItem.schemeId === newItem.schemeId)) {
        added.push(newItem)
      }
    })

    // 找出删除的商品
    originalList.forEach((originalItem) => {
      if (!newList.some((newItem) => newItem.schemeId === originalItem.schemeId)) {
        removed.push(originalItem)
      }
    })

    return { added, removed }
  }

  // endregion
}
export default MutationCreateTrainClassCommodityDiff
