schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""确认线下退款(此操作是在同意退款申请后，管理员通过线下退款后，用于确认已退款)
		@param refundOrderNo 退款单号
		@param remark        备注
	"""
	affirmOfflineOrderRefund(refundOrderNo:String,remark:String):Void
	affirmOfflineRefund(affirmInfo:AffirmOnlineBatchRefundRequest):Void
	"""同意退款
		@param batchRefundNo 批次退款单
	"""
	agreeBatchRefund(batchRefundNo:String):Void
	agreeBatchRefundApply(agreeInfo:AgreeBatchRefundApplyRequest):BatchRefundOrderResponse
	"""同意退款(此操作是在同意了退款申请后同意进行退款)
		@param refundOrderNo 退款单号
	"""
	agreeRefund(refundOrderNo:String):Void
	"""同意退款申请
		@param refundOrderNo 退款单号
		@param remark        备注
	"""
	agreeRefundApply(refundOrderNo:String,remark:String):Void
	applyBatchRefund(applyInfo:ApplyBatchRefundRequest):BatchRefundOrderResponse
	applyBatchRefundCheck(applyInfo:ApplyBatchRefundRequest):BatchApplyRefundCheckResponse
	applyRefund(applyInfo:ApplyRefundRequest):RefundOrderResponse
	applyRefundCheck(applyInfo:ApplyRefundRequest):ApplyRefundCheckResponse
	cancelBatchOrder(cancelInfo:BatchOrderCancelRequest):BatchOrderResponse
	cancelBatchRefund(cancelInfo:CancelBatchRefundRequest):BatchRefundOrderResponse
	"""取消订单
		@param orderNo  订单号
		@param reasonId 取消原因ID
		@param remark   取消备注
	"""
	cancelOrder(orderNo:String,reasonId:String,remark:String):Void
	"""取消退款申请
		@param refundOrderNo 退款单号
		@param reason        取消退款申请原因，可为空
	"""
	cancelRefundApply(refundOrderNo:String,reason:String):Void
	commitBatchOrder(commitInfo:BatchOrderCommitRequest):BatchOrderResponse
	confirmBatchOrderOfflinePay(paymentResult:BatchOrderConfirmPaymentRequest):Void
	createBatchOrder(createInfo:BatchOrderCreateRequest):BatchOrderResponse
	createBatchOrderAndOrder(batchNo:String,createInfo:BatchOrderOrderCreateRequest):String
	createOrder(createInfo:OrderCreateRequest):OrderResponse
	forceCancelBatchOrder(cancelInfo:BatchOrderCancelRequest):BatchOrderResponse
	"""强制关闭订单
		@param orderNo  订单号
		@param reasonId 关闭原因ID
		@param remark   关闭备注
	"""
	forceCloseOrder(orderNo:String,reasonId:String,remark:String):Void
	"""隐藏用户订单
		@param orderNo 订单号
	"""
	hideOrder(orderNo:String):Void
	offlinePayBatchOrder(batchOrderOfflinePayInfo:BatchOrderOfflinePayRequest):Void
	onlinePayBatchOrder(batchOrderPayInfo:BatchOrderPayRequest):String
	payOrder(orderPayInfo:OrderPayInfoRequest):String
	rejectBatchRefund(rejectInfo:RejectBatchRefundRequest):BatchRefundOrderResponse
	rejectBatchRefundApply(rejectInfo:RejectBatchRefundApplyRequest):BatchRefundOrderResponse
	"""拒绝退款(此操作是在同意了退款申请后在放款给买家时拒绝的)
		@param refundOrderNo 退款单号
		@param reason        拒绝退款原因
	"""
	rejectRefund(refundOrderNo:String,reason:String):Void
	"""拒绝退款申请
		@param refundOrderNo 退款单号
		@param reason        拒绝退款申请原因
	"""
	rejectRefundApply(refundOrderNo:String,reason:String):Void
	"""删除批次单，只有起始状态的批次单可以删除
		@param batchNo 批次单号
	"""
	removeBatchOrderInvoice(batchNo:String):Void
	"""重试线上退款
		@param batchRefundNo 批次退款单
	"""
	retryOnlineRefund(batchRefundNo:String):Void
	"""重试批次退款单下订单退款单回收物品
		@param subOrderNo 子订单号
	"""
	retrySubOrderRecycleProduct(subOrderNo:String):Void
	updateBatchOrderInvoice(updateInfo:BatchOrderInvoiceUpdateRequest):Void
	updateOrderInvoice(updateInfo:OrderInvoiceUpdateRequest):Void
}
"""确认批次线上退款信息"""
input AffirmOnlineBatchRefundRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.AffirmOnlineBatchRefundRequest") {
	"""批次退款单号"""
	batchRefundOrderNo:String!
	"""备注"""
	remark:String
}
"""同意批次退款信息"""
input AgreeBatchRefundApplyRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.AgreeBatchRefundApplyRequest") {
	"""批次退款单号"""
	batchRefundOrderNo:String!
	"""备注"""
	remark:String
}
"""批次退款申请信息"""
input ApplyBatchRefundRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.ApplyBatchRefundRequest") {
	"""批次单号"""
	batchNo:String!
	"""批次退款单退款方式,空值表示按原来批次单支付方式退款
		1:线上退款
		2:线下退款
	"""
	mode:Int
	"""申请的退款类型，默认3
		1:退货不退款
		2:退款不退货
		3:退货并退款
	"""
	type:Int!
	"""退款原因ID"""
	reasonId:String
	"""退款描述"""
	reason:String
	"""退款方式
		普通退款、强制退款
		默认普通退款
	"""
	refundMeans:RefundMeansEnum
}
"""申请退款信息"""
input ApplyRefundRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.ApplyRefundRequest") {
	"""订单号"""
	orderNo:String
	"""子订单号"""
	subOrderNo:String
	"""退款原因ID"""
	reasonId:String
	"""退款原因"""
	reason:String
	refundMeans:RefundMeansEnum
}
"""批次单取消信息
	<AUTHOR>
	@date 2020/8/18
	@since 1.1.0
"""
input BatchOrderCancelRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.BatchOrderCancelRequest") {
	"""批次单号"""
	batchNo:String!
	"""取消原因ID"""
	reasonId:String!
	"""取消原因描述"""
	reasonDescription:String!
}
"""批次单提交信息
	<AUTHOR>
	@date 2020/8/18
	@since 1.1.0
"""
input BatchOrderCommitRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.BatchOrderCommitRequest") {
	"""批次单号"""
	batchNo:String
	"""批次单总金额
		如果有设置金额则批次单以此金额不自动计算,否则会自动计算批次的价格(将批次中所有的订单金额累加)
	"""
	totalMoney:BigDecimal
}
"""确认线下支付信息
	<AUTHOR>
	@date 2020/8/18
	@since 1.1.0
"""
input BatchOrderConfirmPaymentRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.BatchOrderConfirmPaymentRequest") {
	"""批次单号"""
	batchNo:String
	"""是否成功"""
	success:Boolean!
	"""失败原因"""
	errMsg:String
	"""真正支付完成的收款账户id"""
	receiveAccountId:String
}
"""批次单创建信息
	<AUTHOR>
	@date 2020/8/18
	@since 1.1.0
"""
input BatchOrderCreateRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.BatchOrderCreateRequest") {
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""集体缴费单位"""
	collectiveUnitId:String
	"""发票索要信息"""
	invoiceInfo:InvoiceRequest1
}
"""批次单更新发票信息
	<AUTHOR>
	@date 2020/8/18
	@since 1.1.0
"""
input BatchOrderInvoiceUpdateRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.BatchOrderInvoiceUpdateRequest") {
	"""批次单号"""
	batchOrderNo:String
	"""是否需要发票"""
	needInvoice:Boolean!
	"""发票信息"""
	invoiceInfo:InvoiceRequest1
}
"""线下支付信息
	<AUTHOR>
	@date 2020/8/18
	@since 1.1.0
"""
input BatchOrderOfflinePayRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.BatchOrderOfflinePayRequest") {
	"""要支付的批次号"""
	batchNo:String
	"""支付金额"""
	payMoney:BigDecimal
	"""付款人id"""
	payerId:String
	"""收款人账户id"""
	receiveAccountId:String
	"""备注"""
	remark:String
	"""线下付款凭证文件路径集合"""
	paths:[String]
}
"""批次单下订单创建信息
	<AUTHOR>
	@date 2020/8/18
	@since 1.1.0
"""
input BatchOrderOrderCreateRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.BatchOrderOrderCreateRequest") {
	"""买家ID"""
	buyerId:String
	"""销售渠道ID"""
	marketingChannelId:String
	"""发票信息"""
	invoiceInfo:InvoiceRequest1
	"""子订单集合"""
	subOrderList:[SubOrderItemCreateRequest]
}
"""批次单线上支付信息
	<AUTHOR>
	@date 2020/8/18
	@since 1.1.0
"""
input BatchOrderPayRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.BatchOrderPayRequest") {
	"""批次单号"""
	batchNo:String
	"""描述"""
	description:String
	"""收款账号编号"""
	receiveAccountId:String
	"""支付页面地址"""
	pageUrl:String
	"""扩展信息"""
	extParams:Map
}
"""取消批次退款信息"""
input CancelBatchRefundRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.CancelBatchRefundRequest") {
	"""批次退款单号"""
	batchRefundOrderNo:String!
	"""取消原因"""
	reason:String
}
"""索要发票信息"""
input InvoiceRequest1 @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.InvoiceRequest") {
	"""[必填]发票抬头"""
	title:String
	"""[必填]发票抬头类型
		@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTitleTypeConst
	"""
	titleType:Int!
	"""发票类型
		@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTypeConst
	"""
	type:Int!
	"""纳税人识别号，发票抬头类型为企业时必填"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账号"""
	account:String
	"""发票邮箱"""
	email:String
	"""发票备注"""
	remark:String
	"""发票的object信息集合"""
	objectList:[InvoiceObj1]
	"""是否电子发票"""
	electron:Boolean!
	"""是否非税发票"""
	noTaxBill:Boolean!
}
input InvoiceObj1 @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.InvoiceRequest$InvoiceObj") {
	objectType:String
	objectId:String
}
"""订单创建信息"""
input OrderCreateRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.OrderCreateRequest") {
	"""销售渠道ID"""
	marketingChannelId:String
	"""发票信息"""
	invoiceInfo:InvoiceRequest1
	"""子订单集合"""
	subOrderList:[SubOrderItemCreateRequest]
}
"""订单发票更新信息"""
input OrderInvoiceUpdateRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.OrderInvoiceUpdateRequest") {
	"""订单号"""
	orderNo:String
	"""是否需要发票"""
	needInvoice:Boolean!
	"""发票信息"""
	invoiceInfo:InvoiceRequest1
}
input OrderPayInfoRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.OrderPayInfoRequest") {
	orderNo:String
	receiveAccountId:String
	pageUrl:String
	extParams:Map
}
"""拒绝批次退款申请信息"""
input RejectBatchRefundApplyRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.RejectBatchRefundApplyRequest") {
	"""批次退款单号"""
	batchRefundOrderNo:String!
	"""拒绝原因"""
	reason:String!
}
"""拒绝批次退款信息"""
input RejectBatchRefundRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.RejectBatchRefundRequest") {
	"""批次退款单号"""
	batchRefundOrderNo:String!
	"""拒绝原因"""
	reason:String!
}
input SubOrderItemCreateRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.SubOrderItemCreateRequest") {
	"""商品ID"""
	commodityId:String
	"""是否需要发票"""
	needBill:Boolean!
	"""购买数量"""
	purchaseQuantity:Int!
}
enum RefundMeansEnum @type(value:"com.fjhb.platform.core.v1.order.api.enums.RefundMeansEnum") {
	NORMAL_REFUND
	FORCIBLY_REFUND
}
"""索要发票信息"""
type InvoiceRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.InvoiceRequest") {
	"""[必填]发票抬头"""
	title:String
	"""[必填]发票抬头类型
		@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTitleTypeConst
	"""
	titleType:Int!
	"""发票类型
		@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTypeConst
	"""
	type:Int!
	"""纳税人识别号，发票抬头类型为企业时必填"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账号"""
	account:String
	"""发票邮箱"""
	email:String
	"""发票备注"""
	remark:String
	"""发票的object信息集合"""
	objectList:[InvoiceObj]
	"""是否电子发票"""
	electron:Boolean!
	"""是否非税发票"""
	noTaxBill:Boolean!
}
type InvoiceObj @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.InvoiceRequest$InvoiceObj") {
	objectType:String
	objectId:String
}
"""退款校验结果返回
	<AUTHOR> create 2020/10/9 14:46
"""
type ApplyRefundCheckResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.ApplyRefundCheckResponse") {
	"""订单号"""
	orderNo:String
	"""子订单号"""
	subOrderNo:String
	"""订单校验结果
		如果该校验不通过，则不会进行后面几项校验
		40001 订单不存在
	"""
	subOrderRefundResult:SaleReturnCheckResponse
	"""发票校验结果
		30001 发票蓝票已开具
	"""
	invoiceRefundCheckResult:SaleReturnCheckResponse
	"""商品校验结果
		10001  该已售商品无法进行部分退货
		10002  该已售商品已失效，无法进行退货
		20001  方案考核已通过
	"""
	saleReturnCheckResultList:[SaleReturnCheckResponse]
}
"""批次退款校验结果返回值
	<AUTHOR> create 2020/10/10 13:50
"""
type BatchApplyRefundCheckResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.BatchApplyRefundCheckResponse") {
	"""发票校验结果
		30001 发票蓝票已开具
	"""
	invoiceRefundCheckResult:SaleReturnCheckResponse
	"""每一笔订单的校验结果"""
	applyRefundCheckResultList:[ApplyRefundCheckResponse]
	"""批次订单校验结果
		如果该校验不通过，则不会进行后面几项校验
		40001 订单不存在
		@see com.fjhb.platform.core.v1.order.api.constants.SubOrderRefundConstant
	"""
	orderRefundResult:SaleReturnCheckResponse
}
"""批次单信息
	<AUTHOR>
	@date 2020/8/18
	@since 1.1.0
"""
type BatchOrderResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.BatchOrderResponse") {
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""批次单号"""
	no:String
	"""批次单状态
		0:起始状态
		1:添加关联订单中
		2:已提交，此时不能在对该批次进行追加关联订单
		3:支付中
		4:交易关闭中
		5:支付失败
		6:支付成功
		7:发货中
		8:发货成功过
		9:交易成功
		10:交易关闭
	"""
	status:Int!
	"""批次单总金额(该字段会在批次单提交后计算总金额)"""
	totalMoney:BigDecimal
	"""是否需要发票"""
	needInvoice:Boolean!
	"""发票索要信息"""
	invoiceInfo:InvoiceRequest
	"""收款账户ID"""
	receiveAccountId:String
	"""提交时间"""
	commitTime:DateTime
	"""交易完成时间,如果最终是交易成功就是交易成功时间,如果最终是交易关闭就是交易关闭时间"""
	tradeFinishTime:DateTime
	"""创建类型
		1:系统创建
		2:用户创建
		3:管理员创建
		4:历史迁移
		5:外部接口
	"""
	createType:Int!
	"""创建人id"""
	creatorId:String
	"""创建时间"""
	createTime:DateTime
}
"""批次退款单信息"""
type BatchRefundOrderResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.BatchRefundOrderResponse") {
	"""退款单所属平台ID"""
	platformId:String
	"""退款单所属平台版本ID"""
	platformVersionId:String
	"""退款单所属项目ID"""
	projectId:String
	"""退款单所属子项目ID"""
	subProjectId:String
	"""退款单所属单位ID"""
	unitId:String
	"""退款单所属组织机构ID"""
	organizationId:String
	"""退款单号"""
	no:String
	"""批次号"""
	batchNo:String
	"""退款原因ID"""
	reasonId:String
	"""退款金额"""
	refundAmount:BigDecimal
	"""申请退款描述"""
	reason:String
	"""批次退款单状态
		@see com.fjhb.platform.core.v1.order.api.constants.BatchRefundOrderStatusConst
	"""
	refundStatus:Int!
	"""申请人ID"""
	applyUserId:String
	"""申请时间"""
	applyTime:DateTime
	"""拒绝申请原因描述"""
	refuseApplyDesc:String
	"""拒绝退款原因描述"""
	refuseRefundDesc:String
	"""创建人ID"""
	creatorId:String
	"""审核时间"""
	auditTime:DateTime
	"""确认退款时间(即点击放款的时间)"""
	affirmRefundTime:DateTime
	"""退款成功或退款失败的时间(即退款单处于最终退款结果的时间)"""
	finishTime:DateTime
	"""创建方式
		@see com.fjhb.platform.core.v1.order.api.constants.BatchRefundOrderCreateTypeConst
	"""
	createType:Int!
	"""退货单类型,默认退货并退款
		@see com.fjhb.platform.core.v1.order.api.constants.BatchRefundOrderTypeConst
	"""
	type:Int!
	"""退款模式
		@see com.fjhb.platform.core.v1.order.api.constants.BatchRefundOrderModeConst
	"""
	mode:Int!
	"""取消退款原因"""
	cancelReason:String
	"""备注"""
	remark:String
}
"""订单"""
type OrderResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.OrderResponse") {
	"""订单号"""
	orderNo:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""买家ID"""
	buyerId:String
	"""买家名称"""
	buyerName:String
	"""卖家ID"""
	sellerId:String
	"""卖家名称"""
	sellerName:String
	"""是否需要发票"""
	needInvoice:Boolean!
	"""发票索要信息"""
	invoiceInfo:InvoiceRequest
	"""子订单列表"""
	subOrderList:[SubOrderItemResponse]
	"""订单状态
		@see com.fjhb.platform.core.v1.order.api.constants.OrderStatusConst
	"""
	orderStatus:Int!
	"""订单总金额"""
	totalAmount:BigDecimal
	"""创建方式
		@see com.fjhb.platform.core.v1.order.api.constants.OrderCreateTypeConst
	"""
	createType:Int!
	"""创建时间"""
	createTime:DateTime
}
"""退款单拓展信息"""
type RefundOrderExtResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.RefundOrderExtResponse") {
	"""业务自定义的objectType"""
	objectType:String
	"""业务自定义的objectId"""
	objectId:String
	"""备注"""
	remark:String
}
"""退款单"""
type RefundOrderResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.RefundOrderResponse") {
	"""退款单号"""
	refundServiceNo:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""主订单号"""
	refundMasterOrderNo:String
	"""子订单号"""
	refundSubOrderNo:String
	"""是否为虚拟物品"""
	refundProductVirtual:String
	"""退款金额"""
	refundAmount:BigDecimal
	"""退款状态
		@see com.fjhb.platform.core.v1.order.api.constants.RefundStatusConst
	"""
	refundStatus:Int!
	"""退款申请人ID"""
	applyUserId:String
	"""退款申请时间"""
	applyTime:DateTime
	"""退款原因ID"""
	reasonId:String
	"""退款原因描述"""
	reason:String
	"""审核时间"""
	auditTime:DateTime
	"""确认退款时间(即点击放款的时间)"""
	affirmRefundTime:DateTime
	"""退款成功或退款失败的时间(即退款单处于最终退款结果的时间)"""
	finishTime:DateTime
	"""创建类型
		@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderCreateTypeConst
	"""
	createType:Int!
	"""退款单类型
		@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderTypeConst
	"""
	type:Int!
	"""退款方式
		@see com.fjhb.platform.core.v1.order.api.constants.RefundModeConst
	"""
	mode:Int!
	"""取消退款原因描述"""
	cancelReason:String
	"""取消退款申请时间"""
	cancelApplyDate:DateTime
	"""拒绝申请原因描述"""
	refuseApplyDesc:String
	"""拒绝退款原因描述"""
	refuseRefundDesc:String
	"""备注"""
	remark:String
	"""操作员ID"""
	operatorId:String
	"""拓展信息集合"""
	extList:[RefundOrderExtResponse]
}
"""校验结果返回类
	<AUTHOR> create 2020/10/13 15:18
"""
type SaleReturnCheckResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.SaleReturnCheckResponse") {
	"""是否允退货"""
	allow:Boolean!
	"""不允许退货原因"""
	disallowMessage:String
	"""校验结果返回code
		<p>
		商品校验结果code 10开头 10001
		物品校验结果code 20开头 20001
		发票校验结果code 30开头 30001
		订单校验结果code 40开头 40001
	"""
	code:String
}
"""子订单的外链信息"""
type SubOrderItemLinkResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.SubOrderItemLinkResponse") {
	"""子订单外链资源的objectType"""
	objectType:String
	"""子订单外链资源的objectId"""
	objectId:String
	"""备注"""
	remark:String
}
"""子订单"""
type SubOrderItemResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.SubOrderItemResponse") {
	"""子订单号"""
	subOrderNo:String
	"""商品溯源码"""
	commodityTraceCode:String
	"""商品ID"""
	commodityId:String
	"""商品名称"""
	commodityName:String
	"""商品图片地址"""
	photoPath:String
	"""商品规格"""
	specification:String
	"""是否为虚拟物品"""
	virtualGoods:Boolean!
	"""商品标价"""
	labelPrice:BigDecimal
	"""成交单价"""
	dealPrice:BigDecimal
	"""购买数量"""
	purchaseQuantity:Int!
	"""实付总价"""
	totalAmount:BigDecimal
	"""子订单状态
		@see com.fjhb.platform.core.v1.order.api.constants.SubOrderStatusConst
	"""
	orderStatus:Int!
	"""是否需要发票"""
	needBill:Boolean!
	"""子订单的外链资源"""
	subOrderLinks:[SubOrderItemLinkResponse]
}

scalar List
