schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取当前专题信息"""
	getCurrentTrainingChannelInfo:TrainingChannelPageResponse @optionalLogin
	"""子项目-分页获取网校下的专题信息
		@param id                网校id
		@param trainingChanelIds 专题id列表
	"""
	getPageTrainingChannelInfo(page:Page,id:String,trainingChanelIds:[String],isShowAll:Boolean):TrainingChannelPageResponsePage @page(for:"TrainingChannelPageResponse") @optionalLogin
	"""子项目级别-分页获取网校下的专题简要信息
		@param id                网校id
		@param trainingChanelIds 专题id列表
		@param isShowAll 是否查询所有专题（默认为false，查询启用且在网校下显示的专题）
	"""
	getPageTrainingChannelSimpleInfoInSubject(page:Page,id:String,trainingChanelIds:[String],isShowAll:Boolean):TrainingChannelSimplePageResponsePage @page(for:"TrainingChannelSimplePageResponse") @optionalLogin
	"""子项目-根据专题id查询专题详情"""
	getTrainingChannelDetailById(id:String):TrainingChannelDetailResponse @optionalLogin
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
type RegionModel @type(value:"com.fjhb.ms.basicdata.model.RegionModel") {
	regionId:String
	regionPath:String
	provinceId:String
	provinceName:String
	cityId:String
	cityName:String
	countyId:String
	countyName:String
}
"""附件返回值"""
type AttachmentResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.nested.AttachmentResponse") {
	"""附件名称"""
	name:String
	"""附件地址"""
	url:String
}
type IndustryModel @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.IndustryModel") {
	"""行业id"""
	industryId:String
	"""行业名称"""
	industryName:String
}
type NetSchoolResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.NetSchoolResponse") {
	"""门户类型（1：web端 2：移动端）"""
	portalType:Int
	"""网校域名"""
	netSchoolDomainName:String
}
type TrainingChannelDetailResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.TrainingChannelDetailResponse") {
	"""专题id"""
	id:String
	"""网校id"""
	servicerId:String
	"""网校名称"""
	netSchoolName:String
	"""网校域名"""
	netSchoolDoMain:[NetSchoolResponse]
	"""专题入口名称"""
	entryName:String
	"""专题名称"""
	name:String
	"""域名类型（1：系统默认域名 2：自定义）"""
	domainNameType:Int
	"""专题域名名称"""
	domainName:String
	"""专题类型：
		1-地区  2-行业  3-班级
	"""
	types:[Int]
	"""是否显示在网校"""
	showOnNetSchool:Boolean
	"""PC端专题模板编号"""
	pcTemplateNo:String
	"""H5端专题模板编号"""
	h5TemplateNo:String
	"""启用状态（0：停用 1：启用）"""
	enable:Boolean
	"""状态（1：草稿 2：正常）"""
	status:Int
	"""排序号码"""
	sort:Int
	"""适用地区"""
	regions:[RegionModel]
	"""适用行业"""
	industrys:[String]
	"""专题门户"""
	topic:TrainingChannelTopicResponse
	"""已配置方案数"""
	configuredPlans:Long!
	"""单位名称"""
	unitName:String
	"""是否允许访问"""
	allowAccess:Boolean
}
type TrainingChannelPageResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.TrainingChannelPageResponse") {
	"""专题id"""
	id:String
	"""专题名称"""
	name:String
	"""入口名称"""
	entryName:String
	"""专题类型：
		1-地区  2-行业  3-班级
	"""
	types:[Int]
	"""是否展示在网校"""
	showOnNetSchool:Boolean
	"""是否允许访问"""
	allowAccess:Boolean
	"""网校id"""
	netSchoolId:String
	"""网校名称"""
	netSchoolName:String
	"""网校域名"""
	netSchoolDomainName:[NetSchoolResponse]
	"""专题域名"""
	domainName:String
	"""PC端专题模板编号"""
	pcTemplateNo:String
	"""H5端专题模板编号"""
	h5TemplateNo:String
	"""已配置方案数"""
	configuredPlans:Long!
	"""启用状态（0：停用 1：启用）"""
	enable:Boolean
	"""排序"""
	sort:Int
	"""适用地区"""
	regions:[RegionModel]
	"""适用行业"""
	industrys:[IndustryModel]
	"""最后编辑时间"""
	updatedTime:String
	"""单位名称"""
	unitName:String
}
"""<AUTHOR> linq
	@date : 2025-05-15 14:08
	@description : 专题简要信息列表
"""
type TrainingChannelSimplePageResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.TrainingChannelSimplePageResponse") {
	"""专题id"""
	id:String
	"""专题类型：
		1-地区  2-行业  3-班级
	"""
	types:[Int]
	"""适用行业id"""
	industryIdList:[String]
	"""单位名称"""
	unitName:String
}
type TrainingChannelTopicPhotoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.TrainingChannelTopicPhotoResponse") {
	"""专题门户轮播图id"""
	id:String
	"""专题门户轮播图类型：1 web端、2 H5端"""
	type:Int
	"""专题门户轮播图地址"""
	pictureUrl:String
	"""链接地址"""
	linkUrl:String
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:String
}
type TrainingChannelTopicResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.trainingchannel.TrainingChannelTopicResponse") {
	"""专题门户id"""
	id:String
	"""logo类型（1：文字、2：图片）"""
	logoType:Int
	"""专题门户logo名称（专题门户logo类型为文字时，有值）"""
	logoName:String
	"""专题门户logo图片地址（专题门户logo类型为图片时，有值）"""
	logoPictureUrl:String
	"""客服电话类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
		com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.CustomerServicePhoneTypes
	"""
	customerServicePhoneType:Int
	"""客服电话"""
	customerServicePhone:String
	"""客服电话图片路径"""
	customerServicePhonePictureUrl:String
	"""培训流程类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
		@see com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.TrainingProcessTypes
	"""
	trainingProcessType:Int
	"""培训流程图片
		@see com.fjhb.platform.jxjy.v1.api.trainingchannel.event.trainingchannelonlinecollective.Attachment
	"""
	trainingProcessAttachments:[AttachmentResponse]
	"""企业微信客服类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
	"""
	enterpriseWechatCustomerType:Int
	"""企业微信客服图片"""
	enterpriseWechatCustomerAttachments:[AttachmentResponse]
	"""咨询时间"""
	seekTime:String
	"""专题门户底部落款类型
		NET_SCHOOL= 1;同本网校
		CUSTOM = 2；自定义
		com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.PortalBottomShowTypes
	"""
	bottomShowType:Int
	"""专题门户底部落款内容（专题门户底部落款类型为自定义时 有值）"""
	bottomShowContent:String
	"""轮播图集合"""
	photos:[TrainingChannelTopicPhotoResponse]
}

scalar List
type TrainingChannelPageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingChannelPageResponse]}
type TrainingChannelSimplePageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingChannelSimplePageResponse]}
