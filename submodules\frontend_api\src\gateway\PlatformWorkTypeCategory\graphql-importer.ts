import getAllCategoryList from './queries/getAllCategoryList.graphql'
import getAllTopCategoryList from './queries/getAllTopCategoryList.graphql'
import getCategoryAndParents from './queries/getCategoryAndParents.graphql'
import getCategoryListByWorkTypeId from './queries/getCategoryListByWorkTypeId.graphql'
import getCategoryListByWorkTypeIds from './queries/getCategoryListByWorkTypeIds.graphql'
import getChildren from './queries/getChildren.graphql'
import getWorkTypeCategoryList from './queries/getWorkTypeCategoryList.graphql'
import createWorkTypeCategory from './mutates/createWorkTypeCategory.graphql'
import moveDownCategory from './mutates/moveDownCategory.graphql'
import moveUpCategory from './mutates/moveUpCategory.graphql'
import removeWorkTypeCategory from './mutates/removeWorkTypeCategory.graphql'
import updateWorkTypeCategory from './mutates/updateWorkTypeCategory.graphql'
import updateWorkTypeCategoryRelations from './mutates/updateWorkTypeCategoryRelations.graphql'

export {
  getAllCategoryList,
  getAllTopCategoryList,
  getCategoryAndParents,
  getCategoryListByWorkTypeId,
  getCategoryListByWorkTypeIds,
  getChildren,
  getWorkTypeCategoryList,
  createWorkTypeCategory,
  moveDownCategory,
  moveUpCategory,
  removeWorkTypeCategory,
  updateWorkTypeCategory,
  updateWorkTypeCategoryRelations
}
