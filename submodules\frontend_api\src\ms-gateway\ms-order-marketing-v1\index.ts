import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-order-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-order-marketing-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * <AUTHOR>
@date 2024/8/16 10:38
 */
export class ApplyEnterSaleChannelRequest {
  /**
   * 渠道类型，由枚举类ChannelType定义。
<p>
自营渠道 0
分销渠道 1
专题渠道 2
华医网 3
推广门户渠道 4
@see ChannelType
   */
  channelType?: number
  /**
   * 渠道类型ID，具体含义取决于渠道类型：
- 自营：网店ID；
- 专题：专题ID；
- 分销：分销商ID；
- 推广门户：推广门户唯一标识
   */
  channelTypeId?: string
}

/**
 * <AUTHOR>
@date 2024/8/16 10:38
 */
export class CalculateOrderPriceRequest {
  /**
   * 销售渠道购买凭证token,目前为销售渠道id
   */
  saleChannelPurchaseToken?: string
  /**
   * 商品id
   */
  commodityRequestList?: Array<CommodityRequest>
}

export class CommodityRequest {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品skuId
   */
  commoditySkuId?: string
  /**
   * 商品使用的价格策略类型 1-定价策略 2-优惠策略
   */
  policyType?: number
  /**
   * 策略id
若使用定价策略则为定价策略id,若使用优惠策略则为优惠策略id
   */
  policyId?: string
}

/**
 * <AUTHOR>
@date 2024/8/16 10:38
 */
export class ApplyEnterSaleChannelResponse {
  /**
   * 销售渠道购买凭证,目前为销售渠道id
   */
  saleChannelPurchaseToken: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * <AUTHOR>
@date 2024/8/16 10:38
 */
export class CalculateOrderPriceResponse {
  /**
   * 子订单价格信息
   */
  subOrderPriceInfoList: Array<SubOrderPriceInfo>
  /**
   * 总价格
   */
  totalAmount: number
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

export class SubOrderPriceInfo {
  /**
   * 子订单订单价格
   */
  amount: number
  /**
   * 商品skuId
   */
  commoditySkuId: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyEnterSaleChannel(
    request: ApplyEnterSaleChannelRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyEnterSaleChannel,
    operation?: string
  ): Promise<Response<ApplyEnterSaleChannelResponse>> {
    return commonRequestApi<ApplyEnterSaleChannelResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async calculateOrderPrice(
    request: CalculateOrderPriceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.calculateOrderPrice,
    operation?: string
  ): Promise<Response<CalculateOrderPriceResponse>> {
    return commonRequestApi<CalculateOrderPriceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
