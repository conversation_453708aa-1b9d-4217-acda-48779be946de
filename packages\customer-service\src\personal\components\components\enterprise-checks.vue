<template>
  <div class="center">
    <el-input
      id="openUnit"
      v-model="unitName"
      placeholder="请选择工作单位（全称）"
      @focus="openUnit()"
      clearable
      @clear="clear"
    ></el-input>
    <!--选择单位-->
    <el-drawer title="选择单位" :visible.sync="unitDialog" size="900px" custom-class="m-drawer" :modal="false">
      <div class="drawer-bd">
        <!--条件查询-->
        <el-row :gutter="0" class="m-query">
          <el-form :inline="true" label-width="auto" @submit.native.prevent>
            <el-col :span="12">
              <el-form-item>
                <el-input v-model="enterpriseInformation.key" clearable placeholder="请输入单位名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item>
                <el-button type="primary" :disabled="textlength || loading" @click="doSearch">查询</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <div class="clear f-mb30">
          <div class="f-fl f-ci">请至少输入4个关键字</div>
          <div class="f-fr" v-if="deepSearch" @click="addUnit()">
            <a class="f-cb">找不到单位，手动输入！</a>
          </div>
          <div class="f-fr" v-else @click="enterDeepSearch()">
            <a class="f-cb">还是找不到单位，进行深度搜索！</a>
          </div>
        </div>
        <!--表格-->
        <el-table
          stripe
          :data="enterpriseList"
          max-height="500px"
          class="m-table"
          v-loading="loading"
          v-if="enterpriseList.length"
        >
          <el-table-column label="单位名称" align="center">
            <template v-slot="{ row }"><span v-html="showUnitName(row.name)"></span></template>
          </el-table-column>
          <el-table-column label="社会统一信用码" align="center">
            <template v-slot="{ row }">{{ row.code }}</template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template v-slot="{ row }">
              <el-button type="text" size="mini" @click="selectUnit(row)"
                >{{ row.code == unitObject.code ? '取消选择' : '选择' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="m-no-date" v-else v-loading="loading">
          <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
          <div class="date-bd">
            <p class="f-f15 f-c9" v-if="noQueryUnitName">未搜索到您的单位信息</p>
            <p class="f-f15 f-c9" v-else>请输入单位名称，搜索您的单位信息</p>
          </div>
        </div>
        <!--分页-->
        <hb-pagination
          style="margin-top: 10px"
          v-if="enterpriseInformation.enterpriseList.length"
          :page="page"
          v-bind="page"
        >
        </hb-pagination>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button @click="cencelUnit()">取消</el-button>
        <el-button type="primary" @click="sureUnit()">确定</el-button>
      </div>
    </el-drawer>

    <!--新增单位-->
    <el-drawer title="新增单位" :visible.sync="addUnitDialog" size="600px" custom-class="m-drawer" :modal="false">
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert">
          为确保单位数据真实性，请输入单位全称，及统一社会信用代码。
        </el-alert>
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form
              ref="addUnitRef"
              :model="addEnterpriseItem"
              :rules="rules"
              label-width="auto"
              class="m-form f-mt20"
            >
              <el-form-item label="单位名称：" required prop="name">
                <el-input v-model="addEnterpriseItem.name" placeholder="请输入单位全称" />
              </el-form-item>
              <el-form-item label="统一社会信用代码：" required prop="code">
                <el-input
                  v-model="addEnterpriseItem.code"
                  placeholder="请输入18位有效的统一社会信用代码"
                  maxlength="18"
                  @change="toUpFunc"
                />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="cencelAddUnit()">取消</el-button>
                <el-button type="primary" @click="sureAddUnit()">确定</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import { ElForm } from 'element-ui/types/form'
  import { TypeEnum } from '@api/service/common/enterprise-Information/enums/TypeEnum'
  import EnterpriseInformation from '@api/service/common/enterprise-Information/EnterpriseList'
  import EnterpriseItem from '@api/service/common/enterprise-Information/EnterpriseItem'
  import { forEach } from 'lodash'

  @Component
  export default class extends Vue {
    @Ref('addUnitRef')
    addUnitRef: ElForm

    constructor() {
      super()
      this.page = new UiPage(this.searchUnit, this.searchUnit)
    }

    @Watch('addEnterpriseItem.code')
    enterpriseItemToUpperCase(val: string) {
      this.addEnterpriseItem.code = this.addEnterpriseItem.code.toUpperCase()
    }

    /**
     * 企查查是否配置
     */
    @Prop({
      type: Boolean,
      default: false
    })
    qccOption: boolean
    /**
     * 企查查是否配置
     */
    @Prop({
      type: Boolean,
      default: false
    })
    tycOption: boolean
    /**
     * 工作单位名称
     */
    @Prop({
      type: String,
      default: ''
    })
    unitName: string

    // 加载动画
    loading = false
    // 分页
    page: UiPage
    // 选择单位弹框
    unitDialog = false
    // 添加单位弹框
    addUnitDialog = false
    // 深度搜索
    deepSearch = false
    // 单位选择列表
    unitObject: EnterpriseItem = new EnterpriseItem()
    // 天眼查企查查枚举
    TypeEnum = TypeEnum
    // 是天眼查还是企查查
    queryType = 0
    // 天眼查企查查模型
    enterpriseInformation = new EnterpriseInformation()
    // 企业列表
    enterpriseList: Array<EnterpriseItem> = []
    // 新增企业
    addEnterpriseItem = new EnterpriseItem()
    // 选中的item
    selectObject = new EnterpriseItem()
    // 未搜索到单位名称
    noQueryUnitName = false
    rules = {
      name: [
        { required: true, message: '请填写单位名称', trigger: 'blur' },
        { required: true, min: 4, message: '请填写单位全称，至少4个字', trigger: ['blur', 'change'] }
      ],
      code: [
        { required: true, message: '请填写正确的统一社会信用代码', trigger: 'change' },
        {
          validator: this.taxpayerNoValidate,
          trigger: ['blur']
        }
      ]
    }

    /**
     * -------------------------------查询方法-------------------------------
     */

    /**
     * 查询口
     */
    async doSearch() {
      this.loading = true
      const res = await this.enterpriseInformation.queryInformationByFuzzyInPublic()
      if (res.isSuccess()) {
        this.page.currentChange(1)
      } else {
        console.error(res.getMessage() as string)
        this.noQueryUnitName = true
        this.loading = false
      }
    }

    /**
     * 查询工作单位
     */
    async searchUnit() {
      try {
        this.loading = true
        this.enterpriseList = await this.enterpriseInformation.pageInformationByFuzzyInPublic(this.page as any)
        if (this.enterpriseList.length) {
          this.noQueryUnitName = false
        } else {
          this.noQueryUnitName = true
        }
      } catch (e) {
        console.error(e)
      } finally {
        this.loading = false
      }
    }

    /**
     * -------------------------------选择单位事件交互-------------------------------
     */

    /**
     * 打开选择单位弹框
     */
    openUnit() {
      this.selectObject = new EnterpriseItem()
      this.unitObject = new EnterpriseItem()
      this.deepSearch = false
      this.noQueryUnitName = false
      const inputEl = document.getElementById('openUnit')
      inputEl.blur()
      this.hasTyQccOption()
      this.enterpriseInformation.key = ''
      this.enterpriseList = new Array<EnterpriseItem>()
      this.enterpriseInformation.type = this.queryType
      this.unitDialog = true
      if (this.enterpriseInformation.key) {
        this.searchUnit()
      }
    }

    /**
     * 工作单位选中
     */
    selectUnit(row: EnterpriseItem) {
      if (this.unitObject.code == row.code) {
        this.unitObject = new EnterpriseItem()
      } else {
        this.unitObject = row
      }
    }

    /**
     * 取消选择单位
     */
    cencelUnit() {
      this.unitDialog = false
      this.unitObject = this.selectObject
    }

    /**
     * 确认选择单位
     */
    sureUnit() {
      if (!this.unitObject.name && !this.unitObject.code) {
        this.$message.error('还未选择对应单位，请选择。')
        return
      }
      this.$emit('unitObject', this.unitObject)
      this.unitName = this.unitObject.name
      this.selectObject = this.unitObject
      this.unitDialog = false
    }

    clear() {
      this.$emit('unitObject', new EnterpriseItem())
      this.unitObject = new EnterpriseItem()
    }

    /**
     * -------------------------------添加单位弹窗事件交互-------------------------------
     */

    /**
     * 打开添加单位弹窗（找不到单位，手动输入！）
     */
    addUnit() {
      if (this.addUnitRef) {
        this.addUnitRef.clearValidate()
      }
      this.unitDialog = false
      this.addUnitDialog = true
    }

    /**
     * 确认添加单位
     */
    sureAddUnit() {
      this.loading = false
      this.addUnitRef.validate(async (value) => {
        if (value) {
          this.unitDialog = false
          this.addUnitDialog = false
          this.unitObject = this.addEnterpriseItem
          this.unitName = this.unitObject.name
          this.$emit('unitObject', this.unitObject)
        } else {
          this.loading = false
        }
      })
    }

    /**
     * 取消添加单位
     */
    cencelAddUnit() {
      this.addEnterpriseItem = new EnterpriseItem()
      this.unitDialog = true
      this.addUnitDialog = false
    }

    /**
     * -------------------------冗余代码区分线-------------------------
     */

    /**
     * 统一社会信用代码验证
     */
    taxpayerNoValidate(rule: any, value: any, callback: any) {
      ;/^[A-Za-z0-9]{18}$/.test(value) ? callback() : callback(new Error('请输入正确的18位统一社会信用代码'))
    }

    /**
     * 进入深度搜索
     */
    enterDeepSearch() {
      if (this.textlength) {
        this.$message.error('请至少输入4个关键字进行深度搜索！')
        return
      }
      this.enterpriseInformation.type = TypeEnum.qcc
      if (this.enterpriseInformation.key) {
        this.doSearch()
      }
      this.deepSearch = true
    }

    /**
     * 字体长度是否符合要求（大于等于四个字）
     */
    get textlength() {
      return this.enterpriseInformation.key.length < 4
    }

    hasTyQccOption() {
      if (this.qccOption && this.tycOption) {
        this.deepSearch = false
        this.queryType = TypeEnum.tyc
      } else if (this.qccOption) {
        this.deepSearch = true
        this.queryType = TypeEnum.qcc
      } else {
        this.deepSearch = true
        this.queryType = TypeEnum.tyc
      }
    }

    showUnitName(name: string) {
      this.enterpriseInformation.key.split('')
      const escapeRegex = (text: string) => {
        return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')
      }
      const wordPattern = this.enterpriseInformation.key
        .split('')
        .map((word) => escapeRegex(word))
        .join('|')
      const regex = new RegExp(wordPattern, 'gi')
      return name.replace(regex, `<span style="color: red">$&</span>`)
    }

    get toUpFunc() {
      return (this.addEnterpriseItem.code = this.addEnterpriseItem.code.toUpperCase())
    }
  }
</script>
<style scoped lang="scss">
  .f-fl {
    float: left;
  }

  .f-fr {
    float: right;
  }

  .f-ci {
    color: #e93737;
  }

  .f-cb {
    color: #1f5cc7;
  }

  .el-form-item .el-form-item {
    margin-bottom: 22px;
  }
</style>
