import batchGetTrainingInstitutionPortalInfoGQL from './graphql/batchGetTrainingInstitutionPortalInfo.graphql'
import { DocumentNode } from 'graphql/index'
import { Response } from '@hbfe/common'
import {
  PortalInfoResponse1,
  requestConfig,
  SERVER_URL
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import request from '@api/request'

/**
 * 【重写】批量查询门户配置信息
 * @param params 查询参数
 * @param params.portalType0 门户类型
 * @param params.portalType1 门户类型
 * @param params.servicerId 服务商id
 * @param serviceCapability
 * @param query 查询GQL语句
 * @param operation
 */
export const batchGetTrainingInstitutionPortalInfo = async (
  params: { portalType0: number; portalType1: number; servicerId?: string },
  serviceCapability = '',
  query: DocumentNode = batchGetTrainingInstitutionPortalInfoGQL,
  operation?: string
): Promise<Response<{
  fn0: PortalInfoResponse1
  fn1: PortalInfoResponse1
}>> => {
  return request<{
    fn0: PortalInfoResponse1
    fn1: PortalInfoResponse1
  }>(
    SERVER_URL,
    {
      query: query,
      variables: {
        portalType0: params.portalType0,
        portalType1: params.portalType1,
        servicerId: params.servicerId
      },
      operation: operation
    },
    Object.assign(requestConfig, {
      returnTypeCryptoForGraphql: false,
      returnTypeNoCryptoForGraphql: false,
      serviceCapability: serviceCapability,
      isUnAuthorize: true
    })
  )
}
