import AbstractEnum from '@api/service/common/enums/AbstractEnum'
import { BatchOrderTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'

/**
 * @description 【集体报名订单】发票状态枚举
 */
export enum BatchOrderInvoiceStatusEnum {
  // 待开票
  Wait_For_Invoice = 1,
  // 已开票
  Complete,
  // 冻结中
  Frozen,
  // 已作废
  Invalid
}

/**
 * @description 【集体报名订单】发票状态
 */
class BatchOrderInvoiceStatus extends AbstractEnum<BatchOrderInvoiceStatusEnum> {
  static enum = BatchOrderInvoiceStatusEnum
  constructor(status?: BatchOrderTradeStatusEnum) {
    super()
    this.map.set(BatchOrderInvoiceStatusEnum.Wait_For_Invoice, '待开票')
    this.map.set(BatchOrderInvoiceStatusEnum.Complete, '已开票')
    this.map.set(BatchOrderInvoiceStatusEnum.Frozen, '冻结中')
    this.map.set(BatchOrderInvoiceStatusEnum.Invalid, '已作废')
  }
}

export default new BatchOrderInvoiceStatus()
