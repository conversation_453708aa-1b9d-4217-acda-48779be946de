<template>
  <el-drawer title="选择模板" :visible.sync="openDialog" size="600px" custom-class="m-drawer">
    <div class="drawer-bd">
      <!--表格-->
      <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="模板名称" min-width="140">
          <template>这是期别完整名称这是期别完整名称</template>
        </el-table-column>
        <el-table-column label="模板说明" min-width="140" align="center">
          <template>这是培训方案完整名称</template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template slot-scope="scope">
            <div v-if="scope.$index === 0">
              <el-button type="text">取消选择</el-button>
            </div>
            <div v-else>
              <el-button type="text">选择</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <!--<el-pagination-->
      <!--  background-->
      <!--  class="f-mt15 f-tr"-->
      <!--  @size-change="handleSizeChange"-->
      <!--  @current-change="handleCurrentChange"-->
      <!--  :current-page="currentPage4"-->
      <!--  :page-sizes="[100, 200, 300, 400]"-->
      <!--  :page-size="100"-->
      <!--  layout="total, sizes, prev, pager, next, jumper"-->
      <!--  :total="400"-->
      <!--&gt;-->
      <!--</el-pagination>-->
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="openDialog = false">返回</el-button>
      <el-button type="primary">确定</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'

  @Component({})
  export default class extends Vue {
    // todo
    form = [{}]
    openDialog = false
    tableData = [{}]
    radio = ''
    input = ''
  }
</script>
<style scoped lang="scss"></style>
