<template>
  <div>
    <!-- 查看详情抽屉 -->
    <el-drawer title="管理员详情" :visible.sync="isShowDrawer" size="700px" custom-class="m-drawer"  :wrapper-closable="false"
               :close-on-press-escape="false">
      <div class="drawer-bd">
        <div class="m-tit"><span class="tit-txt">基本信息</span></div>
        <el-form ref="form" :model="userDetail" label-width="110px" class="m-text-form f-mt10">
          <el-form-item label="帐号：">{{ userDetail.adminAccount }}</el-form-item>
          <el-form-item label="姓名 / 昵称：">{{ userDetail.userName }}</el-form-item>
          <el-form-item label="性别：">{{ userDetail.gender == 1 ? '男' : '女' }}</el-form-item>
          <el-form-item label="手机号：">{{ userDetail.phone || '-' }}</el-form-item>
          <el-form-item label="邮箱：">{{ userDetail.email || '-' }}</el-form-item>
        </el-form>
        <div class="m-tit f-mt20"><span class="tit-txt">角色</span></div>
        <el-table :data="roleInfoList" max-height="500px" class="m-table" v-if="roleInfoList.length">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="角色" min-width="230">
            <template slot-scope="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column label="说明" min-width="250">
            <template slot-scope="scope">{{ scope.row.description || '-' }}</template>
          </el-table-column>
        </el-table>
        <el-empty slot="empty" :image-size="40" description="暂无数据，请添加角色~" v-if="!roleInfoList.length" />
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import PageAdminInfoResponse from '@api/service/management/user/query/manager/vo/PageAdminInfoResponse'
  import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import { UiPage } from '@hbfe/common'
  import { QueryRoleList } from '@api/service/management/authority/role/query/QueryRoleList'
  @Component
  export default class extends Vue {
    getQueryRoleList: QueryRoleList = AuthorityModule.roleFactory.getQueryRoleList()
    roleInfoList: Array<RoleInfoResponseVo> = new Array<RoleInfoResponseVo>()
    drawerForm = {
      name: 'sdfs'
    }
    /**
     * 是否展示
     */
    @Prop({
      required: true,
      type: Boolean,
      default: false
    })
    value: boolean
    @Prop({
      required: true,
      type: PageAdminInfoResponse
    })
    userDetail: PageAdminInfoResponse
    @Watch('value', {
      immediate: true,
      deep: true
    })
    valueChange(val: boolean) {
      this.isShowDrawer = cloneDeep(val)
    }
    @Emit('input')
    @Watch('isShowDrawer', {
      immediate: true,
      deep: true
    })
    showRoleDialogChange(val: number) {
      return val
    }
    isShowDrawer = false
    async created() {
      await this.getQueryRoleList.queryRoleList()
      this.roleInfoList = this.getQueryRoleList.roleList

      this.roleInfoList = this.roleInfoList.filter(
        item =>
          this.userDetail.roleList &&
          this.userDetail.roleList.length &&
          this.userDetail.roleList.some(ele => ele.roleId === item.id)
      )
    }
  }
</script>

<style scoped></style>
