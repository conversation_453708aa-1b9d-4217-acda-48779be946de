<template>
  <baseInfo-diff
    v-bind="$attrs"
    ref="basicInfoFormDiff"
    :contextTitle="contextTitle"
    :context="context"
    v-on="$listeners"
  >
    <template #thirdPlatformId="{ form, createMode }">
      <el-form label-width="150px" class="m-form" v-if="defaultSchemeTypeDiff === 'train_cooperate'">
        <el-form-item
          label="指定平台："
          prop="thirdPlatformId"
          v-if="createMode === 1 && queryPlatform.list.length > 1"
        >
          <div slot="label">
            <span class="f-cr f-mr5">*</span>
            <span class="f-vm">指定平台</span>
            <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
              <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
              <div slot="content">
                <p>指定平台配置说明：</p>
                <p>
                  如本培训方案类型选择合作办学，需指定方案合作的平台名称。请注意培训方案一经过发布该配置不支持修改。
                </p>
              </div>
            </el-tooltip>
            <span>：</span>
          </div>
          <el-select v-model="form.thirdPlatformId" clearable placeholder="请选择指定的平台名称" class="form-l">
            <el-option
              v-for="item in queryPlatform.list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
            <!-- <el-option value="选项2"></el-option> -->
          </el-select>
        </el-form-item>
        <el-form-item
          label="指定平台："
          prop="thirdPlatformId"
          v-if="createMode !== 1 || queryPlatform.list.length === 1"
          >{{ thirdPlatformName(form) }}</el-form-item
        >
      </el-form>
    </template>
  </baseInfo-diff>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  //   import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  import QueryPlatform from '@api/service/diff/common/zztt/dictionary/QueryPlatform'
  import BaseInfo, { BasicInfoForm } from '@hbfe/jxjy-admin-scheme/src/components/basic-info.vue'
  import { AchievementExhibitionEnum } from '@api/service/common/enums/train-class/AchievementExhibitionEnum'
  import { ProxyRef } from '@api/service/common/utils/ProxyRefMethods'

  /**
   * 基础数据表单项
   */
  class BasicInfoFormDiff extends BasicInfoForm {
    // 指定平台id
    thirdPlatformId: string
  }
  @Component
  class BaseInfoDiff extends BaseInfo {
    queryPlatform = QueryPlatform

    async mounted() {
      // 指定平台字典口查询
      await this.queryPlatform.queryList()
    }

    /**
     * 表单验证页面绑定数据
     */
    form: BasicInfoFormDiff = {
      schemeType: 1,
      picture: '',
      name: '',
      needDataSync: null,
      notice: '',
      introContent: '',
      showNoticeDialog: false,
      thirdPlatformId: '',
      achievementExhibition: AchievementExhibitionEnum.signUpTime
    } as BasicInfoFormDiff

    initForm: BasicInfoFormDiff = {
      schemeType: 1,
      picture: '',
      name: '',
      needDataSync: null,
      notice: '',
      introContent: '',
      showNoticeDialog: false,
      thirdPlatformId: '',
      achievementExhibition: AchievementExhibitionEnum.signUpTime
    } as BasicInfoFormDiff

    /**
     * 培训班类型切换
     */
    async changeClassType(value: 'train_class' | 'train_cooperate') {
      if (value === 'train_class') {
        this.form.thirdPlatformId = ''
      } else if (value === 'train_cooperate') {
        await this.queryPlatform.queryList()
        this.form.thirdPlatformId = this.queryPlatform.list[0]?.id
      }
      if (value === 'train_class' && this.commodityBasicInfo.schemeType === this.schemeTypeEnum.trainingCooperation) {
        this.handleSchemeType(this.schemeTypeEnum.chooseCourseLearning)
      }
      this.$emit('update:defaultSchemeType', value)
    }
  }

  @Component({
    components: {
      BaseInfoDiff
    }
  })
  @ProxyRef('basicInfoFormDiff', true)
  export default class extends Vue {
    // 指定网校
    queryPlatform = QueryPlatform
    @Ref('basicInfoFormDiff') basicInfoFormDiff: BaseInfoDiff
    // 配置说明
    contextTitle = '配置说明'
    // 配置说明
    context =
      '合作办学是指不同培训机构之间，通过一定的合作形式，共同开展教育培训活动的一种模式。如当前方案与指定平台开展合作，请选择此类型。'

    /**
     * 培训班类型默认选中“培训班”
     */
    @Prop({
      type: String,
      default: 'train_class'
    })
    defaultSchemeTypeDiff!: 'train_class' | 'train_cooperate'

    /**
     * 获取指定平台名称
     */
    get thirdPlatformName() {
      return (form: BasicInfoFormDiff) => {
        return this.queryPlatform.map.get(form.thirdPlatformId)?.name
      }
    }
  }
</script>
