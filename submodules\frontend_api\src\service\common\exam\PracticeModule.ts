import PlatformExamGateway, {
  SingleQuestionAnswerParamDTO,
  SingleQuestionAnswerStatisticDTO
} from '@api/gateway/PlatformExam'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '../../../store'
import ConfigCenterModule from '../config-center/ConfigCenter'
import Response, { ResponseStatus } from '../../../Response'
import ExamGateway, { FavoriteQuestionRequest } from '@api/gateway/btpx@GeneralExam-default'
import $http from '@packages/request'
import { Role, RoleType, Secure } from '../../../Secure'
import SubmitAnswerPracticePaper from './interface/SubmitAnswerPracticePaper'
import AnswerRecordModule from '@api/service/customer/answer-record/AnswerRecordModule'
import AnswerPracticePaper from '@api/service/common/models/exam/answer/AnswerPracticePaper'
import AnswerQuestion, { QuestionAnswerResult } from '@api/service/common/models/exam/question/answer/AnswerQuestion'
import AnswerQuestionWithPosition from '@api/service/common/models/exam/question/answer/AnswerQuestionWithPosition'

export class SubmitQuestion {
  // 小题下标
  questionIndex: number
  // 子题下标
  childIndex: number
  // 回答结果
  submitAnswer: any
}

export class MarkQuestion {
  // 小题下标
  questionIndex: number
  // 子题下标
  childIndex: number
  // 是否标记
  flag: boolean
}

const urlPath = {
  loginIn: '/gateway/examination/loginin',
  enter: '/gateway/practiceAnswerPaper/',
  enterAndReturnSimpleInfo: '/gateway/practiceAnswerPaper/simple/',
  getAnswerQuestion: '/gateway/practiceAnswerPaper/simple/',
  getAnswerQuestions: '/gateway/practiceAnswerPaper/simple/',
  submitQuestion: '/gateway/practiceAnswerPaper/',
  viewAnswerPracticePaper: '/gateway/practiceAnswerPaper/',
  sign: '/gateway/practiceAnswerPaper/',
  submit: '/gateway/practiceAnswerPaper/',
  clearAnswer: '/gateway/practiceAnswerPaper/'
}

export interface IState {
  schemeId: string
  issueId: string
  answerPracticePaper: AnswerPracticePaper
  questionAnswerStatistic: Array<SingleQuestionAnswerStatisticDTO>
}

@Module({
  namespaced: true,
  name: 'CommonPracticeModule',
  store,
  dynamic: true
})
class PracticeModule extends VuexModule implements IState {
  schemeId = ''
  issueId = ''
  answerPracticePaper = new AnswerPracticePaper()
  questionAnswerStatistic = new Array<SingleQuestionAnswerStatisticDTO>()

  /**
   * 进入作答
   */
  @Role([RoleType.user])
  @Action
  async enter(params: { schemeId: string; issueId: string; answersId: string; simpleInfo: boolean }) {
    const examUrl = params.simpleInfo ? urlPath.enterAndReturnSimpleInfo : urlPath.enter
    const enterResponse: Response<any> = await $http.get(
      ConfigCenterModule.getIngressByName('ingress.exam') + examUrl + params.answersId
    )
    if (!enterResponse.status.isSuccess() || enterResponse.data.status === false) {
      return new ResponseStatus(500, enterResponse.data?.info || '进入作答失败')
    }

    const answerPracticePaper: AnswerPracticePaper = enterResponse.data?.info || new AnswerPracticePaper()
    this.setSchemeInfo(params)
    this.setAnswerPracticePaper(answerPracticePaper)
    if (!params.simpleInfo && answerPracticePaper.answerQuestions) {
      const questionIds = new Array<string>()
      answerPracticePaper.answerQuestions.forEach(question => {
        questionIds.push(question.questionId)
      })
      const response = await ExamGateway.validateFavorite({
        schemeId: this.schemeId,
        issueId: this.issueId,
        questionIds: questionIds
      })
      if (response.status.isSuccess()) {
        for (const question of answerPracticePaper.answerQuestions) {
          const param: FavoriteQuestionRequest = new FavoriteQuestionRequest()
          param.schemeId = this.schemeId
          param.issueId = this.issueId
          param.questionId = question.questionId
          question.favorite = response.data.some(p => p.questionId == question.questionId && p.favorite)
        }
      } else {
        return response.status
      }
    }
    this.setAnswerPracticePaper(answerPracticePaper)

    return new ResponseStatus(200)
  }

  @Role([RoleType.user])
  @Action
  async loadQuestionInfo(questionIndex: number) {
    const questionId = this.answerPracticePaper?.answerQuestions[questionIndex]?.questionId
    // const questionIndex = this.answerPracticePaper?.answerQuestions?.findIndex(p => p.questionId === questionId) || -1
    if (questionIndex === -1 || !questionId) {
      return new ResponseStatus(500, '试题不存在')
    } else if (this.answerPracticePaper?.answerQuestions[questionIndex].loaded) {
      return new ResponseStatus(200, '试题已加载')
    }
    const loadParam = {
      answerPaperIndex: this.answerPracticePaper.answerPaperIndex,
      answerPaperId: this.answerPracticePaper.id,
      questionIndex: questionIndex,
      questionId: questionId
    }
    const response: Response<any> = await $http.post(
      ConfigCenterModule.getIngressByName('ingress.exam') +
        urlPath.getAnswerQuestion +
        this.answerPracticePaper.answersId +
        '/question',
      loadParam
    )
    if (!response.status.isSuccess() || response.data.status === false) {
      return new ResponseStatus(500, response.data?.info || '获取试题信息失败')
    }
    const question = response.data.info
    const param: FavoriteQuestionRequest = new FavoriteQuestionRequest()
    param.schemeId = this.schemeId
    param.issueId = this.issueId
    param.questionId = question.id
    const favoriteResponse = await ExamGateway.isFavoriteQuestion(param)
    if (favoriteResponse.status.isSuccess()) {
      question.favorite = favoriteResponse.data
    }
    this.setAnswerQuestionToAnswerPaper({ questionIndex: questionIndex, answerQuestion: question })
    return new ResponseStatus(200)
  }
  @Role([RoleType.user])
  @Action
  async loadAnsweredQuestionInfo() {
    if (!this.answerPracticePaper) {
      return new ResponseStatus(500, '请先开始练习')
    }

    const loadParam = {
      answerPaperIndex: this.answerPracticePaper.answerPaperIndex,
      answerPaperId: this.answerPracticePaper.id,
      questionPositions: new Array<any>()
    }

    this.answerPracticePaper?.answerQuestions.forEach((answerQuestion, questionIndex) => {
      if (answerQuestion.answered && !answerQuestion.loaded) {
        const questionPosition = {
          questionIndex: questionIndex,
          questionId: answerQuestion.questionId
        }
        loadParam.questionPositions.push(questionPosition)
      }
    })

    if (loadParam.questionPositions.length == 0) {
      return new ResponseStatus(200)
    }

    const response: Response<any> = await $http.post(
      ConfigCenterModule.getIngressByName('ingress.exam') +
        urlPath.getAnswerQuestions +
        this.answerPracticePaper.answersId +
        '/questions',
      loadParam
    )
    if (!response.status.isSuccess() || response.data.status === false) {
      return new ResponseStatus(500, response.data?.info || '获取试题信息失败')
    }
    const questions = response.data.info

    const questionIds = new Array<string>()
    questions.forEach((question: AnswerQuestion) => {
      questionIds.push(question.questionId)
    })
    const response2 = await ExamGateway.validateFavorite({
      schemeId: this.schemeId,
      issueId: this.issueId,
      questionIds: questionIds
    })
    if (response2.status.isSuccess()) {
      for (const question of questions) {
        const param: FavoriteQuestionRequest = new FavoriteQuestionRequest()
        param.schemeId = this.schemeId
        param.issueId = this.issueId
        param.questionId = question.id
        question.favorite = response2.data.some(p => p.questionId == question.questionId && p.favorite)
        this.setAnswerQuestionToAnswerPaper({ questionIndex: question.questionIndex, answerQuestion: question })
      }
    } else {
      return response2.status
    }

    this.setAnswerPracticePaper(this.answerPracticePaper)

    return new ResponseStatus(200)
  }

  /**
   * 查看答卷
   */
  @Role([RoleType.user])
  @Action
  async viewAnswerPracticePaper(params: { schemeId: string; issueId: string; id: string; answersId: string }) {
    const enterResponse: Response<any> = await $http.get(
      ConfigCenterModule.getIngressByName('ingress.exam') +
        urlPath.viewAnswerPracticePaper +
        params.answersId +
        '/view',
      {
        params: {
          answerRecordId: params.id
        }
      }
    )
    if (!enterResponse.status.isSuccess() || enterResponse.data.status === false) {
      return new ResponseStatus(500, enterResponse.data?.info || '查看答卷失败')
    }

    const answerPracticePaper: AnswerPracticePaper = enterResponse.data?.info || new AnswerPracticePaper()
    this.setSchemeInfo(params)
    this.setAnswerPracticePaper(answerPracticePaper)

    const questionIds = new Array<string>()
    answerPracticePaper.answerQuestions.forEach(question => {
      questionIds.push(question.questionId)
    })
    const response = await ExamGateway.validateFavorite({
      schemeId: this.schemeId,
      issueId: this.issueId,
      questionIds: questionIds
    })
    if (response.status.isSuccess()) {
      for (const question of answerPracticePaper.answerQuestions) {
        const param: FavoriteQuestionRequest = new FavoriteQuestionRequest()
        param.schemeId = this.schemeId
        param.issueId = this.issueId
        param.questionId = question.questionId
        question.favorite = response.data.some(p => p.questionId == question.questionId && p.favorite)
      }
    } else {
      return response.status
    }
    this.setAnswerPracticePaper(answerPracticePaper)
    return new ResponseStatus(200)
  }

  /**
   * 标记试题
   * @param ctx
   * @param question
   */
  @Action
  async doChangeQuestionUnsure(param: MarkQuestion) {
    const sign: any = param
    console.log(param)
    const enterResponse: Response<any> = await $http.put(
      ConfigCenterModule.getIngressByName('ingress.exam') + urlPath.sign + this.answerPracticePaper.answersId + '/sign',
      sign
    )

    if (!enterResponse.status.isSuccess() || enterResponse.data.status === false) {
      return new ResponseStatus(500, enterResponse.data?.info || '标记试题失败')
    }
    this.setMarkQuestion(param)
    return new ResponseStatus(200)
  }

  /**
   * 回答试题
   * @param ctx
   * @param question
   */
  @Action
  async doAnswerQuestion(param: SubmitQuestion) {
    const submit: any = param
    this.setAnswer(submit)

    return new ResponseStatus(200)
  }

  /**
   * 提交试题
   * @param ctx
   * @param question
   */
  @Action
  async doSubmitQuestion(param: SubmitQuestion) {
    const submit: any = param
    const enterResponse: Response<any> = await $http.put(
      ConfigCenterModule.getIngressByName('ingress.exam') +
        urlPath.submitQuestion +
        this.answerPracticePaper.answersId +
        '/submitQA',
      submit
    )

    if (!enterResponse.status.isSuccess() || enterResponse.data.status === false) {
      return new ResponseStatus(500, enterResponse.data?.info || '提交试题失败')
    }
    this.setAnswer(submit)
    return new ResponseStatus(200)
  }

  /**
   * 提交试卷
   */
  @Action
  async doSubmitPaper(param: SubmitAnswerPracticePaper) {
    param.answersId = this.answerPracticePaper.answersId
    param.answerRecordId = this.answerPracticePaper.id
    const enterResponse: Response<any> = await $http.put(
      ConfigCenterModule.getIngressByName('ingress.exam') + urlPath.submit + param.answersId + '/submit',
      param
    )
    if (!enterResponse.status.isSuccess() || enterResponse.data.status === false) {
      return new ResponseStatus(500, enterResponse.data?.info || '提交试卷失败')
    }
    AnswerRecordModule.setNeedReload({
      schemeId: this.schemeId,
      issueId: this.issueId
    })
    this.ALERT_EXAM_RELOAD({
      schemeId: this.schemeId,
      issueId: this.issueId,
      answersId: this.answerPracticePaper.answersId
    })
    await this.viewAnswerPracticePaper({
      schemeId: this.schemeId,
      issueId: this.issueId,
      id: this.answerPracticePaper.id,
      answersId: this.answerPracticePaper.answersId
    })

    return new ResponseStatus(200)
  }

  /**
   * 获取答题解析
   */
  @Role([RoleType.user])
  @Action
  async loadQuestionAnswerStatistic(questionIds: Array<string>) {
    const paramDTO: SingleQuestionAnswerParamDTO = new SingleQuestionAnswerParamDTO()
    paramDTO.questionId = questionIds
    const response = await PlatformExamGateway.statisticUserSingleQuestionAnswerInfo(paramDTO)

    if (questionIds) {
      const result: Array<SingleQuestionAnswerStatisticDTO> = response?.data
      if (result) {
        const restQuestionIds = questionIds.filter(p => !result.some(r => r.questionId === p))
        const generateQuestions = restQuestionIds.map(id => {
          const result = new SingleQuestionAnswerStatisticDTO()
          result.questionId = id
          result.allAnsweredTimes = 0
          result.allCorrectRate = 0
          result.allCorrectTimes = 0
          result.allWrongTimes = 0
          result.allUnknownTimes = 0
          result.answeredTimes = 0
          result.correctRate = 0
          result.correctTimes = 0
          result.wrongTimes = 0
          result.unknownTimes = 0
          return result
        })
        result.push(...generateQuestions)
        this.setAnswerStatistic(result)
      }
    }

    return response.status
  }

  /**
   * 添加收藏
   */
  @Role([RoleType.user])
  @Action
  async addFavorite(questionId: string) {
    const addFavorite: FavoriteQuestionRequest = new FavoriteQuestionRequest()
    addFavorite.schemeId = this.schemeId
    addFavorite.issueId = this.issueId
    addFavorite.questionId = questionId
    const response = await ExamGateway.addUserFavoriteQuestion(addFavorite)
    // 收藏暂时没有刷新需求
    // AnswerRecordModule.setNeedReload({
    //   schemeId: this.schemeId,
    //   issueId: this.issueId
    // })
    this.ALERT_EXAM_RELOAD({
      schemeId: this.schemeId,
      issueId: this.issueId,
      answersId: this.answerPracticePaper.answersId
    })
    return response.status
  }

  /**
   * 移除收藏
   */
  @Role([RoleType.user])
  @Action
  async removeFavorite(questionId: string) {
    const addFavorite: FavoriteQuestionRequest = new FavoriteQuestionRequest()
    addFavorite.schemeId = this.schemeId
    addFavorite.issueId = this.issueId
    addFavorite.questionId = questionId
    const response = await ExamGateway.removeUserFavoriteQuestion(addFavorite)
    // AnswerRecordModule.setNeedReload({
    //   schemeId: this.schemeId,
    //   issueId: this.issueId
    // })
    this.ALERT_EXAM_RELOAD({
      schemeId: this.schemeId,
      issueId: this.issueId,
      answersId: this.answerPracticePaper.answersId
    })
    return response.status
  }
  //清空答案
  @Action
  async clearAnswers() {
    const param: any = {}
    param.answersId = this.answerPracticePaper.answersId
    const enterResponse: Response<any> = await $http.post(
      ConfigCenterModule.getIngressByName('ingress.exam') + urlPath.clearAnswer + param.answersId + '/clearAnswer'
    )
    if (!enterResponse.status.isSuccess() || enterResponse.data.status === false) {
      return new ResponseStatus(500, enterResponse.data?.info || '清空答案失败')
    }
    const status: ResponseStatus = await this.viewAnswerPracticePaper({
      schemeId: this.schemeId,
      issueId: this.issueId,
      id: this.answerPracticePaper.id,
      answersId: this.answerPracticePaper.answersId
    })

    return status
  }

  @Mutation
  setAnswerPracticePaper(payload: AnswerPracticePaper) {
    this.answerPracticePaper = payload
  }

  @Mutation
  setSchemeInfo(payload: { schemeId: string; issueId: string }) {
    this.schemeId = payload.schemeId
    this.issueId = payload.issueId
  }

  @Mutation
  setAnswer(payload: SubmitQuestion) {
    if (payload.childIndex !== -1) {
      this.answerPracticePaper.answerQuestions[payload.questionIndex].childrenAnswers[payload.childIndex].submitAnswer =
        payload.submitAnswer
      this.answerPracticePaper.answerQuestions[payload.questionIndex].childrenAnswers[payload.childIndex].answered =
        payload.submitAnswer === false || !!payload.submitAnswer
    } else {
      this.answerPracticePaper.answerQuestions[payload.questionIndex].submitAnswer = payload.submitAnswer
      this.answerPracticePaper.answerQuestions[payload.questionIndex].answered =
        payload.submitAnswer === false || !!payload.submitAnswer
    }
  }

  @Mutation
  setMarkQuestion(payload: MarkQuestion) {
    if (payload.childIndex !== -1) {
      this.answerPracticePaper.answerQuestions[payload.questionIndex].childrenAnswers[payload.childIndex].flag =
        payload.flag
    } else {
      this.answerPracticePaper.answerQuestions[payload.questionIndex].flag = payload.flag
    }
  }

  @Mutation
  setAnswerStatistic(payload: Array<SingleQuestionAnswerStatisticDTO>) {
    this.questionAnswerStatistic = this.questionAnswerStatistic.filter(
      statistic => !payload.some(p => p.questionId === statistic.questionId)
    )
    this.questionAnswerStatistic.push(...payload)
  }

  @Mutation
  setAnswerQuestionToAnswerPaper(payload: { questionIndex: number; answerQuestion: any }) {
    this.answerPracticePaper.answerQuestions[payload.questionIndex] = payload.answerQuestion
    this.answerPracticePaper.answerQuestions[payload.questionIndex].loaded = true
  }

  @Mutation
  ALERT_EXAM_RELOAD(payload: any) {
    console.log('提醒需要重载' + JSON.stringify(payload))
  }

  /**
   * 获取 试题作答统计
   * @param ctx
   */
  get getQuestionAnswerStatistic() {
    return (questionIds: Array<string>) => {
      return this.questionAnswerStatistic.filter(p => questionIds.some(questionId => questionId === p.questionId))
    }
  }

  /**
   * 获取当前已批改答卷的错题（若未批改，数据为空）
   */
  get getMarkedAnswerPracticePaperWrongQuestionsWithPosition() {
    return () => {
      const questions = new Array<AnswerQuestionWithPosition>()
      if (this.answerPracticePaper.marked) {
        this.answerPracticePaper.answerQuestions.forEach((answerQuestion, index) => {
          if (
            answerQuestion.answerResult === QuestionAnswerResult.WRONG ||
            answerQuestion.answerResult === QuestionAnswerResult.PARTIALLY
          ) {
            const answerQuestionWithPosition: AnswerQuestionWithPosition = new AnswerQuestionWithPosition()
            Object.assign(answerQuestionWithPosition, answerQuestion)
            answerQuestionWithPosition.position = index
            questions.push(answerQuestionWithPosition)
          }
        })
      }
      return questions
    }
  }
}

export default getModule(PracticeModule)
