import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import DatumConfigModel from '@api/service/common/models/anticheat/DatumConfigModel'
import { ResponseStatus } from '@api/Response'
import AntiCheatGateway from '@api/gateway/AntiCheat-default'
import moment from 'moment'
import { Constants } from '@api/service/common/models/common/Constants'
import { Role, RoleType } from '@api/Secure'

interface DatumConfigState {
  datumConfig: DatumConfigModel
  isLoadDatumConfig: boolean
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerDatumConfigModule', store })
class DatumConfigModule extends VuexModule implements DatumConfigState {
  public datumConfig: DatumConfigModel = new DatumConfigModel()
  public isLoadDatumConfig = false

  /**
   * 加载基准照采集配置
   */
  @Action
  @Role([RoleType.user])
  public async init(): Promise<ResponseStatus> {
    if (!this.isLoadDatumConfig) {
      const { status, data } = await AntiCheatGateway.findDatumConfigByPlatform()
      if (status.isSuccess()) {
        if (data) {
          const config: DatumConfigModel = new DatumConfigModel()
          config.protocolText = data.protocolText
          config.id = data.id
          config.collectCount = data.collectCount
          if (data.createTime) {
            config.createTime = moment(data.createTime, Constants.DATE_PATTERN).toDate()
          }
          if (data.updateTime) {
            config.updateTime = moment(data.updateTime, Constants.DATE_PATTERN).toDate()
          }
          config.updateCount = data.updateCount
          config.updateUserId = data.updateUserId
          config.createUserId = data.createUserId
          config.enable = data.enable
          this.SET_DATUM_CONFIG(config)
        }
      } else {
        return Promise.reject(status)
      }
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 重新加载基准照采集配置
   */
  @Action
  @Role([RoleType.user])
  public async doReloadConfig(): Promise<ResponseStatus> {
    this.SET_IS_LOAD_DATUM_CONFIG(false)
    return this.init()
  }

  @Mutation
  @Role([RoleType.user])
  private SET_DATUM_CONFIG(config: DatumConfigModel) {
    this.isLoadDatumConfig = true
    this.datumConfig = config
  }

  @Mutation
  private SET_IS_LOAD_DATUM_CONFIG(isload: boolean) {
    this.isLoadDatumConfig = isload
  }

  /**
   * 是否启用基准照采集
   */
  get enable(): boolean {
    if (this.datumConfig?.id) {
      return this.datumConfig.enable
    }
    return false
  }
}

export default getModule(DatumConfigModule)
