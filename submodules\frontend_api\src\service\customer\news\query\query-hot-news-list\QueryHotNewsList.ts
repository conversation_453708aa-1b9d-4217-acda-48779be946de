/*
 * @Description: 获取热门资讯
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-24 20:04:16
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 14:26:36
 */
import { Response } from '@hbfe/common'
import HotNewsVo from '@api/service/customer/news/query/query-hot-news-list/vo/HotNewsVo'

import BasicDataQueryForestage, {
  TrainingChannelReviewTopNewsCommonRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { uniq } from 'lodash'
/**
 * 获取热门资讯
 */
class QueryHostNewsList {
  /**
   * @description: 获取热门资讯
   * @param {Number} count 请求热门资讯数量
   * @return {*} Response<Array<HotNewsVo>>
   * @author: <PERSON>
   */
  async queryHostNewsList(topNum = 5): Promise<Array<HotNewsVo>> {
    const { data } = await BasicDataQueryForestage.listReviewTopNews({ topNum })
    const hotNewsArr = new Array<HotNewsVo>()
    const response = await BasicDataQueryForestage.listRootNewsCategory(1)
    // * 目前只有帮助中心有二级分类
    // const category = response.data.find(res => res.categoryName.indexOf('帮助中心') > -1)
    // const childResponse = await BasicDataQueryForestage.listChildNewsCategory({
    //   status: 1,
    //   necId: category.newsCategoryId
    // })
    const temp = response.data
    // temp.push(...childResponse.data.map(res => res))
    data.map(item => hotNewsArr.push(HotNewsVo.from(item)))
    hotNewsArr.map(res => {
      const curCategory = temp.find(category => category.newsCategoryId === res.necId)
      if (curCategory) {
        res.necName = curCategory.categoryName
      }
    })
    console.log('hotNewsArr', hotNewsArr)
    return hotNewsArr
  }

  /**
   * @description: 获取专题热门资讯
   * @param specialId 专题
   * @param topNum count 请求热门资讯数量
   */
  async querySpecialHostNewsList(specialId: string, topNum = 5): Promise<Array<HotNewsVo>> {
    const request = new TrainingChannelReviewTopNewsCommonRequest()
    request.specialSubjectId = specialId
    request.topNum = topNum
    const { data } = await BasicDataQueryForestage.listTrainingChannelReviewTopNews(request)
    const hotNewsArr = new Array<HotNewsVo>()
    const response = await BasicDataQueryForestage.listRootNewsCategory(1)
    const temp = response.data
    data?.length && data.map(item => hotNewsArr.push(HotNewsVo.from(item)))
    hotNewsArr.map(res => {
      const curCategory = temp.find(category => category.newsCategoryId === res.necId)
      if (curCategory) {
        res.necName = curCategory.categoryName
      }
    })
    return hotNewsArr
  }
}
export default QueryHostNewsList
