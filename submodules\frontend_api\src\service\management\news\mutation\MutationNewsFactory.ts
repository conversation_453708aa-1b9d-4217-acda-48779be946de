/*
 * @Description: 资讯业务工厂
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-09 18:38:33
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-17 15:24:29
 */
import MutationCreateNews from '@api/service/management/news/mutation/mutation-news-created/MutationCreateNews'
import MutationNewsChangeStatus from '@api/service/management/news/mutation/mutation-news-change-status/MutationNewsChangeStatus'
import MutationNewsDelete from '@api/service/management/news/mutation/mutation-news-delete/MutationNewsDelete'
import MutationNewsUpdate from '@api/service/management/news/mutation/mutation-news-update/mutationNewsUpdate'
class MutationNewsFactory {
  /**
   * 创建资讯
   * @returns
   */
  mutationCreateNews() {
    return new MutationCreateNews()
  }
  /**
   * 资讯状态转换 草稿↔↔发布
   */
  mutationNewsChangeStatus(id: string) {
    return new MutationNewsChangeStatus(id)
  }
  /**
   * 删除资讯
   */
  mutationNewsDelete() {
    return new MutationNewsDelete()
  }
  /**
   * 更新资讯
   */
  mutationNewsUpdate() {
    return new MutationNewsUpdate()
  }
}

export default MutationNewsFactory
