import PeriodImplementBase from '@api/service/management/implement/models/PeriodImplementBase'
import { SchemeIssueRegistrationResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

export default class PeriodResult extends PeriodImplementBase {
  /**
   * 合格人数
   */
  qualifiedNum: number = undefined

  /*
   * 已报名人数
   */
  signedNumber: number = undefined

  constructor(periodId?: string) {
    super(periodId)
  }

  static from(dto: SchemeIssueRegistrationResponse) {
    const vo = new PeriodResult()
    const { issueConfig, signUpNum, qualifiedNum } = dto
    vo.qualifiedNum = qualifiedNum
    if (issueConfig) {
      vo.signedNumber = signUpNum || 0
      vo.id = issueConfig.issueId
      vo.schemeId = issueConfig.schemeId
      vo.no = issueConfig.issueNum
      vo.name = issueConfig.issueName
      vo.applicationData.begin = issueConfig.startSignUpTime
      vo.applicationData.end = issueConfig.endSignUpTime
      vo.applicantsNumber = issueConfig.allowSignUpNum
      vo.trainingTime.begin = issueConfig.startTrainTime
      vo.trainingTime.end = issueConfig.endTrainTime
    }

    return vo
  }
}
