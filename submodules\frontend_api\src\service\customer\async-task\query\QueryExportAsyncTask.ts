import AsyncTaskItemVo from './vo/AsyncTaskItemVo'
import { Page, Response } from '@hbfe/common'
import MsDataExportBackstageGateway, {
  UserJobLogResponse
} from '@api/platform-gateway/fxnl-data-export-gateway-backstage'
import JobRequestVo from './vo/JobRequestVo'
import MsBasicDataQueryBackstage, {
  AdminInfoResponse,
  AdminQueryRequest,
  AdminUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import OperateUser from './vo/OperateUser'
import PCollectivesign, {
  ExportTaskDataResponse,
  QueryTaskInfoResponse
} from '@api/platform-gateway/jxjy-collectivesign-v1'

/**
 * 查询异步任务列表
 */
class QueryExportAsyncTask {
  /**
   * @description: 查询导出任务列表 --- 成功
   * @param {Page} page
   * @param {JobRequestVo} request
   */
  async queryAsyncTaskList(page: Page, request: JobRequestVo): Promise<Response<Array<AsyncTaskItemVo>>> {
    const params = request.toDto()
    const res = await MsDataExportBackstageGateway.pageExportTaskInfoInMyself({ page: page, jobRequest: params })
    const response = new Response<Array<AsyncTaskItemVo>>()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }

    response.status = res.status
    response.data = new Array<AsyncTaskItemVo>()
    page.totalSize = res.data?.totalSize
    page.pageSize = res.data?.pageSize
    const idList = new Array<string>()
    res.data?.currentPageData?.forEach((userJobLogResponse: UserJobLogResponse) => {
      const asyncTaskItemVo = new AsyncTaskItemVo()
      idList.push(userJobLogResponse.operatorUserId)
      asyncTaskItemVo.from(userJobLogResponse)
      response.data?.push(asyncTaskItemVo)
    })
    // if (!idList?.length) {
    //   console.error('操作人id数组为空！')
    //   return response
    // }

    // const list: Array<OperateUser> = await this.queryOperateNameById(idList)
    // if (!list?.length) {
    //   console.error('查询操作人信息失败！')
    // } else {
    //   response.data?.forEach(far => {
    //     list.forEach(sub => {
    //       if (far.operatorUserId === sub.id) {
    //         far.operateUser = sub.name
    //       }
    //     })
    //   })
    // }
    return response
  }
  /**
   * @description: 查询导出任务列表 --- 失败
   * @param {Page} page
   * @param {string} batchNo 批次号
   */
  async queryAsyncFailTaskList(page: Page, batchNo: string): Promise<Response<Array<AsyncTaskItemVo>>> {
    const res = await PCollectivesign.queryForTask({
      page: page,
      request: {
        batchNo,
        category: 'EXCUTE_FAIL_EXPORT'
      }
    })
    const response = new Response<Array<AsyncTaskItemVo>>()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }

    response.status = res.status
    response.data = new Array<AsyncTaskItemVo>()
    page.totalSize = res.data?.totalSize
    page.pageSize = res.data?.pageSize
    res.data?.currentPageData?.forEach(item => {
      const asyncTaskItemVo = new AsyncTaskItemVo()
      asyncTaskItemVo.fromQueryTaskInfoResponse(item)
      response.data?.push(asyncTaskItemVo)
    })
    return response
  }
  /**
   * @description: 下载导出任务 --- 失败
   * @param {string}   taskId?: string 任务编号
   */
  async exportFail(taskId?: string): Promise<Response<ExportTaskDataResponse>> {
    const res = await PCollectivesign.exportFail({
      taskId
    })

    return res
  }

  /**
   * 根据操作人id查操作人名称
   * @param userIds
   * @private
   */
  private async queryOperateNameById(userIds: Array<string>): Promise<Array<OperateUser>> {
    const page = new Page()
    page.pageNo = 1
    page.pageSize = userIds?.length
    const params = new AdminQueryRequest()
    params.user = new AdminUserRequest()
    params.user.userIdList = new Array<string>()
    params.user.userIdList = [...new Set(userIds)]
    const res = await MsBasicDataQueryBackstage.pageAdminInfoInServicer({ page: page, request: params })
    const result = new Array<OperateUser>()
    if (!res.status?.isSuccess()) {
      return new Array<OperateUser>()
    }
    res.data?.currentPageData?.map((item: AdminInfoResponse) => {
      const user = new OperateUser()
      user.id = item.userInfo?.userId
      user.name = item.userInfo?.userName
      result.push(user)
    })
    return result
  }
}

export default QueryExportAsyncTask
