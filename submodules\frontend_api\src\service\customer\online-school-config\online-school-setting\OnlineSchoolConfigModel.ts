import MsSeries, { OnlineSchoolConfigResponse } from '@api/ms-gateway/ms-servicer-series-v1'
class OnlineSchoolConfigModel {
  /**
   * 配置
   */
  config: OnlineSchoolConfigResponse = new OnlineSchoolConfigResponse()
  /**
   * 获取网校配置
   */
  async getConfig() {
    const res = await MsSeries.getOnlineSchoolConfig()
    if (res.status.isSuccess() && res.data) {
      this.config = res.data
    } else {
      this.config = new OnlineSchoolConfigResponse()
    }
    return this.config
  }
}
export default new OnlineSchoolConfigModel()
