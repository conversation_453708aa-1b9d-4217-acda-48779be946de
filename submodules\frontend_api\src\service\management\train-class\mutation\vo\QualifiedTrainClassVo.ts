import { MetaData, OneKeyPassRequest } from '@api/platform-gateway/platform-learningscheme-v1'
import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'

/**
 * @description
 */
class QualifiedTrainClassVo {
  /**
   * 参训资格id
   */
  qualificationId = ''

  /**
   * 培训方案类型 1：选课规则 2：自主选课
   */
  schemeType: TrainClassSchemeEnum = null

  /**
   * 合格时间
   */
  qualifiedTime = ''

  /**
   * 考试合格分
   */
  examScore: number = null

  /**
   * 课程测验合格分
   */
  courseQuizScore: number = null

  to(): OneKeyPassRequest {
    const to = new OneKeyPassRequest()
    to.qualificationId = this.qualificationId
    to.passTimeType = 1
    to.metaDataList = [] as MetaData[]
    if (this.examScore) {
      const option = new MetaData()
      option.key = 'examLearning.qualifiedScore'
      option.value = String(this.examScore)
      to.metaDataList.push(option)
    }
    // if (this.courseQuizScore) {
    const option = new MetaData()
    if (this.schemeType === TrainClassSchemeEnum.Choose_Course_Learning) {
      // 选课规则
      option.key = 'chooseCourseLearning.courseQuizQualifiedScore'
    }
    if (this.schemeType === TrainClassSchemeEnum.Autonomous_Course_Learning) {
      // 自主选课
      option.key = 'autonomousCourseLearning.courseQuizQualifiedScore'
    }
    option.value = this.courseQuizScore ? String(this.courseQuizScore) : '0'
    to.metaDataList.push(option)
    // }
    // * 选择合格时间
    if (this.qualifiedTime) {
      to.passTimeType = 0
      const option = new MetaData()
      if (this.schemeType === TrainClassSchemeEnum.Choose_Course_Learning) {
        // 选课规则
        option.key = 'chooseCourseLearning.immediatelyTime'
      }
      if (this.schemeType === TrainClassSchemeEnum.Autonomous_Course_Learning) {
        // 自主选课
        option.key = 'autonomousCourseLearning.immediatelyTime'
      }
      option.value = String(new Date(this.qualifiedTime).getTime())
      to.metaDataList.push(option)
    }
    return to
  }
}

export default QualifiedTrainClassVo
