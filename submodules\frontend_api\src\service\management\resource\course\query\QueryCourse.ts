import {
  CoursewareSupplierRequest,
  CoursewareSupplierResponse,
  default as MsBasicdataQueryFrontGatewayBasicDataQueryBackstage,
  default as BasicDataQueryBackstage,
  ServicerBaseRequest,
  StudentInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import * as GraphqlImporter from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage/graphql-importer'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  CourseInfoRequest,
  CourseInSchemeRequest,
  CourseInSchemeResponse,
  CourseOfCourseTrainingOutlineRequest,
  CoursePackageRequest,
  CoursePeriodCountOfCourseTrainingOutlineResponse,
  CourseResponse,
  CourseResponsePage,
  CourseSortEnum,
  CourseSortRequest,
  CourseV2Request,
  RepeatedCoursesInSpecifiedCoursePackageRequest,
  RepeatedCoursesInSpecifiedCoursePackageResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import * as CourseGraphqlImporter from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage/graphql-importer'
import { getRepeatedCoursesInSpecifiedCoursePackageInServicer } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage/graphql-importer'
import MsCourseLearningQueryFrontGatewayCourseLearningForestage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import MsExamQueryFrontGatewayExamQueryBackStage, {
  CourseQuestionCountResponse,
  QuestionRequest
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import QueryTechnologyLevel from '@api/service/common/basic-data-dictionary/query/QueryTechnologyLevel'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import QueryCourseList from '@api/service/customer/course/query/QueryCourseList'
import QuerySchemePackageCourseListParams from '@api/service/customer/course/query/vo/QuerySchemePackageCourseListParams'
import TrainingOutlineCourse from '@api/service/customer/course/query/vo/TrainingOutlineCourse'
import QueryServiceProvider from '@api/service/management/authority/service-provider/query/QueryServiceProvider'
import CoursePackageDetailVo from '@api/service/management/resource/course-package/query/vo/CoursePackageDetailVo'
import OutlineCourseInfo from '@api/service/management/resource/course/models/OutlineCourseInfo'
import CourseDetail from '@api/service/management/resource/course/query/vo/CourseDetail'
import CourseEvaluateListDetail from '@api/service/management/resource/course/query/vo/CourseEvaluateListDetail'
import CourseInSchemeResult from '@api/service/management/resource/course/query/vo/CourseInSchemeResult'
import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'
import CourseStatisticVo from '@api/service/management/resource/course/query/vo/CourseStatisticVo'
import QueryCourseListParam from '@api/service/management/resource/course/query/vo/QueryCourseListParam'
import RepeatCourseListInCoursePackage from '@api/service/management/resource/course/query/vo/RepeatCourseListInCoursePackage'
import { CompulsoryCourseInfo } from '@api/service/management/train-class/mutation/vo/CompulsoryCourseInfo'
import { Page, UiPage } from '@hbfe/common'

class QueryCourse {
  /**
   * 供应商数据缓存
   */
  providerCache = new Map<string, CoursewareSupplierResponse>()
  /**
   * 课程试题数据缓存
   */
  courseQuestionCountMapCache = new Map<string, number>()

  /**
   * @deprecated
   * @param courseIdList
   * @private
   */
  private async listQuestionCountInCourses(courseIdList: Array<string>): Promise<Map<string, number>> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    const queryString = ['query pageQuestionInServicer($page: Page, ']
    const params: Array<string> = []
    const requestBody = {
      page
    }
    const map = new Map<string, number>()
    courseIdList.forEach((id: string, index: number) => {
      map.set(id, 0)
      params.push(`$request${index}: QuestionRequest`)
      const request = new QuestionRequest()
      request.relateCourseIds = [id]
      requestBody[`request${index}`] = request
      queryString.push(`
          page${index}:pageQuestionInServicer(page: $page, request: $request${index}) {
            totalSize
          }
      `)
    })
    queryString[0] = queryString[0] + params.join(',') + '){'
    queryString.push('}')
    const result = await MsExamQueryFrontGatewayExamQueryBackStage.pageQuestionInServicer(
      requestBody,
      queryString.join('\n') as any
    )
    const keys = Object.keys(result.data)
    keys.forEach((key: string, index: number) => {
      result.data[`page${index}`] && map.set(courseIdList[index], result.data[`page${index}`]?.totalSize)
    })
    return map
  }

  /**
   * 查询课程包集合
   * @param idList
   */
  private async listCoursePackage(idList: Array<string>): Promise<Array<CoursePackageDetailVo>> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    const request = new CoursePackageRequest()
    request.coursePackageId = idList
    let result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursePackageInServicer({
      page: page,
      request
    })
    if (result.data.totalSize) {
      result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursePackageInServicer({
        page: page,
        request
      })
    }
    return result.data.currentPageData.map(CoursePackageDetailVo.from)
  }

  /**
   * 根据课程 id 集合查询课程对应的试题数量
   * @param courseIdList
   * @private
   */
  private async getQuestionCountWithCourseIdList(courseIdList: Array<string>): Promise<Map<string, number>> {
    const tempIds: string[] = []
    courseIdList.map(res => {
      console.log('this.courseQuestionCountMapCache.has(res)', this.courseQuestionCountMapCache.has(res))
      if (!this.courseQuestionCountMapCache.has(res) && res) {
        tempIds.push(res)
      }
    })
    if (tempIds?.length) {
      const result = await MsExamQueryFrontGatewayExamQueryBackStage.getQuestionCountByRelateCourseInServicer({
        courseIdList: tempIds,
        enable: true
      })
      result.data?.forEach((response: CourseQuestionCountResponse) => {
        this.courseQuestionCountMapCache.set(response.courseId, response.questionCount)
      })
    }
    return this.courseQuestionCountMapCache
  }

  /**
   * 课程分页
   * @param page
   * @param pageQueryParam
   */
  async queryCoursePage(page: Page, pageQueryParam: QueryCourseListParam): Promise<Array<CourseListDetail>> {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
      page,
      request: pageQueryParam.to()
    })
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize
    const courseIdSet = new Set<string>()
    const providerIds: string[] = []
    let list: CourseListDetail[] = []
    if (result.data.currentPageData && result.data.currentPageData.length) {
      list = result.data.currentPageData.map((course: CourseResponse) => {
        courseIdSet.add(course.id)
        providerIds.push(course.supplierId)
        return CourseListDetail.from(course)
      })
      this.batchQueryProvider(providerIds).then(response => {
        list = list.map(res => CourseListDetail.addProvider(res, response))
      })

      if (courseIdSet.size) {
        this.getQuestionCountWithCourseIdList(Array.from(courseIdSet.values())).then(res => {
          list.forEach((detail: CourseListDetail) => {
            detail.questionCount = res.get(detail.id)
          })
        })
      }
    }
    return list
  }

  /**
   * 课程分页 只包含课程信息的分页口
   * @param page
   * @param pageQueryParam
   */
  async pageCourseList(page: Page, pageQueryParam: QueryCourseListParam): Promise<Array<CourseListDetail>> {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
      page,
      request: pageQueryParam.to()
    })
    page.totalSize = result.data?.totalSize ?? 0
    page.totalPageSize = result.data?.totalPageSize ?? 0
    return result.data?.currentPageData?.map(CourseListDetail.from) || []
  }

  // * 【自用】 批量查询课件供应商
  async batchQueryProvider(ids: string[]) {
    ids = Array.from(new Set(ids))
    const tempIds: string[] = []
    ids.map(res => {
      if (!this.providerCache.has(res) && res) {
        tempIds.push(res)
      }
    })
    if (tempIds.length) {
      const page = new Page(1, tempIds.length)
      const providerRequest = new CoursewareSupplierRequest()
      providerRequest.servicerBase = new ServicerBaseRequest()
      providerRequest.servicerBase.servicerIdList = tempIds
      const providerResponse = await BasicDataQueryBackstage.pageCoursewareSupplierInfoInSubProject({
        page,
        request: providerRequest
      })
      if (providerResponse.status.code !== 200) {
        return Promise.reject(providerResponse)
      }
      providerResponse.data.currentPageData.map(res => {
        this.providerCache.set(res.servicerBase.servicerId, res)
      })
    }
    console.log('this.providerCache', this.providerCache)
    const list = ids.map(res => {
      if (this.providerCache.has(res)) {
        return this.providerCache.get(res)
      }
    })
    return list
  }

  /**
   * 根据 id 集合查询课程集合
   * @param idList
   */
  // async queryCourseByIdList(idList: Array<string>): Promise<Array<CourseListDetail>> {
  //   const request = new CourseRequest()
  //   request.courseIdList = idList
  //   const page = new UiPage()
  //   page.pageNo = 1
  //   page.pageSize = 1
  //   const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
  //     page,
  //     request
  //   })
  //   page.pageSize = result.data.totalSize
  //   const lastQueryResult = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
  //     page,
  //     request
  //   })
  //   return lastQueryResult.data.currentPageData.map(CourseListDetail.from)
  // }

  /**
   * 根据 id 集合查询课程集合V2 优化口
   * @param idList
   */
  async queryCourseByIdListPage(idList: Array<string>): Promise<Array<CourseListDetail>> {
    const param = new CourseV2Request()
    param.pageNo = 1
    param.pageSize = idList.length
    param.courseIdList = idList
    const data = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseV2InServicer(param)
    return data.data.currentPageData.map(CourseListDetail.from)
  }
  /**
   * 根据 id 集合查询课程集合V2
   * @param idList
   */
  async queryCourseByIdList(idList: Array<string>): Promise<Array<CourseListDetail>> {
    const req = new RewriteGraph<CourseResponsePage, CourseV2Request>(
      MsCourseLearningQueryFrontGatewayCourseLearningBackstage._commonQuery,
      CourseGraphqlImporter.pageCourseV2InServicer
    )
    const rqList = new Array<CourseV2Request>()
    for (let index = 0; index < idList.length / 200; index++) {
      const rq = new CourseV2Request()
      rq.pageNo = 1
      rq.pageSize = 200
      rq.courseIdList = idList.slice(index * 200, index * 200 + 200)
      rqList.push(rq)
    }
    await req.request(rqList)
    const keys = [...req.indexMap.keys()]
    const lastQueryResult = new Array<CourseResponse>()
    keys.map((key, index) => {
      lastQueryResult.push(...req.indexMap.get(index).currentPageData)
    })
    return lastQueryResult.map(CourseListDetail.from)
  }

  /**
   * 补充 课程 集合查教师名称
   */
  async queryTeacherByIdList(courseList: Array<CourseListDetail>) {
    const teacherInfoMap = new Map<string, string>()
    const teacherIdList = courseList.map(item => item.teacherIds).flat(1)
    if (teacherIdList.length) {
      const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listTeacherInServicer([
        ...new Set(teacherIdList)
      ])
      result.data?.forEach(item => {
        teacherInfoMap.set(item.id, item.name)
      })
      courseList.forEach(item => {
        item.teacherNames = item.teacherIds.map(id => teacherInfoMap.get(id))
      })
    }
    return courseList
  }

  /**
   * 根据 id 查询课程详情
   * @param id
   */
  async queryCourseById(id: string): Promise<CourseDetail> {
    const queryServiceProvider = new QueryServiceProvider()

    const response = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.getCourseInServicer(id)
    let supplierResponse
    if (response.data.supplierId) {
      supplierResponse = await queryServiceProvider.serchProvider(response.data.supplierId)
    }
    let resultDetail = new CourseDetail()
    resultDetail = CourseDetail.from(response.data, supplierResponse)
    await resultDetail.queryTeacher()
    return resultDetail
  }

  /**
   * 根据课程查询课程评价分页
   * @param page
   * @param id
   */
  async queryCourseEvaluateById(page: UiPage, id: string) {
    const remoteResponse = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageStudentCourseAppraiseInServicer(
      { page, courseId: id }
    )
    page.totalSize = remoteResponse.data.totalSize
    page.totalPageSize = remoteResponse.data.totalPageSize

    const userIdList = remoteResponse.data.currentPageData.map(detail => detail.courseAppraisalInfo.appraisalUserId)

    const rew = new RewriteGraph<StudentInfoResponse, string>(
      MsBasicdataQueryFrontGatewayBasicDataQueryBackstage._commonQuery,
      GraphqlImporter.getStudentInfoInServicer
    )
    await rew.request(userIdList)

    const userList = rew.itemMap

    const res = await QueryTechnologyLevel.query()

    return remoteResponse.data.currentPageData.map(item =>
      CourseEvaluateListDetail.from(item, userList.get(item.courseAppraisalInfo.appraisalUserId), res)
    )
  }

  /**
   * 根据分类 id 集合查询课程集合
   * @param categoryIdList
   */
  // todo 等待实现
  async queryCourseListByCategories(categoryIdList: Array<string>): Promise<Array<CourseDetail>> {
    console.log(categoryIdList)
    return new Array<CourseDetail>()
  }

  // 获取课件被课程引用的列表
  async queryCourseListByCoursewareId(coursewareId: string): Promise<Array<CourseListDetail>> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 10
    const pageQueryParam = new QueryCourseListParam()
    pageQueryParam.coursewareId = coursewareId
    const orderType = new CourseSortRequest()
    orderType.courseSort = CourseSortEnum.CREATE_TIME
    orderType.sortType = 0
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
      page,
      request: pageQueryParam.to(),
      sort: [orderType]
    })
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize
    return result.data.currentPageData.map(CourseListDetail.from)
  }

  /**
   * 根据课程 id 集合查询课程名称
   * @param idList
   * @private
   */
  private async queryCourseNameByIdList(
    idList: Array<string>
  ): Promise<Map<string, { name: string; supplierId: string }>> {
    const queryCourseList = new QueryCourseList()
    const result = await queryCourseList.queryCoursePageByIdList(idList)
    const map = new Map<string, { name: string; supplierId: string }>()
    result.forEach((course: CourseResponse) => {
      map.set(course.id, { name: course.name, supplierId: course.supplierId })
    })
    return map
  }

  /**
   * 查询方案下面的课程分页集合
   * @param page
   * @param params
   */
  async queryCoursePageInSchemeByByPackage(
    page: UiPage,
    params: QuerySchemePackageCourseListParams
  ): Promise<Array<TrainingOutlineCourse>> {
    const request = new CourseInSchemeRequest()
    request.schemeId = params.schemeId
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    request.courseOfCourseTrainingOutline.outlineIds = params.outlineIdList
    // 获取服务端返回的数据
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInSchemeInServicer({
      page,
      request
    })

    // 构造出一个只有课程 id 的集合
    const courseIdList = new Array<string>()
    result.data.currentPageData.forEach((course: CourseInSchemeResponse) => {
      courseIdList.push(course.course.courseId)
    })

    // 将集合去查询课程名称，填充并且返回 map 结构 { courseId: name }
    const courseNameMap = await this.queryCourseNameByIdList(courseIdList)
    return result.data.currentPageData.map((response: CourseInSchemeResponse) => {
      const course = TrainingOutlineCourse.from(response)
      course.name = courseNameMap.get(course.id).name
      return course
    })
  }

  /**
   * 分页查询课程大纲节点下课程
   * @description 培训方案详情页加载课程大纲下课程时使用
   * @param page 分页
   * @param outlineIds 课程大纲节点id
   * @param schemeId 培训方案id
   */
  async queryCourseListInSchemeByOutline(
    page: Page,
    outlineIds: string[],
    schemeId: string,
    courseName?: string
  ): Promise<TrainingOutlineCourse[]> {
    let result = [] as TrainingOutlineCourse[]
    // 如果课程大纲节点id集合为空
    if (!outlineIds.length) {
      page.totalPageSize = 0
      page.totalSize = 0
      return result
    }
    /** 查询课程大纲下课程 */
    const request = new CourseInSchemeRequest()
    request.schemeId = schemeId
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    request.courseOfCourseTrainingOutline.outlineIds = outlineIds
    request.course = new CourseInfoRequest()
    request.course.courseName = courseName
    const response = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInSchemeInServicer({
      page,
      request
    })
    page.totalPageSize = response.data?.totalPageSize
    page.totalSize = response.data?.totalSize
    if (response.data?.currentPageData && response.data?.currentPageData?.length) {
      const metadata = response.data.currentPageData
      result = metadata.map(TrainingOutlineCourse.from)
      // 获取课程包id集合
      const coursePackageIdList = [...new Set(result.map(item => item.sourceCoursePackageId).filter(Boolean))]
      // 获取课程id集合
      const courseIdList = [...new Set(result.map(item => item.id).filter(Boolean))]
      /** 获取课程包信息并赋值 */
      if (coursePackageIdList.length) {
        const coursePackageList = await this.queryCoursePackageListInSchemeByOutline(coursePackageIdList)
        result.forEach(item => {
          const target = coursePackageList.find(el => el.id === item.sourceCoursePackageId)
          if (target) item.sourceCoursePackageName = target.name
        })
      }
      /** 获取课程信息并赋值 */
      if (courseIdList.length) {
        const courseList = await this.queryCourseListInScheme(courseIdList)
        result.forEach(item => {
          const target = courseList.find(el => el.courseId === item.id)
          if (target) {
            item.name = target.name || ''
            item.providerId = target.supplierId || ''
          }
        })
      }
      /** 获取课件供应商信息并赋值 */
      // 获取课件供应商id集合
      const providerIdList = [...new Set(result.map(item => item.providerId).filter(Boolean))]
      if (providerIdList.length) {
        const providerList = await this.batchQueryProvider(providerIdList)
        result.forEach(item => {
          const target = providerList.find(el => el.servicerBase?.servicerId === item.providerId)
          if (target) item.providerName = target.servicerBase.servicerName
        })
      }
    }
    console.log('###CourseListInScheme', result)
    return result
  }

  /**
   * 根据课程大纲下课程包id集合查询课程包信息
   * @description 仅培训方案详情页使用
   * @param coursePackageIdList 课程包id集合
   * @private
   */
  private async queryCoursePackageListInSchemeByOutline(
    coursePackageIdList: string[]
  ): Promise<CoursePackageDetailVo[]> {
    let result = [] as CoursePackageDetailVo[]
    const page = new Page(1, coursePackageIdList.length)
    const request = new CoursePackageRequest()
    request.coursePackageId = coursePackageIdList
    const resp = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCoursePackageInServicer({
      page,
      request
    })
    if (resp.status?.isSuccess() && resp.data?.currentPageData && resp.data?.currentPageData?.length) {
      const metadata = resp.data.currentPageData
      result = metadata.map(CoursePackageDetailVo.from)
    }
    return result
  }

  /**
   * 根据课程大纲下课程id集合查询课程信息（课程名称和课件提供商id）
   * @description 仅培训方案详情页使用
   * @param courseIdList 课程id集合
   * @private
   */
  private async queryCourseListInScheme(
    courseIdList: string[]
  ): Promise<{ courseId: string; name: string; supplierId: string }[]> {
    let result = [] as { courseId: string; name: string; supplierId: string }[]
    const request = new CourseV2Request()
    request.pageNo = 1
    request.pageSize = courseIdList.length
    request.courseIdList = courseIdList
    const resp = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseV2InServicer(request)
    if (resp.status?.isSuccess() && resp.data?.currentPageData && resp.data?.currentPageData?.length) {
      const metadata = resp.data.currentPageData
      result = metadata.map(item => {
        return { courseId: item.id, name: item.name, supplierId: item.supplierId }
      })
    }
    return result
  }

  /**
   * 查询方案下面的课程集合
   * @param params
   */
  async queryCourseListInSchemeByByPackage(params: QuerySchemePackageCourseListParams): Promise<CourseInSchemeResult> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    const request = new CourseInSchemeRequest()
    request.schemeId = params.schemeId
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    request.courseOfCourseTrainingOutline.outlineIds = params.outlineIdList
    // 获取服务端返回的数据
    let result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInSchemeInServicer({
      page,
      request
    })

    if (result.data.totalSize) {
      page.pageSize = result.data.totalSize
      result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInSchemeInServicer({
        page,
        request
      })
    }
    // 构造出一个只有课程 id 的集合
    const courseIdList = new Array<string>()
    const coursePackageIdSet = new Set<string>()

    const resultMap = new CourseInSchemeResult()
    resultMap.totalSize = result.data.totalSize
    resultMap.trainingOutlineCourse = result.data.currentPageData
      .sort((a, b) => a.courseOfCourseTrainingOutline.sort - b.courseOfCourseTrainingOutline.sort)
      .map((response: CourseInSchemeResponse) => {
        resultMap.totalPeriod += response.courseOfCourseTrainingOutline.period
        const course = TrainingOutlineCourse.from(response)
        courseIdList.push(response.course.courseId)
        if (response.range.courseSourceType === '1') {
          coursePackageIdSet.add(response.range.courseSourceId)
        }
        return course
      })

    let coursePackageList: Array<CoursePackageDetailVo> = new Array<CoursePackageDetailVo>()
    if (coursePackageIdSet.size) {
      coursePackageList = await this.listCoursePackage(Array.from(coursePackageIdSet.values()))
    }

    // 将集合去查询课程名称，填充并且返回 map 结构 { courseId: name }
    const courseNameMap = await this.queryCourseNameByIdList(courseIdList)
    resultMap.trainingOutlineCourse.forEach((detail: TrainingOutlineCourse) => {
      detail.name = courseNameMap.get(detail.id).name
      detail.providerName = courseNameMap.get(detail.id)?.supplierId
      const findCoursePackage = coursePackageList.find((coursePackage: CoursePackageDetailVo) => {
        return coursePackage.id === detail.sourceCoursePackageId
      })
      if (findCoursePackage) {
        detail.sourceCoursePackageName = findCoursePackage.name
      }
    })
    let providerIds = resultMap.trainingOutlineCourse.map(res => res.providerName)
    providerIds = Array.from(new Set(providerIds))
    const providerList = await this.batchQueryProvider(providerIds)
    resultMap.trainingOutlineCourse.forEach((detail: TrainingOutlineCourse) => {
      if (providerList && providerList.length) {
        const curProvider = providerList.find(res => res.servicerBase.servicerId === detail.providerName)
        if (curProvider) {
          detail.providerName = curProvider.servicerBase.servicerName
        }
      }
    })

    return resultMap
  }

  /**
   * 查询课程包下重复课程列表
   * @param sourceCoursePackageIdList 原有课程包id集合
   * @param targetCoursePackageId 目标课程包id
   */
  async queryRepeatCourseListInCoursePackage(
    sourceCoursePackageIdList: string[],
    targetCoursePackageId: string
  ): Promise<RepeatCourseListInCoursePackage[]> {
    const result = [] as RepeatCourseListInCoursePackage[]
    // 如果原有课程包id集合为空、直接返回
    if (!sourceCoursePackageIdList.length || !targetCoursePackageId) return result
    // 源课程包id去重
    const filterCoursePackageIdList = [...new Set(sourceCoursePackageIdList.map(item => item).filter(Boolean))]
    // 构建查询入参
    const coursePackageList: RepeatedCoursesInSpecifiedCoursePackageRequest[] = []
    filterCoursePackageIdList.forEach(item => {
      coursePackageList.push({
        sourceCoursePackageId: item,
        targetCoursePackageId
      })
    })
    // 重写graphql，批量请求
    const reWriteGraphql = new RewriteGraph<
      RepeatedCoursesInSpecifiedCoursePackageResponse[],
      RepeatedCoursesInSpecifiedCoursePackageRequest
    >(
      MsCourseLearningQueryFrontGatewayCourseLearningBackstage._commonQuery,
      getRepeatedCoursesInSpecifiedCoursePackageInServicer
    )
    await reWriteGraphql.request(coursePackageList)
    // 处理返回值
    for (const [key, value] of reWriteGraphql.itemMap.entries()) {
      value?.forEach(item => {
        const option = new RepeatCourseListInCoursePackage()
        option.coursePackageId = item.sourceCoursePackageId
        option.courseId = item.courseId
        option.courseName = item.courseName
        result.push(option)
      })
    }
    console.log('###repeatCourseListInCoursePackage', result)
    return result
  }

  /**
   * 分页查询课程大纲下课程统计数据（课程大纲下课程总数、课程大纲下课程总学时）
   * @param outlineIds 末级课程大纲节点id集合
   * @param schemeId
   */
  async queryCourseStatisticByScheme(schemeId: string): Promise<CourseStatisticVo[]> {
    const result = [] as CourseStatisticVo[]
    const response = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.statisticsCourseInSchemeInServicer(
      schemeId
    )
    if (response.status?.isSuccess() && response.data && response.data?.length) {
      const metadata = response.data
      metadata.forEach(item => {
        const option = new CourseStatisticVo()
        option.outlineId = item.outlineId
        option.courseTotal = item.courseStatisticOfCourseTrainingOutline?.courseCount || 0
        option.coursePeriodTotal = item.courseStatisticOfCourseTrainingOutline?.courseTotalPeriod || 0
        result.push(option)
      })
    }
    console.log('###CourseStatisticByOutline', result)
    return result
  }

  /**
   * 分页查询课程大纲节点下指定课程列表
   * @param page 分页参数
   * @param outlineId 课程大纲节点id
   * @param schemeId 培训方案id
   * @param courseIdList 课程id集合
   */
  async queryCourseListInSchemeByOutlineByCourseIdList(
    page: Page,
    outlineId: string,
    schemeId: string,
    courseIdList: string[]
  ): Promise<TrainingOutlineCourse[]> {
    // TODO LWF
    return [] as TrainingOutlineCourse[]
  }

  /**
   * 批量查询课程大纲节点下指定课程列表的统计学时
   * @param  outlineInfo 大纲节点信息
   * @return Map结构
   * {
   *   课程大纲节点id：指定课程列表的统计学时
   * }
   */
  async queryCourseStatisticInSchemeByOutlineByCourseIdList(
    outlineInfo: OutlineCourseInfo[]
  ): Promise<Map<string, number>> {
    // TODO LWF
    const result = new Map<string, number>()
    return result
  }
  /**
   * 查询课程大纲节点下指定课程列表的统计学时
   * @param  outlineInfo 大纲节点信息
   */
  async countPeriodCountOfCourseTrainingOutlineInSchemeInServicer(
    schemeId: string,
    outlineId: string,
    courseIdList: string[]
  ) {
    const request = new CourseInSchemeRequest()
    request.schemeId = schemeId
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    request.courseOfCourseTrainingOutline.outlineIds = [outlineId]
    request.course.courseIdList = courseIdList
    const periodCount = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.countPeriodCountOfCourseTrainingOutlineInSchemeInServicer(
      request
    )
    return periodCount
  }
  /**
   * 批量查询课程大纲节点下指定课程列表的统计学时
   * @param  outlineInfo 大纲节点信息
   */
  async allCountPeriodCountOfCourseTrainingOutlineInSchemeInServicer(
    schemeId: string,
    outline: Array<{ outlineId: string; courseIdList: string[] }>
  ) {
    const periodCount = new Map<string, number>()
    const outlineList = outline.map(ite => {
      const request = new CourseInSchemeRequest()
      request.schemeId = schemeId
      request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
      request.courseOfCourseTrainingOutline.outlineIds = [ite.outlineId]
      request.course = new CourseInfoRequest()
      request.course.courseIdList = ite.courseIdList
      return request
    })

    const req = new RewriteGraph<CoursePeriodCountOfCourseTrainingOutlineResponse, CourseInSchemeRequest>(
      MsCourseLearningQueryFrontGatewayCourseLearningForestage._commonQuery,
      CourseGraphqlImporter.countPeriodCountOfCourseTrainingOutlineInSchemeInServicer
    )
    await req.request(outlineList)
    req.itemMap.forEach((val, key) => {
      periodCount.set(key.courseOfCourseTrainingOutline.outlineIds[0], val.courseTotalPeriod)
    })
    return periodCount
  }

  /**
   * 全量查询课程大纲节点下课程
   * @description 培训方案详情页加载课程大纲下课程时使用
   * @param page 分页
   * @param outlineIds 课程大纲节点id
   * @param schemeId 培训方案id
   */
  async queryAllCourseListInSchemeByOutline(
    schemeId: string,
    outlineIds: string,
    courseIdList: string[]
  ): Promise<CompulsoryCourseInfo[]> {
    const result = new Array<CompulsoryCourseInfo>()
    const results = new Array<CourseInSchemeResponse>()
    // 如果课程大纲节点id集合为空
    if (!outlineIds.length) {
      return result
    }
    const page = new Page(1, 200)
    /** 查询课程大纲下课程 */
    const request = new CourseInSchemeRequest()
    request.schemeId = schemeId
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    request.courseOfCourseTrainingOutline.outlineIds = [outlineIds]
    request.course = new CourseInfoRequest()
    request.course.courseIdList = courseIdList
    const response = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInSchemeInServicer({
      page,
      request
    })
    results.push(...response?.data?.currentPageData)
    if (response?.data?.totalPageSize > 1) {
      const num = Array.from({ length: response?.data?.totalPageSize - 1 }, (v, k) => k + 2)
      await Promise.all(
        num.map(async ite => {
          const red = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInSchemeInServicer({
            page: { pageNo: ite, pageSize: 200 },
            request
          })
          return red
        })
      ).then(res => {
        console.log(res, 'res')
        res.map(re => {
          results.push(...re?.data?.currentPageData)
        })
      })
    }
    results.forEach(item => {
      const course = new CompulsoryCourseInfo()
      course.id = item.course.courseId
      course.name = item.course.courseName
      course.period = item.courseOfCourseTrainingOutline.period
      course.outlineId = item.courseOfCourseTrainingOutline.outlineId
      result.push(course)
    })
    console.log('###CourseListInScheme', result)
    return result
  }
}

export default QueryCourse
