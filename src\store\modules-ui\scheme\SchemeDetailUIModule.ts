import { getModule, Module, Action, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'

/**
 * @description 培训方案详情UI数据持久层
 */
@Module({ namespaced: true, store, dynamic: true, name: 'SchemeDetailUIModule' })
class SchemeDetailUIModule extends VuexModule {
  /**
   * 培训班方案详情
   */
  schemeDetail: TrainClassDetailClassVo

  /**
   * 设置培训班方案详情
   */
  @Action({ rawError: true })
  setSchemeDetail(detail: TrainClassDetailClassVo) {
    this.SET_SCHEME_DETAIL(detail)
  }

  /**
   * 设置培训班方案详情
   */
  @Mutation
  SET_SCHEME_DETAIL(detail: TrainClassDetailClassVo) {
    this.schemeDetail = detail
  }
}

export default getModule(SchemeDetailUIModule)
