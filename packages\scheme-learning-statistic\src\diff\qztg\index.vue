<template>
  <SchemeSellStatisticQztg ref="schemeLearningStatisticRef">
    <template #sale-channel="{ localSkuProperty }">
      <el-form-item label="销售渠道">
        <biz-sale-channel-select v-model="localSkuProperty.tradeChannels"></biz-sale-channel-select>
      </el-form-item>
    </template>
  </SchemeSellStatisticQztg>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import SchemeLearningStatistic from '@hbfe/jxjy-admin-schemeLearningStatistic/src/index.vue'
  import { CommoditySkuRequestVo } from '@api/service/diff/management/qztg/statisticalReport/model/CommoditySkuRequestVo'
  import QuerySchemeLearningList from '@api/service/diff/management/qztg/statisticalReport/QuerySchemeLearningList'
  import bizSaleChannelSelect from '@hbfe/jxjy-admin-components/src/biz-sale-channel-select.vue'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-common/src/models/sku'
  class NewSchemeSkuProperty extends SchemeSkuProperty {
    tradeChannels: number = null
  }

  @Component
  class SchemeSellStatisticQztg extends SchemeLearningStatistic {
    /**
     * 查询入参
     */
    CommoditySkuRequestVo = new CommoditySkuRequestVo()
    /**
     * 本地筛选项
     */
    localSkuProperty: NewSchemeSkuProperty = new NewSchemeSkuProperty()
    /**
     * 方法类
     */
    getQuerySchemeLearningList: QuerySchemeLearningList = new QuerySchemeLearningList()

    /**
     * 查询
     */
    async listSchemeLearningReportFormsInServicer() {
      this.CommoditySkuRequestVo.saleChannels = this.localSkuProperty.tradeChannels
        ? [this.localSkuProperty.tradeChannels]
        : null
      return await this.getQuerySchemeLearningList.listSchemeLearningReportFormsInServicer(
        this.trainSchemePage,
        this.CommoditySkuRequestVo
      )
    }
  }
  @Component({
    components: {
      SchemeSellStatisticQztg,
      bizSaleChannelSelect
    }
  })
  export default class extends Vue {
    @Ref('schemeLearningStatisticRef') schemeLearningStatisticRef: SchemeSellStatisticQztg

    async mounted() {
      this.schemeLearningStatisticRef.initQueryParam()
    }
  }
</script>
