import { InvoiceConfigResponse, PurchaseChannel, TerminalResponse } from '@api/ms-gateway/ms-trade-configuration-v1'

class PurchaseChannelTypeVo {
  /**
   * 购买渠道id 如果该值不存在或者null 代表未创建当前购买渠道
   */
  id: string
  /**
   * 渠道类型
   */
  type: number
  /**
   * 渠道名称
   */
  channelName: string
  /**
   * 状态 (1-启用，0-禁用）
   */
  status: number
  /**
   * 终端列表
   */
  terminalList: Array<TerminalResponse> = new Array<TerminalResponse>()
  /**
   * 发票配置
   */
  invoiceConfig: InvoiceConfigResponse = new InvoiceConfigResponse()

  static from(res: PurchaseChannel) {
    const purchaseChannel = new PurchaseChannelTypeVo()
    purchaseChannel.id = res.purchaseChannelId
    purchaseChannel.type = res.channelType
    purchaseChannel.status = res.status
    purchaseChannel.terminalList = new Array<TerminalResponse>()
    purchaseChannel.terminalList = res.terminalList ? res.terminalList : new Array<TerminalResponse>()
    purchaseChannel.invoiceConfig = new InvoiceConfigResponse()
    purchaseChannel.invoiceConfig = res.invoiceConfig
    return purchaseChannel
  }
}
export default PurchaseChannelTypeVo
