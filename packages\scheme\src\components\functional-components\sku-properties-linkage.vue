<template>
  <div class="prue">
    <el-form ref="skuPropertiesLinkageForm" :model="skuProperties" :rules="rules" label-width="150px" class="m-form">
      <el-form-item label="年度：" prop="year">
        <el-select
          v-model="skuProperties.year"
          @clear="clearString('year')"
          placeholder="请选择年度"
          class="form-s"
          clearable
          :disabled="createMode === 3"
          @change="setCommodityBasicInfo"
          filterable
        >
          <el-option v-for="item in yearOptions" :label="item.year" :value="item.id" :key="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="地区：" prop="region">
        <el-cascader
          v-model="skuProperties.region"
          :props="regionProps"
          :options="regionOptions"
          @clear="clearStringArr('region')"
          placeholder="请选择地区"
          class="form-l"
          clearable
          :disabled="createMode === 3"
        />
      </el-form-item>
      <el-form-item label="行业：" prop="industry">
        <el-select
          v-model="skuProperties.industry"
          @clear="clearString('industry')"
          placeholder="请选择行业"
          class="form-l"
          clearable
          @change="handleIndustryChange"
          :disabled="createMode === 3"
          filterable
        >
          <el-option v-for="item in industryOptions" :label="item.name" :value="item.id" :key="item.id"></el-option>
        </el-select>
      </el-form-item>
      <!-- {{ skuProperties.industry }} -->
      <template v-if="isTeacherIndustry">
        <el-form-item label="学段：">
          <el-select
            placeholder="请选择学段"
            class="form-l"
            v-model="skuProperties.studyPeriod"
            :disabled="createMode === 3"
            @change="getSubjectOptions(skuProperties.studyPeriod)"
            clearable
          >
            <el-option
              :value="item.propertyId"
              :label="item.name"
              v-for="item in studyPeriodOptions"
              :key="item.propertyId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="学科：">
          <el-select
            placeholder="请选择学科"
            class="form-l"
            v-model="skuProperties.subject"
            clearable
            :disabled="createMode === 3"
          >
            <el-option
              :value="item.propertyId"
              :label="item.name"
              v-for="item in subjectOptions"
              :key="item.propertyId"
            ></el-option>
          </el-select>
        </el-form-item>
      </template>
      <el-form-item
        label="技术等级"
        prop="jobLevel"
        v-if="isworkServiceIndustry && (skuVisible.jobLevel || createMode === 3)"
      >
        <el-select
          class="form-l"
          v-model="skuProperties.jobLevel"
          placeholder="请选择技术等级"
          :disabled="createMode === 3"
          clearable
          filterable
          @clear="clearString('jobLevel')"
        >
          <el-option
            v-for="item in technologyLevelOptions"
            :label="optionName(item)"
            :key="item.propertyId"
            :value="item.propertyId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="科目类型："
        prop="subjectType"
        v-if="
          (isSocietyIndustry || isConstructionIndustry || isPharmacistIndustry) &&
          (skuVisible.subjectType || skuVisible.configYsType || createMode === 3)
        "
        :rules="isSocietyIndustry || isPharmacistIndustry ? rules.subjectType : [{ required: false }]"
      >
        <el-select
          v-model="skuProperties.subjectType"
          @clear="clearString('subjectType')"
          placeholder="请选择科目类型"
          class="form-l"
          clearable
          :disabled="createMode === 3"
          filterable
        >
          <el-option
            v-for="item in subjectTypeOptions"
            :label="optionName(item)"
            :value="item.propertyId"
            :key="item.propertyId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="培训专业："
        prop="societyTrainingMajor"
        v-if="isSocietyIndustry && (skuVisible.trainingCategory || createMode === 3)"
      >
        <el-cascader
          v-model="skuProperties.societyTrainingMajor"
          :props="societyMajorProps"
          :options="societyMajorOptions"
          @clear="clearStringArr('societyTrainingMajor')"
          placeholder="请选择培训专业"
          class="form-l"
          clearable
          :disabled="createMode === 3"
          filterable
        ></el-cascader>
      </el-form-item>
      <el-form-item
        label="培训类别："
        prop="trainingCategory"
        v-if="
          (isConstructionIndustry || isOccupationalHealthIndustry) && (skuVisible.trainingCategory || createMode === 3)
        "
      >
        <el-select
          v-model="skuProperties.trainingCategory"
          @clear="clearString('trainingCategory')"
          placeholder="请选择培训类别"
          class="form-l"
          @change="handleTrainingCategoryChange"
          clearable
          :disabled="createMode === 3"
          filterable
        >
          <el-option
            v-for="item in trainingCategoryOptions"
            :label="optionName(item)"
            :value="item.propertyId"
            :key="item.propertyId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="培训对象" prop="trainingObject" v-if="isOccupationalHealthIndustry">
        <el-select
          class="form-l"
          v-model="skuProperties.trainingObject"
          placeholder="请选择培训对象"
          :disabled="createMode === 3"
          clearable
          filterable
          @change="handleTrainingObjectChange"
          @clear="clearString('trainingObject')"
        >
          <el-option
            v-for="item in trainingObjectOptions"
            :label="optionName(item)"
            :key="item.propertyId"
            :value="item.propertyId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="岗位类别" prop="positionCategory" v-if="isOccupationalHealthIndustry">
        <el-select
          class="form-l"
          v-model="skuProperties.positionCategory"
          placeholder="请选择岗位类别"
          :disabled="createMode === 3"
          clearable
          filterable
          @clear="clearString('positionCategory')"
        >
          <el-option
            v-for="item in positionCategoryOptions"
            :label="optionName(item)"
            :key="item.propertyId"
            :value="item.propertyId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="培训专业："
        v-if="isConstructionIndustry && (skuVisible.trainingCategory || createMode === 3)"
      >
        <el-select
          v-model="skuProperties.constructionTrainingMajor"
          @clear="clearString('constructionTrainingMajor')"
          placeholder="请选培训专业"
          class="form-l"
          clearable
          :disabled="createMode === 3"
          filterable
        >
          <el-option
            v-for="item in constructionTrainingMajorOptions"
            :label="optionName(item)"
            :value="item.propertyId"
            :key="item.propertyId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="执业类别" prop="occupational" v-if="isPharmacistIndustry">
        <el-cascader
          v-model="skuProperties.occupational"
          :props="yssocietyMajorProps"
          :options="constructionTrainingMajorOptions"
          @clear="clearStringArr('occupational')"
          placeholder="请选培训执业类别"
          class="form-l"
          clearable
          :disabled="createMode === 3"
          filterable
        ></el-cascader>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Watch, Ref } from 'vue-property-decorator'
  import YearVo from '@api/service/common/basic-data-dictionary/query/vo/YearVo'
  import QueryYear from '@api/service/common/basic-data-dictionary/query/QueryYear'
  import { cloneDeep } from 'lodash'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import QuerySubjectType from '@api/service/common/basic-data-dictionary/query/QuerySubjectType'
  import SubjectTypeVo from '@api/service/common/basic-data-dictionary/query/vo/SubjectTypeVo'
  import TrainingMajorVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingMajorVo'
  import MajorParam from '@api/service/common/basic-data-dictionary/query/vo/majorParam'
  import QueryTrainingMajor from '@api/service/common/basic-data-dictionary/query/QueryTrainingMajor'
  import QueryTrainingCategory from '@api/service/common/basic-data-dictionary/query/QueryTrainingCategory'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import RegionVo from '@api/service/common/basic-data-dictionary/query/vo/RegionVo'
  import CreateSchemeUIModule from '@/store/modules-ui/scheme/CreateSchemeUIModule'
  import SocietyTrainingMajorVo from '@api/service/common/basic-data-dictionary/query/vo/SocietyTrainingMajorVo'
  import DataResolve from '@api/service/common/utils/DataResolve'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
  import QueryJobLevel from '@api/service/common/basic-data-dictionary/query/QueryJobLevel'
  import QueryPositionCategory from '@api/service/common/basic-data-dictionary/query/QueryPositionCategory'
  import QueryTrainingObject from '@api/service/common/basic-data-dictionary/query/QueryTrainingObject'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
  import QueryGrade from '@api/service/common/basic-data-dictionary/query/QueryGrade'
  import QuerySubject from '@api/service/common/basic-data-dictionary/query/QuerySubject'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
  import QueryOperationIndustry from '@api/service/common/basic-data-dictionary/query/QueryOperationIndustry'
  import AssembleTree from '@api/service/common/utils/AssembleTree'
  import QuerycertType from '@api/service/common/basic-data-dictionary/query/QueryCertType'

  /**
   * sku属性列表
   */
  export class SkuProperties {
    // 行业类型：1-人社，2-建设
    type: number
    year: string
    region: string[]
    industry: string
    subjectType: string
    trainingCategory: string
    societyTrainingMajor: string[]
    constructionTrainingMajor: string
    jobLevel: string
    trainingObject: string
    positionCategory: string
    studyPeriod: string
    subject: string
    occupational: string[]
    constructor() {
      this.type = 1
      this.year = ''
      this.region = [] as string[]
      this.industry = ''
      this.subjectType = ''
      this.trainingCategory = ''
      this.societyTrainingMajor = [] as string[]
      this.constructionTrainingMajor = ''
      this.jobLevel = ''
      this.trainingObject = ''
      this.positionCategory = ''
      this.studyPeriod = ''
      this.subject = ''
      this.occupational = [] as string[]
    }
  }

  @Component
  export default class extends Vue {
    @Ref('skuPropertiesLinkageForm') skuPropertiesLinkageForm: any

    /**
     * sku属性
     */
    skuProperties: SkuProperties = new SkuProperties()
    /**
     * sku属性回显
     */
    skuProperty: SkuPropertyResponseVo = new SkuPropertyResponseVo()
    /**
     * 年度选项
     */
    yearOptions: Array<YearVo> = new Array<YearVo>()

    /**
     * 地区选项
     */
    regionOptions: Array<RegionTreeVo> = new Array<RegionTreeVo>()
    /**
     * 全国树
     */
    nationWideTree: Array<RegionTreeVo> = new Array<RegionTreeVo>()
    /**
     * 获取服务地区id
     */
    serviceId: string[]

    /**
     * 行业选项
     */
    industryOptions: Array<IndustryVo> = new Array<IndustryVo>()
    /**
     * 科目类型选项
     */
    subjectTypeOptions: Array<SubjectTypeVo> = new Array<SubjectTypeVo>()
    /**
     * 人社专业选项
     */
    societyMajorOptions: Array<SocietyTrainingMajorVo> = new Array<SocietyTrainingMajorVo>()
    /**
     * 培训类别选项
     */
    trainingCategoryOptions: Array<TrainingCategoryVo> = new Array<TrainingCategoryVo>()
    /**
     * 建设培训类别选项
     */
    constructionTrainingMajorOptions: Array<TrainingMajorVo> = new Array<TrainingMajorVo>()
    /**
     * 工勤技术等级选项
     */
    technologyLevelOptions: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
    /**
     * 职业卫生岗位类别选项
     */
    positionCategoryOptions: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
    /**
     * 职业卫生培训对象选项
     */
    trainingObjectOptions: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
    //教师学段选项
    studyPeriodOptions = new Array<TrainingPropertyResponse>()
    //教师学科选项
    subjectOptions = new Array<TrainingPropertyResponse>()
    //执业类别选项
    occupationalOptions = new Array<SocietyTrainingMajorVo>()
    /**
     * 人社专业级联配置
     */
    societyMajorProps = {}
    /**
     * 药师专业级联配置
     */
    yssocietyMajorProps = {}
    /**
     * 地区级联配置
     */
    regionProps = {}

    rules = {
      // 年度
      year: [{ required: true, message: '请选择年度', trigger: ['blur', 'change'] }],
      // 地区
      region: [{ required: true, message: '请选择地区', trigger: ['blur', 'change'] }],
      // 行业
      industry: [{ required: true, message: '请选择行业', trigger: ['blur', 'change'] }],
      // 科目类型
      subjectType: [{ required: true, message: '请选择科目类型', trigger: ['blur', 'change'] }],
      // 培训类别
      trainingCategory: [{ required: true, message: '请选择培训类别', trigger: ['blur', 'change'] }],
      // 技术等级
      jobLevel: [{ required: true, message: '请选择技术等级', trigger: ['blur', 'change'] }],
      // 培训对象
      trainingObject: [{ required: true, message: '请选择培训对象', trigger: ['blur', 'change'] }],
      // 岗位类别
      positionCategory: [{ required: true, message: '请选择岗位类别', trigger: ['blur', 'change'] }]
    }

    /**
     * 当前网校信息
     */
    envConfig = {
      // 人社行业Id
      societyIndustryId: IndustryIdEnum.RS,
      // 建设行业Id
      constructionIndustryId: IndustryIdEnum.JS,
      // 工勤行业Id
      workServiceId: IndustryIdEnum.GQ,
      // 职业卫生行业Id
      occupationalHealthId: IndustryIdEnum.WS,
      // 职业卫生行业Id
      isTeacherIndustryId: IndustryIdEnum.LS,
      // 专业科目类型Id
      professionalSubjectTypeId: '',
      // 公需+专业科目类型Id
      mixedSubjectTypeId: '',
      // 药师行业Id
      pharmacistId: IndustryIdEnum.YS
    }

    /**
     * sku查询参数
     */
    skuQueryParams: MajorParam = {
      // 行业分类属性Id
      industryPropertyId: '',
      // 人社行业 - 父级培训类别
      parentPropertyId: ''
    }

    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: true,
      // 培训类别
      trainingCategory: true,
      // 技术等级
      jobLevel: true,
      // 培训对象
      trainingObject: true,
      // 执业类别
      configYsType: true
    }

    @Watch('skuProperties', {
      immediate: true,
      deep: true
    })
    skuPropertiesChange(val: any) {
      console.log('skuProperties--', cloneDeep(val))
    }

    /**
     * 创建模式，1-创建，2-复制，3-编辑，默认：0
     */
    get createMode() {
      return CreateSchemeUIModule.createMode
    }

    /**
     * 是否人设行业
     */
    get isSocietyIndustry() {
      return this.skuProperties.industry && this.skuProperties.industry === this.envConfig.societyIndustryId
    }

    get isPharmacistIndustry() {
      return this.skuProperties.industry && this.skuProperties.industry === this.envConfig.pharmacistId
    }

    /**
     * 是否建设行业
     */
    get isConstructionIndustry() {
      return this.skuProperties.industry && this.skuProperties.industry === this.envConfig.constructionIndustryId
    }

    /**
     * 是否工勤行业
     */
    get isworkServiceIndustry() {
      return this.skuProperties.industry && this.skuProperties.industry === this.envConfig.workServiceId
    }

    /**
     * 是否职业卫生行业
     */
    get isOccupationalHealthIndustry() {
      return this.skuProperties.industry && this.skuProperties.industry === this.envConfig.occupationalHealthId
    }
    //是否教师行业
    get isTeacherIndustry() {
      return this.skuProperties.industry && this.skuProperties.industry === this.envConfig.isTeacherIndustryId
    }

    /**
     * 选项名称
     */
    get optionName() {
      return (item: SubjectTypeVo | SocietyTrainingMajorVo | TrainingCategoryVo | TrainingMajorVo) => {
        const showName = item.showName
        return showName ? item.name + '（' + item.showName + '）' : item.name
      }
    }

    /**
     * 初始化基础信息
     */
    async initBase() {
      // 加载年度
      let res = await QueryYear.queryYearList()
      this.yearOptions = res.isSuccess()
        ? QueryYear.yearList.sort((a, b) => Number(b.year) - Number(a.year))
        : ([] as YearVo[])
      this.nationWideTree = await QueryBusinessRegion.getCountrywideRegion()
      this.serviceId = await QueryBusinessRegion.getServiceRegionIds()
      this.regionOptions = await QueryBusinessRegion.filterRegionTree(this.nationWideTree, this.serviceId)
      const regionIds = this.skuProperty.region.skuPropertyValueId?.split('/') || []
      const regionNames = this.skuProperty.region.skuPropertyName?.split('/') || []
      if (this.skuProperty.region.skuPropertyValueId) {
        let province = this.regionOptions.find((item) => item.id === regionIds[0])
        if (!province) {
          province = new RegionTreeVo()
          province.id = regionIds[0]
          province.name = regionNames[0]
          province.children = new Array<RegionTreeVo>()
          province.disabled = true
          this.regionOptions.push(province)
        }
        let city
        if (regionIds.length > 1) {
          city = province.children.find((item) => item.id === regionIds[1])
          if (!city) {
            city = new RegionTreeVo()
            city.id = regionIds[1]
            city.name = regionNames[1]
            city.children = new Array<RegionTreeVo>()
            city.disabled = true
            province.children.push(city)
          }
        }
        if (regionIds.length > 2) {
          let county = city.children.find((item) => item.id === regionIds[2])
          if (!county) {
            county = new RegionTreeVo()
            county.id = regionIds[2]
            county.name = regionNames[2]
            county.children = undefined
            county.disabled = true
            city.children.push(county)
          }
        }
      }
      this.regionProps = {
        lazy: false,
        value: 'id',
        label: 'name',
        multiple: false,
        checkStrictly: true
      }
      res = await QueryIndustry.queryIndustry()
      this.industryOptions = res.isSuccess() ? QueryIndustry.industryList : ([] as IndustryVo[])
      if (
        this.skuProperty.industry?.skuPropertyValueId &&
        !this.industryOptions?.find((item) => item.id === this.skuProperty.industry.skuPropertyValueId)
      ) {
        this.industryOptions.push({
          id: this.skuProperty.industry.skuPropertyValueId,
          name: this.skuProperty.industry.skuPropertyName
        } as IndustryVo)
      }
      await this.getStudyPeriodOptions()
    }
    /**
     * 获取技术等级 - 工勤行业
     */
    async getTechnologyLevelOptions() {
      const data = await QueryJobLevel.QueryJobLevelByIndustry(
        this.skuProperties.industry,
        this.skuQueryParams.industryPropertyId
      )
      this.technologyLevelOptions = data
      if (
        this.skuProperty.jobLevel?.skuPropertyValueId &&
        !this.technologyLevelOptions?.find((item) => item.propertyId === this.skuProperty.jobLevel.skuPropertyValueId)
      ) {
        this.technologyLevelOptions.push({
          propertyId: this.skuProperty.jobLevel.skuPropertyValueId,
          name: this.skuProperty.jobLevel.skuPropertyName
        } as TrainingPropertyResponse)
      }
    }
    /**
     * 获取岗位类别 - 职业卫生行业
     */
    async getPositionCategoryOptions() {
      let data = [] as TrainingPropertyResponse[]
      if (this.skuQueryParams.industryPropertyId && this.skuProperties.trainingObject) {
        data = await QueryPositionCategory.queryPositionCategoryByTrainingObjectId(
          this.skuQueryParams.industryPropertyId,
          this.skuProperties.trainingObject
        )
      }
      this.positionCategoryOptions = data
      if (
        this.skuProperty.positionCategory?.skuPropertyValueId &&
        !this.positionCategoryOptions?.find(
          (item) => item.propertyId === this.skuProperty.positionCategory.skuPropertyValueId
        )
      ) {
        this.positionCategoryOptions.push({
          propertyId: this.skuProperty.positionCategory.skuPropertyValueId,
          name: this.skuProperty.positionCategory.skuPropertyName
        } as TrainingPropertyResponse)
      }
    }
    /**
     * 获取培训对象 - 职业卫生行业
     */
    async getTrainingObjectByOptions() {
      const data = await QueryTrainingObject.queryTrainingObjectByIndustry(
        this.skuProperties.industry,
        this.skuQueryParams.industryPropertyId
      )
      this.trainingObjectOptions = data
      if (
        this.skuProperty.trainingObject?.skuPropertyValueId &&
        !this.trainingObjectOptions?.find(
          (item) => item.propertyId === this.skuProperty.trainingObject.skuPropertyValueId
        )
      ) {
        this.trainingObjectOptions.push({
          propertyId: this.skuProperty.trainingObject.skuPropertyValueId,
          name: this.skuProperty.trainingObject.skuPropertyName
        } as TrainingPropertyResponse)
      }
    }
    /**
     * 设置默认行业
     */
    setDefaultIndustry() {
      // 暂时不考虑行业没了或新增的情况。。
      if (this.industryOptions?.length === 1) {
        this.skuProperties.industry = this.industryOptions[0].id
        this.handleIndustryChange()
      }
    }
    //获取教师学段选项
    async getStudyPeriodOptions() {
      await QueryGrade.queryGradeByIndustry()
      this.studyPeriodOptions = QueryGrade.gradeList
    }
    //获取教师学科选项
    async getSubjectOptions(val?: string) {
      this.skuProperties.subject = ''
      const res = this.skuProperties.studyPeriod
        ? await QuerySubject.querySubjectByIndustry(this.skuProperties.studyPeriod)
        : val
        ? await QuerySubject.querySubjectByIndustry(val)
        : []
      this.subjectOptions = res
    }

    /**
     * 获取科目类型选项
     */
    async getSubjectTypeOptions() {
      const res = await QuerySubjectType.querySubjectTypeList(
        this.skuQueryParams.industryPropertyId,
        this.skuProperties.industry
      )
      this.subjectTypeOptions = res.isSuccess() ? QuerySubjectType.subjectTypeList : ([] as SubjectTypeVo[])
      if (
        this.skuProperty.subjectType?.skuPropertyValueId &&
        !this.subjectTypeOptions?.find((item) => item.propertyId === this.skuProperty.subjectType.skuPropertyValueId)
      ) {
        this.subjectTypeOptions.push({
          propertyId: this.skuProperty.subjectType.skuPropertyValueId,
          name: this.skuProperty.subjectType.skuPropertyName
        } as SubjectTypeVo)
      }
      // TODO 很粗糙的判断方式，待优化
      this.subjectTypeOptions?.forEach((el: SubjectTypeVo) => {
        if (el.name === '专业') {
          this.envConfig.professionalSubjectTypeId = el.propertyId
        }
        if (el.name === '公需+专业') {
          this.envConfig.mixedSubjectTypeId = el.propertyId
        }
      })
    }

    /**
     * 获取培训专业 - 人社行业
     */
    async getSocietyTrainingMajorOptions() {
      this.societyMajorProps = {
        lazy: false,
        value: 'propertyId',
        label: 'optionName',
        multiple: false,
        checkStrictly: false
      }
      const res = await QueryTrainingCategory.queryTrainingCategory(
        this.skuQueryParams.industryPropertyId,
        this.skuProperties.industry
      )
      let majorList = res.isSuccess() ? QueryTrainingCategory.trainingCategoryList : ([] as TrainingCategoryVo[])
      this.societyMajorOptions = new Array<SocietyTrainingMajorVo>()
      await Promise.all(
        majorList?.map(async (el: TrainingCategoryVo) => {
          const option = new SocietyTrainingMajorVo()
          Object.assign(option, el)
          option.children = (await this.getAllDataList(el.propertyId)) as any
          this.societyMajorOptions.push(option)
        })
      )
      if (this.skuProperty.trainingMajor?.skuPropertyValueId && !majorList.length) {
        majorList = await QueryOperationIndustry.getOperationTraining(this.skuProperties.industry)
        await Promise.all(
          majorList.map(async (item) => {
            item['optionName'] = item.name
            const children = await QueryOperationIndustry.getIndustryDetail(item.propertyId)
            item['children'] = children?.map((item) => {
              item['leaf'] = true
              item['optionName'] = item.name
              return item
            })
          })
        )
        this.societyMajorOptions.push(...(majorList as SocietyTrainingMajorVo[]))
      }
    }
    /**
     * 获取执业类别 - 药师行业
     */
    async getPractCategory() {
      this.yssocietyMajorProps = {
        lazy: false,
        value: 'propertyId',
        label: 'optionName',
        multiple: false,
        checkStrictly: false
      }
      const res = await QueryOperationIndustry.getCertificatesType(this.skuProperties.industry)
      const majorList = res ? res : ([] as TrainingCategoryVo[])
      this.constructionTrainingMajorOptions = [] as SocietyTrainingMajorVo[]
      // const list = majorList.map(item => {
      //   const option = new SocietyTrainingMajorVo()
      //   Object.assign(option, item)
      //   return option
      // })
      // const tree = new AssembleTree(list, 'propertyId', 'parentId')
      // this.constructionTrainingMajorOptions = tree.assembleTree()
      await Promise.all(
        majorList.map(async (item) => {
          item['optionName'] = item.name
          const children = await QueryOperationIndustry.getPractitionerCategory(item.propertyId)
          item['children'] = children?.map((item) => {
            item['leaf'] = true
            item['optionName'] = item.name
            return item
          })
        })
      )
      this.constructionTrainingMajorOptions.push(...(majorList as SocietyTrainingMajorVo[]))

      console.log(this.constructionTrainingMajorOptions, 'constructionTrainingMajorOptions')
    }
    /**
     * 获取培训专业 - 人社行业
     */
    async getSocietyTrainingMajorOptionsV2() {
      this.societyMajorProps = {
        lazy: false,
        value: 'propertyId',
        label: 'optionName',
        multiple: false,
        checkStrictly: false
      }
      const res = await QueryTrainingCategory.queryTrainingCategoryV2(
        this.skuQueryParams.industryPropertyId,
        this.skuProperties.industry
      )
      const majorList = res.isSuccess() ? QueryTrainingCategory.trainingCategoryListV2 : ([] as TrainingCategoryVo[])
      this.societyMajorOptions = new Array<SocietyTrainingMajorVo>()
      const list = majorList?.map((el: TrainingCategoryVo) => {
        const option = new SocietyTrainingMajorVo()
        Object.assign(option, el)
        return option
      })
      const tree = new AssembleTree(list, 'propertyId', 'parentId')
      this.societyMajorOptions = tree.assembleTree()
    }
    /**
     * 获取子节点列表
     */
    async getAllDataList(propertyId: string) {
      const params: MajorParam = new MajorParam()
      params.industryPropertyId = this.skuQueryParams.industryPropertyId
      params.parentPropertyId = propertyId
      const res = await QueryTrainingMajor.queryTrainingMajor(params)
      const tree = res.isSuccess() ? QueryTrainingMajor.trainingMajorList : ([] as TrainingCategoryVo[])
      // 设置对应子节点名称
      tree.map(async (item) => {
        Object.assign(item, { leaf: true, optionName: this.optionName(item) })
      })
      return tree.length > 0 ? tree : undefined
    }

    /**
     * 获取培训类别 - 建设行业/职业卫生行业
     */
    async getTrainingCategoryOptions() {
      const res = await QueryTrainingCategory.queryTrainingCategory(
        this.skuQueryParams.industryPropertyId,
        this.skuProperties.industry
      )
      this.trainingCategoryOptions = res.isSuccess()
        ? QueryTrainingCategory.trainingCategoryList
        : ([] as TrainingCategoryVo[])
      if (
        this.skuProperty.trainingCategory?.skuPropertyValueId &&
        !this.trainingCategoryOptions?.find(
          (item) => item.propertyId === this.skuProperty.trainingCategory.skuPropertyValueId
        )
      ) {
        this.trainingCategoryOptions.push({
          propertyId: this.skuProperty.trainingCategory.skuPropertyValueId,
          name: this.skuProperty.trainingCategory.skuPropertyName
        } as TrainingCategoryVo)
      }
    }

    /**
     * 获取培训专业 - 建设行业
     */
    async getConstructionTrainingMajorOptions() {
      this.skuQueryParams.parentPropertyId = this.skuProperties.trainingCategory
      console.log(this.skuQueryParams, 'this.skuQueryParams')
      if (!this.skuQueryParams.parentPropertyId)
        return (this.constructionTrainingMajorOptions = [] as TrainingMajorVo[])
      const res = await QueryTrainingMajor.queryTrainingMajor(this.skuQueryParams)

      this.constructionTrainingMajorOptions = res.isSuccess()
        ? QueryTrainingMajor.trainingMajorList
        : ([] as TrainingMajorVo[])
      if (
        this.skuProperty.trainingMajor?.skuPropertyValueId &&
        !this.constructionTrainingMajorOptions?.find(
          (item) => item.propertyId === this.skuProperty.trainingMajor.skuPropertyValueId
        )
      ) {
        this.constructionTrainingMajorOptions.push({
          propertyId: this.skuProperty.trainingMajor.skuPropertyValueId,
          name: this.skuProperty.trainingMajor.skuPropertyName
        } as TrainingCategoryVo)
      }
    }

    /**
     * 专业回显-人社行业
     */
    async societyTrainingMajorEcho(id: string) {
      const societyMajor = this.societyMajorOptions.find((item) => {
        const societyMajorItem = item.children?.some((el) => el.propertyId === id)
        if (societyMajorItem) return item
      })
      if (societyMajor) this.skuProperties.societyTrainingMajor.unshift(societyMajor?.propertyId)
      console.log(societyMajor, this.skuProperties.societyTrainingMajor)
    }

    /**
     * 清空选项 - 字符串
     */
    clearString(type: string) {
      if (!this.skuProperties[type]) return
      this.skuProperties[type] = ''
    }

    /**
     * 清空选项 - 字符串数组
     */
    clearStringArr(type: string) {
      if (!this.skuProperties[type]) return
      this.skuProperties[type] = [] as string[]
    }

    /**
     * 培训行业切换
     */
    async handleIndustryChange() {
      if (!this.skuProperties.industry) return
      this.skuProperties.subjectType = ''
      this.skuProperties.trainingCategory = ''
      this.skuProperties.societyTrainingMajor = [] as string[]
      this.skuProperties.constructionTrainingMajor = ''
      this.skuProperties.jobLevel = ''
      this.skuProperties.trainingObject = ''
      this.skuProperties.positionCategory = ''
      this.skuProperties.studyPeriod = ''
      this.skuProperties.subject = ''
      this.skuProperties.occupational = [] as string[]
      await this.getIndustryDifference()
      if (this.isConstructionIndustry || this.isSocietyIndustry || this.isPharmacistIndustry)
        await this.getSubjectTypeOptions()
      if (this.isConstructionIndustry || this.isOccupationalHealthIndustry) await this.getTrainingCategoryOptions()
      if (this.isConstructionIndustry) await this.getConstructionTrainingMajorOptions()
      if (this.isSocietyIndustry) await this.getSocietyTrainingMajorOptionsV2()
      if (this.isworkServiceIndustry) await this.getTechnologyLevelOptions()
      if (this.isOccupationalHealthIndustry) await this.getTrainingObjectByOptions()
      if (this.isOccupationalHealthIndustry && this.skuProperties.trainingObject)
        await this.getPositionCategoryOptions()
      if (this.isTeacherIndustry) await this.getStudyPeriodOptions()
      if (this.isPharmacistIndustry) await this.getPractCategory()
      this.setCommodityBasicInfo()
    }

    /**
     * 培训类别切换
     */
    async handleTrainingCategoryChange() {
      this.skuProperties.constructionTrainingMajor = ''
      await this.getConstructionTrainingMajorOptions()
    }

    /**
     * 获取培训对象筛选项
     * @returns {Promise<void>}
     */
    async handleTrainingObjectChange() {
      this.skuProperties.positionCategory = ''
      await this.getPositionCategoryOptions()
    }
    /**
     * 根据培训行业差异化
     */
    async getIndustryDifference() {
      const selectedIndustry = this.industryOptions?.find((el) => el.id === this.skuProperties.industry)
      this.skuQueryParams.industryPropertyId = (selectedIndustry && selectedIndustry.propertyId) || ''
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.skuQueryParams.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const jobLevel = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const trainingObject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const configYsType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.PRACTITIONER_CATEGORY)
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.jobLevel = jobLevel > -1
      this.skuVisible.trainingObject = trainingObject > -1
      this.skuVisible.configYsType = configYsType > -1
    }

    /**
     * 表单校验 - sku配置
     */
    validateForm() {
      let isValid = false
      this.skuPropertiesLinkageForm.validate((valid: number) => {
        if (valid) {
          isValid = true
        }
      })
      return isValid
    }

    /**
     * 设置参数
     */
    setCommodityBasicInfo() {
      this.$emit('setCommodityBasicInfo')
    }
  }
</script>

<style lang="scss" scoped>
  .pure {
    margin: 0;
    padding: 0;
  }
</style>
