"""独立部署的微服务,K8S服务名:ms-basicdata-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""@Description  身份证姓名获取角色id
		<AUTHOR>
		@Date 2023/5/8 15:33
	"""
	findRoleListByIdentity(request:UserRequest):[RoleResponse] @optionalLogin
	"""查询资讯展示详细
		@param commonRequest 通用查询条件，与查询列表相同的查询条件
		@return NewsDetailWithPreviousAndNext 详细资讯信息及上下资讯id
	"""
	getCommonNewsDetailWithPreviousAndNext(commonRequest:NewsWithPreviousAndNextCommonRequest):NewsDetailWithPreviousAndNextCommonResponse @optionalLogin
	"""功能描述：通用 根据账户ID查询企业单位管理员详情查询接口
		描述：查询当前企业单位下指定管理员的信息，如不存在返回null
		@param accountId               : 帐户ID
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
		@date : 2022年6月9日 16:50:10
	"""
	getEnterpriseUnitAdminInfoByPublish(accountId:String):EnterpriseUnitAdminInfoResponse
	"""功能描述：企业-当前登录企业管理员信息
		描述：查询当前登录管理员的信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
	"""
	getEnterpriseUnitAdminInfoInMyself:EnterpriseUnitAdminInfoResponse
	"""功能描述：人社讲师-查询企业单位信息-详情接口
		描述：人社讲师-查询企业单位信息-详情接口
		@param unitId                  :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse
		@date : 2022年6月9日 10:49:47
	"""
	getEnterpriseUnitInfoInLibraryTeacher(unitId:String):EnterpriseUnitInfoResponse
	"""功能描述：学员-查询企业单位信息-详情接口
		描述：学员-查询企业单位信息-详情接口
		@param unitId                  :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse
		@date : 2022年6月9日 10:49:47
	"""
	getEnterpriseUnitInfoInStudent(unitId:String):EnterpriseUnitInfoResponse @optionalLogin
	"""根据分类id获取顶级分类信息
		@param rootCategoryCode 顶级分类代码
		@param code 分类代码
		@return
	"""
	getNewsCategoryId(rootCategoryCode:String!,code:String!):NewsCategoryResponse @optionalLogin
	"""查询资讯展示详细
		@param newId 资讯id
		@return NewsDetailWithPreviousAndNext 详细资讯信息及上下资讯id
	"""
	getNewsDetailWithPreviousAndNext(newId:String!):NewsDetailWithPreviousAndNext @optionalLogin
	"""功能描述 :用户-查询当前登录用户-详情接口
		描述：查询当前登录用户的详细信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.StudentInfoResponse
		@date : 2022/3/31 16:54
	"""
	getUserInfoInMyself:UserInfoResponse
	listChildNewsCategory(status:Int!,necId:String!):[NewsCategoryResponse] @optionalLogin
	"""查询弹窗公告列表
		@param topNum top数量,在1~50之间
		@return 资讯信息列表
	"""
	listPopUpsNews(topNum:Int!):[NewsInfoResponse] @optionalLogin
	"""查询浏览数最多的资讯列表
		@param topNum top数量,在1~50之间
		@return 资讯信息列表
	"""
	listReviewTopNews(topNum:Int!):[NewsInfoResponse] @optionalLogin
	listRootNewsCategory(status:Int!):[NewsCategoryResponse] @optionalLogin
	"""查询最多资讯的资讯分类列表
		@param necId  排除的资讯分类id
		@param topNum top数量,在1~50之间
		@return NewsCategoryResponse 资讯分类信息
	"""
	listTopNewsCategory(necId:String!,topNum:Int!):[NewsCategoryResponse] @optionalLogin
	"""查询资讯分页列表-通用
		@param request 信息中心资讯查询条件(资讯分类id与资讯发布的地区编码)
		@return 资讯分页列表信息
	"""
	pageCommonSimpleNewsByPublish(request:NewsQueryCommonRequest,page:Page):SimpleNewsByPublishCommonResponsePage @page(for:"SimpleNewsByPublishCommonResponse") @optionalLogin
	"""首页根据条件查询资讯列表
		@param newsFrontQueryRequest 首页资讯查询条件
		@param page                  分页信息
		@return
		@throws InvalidProtocolBufferException
	"""
	pageCompleteNewsByCodeList(newsFrontQueryRequest:NewsFrontQueryByCodeRequest,page:Page):CompleteNewsByPublishResponsePage @page(for:"CompleteNewsByPublishResponse") @optionalLogin
	"""首页根据顶级分类代码查询资讯列表
		@param newsFrontQueryRequest 首页资讯查询条件
		@param page                  分页信息
		@return
		@throws InvalidProtocolBufferException
	"""
	pageCompleteNewsByRootCategoryCode(newsFrontQueryRequest:NewsFrontQueryRequest,page:Page):CompleteNewsByPublishResponsePage @page(for:"CompleteNewsByPublishResponse") @optionalLogin
	"""查询资讯分页列表
		@param necId 资讯分类id
		@return 资讯分页列表信息
	"""
	pageSimpleNewsByPublish(necId:String!,page:Page):SimpleNewsByPublishResponsePage @page(for:"SimpleNewsByPublishResponse") @optionalLogin
	"""查询资讯分页列表
		@param request 信息中心资讯查询条件(资讯分类id与资讯发布的地区编码)
		@return 资讯分页列表信息
	"""
	pageSimpleNewsByPublishAndAreaCodePath(request:NewsQueryByAreaCodePathRequest,page:Page):SimpleNewsByPublishResponsePage @page(for:"SimpleNewsByPublishResponse") @optionalLogin
	"""查询资讯分页列表，可以根据排序字段进行排序
		@param request 信息中心资讯查询条件
		@return 资讯分页列表信息
	"""
	pageSimpleNewsByPublishForOrder(request:NewsQueryForOrderRequest,page:Page):SimpleNewsByPublishResponseWithReviewCountPage @page(for:"SimpleNewsByPublishResponseWithReviewCount") @optionalLogin
	"""功能描述：查询用户信息列表-分页接口
		描述：查询子项目下的学员分页信息，默认按创建时间降序排，如不存在返回null
		@param page                    :
		@param request                 : 查询参数
		@param dataFetchingEnvironment :
		@date : 2022年11月7日 09:37:49
	"""
	pageUserInfoInGeneral(page:Page,request:UserQueryRequest):UserInfoResponsePage @page(for:"UserInfoResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""功能描述：账户信息查询条件
	@Author： wtl
	@Date： 2022年5月11日 15:30:56
"""
input AccountRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.AccountRequest") {
	"""账户类型 1：企业帐户，2：企业个人帐户，3：个人帐户
		@see AccountTypes
	"""
	accountTypeList:[Int]
	"""账户状态 1：正常，2：冻结
		@see AccountStatus
	"""
	statusList:[Int]
	"""创建时间范围"""
	createTimeScope:DateScopeRequest
	"""创建人用户id"""
	createdUserId:String
	"""单位id （原始单位id，不会随着id 人员与单位的关系变化而变化）"""
	unitId:[String]
	"""单位id匹配方式 默认-1、and匹配 2、or匹配
		@see MatchTypeConstant
	"""
	unitIdMatchType:Int
}
"""功能描述：登录认证查询条件
	@Author： wtl
	@Date： 2022年1月26日 09:30:12
"""
input AuthenticationRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.AuthenticationRequest") {
	"""帐号"""
	identity:String
}
"""功能描述：用户基本查询条件
	@Author： wtl
	@Date： 2022年1月26日 09:30:12
"""
input UserRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.UserRequest") {
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	phoneMatchType:Int
}
"""首页资讯查询条件"""
input NewsFrontQueryByCodeRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.news.NewsFrontQueryByCodeRequest") {
	"""资讯标题 可为空"""
	title:String
	"""资讯分类代码  必填"""
	codes:[String]
	"""顶级分类代码  必填"""
	rootCategoryCode:String
}
"""首页资讯查询条件"""
input NewsFrontQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.news.NewsFrontQueryRequest") {
	"""资讯标题 可为空"""
	title:String
	"""资讯分类编代码  可为空"""
	code:String
	"""顶级分类代码  必填"""
	rootCategoryCode:String
}
"""资讯查询条件"""
input NewsQueryByAreaCodePathRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.news.NewsQueryByAreaCodePathRequest") {
	"""资讯分类编号"""
	necId:String!
	"""资讯发布的地区编码"""
	areaCodePath:String!
}
"""资讯查询条件"""
input NewsQueryCommonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.news.NewsQueryCommonRequest") {
	"""标题"""
	title:String
	"""资讯分类编号"""
	necId:String
	"""资讯发布的地区编码"""
	areaCodePath:String
	"""是否包含下级地区 -默认 false不包含 比如/350000 只查询 /350000的"""
	includeSubordinates:Boolean
	"""排序"""
	order:[OrderRequest]
}
"""资讯查询条件，支持针对不同字段"""
input NewsQueryForOrderRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.news.NewsQueryForOrderRequest") {
	"""资讯分类编号"""
	necId:String!
	"""排序字段名称，格式如下
		发布时间：0
		浏览数量：1
		<p>
		若排序字段为-1，默认按照从置顶到非置顶，发布时间从新到旧顺序排列
	"""
	orderFiled:Int
	"""排序方式
		0 升序
		1 降序
	"""
	orderType:Int
}
"""资讯查询条件"""
input NewsWithPreviousAndNextCommonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.news.NewsWithPreviousAndNextCommonRequest") {
	"""资讯编号"""
	newId:String
	"""资讯发布的地区编码"""
	areaCodePath:String
}
input OrderRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.news.nested.OrderRequest") {
	"""排序字段
		发布时间：0
		浏览数量：1
		置顶(0-不置顶 1-置顶)：   2
		发布地区编码: 3
	"""
	orderField:Int
	"""排序方式  0 升序   1 降序"""
	orderType:Int
}
"""功能描述：学员集体缴费信息
	@Author： wtl
	@Date： 2022年4月21日 08:58:49
"""
input CollectiveRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.student.CollectiveRequest") {
	"""集体缴费管理员用户id集合"""
	collectiveUserIdList:[String]
}
"""功能描述：学员查询条件
	@Author： wtl
	@Date： 2022年1月26日 10:10:33
"""
input UserInfoRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.student.UserInfoRequest") {
	"""工作单位名称（模糊）"""
	companyName:String
	"""单位所属地区路径集合（模糊，右like）"""
	regionPathList:[String]
	"""单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	regionPathListMatchType:Int
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	phoneMatchType:Int
}
"""功能描述：学员查询条件
	@Author： wtl
	@Date： 2022年1月26日 10:10:33
"""
input UserQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.student.UserQueryRequest") {
	"""账户信息"""
	account:AccountRequest
	"""用户信息"""
	user:UserInfoRequest
	"""用户认证信息"""
	authentication:AuthenticationRequest
	"""集体缴费信息"""
	collective:CollectiveRequest
	"""排序"""
	sortList:[UserSortRequest]
}
"""功能描述 : 学员排序参数
	@date : 2022/4/1 17:15
"""
input UserSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.request.student.UserSortRequest") {
	"""排序字段"""
	sortField:PersonAccountSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
input DateScopeRequest @type(value:"com.fjhb.ms.basicdata.repository.DateScopeRequest") {
	beginTime:DateTime
	endTime:DateTime
}
enum SortTypeEnum @type(value:"com.fjhb.ms.basicdata.enums.SortTypeEnum") {
	ASC
	DESC
}
type RegionModel @type(value:"com.fjhb.ms.basicdata.model.RegionModel") {
	regionId:String
	regionPath:String
	provinceId:String
	provinceName:String
	cityId:String
	cityName:String
	countyId:String
	countyName:String
}
"""功能描述：账户信息
	@Author： wtl
	@Date： 2022年5月11日 15:30:56
"""
type AccountResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.AccountResponse") {
	"""账户id"""
	accountId:String
	"""帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
		@see AccountTypes
	"""
	accountType:Int
	"""单位信息"""
	unitInfo:UnitInfoResponse
	"""所属顶级企业帐户Id"""
	rootAccountId:String
	"""帐户状态 1：正常，2：冻结，3：注销
		@see AccountStatus
	"""
	status:Int
	"""注册方式
		0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
		@see AccountRegisterTypes
	"""
	registerType:Int
	"""来源类型
		0内置，1项目主网站，2安卓，3IOS
		@see AccountSourceTypes
	"""
	sourceType:Int
	"""创建时间"""
	createdTime:DateTime
	"""最后更新时间"""
	lastUpdateTime:DateTime
}
"""功能描述：帐户认证信息
	@Author： wtl
	@Date： 2022年5月11日 14:23:18
"""
type AuthenticationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.AuthenticationResponse") {
	"""帐号"""
	identity:String
	"""认证标识类型
		1用户名,2手机,3身份证,4邮箱,5第三方OpenId
	"""
	identityType:Int
	"""认证方式状态 1启用，2禁用
		@see AuthenticationStatusEnum
	"""
	status:Int
}
"""功能描述：角色信息
	@Author： wtl
	@Date： 2022/1/24 20:17
"""
type RoleResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.RoleResponse") {
	"""角色id"""
	roleId:String
	"""角色名称"""
	roleName:String
	"""角色类型
		（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
		@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
	"""
	roleType:String
	"""角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
		@see RoleCategories
	"""
	roleCategory:Int
}
"""功能描述 : 企业单位管理员信息
	@date : 2022/6/18 12:24
"""
type EnterpriseUnitAdminInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse") {
	"""企业单位管理员归属信息"""
	enterpriseUnitAdminOwner:EnterpriseUnitAdminOwnerResponse
	"""账户信息"""
	accountInfo:AccountResponse
	"""管理员用户信息"""
	userInfo:AdminUserInfoResponse
	"""人员信息"""
	personInfo:EnterpriseUnitPersonInfoResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
	"""角色信息集合"""
	roleList:[RoleResponse]
}
"""功能描述：管理员用户信息
	@Author： wtl
	@Date： 2022年1月25日 15:48:48
"""
type AdminUserInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.nested.AdminUserInfoResponse") {
	"""管辖地区集合"""
	manageRegionList:[RegionModel]
	"""办公室（所在处/科室）"""
	office:String
	"""岗位/职位"""
	position:String
	"""备注"""
	remark:String
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
}
"""功能描述 : 企业单位管理员归属查询条件
	@date : 2022年9月2日 10:53:02
"""
type EnterpriseUnitAdminOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.nested.EnterpriseUnitAdminOwnerResponse") {
	"""企业单位id"""
	enterpriseUnitIdList:[String]
}
"""人员信息模型"""
type EnterpriseUnitPersonInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.nested.EnterpriseUnitPersonInfoResponse") {
	"""是否法人帐号"""
	isCorporateAccount:Boolean
	"""人员实名认证信息"""
	personIdentityVerificationInfo:PersonIdentityVerificationResponse
}
"""人员实名认证信息模型"""
type PersonIdentityVerificationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.nested.PersonIdentityVerificationResponse") {
	"""是否已认证"""
	identityVerification:Boolean
	"""认证渠道(1:闽政通 2：腾讯)"""
	identityVerificationChannel:Int
	"""认证时间"""
	identityVerificationTime:DateTime
}
"""单位信息模型"""
type UnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.admin.nested.UnitInfoResponse") {
	"""单位ID"""
	unitId:String
}
"""网校前端查询的资讯信息"""
type CompleteNewsByPublishResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.CompleteNewsByPublishResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""摘要"""
	summary:String
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""图片路径"""
	coverPath:String
	"""分类名称"""
	name:String
}
"""资讯分类信息"""
type NewsCategoryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.NewsCategoryResponse") {
	"""资讯分类编号"""
	newsCategoryId:String
	"""分类名称"""
	categoryName:String
	"""分类代码"""
	code:String
}
"""详细资讯信息"""
type NewsDetailResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.NewsDetailResponse") {
	"""资讯编号"""
	newId:String
	"""平台id"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	serviceId:String
	"""分类id"""
	necId:String
	"""标题"""
	title:String
	"""摘要"""
	summary:String
	"""内容"""
	content:String
	"""封面图片路径"""
	coverPath:String
	"""来源"""
	source:String
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""是否置顶"""
	isTop:Boolean!
	"""发布地区编码"""
	areaCodePath:String
	"""资讯状态 0 草稿 1正常"""
	status:Int!
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""浏览数量"""
	reviewCount:Int!
	"""弹窗起始时间"""
	popupBeginTime:DateTime
	"""弹窗截止时间"""
	popupEndTime:DateTime
}
"""详细资讯信息及上下资讯id"""
type NewsDetailWithPreviousAndNext @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.NewsDetailWithPreviousAndNext") {
	"""详细资讯信息"""
	newsDetail:NewsDetailResponse
	"""上一条资讯id"""
	previousId:String
	"""下一条资讯id"""
	nextId:String
}
"""详细资讯信息及上下资讯id"""
type NewsDetailWithPreviousAndNextCommonResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.NewsDetailWithPreviousAndNextCommonResponse") {
	"""详细资讯信息"""
	newsDetail:NewsDetailResponse
	"""上一条资讯id"""
	previousId:String
	"""下一条资讯id"""
	nextId:String
}
"""资讯信息"""
type NewsInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.NewsInfoResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""发布时间"""
	publishTime:DateTime
	"""资讯分类id"""
	necId:String
}
"""网校前端查询的资讯信息"""
type SimpleNewsByPublishCommonResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.SimpleNewsByPublishCommonResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""是否置顶"""
	isTop:Boolean!
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""摘要"""
	summary:String
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
}
"""网校前端查询的资讯信息"""
type SimpleNewsByPublishResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.SimpleNewsByPublishResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""是否置顶"""
	isTop:Boolean!
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""摘要"""
	summary:String
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
}
"""网校前端查询的资讯信息"""
type SimpleNewsByPublishResponseWithReviewCount @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.news.SimpleNewsByPublishResponseWithReviewCount") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""是否置顶"""
	isTop:Boolean!
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""摘要"""
	summary:String
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""浏览数量"""
	reviewCount:Int!
}
"""功能描述：学员信息
	@Author： wtl
	@Date： 2022年1月26日 10:38:15
"""
type UserInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.UserInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""用户信息"""
	userInfo:UserInformationResponse
	"""人员信息"""
	personInfo:PersonInfoResponse
	"""第三方绑定信息"""
	openPlatformBind:OpenPlatformBindResponse
}
"""功能描述：附件信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type AttachmentInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.AttachmentInfoResponse") {
	"""附件名称"""
	name:String
	"""附件地址"""
	url:String
}
"""功能描述：学员绑定信息
	@Author： wtl
	@Date： 2022年5月12日 14:42:51
"""
type OpenPlatformBindResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.OpenPlatformBindResponse") {
	"""是否绑定微信"""
	bindWX:Boolean!
	"""微信昵称"""
	nickNameByWX:String
}
type PersonInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.PersonInfoResponse") {
	"""人员ID"""
	personId:String
	"""姓名"""
	name:String
	"""证件类型
		@see IdCardTypes
	"""
	idCardType:Int
	"""身份证号"""
	idCard:String
	"""性别
		@see Genders
	"""
	gender:Int
	"""出生日期"""
	birthday:DateTime
	"""民族（字典）"""
	ethnicity:String
	"""居住地区"""
	resideCityArea:RegionResponse
	"""联系地址"""
	address:String
	"""手机号"""
	phone:String
}
type RegionResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.RegionResponse") {
	"""地区ID"""
	regionId:String
	"""地区路径"""
	regionPath:String
	"""省份ID"""
	provinceId:String
	"""省份名称"""
	provinceName:String
	"""地市ID"""
	cityId:String
	"""地市名称"""
	cityName:String
	"""区县ID"""
	countyId:String
	"""区县名称"""
	countyName:String
}
"""功能描述：学员证书信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentCertificateResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.StudentCertificateResponse") {
	"""证书id"""
	certificateId:String
	"""证书编号"""
	certificateNo:String
	"""证书类别"""
	certificateCategory:String
	"""注册专业"""
	registerProfessional:String
	"""发证日期"""
	releaseStartTime:DateTime
	"""证书有效期"""
	certificateEndTime:DateTime
	"""证书附件信息"""
	attachmentList:[AttachmentInfoResponse]
}
"""功能描述：学员行业信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentIndustryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.StudentIndustryResponse") {
	"""用户行业id"""
	userIndustryId:String
	"""行业id"""
	industryId:String
	"""一级专业类别id"""
	firstProfessionalCategory:String
	"""二级专业类别id"""
	secondProfessionalCategory:String
	"""职称等级"""
	professionalQualification:String
	"""学员证书信息集合"""
	userCertificateList:[StudentCertificateResponse]
}
"""功能描述：用户信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type UserInformationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.student.nested.UserInformationResponse") {
	"""用户昵称"""
	nickName:String
	"""单位所属地区"""
	region:RegionModel
	"""工作单位名称"""
	companyName:String
	"""头像地址"""
	photo:String
	"""联系地址"""
	address:String
	"""学员行业信息集合"""
	userIndustryList:[StudentIndustryResponse]
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
}
"""功能描述：企业单位信息
	@Author： wtl
	@Date： 2022年6月9日 14:20:55
"""
type EnterpriseUnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.EnterpriseUnitInfoResponse") {
	"""企业单位业务归属信息"""
	businessOwnerInfo:EnterpriseUnitBusinessOwnerResponse
	"""单位基本信息"""
	unitBase:EnterpriseUnitBaseResponse
	"""经营信息"""
	businessInfo:BusinessInfoResponse
	"""单位认证信息"""
	unitIdentityVerificationInfo:UnitIdentityVerificationResponse
}
"""功能描述：附件信息"""
type AttachmentResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.AttachmentResponse") {
	"""附件id"""
	attachmentId:String
	"""附件类型
		1《营业执照 / 民办法人登记证书/非民办企业法人登记证书》
		2《开展短期职业培训承诺书》（加盖公司公章）》
		3《培训单位基本信息表》（加盖公司公章）》
		4《培训单位办学许可证》（加盖公司公章）》
		5《年度年审合格证书》（加盖公司公章）》
		6 开通依据
	"""
	attachmentType:Int
	"""附件名称"""
	attachmentName:String
	"""附件路径"""
	attachmentPath:String
	"""创建时间"""
	createdTime:DateTime
}
"""功能描述：企业经营信息
	@Author： wtl
	@Date： 2022年6月9日 14:31:03
"""
type BusinessInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.BusinessInfoResponse") {
	"""营业期限起始日期"""
	operatingBeginDate:DateTime
	"""营业期限截止日期"""
	operatingEndDZhjyEnterpriseUnitBackStageQueryResolverate:DateTime
	"""行业信息"""
	industry:IndustryResponse
	"""经营范围,例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。"""
	businessScope:String
	"""主营业务
		例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。
	"""
	mainBusiness:String
}
"""功能描述：单位联系人信息"""
type ContactPersonInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.ContactPersonInfoResponse") {
	"""联系人"""
	contact:String
	"""联系电话"""
	contactPhone:String
}
"""功能描述：企业单位信息
	@Author： wtl
	@Date： 2022年6月9日 14:23:04
"""
type EnterpriseUnitBaseResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.EnterpriseUnitBaseResponse") {
	"""单位ID"""
	unitId:String
	"""单位名称"""
	unitName:String
	"""单位英文名称"""
	enName:String
	"""统一社会信用代码"""
	code:String
	"""单位简称"""
	shortName:String
	"""单位业务类型
		说明：
		1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
		11地区管理单位,12行业主管单位,13技工院校,14职业院校.15线上培训机构,10000实名制报表补贴单位,10001评价机构
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
		@see UnitBusinessQueryTypes
	"""
	businessType:Int
	"""logo"""
	logo:String
	"""法人信息"""
	legalPersonInfo:LegalPersonResponse
	"""单位类型（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）"""
	unitType:UnitTypeResponse
	"""单位规模（1：微型 2：小型 3：中型 4：大型）
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitScales
	"""
	scale:Int
	"""企业经济类型(国有经济、联营经济、私营企业、股份制、港澳台投资、外商投资、其他经济)
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitEconomicTypes
	"""
	economicTypes:Int
	"""产业类别"""
	industrialCategory:String
	"""成立日期"""
	foundedDate:DateTime
	"""联系电话"""
	phone:String
	"""邮政编码"""
	postCode:String
	"""传真"""
	faxNumber:String
	"""注册地区"""
	region:RegionModel
	"""联系地址"""
	address:String
	"""注册地址"""
	registerAddress:String
	"""登记机关"""
	registeredOrgan:String
	"""注册资金"""
	registeredCapital:String
	"""创建时间"""
	createdTime:DateTime
	"""单位状态
		说明：1正常,2冻结
	"""
	status:Int
	"""工商注册号"""
	businessRegistrationNumber:String
	"""纳税人资质"""
	taxpayerQualification:String
	"""邮箱地址"""
	emailAddress:String
	"""联系人信息"""
	contactPersonInfo:ContactPersonInfoResponse
	"""单位资质附件类型"""
	attachmentList:[AttachmentResponse]
}
"""企业单位业务归属信息"""
type EnterpriseUnitBusinessOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.EnterpriseUnitBusinessOwnerResponse") {
	"""企业归属信息路径
		单位路径（若单位为福州市企业，则该值为:"/福建省企业id/福州市企业id"）
	"""
	unitIdPath:String
}
"""行业信息
	<AUTHOR>
	@date 2022-06-18
"""
type IndustryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.IndustryResponse") {
	"""行业信息ID路径"""
	industryIdPath:String
	"""门类"""
	firstLevelIndustryId:String
	"""大类"""
	secondLevelIndustryId:String
	"""中类"""
	thirdLevelIndustryId:String
	"""小类"""
	fourthLevelIndustryId:String
}
"""功能描述：企业法人信息
	@Author： wtl
	@Date： 2022年6月9日 14:31:03
"""
type LegalPersonResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.LegalPersonResponse") {
	"""法定代表人"""
	legalPerson:String
	"""证件类型"""
	idCardType:String
	"""证件号"""
	idCard:String
}
"""单位认证信息模型"""
type UnitIdentityVerificationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.UnitIdentityVerificationResponse") {
	"""是否已认证"""
	identityVerification:Boolean
	"""认证渠道（单位认证渠道：UnitIdentityVerificationChannels 人员认证渠道：PersonIdentityVerificationChannels）
		@see PersonIdentityVerificationChannels
		@see UnitIdentityVerificationChannels
	"""
	identityVerificationChannel:Int
	"""认证时间"""
	identityVerificationTime:DateTime
}
"""单位类型:（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）
	<AUTHOR>
	@date : 2022/6/18 14:15
"""
type UnitTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.zhjy.graphql.response.unit.nested.UnitTypeResponse") {
	"""单位类型ID路径"""
	unitTypeIdPath:String
	"""一级"""
	firstLevelUnitTypeId:String
	"""二级"""
	secondLevelUnitTypeId:String
	"""三级"""
	thirdLevelUnitTypeId:String
}
"""功能描述：学员排序字段
	@Author： wtl
	@Date： 2021/12/27 10:32
"""
enum PersonAccountSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.PersonAccountSortFieldEnum") {
	"""创建时间"""
	createdTime
	"""授予性质"""
	nature
}

scalar List
type SimpleNewsByPublishCommonResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SimpleNewsByPublishCommonResponse]}
type CompleteNewsByPublishResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CompleteNewsByPublishResponse]}
type SimpleNewsByPublishResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SimpleNewsByPublishResponse]}
type SimpleNewsByPublishResponseWithReviewCountPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SimpleNewsByPublishResponseWithReviewCount]}
type UserInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserInfoResponse]}
