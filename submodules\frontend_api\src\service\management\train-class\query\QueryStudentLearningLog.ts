import { LearningOperateTypeEnum } from '@api/service/management/train-class/query/enum/LearningOperateType'
import { Page, ResponseStatus } from '@hbfe/common'
import StudentLearningLogDetail from '@api/service/management/train-class/query/vo/StudentLearningLogDetail'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  PageLearningLogsRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import StudentCourseLearning from '@api/platform-gateway/student-course-learning-query-back-gateway'

/**
 * @description 查询学员学习日志
 */
class QueryStudentLearningLog {
  /**
   * 学号
   */
  private studentNo = ''
  /**
   * 学习类型
   */
  private learningType: number[]
  /**
   * 【必填】学员参训资格Id
   * @private
   */
  qualificationId = ''
  /**
   * 课程Id
   */
  courseId = ''
  /**
   * 课程名称
   */
  courseName = ''
  /**
   * 操作类型
   */
  operateType: LearningOperateTypeEnum = null
  /**
   * 记录时间-开始时间
   */
  recordDateStartTime = ''
  /**
   * 记录时间-结束时间
   */
  recordDateEndTime = ''

  /**
   * 获取请求参数
   * @private
   */
  private getRequest(): PageLearningLogsRequest {
    const request = new PageLearningLogsRequest()
    request.learningTypeList = this.learningType
    request.qualificationId = this.qualificationId
    // if (this.courseId) {
    //   request.courseId = this.courseId
    // }
    if (this.courseName) {
      request.courseName = this.courseName
    }
    if ((this.operateType ?? '') !== '') {
      request.type = this.operateType
    }
    if (this.recordDateStartTime) {
      request.logBeginTime = this.recordDateStartTime
    }
    if (this.recordDateEndTime) {
      request.logEndTime = this.recordDateEndTime
    }
    return request
  }

  /**
   * 获取操作类型
   * @param studentNo 学号
   * @return {Promise<ResponseStatus>}
   */
  async getLearningType(studentNo: string) {
    if (!studentNo) return new ResponseStatus(500, '学号为空')
    const { status, data } = await StudentCourseLearning.getLearningType(studentNo)
    if (status.isSuccess()) {
      this.learningType = data
    }
  }
  /**
   * 【超管】查询学员学习日志列表
   * @param page 分页参数
   * @description 跳转入口：
   * 统计报表-学员学习明细-查阅学习日志
   * 客服咨询-业务咨询-学习内容-学习日志
   */
  async queryStudentLearningLogListInSubProject(page: Page): Promise<StudentLearningLogDetail[]> {
    let result = [] as StudentLearningLogDetail[]
    const request = this.getRequest()
    const { status, data } = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageLearningLogsInServicer({
      page,
      pageLearningLogsRequest: request
    })
    if (data) {
      page.totalPageSize = data.totalPageSize
      page.totalSize = data.totalSize
      if (status && status && data && data.currentPageData && data.currentPageData.length) {
        result = data.currentPageData.map((itm) => {
          const opt = new StudentLearningLogDetail()
          opt.operateType = itm.type
          opt.courseName = itm.courseName
          opt.courseId = itm.courseId
          opt.recordDate = itm.logTime
          return opt
        })
      }
    }
    return result
  }

  /**
   * 【分销商】查询学员学习日志列表
   * @param page 分页参数
   */
  async queryStudentLearningLogListInDistributor(page: Page): Promise<StudentLearningLogDetail[]> {
    let result = [] as StudentLearningLogDetail[]
    const request = this.getRequest()
    const { status, data } =
      await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageLearningLogsInDistributor({
        page,
        pageLearningLogsRequest: request
      })
    if (data) {
      page.totalPageSize = data.totalPageSize
      page.totalSize = data.totalSize
      if (status && status && data && data.currentPageData && data.currentPageData.length) {
        result = data.currentPageData.map((itm) => {
          const opt = new StudentLearningLogDetail()
          opt.operateType = itm.type
          opt.courseName = itm.courseName
          opt.courseId = itm.courseId
          opt.recordDate = itm.logTime
          return opt
        })
      }
    }
    return result
  }
}

export default QueryStudentLearningLog
