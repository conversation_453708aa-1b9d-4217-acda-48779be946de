import { Page, Response } from '@hbfe/common'
import QueryOrderListVo from '@api/service/diff/management/zztt/trade/order/model/QueryOrderListVo'
import OrderDetailVo from '@api/service/diff/management/zztt/trade/order/model/OrderDetailVo'
import MsTradeQuery, {
  OrderBasicDataRequest,
  OrderInfoRequest,
  OrderSortField,
  OrderSortRequest,
  OrderStatisticResponse,
  ReturnOrderRequest,
  SortPolicy,
  SubOrderInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import OrderStatisticResponseVo from '@api/service/management/trade/single/order/query/vo/OrderStatisticResponseVo'
import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'
import UserModule from '@api/service/management/user/UserModule'
import StudentUserInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'
import BuyerInfoVo from '@api/service/management/trade/single/order/query/vo/BuyerInfoVo'
import { OrderTransaction } from '@api/service/management/trade/single/order/query/enum/OrderTransactionStatus'
import { cloneDeep, uniq } from 'lodash'
import QueryTradeConfig from '@api/service/common/trade-config/query/QueryTradeConfig'
import { ReturnOrderStatusEnum } from '@api/service/management/trade/single/order/enum/returnOrderStatusEnum'
import QueryPlatform from '@api/service/diff/common/zztt/dictionary/QueryPlatform'
import DataExportBackstage from '@api/diff-gateway/zztt-data-export-gateway-backstage'
import statisticOrderInServicer from '@api/service/management/trade/single/order/query/graphql/statisticOrderInServicer.graphql'
import TradeQueryFrontGatewayTradeQueryBackstage from '@api/diff-gateway/zztt-trade-query-front-gateway-TradeQueryBackstage'
/**
 * @description 订单查询
 */
class QueryOrder {
  /**
   * 查询订单商品分页
   * @param {Page} page - 分页参数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryOrderList(
    page: Page,
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderDetailVo[]> {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName || queryParams.loginAccount)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) {
        page.totalSize = 0
        page.totalPageSize = 0
        return [] as OrderDetailVo[]
      }
    }
    const request = await queryParams.to(isBusinessConsult)
    // 过滤批次单产生的订单
    if (!request.orderBasicData) request.orderBasicData = new OrderBasicDataRequest()
    // request.orderBasicData.channelTypesList = [1, 3]
    // request.orderBasicData.orderType = 1
    const option = new OrderSortRequest()
    option.field = OrderSortField.ORDER_NORMAL_TIME
    option.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(option) as OrderSortRequest[]
    const response = await MsTradeQuery.pageOrderInServicer({
      page,
      request,
      sortRequest
    })
    await QueryPlatform.queryList()
    page.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize
    const result =
      response.status.isSuccess() && this.isWeightyArr(response.data?.currentPageData)
        ? response.data?.currentPageData?.map(OrderDetailVo.from)
        : ([] as OrderDetailVo[])
    const res = await this.fillBuyerInfo(result)
    // * 添加退款订单信息
    if (isBusinessConsult && response?.data?.currentPageData?.length) {
      // 返回方案id,用于判断是否是华医网特殊方案
      res.map(async (item) => {
        const orderObj = response.data.currentPageData.find((res) => res.orderNo === item.orderNo)
        if (orderObj && orderObj?.subOrderItems && orderObj?.subOrderItems?.length) {
          const detailVo = orderObj.subOrderItems[0].deliveryCommoditySku?.resource
          item.schemeId = (detailVo as any)?.schemeId || ''
        }
      })
      let orderIds = response.data.currentPageData.map((order) => {
        if (order.orderNo) {
          return order.orderNo
        }
      })

      if (orderIds.length) {
        orderIds = uniq(orderIds)
        const request = new ReturnOrderRequest()
        request.subOrderInfo = new SubOrderInfoRequest()
        request.subOrderInfo.orderInfo = new OrderInfoRequest()
        request.subOrderInfo.orderInfo.orderNoList = orderIds
        const newPage = new Page()
        newPage.pageNo = 1
        newPage.pageSize = page.pageSize
        const response = await TradeQueryFrontGatewayTradeQueryBackstage.pageReturnOrderInServicer({
          page: newPage,
          request
        })

        res.map(async (item) => {
          const curOrder = response.data.currentPageData?.find(
            (res) => res.subOrderInfo.orderInfo.orderNo === item.orderNo
          )
          if (curOrder?.returnOrderNo) {
            if ([0, 1, 2, 4, 5, 6].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunding
            }
            if ([8, 9, 10, 11].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunded
            }
            if ([3, 7].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refundFailure
            }
          } else {
            item.returnOrderStatus = ReturnOrderStatusEnum.noRefund
          }
          if (!item.receiveAccountType && item.batchOrderNo) {
            const res = await MsTradeQuery.getBatchOrderInServicer(item.batchOrderNo)
            if (res.status?.isSuccess()) {
              item.receiveAccountType = res.data?.payInfo?.receiveAccount?.receiveAccountType == 1 ? 0 : 1
              item.payChannelName = res.data?.payInfo?.receiveAccount?.payChannelName
            }
          }
        })
      }
    }
    return res
  }

  /**
   * 查询分销订单商品分页
   * @param {Page} page - 分页参数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryFxOrderList(
    page: Page,
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderDetailVo[]> {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return [] as OrderDetailVo[]
    }
    const request = await queryParams.to(isBusinessConsult)
    // 过滤批次单产生的订单
    if (!request.orderBasicData) request.orderBasicData = new OrderBasicDataRequest()
    // request.orderBasicData.channelTypesList = [1, 3]
    // request.orderBasicData.orderType = 1
    const option = new OrderSortRequest()
    option.field = OrderSortField.ORDER_NORMAL_TIME
    option.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(option) as OrderSortRequest[]
    const response = await MsTradeQuery.pageOrderInDistributor({
      page,
      request,
      sortRequest
    })
    page.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize
    const result =
      response.status.isSuccess() && this.isWeightyArr(response.data?.currentPageData)
        ? response.data?.currentPageData?.map(OrderDetailVo.from)
        : ([] as OrderDetailVo[])
    // console.log('filterOrderList', result, response)
    const res = await this.fillBuyerInfo(result)
    // * 添加退款订单信息
    if (isBusinessConsult && response?.data?.currentPageData?.length) {
      let orderIds = response.data.currentPageData.map((order) => {
        if (order.orderNo) {
          return order.orderNo
        }
      })

      if (orderIds.length) {
        orderIds = uniq(orderIds)
        const request = new ReturnOrderRequest()
        request.subOrderInfo = new SubOrderInfoRequest()
        request.subOrderInfo.orderInfo = new OrderInfoRequest()
        request.subOrderInfo.orderInfo.orderNoList = orderIds
        const newPage = new Page()
        newPage.pageNo = 1
        newPage.pageSize = page.pageSize
        const response = await TradeQueryFrontGatewayTradeQueryBackstage.pageReturnOrderInDistributor({
          page: newPage,
          request
        })

        res.map(async (item) => {
          const curOrder = response.data.currentPageData.find(
            (res) => res.subOrderInfo.orderInfo.orderNo === item.orderNo
          )
          if (curOrder?.returnOrderNo) {
            if ([0, 1, 2, 4, 5, 6].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunding
            }
            if ([8, 9, 10].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunded
            }
            if ([3, 7].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refundFailure
            }
          } else {
            item.returnOrderStatus = ReturnOrderStatusEnum.noRefund
          }
          if (!item.receiveAccountType && item.batchOrderNo) {
            const res = await MsTradeQuery.getBatchOrderInDistributor(item.batchOrderNo)
            item.receiveAccountType = res.data.payInfo.receiveAccount.receiveAccountType == 1 ? 0 : 1
            item.payChannelName = res.data.payInfo.receiveAccount.payChannelName
          }
        })
      }
    }
    return res
  }
  /**
   * 获取订单总金额、总数量
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryOrderListStatistic(
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderStatisticResponseVo> {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return new OrderStatisticResponseVo()
    }
    const result = new OrderStatisticResponseVo()
    result.totalOrderCount = await this.getOrderTotalCount(queryParams, isBusinessConsult)
    result.totalOrderAmount = await this.getOrderTotalAmount(queryParams, isBusinessConsult)
    // console.log('orderListStatistic', result)
    return result
  }

  /**
   * 获取订单总金额、总数量 去学时
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryOrderListStatisticRemoveTotalPeriod(
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderStatisticResponseVo> {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName || queryParams.loginAccount)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return new OrderStatisticResponseVo()
    }
    const result = new OrderStatisticResponseVo()
    // result.totalOrderCount = await this.getOrderTotalCount(queryParams, isBusinessConsult)
    const res = await this.getOrderTotalAmountRemoveTotalPeriod(queryParams, isBusinessConsult)
    result.totalOrderAmount = res.totalOrderAmount
    result.totalOrderCount = res.totalOrderCount
    // console.log('orderListStatistic', result)
    return result
  }

  /**
   * 获取总数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  private async getOrderTotalCount(queryParams: QueryOrderListVo, isBusinessConsult: boolean): Promise<number> {
    let result = 0
    // 获取下单数
    const params = await queryParams.to(isBusinessConsult)
    const page = new Page()
    // 过滤批次单产生的订单
    if (!params.orderBasicData) params.orderBasicData = new OrderBasicDataRequest()
    // params.orderBasicData.orderType = 1
    const response = await MsTradeQuery.pageOrderInServicer({
      page,
      request: params
    })
    if (response.status?.isSuccess()) {
      result = response.data?.totalSize ?? 0
    }
    return result
  }

  /**
   * 获取交易成功总金额
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  private async getOrderTotalAmount(queryParams: QueryOrderListVo, isBusinessConsult: boolean): Promise<number> {
    let result = 0
    const params = cloneDeep(queryParams)
    params.orderStatus = OrderTransaction.Complete_Transaction
    const remoteParams = await params.to(isBusinessConsult)
    // 过滤批次单产生的订单
    if (!remoteParams) remoteParams.orderBasicData = new OrderBasicDataRequest()
    // remoteParams.orderBasicData.orderType = 1
    const response = await MsTradeQuery.statisticOrderInServicer(remoteParams)
    if (response.status?.isSuccess()) {
      result = response.data?.totalOrderAmount ?? 0
    }
    return result
  }
  /**
   * 获取交易成功总金额
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  private async getOrderTotalAmountRemoveTotalPeriod(
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderStatisticResponse> {
    const params = cloneDeep(queryParams)
    params.orderStatus = OrderTransaction.Complete_Transaction
    const remoteParams = await params.to(isBusinessConsult)
    // 过滤批次单产生的订单
    if (!remoteParams) remoteParams.orderBasicData = new OrderBasicDataRequest()
    // remoteParams.orderBasicData.orderType = 1
    const response = await MsTradeQuery._commonQuery<OrderStatisticResponse>(statisticOrderInServicer, {
      request: remoteParams
    })
    // const response = await MsTradeQuery.statisticOrderInServicer(remoteParams)

    return response.data
  }
  /**
   * 根据订单号查询买家id
   */
  async queryBuyerIdByOrderNo(orderNo: string): Promise<string | undefined> {
    if (!orderNo) return undefined
    const response = await MsTradeQuery.getOrderInServicer(orderNo)
    if (response.status?.isSuccess()) {
      return response.data?.buyer?.userId || undefined
    }
    return undefined
  }

  /**
   * 导出订单列表数据
   */
  async exportOrder(exportParams: QueryOrderListVo): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await exportParams.to(false)
    const response = await DataExportBackstage.exportOrderExcelInServicer({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
  /**
   * 导出订单列表数据（分销）
   */
  async exportFxOrder(exportParams: QueryOrderListVo): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await exportParams.to(false)
    const response = await DataExportBackstage.exportOrderExcelInDistributor({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }

  /**
   * 填充买家相关信息
   */
  private async fillBuyerInfo(arr: OrderDetailVo[]): Promise<OrderDetailVo[]> {
    const userIds =
      arr?.map((item) => {
        return item.buyerInfo.userId
      }) || ([] as string[])
    if (!userIds.length) return arr
    const queryRemote: QueryStudentList = UserModule.queryUserFactory.queryStudentList
    const response = await queryRemote.queryStudentListInSubject(userIds)
    const result: Map<string, BuyerInfoVo> = new Map<string, BuyerInfoVo>()
    if (response.status?.isSuccess()) {
      response.data?.map((item: StudentUserInfoVo) => {
        const info: BuyerInfoVo = new BuyerInfoVo()
        info.userId = item.userId
        info.userName = item.userName
        info.userIdCard = item.idCard
        info.userPhoneNumber = item.phone
        result.set(item.userId, info)
      })
    }
    arr.forEach((item: OrderDetailVo) => {
      item.buyerInfo = result.get(item.buyerInfo.userId) || new BuyerInfoVo()
    })
    const res = await this.fillReapplyInvoiceInfo(arr)
    return res
  }

  /**
   * （联合网校补要发票配置）填充是否支持补要发票信息
   */
  private async fillReapplyInvoiceInfo(arr: OrderDetailVo[]): Promise<OrderDetailVo[]> {
    const queryRemote = new QueryTradeConfig()
    const response: boolean = await queryRemote.hasOpenInvoice()
    arr?.forEach((item: OrderDetailVo) => {
      item.enableReApplyInvoice = Boolean(item.enableReApplyInvoice && response)
    })
    return arr
  }

  /**
   * 数组是否有质量（是否不为空）
   */
  private isWeightyArr<T>(arr: T[]): boolean {
    return Array.isArray(arr) && arr.length ? true : false
  }

  /**
   * 校验用户是否存在
   */
  private async validateExistUser(queryParams: QueryOrderListVo): Promise<boolean> {
    const queryRemote: QueryStudentList = UserModule.queryUserFactory.queryStudentList
    queryRemote.queryStudentIdParams.idCard = queryParams.idCard || undefined
    queryRemote.queryStudentIdParams.userName = queryParams.userName || undefined
    queryRemote.queryStudentIdParams.loginAccount = queryParams.loginAccount || undefined
    const result = await queryRemote.queryStudentIdList()
    return Array.isArray(result.data) && result.data.length ? true : false
  }
}

export default QueryOrder
