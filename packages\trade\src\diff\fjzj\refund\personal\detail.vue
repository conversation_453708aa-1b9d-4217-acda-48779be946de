<route-params content="/:id"></route-params>
<route-meta>
{
"isMenu":true,
"hideMenu": true,
"onlyShowOnTab":true,
"title": "个人订单退款详情"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('refundDetail')" query desc="退款详情详情" actions="created">
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/training/trade/refund/personal')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/trade/refund/personal' }">个人报名退款</el-breadcrumb-item>
      <el-breadcrumb-item>详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>退款单号：{{ returnOrderDetail.returnOrderNo }}</p>
          <p class="state" :class="returnOrderStatusClassName">
            {{ OrderRefundStatus.map.get(returnOrderDetail.returnOrderStatus) }}
          </p>

          <div class="op f-mt15" v-if="returnOrderDetail.returnOrderStatus === OrderRefundStatusEnum.pendingAudit">
            <template
              v-if="$hasPermission('approve') && !isZtlogin"
              mutation
              desc="退款审批"
              actions="cancelRefund,refuseRefund,agreeRefund"
            >
              <el-button type="warning" @click="openCancelRefund" size="mini" plain>取消退货/款</el-button>
              <el-button type="warning" @click="openRefuseRefund" size="mini" plain>拒绝退货/款</el-button>
              <el-button type="warning" size="mini" v-if="isWft()" @click="wftDialog" plain>同意退货/款</el-button>
              <el-button type="warning" size="mini" v-else @click="agreeRefund" plain>同意退货/款</el-button></template
            >
          </div>
          <div class="op f-mt15" v-if="returnOrderDetail.returnOrderStatus === OrderRefundStatusEnum.refundFailed">
            <template
              v-if="$hasPermission('retryRecycleRefund')"
              mutation
              desc="重新回收资源"
              actions="retryRecycleRefund"
            >
              <el-button type="warning" size="mini" v-if="!isZtlogin" @click="retryRecycleRefund" plain
                >重新退货</el-button
              >
            </template>
          </div>
          <div class="op f-mt15" v-if="returnOrderDetail.returnOrderStatus === OrderRefundStatusEnum.refundFailed">
            <template v-if="$hasPermission('continueRefund')" mutation desc="继续退款" actions="continueRefund">
              <el-button type="warning" size="mini" v-if="!isZtlogin" @click="continueRefund" plain>继续退款</el-button>
            </template>
          </div>
          <div
            class="op f-mt15"
            v-if="returnOrderDetail.returnOrderStatus === OrderRefundStatusEnum.pendingConfirmRefund"
          >
            <template v-if="$hasPermission('confirmRefund')" mutation desc="确认退款" actions="confirmRefund">
              <el-button type="warning" size="mini" v-if="!isZtlogin" @click="confirmRefund" plain
                >确认退款</el-button
              ></template
            >
          </div>
        </div>
        <el-steps :active="returnOrderDetail.refundProcessActivePoint + 1" align-center class="process">
          <el-step
            v-for="(item, index) in returnOrderDetail.refundProcessList"
            :key="index"
            :title="RefundProcessTypeStatus.map.get(item.type)"
            :description="item.description"
            :icon="refundProcessIconClass(item)"
          ></el-step>
        </el-steps>
      </el-card>
      <!--订单信息-->
      <el-card shadow="never" class="m-card is-header m-order-info f-mb15">
        <div class="f-flex-sub f-plr20 f-pt10">
          <div class="m-tit">
            <span class="tit-txt">退款信息</span>
          </div>
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="12">
              <el-form-item label="退款类型：">
                {{ OrderRefundType.map.get(returnOrderDetail.refundType) }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款金额：">￥{{ returnOrderDetail.refundInfo.refundPrice || 0 }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款方式：">{{
                returnOrderDetail.refundInfo.refundWay === RefundWayEnum.online ? '线上退款' : '线下退款'
              }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款原因：">{{ returnOrderDetail.refundInfo.refundReason || '-' }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="退款说明：">{{ returnOrderDetail.refundInfo.refundInfo || '-' }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="物品内容：">
                <template v-if="isHywSpecialScheme">
                  <div class="m-refund-good f-mb10" v-if="returnOrderDetail.ext == 2 || returnOrderDetail.ext == 3">
                    <div class="f-flex-sub">
                      <div class="tit f-to"><i class="el-icon-s-goods"></i>2025公需课</div>
                      <div class="tag">
                        <el-tag type="info" size="small">选课规则</el-tag>
                        <el-tag type="info" size="small">2025年</el-tag>
                        <el-tag type="info" size="small">人社行业</el-tag>
                        <el-tag type="info" size="small">福建省</el-tag>
                        <el-tag type="info" size="small">公需科目</el-tag>
                        <el-tag type="info" size="small">30学时</el-tag>
                      </div>
                    </div>
                  </div>
                  <div class="m-refund-good" v-if="returnOrderDetail.ext == 1 || returnOrderDetail.ext == 3">
                    <div class="f-flex-sub">
                      <div class="tit f-to"><i class="el-icon-s-goods"></i>华医网2025年专业课</div>
                      <div class="tag">
                        <el-tag type="info" size="small">合作办学</el-tag>
                        <el-tag type="info" size="small">2025年</el-tag>
                        <el-tag type="info" size="small">人社行业</el-tag>
                        <el-tag type="info" size="small">专业科目</el-tag>
                        <el-tag type="info" size="small">60学时</el-tag>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div
                    class="m-refund-good f-mb10"
                    v-for="(item, index) in returnOrderDetail.refundInfo.refundCommodityList"
                    :key="index"
                  >
                    <div class="f-flex-sub">
                      <div class="tit f-to"><i class="el-icon-s-goods"></i>{{ item.schemeName }}</div>
                      <div class="tag">
                        <el-tag type="info" size="small" v-if="SchemeType.map.get(item.schemeType)">{{
                          SchemeType.map.get(item.schemeType)
                        }}</el-tag>

                        <el-tag
                          v-for="(it, idx) in getChangeClassPrototype(item.sku)"
                          :key="idx"
                          type="info"
                          size="small"
                        >
                          <template>{{ it }}</template>
                        </el-tag>

                        <el-tag type="info" size="small" v-if="returnOrderDetail.operationCommodity.period"
                          >{{ returnOrderDetail.operationCommodity.period }}学时</el-tag
                        >
                      </div>
                    </div>
                  </div>
                </template>
              </el-form-item>
            </el-col>
            <!-- 退款处理中 👇-->
            <el-col :span="24" class="f-ml10" v-if="isReturnOrderStatusValid">
              <el-alert type="warning" show-icon :closable="false" class="m-alert f-ml50">
                <p>收款帐号是微信支付/支付宝，且退款方式为“线上退款”，确认退款之后，无需输入密码，即触发款项退款。</p>
              </el-alert>
            </el-col>
          </el-form>
        </div>
        <div class="right f-plr20 f-ptb10">
          <div class="m-tit">
            <span class="tit-txt">退款单号</span>
          </div>
          <el-form label-width="auto" class="m-text-form is-column f-pt10 f-pl20">
            <el-form-item label="退款单号：">{{ returnOrderDetail.returnOrderNo || '-' }}</el-form-item>
          </el-form>
          <div class="m-tit">
            <span class="tit-txt">退款操作记录</span>
          </div>
          <div class="f-pl20" v-for="(item, index) in returnOrderDetail.refundRecords" :key="index">
            <p>
              <span class="f-fb">{{ item.name || '' }}</span>
              <span v-if="item.name">在</span>
              <span class="f-fb">{{ item.time || '-' }}</span>
              {{
                item.UIReturnOrderStatue === 0
                  ? '发起退款申请'
                  : item.UIReturnOrderStatue === 1
                  ? '同意退款申请'
                  : item.UIReturnOrderStatue === 2
                  ? '拒绝退款申请'
                  : item.UIReturnOrderStatue === 3
                  ? '发起“取消退款”操作'
                  : '退款成功'
              }}
            </p>
            <el-form label-width="auto" class="m-text-form is-column f-pt10 f-pb20">
              <el-form-item label="退款金额：" v-if="item.UIReturnOrderStatue === 0">¥ {{ item.money }}</el-form-item>
              <el-form-item label="退款状态：" v-if="item.UIReturnOrderStatue === 0"
                >{{ RefundOrderStatusMapType.map.get(item.UIReturnOrderStatue) || '-' }}
              </el-form-item>
              <el-form-item label="退款说明：" v-if="item.UIReturnOrderStatue === 0">{{
                item.tipMsg || '-'
              }}</el-form-item>
              <el-form-item label="拒绝原因：" v-if="item.UIReturnOrderStatue === 2">{{
                item.tipMsg || '-'
              }}</el-form-item>
              <el-form-item label="取消原因：" v-if="item.UIReturnOrderStatue === 3">{{
                item.tipMsg || '-'
              }}</el-form-item>
            </el-form>
          </div>
        </div>
      </el-card>

      <!--购买清单-->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="f-plr20 f-pt10">
          <div class="m-tit is-small">
            <span class="tit-txt">商品信息</span>
          </div>
          <el-form label-width="auto" class="m-text-form f-pt10 f-pb5">
            <el-col :span="8">
              <el-form-item label="购买人：">{{ returnOrderDetail.buyer.userName || '-' }}</el-form-item>
            </el-col>
            <el-col :span="8" v-if="queryShowLoginAccount.isShowLoginAccount">
              <el-form-item label="登录账号：">{{ returnOrderDetail.buyer.loginAccount || '-' }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="证件号：">{{ returnOrderDetail.buyer.idCard || '-' }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="手机号：">{{ returnOrderDetail.buyer.phone || '-' }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单号：">
                <span @click="toOrder(returnOrderDetail.orderNo)" class="f-cb f-link f-underline">{{
                  returnOrderDetail.orderNo || '-'
                }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="交易流水号：">{{ returnOrderDetail.flowNo || '-' }}</el-form-item>
            </el-col>
            <el-col :span="8" v-if="returnOrderDetail.commodityAuthInfo">
              <el-form-item label="分销商：">{{ getDistribution('distributorName') }}</el-form-item>
            </el-col>
            <el-col :span="8" v-if="subDistributionId">
              <el-form-item label="上级分销商：">{{ getDistribution('superiorDistributorName') }}</el-form-item>
            </el-col>
          </el-form>
          <el-table :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="物品名称" min-width="300">
              <template>
                <!-- el-tag 退款成功👇 -->
                <div
                  v-if="
                    !returnOrderDetail.operationCommodity.exchanged &&
                    !returnOrderDetail.operationCommodity.isExchangeIssue
                  "
                >
                  <p>
                    {{ returnOrderDetail.operationCommodity.commodityName || '-'
                    }}<el-tag
                      type="success"
                      size="small"
                      class="f-ml10"
                      v-if="returnOrderDetail.operationCommodity.saleChannel == 2"
                      >专题</el-tag
                    >
                  </p>
                  <p v-if="returnOrderDetail.operationCommodity.saleChannelName">
                    专题名称：{{ returnOrderDetail.operationCommodity.saleChannelName }}
                  </p>

                  <p v-if="returnOrderDetail.operationCommodity.trainingPeriodName">
                    培训期别：{{ returnOrderDetail.operationCommodity.trainingPeriodName || '-' }}
                  </p>
                </div>
                <div v-else>
                  <p>
                    <el-tag type="danger" size="mini">初始物品</el-tag
                    >{{ returnOrderDetail.operationCommodity.originCommodityName || '-' }}
                  </p>
                  <p>培训期别：{{ returnOrderDetail.operationCommodity.originTrainingPeriodName || '-' }}</p>
                  <p>
                    <el-tag type="danger" size="mini">最新物品</el-tag
                    >{{ returnOrderDetail.operationCommodity.commodityName || '-' }}
                  </p>

                  <p>培训期别：{{ returnOrderDetail.operationCommodity.trainingPeriodName || '-' }}</p>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="学时" min-width="150" align="center">
              <template>{{ returnOrderDetail.operationCommodity.period || '-' }}</template>
            </el-table-column>
            <el-table-column label="数量" min-width="150" align="center">
              <template>{{ returnOrderDetail.operationCommodity.quantity }}</template>
            </el-table-column>
            <el-table-column label="单价(元)" min-width="150" align="right">
              <template>{{ returnOrderDetail.operationCommodity.originalPrice }}</template>
            </el-table-column>
            <el-table-column
              label="分销单价(元)"
              min-width="150"
              align="right"
              v-if="returnOrderDetail.operationCommodity.commodityAuthInfo"
            >
              <template>{{
                returnOrderDetail.operationCommodity.refundQuantity * returnOrderDetail.operationCommodity.price
              }}</template>
            </el-table-column>
            <el-table-column label="实付金额(元)" min-width="150" align="right">
              <template>
                {{ returnOrderDetail.operationCommodity.payAmount }}
                <el-tag type="warning" size="mini" class="f-ml5" v-if="returnOrderDetail.operationCommodity.useDiscount"
                  >优惠申请
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <el-drawer title="取消退款申请" :visible.sync="cancelRefundDialog" size="800px" custom-class="m-drawer">
        <div class="drawer-bd">
          <el-alert type="warning" show-icon :closable="false" class="m-alert">
            确认取消该订单的退款申请？取消后需要重新发起退款！
          </el-alert>
          <el-row type="flex" justify="center">
            <el-col :span="18">
              <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                <el-form-item label="取消原因：" required>
                  <el-input type="textarea" :rows="6" v-model="cancelReason" placeholder="请输入取消原因" />
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button @click="cancelRefundDialog = false">取消</el-button>
                  <el-button type="primary" @click="cancelRefund">确定</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-drawer>
      <el-drawer title="拒绝退款申请" :visible.sync="refuseRefundDialog" size="800px" custom-class="m-drawer">
        <div class="drawer-bd">
          <el-row type="flex" justify="center">
            <el-col :span="18">
              <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                <el-form-item label="拒绝退款原因：" required>
                  <el-input type="textarea" :rows="6" v-model="cancelReason" placeholder="请输入拒绝退款原因" />
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button @click="refuseRefundDialog = false">取消</el-button>
                  <el-button type="primary" @click="refuseRefund">确定</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-drawer>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { bind, debounce } from 'lodash-decorators'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import QueryRefundList from '@api/service/management/trade/single/order/query/QueryRefundDetail'
  // import ReturnOrderResponseVo, {
  //   SchemeResourceResponse
  // } from '@api/service/management/trade/single/order/query/vo/ReturnOrderResponseVo'
  import ReturnOrderResponseVo, {
    SchemeResourceResponse
  } from '@api/service/diff/management/fjzj/trade/order/model/ReturnOrderResponseVo'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplicationDiff } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  // import MutationReturnOrder from '@api/service/management/trade/single/order/mutation/MutationReturnOrder'
  import MutationReturnOrder from '@api/service/diff/management/fjzj/trade/single/order/mutation/MutationReturnOrder'
  import ReturnOrderDetail from '@api/service/diff/management/fjzj/trade/single/order/ReturnOrderDetail'
  import OrderRefundType from '@api/service/common/return-order/enums/OrderRefundType'
  import OrderRefundStatus from '@api/service/common/return-order/enums/OrderRefundStatus'
  import { RefundProcessTypeEnum } from '@api/service/management/trade/single/order/enum/RefundProcessTypeEnum'
  import RefundProcess from '@api/service/management/trade/single/order/models/RefundProcess'
  import RefundProcessTypeStatus from '@api/service/management/trade/single/order/enum/RefundProcessTypeEnum'
  import { RefundWayEnum } from '@api/service/management/trade/single/order/enum/RefundWayEnum'
  import { OrderRefundStatusEnum } from '@api/service/common/return-order/enums/OrderRefundStatus'
  import RefundOrderStatusMapType from '@api/service/common/trade/RefundOrderStatusEnum'
  import RefundCommodity from '@api/service/management/trade/single/order/models/RefundCommodity'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
  @Component
  export default class extends Vue {
    form = {} //form表单

    cancelRefundDialog = false //取消退款弹窗标识

    refuseRefundDialog = false //拒绝退款弹窗标识

    cancelReason = '' //拒绝退款原因

    refundList: ReturnOrderResponseVo = new ReturnOrderResponseVo() //退款单详情对象

    tableData: Array<RefundCommodity> = new Array<RefundCommodity>() //退货信息

    mutationReturnOrder = new MutationReturnOrder() //退款业务接口请求

    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable // 是否开启过分销增值能力服务

    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs) // 判断当前登录角色是否是分销管理员

    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly) // 判断当前登录角色是否是专题管理员

    queryShowLoginAccount = QueryShowLoginAccount // 查询阿波罗是否显示登录账号

    OrderRefundType = OrderRefundType // 退款类型

    RefundWayEnum = RefundWayEnum // 退款方式

    OrderRefundStatusEnum = OrderRefundStatusEnum //退货状态

    OrderRefundStatus = OrderRefundStatus //退货状态

    RefundProcessTypeStatus = RefundProcessTypeStatus //退款流程状态枚举

    RefundOrderStatusMapType = RefundOrderStatusMapType //退款记录状态枚举

    returnOrderDetail = new ReturnOrderDetail() //退款新模型

    SchemeType = SchemeType //选课规则

    /**
     * 获取华医网特殊方案
     */
    get hywSchemeList(): string[] {
      const list: { fjzjSchemeId: string }[] = JSON.parse(
        ConfigCenterModule.getFrontendApplicationDiff(frontendApplicationDiff.hywSchemeId)
      )
      return list.map((item) => item.fjzjSchemeId)
    }

    /**
     * 是否有上级分销商
     */
    get subDistributionId() {
      return this.returnOrderDetail.commodityAuthInfo && this.returnOrderDetail.commodityAuthInfo.superiorDistributorId
    }

    /**
     * 是否展示退款处理中的提示语
     */
    get isReturnOrderStatusValid() {
      const { returnOrderStatus } = this.returnOrderDetail
      return [4, 5, 6].includes(returnOrderStatus)
    }
    /**
     * 退货状态样式
     */
    get returnOrderStatusClassName() {
      const { returnOrderStatus } = this.returnOrderDetail
      if ([1, 4, 5, 8, 9].includes(returnOrderStatus)) {
        return 'f-co'
      } else if (returnOrderStatus === 2) {
        return 'f-c9'
      } else if ([3, 7].includes(returnOrderStatus)) {
        return 'f-cr'
      } else if ([6, 10].includes(returnOrderStatus)) {
        return 'f-cb'
      } else {
        return 'f-cg'
      }
    }
    /**
     * 退货状态步骤样式
     */
    get refundProcessIconClass() {
      const iconMap = {
        [RefundProcessTypeEnum.submitApplication]: 'hb-iconfont icon-mytraining',
        [RefundProcessTypeEnum.approveApplication]: 'hb-iconfont icon-auditrecord',
        [RefundProcessTypeEnum.handleReturn]: 'hb-iconfont icon-refundmanage',
        [RefundProcessTypeEnum.refundConfirmation]: 'hb-iconfont icon-invoicemanage',
        [RefundProcessTypeEnum.processRefund]: 'hb-iconfont icon-payment',
        [RefundProcessTypeEnum.completeProcess]: 'hb-iconfont icon-success'
      }

      return (item: RefundProcess) => {
        return iconMap[item.type] || ''
      }
    }

    /**
     * 是否是华医网特殊方案
     */
    get isHywSpecialScheme() {
      const schemeId = this.returnOrderDetail?.operationCommodity?.schemeId
      const schemeIds = this.hywSchemeList
      return schemeIds.includes(schemeId)
    }

    //==========================退款相关请求========================

    async activated() {
      this.returnOrderDetail.returnOrderNo = this.$route.params.id
      await this.geteRfundOrderDetail()
    }
    /*
     * 跳转订单详情页
     * */
    toOrder(id: string) {
      this.$router.push('/training/trade/order/personal/detail/' + id)
    }
    /*
     * 取消退款弹窗
     * */
    openCancelRefund() {
      this.cancelReason = ''
      this.cancelRefundDialog = true
    }
    /*
     * 拒绝退款弹窗
     * */
    openRefuseRefund() {
      this.cancelReason = ''
      this.refuseRefundDialog = true
    }

    /*
     * 退款详情
     * */
    async geteRfundOrderDetail() {
      try {
        if (this.isFxlogin && this.isHadFxAbility) {
          await this.returnOrderDetail.queryDetailByFx()
        } else {
          await this.returnOrderDetail.queryDetail()
        }
      } catch (e) {
        console.log(e, '加载个人报名退款订单详情页失败')
      } finally {
        // this.refundList = this.returnOrderDetail.returnOrderDetail
        this.tableData = [this.returnOrderDetail.operationCommodity]
      }
    }
    /**
     * 判断是否是威富通以及线下
     */
    isWft() {
      if (
        this.returnOrderDetail.flowNo.includes('WFT') &&
        this.returnOrderDetail.refundInfo.refundWay == RefundWayEnum.online
      ) {
        return true
      } else {
        return false
      }
    }

    /**
     * 威富通退款点击事件
     * @param item
     */
    wftDialog() {
      this.$alert('兴业聚合支付（威富通）线上退款到账需要1~3个工作日，确认退款吗?', '提示', {
        confirmButtonText: '确认',
        type: 'warning'
      }).then(() => {
        this.agreeRefund()
      })
    }
    // 获取分销商
    getDistribution(item: string) {
      if (item == 'distributorName') {
        return this.returnOrderDetail.commodityAuthInfo ? this.returnOrderDetail.commodityAuthInfo.distributorName : '-'
      } else {
        return this.returnOrderDetail.commodityAuthInfo
          ? this.returnOrderDetail.commodityAuthInfo.superiorDistributorName
          : '-'
      }
    }
    //换班记录获取属性
    getChangeClassPrototype(val: SkuPropertyResponseVo) {
      const arrList = new Array<any>()
      if (!val) return
      if (val.industry && val.industry.skuPropertyName) arrList.push(val.industry.skuPropertyName)
      if (val.year && val.year.skuPropertyName) arrList.push(val.year.skuPropertyName + '年')
      if (val.subjectType && val.subjectType.skuPropertyName) arrList.push(val.subjectType.skuPropertyName)
      if (val.trainingMajor && val.trainingMajor.skuPropertyName) arrList.push(val.trainingMajor.skuPropertyName)
      if (val.trainingCategory && val.trainingCategory.skuPropertyName)
        arrList.push(val.trainingCategory.skuPropertyName)
      if (val.trainingObject && val.trainingObject.skuPropertyName) arrList.push(val.trainingObject.skuPropertyName)
      if (val.positionCategory && val.positionCategory.skuPropertyName)
        arrList.push(val.positionCategory.skuPropertyName)
      if (val.jobLevel && val.jobLevel.skuPropertyName) arrList.push(val.jobLevel.skuPropertyName)
      if (val.learningPhase && val.learningPhase.skuPropertyName) arrList.push(val.learningPhase.skuPropertyName)
      if (val.discipline && val.discipline.skuPropertyName) arrList.push(val.discipline.skuPropertyName)
      if (val.region && val.region.skuPropertyName) arrList.push(val.region.skuPropertyName)
      if (!arrList.length) return
      return arrList
    }
    //==========================退款相关业务========================
    /*
     * 取消退款
     * */
    @bind
    @debounce(200)
    async cancelRefund() {
      if (!this.cancelReason) {
        this.$message.error('请填写取消退款原因')
        return
      }
      this.mutationReturnOrder.returnOrderNo = this.returnOrderDetail.returnOrderNo
      this.mutationReturnOrder.note = this.cancelReason
      this.mutationReturnOrder.orderNo = this.returnOrderDetail.orderNo
      let status
      // 如果是差异化方案，调用差异化方法
      if (this.isHywSpecialScheme) {
        status = await this.mutationReturnOrder.cancelReturnApplyDiff()
      } else {
        status = await this.mutationReturnOrder.cancelReturnApply()
      }
      if (!status.isSuccess()) {
        this.$message.error('取消退款失败')
        return
      }
      this.cancelRefundDialog = false
      this.$message.success('取消退款成功')
      await this.geteRfundOrderDetail()
    }
    /*
     * 拒绝退款
     * */
    async refuseRefund() {
      if (!this.cancelReason) {
        this.$message.error('请填写拒绝退款原因')
        return
      }
      this.mutationReturnOrder.returnOrderNo = this.returnOrderDetail.returnOrderNo
      this.mutationReturnOrder.note = this.cancelReason
      this.mutationReturnOrder.orderNo = this.returnOrderDetail.orderNo
      let status
      // 如果是差异化方案，调用差异化方法
      if (this.isHywSpecialScheme) {
        status = await this.mutationReturnOrder.rejectReturnApplyDiff()
      } else {
        status = await this.mutationReturnOrder.rejectReturnApply()
      }
      if (!status.isSuccess()) {
        this.$message.error('拒绝退款失败')
        return
      }
      this.refuseRefundDialog = false
      this.$message.success('拒绝退款成功')
      await this.geteRfundOrderDetail()
    }
    /*
     * 同意退款
     * */
    async agreeRefund() {
      // try {
      this.mutationReturnOrder.returnOrderNo = this.returnOrderDetail.returnOrderNo
      this.mutationReturnOrder.note = this.cancelReason
      this.mutationReturnOrder.orderNo = this.returnOrderDetail.orderNo
      let status
      // 如果是差异化方案，调用差异化方法
      if (this.isHywSpecialScheme) {
        status = await this.mutationReturnOrder.agreeReturnApplyDiff()
      } else {
        status = await this.mutationReturnOrder.agreeReturnApply()
      }
      if (!status.isSuccess()) {
        this.$message.error('同意退款失败')
        return
      }
      // } catch (e) {
      //   console.log(e, '同意退款失败')
      // } finally {
      // 延迟调用
      setTimeout(async () => {
        this.$message.success('同意退款成功')
        await this.geteRfundOrderDetail()
      }, 500)
      // }
    }
    /*
     * 确认退款
     * */
    async confirmRefund() {
      // try {
      this.mutationReturnOrder.returnOrderNo = this.returnOrderDetail.returnOrderNo
      const res = await this.mutationReturnOrder.confirmRefund()
      // if (res.code === 300) {
      //   this.$message.error('系统已完成退款')
      // }
      // } catch (e) {
      //   console.log(e, '确认退款失败')
      // } finally {
      if (!res.isSuccess()) {
        this.$message.error('确认退款失败')
        return
      }
      // 延迟调用
      setTimeout(async () => {
        this.$message.success('发起确认退款中')
        await this.geteRfundOrderDetail()
      }, 500)
      // }
    }
    /*
     * 继续退款
     * */
    async continueRefund() {
      // try {
      this.mutationReturnOrder.returnOrderNo = this.returnOrderDetail.returnOrderNo
      const status = await this.mutationReturnOrder.retryRefund()
      // } catch (e) {
      //   console.log(e, '继续退款失败')
      // } finally {
      if (!status.isSuccess()) {
        this.$message.error('继续退款失败')
        return
      }
      this.$message.success('继续退款成功')
      await this.geteRfundOrderDetail()
      // }
    }
    /*
     *  重新回收做资源
     * */
    async retryRecycleRefund() {
      // try {
      this.mutationReturnOrder.returnOrderNo = this.returnOrderDetail.returnOrderNo
      const status = await this.mutationReturnOrder.retryRecycleResource()
      // } catch (e) {
      //   console.log(e, '重新回收做资源失败')
      // } finally {
      if (!status.isSuccess()) {
        this.$message.error('重新回收做资源失败')
        return
      }
      this.$message.success('系统已重新发起回收资源！')
      await this.geteRfundOrderDetail()
      // }
    }
  }
</script>

<style scoped></style>
