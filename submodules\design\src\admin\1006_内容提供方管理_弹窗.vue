<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--新建内容提供方管理-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20">新建内容提供方管理</el-button>
        <el-drawer
          title="新建内容提供方管理"
          :visible.sync="dialog3"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="内容提供方名称：" required>
                    <el-input
                      v-model="form.name"
                      clearable
                      maxlength="30"
                      show-word-limit
                      placeholder="请输入内容提供方名称，不超过30字"
                    />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">保存</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <el-button @click="dialog4 = true" type="primary" class="f-mr20">编辑内容提供方管理</el-button>
        <el-drawer
          title="编辑内容提供方管理"
          :visible.sync="dialog4"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="内容提供方名称：" required>
                    <el-input
                      v-model="form.name"
                      clearable
                      maxlength="30"
                      show-word-limit
                      placeholder="这是内容提供方"
                    />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--新建/修改课件分类-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">编辑课件分类</el-button>
        <el-drawer
          title="编辑课件分类"
          :visible.sync="dialog1"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="所属节点：" required>
                    <el-select v-model="form.region" clearable placeholder="请选择所属节点" class="form-m">
                      <el-option label="帮助中心" value="shanghai"></el-option>
                      <el-option label="消息" value="beijing"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="分类名称：" required>
                    <el-input
                      v-model="form.name"
                      clearable
                      maxlength="30"
                      show-word-limit
                      placeholder="请输入分类名称，不超过30个字"
                    />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--课件分类详情-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">课件分类详情</el-button>
        <el-drawer
          title="课件分类详情"
          :visible.sync="dialog2"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-text-form is-column f-mt20">
                  <el-form-item label="所属节点：">所属节点名称</el-form-item>
                  <el-form-item label="分类名称：">分类名称分类名称分类名称分类名称</el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <el-button type="primary" @click="dialog5 = true" class="f-mr20">启用</el-button>
        <el-dialog :visible.sync="dialog5" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-warning warning"></i>
            <div class="txt">
              <p class="f-f20">确定要启用该内容提供方？</p>
            </div>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>
        <el-button type="primary" @click="dialog6 = true" class="f-mr20">停止</el-button>
        <el-dialog :visible.sync="dialog6" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-warning warning"></i>
            <div class="txt">
              <p class="f-f20">确定要停用内容提供方？停用后学员选课页面无法看到该内容提供方。</p>
            </div>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
