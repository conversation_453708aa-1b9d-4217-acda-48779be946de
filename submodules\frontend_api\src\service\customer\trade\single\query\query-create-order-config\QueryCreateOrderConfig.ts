import msOfflineInvoice from '@api/ms-gateway/ms-offlineinvoice-v1'
import msOrder, { Commodity, InvoiceConfigResponse } from '@api/ms-gateway/ms-order-v1'
import TradeModule from '@api/service/customer/trade/TradeModule'
import { PurchaseChannelTypeEnum } from '@api/service/customer/trade/single/enum/PurchaseChannelTypeEnum'
import { ShipTypeEnum } from '@api/service/customer/trade/single/enum/ShipTypeEnum'
import TrainClassModule from '@api/service/customer/train-class/TrainClassModule'
import TrainClassDetailClassVo from '@api/service/customer/train-class/query/vo/TrainClassDetailClassVo'
import { Description, upMyLog } from '@hbfe/jxjy-customer-common/src/monitor/WebfunnyUpMyLog'
import { ResponseStatus } from '@hbfe/common'
import { cloneDeep } from 'lodash'
import QueryTrainClassDetail from '@api/service/customer/train-class/query/QueryTrainClassDetail'
import CreateOrderCommodity from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/CreateOrderCommodity'
import IssueDetail from '@api/service/customer/train-class/offlinePart/model/IssueDetail'
import QueryTrainClassIssue from '@api/service/customer/train-class/offlinePart/QueryTrainClassIssue'
import IssueListInDistributor from '@hbfe/fx-api/dist/service/customer/distribution-issue/IssueListInDistributor'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

// 加载创建订单/支付订单需要加载的配置 如培训券 发票 商品信息等
export default class QueryCreateOrderConfig {
  // 方案Id
  private commoditySkuId = ''
  private commoditySkuIds: string[] = []

  /**
   * 是否分销环境
   */
  private isFx = false
  //   门户商品id
  private portalCommoditySkuId = ''
  //渠道
  private purchaseChannelType = PurchaseChannelTypeEnum.PurchaseChannelTypeEnumUserBuy
  // 查询期别列表实例
  private queryTrainClassIssue = new QueryTrainClassIssue()
  // 培训班详情
  commodityDetail = new TrainClassDetailClassVo()
  // 培训班详情（多个商品）
  commodityDetailList = new Array<TrainClassDetailClassVo>()
  //发票配置详情
  invoiceConfig = new InvoiceConfigResponse()
  //配送列表
  shipList: ShipTypeEnum[] = []
  // order业务对象实例
  // private _orderInstance: CustomerOrderAction = undefined
  /**
   * 班级可选期别Map <商品，期别列表>
   */
  commodityIssueListMap = new Map<string, Array<IssueDetail>>()
  constructor(
    commoditySkuId?: string,
    commoditySkuIds?: string[],
    purchaseChannelType = PurchaseChannelTypeEnum.PurchaseChannelTypeEnumUserBuy,
    portalCommoditySkuId?: string,
    isFx?: boolean
  ) {
    this.commoditySkuId = commoditySkuId
    this.commoditySkuIds = commoditySkuIds
    this.portalCommoditySkuId = portalCommoditySkuId
    this.purchaseChannelType = purchaseChannelType
    this.isFx = isFx
  }

  /**
   * @description: 拿到创建订单对象
   */
  getCreateOrderAction() {
    return TradeModule.MutationTradeFactory.getCreateOrderAction()
  }
  /**
   * @description: 拿到创建订单对象
   */
  getCreateOrder() {
    const createOrder = this.getCreateOrderAction()
    if (this.commoditySkuIds?.length > 1) {
      let commodityList = new Array<CreateOrderCommodity>()
      commodityList = this.commoditySkuIds.map((item) => {
        const commodity = new CreateOrderCommodity()
        commodity.skuId = item
        commodity.quantity = 1
        return commodity
      })
      createOrder.createOrderParams.commodities = commodityList
      createOrder.createOrderParams.purchaseChannelType = 1
    } else {
      const commodity = new CreateOrderCommodity()
      commodity.skuId = this.commoditySkuId
      commodity.quantity = 1
      createOrder.createOrderParams.commodities = [commodity]
      createOrder.createOrderParams.purchaseChannelType = 1
    }
    // createOrder.createOrderParams.needInvoice = false
    return createOrder
  }

  /**
   * @description: 初始化下单页面的配置。请先调用该方法。
   * @param {*}
   * @return {*}
   */

  async queryCreateOrderConfigInfo(trainingChannelId?: string) {
    console.log('lzh 加载信息')

    // 加载商品信息
    let res = await this.queryCommodity(trainingChannelId)
    // if (res.isSuccess()) {
    res = await this.queryInvoice(this.purchaseChannelType)
    res = await this.queryShippingMethodsForSchool()

    return res
    // 转换商品模型
    // const commodityDetail = ParseUtil.parseCommodity(this.commodityDetail)

    // res = await this.queryUnitOrganizationId(commodityDetail)
    // if (!res.isSuccess()) return res
    // const customerOrderAction = MutationTradeFacade.customerOrderActionMultiton(commodityDetail)
    // 初始化商品
    // this._orderInstance = customerOrderAction
    // 加载机构组织代码信息
    // await this.setOrderParams()
    // return new ResponseStatus(200)
    // 加载培训券 TODO: LPJ
    // ...
  }
  /*
   * 查询配送列表
   *
   * */
  private async queryShippingMethodsForSchool() {
    const res = await msOfflineInvoice.queryShippingMethodsForSchool()
    // 加载商品信息
    if (res.status.isSuccess()) {
      this.shipList = res.data.shippingMethods
    } else {
      // webfunny埋点
      const description = new Description()
      description.response = res
      description.message = '加载配送渠道失败'
      upMyLog(description, 'queryShippingMethodsForSchool')
    }
    return res.status
  }
  /**
   * @description: 加载商品信息
   * @param {*}
   * @return {*}
   */

  private async queryCommodity(trainingChannelId?: string) {
    this.commodityDetailList = []
    this.commodityIssueListMap = new Map<string, Array<IssueDetail>>()

    if (this.commoditySkuIds?.length > 1) {
      // 多个商品 不考虑专题
      await Promise.all(
        this.commoditySkuIds.map(async (item) => {
          let queryTrainClassDetail = new QueryTrainClassDetail()
          queryTrainClassDetail = TrainClassModule.queryTrainClassFactory.getQueryTrainClassDetail()
          queryTrainClassDetail.commodityId = item
          queryTrainClassDetail.portalCommoditySkuId = item
          // 加载商品信息
          const status = await queryTrainClassDetail.queryTrainClassDetail(trainingChannelId, true)
          // if (!queryTrainClassDetail.trainClassDetail.commoditySkuId) {
          //   queryTrainClassDetail.trainClassDetail.commoditySkuId = item
          // }
          // 获取期别
          if (
            [TrainingModeEnum.mixed, TrainingModeEnum.offline].includes(
              queryTrainClassDetail.trainClassDetail?.skuProperty?.trainingMode?.skuPropertyValueId
            )
          ) {
            if (this.isFx) {
              const queryFxIssue = new IssueListInDistributor()

              const issueFxListDto = await queryFxIssue.querySchemeAvailableIssueList(
                queryTrainClassDetail.commodityId,
                queryTrainClassDetail.trainClassDetail.schemeId
              )
              const issueList = issueFxListDto.map((item) => {
                return Object.assign(new IssueDetail(), item) as IssueDetail
              })
              this.commodityIssueListMap.set(item, issueList)
            } else if (trainingChannelId) {
              const issueList = await this.queryTrainClassIssue.queryChannelSchemeAvailableIssueList(item)
              this.commodityIssueListMap.set(item, issueList)
            } else {
              const issueList = await this.queryTrainClassIssue.querySchemeAvailableIssueList(item)
              this.commodityIssueListMap.set(item, issueList)
            }
          }

          if (!status.isSuccess()) {
            // webfunny埋点
            const description = new Description()
            description.params = item
            description.response = status
            description.message = '加载商品信息失败'
            upMyLog(description, 'queryTrainClassDetail')
            return status
          }
          this.commodityDetailList.push(cloneDeep(queryTrainClassDetail.trainClassDetail))
          console.log(cloneDeep(queryTrainClassDetail.trainClassDetail), '(queryTrainClassDetail.trainClassDetail) lzh')
          console.log(this.commodityDetailList, 'this.commodityDetailList lzh')
        })
      )
      if (this.commodityDetailList?.length) {
        return new ResponseStatus(200, '加载多个商品成功')
      } else {
        return new ResponseStatus(500, '加载多个商品失败')
      }
    } else {
      const queryTrainClassDetail = TrainClassModule.queryTrainClassFactory.getQueryTrainClassDetail()
      queryTrainClassDetail.commodityId = this.commoditySkuId
      queryTrainClassDetail.portalCommoditySkuId = this.portalCommoditySkuId

      // 加载商品信息
      const status = await queryTrainClassDetail.queryTrainClassDetail(trainingChannelId, true)

      // 获取期别
      if (
        [TrainingModeEnum.mixed, TrainingModeEnum.offline].includes(
          queryTrainClassDetail.trainClassDetail.skuProperty.trainingMode.skuPropertyValueId
        )
      ) {
        if (this.isFx) {
          const queryFxIssue = new IssueListInDistributor()

          const issueFxListDto = await queryFxIssue.querySchemeAvailableIssueList(
            queryTrainClassDetail.trainClassDetail.commoditySkuId,
            queryTrainClassDetail.trainClassDetail.schemeId
          )
          const issueList = issueFxListDto.map((item) => {
            return Object.assign(new IssueDetail(), item) as IssueDetail
          })
          this.commodityIssueListMap.set(this.commoditySkuId, issueList)
        } else if (trainingChannelId) {
          const issueList = await this.queryTrainClassIssue.queryChannelSchemeAvailableIssueList(this.commoditySkuId)
          this.commodityIssueListMap.set(this.commoditySkuId, issueList)
        } else {
          const issueList = await this.queryTrainClassIssue.querySchemeAvailableIssueList(this.commoditySkuId)
          this.commodityIssueListMap.set(this.commoditySkuId, issueList)
        }
      }

      if (!status.isSuccess()) {
        // webfunny埋点
        const description = new Description()
        description.params = this.commoditySkuId
        description.response = status
        description.message = '加载商品信息失败'
        upMyLog(description, 'queryTrainClassDetail')
        return status
      }
      this.commodityDetail = queryTrainClassDetail.trainClassDetail
      // 加载机构组织代码
      return status
    }
  }
  /**
   * @description: 发票信息
   * @param {*}
   * @return {*}
   */

  private async queryInvoice(purchaseChannelType: PurchaseChannelTypeEnum) {
    // 加载商品信息
    const res = await msOrder.preparePlaceOrder(purchaseChannelType)
    if (!res.status.isSuccess()) {
      // webfunny埋点
      const description = new Description()
      description.params = purchaseChannelType
      description.response = res
      description.message = '获取发票配置失败'
      upMyLog(description, 'preparePlaceOrder')
    }
    this.invoiceConfig = res.data.invoiceConfigResult
    // }

    return res.status
  }

  /**
   * @description: 加载机构组织代码
   * @param commodityDetail 商品信息
   * @return {*}
   */

  // private async queryUnitOrganizationId(commodityDetail: Commodity) {
  //   const res = await trainingInstitutionGateway.detail(this.commodityDetail.trainingInstitutionId)
  //   if (!res.status.isSuccess()) {
  //     return res.status
  //   }
  //   commodityDetail.unitCode = res.data.code
  //   return res.status
  // }
}
