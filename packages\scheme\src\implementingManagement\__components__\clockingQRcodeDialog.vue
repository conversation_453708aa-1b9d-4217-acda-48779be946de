<template>
  <el-drawer title="考勤二维码" :visible.sync="openDialog" size="480px" custom-class="m-drawer" :modal="false">
    <div class="drawer-bd">
      <div class="m-view-qrcode">
        <div class="item">
          <div class="content" :id="qrCodeModule.planId">
            <div class="tit">{{ qrCodeModule.courseName }}</div>
            <div class="cate">{{ qrCodeModule.date }}</div>
            <div class="date">
              {{ getAttendanceType(qrCodeModule.attendanceType) }}：{{ qrCodeModule.checkTime.begin || '-' }} 至
              {{ qrCodeModule.checkTime.end || '-' }}
            </div>
            <div class="code"><img :src="qrCodeModule.qrCode" class="u-qr-code" /></div>
          </div>
          <div class="op">
            <el-button type="primary" round size="medium" @click="downloadPoster(qrCodeModule.planId)"
              >保存至本地
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button type="primary" @click="openDialog = false">关闭</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Prop } from 'vue-property-decorator'
  import * as htmlToImage from 'html-to-image'
  import AttendanceQRCodeDto from '@api/service/management/implement/models/AttendanceQRCodeDto'
  import QRCode from 'qrcode'
  import CryptUtil from '@api/service/common/crypt/CryptUtil'
  import { EnterTypeEnums } from '@api/service/common/auth-status-tool/enums/EnterTypeEnums'
  import { AttendanceTypeEnum } from '@api/service/management/implement/enums/AttendanceTypeEnum'

  @Component({})
  export default class extends Vue {
    @Prop({
      type: String,
      default: ''
    })
    schemeId: string
    periodId = ''
    openDialog = false
    qrCodeModule = new AttendanceQRCodeDto()
    // 二维码枚举赋值
    EnterTypeEnums = EnterTypeEnums
    qrcode = 'https://www.baidu.com/' // url  el-image  src=qrcode
    async open(row: AttendanceQRCodeDto) {
      this.qrCodeModule = row
      this.openDialog = true
      // this.downloadPoster(row.planId)
      // 循环list 生成二维码
      this.qrCodeModule.qrCode = await this.getQrcode()
    }

    // 下载海报
    downloadPoster(id: string) {
      this.domToPic(id, {
        width: 1140,
        height: 1320,
        name: this.qrCodeName
      })
    }

    // 下载方法
    domToPic(domId: string, config = { width: 2105, height: 3035, name: this.qrCodeName }) {
      const node = document.getElementById(domId)
      htmlToImage
        .toPng(node)
        .then((pngUrl: string) => {
          const img = new Image()
          img.src = pngUrl
          img.onload = () => {
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')
            canvas.width = config.width
            canvas.height = config.height
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
            canvas.toBlob((blob: Blob) => {
              const url = window.URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.href = url
              link.download = `${config.name}.jpeg`
              document.body.appendChild(link)
              link.click()
              window.URL.revokeObjectURL(url)
            })
          }
        })
        .catch(function (error: any) {
          console.error('oops, something went wrong!', error)
        })
    }

    // 获取二维码
    get getQrcode() {
      return async () => {
        // 考勤入参 教学计划id 教学计划项id 方案id 期别id考勤类型 目标路径：/pages/training/attendance
        const origin = window.location.origin
        const accessPath =
          origin + `/h5/#/pages/transfer/qr_transfer?entryType=${EnterTypeEnums.classCheckIn}&nextAddress=`
        const storageURL = await CryptUtil.encryptStr(
          `/pages/training/attendance?planId=${this.qrCodeModule.planId}&planItemId=${this.qrCodeModule.planItemId}&planItemGroupId=${this.qrCodeModule.planItemGroupId}&&signType=${this.qrCodeModule.attendanceType}&schemeId=${this.schemeId}&periodId=${this.periodId}`
        )
        const qrcode = await QRCode.toDataURL(accessPath + storageURL)
        return qrcode
      }
    }

    // 二维码名称
    get qrCodeName() {
      let attendanceType = ''
      if (this.qrCodeModule.attendanceType == AttendanceTypeEnum.signIn) {
        attendanceType = '签到'
      } else {
        attendanceType = '签退'
      }
      return this.qrCodeModule.courseName + '(' + this.qrCodeModule.date + attendanceType + ')' || '考勤二维码'
    }

    get getAttendanceType() {
      return (item: AttendanceTypeEnum) => {
        return item == AttendanceTypeEnum.signIn ? '签到' : '签退'
      }
    }
  }
</script>

<style scoped lang="scss"></style>
