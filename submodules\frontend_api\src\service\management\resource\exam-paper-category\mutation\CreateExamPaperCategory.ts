import { ResponseStatus } from '@hbfe/common'
import ExamCategoryGateway from '@api/ms-gateway/ms-examextraction-v1'
import CreateExamPaperCategoryDto from '@api/service/management/resource/exam-paper-category/mutation/vo/CreateExamPaperCategoryVo'

/**
 * @description: 创建试卷分类
 * @param {*}
 * @return {*}
 */
class CreateExamCategory {
  createExamPaperCategoryParams: CreateExamPaperCategoryDto = new CreateExamPaperCategoryDto()

  async doCreateExamCategory(): Promise<ResponseStatus> {
    const { status } = await ExamCategoryGateway.createPaperPublishConfigureCategory(this.createExamPaperCategoryParams)
    return status
  }
}
export default CreateExamCategory
