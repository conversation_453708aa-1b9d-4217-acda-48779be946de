import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 作答形式枚举
 * attachments 提交附件
 * online_editing 在线编辑
 */
export enum AnswerTypeEnum {
  attachments = 1,
  online_editing = 2
}

/**
 * @description 作答形式
 */
class AnswerType extends AbstractEnum<AnswerTypeEnum> {
  static enum = AnswerTypeEnum

  constructor(status?: AnswerTypeEnum) {
    super()
    this.current = status
    this.map.set(AnswerTypeEnum.attachments, '提交附件')
    this.map.set(AnswerTypeEnum.online_editing, '在线编辑')
  }
}

export default AnswerType
