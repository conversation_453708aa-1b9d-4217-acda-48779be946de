/**
 * 课件信息
 */
import {
  CoursewareDetailResponse,
  CoursewareResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import CoursewareCategory from '@api/service/management/resource/courseware-category/query/vo/CoursewareCategory'
import CoursewareDetail from '@api/service/management/resource/courseware/query/vo/CoursewareDetail'
import AdminUserInfoVo from '@api/service/management/user/query/manager/vo/AdminUserInfoVo'

class CoursewareListDetail extends CoursewareDetail {
  categories: Array<CoursewareCategory> = new Array<CoursewareCategory>()
  selected = false

  static from(coursewareResponse: CoursewareResponse) {
    return CoursewareDetail.from(coursewareResponse as CoursewareDetailResponse) as CoursewareListDetail
  }

  static addUserInfo(dto: CoursewareListDetail, userMap: Map<string, AdminUserInfoVo>) {
    dto.creator.name = userMap.get(dto.creator.id)?.userName || ''
    return dto
  }
}

export default CoursewareListDetail
