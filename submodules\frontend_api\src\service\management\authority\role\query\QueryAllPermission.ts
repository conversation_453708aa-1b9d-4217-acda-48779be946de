import roleGateWay, { SecurityObjectGroupDto } from '@api/ms-gateway/ms-role-v1'
export class QueryAllPermission {
  /*
   *  获取所有组安全对象
   * */
  async getAllPermission() {
    try {
      const res = await roleGateWay.getAllPermission()
      let tmpArr: SecurityObjectGroupDto[] = []

      if (res.status.isSuccess()) {
        tmpArr = res.data
      }

      console.log('调用了getAllPermission方法，返回值=', tmpArr)
      return tmpArr
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/query/QueryAllPermission.ts所处方法，getAllPermission',
        e
      )
    }
  }
}
