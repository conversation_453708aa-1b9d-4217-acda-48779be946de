<template>
  <div>
    <template v-if="$hasPermission('BizUnitTree')" desc="用户管理-钉钉部门树" actions="created,departments">
      <el-form v-if="searchable" inline @keydown.native.enter="search">
        <el-form-item>
          <el-input v-if="searchable" clearable v-model="name" @clear="search" placeholder="请输入单位名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
      </el-form>
      <div v-loading="loadingDepartment" style="min-height: 100px">
        <el-tree
          ref="tree"
          :props="{ label: 'name', children: 'subDepartments', isLeaf: 'leaf' }"
          :node-key="field"
          highlight-current
          @node-click="select"
          :load="loadDepartment"
          :expand-on-click-node="false"
          @check="check"
          :show-checkbox="multiple"
          :filter-node-method="filterNode"
          lazy
        >
          <div slot-scope="{ node, data }">
            <span>{{ data.name }}</span>
            <i class="el-icon-loading ml10" v-if="loadingCount"></i>
            <span v-if="userCountMap[data.unitId] && showUserCount" style="font-size: 12px; color: #2d8cf0;">
              （{{ userCountMap[data.unitId].current }} / {{ userCountMap[data.unitId].total }}）
            </span>
          </div>
        </el-tree>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
  // import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  // // import DingTalkDepartmentModule from '@api/module/common/dingtalk/department/DingTalkDepartmentModule'
  // import DingTalkDepartment from '@api/model/dingtalk/department/DingTalkDepartment'
  // import { ElTree } from 'element-ui/types/tree'
  // import UserModule from '@api/module/management/user/UserModule'

  // @Component
  // export default class extends Vue {
  //   @Prop({
  //     type: Boolean,
  //     default: false
  //   })
  //   showUserCount: boolean
  //   @Prop({
  //     type: Boolean,
  //     default: false
  //   })
  //   multiple: boolean
  //   @Prop({
  //     type: Boolean,
  //     default: false
  //   })
  //   searchable: boolean
  //   @Prop({
  //     type: String,
  //     default: 'unitId'
  //   })
  //   field: string

  //   @Ref('tree')
  //   tree: ElTree<any, any>

  //   name = ''
  //   searchName = ''
  //   userCountMap: {
  //     [key: string]: {
  //       current: number
  //       total: number
  //     }
  //   } = {}

  //   search() {
  //     this.searchName = this.name
  //   }

  //   loadingCount = false

  //   async loadDepartment(node: any, resolve: (data: any) => {}) {
  //     await this.loadCoreData()
  //     const deptId = !node.data ? (null as any) : node.data.deptId
  //     // const departments = DingTalkDepartmentModule.getSubDepartments(deptId, false)
  //     // if (this.showUserCount) {
  //     //   await this.loadUserCount(departments)
  //     // }
  //     // resolve(departments)
  //   }

  //   isLoaded = false
  //   loadingDepartment = false

  //   async loadCoreData() {
  //     if (this.isLoaded) return
  //     try {
  //       this.loadingDepartment = true
  //       // await DingTalkDepartmentModule.init()
  //       this.isLoaded = true
  //     } catch (e) {
  //       // todo
  //     } finally {
  //       this.loadingDepartment = false
  //     }
  //   }

  //   @Watch('searchName')
  //   searchNameChange() {
  //     this.tree.filter(this.searchName)
  //   }

  //   filterNode(value: string, data: any) {
  //     if (!value) return true
  //     return (data.name || '').indexOf(value) !== -1
  //   }

  //   async loadUserCount(departments: Array<any>) {
  //     // const unitIds = departments.map(item => item.unitId)
  //     // TODO: 修复报错隐藏 -lpj
  //     // await UserModule.findUnitStudentCount(unitIds)
  //     UserModule.unitUserCountList.forEach(item => {
  //       this.$set(this.userCountMap, item.unitId, {
  //         total: item.totalUnitUserCount,
  //         current: item.currentUnitUserCount
  //       })
  //     })
  //   }

  //   select(data: any) {
  //     if (!this.multiple) {
  //       this.$emit('input', data[this.field])
  //       this.selectNodes = [data]
  //     }
  //   }

  //   selectNodes: DingTalkDepartment[] = []

  //   check() {
  //     if (this.multiple) {
  //       this.$emit('input', this.tree.getCheckedKeys())
  //       this.selectNodes = this.tree.getCheckedNodes()
  //     }
  //   }

  //   clearSelect() {
  //     this.tree.setCurrentKey(undefined)
  //   }
  // }
</script>
