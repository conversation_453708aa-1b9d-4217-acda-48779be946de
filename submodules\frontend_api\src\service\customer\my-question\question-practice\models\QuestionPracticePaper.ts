/**
 * 试题练习
 */
class QuestionPracticePaper {
  /**
   * 试卷id
   */
  id: string

  /**
   * 学习方案id
   */
  schemeId: string

  /**
   * 学习方式id
   */
  learningId: string

  /**
   * 抽题方式，1：指定考纲方式，2：按试题题型及抽题比例
   */
  fetchWay: number

  /**
   * 指定考纲方式
   */
  designatedExaminationOutlineWay: SupportDesignatedExaminationOutlineWay
}

export class SupportDesignatedExaminationOutlineWay {
  /**
   * 是否开启历年真题
   */
  openReal: boolean
  /**
   * 是否开启练习题
   */
  openPractice: boolean
  /**
   * 是否开启模拟试题
   */
  openSimulation: boolean
}

export default QuestionPracticePaper
