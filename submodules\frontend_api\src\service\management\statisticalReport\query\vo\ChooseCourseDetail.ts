import CourseLearningBackstage, {
  ChooseCourseStatisticsInfo,
  CourseDetailResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import * as GraphqlImporter from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage/graphql-importer'
import BasicDataQueryBackstage, {
  CoursewareSupplierRequest,
  CoursewareSupplierResponse,
  ServicerBaseRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { Page } from '@hbfe/common'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import { TechnologyLevelVo } from '@api/service/common/basic-data-dictionary/query/QueryTechnologyLevel'

class ChooseCourseDetail extends ChooseCourseStatisticsInfo {
  // * 课件供应商名称
  providerName = ''
  // * 课程名称名称
  courseName = ''
  // * 技术等级名称
  technicalGradeName = ''

  static async from(dto: ChooseCourseStatisticsInfo[]): Promise<ChooseCourseDetail[]> {
    const courseIds: string[] = []
    const providerIds: string[] = []
    let technicalGradeIds: string[] = []
    dto.map(res => {
      courseIds.push(res.courseId)
      providerIds.push(res.supplierId)
      technicalGradeIds.push(res.technicalGrade)
    })
    technicalGradeIds = Array.from(new Set(technicalGradeIds))
    let courseMap: Map<string, CourseDetailResponse>
    let providerList: CoursewareSupplierResponse[]
    let technicalGradeList: TechnologyLevelVo[]
    if (courseIds && courseIds.length) {
      courseMap = await this.batchQueryCourseDetail(courseIds)
    }
    if (providerIds && providerIds.length) {
      providerList = await this.batchQueryProvider(providerIds)
    }
    if (technicalGradeIds && technicalGradeIds.length) {
      const factory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
      technicalGradeList = await factory.queryTechnologyLevel.query()
    }
    return dto.map(res => {
      let vo: ChooseCourseDetail = new ChooseCourseDetail()
      vo = Object.assign(vo, res)
      if (courseMap.get(res.courseId)) {
        vo.courseName = courseMap.get(res.courseId).name || ''
      }
      vo.providerName = ''
      if (providerList && providerList?.length) {
        providerList.map(provider => {
          if (provider.servicerBase.servicerId) {
            if (provider.servicerBase.servicerId === res.supplierId) {
              vo.providerName = provider.servicerBase.servicerName
            }
          }
        })
      }
      vo.technicalGradeName = technicalGradeList.find(item => item.id === res.technicalGrade).showName || ''
      console.log('vo', vo)
      return vo
    })
  }

  static async batchQueryCourseDetail(ids: string[]) {
    ids = Array.from(new Set(ids))
    const rew = new RewriteGraph<CourseDetailResponse, string>(
      CourseLearningBackstage._commonQuery,
      GraphqlImporter.getCourseInServicer
    )
    await rew.request(ids)
    return rew.itemMap
  }

  static async batchQueryProvider(ids: string[]) {
    ids = Array.from(new Set(ids))
    const page = new Page(1, ids.length)
    const providerRequest = new CoursewareSupplierRequest()
    providerRequest.servicerBase = new ServicerBaseRequest()
    providerRequest.servicerBase.servicerIdList = ids
    const providerResponse = await BasicDataQueryBackstage.pageCoursewareSupplierInfoInSubProject({
      page,
      request: providerRequest
    })
    if (providerResponse.status.code !== 200) {
      return Promise.reject(providerResponse)
    }
    return providerResponse.data.currentPageData
  }
}

export default ChooseCourseDetail
