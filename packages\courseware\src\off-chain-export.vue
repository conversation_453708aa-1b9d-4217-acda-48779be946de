<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/courseware' }">课件管理</el-breadcrumb-item>
      <el-breadcrumb-item>外链课件导入</el-breadcrumb-item>
    </el-breadcrumb>
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
      <p>温馨提示：</p>
      <p>
        1.平台仅支持外链课件的批量导入功能，在导入请下载[导入外链课件模板]，并严格根据表格内容填写保存后再导入系统；
      </p>
      <p>2.导入表格一次最多支持1000条记录，若超过1000条则不能正常导入；</p>
      <p>3.导入后可以通过“导入任务查看” 查看并确认导入结果；</p>
      <p>4.仅支持新建课件，已存在的课件无法通过导入修改；</p>
    </el-alert>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-row type="flex" justify="center">
          <el-col :sm="14" :lg="10">
            <el-steps direction="vertical" :active="2" class="m-vertical-steps">
              <el-step title="下载导入外链课件模板，填写要求信息">
                <div slot="description">
                  <el-button
                    type="primary"
                    size="small"
                    plain
                    class="f-mt5"
                    icon="el-icon-download"
                    @click="downloadTemplate"
                  >
                    导入外链课件模板
                  </el-button>
                </div>
              </el-step>
              <el-step title="上传填写好的外链课件导入表格">
                <div slot="description">
                  <min-upload-file v-model="hbFileUploadResponse" :file-type="1">
                    <el-button slot="trigger" type="primary" plain>点击上传模板</el-button>
                  </min-upload-file>
                </div>
              </el-step>
            </el-steps>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc">
        <el-button @click="goBack('/resource/courseware')">返回上一级</el-button>
        <el-button type="primary" @click="doSave">上传</el-button>
      </div>
    </div>
    <el-dialog title="提示" :visible.sync="importSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导入成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导入任务查看-外链课件导入任务</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="importSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goImportDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import ImportCourseware from '@api/service/management/resource/courseware/mutation/ImportCourseware'
  import MinUploadFile from '@hbfe/jxjy-admin-courseware/src/components/min-upload-file.vue'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'

  @Component({
    components: { MinUploadFile }
  })
  export default class extends Vue {
    //文件上传之后的回调参数
    hbFileUploadResponse = new HBFileUploadResponse()
    //导入成功弹窗
    importSuccessVisible = false
    // 导入模型
    importModel = new ImportCourseware()

    //下载模板
    downloadTemplate() {
      const link = document.createElement('a')
      const resolver = this.$router.resolve({
        name:
          '/mfs/ms-file/public/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/外链课件批量导入模板.xlsx'
      })

      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.'),
        typeName = urlArr.pop()
      link.setAttribute('download', '导入外链课件模板')
      document.body.appendChild(link)
      link.click()
      link.remove()
    }

    // 保存
    async doSave() {
      if (!this.hbFileUploadResponse.url || this.hbFileUploadResponse.url == '') {
        this.$message.warning('请上传外链课件模板')
        return
      }
      this.importModel.modelUrl = this.hbFileUploadResponse.url
      this.importModel.modelName = this.hbFileUploadResponse.fileName
      const res = await this.importModel.doImport(this.importModel.modelUrl, this.importModel.modelName)
      if (res.status.isSuccess()) {
        this.$message.success('保存成功')
        this.importSuccessVisible = true
      } else {
        this.$message.error('保存失败')
      }
    }

    // 获取外链信息
    async getOffChainInfo() {
      const res = await this.importModel.queryImportTemplatePath()
      this.hbFileUploadResponse.url = res
      // if (res.isSuccess()) {
      //   //console.log(this.mutationCollectiveSignUp.collectiveSignUp)
      //   this.hbFileUploadResponse = new HBFileUploadResponse()
      //   this.hbFileUploadResponse.url = this.importModel.modelUrl
      //   this.hbFileUploadResponse.fileName = this.importModel.modelName
      // }
    }

    // 返回上一级
    goBack(url: string) {
      this.$router.push(url)
    }

    async created() {
      await this.getOffChainInfo()
    }

    // 下载导入数据
    goImportDownloadPage() {
      this.importSuccessVisible = false
      this.$router.push({
        path: '/training/task/importtask',
        query: { type: '外链课件导入任务' }
      })
    }
  }
</script>
