import { WechatLoginConfigResponse, WechatLoginConfigSaveRequest } from '@api/ms-gateway/ms-servicer-series-v1'
import AccountSettingVo from './AccountSettingVo'
import AuthSeetingVo from './AuthSettingVo'
import GeneralLoginSettingVo from './GeneralLoginSettingVo'
import ScanSeetingVo from './ScanSettingVo'

class LoginSettingVo {
  /**
   * 微信授权登录设置
   */
  authSeetingVo = new AuthSeetingVo()
  /**
   * 微信扫码登录设置
   */
  scanSeetingVo = new ScanSeetingVo()
  /**
   * 账号登录设置
   */
  accountSettingVo = new AccountSettingVo()
  /**
   * 通用登录规则
   */
  generalLogin = new GeneralLoginSettingVo()
  from(res: WechatLoginConfigResponse) {
    this.authSeetingVo.appId = res?.authAppId
    this.authSeetingVo.secret = res?.authAppSecret
    this.authSeetingVo.enable = res?.enabledAuth
    this.scanSeetingVo.enable = res?.enabled
    this.scanSeetingVo.appId = res?.qRAppId
    this.scanSeetingVo.secret = res?.qRAppSecret
    this.accountSettingVo.enable = res?.enabledAccLogin
  }
  to(): WechatLoginConfigSaveRequest {
    const request = new WechatLoginConfigSaveRequest()
    request.enabled = this.scanSeetingVo.enable
    request.qrAppId = this.scanSeetingVo.appId
    request.qrAppSecret = this.scanSeetingVo.secret
    request.authAppId = this.authSeetingVo.appId
    request.authAppSecret = this.authSeetingVo.secret
    request.enabledAuth = this.authSeetingVo.enable
    request.enabledAccLogin = this.accountSettingVo.enable
    return request
  }
}
export default LoginSettingVo
