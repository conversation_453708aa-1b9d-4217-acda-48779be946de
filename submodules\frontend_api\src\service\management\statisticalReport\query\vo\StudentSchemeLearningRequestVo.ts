import {
  ConnectManageSystemRequest,
  DataAnalysisRequest,
  LearningRegisterRequest,
  SchemeRequest,
  StudentLearningRequest,
  UserRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

export enum SyncResultEnmu {
  // * 未同步
  Unsynchronized = 1,
  // * 已同步
  Synchronized,
  // * 同步失败
  SynchronizationFailure,
  // * 待同步
  Waitsynchronized
}

export class StudentSchemeLearningRequestVo {
  /**
   * 学员信息
   */
  student?: UserRequest = new UserRequest()
  /**
   * 报名信息
   */
  learningRegister?: LearningRegisterRequest = new LearningRegisterRequest()
  /**
   * 培训班信息
   */
  scheme?: SchemeRequest = new SchemeRequest()
  /**
   * 学习信息
   */
  studentLearning?: StudentLearningRequest = new StudentLearningRequest()
  /**
   * 数据分析信息
   */
  dataAnalysis?: DataAnalysisRequest = new DataAnalysisRequest()
  /**
   * 名字
   */
  name = ''
  /**
   * 身份证
   */

  idCard = ''
  /**
   * 手机号
   */
  phone = ''
  /**
   * 登录账号
   */
  loginAccount = ''
  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels? = new Array<number>()
  /**
   * 专题名称
   */
  trainingChannelName?: string = ''
  /**
   * 成果是否同步
   */
  syncResult: SyncResultEnmu = null
  /**
   * 对接管理系统
   */
  connectManageSystem: ConnectManageSystemRequest = new ConnectManageSystemRequest()
  /**
   * 分销商名称
   */
  distributorName = ''
  /**
   * 分销商Id
   */
  distributorId = ''
  /**
   * 推广门户别名`
   */
  promoteThePortalAlias = ''
  /**
   * 推广门户Id
   */
  portalId = ''
  /**
   * 查看非门户推广数据
   */
  notDistributionPortal = false
  /**
   * 培训形式
   * trainingWay0001：网授
   * trainingWay0002：面网授
   */
  trainingType = 'trainingWay0001'
  /**
   * 期别id
   */
  issueId = ''
}
