import platformStatisticReportQueryGateway, {
  LearningStatisticDTO,
  LearningStatisticRequest,
  SchemeSkuLearningStatisticRequest,
  SchemeSkuLearniningStatisticsResponse
} from '@api/gateway/PlatformStatisticReportQuery'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { Page } from '@api/service/common/models/Page'
import CommonModule from '@api/service/common/common/CommonModule'
import { SkuLearningStatisticUIDTO } from '@api/service/common/models/statistic/SkuLearningStatisticUIDTO'

export interface ILearningStatistic {
  /**
   * 学习统计数据集合（已分页）
   */
  learningStatisticList: Array<LearningStatisticDTO>
  /**
   * 学习统计分页参数
   */
  learningStatisticTotalSize: number

  schemeSkuLearningStatisticList: Array<SchemeSkuLearniningStatisticsResponse>
  schemeSkuLearningStatisticTotalSize: number
}

@Module({ namespaced: true, store, dynamic: true, name: 'CustomerLearningStatistic' })
class LearningStatistic extends VuexModule implements ILearningStatistic {
  //region state
  learningStatisticList: Array<LearningStatisticDTO> = new Array<LearningStatisticDTO>()
  learningStatisticTotalSize = 0

  schemeSkuLearningStatisticList: Array<SchemeSkuLearniningStatisticsResponse> = new Array<
    SchemeSkuLearniningStatisticsResponse
  >()
  schemeSkuLearningStatisticTotalSize = 0
  //endregion

  //region action
  /**
   * 获取学员学习统计分页
   * @param params
   */
  @Action
  async pageLearningStatistic(params: { page: Page; learningStatisticQueryParamsDTO: LearningStatisticRequest }) {
    const { data, status } = await platformStatisticReportQueryGateway.pageUserLearningStatistic({
      page: params.page,
      request: params.learningStatisticQueryParamsDTO
    })
    if (status.isSuccess()) {
      const pageList = new Array<LearningStatisticDTO>()
      Object.assign(pageList, data.currentPageData)
      this.SET_LIST(pageList)
      this.SET_TOTAL_SIZE(data.totalSize)
    }
    return status
  }

  /**
   * 获取方案sku学习统计
   * @param learningStatisticQueryParamsDTO
   */
  @Action
  async schemeSkuLearningStatistic(learningStatisticQueryParamsDTO: SchemeSkuLearningStatisticRequest) {
    const { data, status } = await platformStatisticReportQueryGateway.schemeSkuLearningStatistic(
      learningStatisticQueryParamsDTO
    )
    if (status.isSuccess()) {
      const pageList = new Array<SchemeSkuLearniningStatisticsResponse>()
      Object.assign(pageList, data)
      this.SET_SCHEME_SKU_LEARNING_STATISTIC_LIST(pageList)
      this.SET_SCHEME_SKU_LEARNING_STATISTIC_TOTAL_SIZE(data.length)
    }
    return status
  }
  //endregion

  //region mutation
  @Mutation
  private SET_LIST(learningStatisticList: Array<LearningStatisticDTO>) {
    this.learningStatisticList = learningStatisticList
  }

  @Mutation
  private SET_TOTAL_SIZE(totalSize: number) {
    this.learningStatisticTotalSize = totalSize
  }

  @Mutation
  private SET_SCHEME_SKU_LEARNING_STATISTIC_LIST(learningStatisticList: Array<SchemeSkuLearniningStatisticsResponse>) {
    this.schemeSkuLearningStatisticList = learningStatisticList
  }

  @Mutation
  private SET_SCHEME_SKU_LEARNING_STATISTIC_TOTAL_SIZE(totalSize: number) {
    this.schemeSkuLearningStatisticTotalSize = totalSize
  }
  //endregion

  //region get
  /**
   * 培训对象维度的数据整合方法
   * 调用该方法前请调用schemeSkuLearningStatistic()进行统计数据的获取
   */
  get traineesDimensionData() {
    return () => {
      CommonModule.init()
      // const traineesList = CommonModule.getTraineesList
      // const jobCategoryList = CommonModule.getJobCategoryList
      // const unitCategoryList = CommonModule.getUnitCategoryList
      const resultList = new Array<SkuLearningStatisticUIDTO>()
      // traineesList.forEach(trainees => {
      //   const result = new SkuLearningStatisticUIDTO()
      //   result.traineesId = trainees.id
      //   result.traineesName = trainees.optionName
      //   const traineesItemList = this.schemeSkuLearningStatisticList.filter(sku => sku.traineesId === trainees.id)
      //   if (trainees.optionCode === 'slry') {
      //     result.studyingObjects = new Array<SkuLearningStatisticObjectItemDTO>()
      //     result.studyFinishObjects = new Array<SkuLearningStatisticObjectItemDTO>()
      //     result.totalObjects = new Array<SkuLearningStatisticObjectItemDTO>()
      //     const resultStudying = new Array<SkuLearningStatisticObjectItemDTO>()
      //     const resultStudyFinish = new Array<SkuLearningStatisticObjectItemDTO>()
      //     const resultStudyCount = new Array<SkuLearningStatisticObjectItemDTO>()
      //     jobCategoryList.forEach(job => {
      //       const jobItemList = traineesItemList.filter(trainees => trainees.jobCategoryId === job.id)
      //       const jobStudying = new SkuLearningStatisticObjectItemDTO()
      //       const jobStudyFinish = new SkuLearningStatisticObjectItemDTO()
      //       const jobStudyCount = new SkuLearningStatisticObjectItemDTO()
      //       jobItemList.forEach(j => {
      //         jobStudying.objectValue += j.studyingCount
      //         jobStudyFinish.objectValue += j.studyFinishCount
      //         jobStudyCount.objectValue += j.studyCount
      //         jobStudying.objectKeyId = job.id
      //         jobStudying.objectKeyName = job.optionName
      //         jobStudyFinish.objectKeyId = job.id
      //         jobStudyFinish.objectKeyName = job.optionName
      //         jobStudyCount.objectKeyId = job.id
      //         jobStudyCount.objectKeyName = job.optionName
      //       })
      //       if (jobItemList.length > 0) {
      //         resultStudying.push(jobStudying)
      //         resultStudyFinish.push(jobStudyFinish)
      //         resultStudyCount.push(jobStudyCount)
      //       }
      //     })
      //     result.studyingObjects = resultStudying
      //     result.studyFinishObjects = resultStudyFinish
      //     result.totalObjects = resultStudyCount
      //     resultList.push(result)
      //   }
      //   if (trainees.optionCode === 'fsgzry') {
      //     result.studyingObjects = new Array<SkuLearningStatisticObjectItemDTO>()
      //     result.studyFinishObjects = new Array<SkuLearningStatisticObjectItemDTO>()
      //     result.totalObjects = new Array<SkuLearningStatisticObjectItemDTO>()
      //     const resultStudying = new Array<SkuLearningStatisticObjectItemDTO>()
      //     const resultStudyFinish = new Array<SkuLearningStatisticObjectItemDTO>()
      //     const resultStudyCount = new Array<SkuLearningStatisticObjectItemDTO>()
      //     unitCategoryList.forEach(unit => {
      //       const unitItemList = traineesItemList.filter(trainees => trainees.unitCategoryId === unit.id)
      //       const unitStudying = new SkuLearningStatisticObjectItemDTO()
      //       const unitStudyFinish = new SkuLearningStatisticObjectItemDTO()
      //       const unitStudyCount = new SkuLearningStatisticObjectItemDTO()
      //       unitItemList.forEach(u => {
      //         unitStudying.objectValue += u.studyingCount
      //         unitStudyFinish.objectValue += u.studyFinishCount
      //         unitStudyCount.objectValue += u.studyCount
      //         unitStudying.objectKeyId = unit.id
      //         unitStudying.objectKeyName = unit.optionName
      //         unitStudyFinish.objectKeyId = unit.id
      //         unitStudyFinish.objectKeyName = unit.optionName
      //         unitStudyCount.objectKeyId = unit.id
      //         unitStudyCount.objectKeyName = unit.optionName
      //       })
      //       if (unitItemList.length > 0) {
      //         resultStudying.push(unitStudying)
      //         resultStudyFinish.push(unitStudyFinish)
      //         resultStudyCount.push(unitStudyCount)
      //       }
      //     })
      //     result.studyingObjects = resultStudying
      //     result.studyFinishObjects = resultStudyFinish
      //     result.totalObjects = resultStudyCount
      //     resultList.push(result)
      //   }
      // })
      return resultList
    }
  }
  //endregion
}

export default getModule(LearningStatistic)
