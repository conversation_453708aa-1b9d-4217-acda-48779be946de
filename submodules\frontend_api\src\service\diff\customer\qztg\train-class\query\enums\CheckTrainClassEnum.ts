export enum CheckTrainClassEnum {
  /**
   * 成功
   */
  success = '200',

  /**
   * 商品不存在
   */
  productNotFound = '30001',

  /*
   * 商品已下架
   */
  productOffShelf = '30002',

  /**
   * 不支持的渠道
   */
  unsupportedChannel = '30003',

  /**
   * 渠道已关闭
   */
  channelClosed = '30004',

  /**
   * 商品资源不可用
   */
  productResourceUnavailable = '30005',

  /**
   * 不开放学员报名
   */
  notInChannel = '30007',

  /**
   * 重复报名同一班级
   */
  duplicateRegistration = '50001',

  /**
   * 培训未开始
   */
  trainingNotStart = '50002',

  /**
   * 培训已结束无法报名
   */
  trainingEnded = '50003',

  /**
   * 报名未开始
   */
  registerNoStart = '50004',

  /**
   * 报名已结束
   */
  registerEnded = '50005',

  /**
   * 存在未支付的集体报名订单
   */
  unpaidGroupOrderExists = '52009',

  /**
   * 存在未支付的订单
   */
  unpaidOrderExists = '52001',

  /**
   * 其他异常
   */
  error = '500'
}
