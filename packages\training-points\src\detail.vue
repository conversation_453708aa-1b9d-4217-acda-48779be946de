<route-params content="/:id"></route-params>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/training-points' }">培训点管理</el-breadcrumb-item>
      <el-breadcrumb-item>培训点详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card">
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="trainingPointInfoInfo" label-width="auto" class="m-form">
              <el-form-item label="培训点名称：">
                {{ trainingPointInfoInfo.trainingPlaceName }}
              </el-form-item>
              <el-form-item label="具体定位地点：">
                <div class="m-map-sel">
                  <div class="content">
                    <Map
                      :locationDetail="locationDetail"
                      ref="map"
                      :isUserInput="false"
                      :isMapClickEnabled="false"
                    ></Map>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="您选中的培训点地址：">
                {{ trainingPointInfoInfo.selectAddress }}
              </el-form-item>
              <el-form-item label="培训点所在地区：">
                {{ trainingPointInfoInfo.trainingPlaceRegionNames }}
              </el-form-item>
              <el-form-item label="培训教室：">
                {{ trainingPointInfoInfo.classroomName }}
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import Map from './component/map.vue'
  import QueryRegionInfo from '@api/service/common/tx-map/QueryRegionInfo'
  import TrainingPlaceManage from '@api/service/management/resource/training-place-manage/TrainingPlaceManage'
  import TrainingPlaceInfo from '@api/service/management/resource/training-place-manage/TrainingPlaceInfo'
  @Component({
    components: {
      Map
    }
  })
  export default class extends Vue {
    @Ref('map') map: Map
    page: UiPage
    query: Query = new Query()
    constructor() {
      super()
      //   this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }

    locationDetail = {
      latitude: 0,
      longitude: 0,
      workPlace: ''
    }
    trainingPointManage = new TrainingPlaceManage()
    trainingPointInfoInfo = new TrainingPlaceInfo()
    queryRegionInfo = new QueryRegionInfo()
    id = ''

    async queryDetail() {
      try {
        await this.trainingPointInfoInfo.queryDetail(`${this.$route.params.id}`)
        this.locationDetail.latitude = this.trainingPointInfoInfo.latitude
        this.locationDetail.longitude = this.trainingPointInfoInfo.longitude
        this.$nextTick(() => {
          this.map.drawerOpened()
        })
      } catch (e) {
        console.log(e)
      }
    }

    created() {
      this.queryDetail()
    }
  }
</script>
