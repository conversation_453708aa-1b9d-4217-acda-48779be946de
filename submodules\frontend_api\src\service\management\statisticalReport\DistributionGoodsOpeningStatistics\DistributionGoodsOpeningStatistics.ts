import { Page } from '@hbfe/common'
import DistributionGoodsOpeningStatisticsItem from './model/DistributionGoodsOpeningStatisticsItem'
import DistributionGoodsOpeningStatisticsParams from './model/DistributionGoodsOpeningStatisticsParams'
import FxnlQueryFront, { QueryWayType } from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import TotalStatic from '@api/service/management/statisticalReport/models/TotalStatic'
import FxnlDataExport from '@api/platform-gateway/fxnl-data-export-gateway-backstage'
import StatisticInfoItem from '../models/StatisticInfoItem'
import SummaryInfo from './model/SummaryInfo'

/**
 * 分销商品开通统计
 */
export default class DistributionGoodsOpeningStatistics {
  /**
   * 查询参数
   */
  param = new DistributionGoodsOpeningStatisticsParams()
  /**
   * 列表
   */
  list: DistributionGoodsOpeningStatisticsItem[] = []
  /**
   * 统计数据
   */
  staticData: TotalStatic = new TotalStatic()
  /**
   * 合计数据
   */
  summaryStaticData: Array<SummaryInfo> = new Array<SummaryInfo>()
  /**
   * 查询列表方法——供应商
   */
  async queryList(page: Page) {
    const request = DistributionGoodsOpeningStatisticsParams.toStatisticTradeRecordRequest(this.param)
    const response = await FxnlQueryFront.pageCommoditySkuDistributorOpenReportInSupplier({ page, request })
    if (!response.status.isSuccess()) {
      console.error('获取分销商品开通列表失败', response)
      return response
    }
    page.totalPageSize = response.data.totalPageSize
    page.totalSize = response.data.totalSize
    this.list = DistributionGoodsOpeningStatisticsItem.fromCommodityOpenReportResponse(response.data.currentPageData)
    return response
  }

  /**
   * 导出列表方法——供应商
   */
  async exportList() {
    const request = DistributionGoodsOpeningStatisticsParams.toStatisticTradeRecordRequestExport(this.param)
    return FxnlDataExport.exportCommodityOpenStatisticsDetailExcelInSupplier(request)
  }
  /**
   * 查询数据统计——供应商
   */
  async queryTotalStatic() {
    const request = DistributionGoodsOpeningStatisticsParams.toStatisticTradeRecordRequest(this.param)
    const response = await FxnlQueryFront.statisticTradeRecordInSupplier(request)
    if (!response.status.isSuccess()) {
      console.error('获取统计数据报错——供应商', response)
      return response
    }
    this.staticData = TotalStatic.TradeStatisticResponseToTotalStatic(response.data)
    return response
  }

  /**
   * 查询列表方法——分销商
   */
  async queryListDistributor(page: Page) {
    const request = DistributionGoodsOpeningStatisticsParams.toStatisticTradeRecordRequest(this.param)
    const temp = false
    const response = await FxnlQueryFront.pageCommoditySkuDistributorOpenReportInDistributor({
      page: page,
      request: request
    })
    if (!response.status.isSuccess()) {
      console.error('获取分销商品开通统计——分销商报错', response)
      return response
    }
    page.totalPageSize = response.data.totalPageSize
    page.totalSize = response.data.totalSize
    this.list = DistributionGoodsOpeningStatisticsItem.fromCommodityDistributorOpenReportResponseInDistributor(
      response.data.currentPageData
    )
    return response
  }

  /**
   * 导出列表方法——分销商
   */
  async exportListDistributor() {
    const request = DistributionGoodsOpeningStatisticsParams.toStatisticTradeRecordRequestExport(this.param)
    return FxnlDataExport.exportCommodityDistributorOpenReportInDistributor(request)
  }
  /**
   * 查询数据统计——分销商
   */
  async queryTotalStaticDistributor() {
    const request = DistributionGoodsOpeningStatisticsParams.toStatisticTradeRecordRequest(this.param)
    request.queryWayType = QueryWayType.ONLY_ME
    const response = await FxnlQueryFront.statisticTradeRecordInDistributor(request)
    if (!response.status.isSuccess()) {
      console.error('获取统计数据报错——供应商', response)
      return response
    }
    this.staticData = TotalStatic.TradeStatisticResponseToTotalStatic(response.data)
    return response
  }
  /**
   * 查询合计数据
   */
  async querySummaryStaticDistributor() {
    const request = DistributionGoodsOpeningStatisticsParams.toStatisticTradeRecordRequest(this.param)
    request.queryWayType = QueryWayType.ONLY_ME
    const response = await FxnlQueryFront.statisticTradeSummaryInDistributor(request)
    if (!response.status.isSuccess()) {
      console.error('获取统计数据报错——供应商', response)
      return response
    }
    this.summaryStaticData = response.data.map(item => {
      const temp = new SummaryInfo()
      temp.summaryType = item.summaryType
      temp.summaryInfo = StatisticInfoItem.toSummaryData(item.summaryInfo)
      return temp
    })

    return response
  }
}
