import { Route } from 'vue-router'

export enum SystemLogType {
  requestTooLong
}

export const systemLogTransformer = (systemLogType: SystemLogType) => {
  return {
    [SystemLogType.requestTooLong]: '请求耗时超过 1s'
  }[systemLogType]
}

class SystemLog {
  router: Route
  type: SystemLogType = SystemLogType.requestTooLong
  content = ''
  title: string | undefined
  spentTime = 0
}

export default SystemLog
